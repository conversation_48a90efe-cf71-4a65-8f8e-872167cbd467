---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright ? 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/SystemSettingData/ModelSetting")
require("Logic/Battle/SkillActDelegateMgr")

---角色模型控制
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.4.8
---@class ModelController
ModelController = {}
local this = ModelController

MaleSkeleton = "M_BaseBiped" ---男性骨架
FemaleSkeleton = "W_BaseBiped" ---女性骨架

PlayerMaterial = "PlayerSelfSSCH"
OtherMaterial = "PlayerSSCH"

MaleFace = "CM_0002_f"
FemaleFace = "CW_0002_f"

MaleBody = "CM_0003_b"
FemaleBody = "CW_0003_b"

MaleUniformItemId = 67004 ---男制服物品編號
FemaleUniformItemId = 67504 ---女制服物品編號

albedoTextureName = "_MainTex"
normalTextureName = "_BumpMap"
metallicTextureName = "_MetallicGlossMap"
emissiveTextureName = "_EmissiveTex"
emissiveIntensityName = "_EmissiveInten"
noiseTextureName = "_NoiseMap"
dissolveThreshoName = "_DissolveThreshold"
DissolveEdgeName = "_DissolveEdge"
dissolveIntensityName = "_DeformationIntensity"
FadeThresholdName = "_FadeThreshold"
xrayColorName = "_XColor"
hitColorName = "_HitColor"  -- 受擊顏色
hitRimPow = "_HitRimPow" -- 受擊色邊緣化
hitColorIntensity = "_HitRimIntensity" -- 受擊強度
glowColorName = "_GlowColor"  -- 自發光顏色
glowRimPow = "_GlowRimPow" -- 自發光色邊緣化
glowColorIntensity = "_GlowRimIntensity" -- 自發光強度
ScanValueName = "_ScanValue" --- 掃描值
ScanColorName = "_ScanColor" ---掃描顏色
ScanObjectHeightName = "_ScanObjectHeight" ---物件整體高度
ScanObjectFootName = "_ScanObjectFoot" ---物件腳位置


albedoTextureId = Shader.PropertyToID(albedoTextureName)
normalTextureId = Shader.PropertyToID(normalTextureName)
metallicTextureId = Shader.PropertyToID(metallicTextureName)
emissiveTextureId = Shader.PropertyToID(emissiveTextureName)
emissiveIntensityId = Shader.PropertyToID(emissiveIntensityName)
noiseTextureId = Shader.PropertyToID(noiseTextureName)
dissolveThreshold = Shader.PropertyToID(dissolveThreshoName)
dissolveIntensity = Shader.PropertyToID(dissolveIntensityName)
dissolveEdge = Shader.PropertyToID(DissolveEdgeName)
FadeEffectThreshold = Shader.PropertyToID(FadeThresholdName)
xrayColorID = Shader.PropertyToID(xrayColorName)
hitColorID = Shader.PropertyToID(hitColorName)
hitRimPowID = Shader.PropertyToID(hitRimPow)
hitColorIntensityID = Shader.PropertyToID(hitColorIntensity)
glowColorID = Shader.PropertyToID(glowColorName)
glowRimPowID = Shader.PropertyToID(glowRimPow)
glowColorIntensityID = Shader.PropertyToID(glowColorIntensity)
ScanValueID = Shader.PropertyToID(ScanValueName)
ScanColorID = Shader.PropertyToID(ScanColorName)
ScanObjectHeightID = Shader.PropertyToID(ScanObjectHeightName)
ScanObjectFootID = Shader.PropertyToID(ScanObjectFootName)

SNEAKSHADER = "HEM/Special/Sneak"
HOLOGRAMSHADER = "HEM/3D/HologramFX"

local MESH_ROLE_PATH = "Mesh_Role/"

--- 顯示設定相關
local m_ISSHOWACTIONEFFECT = true

---武器的LOD物件模型開頭名稱
local WEAPON_OBJECT_LOD_NAME_PRE = "LOD"

local WEAPON_HAND_STR = 
{
    Left = "LeftHand",
    Right = "RightHand"
}

--- StringToHash內的字串要更改的話請同步更改 Animator 內的 Parameters 名稱
AnimationHash = {
    Bool_IsBattle = Animator.StringToHash("battle"), -- 是否處於戰鬥狀態
    Bool_IsMeditate = Animator.StringToHash("IsMeditate"), -- 是否處於打坐狀態
    Bool_IsCollect = Animator.StringToHash("IsCollect"), -- 是否採集中
    Bool_IsPickUp = Animator.StringToHash("IsPickUp"), -- 是否採集中
    Bool_IsDie = Animator.StringToHash("IsDie"), -- 是否死亡
    Bool_IsStun = Animator.StringToHash("IsStun"), -- 是否暈眩
    Bool_IsDown = Animator.StringToHash("IsDown"), -- 是否擊倒
    Bool_IsFly = Animator.StringToHash("IsFly"), -- 是否擊飛
    Bool_IsLoopSituation = Animator.StringToHash("IsLoopSituation"), -- 是否重複播放情境動作
    Bool_IsFormation = Animator.StringToHash("IsFormation"), -- 是否播放陣法動作
    Bool_IsOpen = Animator.StringToHash("open"), -- 是否播放機關動作
    Trigger_Atk = Animator.StringToHash("atk"), -- 觸發攻擊
    Trigger_Switch = Animator.StringToHash("switch"), -- 觸發換招(武器)
    Trigger_Meditate = Animator.StringToHash("TriMeditate"), -- 觸發打坐
    Trigger_Collect = Animator.StringToHash("TriCollect"), -- 觸發採集
    Trigger_PickUp = Animator.StringToHash("TriPickUp"), -- 觸發採集2
    Trigger_Show = Animator.StringToHash("show"), -- 觸發情境動作
    Trigger_Formation = Animator.StringToHash("TriFormation"), --觸發陣法動作
    Trigger_Die = Animator.StringToHash("TriDie"), -- 觸發死亡
    Trigger_Stun = Animator.StringToHash("TriStun"), -- 觸發暈眩
    Trigger_Down = Animator.StringToHash("TriDown"), -- 觸發擊倒
    Trigger_Fly = Animator.StringToHash("TriFly"), -- 觸發擊飛
    Trigger_EndFly = Animator.StringToHash("TriEndFly"), -- 觸發結束擊飛
    Trigger_GetHit = Animator.StringToHash("TriHit"), -- 觸發受擊
    Trigger_SwichRidingSeat = Animator.StringToHash("to change"), -- 坐騎切換座位動作的Trigger
    Trigger_Skip = Animator.StringToHash("Skip"), -- UI用的跳過
    Trigger_Breath = Animator.StringToHash("TriBreath"), -- 叫做呼吸但我先給它套傳送時出現的樣子
    Trigger_SpecialAct = Animator.StringToHash("TriSpecialAct"), -- 特殊演出 需配合Particle
    -- Trigger_StopWalk = Animator.StringToHash("StopWalk"), -- 停止走路
    -- Trigger_StopRun = Animator.StringToHash("StopRun"), -- 停止跑步
    -- Trigger_StopBattleWalk = Animator.StringToHash("StopBattleWalk"), -- 停止戰鬥走路
    -- Trigger_StopBattleRun = Animator.StringToHash("StopBattleRun"), -- 停止戰鬥跑步
    Int_HitDirect = Animator.StringToHash("HitDirect"), -- 受擊方向
    Int_AtkID = Animator.StringToHash("atkID"), -- 攻擊動作ID
    Int_PetAtkID = Animator.StringToHash("atkID1"), -- 也是攻擊動作ID 應該是給寵物用的???
    Int_ShowID = Animator.StringToHash("showID"), -- 情境動作ID
    Int_FormationID = Animator.StringToHash("FormationID"), -- 陣法動作ID
    Int_RidingSeat = Animator.StringToHash("rideId"), -- 坐騎各座位的騎乘動作ID
    Int_SpecialActID = Animator.StringToHash("SpecialActID"), -- 特殊演出 需配合Particle
    Float_Movement = Animator.StringToHash("movement"), --移動速度
    Float_AtkSpeed = Animator.StringToHash("Speed"), -- 攻擊速度
    State_Movement = Animator.StringToHash("Movement"), -- 移動狀態
    State_Battle = Animator.StringToHash("Battle"), -- 戰鬥狀態
    State_Die = Animator.StringToHash("Die") -- 死亡狀態
}

---可表定/自定義的shader部位
ColorKeywords = {
    [1] = "_HairColor",
    [2] = "_SkinColor",
    [3] = "_ClothColor1",
    [4] = "_ClothColor2",
    [5] = "_EmissiveColor",
}

ModelBodyPartString = 
{
    Hair = "Hair",
    Face = "Face",
    Body = "Body",
    Back = "Back"
}

ModelBodyPart = 
{
    [ModelBodyPartString.Hair] = ItemEquipPos.Hat,
    [ModelBodyPartString.Face] = ItemEquipPos.Mask,
    [ModelBodyPartString.Body] = ItemEquipPos.Cloth,
    [ModelBodyPartString.Back] = ItemEquipPos.Cloak
}


---採集動作演出時 配套特效ID
ModelController.COLLECT_EFFECT_ID = 60008

---@type table<string,GameObject> 已載入的模型
ModelController.m_Table_Model = {}

---@type table<string,AnimationSlot> 已載入的 AnimationSlot
local m_Table_Animator = {}

---依據裝置取用模型
local m_MemoryLevelTable = 
{
    [EDeviceLevel.High] = "_",
    [EDeviceLevel.Medium] = "_M_",
    [EDeviceLevel.Low] = "_L_"
}

---武器類別取得對應骨架ID對照
local m_WeaponTypeToWeaponBoneTable = 
{
    [EWeaponType.Knife] = EBoneIdx.R_W0,
    [EWeaponType.Sword] = EBoneIdx.R_W1,
    [EWeaponType.Fist] = EBoneIdx.R_Hand,
    [EWeaponType.Pike] = EBoneIdx.R_W3,
    [EWeaponType.Stick] = EBoneIdx.R_W3,
}


---除武器以外其他都要用到
---@return string 依照裝置規格判斷的模型名
local function GetModelNameByDeviceLevel(iName, iMemoryLevel)
    -- local _Infix
    iMemoryLevel = iMemoryLevel == nil and SettingMgr.m_DeviceLevel or iMemoryLevel

    if string.IsNullOrEmpty(iName) or not string.gmatch(iName, "_") then
        return iName
    else
        local _strTable = string.split(iName, "_")
        local _ReturnStr = ""
        for k, _str in pairs(_strTable) do
            _ReturnStr = _ReturnStr .. _str
            if k == 1 then
                _ReturnStr = _ReturnStr..m_MemoryLevelTable[iMemoryLevel]
            elseif k ~= #_strTable then
                _ReturnStr = _ReturnStr .."_"
            end
        end

        return _ReturnStr
    end
end

---角色基本模型
local function NewCBM(iHairIdx, iGender)
    local _CBMN = {}
    --setmetatable(_CBMN, { __index = self })

    if iHairIdx and iGender then
        ---@type string 頭髮模型名稱
        _CBMN[ModelBodyPart.Hair] = AppearanceData.GetHairModelNameByServerIdx(iHairIdx, iGender)
        ---@type string 臉部模型名稱
        _CBMN[ModelBodyPart.Face] = (iGender == EPlayerGender.Man and MaleFace or FemaleFace)
        -----@type string 面具名稱 (應該有機會拔掉)
        --_CBMN.m_FaceMaskName = ""
        ---@type string 身體模型名稱
        _CBMN[ModelBodyPart.Body] = (iGender == EPlayerGender.Man and MaleBody or FemaleBody)
        ---@type string 背部模型名稱
        _CBMN[ModelBodyPart.Back] = ""
    else
        ---@type string 頭髮模型名稱
        _CBMN[ModelBodyPart.Hair] = ""
        ---@type string 臉部模型名稱
        _CBMN[ModelBodyPart.Face] = ""
        -----@type string 面具名稱 (應該有機會拔掉)
        --_CBMN.m_FaceMaskName = ""
        ---@type string 身體模型名稱
        _CBMN[ModelBodyPart.Body] = ""
        ---@type string 背部模型名稱
        _CBMN[ModelBodyPart.Back] = ""
        
    end

    for _bodypart, _modelName in pairs(_CBMN) do
        _CBMN[_bodypart] = GetModelNameByDeviceLevel(_modelName)
    end

    return _CBMN
end

-- 建立骨架哈希表
local m_BoneHash = {}
for k, v in pairs(EBoneName) do
    m_BoneHash[v] = true
end
local function SetBoneTransform(iSelf)
    if not iSelf.m_table_Bones then
        iSelf.m_table_Bones = {}
    end
    
    local _AllTrans = iSelf.m_ModelObject.transform:GetComponentsInChildren(typeof(UnityEngine.Transform))
    for i = 0, _AllTrans.Length - 1 do
        -- 玩家因換裝(或變色)規劃LOD不同，需要將骨架全存用來正確使用在ModelController:ApplyModel上
        if iSelf.m_PlayerID then
            iSelf.m_table_Bones[_AllTrans[i].name] = _AllTrans[i]
        else
            -- 過濾掉不需要用到的骨架Transform
            -- 骨架hash有這名稱
            if m_BoneHash[_AllTrans[i].name] then
                iSelf.m_table_Bones[_AllTrans[i].name] = _AllTrans[i]
            end
        end
    end
end

--region 動畫的local函數
--- 要用哪個函式取得完整的 AnimatorSlot名稱
---@param iFun function
---@param ... iFun的參數
---@return string 完整的AnimatorSlot_***** (e.g.Animator\\10001\\AnimatorSlot_10001)
local function GetAnimatorFullName(iFun, ...)
    local _Num = iFun(...)
    return "AnimatorSlot_" .. _Num
end

--- 用字串取得完整 AnimatorSlot 名稱
---@param iString string
---@return string 完整的 AnimatorSlot_***** (e.g.Animator\\10001\\AnimatorSlot_10001)
local function GetAnimatorFullNameByStr(iString)
    return "AnimatorSlot_" .. iString
end

--- 透過武器類別和性別取得人形Animator名稱
---@param iWeaponKind EWeaponType
---@param iGender EPlayerGender
local function GetHumanAnimatorName(iWeaponKind, iGender)
    local _ReturnStr
    if iGender == 0 then --男
        if iWeaponKind == 1 then -- 刀
            _ReturnStr = "10001"
        elseif iWeaponKind == 2 then -- 劍
            _ReturnStr = "10101"
        elseif iWeaponKind == 3 then -- 拳
            _ReturnStr = "10201"
        elseif iWeaponKind == 4 then -- 槍
            _ReturnStr = "10301"
        elseif iWeaponKind == 5 then -- 棍
            _ReturnStr = "10401"
        else
	    -- 2023.09.15 Were 不要不塞R
            D.LogError("Can't Find String")
            _ReturnStr = "10001"
        end
    elseif iGender == 1 then --女
        if iWeaponKind == 1 then -- 刀
            _ReturnStr = "11001"
        elseif iWeaponKind == 2 then -- 劍
            _ReturnStr = "11101"
        elseif iWeaponKind == 3 then -- 拳
            _ReturnStr = "11201"
        elseif iWeaponKind == 4 then -- 槍
            _ReturnStr = "11301"
        elseif iWeaponKind == 5 then -- 棍
            _ReturnStr = "11401"
        else
            D.LogError("Can't Find String")
            _ReturnStr = "11001"
        end
    else
        D.LogError("Can't Find String")
        _ReturnStr = nil
    end

    return _ReturnStr
end

--- 透過性別 取得騎乘人物AC
---@param iGender EPlayerGender
local function GetRiderAnimatorName(iGender)
    if iGender == 0 then --男
        return "10901"
    elseif iGender == 1 then --女
        return "11901"
    else
        D.LogError("Can't Find String")
        return nil
    end
end
--endregion

---模型材質換色
---@param iAppearData AppearanceData 模型外觀串表資料
---@param iAppearInfo PlayerAppearData 角色外觀存檔資料
---@param iRendererTable table<idx, Renderer> iRendererAy資料
function ModelController:SetColor(iAppearData, iAppearInfo, iRendererTable)
	local _MaterialPropertyBlock = self.m_MaterialPropertyBlock
    _MaterialPropertyBlock:Clear()
    
	for i = 0, iRendererTable.Length - 1 do
		iRendererTable[i]:GetPropertyBlock(_MaterialPropertyBlock)

		local _isSuccess, _color
		_isSuccess, _color = ColorUtility.TryParseHtmlString( iAppearData.m_ColorHair, _color )
		if _isSuccess then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[1] ), _color)
		elseif iAppearInfo then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[1] ), iAppearInfo.m_ColorValues[1])
		end

		_isSuccess, _color = ColorUtility.TryParseHtmlString( iAppearData.m_ColorSkin, _color )
		if _isSuccess then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[2] ), _color)
		elseif iAppearInfo then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[2] ), iAppearInfo.m_ColorValues[2])
		end

		_isSuccess, _color = ColorUtility.TryParseHtmlString( iAppearData.m_ColorClothA, _color )
		if _isSuccess then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[3] ), _color)
		elseif iAppearInfo then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[3] ), iAppearInfo.m_ColorValues[3])
		end

		_isSuccess, _color = ColorUtility.TryParseHtmlString( iAppearData.m_ColorClothB, _color )
		if _isSuccess then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[4] ), _color)
		elseif iAppearInfo then
			_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[4] ), iAppearInfo.m_ColorValues[4])
		end

        _isSuccess, _color = ColorUtility.TryParseHtmlString( iAppearData.m_ColorEmission, _color )
        if _isSuccess then
            _MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[5] ), _color)
        else
            _MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[5] ), Color.New(0, 0, 0, 1))
        end		

		iRendererTable[i]:SetPropertyBlock( _MaterialPropertyBlock )     
	end
end

---@param iGObj GameObject
---@param iPlayerAppearanceInfo PlayerAppearData
function ModelController:New(iGObj, iPlayerID, iPlayerAppearanceInfo, iEnhance, iLoadCompleteDelegate, iDebugIdentifyName)
    ---@type ModelController
    local _myself = {}
    _myself.transform = iGObj.transform
    _myself.m_PlayerID = iPlayerID
    _myself.m_AppearanceInfo = iPlayerAppearanceInfo
    ---@type table <string,boolean> 正在loading的物件名,是否loading完成
    _myself.m_Table_IsLoading = {}
    --_myself.m_ModelObject = nil
    ---@type table<string,Transform> 所有子Transform
    _myself.m_table_Bones = {}
    _myself.Animator = nil
    _myself.m_Material = nil
    _myself.m_MaterialPropertyBlock = MaterialPropertyBlock.New()
    _myself.m_Renderer = nil
    _myself.m_CharacterBaseModel = nil
    ---@type table<string,ModelHolder> 讀取完畢的Model
    _myself.m_Loaded_Model = {}
    _myself.m_Enhance = iEnhance -- 強化資料
    --- Debug 辨識用名稱
    _myself.m_DebugIdentifyName = iDebugIdentifyName
    ---武器資料
    _myself.m_WeaponData = 
    {
        [WEAPON_HAND_STR.Left] = {},
        [WEAPON_HAND_STR.Right] = {}
    }
    --_myself.m_PreviousWeapon = EWeaponType.Fist
    setmetatable(_myself, { __index = self })
    _myself.m_Table_IsLoading["Skeleton"] = false
    _myself.m_Table_IsLoading["Animator"] = false
    _myself.m_Table_IsLoading["Material"] = false


    --- 記一下舊的 CBM
    _myself.m_OldCBM = nil

    --- 是不是演武用 MC
    _myself.m_IsWugongDemoMC = false

    --- 演武用目標
    _myself.m_WugongDemoTarget = nil

    --- 使用過的特效
    _myself.m_PlayingEffectTable = {}

    if iLoadCompleteDelegate then
        _myself.m_LoadCompleteDelegate = iLoadCompleteDelegate
    end

    local function SetSkeleton(iObj)
        ---讀取骨架
        iObj.gameObject.name = iPlayerAppearanceInfo.m_Gender == EPlayerGender.Man and MaleSkeleton or FemaleSkeleton
        _myself.m_BattleAtkFootEffectPos = iObj.gameObject.transform
        _myself.m_ModelObject = iObj.gameObject
        _myself.m_ModelObject.transform:SetParent(_myself.transform)
        _myself.m_ModelObject.transform.localPosition = Vector3.zero
        _myself.m_ModelObject.layer = _myself.transform.gameObject.layer --Layer.Player
        _myself.m_ModelObject.tag = "Player"
        _myself.m_Animator = _myself.m_ModelObject:GetComponent("Animator")
        if not Extension.IsUnityObjectNull(_myself.m_Animator) then
            _myself.m_AnimationController = Extension.AddMissingComponent(_myself.m_ModelObject, typeof(AnimationController))
            _myself:InitModelAnimationEventListener()
            if _myself.m_AnimationClipDict then
                _myself.m_AnimationController:SetAnimationClipDict(_myself.m_AnimationClipDict)
            end    
        end
        _myself:CreateSMRTable()

        SetBoneTransform(_myself)
        _myself.m_Table_IsLoading["Skeleton"] = true

        --建立臉部細節骨架資料
        _myself:FaceSetAll()
        ---讀取 Animator
        _myself:LoadAnimation(GetAnimatorFullName( GetHumanAnimatorName, _myself.m_AppearanceInfo.m_WeaponType, _myself.m_AppearanceInfo.m_Gender ))

        ResourceMgr.Load( iPlayerAppearanceInfo.m_IsSelf and PlayerMaterial or OtherMaterial,
                function ( iAsset )
                    if iAsset then
                        _myself.m_Material = iAsset
                        _myself.m_Renderer = _myself.m_ModelObject:GetComponentsInChildren(typeof(UnityEngine.Renderer))
                        _myself.m_SkinnedMeshRenderer =  iGObj:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
                        _myself.m_Table_IsLoading["Material"] = true

                        _myself:UpdateWeapon(_myself.m_AppearanceInfo.m_WeaponType)
                        _myself:UpdateModel(false)                 
                    end
				end )

        _myself.m_Effect = {}
    end

    local _tempObj = _myself.transform:Find(iPlayerAppearanceInfo.m_Gender == EPlayerGender.Man and MaleSkeleton or FemaleSkeleton)
    if _tempObj then
        SetSkeleton(_tempObj)
    else
        ResourceMgr.Load( iPlayerAppearanceInfo.m_Gender == EPlayerGender.Man and MaleSkeleton or FemaleSkeleton,
        function ( iAsset )
            if iAsset then
                SetSkeleton(iAsset)
            end
        end )
    end

    return _myself
end

function ModelController:Update()
    if self.m_UpdateModelCo ~= nil then
		if coroutine.status(self.m_UpdateModelCo) == "suspended" then
			coroutine.resume(self.m_UpdateModelCo)
		elseif coroutine.status(self.m_UpdateModelCo) == "dead" then
            self.m_UpdateModelCo = nil
		end
	end

    if self.m_IsModelUpdateComplete == false and self:IsLoadComplete() then
        self:UpdateBodyModel()
        self.m_IsModelUpdateComplete = true
        if self.m_LoadCompleteDelegate then
            self.m_LoadCompleteDelegate(self)
            self.m_LoadCompleteDelegate = nil
        end
    end

    if self.m_IsModifyAnimator and self.m_AnimationController then
        
        if self.m_AnimationClipDict then
            self.m_AnimationController:SetAnimationClipDict(self.m_AnimationClipDict)
        end
        
        self.m_IsModifyAnimator = false
    end
end

---@param iRC RoleController
function ModelController:SetRoleController(iRC)
    ---@type RoleController
    self.m_RoleController = iRC
end

---是否還存在物件
function ModelController:IsModelObject()
    if self.m_ModelObject ~= nil and not Extension.IsUnityObjectNull(self.m_ModelObject) then
        return true
    else
        if self.m_ModelObject then
            self.m_ModelObject = nil
            self:IsRelease()
            AppearanceMgr.DebugLog("自動回收模型 :"..self.transform.gameObject:GetInstanceID())

        end
        
        return false
    end
end

---載入動畫
function ModelController:LoadAnimation(iAnimatorName, iLoadCompleteDelegate)

    local function iloadComplete()
        if Extension.IsUnityObjectNull(self.m_Animator) then
            D.Log("runtimeAnimatorController 空了")
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(self)
            end    
            return
        end
        self.m_Animator.runtimeAnimatorController = m_Table_Animator[iAnimatorName].m_AnimatorController
        self.m_AnimationClipDict = m_Table_Animator[iAnimatorName]:GetDict_Clips()
        self.m_Table_IsLoading["Animator"] = true
        self.m_IsModifyAnimator = true
        
        if iLoadCompleteDelegate then
            iLoadCompleteDelegate(self)
        elseif BattleMgr.m_IS_BATTLEEDITOR_DEBUG then
            if ProjectMgr.IsEditor() then
                BattleEditorMgr.Inst:UpdateAnimator()
            end
            BattleMgr.SetBattleEditorDebug(false)
        end
    end
    
    if m_Table_Animator[iAnimatorName] then
        iloadComplete()
    else
        ResourceMgr.Load( iAnimatorName,
        function( iAsset )
            if iAsset then
                if m_Table_Animator[iAnimatorName] == nil then
                    m_Table_Animator[iAnimatorName] = iAsset
                end
                iloadComplete()
            else
                D.LogError("<color=white> 讀取"..iAnimatorName.." AnimatorSlot fail </color>")
            end
        end )

    end 
end

function ModelController:InitModelAnimationEventListener()
    self.m_AnimationController:SetSelf(self);
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnParticle, self.OnParticle)
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnSound, self.OnPlaySound)
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnCamera, self.OnPlayCameraEffect)
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnPostProcess, self.OnPostProcess)
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnDelegate, self.OnDelegate)
    self.m_AnimationController:AnimationEventAddListener(EAnimationEvent.OnEnd, self.OnEnd)
    --self.m_AnimationController:AnimationEventAddListener("OnDead", self.OnDead)
end

-- function ModelController:CheckAfterLoadComplete(iDelegateFunc)
--     for _, value in pairs(self.m_Table_IsLoading) do
--         if not value then
--             return
--         end
--     end

--     self:UpdateWeapon(self.m_AppearanceInfo.m_WeaponType)
--     self:UpdateModel(false)

--     if iDelegateFunc then
--         iDelegateFunc(self)
--     end
-- end

function ModelController:SetLodRecalculateBounds()
    local _Lod = self.m_ModelObject:GetComponent(typeof(LODGroup))
    _Lod:RecalculateBounds()
end

---@param iGObj GameObject
---@param iNPCAppearData NPCAppearData
function ModelController:NewNPC(iGObj, iNPCAppearData, iLoadCompleteDelegate, iDebugIdentifyName)
    ---@type ModelController
    local _myself = {
        transform = iGObj.transform,
        ---@type UnityEngine.MaterialPropertyBlock
        m_MaterialPropertyBlock = MaterialPropertyBlock.New(),
        m_NPCAppearData = iNPCAppearData,
        m_Renderer = nil,
        ---@type table<string,ModelHolder>
        m_Table_IsLoading = {},
        ---@type table<string,ModelHolder> 讀取完畢的Model
        m_Loaded_Model = {},
        m_Animator = nil,
        --- Debug 辨識用名稱
        m_DebugIdentifyName = iDebugIdentifyName,

        --- 使用過的特效
        m_PlayingEffectTable = {}
    }

    setmetatable(_myself, { __index = self })

    local function LoadCompleteDelegate()
        if iLoadCompleteDelegate then
            -- _myself.m_Table_IsLoading[_myself.m_NPCAppearData.m_ModelName] = true
            iLoadCompleteDelegate(_myself)
        end
    end

    -- if iLoadCompleteDelegate then
    --     self.m_LoadCompleteDelegate = LoadCompleteDelegate
    -- end

    -- 2023/8/7 都改用 resourec.Load by Hui
    local _NowDeviceSetting = SettingMgr.m_DeviceLevel
    local _loadPath = MESH_ROLE_PATH.._myself.m_NPCAppearData.m_ModelName

    ResourceMgr.LoadGroup(_loadPath,
        function( ... )
            local iAsset
            local _T = {...}

            if _myself.m_IsReleased then
                return
            end

            if table.Count(_T) > 0 then
                for i = 1, #_T do
                    if _NowDeviceSetting ~= EDeviceLevel.High then
                        if string.find(_T[i].name, m_MemoryLevelTable[_NowDeviceSetting]) then
                            iAsset = GameObject.Instantiate(_T[i])
                            break
                        end
                        if i == #_T then
                            iAsset = GameObject.Instantiate(_T[i])
                        end
                    else
                        if string.find(_T[i].name, _myself.m_NPCAppearData.m_ModelName) then
                            iAsset = GameObject.Instantiate(_T[i])
                            break
                        end
                    end
                end
            end

            if iAsset ~= nil then
                _myself.m_Loaded_Model[ _loadPath ] = iAsset.gameObject
                _myself.m_ModelObject = iAsset.gameObject
                _myself.m_ModelObject.name = _myself.m_NPCAppearData.m_ModelName
                _myself.m_ModelObject.transform:SetParent(iGObj.transform)
                _myself.m_ModelObject.transform.localPosition = Vector3.zero
                _myself.m_ModelObject.transform.localRotation = Quaternion.identity
                _myself.m_ModelObject.layer = _myself.transform.gameObject.layer
                _myself.m_ModelObject.tag = "NPC"

                AppearanceMgr.AddLoadedCosmetic(_myself, _loadPath)

                for i = 0, _myself.m_ModelObject.transform.childCount - 1 do
                    local _childLOD = _myself.m_ModelObject.transform:GetChild(i)
                    _childLOD.gameObject.layer = _myself.transform.gameObject.layer
                end

                _myself.m_Renderer = _myself.m_ModelObject:GetComponentsInChildren(typeof(UnityEngine.Renderer))
                _myself.m_SkinnedMeshRenderer =  iGObj:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
                if _myself.m_NPCAppearData.m_ModelColorData then
                    for i = 0, _myself.m_Renderer.Length - 1 do
                        local _Renderer = _myself.m_Renderer[i]
                        _Renderer:GetPropertyBlock(_myself.m_MaterialPropertyBlock)
    
                        for j = 1, #ColorKeywords do
                            _myself.m_MaterialPropertyBlock:SetColor(
                                    ColorKeywords[j],
                                    _myself.m_NPCAppearData.m_ModelColorData.m_ColorTable[j]
                            )
                        end
    
                        _Renderer:SetPropertyBlock( _myself.m_MaterialPropertyBlock )
                    end    
                end

                --有些 Npc 沒有 Animator 還是會有 m_AcName 編號
                --沒有AcName就直接跳過了
                if _myself.m_NPCAppearData.m_AcName ~= 0 then
                    _myself.m_Animator = _myself.m_ModelObject:GetComponent("Animator")
                    if not Extension.IsUnityObjectNull(_myself.m_Animator) then
                        _myself.m_AnimationController = Extension.AddMissingComponent(_myself.m_ModelObject, typeof(AnimationController))
                        _myself:InitModelAnimationEventListener()
                        if _myself.m_AnimationClipDict then
                            _myself.m_AnimationController:SetAnimationClipDict(_myself.m_AnimationClipDict)
                        end

                        _myself:LoadAnimation(GetAnimatorFullNameByStr(_myself.m_NPCAppearData.m_AcName), LoadCompleteDelegate)    
                        -- 設定骨架
                        SetBoneTransform(_myself)
                    else
                        LoadCompleteDelegate()
                    end
                else
                    LoadCompleteDelegate()
                end
            else
                D.LogError("讀取資源: " .. _loadPath .. "失敗")
            end
    end, false)


    return _myself
end

---戰鬥編輯器用，假 ModelController
---@param iGObj GameObject
---@param iNPCAppearData NPCAppearData
function ModelController:NewNPC_Editor(iRC, iGObj, iDebugIdentifyName)
    local _myself = {
        ---@type UnityEngine.MaterialPropertyBlock
        m_MaterialPropertyBlock = MaterialPropertyBlock.New(),
        --m_NPCAppearData = iNPCAppearData,
        m_ModelObject = nil,
        m_Renderer = nil,
        ---@type table<string,ModelHolder>
        m_Table_IsLoading = {},
        ---@type table<string,ModelHolder> 讀取完畢的Model
        m_Loaded_Model = {},
        m_Animator = nil,
        --- Debug 辨識用名稱
        m_DebugIdentifyName = iDebugIdentifyName,

        --- 使用過的特效
        m_PlayingEffectTable = {}
    }

    setmetatable(_myself, { __index = self })
    _myself.m_Table_IsLoading["Animator"] = true

    _myself.transform = iGObj.transform
    _myself.m_ModelObject = iGObj
    --_myself.gameObject.name = _myself.m_NPCAppearData.m_ModelName
    --_myself.transform:SetParent(iGObj.transform)
    --_myself.transform.localPosition = Vector3.zero
    --_myself.transform.localRotation = Quaternion.identity
    _myself.m_ModelObject.layer = Layer.NPC
    _myself.m_ModelObject.tag = "NPC"
    
    for i = 0, _myself.transform.childCount - 1 do
        local _childLOD = _myself.transform:GetChild(i)
        _childLOD.gameObject.layer = Layer.NPC
    end

    _myself.m_Renderer = _myself.m_ModelObject:GetComponentsInChildren(typeof(UnityEngine.Renderer))
    _myself.m_SkinnedMeshRenderer =  iGObj:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
    _myself.m_Animator = iGObj:GetComponent("Animator")
    _myself:SetRoleController(iRC)
    return _myself
end

--[[交由AnimationSlot統一管理，為了避開Runtime對UnityEditor.Animation的使用
--- 設定 Animation Clip 資料
function ModelController:SetAnimationClipData()
    if self.m_Animator then
        self.m_AnimationClipDict = {}
        local _TempLayers = self.m_Animator.runtimeAnimatorController.layers
        for i = 0, _TempLayers.Length - 1 do
            if _TempLayers[i].name == "Base Layer" then
                local _TempStateTrans = _TempLayers[i].stateMachine.anyStateTransitions
                for i = 0, _TempStateTrans.Length - 1 do
                    for j = 0, _TempStateTrans[i].conditions.Length - 1 do
                        if _TempStateTrans[i].conditions[j].parameter == "atkID" then
                            local _TempConditions = _TempStateTrans[i].conditions[j]
                            -- if self.m_PlayerID == PlayerData.GetRoleID() then
                            --     D.LogError("i: " .. i)
                            --     local _Temp1 = _TempStateTrans[i]
                            --     local _Temp2 = _TempStateTrans[i].conditions[j]
                            --     local _Temp3 = _TempStateTrans[i].destinationState
                            --     D.LogError("layer: " .. _TempStateTrans[i].destinationState.name .. " / Id: " .. _TempStateTrans[i].conditions[j].threshold)
                            -- end
                            self.m_AnimationClipDict[tonumber(_TempStateTrans[i].conditions[j].threshold)] = _TempStateTrans[i]
                        end
                    end
                end
            end
        end
    end
end]]

---預載模型
---@param iPlayerAppearanceInfo PlayerAppearData
function ModelController:LoadedModelByAppearance(iIsWearUniform)
    ---@type CharacterBaseModel 各部位的模型名稱
    self.m_CharacterBaseModel, self.m_AppearanceDataTable = self:GetModelBase(iIsWearUniform)
    ---@type table<string,ModelHolder> 讀取完畢的Model
    self.m_Loaded_Model = {}
    -- self.m_Table_IsLoading = {}

    for _, value in pairs(self.m_CharacterBaseModel) do
        if type(value) == "string" and not string.IsNullOrEmpty(value) then
            local _ModelName = value
            self.m_Table_IsLoading[_ModelName] = false
            ResourceMgr.Load(_ModelName,
                    function(iAsset)
                        self.m_Table_IsLoading[_ModelName] = true
                        if iAsset ~= nil then
                            self.m_Loaded_Model[_ModelName] = iAsset
                        else
                            D.LogError("[ModelController]沒有模型資源: " .. _ModelName)
                        end
        
		                -- self:SetLod()
                    end)
        end
    end
end

local _UpdateModelCo = function(iModelController, iIsWearUniform)
    if iModelController then
        while not iModelController:IsLoadComplete() do
            coroutine.yield()
        end
        iModelController.m_UpdateModelCo = nil
        iModelController.m_IsModelUpdateComplete = false
        iModelController:LoadedModelByAppearance(iIsWearUniform)
    end
end

---更新身體模型資料
---@param iPlayerAppearanceInfo Appearance
---@param isForce boolean 強制刷新(舊刷新模式)
function ModelController:UpdateModel(iIsWearUniform, isForce)
    if isForce then
        self.m_IsModelUpdateComplete = false
        self:LoadedModelByAppearance(iIsWearUniform)
    elseif not self.m_UpdateModelCo or coroutine.status(self.m_UpdateModelCo) == "dead" then
        self.m_UpdateModelCo = coroutine.start(_UpdateModelCo,self,iIsWearUniform)
    end
end

---替換模型
---目前專門給戰鬥編輯器替換玩家模型
---@param iObj GameObject
function ModelController:SetPlayerModel(iObj)
    self.m_ModelObject = iObj.gameObject
    self.m_ModelObject.transform:SetParent(self.transform)
    self.m_ModelObject.transform.localPosition = Vector3.zero
    self.m_ModelObject.transform.localRotation = Vector3.zero
    self.m_ModelObject.layer = self.transform.gameObject.layer
    self.m_ModelObject.tag = "Player"
    self.m_Animator = self.m_ModelObject:GetComponent("Animator")

    local _AllTrans = self.m_ModelObject.transform:GetComponentsInChildren(typeof(UnityEngine.Transform))
    for i = 0, _AllTrans.Length - 1 do
        self.m_table_Bones[_AllTrans[i].name] = _AllTrans[i]
    end

    RoleMgr.m_RC_Player.m_AnimationController = Extension.AddMissingComponent(self.m_Animator.gameObject, typeof(AnimationController))
    RoleMgr.m_RC_Player:InitModelAnimationEventListener()
end

---縮放模型大小
function ModelController:SetModelSize(iScale, isTween)
    if not Extension.IsUnityObjectNull(self.m_ModelObject) then
        if isTween then
            LeanTween.scale(self.m_ModelObject,Vector3.one * iScale, 0.5)
        else
            self.m_ModelObject.transform.localScale = Vector3.New(iScale, iScale, iScale)
        end
    end
end

---旋轉模型方向
function ModelController:SetModelDirection(iVec3)
    if self.m_ModelObject then
        self.m_ModelObject.transform.localRotation = Quaternion.Euler(iVec3)
    elseif self.gameObject then
        --有w的原本就是euler 先醬
        if iVec3.w then
            self.gameObject.transform.localRotation = iVec3

        else
            self.gameObject.transform.localRotation = Quaternion.Euler(iVec3)

        end
    end
end

---取得模型方向
function ModelController:GetModelEulerAngles()
    if self.m_ModelObject then
        return self.m_ModelObject.transform.localEulerAngles
    elseif self.gameObject then
        return self.gameObject.transform.localEulerAngles
    end

    return Vector3.zero
end

---暫僅適用於玩家
function ModelController:IsLoadComplete()
    local _IsComplete = true
    for _, value in pairs(self.m_Table_IsLoading) do
        --_IsComplete = value and _IsComplete
        if value == false then
            return false
        end
    end
    return _IsComplete
end

---@param iPlayerAppearanceInfo Appearance
---@param iIsWearUniform boolean 是否穿戰鬥服
---@return CharacterBaseModel 取得各部位模型名稱
function ModelController:GetModelBase(iIsWearUniform)
    local _CBM = NewCBM(self.m_AppearanceInfo.m_HairIndex, self.m_AppearanceInfo.m_Gender)
    local _AppearDataTable = {}

    if iIsWearUniform then
        --[[戰鬥服]]
        -- _AppearDataTable[_CBM.m_HairModelName] = _AppearData
        -- _AppearDataTable[_CBM.m_FaceModelName] = _AppearData

		local _AppearData = AppearanceData.GetData(self.m_AppearanceInfo.m_Gender == EPlayerGender.Man and MaleUniformItemId or FemaleUniformItemId)
		if _AppearData ~= nil then
            _CBM[ModelBodyPart.Body] = GetModelNameByDeviceLevel(_AppearData.m_ModelName) 
            _AppearDataTable[_CBM[ModelBodyPart.Body]] = _AppearData
		end
    else
        for _ModelBodyPart, _ItemPos in pairs(ModelBodyPart) do
            --if _CBM[_ItemPos] then
            if string.IsNullOrEmpty(_CBM[_ItemPos]) or self.m_AppearanceInfo.m_Cosmetics[_ItemPos] > 0 then
                local _AppearData = AppearanceData.GetAppearanceByItemIdx(self.m_AppearanceInfo.m_Cosmetics[_ItemPos],self.m_AppearanceInfo.m_Gender)
                if _AppearData ~= nil then
                    _CBM[_ItemPos] = GetModelNameByDeviceLevel(_AppearData.m_ModelName) 
                    _AppearDataTable[_CBM[_ItemPos]] = _AppearData
                end
            else
                if _ItemPos == ModelBodyPart.Back then
                    -- _CBM[_ItemPos] = ""
                    -- _AppearDataTable[_CBM[_ItemPos]] = {}
                end
            end
            --end
        end
    end

    -- 存一下舊的 self
    self.m_OldCBM = self.m_CharacterBaseModel

    return _CBM, _AppearDataTable
end

---取得SkinnedMeshRenderer
---@return SkinnedMeshRenderer
---@param iTarget Tansform 目標
---@param iPartName string 部位名稱
---@param iLodLevel byte lod等級
local function GetSMRByName(iSkeleton, iPartName, iLodLevel)
    local _FindName = "LOD" .. tostring(iLodLevel) .. "/" .. iPartName
    local _TargetTransform = iSkeleton:Find(tostring(_FindName))
    if _TargetTransform ~= nil then
        return _TargetTransform:GetComponent(typeof(SkinnedMeshRenderer))
    else
        return nil
    end
end

local function GetModelLod(iModelHolder, iLodLevel)
    for i = 0, iModelHolder.modelLods.Count - 1 do
        local _Lod = iModelHolder.modelLods[i]
        if tonumber(_Lod.lodLevel) == tonumber(iLodLevel) then
            return _Lod
        end
    end
end

--region 淡入淡出
---淡入/淡出功能，目前只有作用在HEM/Roles/StandardSmoothness2hit Shader
---開始淡化
---@param iFadeType RoleController.FadeType 淡入淡出種類
function ModelController:StartFade(iFadeType)
    -- 閾值，1 = 完全顯示、0 = 完全不顯示
    local _FadeStartThreshold = nil
    local _ShadowMode = nil
    
    if iFadeType == RoleController.FadeType.FadeIn then
        -- 淡入
        -- 從完全不顯示開始
        _FadeStartThreshold = 0
        -- 關閉陰影渲染
        _ShadowMode = EShadowCastingMode.Off
    elseif iFadeType == RoleController.FadeType.FadeOut then
        -- 淡出
        -- 從完全顯示開始
        _FadeStartThreshold = 1
        -- 開啟陰影渲染
        _ShadowMode = EShadowCastingMode.On
    end

    -- 取閾值錯誤
    if not _FadeStartThreshold then
        return
    end

    if not self:IsModelObject() then
        return
    end
    
    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            --self.m_MaterialPropertyBlock:SetFloat(FadeEffectThreshold, _FadeStartThreshold)
            self.m_SkinnedMeshRenderer[i].shadowCastingMode = _ShadowMode
            self.m_SkinnedMeshRenderer[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
            self.m_MaterialPropertyBlock:SetFloat(FadeEffectThreshold, _FadeStartThreshold)
            self.m_SkinnedMeshRenderer[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
        end
    end

    -- if self.gameObject then
    --     self.transform.gameObject:SetActive(true)
    -- end
    
end

-- 更新淡化效果
function ModelController:UpdateFade(iLerpTime)
    iLerpTime = Mathf.Clamp(iLerpTime, 0, 1)

    if not self:IsModelObject() then
        return
    end

    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            self.m_SkinnedMeshRenderer[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
            local _Value = Mathf.Lerp(1, 0, iLerpTime)
            self.m_MaterialPropertyBlock:SetFloat(FadeEffectThreshold, _Value)
            self.m_SkinnedMeshRenderer[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
        end
    end
end

-- 結束及重置淡化
function ModelController:ResetFade(iFadeType)
    local _ShadowMode = nil

    if iFadeType == RoleController.FadeType.FadeIn then
        -- 淡入完成
        -- 開啟陰影渲染
        _ShadowMode = EShadowCastingMode.On
    elseif iFadeType == RoleController.FadeType.FadeOut then
        -- 淡出完成
        -- 關閉陰影渲染
        _ShadowMode = EShadowCastingMode.Off
    end

    if not self:IsModelObject() then
        return
    end
    
    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            self.m_SkinnedMeshRenderer[i].shadowCastingMode = _ShadowMode
        end
    end

    if iFadeType == RoleController.FadeType.FadeOut then
        -- 淡出完成
        if self.m_PlayerID then
            --self.transform.gameObject:SetActive(false)
        end
    end
    
end

---在幾秒內淡入/淡出
function ModelController:DoFade(isFade, iTime)
    
    if not self:IsModelObject() then
        return
    end

    if isFade then
        LeanTween.value(self.m_ModelObject, System.Action_float(function(iValue) self:UpdateFade(iValue) end), 0, 1, iTime)
    else
        LeanTween.value(self.m_ModelObject, System.Action_float(function(iValue) self:UpdateFade(iValue) end), 1, 0, iTime)
    end
end
--endregion

--region 死亡溶解


---用tween跑的融解(非死亡) -有考慮要不要把死亡搓進來
function ModelController:StartDissolve(iIsShow)
    --因為掃太慢沒感覺起始值改0.6
    local _startVal = iIsShow and EffectSetting.EDissolveType.ShowUpFromSneak.m_StartValue or 0
    local _endVal = iIsShow and 0 or 1


    self:ResetDissolveEffect(iIsShow)

    LeanTween.value(self.transform.gameObject,System.Action_float(function(iValue)
        self:UpdateDissolveEffect(iValue, EffectSetting.EDissolveType.ShowUpFromSneak)
    
        if iValue == 0 then
            --結束後把他設回原來死亡用的
            self:ResetDissolveEffect(true)
        end
    end), _startVal, _endVal, EffectSetting.EDissolveType.ShowUpFromSneak.m_Duration)

end

--- 更新溶解特效
function ModelController:UpdateDissolveEffect(iLerpTime, iEDissolveType)
    if not self:IsModelObject() then
        return
    end

    local _val = Mathf.Lerp(0, 1, iLerpTime)
    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            self.m_SkinnedMeshRenderer[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
            self.m_MaterialPropertyBlock:SetFloat(dissolveThreshold, _val)
            --不知道為啥在reset那裡設定無效，改在這邊
            if iEDissolveType then
                self.m_MaterialPropertyBlock:SetFloat(dissolveIntensity, iEDissolveType.m_DeformationIntensity)
                self.m_MaterialPropertyBlock:SetFloat(dissolveEdge, iEDissolveType.m_DissolveEdge)    
            end
            self.m_SkinnedMeshRenderer[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
        end
    end
    if self.m_WeaponData then
        for k,v in pairs(self.m_WeaponData) do
            if v.Renderers then
                for i = 0, v.Renderers.Length - 1 do
                    v.Renderers[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
                    self.m_MaterialPropertyBlock:SetFloat(dissolveThreshold, _val)
                    --不知道為啥在reset那裡設定無效，改在這邊
                    if iEDissolveType then
                        self.m_MaterialPropertyBlock:SetFloat(dissolveIntensity, iEDissolveType.m_DeformationIntensity)
                        self.m_MaterialPropertyBlock:SetFloat(dissolveEdge, iEDissolveType.m_DissolveEdge)    
                    end
                    v.Renderers[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
                end
            end
        end    
    end
end
 
--- 結束及重置
function ModelController:ResetDissolveEffect(iIsShow)
    if not self:IsModelObject() then
        return
    end

    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            self.m_SkinnedMeshRenderer[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
            self.m_MaterialPropertyBlock:SetFloat(dissolveThreshold, iIsShow and 0 or 1)
            self.m_SkinnedMeshRenderer[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
            self.m_SkinnedMeshRenderer[i].shadowCastingMode = iIsShow and EShadowCastingMode.On or EShadowCastingMode.Off
        end
    end
    if self.m_WeaponData then
        for k,v in pairs(self.m_WeaponData) do
            if v.Renderers then
                for i = 0, v.Renderers.Length - 1 do
                    v.Renderers[i]:GetPropertyBlock(self.m_MaterialPropertyBlock)
                    self.m_MaterialPropertyBlock:SetFloat(dissolveThreshold, iIsShow and 0 or 1)
                    v.Renderers[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
                    v.Renderers[i].shadowCastingMode = iIsShow and EShadowCastingMode.On or EShadowCastingMode.Off
        
                end
            end
        end    
    end

end
--endregion 死往溶解

--region 掃描特效

---掃描特效時的縮放值
local function ScanTweenScale(iIsShow ,iValue, iMC)
    if not iMC.m_IsReleased then
        local _ScanScaleStart = EffectSetting.m_ScanScaleStart
        local _ScanScaleStrength = EffectSetting.m_ScanScaleStrength
        local _ScanMaxScale = EffectSetting.m_ScanMaxScale
        
        ---整體調整值
        local _ScanRimStrength = EffectSetting.m_ScanRimStrength
        local ScanRimIntensity = (1 - math.max(0, iValue - _ScanScaleStart) / (1 - _ScanScaleStart)) * _ScanRimStrength
    
        local nowScanScale = 1 + math.min(math.max(0, _ScanScaleStart - iValue) * _ScanScaleStrength, _ScanMaxScale) - ScanRimIntensity
        --記下縮放值
        if iMC.m_NowScanScale == nil then
            iMC.m_NowScanScale = Vector3.New()
        end
        if iMC.m_BeforeScanScale == nil then
            iMC.m_BeforeScanScale = iMC.m_ModelObject.transform.localScale.x
        end
    
        --顯現時不用拉xz軸
        if iIsShow then
            iMC.m_NowScanScale.x = iMC.m_BeforeScanScale
            iMC.m_NowScanScale.z = iMC.m_BeforeScanScale
            iMC.m_NowScanScale.y = iMC.m_BeforeScanScale
        else
            iMC.m_NowScanScale.x = iMC.m_BeforeScanScale / nowScanScale
            iMC.m_NowScanScale.z = iMC.m_BeforeScanScale / nowScanScale
            iMC.m_NowScanScale.y = iMC.m_BeforeScanScale * nowScanScale
        end
        iMC.m_ModelObject.transform.localScale = iMC.m_NowScanScale
    
    end

end

---Tween過程時會跑的
---@param iMC ModelController
local function ScanTweenAction(iValue ,iMC, iRenderer)
	if not Extension.IsUnityObjectNull(iRenderer) and not Extension.IsUnityObjectNull(iMC.m_ModelObject) then
        local _PosVec3 = iMC.m_ModelObject.transform.position
        _PosVec3.y = _PosVec3.y - 1
        local _ScreenPosFoot = _PosVec3.y
        local _ScreenPosHead
        if iMC.m_RoleController then
            _ScreenPosHead = iMC.m_RoleController.m_Height + 1
        else
            _ScreenPosHead = 3 --(2 + 1) 2為預設角色高度
        end

		iRenderer:GetPropertyBlock(iMC.m_MaterialPropertyBlock)
		iMC.m_MaterialPropertyBlock:SetFloat(ScanValueID, iValue)
        iMC.m_MaterialPropertyBlock:SetColor(ScanColorID, Color.ScanColor) 
        iMC.m_MaterialPropertyBlock:SetFloat(ScanObjectHeightID, _ScreenPosHead)
        iMC.m_MaterialPropertyBlock:SetFloat(ScanObjectFootID, _ScreenPosFoot)

		iRenderer:SetPropertyBlock(iMC.m_MaterialPropertyBlock)
        iRenderer.material.renderQueue = 3000
	end	
end

---執行tween的函數
---@param iSelf ModelController
local function RendererScanTween(iSelf, IsShow)
    local _startVal = IsShow and 0 or 1
    local _EndVal = IsShow and 1 or 0

    LeanTween.value(iSelf.transform.gameObject,System.Action_float(function(iValue)
        if not iSelf.m_IsReleased then
            if iSelf.m_SkinnedMeshRenderer and iSelf.m_SkinnedMeshRenderer.Length > 0 then
                for i = 0, iSelf.m_SkinnedMeshRenderer.Length - 1 do
                    ScanTweenAction(iValue, iSelf, iSelf.m_SkinnedMeshRenderer[i])
                end
                if iSelf.m_WeaponData then
                    for k,v in pairs(iSelf.m_WeaponData) do
                        if v.Renderers then
                            for i = 0, v.Renderers.Length - 1 do
                                ScanTweenAction(iValue, iSelf, v.Renderers[i])
                            end
                        end
                    end    
                end
            end
        
            if iValue == _EndVal then
                iSelf:ResetScan(IsShow, IsShow)
            else
                --依據此數值做模型縮放
                ScanTweenScale(IsShow, iValue, iSelf)
            end

        end
    end), _startVal, _EndVal, EffectSetting.m_TeleportDuration)
end

---重設單一meshRenderer的數值
local function ResetRendererScan(iSelf, IsShow, iShowShadow, iMeshRenderer)

    local _Val = IsShow and 1 or 0

    iMeshRenderer:GetPropertyBlock(iSelf.m_MaterialPropertyBlock)
    --local _Value = Mathf.Lerp(1, 0, iLerpTime)
    iSelf.m_MaterialPropertyBlock:SetFloat(ScanValueID, _Val)
    iSelf.m_MaterialPropertyBlock:SetColor(ScanColorID, Color.ScanColor)
    iSelf.m_MaterialPropertyBlock:SetFloat(ScanObjectHeightID, 0)
    iSelf.m_MaterialPropertyBlock:SetFloat(ScanObjectFootID, 0)
    iMeshRenderer:SetPropertyBlock(iSelf.m_MaterialPropertyBlock)

    iMeshRenderer.shadowCastingMode = iShowShadow and EShadowCastingMode.On or EShadowCastingMode.Off
    if IsShow then
        iMeshRenderer.material.renderQueue = 2003
        
    else
        iMeshRenderer.material.renderQueue = 3000
    end

end

---開始掃描出現/消失
function ModelController:StartScan(IsShow)

    if not self:IsModelObject() then
        return
    end

    self:ResetScan(not IsShow, false)
    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        RendererScanTween(self, IsShow)
    end
end

---重設所有meshRenderer的數值
function ModelController:ResetScan(IsShow, iShowShadow)

    if not self:IsModelObject() then
        return
    end

    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            ResetRendererScan(self, IsShow, iShowShadow, self.m_SkinnedMeshRenderer[i])
        end
        if self.m_WeaponData then
            for k,v in pairs(self.m_WeaponData) do
                if v.Renderers then
                    for i = 0, v.Renderers.Length - 1 do
                        ResetRendererScan(self, IsShow, iShowShadow, v.Renderers[i])
                    end
                end
            end    
        end
    end
    if self.m_BeforeScanScale then
        self:SetModelSize(self.m_BeforeScanScale, false)
        self.m_BeforeScanScale = nil
    end

end
--endregion

function ModelController:SetEmmsiveColor(iPartName, iColor)
    --local _new = MaterialPropertyBlock.New()
    for k,v in pairs(self.m_SMRTable[iPartName]) do
        v:GetPropertyBlock(self.m_MaterialPropertyBlock)
        self.m_MaterialPropertyBlock:SetColor(Shader.PropertyToID( ColorKeywords[5] ), iColor)
		v:SetPropertyBlock( self.m_MaterialPropertyBlock)
    end
end

function ModelController:SetEmmsiveIntense(iPartName, iValue)
    --local _new = MaterialPropertyBlock.New()
    if self.m_SMRTable == nil then
        return
    end
    for k,v in pairs(self.m_SMRTable[iPartName]) do
        v:GetPropertyBlock(self.m_MaterialPropertyBlock)
        self.m_MaterialPropertyBlock:SetFloat(emissiveIntensityId, iValue)
		v:SetPropertyBlock( self.m_MaterialPropertyBlock)
    end
end

function ModelController:CreateSMRTable()
    if not self:IsModelObject() then
        self.m_SMRTable = nil
        return
    end

    self.m_SMRTable = {}
    for k,v in pairs(ModelBodyPart) do
        self.m_SMRTable[k] = {}
        for i = 0, LODLevel - 1 do
            local _FindName = "LOD" .. tostring(i) .. "/" .. k
            local _TargetTransform = self.m_ModelObject.transform:Find(tostring(_FindName))
            if _TargetTransform ~= nil then
                self.m_SMRTable[k][i] = _TargetTransform:GetComponent(typeof(SkinnedMeshRenderer))
            else
                self.m_SMRTable[k][i] = nil
            end
        end
    end
end

function ModelController:ApplyModel(iModelName, iPartName)
    for i = 0, LODLevel - 1 do
        ---@type SkinnedMeshRenderer
        local _SMR = self.m_SMRTable[iPartName][i] --GetSMRByName(iSkeleton, iPartName, i)
        if _SMR == nil then
            return
        end

        local _ModelHolder = self.m_Loaded_Model[iModelName]
        if _ModelHolder then
            local _ModelLod = GetModelLod(_ModelHolder, i)

            --if self.m_AppearanceInfo.m_IsSelf then
            _SMR.gameObject.layer = self.transform.gameObject.layer
            -- else
            --     _SMR.gameObject.layer = Layer.Player
            --end

            _SMR.sharedMesh = _ModelLod.mesh
            _SMR.localBounds = _ModelLod.localBounds
            _SMR.sharedMaterial = self.m_Material

            _SMR:GetPropertyBlock(self.m_MaterialPropertyBlock)
            self.m_MaterialPropertyBlock:Clear()

            if _ModelHolder.noiseTexture ~= nil then
                self.m_MaterialPropertyBlock:SetTexture(noiseTextureId, _ModelHolder.noiseTexture)
            end

            if _ModelHolder.albedoTexture ~= nil then
                self.m_MaterialPropertyBlock:SetTexture(albedoTextureId, _ModelHolder.albedoTexture)
            end

            if _ModelHolder.normalTexture ~= nil then
                self.m_MaterialPropertyBlock:SetTexture(normalTextureId, _ModelHolder.normalTexture)
            end

            if _ModelHolder.metallicTexture ~= nil then
                self.m_MaterialPropertyBlock:SetTexture(metallicTextureId, _ModelHolder.metallicTexture)
            end

            if _ModelHolder.emissiveTexture ~= nil then
                self.m_MaterialPropertyBlock:SetTexture(emissiveTextureId, _ModelHolder.emissiveTexture)
            end

            _SMR:SetPropertyBlock(self.m_MaterialPropertyBlock)
            
            local _Renderer = _SMR.transform:GetComponentsInChildren(typeof(UnityEngine.Renderer))


            --設定預設色彩
            if self.m_AppearanceDataTable[iModelName] then
                self:SetColor(self.m_AppearanceDataTable[iModelName], self.m_AppearanceInfo, _Renderer)
            else
                self:SetColor({}, self.m_AppearanceInfo, _Renderer)
            end
            
            local _Bones = _ModelLod.bones
            local _NewBonesTrans = {}
            for j = 0, _Bones.Length - 1 do
                _NewBonesTrans[j + 1] = self.m_table_Bones[_Bones[j]]
            end

            _SMR.bones = _NewBonesTrans
            if _ModelLod.rootBone then
                _SMR.rootBone = self.m_table_Bones[_ModelLod.rootBone]
            end
        else
            if iPartName == table.GetKey(ModelBodyPart,ModelBodyPart.Back) then
                _SMR.sharedMaterial = nil
                _SMR.sharedMesh = nil    
            end
        end
    end

end

---模型產出後把他放到正確的位置
function ModelController:UpdateBodyModel(isCheckBody)
    if not self:IsModelObject() then
        return
    end
    isCheckBody = isCheckBody == nil and true or isCheckBody 
    local _CBM = self.m_CharacterBaseModel
    if _CBM ~= nil then --沒找到角色模組就不刷  TODO: 區分成玩家跟NPC的
        for key, value in pairs(_CBM) do
            self:ApplyModel(value, table.GetKey(ModelBodyPart,key))
        end
             
        -- 搬到這邊才檢查需不需要移除模型
        AppearanceMgr.SetLoadedCosmetic(self, _CBM)

        local _RoleEnhanceCloth = self.m_Enhance:GetEquipEnhance(ItemEquipPos.Cloth)
        local _EnhanceFXDataCloth = nil
        if _RoleEnhanceCloth.m_Index ~= 0 then
            _EnhanceFXDataCloth = EnhanceFXData.GetEnhanceFXDataByIdx(_RoleEnhanceCloth.m_Index)
        end
        if _EnhanceFXDataCloth then
            EffectMgr.PlayEffectWithParent(EEffectType.Model, _EnhanceFXDataCloth.m_Idx_ArmorFX, self:GetBone(EBoneIdx.Pelvis), true, function(iHash)
                    local _EnhanecColor = EnhanceColorData.GetEnhanceColorDataByIdx(
                            (_RoleEnhanceCloth.m_Rarity == 0 and 1 or _RoleEnhanceCloth.m_Rarity) + 50) -- 串表上防具需要 +50
                    self.m_Effect["Armor"] = iHash
                    if _EnhanecColor then
                        EffectMgr.SetParticleEffectFX(iHash, _EnhanecColor)
                    end
                end )
        end

        local _EnhanceFXDataAccessory = nil
        local _RoleEnhanceAccessory = self.m_Enhance:GetEquipEnhance("Accessory")

        if _RoleEnhanceAccessory.m_Index ~= 0 then
            _EnhanceFXDataAccessory = EnhanceFXData.GetEnhanceFXDataByIdx(_RoleEnhanceAccessory.m_Index)
        end
        if _EnhanceFXDataAccessory then
            EffectMgr.PlayEffectWithParent(EEffectType.Model, _EnhanceFXDataAccessory.m_Idx_Accessory, self:GetBone(EBoneIdx.Pelvis), true, function(iHash)
                    local _EnhanecColor = EnhanceColorData.GetEnhanceColorDataByIdx(
                        (_RoleEnhanceAccessory.m_Rarity == 0 and 1 or _RoleEnhanceAccessory.m_Rarity) + 100) -- 串表上飾品需要 +100
                    self.m_Effect["Accessory"]  = iHash
                    if _EnhanecColor then
                        EffectMgr.SetParticleEffectFX(iHash, _EnhanecColor)
                    end
                end )
        end

        self:SetLodRecalculateBounds()
    end

    --if isCheckBody then
        ---如果這個是衣服，而且是創角那套
        self:SwitchBattleSuitEffect(isCheckBody)
    
    --end
end

---更新武器 (資料都要設定好在呼叫這個)
function ModelController:UpdateWeapon(iWeaponType)
    self.m_IsModelUpdateComplete = false
    local _RoleEnchanceEffect = self.m_Enhance:GetEquipEnhance(iWeaponType)
    local _WeaponItemId, _LeftWeapon, _RigthWeapon, _EnhanceFXId, _EnchanceWeaponRarity
    _WeaponItemId = self.m_AppearanceInfo.m_Cosmetics[1]

    if iWeaponType == EWeaponType.Knife then
        _RigthWeapon = EBoneName.R_W0
        _LeftWeapon = nil
    elseif iWeaponType == EWeaponType.Sword then
        _RigthWeapon = EBoneName.R_W1
        _LeftWeapon = EBoneName.L_W1
    elseif iWeaponType == EWeaponType.Fist then
        _RigthWeapon = EBoneName.R_W2
        _LeftWeapon = EBoneName.L_W2
    elseif iWeaponType == EWeaponType.Pike then
        _RigthWeapon = EBoneName.R_W3
        _LeftWeapon = nil
    elseif iWeaponType == EWeaponType.Stick then
        _RigthWeapon = EBoneName.R_W3
        _LeftWeapon = nil
    else
        D.LogError("No EWeaponType in EWeaponType")
        return
    end
    _EnhanceFXId = _RoleEnchanceEffect.m_Index
    _EnchanceWeaponRarity = _RoleEnchanceEffect.m_Rarity

    --self.m_PreviousWeapon = self.m_AppearanceInfo.m_WeaponType
    self.m_AppearanceInfo.m_WeaponType = iWeaponType

    local _EnhanceFXData = nil
    if _EnhanceFXId ~= 0 then
        _EnhanceFXData = EnhanceFXData.GetEnhanceFXDataByIdx(_EnhanceFXId)
    end

    local _WeaponAppearance = AppearanceData.GetAppearanceByItemIdx(_WeaponItemId == 0 and self.m_AppearanceInfo.m_Cosmetics[1] or _WeaponItemId)
    --右手
    if _WeaponAppearance then
        ResourceMgr.Load( _WeaponAppearance.m_ModelName,
            function ( iAsset )
                if iAsset and not self.m_IsReleased then

                    -- 如果原本有資料，舊先把舊的武器都刪掉
                    if not Extension.IsUnityObjectNull(self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject) then
                        if self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.name ~= _WeaponAppearance.m_ModelName then
                            AppearanceMgr.RemoveLoadedCosmetic(self, self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.name)
                        end
                        
                        GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject)
                        self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject = nil
                        self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers = nil
                        self.m_WeaponData[WEAPON_HAND_STR.Right].MeshFilters = nil
                    end

                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject = iAsset
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.name = _WeaponAppearance.m_ModelName
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform:SetParent(self.m_table_Bones[_RigthWeapon])
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform.localPosition = Vector3.zero
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform.localRotation = Vector3.zero
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform.localScale = Vector3.one
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.layer = self.transform.gameObject.layer
                    self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.tag = "Player"

                    AppearanceMgr.AddLoadedCosmetic(self, _WeaponAppearance.m_ModelName)
                    --self.m_Loaded_Model[_WeaponAppearance.m_ModelName] = iAsset

                    for i = 0, self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform.childCount - 1 do
                        local _childLOD = self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform:GetChild(i)
                        -- if self.m_AppearanceInfo.m_IsSelf then
                            _childLOD.gameObject.layer = self.transform.gameObject.layer
                        -- else
                        --     _childLOD.gameObject.layer = Layer.Player
                        -- end
                        _childLOD.gameObject.tag = "Player"
                    end
                    
                    self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers = iAsset:GetComponentsInChildren(typeof(UnityEngine.MeshRenderer))
                    self.m_WeaponData[WEAPON_HAND_STR.Right].MeshFilters = iAsset:GetComponentsInChildren(typeof(UnityEngine.MeshFilter))
                    self:SetColor(_WeaponAppearance, self.m_AppearanceInfo, self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers)

                    if _EnhanceFXData then
                        EffectMgr.PlayEffectWithParent(EEffectType.Model, _EnhanceFXData.m_Idx_WeaponFX, self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform, true, function(iHash)
                            local _EnhanecColor = EnhanceColorData.GetEnhanceColorDataByIdx(_EnchanceWeaponRarity == 0 and 1 or _EnchanceWeaponRarity)
                            self.m_Effect["LeftHand"] = iHash
                            if _EnhanecColor then
                                --local _Renderer = self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform:GetComponentInChildren(typeof(UnityEngine.MeshRenderer))
                                --local _MeshFilter = self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform:GetComponentInChildren(typeof(UnityEngine.MeshFilter))
                                if self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers.Length > 0 and self.m_WeaponData[WEAPON_HAND_STR.Right].MeshFilters.Length > 0 then
                                    EffectMgr.SetWeaponEffectFX(iHash, _EnhanecColor, self.m_AppearanceInfo.m_WeaponType, self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers[1], self.m_WeaponData[WEAPON_HAND_STR.Right].MeshFilters[1].mesh)
                                end
                            end
                        end )
                    end
                end
            end )

        --左手
        if _LeftWeapon then
            local _LeftHandName = _WeaponAppearance.m_ModelName .. "A"
            ResourceMgr.Load( _LeftHandName,
                function ( iAsset )
                    if iAsset and not self.m_IsReleased then
                        if not Extension.IsUnityObjectNull(self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject) then
                            if self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.name ~= _LeftHandName then
                                AppearanceMgr.RemoveLoadedCosmetic(self, self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.name)
                            end
                            GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject)
                            self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject = nil
                            self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers = nil
                            self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters = nil
                        end
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject = iAsset
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.name = _LeftHandName
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform:SetParent(self.m_table_Bones[_LeftWeapon])
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform.localPosition = Vector3.zero
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform.localRotation = Vector3.zero
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform.localScale = Vector3.one
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.layer = self.transform.gameObject.layer
                        self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.tag = "Player"

                        AppearanceMgr.AddLoadedCosmetic(self, _LeftHandName)
                        --self.m_Loaded_Model[_LeftHandName] = iAsset

                        for i = 0, self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform.childCount - 1 do
                            local _childLOD = self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform:GetChild(i)
                            -- if self.m_AppearanceInfo.m_IsSelf then
                                _childLOD.gameObject.layer = self.transform.gameObject.layer
                            -- else
                            --     _childLOD.gameObject.layer = Layer.Player
                            -- end
                            _childLOD.gameObject.tag = "Player"
                        end    
                        
                        self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers = iAsset:GetComponentsInChildren(typeof(UnityEngine.MeshRenderer))
                        self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters = iAsset:GetComponentsInChildren(typeof(UnityEngine.MeshFilter))
                        self:SetColor(_WeaponAppearance, self.m_AppearanceInfo, self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers)
                        
                        if _EnhanceFXData then
                            EffectMgr.PlayEffectWithParent(
                                EEffectType.Model,
                                _EnhanceFXData.m_Idx_WeaponFX,
                                self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform,
                                true ,
                                function(iHash)
                                    local _EnhanecColor = EnhanceColorData.GetEnhanceColorDataByIdx(_EnchanceWeaponRarity == 0 and 1 or _EnchanceWeaponRarity)
                                    self.m_Effect["RightHand"] = iHash
                                    if _EnhanecColor then
                                        if  self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers.Length > 0 and self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters.Length > 0 then
                                            EffectMgr.SetWeaponEffectFX(iHash, _EnhanecColor, self.m_AppearanceInfo.m_WeaponType, self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers[1], self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters[1].mesh)
                                        end
                                    end
                                end )
                        end
                    end
                end )
    
        elseif self.m_WeaponData then
            if self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject then
                GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject)
                self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject = nil
                self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers = nil
                self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters = nil

            end
        end

    else
        -- 如果原本有資料，舊先把舊的武器都刪掉
        --if self.m_WeaponData then
        if self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject then
            GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject)
            self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject = nil  
            self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers = nil
            self.m_WeaponData[WEAPON_HAND_STR.Right].MeshFilters = nil

        end
        
        if self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject then
            GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject)
            self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject = nil
            self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers = nil
            self.m_WeaponData[WEAPON_HAND_STR.Left].MeshFilters = nil

        end
        --end
        if _WeaponItemId ~= 0 then
            D.LogError("_WeaponAppearance is nil")
        end
    end
end

function ModelController:SetRelease()

    self:CancelRelease()
    self.m_RecordRelease = HEMTimeMgr.SystemTime()
    --十分鐘後清除
    --HEMTimeMgr.DoFunctionDelay(30 ,self.m_ReleaseFunction)
end

function ModelController:CancelRelease()
    if self.m_RecordRelease then
        --HEMTimeMgr.CancelDoFunctionDelay(self.m_ReleaseFunction)
        self.m_RecordRelease = nil
    end
end

function ModelController:IsRelease(iUnload)
    self:CancelRelease()
    if self.m_RoleController then
        self.m_RoleController.m_ModelController = nil
        self:SetRoleController()
    end

    if self.m_PlayerID then
        local _PlayerMC
        if RoleMgr.GetPlayerRC() then
            _PlayerMC = RoleMgr.GetPlayerRC():GetModelController()
        end

        if not Extension.IsUnityObjectNull(self.transform) then
            self.transform.gameObject:SetActive(false)
        else
            --return
            D.LogError("Reset MC Error")
        end
    
        ---動畫已預載至 local m_Table_Animator
        if self:CheckAnimator() then
            ---TODO AseetBundle.Unload
            self:SetAniFloat(AnimationHash.Float_Movement, 0.0)
            --ResourceMgr.Unload(self.m_Animator.transform.gameObject.name)
            -- self.m_Animator = nil
        end
    
        if self.m_Material then
            --ResourceMgr.Unload(self.m_Material.name)
        end
    
        -- self.m_Material = nil
        table.Clear(self.m_Table_IsLoading)
        -- table.Clear(self.m_CharacterBaseModel)
    
        -- 歸還特效
        if self.m_Effect then
            for key, value in pairs(self.m_Effect) do
                EffectMgr.ReturnEffect(value)
            end
        end
        
        if self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject then
            local _name = self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.name
            GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject)
            AppearanceMgr.RemoveLoadedCosmetic(self, _name)
            self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject = nil
        end
        if self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject then
            local _name = self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.name
            GameObject.Destroy(self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject)
            AppearanceMgr.RemoveLoadedCosmetic(self, _name)
            self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject = nil
        end
    
        -- if iUnload then
        --     --釋放已暫存的模型
        --     for key, _ in pairs(self.m_Loaded_Model) do
        --         if _PlayerMC == nil or (type(key) == "string" and (_PlayerMC.m_Loaded_Model[key] == nil)) then
        --             ResourceMgr.Unload(key)
        --         end
        --     end
        -- end

        AppearanceMgr.SetLoadedCosmetic(self, NewCBM())

        self.m_Loaded_Model = {}
    
    else
        if not Extension.IsUnityObjectNull(self.m_ModelObject) then
            GameObject.Destroy(self.m_ModelObject)
        end
        if iUnload then
            if table.Count(self.m_Loaded_Model) then
                for k,v in pairs(self.m_Loaded_Model) do
                    AppearanceMgr.RemoveLoadedCosmetic(self, k)
                end
            end
        end
    end

    self.m_IsReleased = true
    setmetatable(self, nil)
end


--region Animator
---檢查 Animator 相關是否已設定完成
---@return boolean
function ModelController:CheckAnimator()
    -- 檢查玩家或NPC模型是否顯示
    if not Extension.IsUnityObjectNull(self.m_ModelObject) then
        if not self.m_ModelObject.activeInHierarchy then
            return false
        end
    elseif not Extension.IsUnityObjectNull(self.gameObject) then
        if not self.gameObject.activeInHierarchy then
            return false
        end
    end

    local _Temp = self.m_Table_IsLoading["Animator"]
    if self.m_Table_IsLoading["Animator"] == nil then
        D.Log("m_Table_IsLoading Animator Is nil: " .. self.transform.gameObject.name)
        return false
    end
    if self.m_Table_IsLoading["Animator"] == false then
        D.Log("m_Table_IsLoading Is false: " .. self.transform.gameObject.name)
        return false
    end
    if self.m_Animator == nil then
        D.Log("m_Animator Is nil: " .. self.transform.gameObject.name)
        return false
    end
    self:CheckRuntimeAnimator()
    return true
end

---如果沒正在跑的動畫就幫他補
function ModelController:CheckRuntimeAnimator()
    --if self:CheckAnimator() then
        --local _anim = self.m_Animator.runtimeAnimatorController
        if Extension.IsUnityObjectNull(self.m_Animator) or Extension.IsUnityObjectNull(self.m_Animator.runtimeAnimatorController) then
            if self.m_DebugIdentifyName then
                D.Log("Check runtimeAnimator Is nil: " .. self.m_DebugIdentifyName)
            end

            if self.m_AppearanceInfo then
                self:LoadAnimation(GetAnimatorFullName( GetHumanAnimatorName, self.m_AppearanceInfo.m_WeaponType, self.m_AppearanceInfo.m_Gender ))
            elseif self.m_NPCAppearData and self.m_NPCAppearData.m_AcName ~= 0 then
                self:LoadAnimation(GetAnimatorFullNameByStr(self.m_NPCAppearData.m_AcName))
            end
        end
    --end
end

--- 設定移動動畫速度
function ModelController:SetCharacterAnimatorMoveSpeed(iValue)
    local _LerpSpeed = 0
    
    -- 檢查玩家或NPC模型是否顯示
    if not Extension.IsUnityObjectNull(self.m_ModelObject) then
        if not self.m_ModelObject.activeInHierarchy then
            return
        end
    elseif not Extension.IsUnityObjectNull(self.gameObject) then
        if not self.gameObject.activeInHierarchy then
            return
        end
    end
    
    if Extension.IsUnityObjectNull(self.m_Animator) then
        return
    end
    
    local _NowSpeed = self.m_Animator:GetFloat(AnimationHash.Float_Movement)

    if iValue ~= 0 then
        _LerpSpeed = Mathf.Lerp(_NowSpeed, iValue, 2 * Time.deltaTime) -- * 2 只是一個修正值
    end

    -- TODO 走路停止的 code，還沒實作 AC 先註解起來
    -- if _LerpSpeed <= 0 then
    --     local _IsBattle = self.m_Animator:GetBool(AnimationHash.Bool_IsBattle)
    --     if _NowSpeed ~= 0 then
    --         if _NowSpeed <= ModelSetting.m_StopMoveSpeedLine then
    --             if _IsBattle then
    --                 self:SetAniTrigger(AnimationHash.Trigger_StopBattleWalk)
    --             else
    --                 self:SetAniTrigger(AnimationHash.Trigger_StopWalk)
    --             end
    --         else
    --             if _IsBattle then
    --                 self:SetAniTrigger(AnimationHash.Trigger_StopBattleRun)
    --             else
    --                 self:SetAniTrigger(AnimationHash.Trigger_StopRun)
    --             end
    --         end
    --     end
    -- end

    self:SetAniFloat(AnimationHash.Float_Movement, _LerpSpeed)
end

--- 隱身
function ModelController:SetSneakEffect(iIsShow)
    --D.LogError("iIsShow: " .. tostring(iIsShow))
    local _sneakShader = Shader.Find(SNEAKSHADER)

    if not self:IsModelObject() then
        return
    end
    
    local _isNowSneakShader = self.m_SkinnedMeshRenderer[0].material.shader:Equals(_sneakShader)

    if iIsShow and not _isNowSneakShader then
        self.m_OriginShader = self.m_SkinnedMeshRenderer[0].material.shader

        ---替換左手武器 右手武器的shader
        self:ResetWeaponShader( true,self.m_SkinnedMeshRenderer[0].material.shader)
        self:ResetWeaponShader( false,self.m_SkinnedMeshRenderer[0].material.shader)
    end

    if self.m_OriginShader ~= nil then
        if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
            for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
                self.m_SkinnedMeshRenderer[i].material.shader = iIsShow and _sneakShader or self.m_OriginShader
            end
        end

        local _Shader = iIsShow and _sneakShader or self.m_OriginShader
        ---替換左手武器 右手武器的shader
        self:ResetWeaponShader( true,_Shader)
        self:ResetWeaponShader( false,_Shader)
    end

    if not iIsShow and _isNowSneakShader then
        --self:StartScan(true)
        self:StartDissolve(true)
    end
end

---特效演出用，暫不開放玩家使用
function ModelController:SetHologramModel(isHologram, iHologramType)
    local _HologramShader = Shader.Find(HOLOGRAMSHADER)
    if not self:IsModelObject() then
        return
    end

    local _isNowHologramShader = self.m_SkinnedMeshRenderer[0].material.shader:Equals(_HologramShader)

    if isHologram and not _isNowHologramShader then
        if self.m_OriginShader == nil then
            self.m_OriginShader = self.m_SkinnedMeshRenderer[0].material.shader
        end
    end

    local _EffectData = iHologramType ~= nil and EffectSetting.m_HologramFXModelEffect[iHologramType] 
    if not _EffectData then
        isHologram = false
    end

    if self.m_OriginShader ~= nil then
        if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
            for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
                self.m_SkinnedMeshRenderer[i].material.shader = isHologram and _HologramShader or self.m_OriginShader
            end
        end

        if isHologram then
            --或許可以寫設定Shader用的結構
            self.m_MaterialPropertyBlock:Clear()
            for k,v in pairs(_EffectData) do
                if type(v) == "number" then
                    --其他的當成float就行了
                    self.m_MaterialPropertyBlock:SetFloat(Shader.PropertyToID( k ), v)
                elseif v.r then
                    --有r的是顏色
                    self.m_MaterialPropertyBlock:SetColor(Shader.PropertyToID( k ), v)
                elseif v.w then
                    --有w就是V4
                    self.m_MaterialPropertyBlock:SetVector(Shader.PropertyToID( k ), v)
                end
            end

            if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
                for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
                    self.m_SkinnedMeshRenderer[i]:SetPropertyBlock(self.m_MaterialPropertyBlock)
                end
            end
        end
    end

    if isHologram and not _isNowHologramShader then
        self:StartScan(isHologram)
    end
end

---切換武器的Shader 目前(2024.9.10)只用在隱身
---@param iIsLeft bool 要換的是左手
---@param iShader Shader 要換成甚麼Shader
function ModelController:ResetWeaponShader( iIsLeft,iShader)

        local _RendererAy 

        if self.m_WeaponData == nil  then
            return
        end

        if iIsLeft and self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject ~=nil  then
            _RendererAy = self.m_WeaponData[WEAPON_HAND_STR.Left].Renderers
        elseif iIsLeft == false and self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject ~=nil  then
            _RendererAy = self.m_WeaponData[WEAPON_HAND_STR.Right].Renderers
        end

        if _RendererAy ~= nil  then
            for i = 0, _RendererAy.Length -1 do
                ---如果武器被掛particle特效 必須排除 避免particle特效被加不必要的材質
                local _IsLOD = GString.CheckStartString(_RendererAy[i].gameObject.name, WEAPON_OBJECT_LOD_NAME_PRE)
                if _IsLOD then
                    _RendererAy[i].material.shader = iShader
                end
            end
        end
end

--region Animator 控制相關
function ModelController:SetAniInteger(iAtkHash, iAtkId)
    if not self:CheckAnimator() then
        do return end
    end
    self.m_Animator:SetInteger(iAtkHash, iAtkId)
end

function ModelController:SetAniBool(iHash, iBool)
    if(not self:CheckAnimator())then
        do return end
    end
    self.m_Animator:SetBool(iHash, iBool)
end

function ModelController:SetAniFloat(iHash, iFloat)
    if(not self:CheckAnimator())then
        do return end
    end
    self.m_Animator:SetFloat(iHash, iFloat)
end

function ModelController:SetAniTrigger(iHash)
    if(not self:CheckAnimator())then
        do return end
    end
    self.m_Animator:SetTrigger(iHash)
end
--endregion Animator 控制相關
--endregion Animator

--region 骨架相關
EParticleVec = {
    Non = 0,
    ---角色Rot
    Role = 1,
    ---骨位Rot
    BonePos = 2
}

---獲得骨架
---@param iBoneIdx EBoneIdx 骨架編號
---@return Transform 骨架Transform
function ModelController:GetBone(iBoneIdx)
    local _Result = nil
    if self.m_table_Bones ~= nil and table.Count(self.m_table_Bones) > 0 then
        local _Key = table.GetKey(EBoneIdx, iBoneIdx)
        if EBoneIdx[_Key] == EBoneIdx.Foot then
            _Result = self.m_BattleAtkFootEffectPos
        elseif EBoneIdx[_Key] == EBoneIdx.TargetGround then
            --Demo模型的話，直接取
            if(self.m_IsWugongDemoMC) then
                _Result = self.m_WugongDemoTarget and self.m_WugongDemoTarget.transform or nil
            else
                --通常前面就會被濾掉了，會出現在這邊是例外狀況或戰鬥編輯器(?
                _Result = BattleMgr.m_Target and BattleMgr.m_Target.transform or nil
            end
        else
            _Result = self.m_table_Bones[EBoneName[_Key]]
        end
    end

    -- Npc類型不會記所有的骨架位置，直接給予該模型位置
    if _Result == nil then
        _Result = self.transform
    end

    return _Result
end

---取出玩家目前模型的骨架(場景動畫專用)
function ModelController:GetBaseBiped()
    if self.m_Material then
        self.m_Material:SetColor(xrayColorName, Color.Black)
    end

    local _AllTrans = {}
    for i = 0, self.m_ModelObject.transform.childCount - 1 do
        _AllTrans[i] = self.m_ModelObject.transform:GetChild(i)
    end


    return _AllTrans
end
--endregion 骨架相關

--region Animation 相關
function ModelController:ResetAnimator(iForce)
    local _Animator = self.m_Animator
    -- for key, value in pairs(_Animator.parameters) do
    --     if value.type == AnimatorControllerParameterType.Bool then
    --         self:SetAniBool(value.nameHash, false)
    --     end
    --     if value.type == AnimatorControllerParameterType.Float then
    --         self:SetAniFloat(value.nameHash, 0)
    --     end
    --     if value.type == AnimatorControllerParameterType.Bool then
    --         self:SetAniInteger(value.nameHash, 0)
    --     end
    -- end

    if Extension.IsUnityObjectNull(_Animator) then
        return
    end

    if _Animator.isHuman then
        self:SetAniInteger(AnimationHash.Int_ShowID, 0)
        ---已經是移動動畫或是可以跟位移混搭的不用切
        local _nowClip = _Animator:GetCurrentAnimatorStateInfo(0):IsName("Movement")
        local _BattleClip = _Animator:GetCurrentAnimatorStateInfo(0):IsName("Battle")
        local _CeaseFireClip = _Animator:GetCurrentAnimatorStateInfo(0):IsName("Ceasefire")
        local _isBattle = self.m_Animator:GetBool(AnimationHash.Bool_IsBattle)
        if iForce or (not _nowClip and not _BattleClip and not _CeaseFireClip and not _isBattle) then
            _Animator:Play(AnimationHash.State_Movement)
        end
    end
end

--- 設定戰鬥動作
function ModelController:SetAtk(iId)
    self:SetAniInteger(AnimationHash.Int_AtkID, iId)
    self:SetAniTrigger(AnimationHash.Trigger_Atk)
end

--- 設定動作播放速度
function ModelController:SetAtkSpeed(iSpeed)
    self:SetAniFloat(AnimationHash.Float_AtkSpeed, iSpeed)
end

---設定戰鬥待機狀態
---@param iIsBattle boolean 是否設定成戰鬥狀態
function ModelController:SetBattleNormalState(iIsBattle)
    if(not self:CheckAnimator())then
        do return end
    end
    
    if self.m_Animator:GetBool(AnimationHash.Bool_IsBattle) ~= iIsBattle then
        BattleMgr.ShowAnimationLog("ModelController SetBattleNormalState: "..tostring(iIsBattle))
        self:SetAniBool(AnimationHash.Bool_IsBattle, iIsBattle)
    end
end

---戰鬥狀態
function ModelController:SetBattleState()
    if(not self:CheckAnimator())then
        do return end
    end

    self.m_Animator:Play(AnimationHash.State_Battle) 
end

--region 核心技變色相關

---核心技特效狀態
local ECoreState =
{
    IsCoreState = 1,
    CoreStateOverSlow = 2,
    CoreStateOverFast = 3,
}

---取消現在的核心技特效狀態
local function CancelCoreTween(iModelController)
    if iModelController.m_EndingCoreEffectId then
        LeanTween.cancel(iModelController.m_EndingCoreEffectId)
        iModelController.m_EndingCoreEffectId = nil
    end

end

---換武器時換顏色
function ModelController:SwitchBattleSuitEffect(isTween)
    if self:IsModelObject() then
        if self.m_AppearanceInfo.m_Cosmetics[ItemEquipPos.Cloth] == CreateRoleSetting.m_CreateModelItemId then
            self:SetEmmsiveColor(ModelBodyPartString.Body, Color.WugongColor[self.m_AppearanceInfo.m_WeaponType])
            if self.m_AppearanceInfo.m_ActiveBattleSuit then
                self.m_CoreState = nil
                self:StartBattleSuitEffect()
            else
                self:ResetBattleSuitEffect(isTween)
            end
        end
    end
end

---核心技開啟時發光
function ModelController:StartBattleSuitEffect()
    self:UpdateCoreTween(ECoreState.IsCoreState)
end

---核心技結束前的閃爍
function ModelController:EndingBattleSuitEffect(iTime)
    if iTime < EffectSetting.m_BattleSuitEffectEndDuration and iTime > EffectSetting.m_BattleSuitEffectEndDuration / 2 then    
        self:UpdateCoreTween(ECoreState.CoreStateOverSlow)
    elseif iTime < EffectSetting.m_BattleSuitEffectEndDuration / 2 then
        self:UpdateCoreTween(ECoreState.CoreStateOverFast)
    end
end

---重置核心技光亮值
function ModelController:ResetBattleSuitEffect(isTween)
    CancelCoreTween(self)
    self.m_CoreState = nil
    
    local _toValue = EffectSetting.m_BattleSuitEffect.m_RecoverValue

    if self.m_RoleController and self.m_RoleController.m_IsSelf then
        local _hotkeyUnit = HotKeyMgr.GetHotKeyUnit( EHotkeyArea.BattleAtkArea, PlayerData.GetWeaponType(), SkillData.ESkillType.CoreSkill )
        local _isCD = CDMgr.IsCD( EIconType.Skill, _hotkeyUnit.m_Idx )

        _toValue = _isCD and EffectSetting.m_BattleSuitEffect.m_StartValue or EffectSetting.m_BattleSuitEffect.m_RecoverValue
    end

    if isTween then
        LeanTween.value(self.m_ModelObject, System.Action_float(function(iValue) 
                if not self.m_IsReleased then
                    self:SetEmmsiveIntense(ModelBodyPartString.Body,iValue) 
                end
            end),
            EffectSetting.m_BattleSuitEffect.m_EndValue,
            _toValue,
            EffectSetting.m_BattleSuitEffect.m_EffectTimeSlow)
    else
        self:SetEmmsiveIntense(ModelBodyPartString.Body,_toValue)
    end
end

---刷新tween的亮度
function ModelController:UpdateCoreTween(iState)

    local function SetBodyEmmsiveIntense(iValue)
        if not self.m_IsReleased then
            self:SetEmmsiveIntense(ModelBodyPartString.Body,iValue)
        end
    end

    if self.m_CoreState ~= iState then
        CancelCoreTween(self)
    else
        return
    end
    self.m_CoreState = iState

    if self.m_CoreState == ECoreState.IsCoreState then
        LeanTween.value(self.m_ModelObject, System.Action_float(SetBodyEmmsiveIntense),
        EffectSetting.m_BattleSuitEffect.m_StartValue,
        EffectSetting.m_BattleSuitEffect.m_EndValue,
        EffectSetting.m_BattleSuitEffect.m_StartTweenTime)
    elseif self.m_CoreState == ECoreState.CoreStateOverSlow then
        self.m_EndingCoreEffectId = LeanTween.value(self.m_ModelObject, System.Action_float(SetBodyEmmsiveIntense),
        EffectSetting.m_BattleSuitEffect.m_StartValue,
        EffectSetting.m_BattleSuitEffect.m_EndValue,
        EffectSetting.m_BattleSuitEffect.m_EffectTimeSlow):setLoopPingPong(200).id
    elseif self.m_CoreState == ECoreState.CoreStateOverFast then
        self.m_EndingCoreEffectId = LeanTween.value(self.m_ModelObject, System.Action_float(SetBodyEmmsiveIntense),
        EffectSetting.m_BattleSuitEffect.m_StartValue,
        EffectSetting.m_BattleSuitEffect.m_EndValue,
        EffectSetting.m_BattleSuitEffect.m_EffectTimeFast):setLoopPingPong(200).id
    end
end

--endregion



---切換武器的動畫
function ModelController:SwitchWeaponAni(iCompleteDelegate)
    
    --切換外功要先載入動畫
    self:LoadAnimation(GetAnimatorFullName( GetHumanAnimatorName, self.m_AppearanceInfo.m_WeaponType, self.m_AppearanceInfo.m_Gender ), 
    function() 
            if iCompleteDelegate then
                iCompleteDelegate()
            else
                if self.m_RoleController == nil or self.m_RoleController.m_StateController:GetStateType() ~= EStateType.Move then
                    self:SetAniTrigger(AnimationHash.Trigger_Switch)
                end
            end
        end
    )
end

--- 死亡狀態
function ModelController:SetDie(iIsDie)
    if(not self:CheckAnimator())then
        do return end
    end

    BattleMgr.ShowAnimationLog("ModelController SetDie: "..tostring(iIsDie))
    self:SetAniBool(AnimationHash.Bool_IsDie, iIsDie)
    if iIsDie then
        self.m_Animator:SetTrigger(AnimationHash.Trigger_Die)
    end
end

--- 開始採集狀態
---@param iActNo number 設定所需要播放的採集動作編號
---@param iSec number 採集要演多久
function ModelController:SetCollect(iActNo,iSec)
    if(not self:CheckAnimator())then
        do return end
    end
    ---是否為採集狀態
    local _Collect = iActNo and iActNo > 0
    self:SetAniBool(AnimationHash.Bool_IsCollect, _Collect) 
    self:SetAniBool(AnimationHash.Bool_IsPickUp, _Collect) 
    if _Collect then
        if(iActNo == 1) then
            self.m_Animator:SetTrigger(AnimationHash.Trigger_Collect)
        end
        if (iActNo == 2)then
            self.m_Animator:SetTrigger(AnimationHash.Trigger_PickUp)
        end
        ---演出採集特效 (生成特效在胸前  配合動畫的雙手會同時搓動)
        ---物件掛在模型的哪個位置
		local _EffectParent = self:GetBone(EBoneIdx.Chest)
        ---EffectMgr登記的hashcode 最後歸還物件要用
		local _HashCode 	
		EffectMgr.PlayEffectWithParent(EEffectType.Model, ModelController.COLLECT_EFFECT_ID, _EffectParent, false,
	function(iHashCode)
							_HashCode = iHashCode
							---調整相對位置
							local _EffectObj = EffectMgr.GetEffectObjByHashCode(_HashCode)
							_EffectObj.transform.localPosition = Vector3(0,-0.9,-0.3)
						end)

		---從給的時間來決定多久後退出採集演出 並歸還特效
		LeanTween.delayedCall(iSec, System.Action(
		function()
            self:SetAniBool(AnimationHash.Bool_IsCollect, false)
			EffectMgr.ReturnEffect(_HashCode)
		end))
        LeanTween.delayedCall(iSec, System.Action(
		function()
            self:SetAniBool(AnimationHash.Bool_IsPickUp, false)
			EffectMgr.ReturnEffect(_HashCode)
		end))
    end
end

--- 擊飛 Y 軸位移
---@param iLimitHeight number 限制高度
---@param iToTopTime number 到最高點時間
---@param iToGroundTime number 到最低點時間
---@param iFlyTime number 總飛行時間
function ModelController:SetMoveY(iLimitHeight, iFlyTime, iToTopTime, iToGroundTime)
    local _StayTime = iFlyTime - iToTopTime - iToGroundTime
    --起始點
    local _StartPos = self.transform.localPosition
    
    -- 確認當前有沒有正在擊飛
    if self.m_FlyTween then
        -- 打斷
        LeanTween.cancel(self.m_FlyTween)
    end

    -- 第一層，飛到最高點
    self.m_FlyTween = LeanTween.value(
            self.m_ModelObject,
            --更新檢查最高點限制
            System.Action_float(function(iValue)
                if _StartPos.y + iValue >= iLimitHeight then
                    self.transform.localPosition = Vector3(_StartPos.x, iLimitHeight, _StartPos.z)
                else
                    self.transform.localPosition = Vector3(_StartPos.x, _StartPos.y + iValue, _StartPos.z)
                end
            end),
            0,
            iLimitHeight,
            iToTopTime):setOnComplete(
            -- 第二層，滯空
            System.Action(function()
                self.m_FlyTween = LeanTween.move(
                        self.m_ModelObject, 
                        self.m_ModelObject.transform.position, 
                        _StayTime):setOnComplete(
                        -- 第三層，落下
                        System.Action(function()
                            self.m_FlyTween = LeanTween.value(
                                    self.m_ModelObject,
                                    System.Action_float(function(iValue)
                                        self.transform.localPosition = Vector3(_StartPos.x, iValue, _StartPos.z)
                                    end),
                                    iLimitHeight,
                                    0,
                                    iToGroundTime):setOnComplete(
                                    System.Action(function() 
                                        self.m_FlyTween = nil
                                        self:SetAniBool(AnimationHash.Bool_IsFly, false)
                                    end)).id

                            self:SetAniTrigger(AnimationHash.Trigger_EndFly);
                        end)).id
            end)
    ).id
end

--region Animation event
--- 設定 Animation Event
function ModelController:SetAnimationEvent(iEventData, iActionIndex)
    local _Result = {}
    if self.m_AnimationClipDict == nil then
        return
    end
    if not self.m_AnimationClipDict:ContainsKey(iEventData.m_ActID) then
        D.Log("[ModelController] m_AnimationClipDict 不含此ActID:"..iEventData.m_ActID)
        return
    end
    local _AnimationClip = self.m_AnimationClipDict[iEventData.m_ActID]
    _AnimationClip.events = {}
    local _EventIndexCounter = {
        [EAnimationEvent.OnParticle] = 0,
        [EAnimationEvent.OnSound] = 0,
        [EAnimationEvent.OnCamera] = 0,
        [EAnimationEvent.OnPostProcess] = 0,
        [EAnimationEvent.OnDelegate] = 0,
        [EAnimationEvent.OnEnd] = 0
    }
    for i = 1 , table.Count(iEventData.m_Ay_Event) do
        local _Data = iEventData.m_Ay_Event[i]
        local _Event = UnityEngine.AnimationEvent.New()
        _Event.functionName = _Data.m_Name
        _Event.time = _Data.m_Timing

        if _EventIndexCounter[_Data.m_Name] then
            _EventIndexCounter[_Data.m_Name] = _EventIndexCounter[_Data.m_Name] +1
            _Event.intParameter = _EventIndexCounter[_Data.m_Name] -- 用來存事件內的事件索引
            _Event.floatParameter = iActionIndex -- 用來存Act的索引，之後用於檢查觸發事件是否正確
        end
        
        _AnimationClip:AddEvent(_Event)

        if not _Result[_Event.functionName] then
            _Result[_Event.functionName] = {}
        end
        table.insert(_Result[_Event.functionName], _Data)
    end

    return _Result--[[, _AnimationClip]]
end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnParticle(iSelf, iIndex, iActIndex, iPlayingSkillActData)
    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end

    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnParticle, iIndex, iActIndex) then
            return
        end
        
        local _result = iPlayingSkillActData:CheckLoopAction(EAnimationEvent.OnParticle ,iIndex)
        if not _result then
            return
        end

        if not iPlayingSkillActData.m_eventsData[EAnimationEvent.OnParticle] then
            BattleMgr.ShowLog("NowActionData's ParticleAy is nil", "red")
            do
                return
            end
        end
    
        if iPlayingSkillActData:SetNextParticle(iIndex) then
            if not m_ISSHOWACTIONEFFECT then
                do
                    return
                end
            end
            
            local _AtkSpeed = _selfRC and _selfRC.m_AtkSpeed or 1
            local _ActEffectData = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnParticle][iPlayingSkillActData.m_ParticleIndex]
            local _EffectData = EffectData.New()
            
            _EffectData.m_TickID = _ActEffectData.m_TickID
            _EffectData.m_ID = _ActEffectData.m_ID
            _EffectData.m_Speed = _ActEffectData.m_Speed * _AtkSpeed
            _EffectData.m_Scale = _ActEffectData.m_isFollow and _ActEffectData.m_Scale or _ActEffectData.m_Scale * iSelf.transform.localScale.x
            _EffectData.m_BonePos = _ActEffectData.m_BonePos
            _EffectData.m_Vector = _ActEffectData.m_Vector
            _EffectData.m_isFollow = _ActEffectData.m_isFollow
            _EffectData.m_isFollowRotation = _ActEffectData.m_isFollowRotation
            _EffectData.m_isLoop = _ActEffectData.m_isLoop
            _EffectData.m_LoopTimes = _ActEffectData.m_LoopTimes

            --目標腳下的例外狀況
            if _EffectData.m_BonePos == EBoneIdx.TargetGround and iPlayingSkillActData.m_TargetSID then
                local _TargetRC = RoleMgr.Get(iPlayingSkillActData.m_TargetSID)
                if _TargetRC == nil then
                    _TargetRC = NPCMgr.GetNPCController(iPlayingSkillActData.m_TargetSID)
                end

                if _TargetRC then
                    _EffectData.m_BonePos = EBoneIdx.Foot
                    _TargetRC:SetEffect(_EffectData)
                end
            else
                iSelf:SetEffect(_EffectData)
            end
            
            --iSelf:PlayActionParticle()
        end
        
        BattleMgr.ShowAnimationLog("RoleController OnParticle")
    end

    -- if iSelf.m_RoleController and iSelf ~= AppearanceMgr.GetDefaultModel() then
    --     RoleController.OnParticle(iSelf.m_RoleController, iIndex, iActIndex)
    --     BattleMgr.ShowAnimationLog("ModelController OnParticle")
    --     -- BattleMgr.ShowAnimationLog("ModelController OnParticle: " .. tostring(self.m_PlayerID))
    --     -- BattleMgr.ShowAnimationLog("ModelController OnParticle: " .. tostring(self.m_NPCID))    
    -- end
end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnPlaySound(iSelf, iIndex, iActIndex, iPlayingSkillActData)
    
    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end

    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnSound, iIndex, iActIndex) then
            return
        end
        
        local _result = iPlayingSkillActData:CheckLoopAction(EAnimationEvent.OnSound, iIndex)
        if not _result then
            return
        end

        if not iPlayingSkillActData.m_eventsData[EAnimationEvent.OnSound] then
            BattleMgr.ShowLog("NowActionData's SoundAy is nil", "red")
            do
                return
            end
        end
        
        if iPlayingSkillActData:SetNextSound(iIndex) then
            if not m_ISSHOWACTIONEFFECT then
                do
                    return
                end
            end

            BattleMgr.ShowAnimationLog("RoleController OnPlaySound")
            local _MixerGroup
            -- 透過戰鬥編輯器播放可能會沒有SkillData
            local _SkillData = iPlayingSkillActData.m_SkillData
            if not _SkillData then
                -- 預設塞玩家技能音
                _MixerGroup = AudioMgr.EMixerGroup.PC_SKL
            elseif iSelf.m_NPCAppearData then
                _MixerGroup = _SkillData.m_SkillType ~= 1 and AudioMgr.EMixerGroup.NPC_SKL or AudioMgr.EMixerGroup.NPC_ATK
            else
                _MixerGroup = _SkillData.m_SkillType ~= 1 and AudioMgr.EMixerGroup.PC_SKL or AudioMgr.EMixerGroup.PC_ATK
            end

            local _ConditionalTickID = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnSound][iPlayingSkillActData.m_SoundIndex].m_TickID

            if _ConditionalTickID and _ConditionalTickID ~= 0 then
                if not TickData.CanTriggerConditionalTick(_ConditionalTickID, _selfRC) then
                    return
                end
            end

            local _AudioId = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnSound][iPlayingSkillActData.m_SoundIndex].m_ID
            if _AudioId ~= 0 then
                local _AtkSpeed = 1
                if _selfRC then
                    _AtkSpeed = _selfRC.m_AtkSpeed
                end
                AudioMgr.PlayAudio(AudioMgr.EAudioType.Sound, _MixerGroup, _AudioId, iSelf.transform, true, nil,
                        function(iHash)
                            iPlayingSkillActData:SetAudioHash(iHash)
                        end, _AtkSpeed)
            end
        end
        
        BattleMgr.ShowAnimationLog("ModelController OnPlaySound")
    end
end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnPlayCameraEffect(iSelf, iIndex, iActIndex, iPlayingSkillActData)

    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end

    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnCamera, iIndex, iActIndex) then
            return
        end

        local _result = iPlayingSkillActData:CheckLoopAction(EAnimationEvent.OnCamera, iIndex)
        if not _result then
            return
        end

        if not iPlayingSkillActData.m_eventsData[EAnimationEvent.OnCamera] then
            BattleMgr.ShowLog("NowActionData's CameraAy is nil", "red")
            do
                return
            end
        end
        
        if iPlayingSkillActData:SetNextCamEffect(iIndex) then
            if not m_ISSHOWACTIONEFFECT then
                do
                    return
                end
            end

            local _OnCameraEvent = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnCamera][iPlayingSkillActData.m_CamEffectIndex]
    
            -- 是玩家本人或有設定玩家可以感受到震動
            if _selfRC == nil or _selfRC ~= RoleMgr.m_RC_Player and not _OnCameraEvent.m_is_GlobalShake then
                return
            end

            if _OnCameraEvent.m_TickID and _OnCameraEvent.m_TickID ~= 0 then
                if not TickData.CanTriggerConditionalTick(_OnCameraEvent.m_TickID, _selfRC) then
                    return
                end
            end
            
            CameraMgr.OnShake(
                    _OnCameraEvent.m_ID,
                    _OnCameraEvent.m_Amplitude, 
                    _OnCameraEvent.m_Frequency,
                    _OnCameraEvent.m_Duration, 
                    _OnCameraEvent.m_EaseType)
        end
        
        BattleMgr.ShowAnimationLog("ModelController OnPlayCameraEffect")
    end

end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnPostProcess(iSelf, iIndex, iActIndex, iPlayingSkillActData)
    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end

    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnPostProcess, iIndex, iActIndex) then
            return
        end

        local _result = iPlayingSkillActData:CheckLoopAction(EAnimationEvent.OnPostProcess, iIndex)
        if not _result then
            return
        end

        if not iPlayingSkillActData.m_eventsData[EAnimationEvent.OnPostProcess] then
            BattleMgr.ShowLog("NowActionData's PostProcessAy is nil", "red")
            do
                return
            end
        end

        if iPlayingSkillActData:SetNextPostProcess(iIndex) then
            if not m_ISSHOWACTIONEFFECT then
                do
                    return
                end
            end

            ---@class PostProcessData
            local _OnProcessEvent = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnPostProcess][iPlayingSkillActData.m_PostProcessIndex]

            -- 是玩家本人或有設定玩家可以感受到震動
            if _selfRC == nil or _selfRC ~= RoleMgr.m_RC_Player and not _OnProcessEvent.m_is_GlobalEffect then
                return
            end

            if _OnProcessEvent.m_TickID and _OnProcessEvent.m_TickID ~= 0 then
                if not TickData.CanTriggerConditionalTick(_OnProcessEvent.m_TickID, _selfRC) then
                    return
                end
            end

            Main_SubCtrl_FullScreenEffect.ShowBattlePostProcess(_OnProcessEvent, true)
        end
        
        BattleMgr.ShowAnimationLog("ModelController OnPostProcess")
    end
end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnDelegate(iSelf, iIndex, iActIndex, iPlayingSkillActData)

    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end

    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnDelegate, iIndex, iActIndex) then
            return
        end

        local _result = iPlayingSkillActData:CheckLoopAction(EAnimationEvent.OnDelegate, iIndex)
        if not _result then
            return
        end

        if not iPlayingSkillActData.m_eventsData[EAnimationEvent.OnDelegate] then
            BattleMgr.ShowLog("NowActionData's DelegateAy is nil", "red")
            do
                return
            end
        end

        if iPlayingSkillActData:SetNextDelegate(iIndex) then
            if not m_ISSHOWACTIONEFFECT then
                do
                    return
                end
            end

            BattleMgr.ShowAnimationLog("RoleController OnDelegate")
            
            -- 執行委派
            local _OnDelegateEvent = iPlayingSkillActData.m_eventsData[EAnimationEvent.OnDelegate][iPlayingSkillActData.m_DelegateIndex]
            if not _OnDelegateEvent then
                return
            end

            if _OnDelegateEvent.m_TickID and _OnDelegateEvent.m_TickID ~= 0 then
                if not TickData.CanTriggerConditionalTick(_OnDelegateEvent.m_TickID, _selfRC) then
                    return
                end
            end
            
            local _paramTable = {}
            if _OnDelegateEvent.m_ID == 1 then
                _paramTable.m_SelfMC = iSelf
            end
            
            SkillActDelegateMgr.TriggerDelegate(_OnDelegateEvent.m_ID, _paramTable)
        end

        BattleMgr.ShowAnimationLog("ModelController OnDelegate")
    end
end

-- function ModelController:EventRunPace()
--     BattleMgr.ShowAnimationLog("ModelController EventRunPace")
-- end

---@param iSelf ModelController
---@param iIndex number
---@param iActIndex number
---@param iPlayingSkillActData PlayingSkillActData 播放武功動作資料(無RC用)
function ModelController.OnEnd(iSelf, iIndex, iActIndex, iPlayingSkillActData)

    if iSelf == AppearanceMgr.GetDefaultModel() then
        return
    end

    local _selfRC = iSelf.m_RoleController
    if iPlayingSkillActData == nil and _selfRC and iSelf ~= AppearanceMgr.GetDefaultModel() then
        iPlayingSkillActData = _selfRC.m_NowPlayingSkillActData
    end


    if iPlayingSkillActData then
        if not iPlayingSkillActData:CheckEventCorrect(EAnimationEvent.OnEnd, iIndex, iActIndex) then
            return
        end
        iPlayingSkillActData:OnEnd()
    end
end

-- function ModelController:NewEvent()
--     BattleMgr.ShowAnimationLog("ModelController NewEvent")
-- end

-- function ModelController:SwitchEnd()
--     BattleMgr.ShowAnimationLog("ModelController SwitchEnd")
-- end

---好像用不到
function ModelController.OnDead(iSelf, iIndex, iActIndex)
    if iSelf.m_RoleController and iSelf ~= AppearanceMgr.GetDefaultModel() then
        --RoleController.OnDead(iSelf.m_RoleController, iIndex, iActIndex)
        BattleMgr.ShowAnimationLog("ModelController OnDead")
    end

end
--endregion Animation event呼叫

--endregion Animation 相關

--region Effect 相關

--- 清光特效
function ModelController:HardClearEffect()

    if self.m_PlayingEffectTable then
        for key, value in pairs(self.m_PlayingEffectTable) do
            EffectMgr.ReturnEffect(value)
        end
    end
    -- 模型縮放
    self:SetModelSize(1)

    -- 玩家隱身解除
    self:SetSneakEffect(false)
end

---特效播放
function ModelController:SetEffect(iEffectData, iSkillData, iBuffData)
    local _BoneTrans = nil
    
    local function PlayEffect (iBoneTrans, iDelegate, iCompleteDelegate, isLeft)
        -- 計算旋轉
        local _rotation = nil
        --沒RC的MC: 創角、演武、RTM
        local _RC = self.m_RoleController
        
        -- 有目標方向(通常是受擊才有)
        if iEffectData.m_LookAtTransform then
            -- 反向旋轉，對企劃編輯比較直覺
            _rotation = Quaternion.LookRotation(iBoneTrans.position - iEffectData.m_LookAtTransform.position)
            if _rotation then
                _rotation = _rotation.eulerAngles
            end
        else
            local _Transform = _RC and _RC:GetRotationObj() or self.m_ModelObject.transform
            -- 不使用跟隨
            if not iEffectData.m_isFollow then
                if iEffectData.m_Vector == EParticleVec.Non then
                    -- 預設旋轉
                    _rotation = nil
                elseif iEffectData.m_Vector == EParticleVec.BonePos then
                    -- 骨架朝向
                    _rotation = iBoneTrans.rotation.eulerAngles
                elseif _RC then
                    _rotation = _RC:GetlocalEulerAngle()
                else
                    -- 角色朝向
                    _rotation = _Transform.eulerAngles
                end
            else
                if iEffectData.m_Vector == EParticleVec.Non then
                    -- 預設旋轉
                    _rotation = nil
                elseif iEffectData.m_Vector == EParticleVec.BonePos then
                    -- 骨架朝向
                    _rotation = iBoneTrans.rotation.localEulerAngles
                else
                    -- 角色朝向
                    if iEffectData.m_BonePos == EBoneIdx.Non or iEffectData.m_BonePos == EBoneIdx.Foot then
                        -- 只有 BonePos == Foot 或 Non 才會採用是否跟隨旋轉設定
                        if iEffectData.m_isFollowRotation and self.m_BattleAtkFootEffectPos then
                            iBoneTrans = self.m_BattleAtkFootEffectPos
                        else
                            iBoneTrans = _RC and _RC.transform or self.transform
                        end

                        if _RC then
                            _rotation = _RC:GetlocalEulerAngle()
                        else
                            -- 角色朝向
                            _rotation = _Transform.localEulerAngles
                        end
                    else
                        _rotation = Vector3.zero
                    end
                end
            end
        end

        if isLeft then
            if _rotation then
                _rotation = Vector3.New(180, _rotation.y, _rotation.z)
            else
                _rotation = Vector3.New(180, 0, 0)
            end
        end

        local _isImportant = _RC and _RC.m_PlayerID == PlayerData.GetRoleID() or true
        if iBuffData ~= nil and iBuffData.m_SpecialPerform == BuffData.ESpecialPerform.Ammunition then
            -- 彈藥效果特殊處理，使用到武功表的「招式順序(m_SkillOrder)」欄位
            if iSkillData ~= nil then
                if iSkillData.m_SkillOrder == 1 then
                    -- 啟動技能，第一次觸發
                    EffectMgr.PlayEffectWithParent(EEffectType.Model, iEffectData.m_ID, self:GetBone(iEffectData.m_BonePos), false,
                    function (iHash)
                        iDelegate(iHash)
                        self.m_Loop2EffectHash = iHash
                        EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData, iSkillData.m_SkillOrder)
                    end,
                    iCompleteDelegate, nil, nil, nil, nil, true, false)
                elseif iSkillData.m_SkillOrder ~= nil then
                    EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData, iSkillData.m_SkillOrder)
                else
                    -- 移除就不會送 iSkillData.m_SkillOrder
                    EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData)
                    self.m_Loop2EffectHash = nil
                end
            end
        else
            EffectMgr.PlayEffectWithParent(EEffectType.Model, 
                iEffectData.m_ID, 
                iBoneTrans,
                _isImportant, 
                iDelegate,
                iCompleteDelegate, 
                nil, 
                iEffectData.m_Speed,
                iEffectData.m_Scale, 
                _rotation,
                iEffectData.m_isFollow)
        end
    end
    
    local function BaseDelegate(iHash, iBonePos, iIsLeft)
        local _PlayingEffectData = EffectMgr.GetEffectByHashCode(iHash)
        if not _PlayingEffectData then
            return
        end
        
        self.m_PlayingEffectTable[iHash] = iHash
        -- 有武器特效，需要套武器Mesh
        if _PlayingEffectData.EffectLifeTime.m_IsHasWeaponFX then
            local _renderer, mesh = self:GetWeaponMeshAndRenderer(iBonePos, iIsLeft)
            if _renderer and mesh then
                EffectMgr.SetWeaponEffectFX(iHash, nil, nil, _renderer, mesh)
            end
        end
        
        -- 有全息投影特效，需要套相關Mesh
        if _PlayingEffectData.EffectLifeTime.m_IsHasHologramFX then
            -- 目前暫時直接取WeaponMesh，如果有而外需求需要另外開函式
            local _renderer, mesh = self:GetWeaponMeshAndRenderer(iBonePos, iIsLeft)
            EffectMgr.SetHologramEffectFX(iHash, _renderer, mesh)
        end
        
        -- 有消失效果，需要關閉顯示 Renderer
        if _PlayingEffectData.EffectLifeTime.m_IsHasModelInvisible and _PlayingEffectData.m_InvisibleCtrlAy then
            for i = 0, _PlayingEffectData.m_InvisibleCtrlAy.Length -1 do
                HEMTimeMgr.DoFunctionDelay(_PlayingEffectData.m_InvisibleCtrlAy[i].m_StartTime / iEffectData.m_Speed,
                function()
                    -- 開啟人物顯示
                    self:SetModelActive_WithoutChildObject(false)

                    HEMTimeMgr.DoFunctionDelay(_PlayingEffectData.m_InvisibleCtrlAy[i].m_Duration / iEffectData.m_Speed,
                        function()
                            -- 關閉人物顯示
                            self:SetModelActive_WithoutChildObject(true)
                        end)
                end)
            end
        end

        if iEffectData.m_LoadCompleteDelegate then
            iEffectData.m_LoadCompleteDelegate(iHash)
        end
    end
    
    local function BaseCompleteDelegate(iHash)
        local _PlayingEffectData = EffectMgr.GetEffectByHashCode(iHash)
        if not _PlayingEffectData then
            return
        end

        self.m_PlayingEffectTable[iHash] = nil
    end

    if iEffectData.m_TickID and iEffectData.m_TickID ~= 0 then
        if not TickData.CanTriggerConditionalTick(iEffectData.m_TickID, self.m_RoleController) then
            return
        end
    end

    -- 取骨架 Transform
    if iEffectData.m_BonePos == EBoneIdx.BothHand then
        -- 雙手要特殊處理
        local _RelationEffectHash = 0
        if self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject then
            _BoneTrans = self.m_WeaponData[WEAPON_HAND_STR.Left].gameObject.transform
            PlayEffect (
                    _BoneTrans,
                    function(iHash) 
                        _RelationEffectHash = iHash
                        BaseDelegate(iHash, iEffectData.m_BonePos, true)
                    end,
                    BaseCompleteDelegate,
                    true)    
        end

        if self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject then
            _BoneTrans = self.m_WeaponData[WEAPON_HAND_STR.Right].gameObject.transform
            PlayEffect (
                    _BoneTrans,
                    function(iHash) 
                        EffectMgr.SetReturnRelationEffect(iHash, _RelationEffectHash)
                        BaseDelegate(iHash, iEffectData.m_BonePos, false)
                    end,
                    BaseCompleteDelegate,
                    false)    
        end
        
    else
        _BoneTrans = self:GetBone(iEffectData.m_BonePos)
        PlayEffect (_BoneTrans,
                function(iHash) 
                    BaseDelegate(iHash, iEffectData.m_BonePos) 
                end,
                BaseCompleteDelegate)
    end
end

function ModelController:SetLoop2Effect(iEffectData, iSkillData, iStackCount)
    if iSkillData ~= nil then
        if iStackCount == 1 then
            EffectMgr.PlayEffectWithParent(EEffectType.Model, iEffectData.m_ID, self:GetBone(iEffectData.m_BonePos), false,
            function (iHash)
                self.m_Loop2EffectHash = iHash
                EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData, iStackCount)
            end,
            nil, nil, nil, nil, nil, true, false)
        elseif iStackCount ~= nil then
            EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData, iStackCount)
        else
            -- 移除就不會送 iStackCount
            EffectMgr.SetLoop2Effect(self.m_Loop2EffectHash, iSkillData)
            self.m_Loop2EffectHash = nil
        end
    end
end

--- 取得武器Mesh與Renderer(只有武器有MeshRenderer)
function ModelController:GetWeaponMeshAndRenderer(iBonePos, iIsLeft)
    local _Renderer = nil
    local _MeshFilter = nil
    local _WeaponData = self.m_WeaponData
    
    -- 特效需求位置
    if iBonePos >= EBoneIdx.L_W0 and iBonePos <= EBoneIdx.L_W4 then
        -- 左手特效
        -- 有左手武器
        if not Extension.IsUnityObjectNull(_WeaponData[WEAPON_HAND_STR.Left].gameObject) then
            _Renderer = _WeaponData[WEAPON_HAND_STR.Left].Renderers[1]
            _MeshFilter = _WeaponData[WEAPON_HAND_STR.Left].MeshFilters[1]
        else 
            -- 直接取右手武器Mesh
            _Renderer = _WeaponData[WEAPON_HAND_STR.Right].Renderers[1]
            _MeshFilter = _WeaponData[WEAPON_HAND_STR.Right].MeshFilters[1]
        end
    elseif iBonePos >= EBoneIdx.R_W0 and iBonePos <= EBoneIdx.R_W4 then
        -- 右手特效
        -- 取右手武器Mesh
        _Renderer = _WeaponData[WEAPON_HAND_STR.Right].Renderers[1]
        _MeshFilter = _WeaponData[WEAPON_HAND_STR.Right].MeshFilters[1]
elseif iBonePos == EBoneIdx.BothHand then
        -- 雙手特效
        -- 判斷是要取左手還右手
        if iIsLeft then
            -- 跟左手武器取法相同
            if not Extension.IsUnityObjectNull(_WeaponData[WEAPON_HAND_STR.Left].gameObject) then
                _Renderer = _WeaponData[WEAPON_HAND_STR.Left].Renderers[1]
                _MeshFilter = _WeaponData[WEAPON_HAND_STR.Left].MeshFilters[1]
                else
                -- 直接取右手武器Mesh
                _Renderer = _WeaponData[WEAPON_HAND_STR.Right].Renderers[1]
                _MeshFilter = _WeaponData[WEAPON_HAND_STR.Right].MeshFilters[1]
                end
        else
            -- 跟右手武器取法相同
            _Renderer = _WeaponData[WEAPON_HAND_STR.Right].Renderers[1]
            _MeshFilter = _WeaponData[WEAPON_HAND_STR.Right].MeshFilters[1]
        end
    end

    -- 防呆
    if _Renderer and _MeshFilter then
        return _Renderer, _MeshFilter.mesh
    else 
        return nil
    end
end

--region 投射物
-- 設定投射物
function ModelController:SetFlyEffect(iTickAction)
    if self.m_FlyingEffect == nil then
        self.m_FlyingEffect = {}
    end

    if iTickAction.TickData.m_RangeType == TickData.ERangeType.Track and iTickAction.TargetPlayerId == iTickAction.SenderId then
        BattleMgr.ShowLog("追蹤飛行物的發送者 & 受擊者是同一人")
        do return end
    end
    
    ---從武器類別去取對應武器骨架位置
    local _WeaponType = PlayerData.GetWeaponType()
    local _WeaponBone = m_WeaponTypeToWeaponBoneTable[_WeaponType]
    
    EffectMgr.PlayEffectWithParent(EEffectType.Model, iTickAction.TickData.m_FlyEffectId,self:GetBone(_WeaponBone), false,
function(iHash)
    self.m_FlyingEffect[iTickAction.SkillId] = iHash
    EffectMgr.SetTween(iHash, iTickAction)
end, nil, nil, nil, nil, self.m_ModelObject.transform.localEulerAngles, false)

end

function ModelController:ReturnFlyEffect(iIdx)
    if self.m_FlyingEffect[iIdx] then
        EffectMgr.ReturnEffect(self.m_FlyingEffect[iIdx])
    end
end
--endregion 投射物
--endregion

--region 受擊演出
local _ShakeBase_Vect = Vector3(1, 0, 1)
--- 模型抖動(只有NPC會抖)
--- ModelController 詳細請看 ModelController:NewNPC
---@param iValueKind EValueKind
function ModelController:ShakeEffect(iValueKind)
    local _WaitTime = 0
    local _Angle = Random.Range(0, 360)
    local _X_Direct = Mathf.Cos(Mathf.Deg2Rad * _Angle)
    local _Z_Direct = Mathf.Sin(Mathf.Deg2Rad * _Angle)
    local _ShakeDirection = Vector3(_X_Direct, 0, _Z_Direct)
    local _OriginalPosition = self.m_ModelObject.transform.localPosition
    local _OriginalScale = self.m_ModelObject.transform.localScale;

    -- 上次震動時間
    local _LastShakeTime = 0
    -- 震動次數
    local _ShakeTime = BattleMgr.m_AffectShakeScrObj.m_ShakeTimes
    -- 單次震動間隔
    local _perShake = BattleMgr.m_AffectShakeScrObj.m_Duration / _ShakeTime
    -- 抖動幅度
    local _ShakePower = 0;
    if iValueKind == EValueKind.LessHp then
        _ShakePower = BattleMgr.m_AffectShakeScrObj.m_NormalPower
    elseif iValueKind == EValueKind.CriLessHp then
        _ShakePower = BattleMgr.m_AffectShakeScrObj.m_CriticalPower
    end

    while _WaitTime < BattleMgr.m_AffectShakeScrObj.m_Duration do
        if (_WaitTime == 0 or _WaitTime - _LastShakeTime >= _perShake) and _ShakeTime > 0 then
            _LastShakeTime = _WaitTime
            _perShake = _perShake / 2
            _ShakePower = Mathf.Lerp(_ShakePower, 0, _WaitTime / BattleMgr.m_AffectShakeScrObj.m_Duration)

            if self.m_ModelObject.transform.localPosition.x - _OriginalPosition.x > 0 then
                self.m_ModelObject.transform.localPosition = _OriginalPosition - (_ShakeDirection + _ShakeBase_Vect) * _ShakePower
            else
                self.m_ModelObject.transform.localPosition = _OriginalPosition + (_ShakeDirection + _ShakeBase_Vect) * _ShakePower
            end
            
            _ShakeTime = _ShakeTime - 1
        end

        _WaitTime = _WaitTime + Time.fixedDeltaTime
        coroutine.yield()
    end

    self.m_ModelObject.transform.localScale = _OriginalScale;
    self.m_ModelObject.transform.localPosition = Vector3.zero;
end

---自發光外框
---@param iGlowType EGlowType
function ModelController:SetGlowEffect(iGlowType)
    local _Color = Color.GlowColor[iGlowType]
    local _Intens = 0
    if _Color == nil then
        _Color = Color.Black
    end
    
    if iGlowType == EGlowType.NONE then
        _Intens = 0
    elseif iGlowType == EGlowType.FAKEPLAYER then
        _Intens = 1
    elseif iGlowType == EGlowType.PLAYER_BUFF_EFFECT then
        _Intens = 15
    end
    
    for i = 0, self.m_Renderer.Length - 1 do
        local _Renderer = self.m_Renderer[i]
        if not Extension.IsUnityObjectNull(_Renderer) then
            _Renderer:GetPropertyBlock(self.m_MaterialPropertyBlock)

            self.m_MaterialPropertyBlock:SetColor(glowColorID, _Color)
            self.m_MaterialPropertyBlock:SetFloat(glowColorIntensityID, _Intens)
    
            _Renderer:SetPropertyBlock( self.m_MaterialPropertyBlock )    
        end
    end

end

---受擊外框
---@param iHitState EHitState
function ModelController:SetHitEffect(iHitState)
    
    local _Color = nil
    local _Intens = 0
    
    if iHitState == EHitState.NONE then
        _Color = Color.Black
        _Intens = 0
    elseif iHitState == EHitState.NORMAL then
        _Color = Color.AquaBlue
        _Intens = 1
    elseif iHitState == EHitState.CRITICAL then
        _Color = Color.Golden
        _Intens = 1
    end

    -- 啟用Debug，統一顯示紅色
    if BattleMgr.m_IsShowSkillArea.Client and iHitState ~= EHitState.NONE then
        _Color = Color.Red
        _Intens = 1
    end
    
    for i = 0, self.m_Renderer.Length - 1 do
        local _Renderer = self.m_Renderer[i]
        if not Extension.IsUnityObjectNull(_Renderer) then
            _Renderer:GetPropertyBlock(self.m_MaterialPropertyBlock)

            self.m_MaterialPropertyBlock:SetColor(hitColorID, _Color)
            self.m_MaterialPropertyBlock:SetFloat(hitColorIntensityID, _Intens)
            
            -- 受擊光不邊緣化
            if BattleMgr.m_IsShowSkillArea.Client and iHitState ~= EHitState.NONE then
                self.m_MaterialPropertyBlock:SetFloat(hitRimPowID, 0)
            end
    
            _Renderer:SetPropertyBlock( self.m_MaterialPropertyBlock )    
        end
    end
end

function ModelController:ResetHitEffect()
    self:SetHitEffect(EHitState.NONE)
end
--endregion

---把借走的模型拿回來(場景動畫專用)
function ModelController:ReturnBaseBiped(iTrans)

    if not self:IsModelObject() then
        return
    end

    if self.m_Material then
        self.m_Material:SetColor(xrayColorName, Color.White)
    end

    for _,v in pairs(iTrans)do
        v:SetParent(self.m_ModelObject.transform)
        v.localPosition = Vector3(0, 0, 0)
    end
end

--- 開關武器模型 Active
function ModelController:ShowWeapon(iShow)
    if self.m_WeaponData then
        for k,v in pairs(self.m_WeaponData)do
            if v.gameObject ~= nil then
                v.gameObject:SetActive(iShow)
            end
        end
    end
end

--- 開關角色模型 Active
function ModelController:SetBaseBipedActive(iActive)
    if not self:IsModelObject() then
        return
    end

    self.m_ModelObject:SetActive(iActive)
end

--- 開關人物顯示(含武器)
--- **注意** 這個方法只關閉 Renderer，不會讓 Animator 中斷、子物件不會消失
function ModelController:SetModelActive_WithoutChildObject(iActive)
    if not self:IsModelObject() then
        return
    end
    
    self:SetModelVisible(iActive)
    self:SetWeaponVisible(iActive)
end

--- 透過開關 Renderer 設定角色模型可見
--- 不會讓 Animator 中斷、子物件不會消失
function ModelController:SetModelVisible(iShow)
    if not self:IsModelObject() then
        return
    end
    
    -- 透過 SkinnedRenderer 開關角色顯示
    if self.m_SkinnedMeshRenderer and self.m_SkinnedMeshRenderer.Length > 0 then
        for i = 0, self.m_SkinnedMeshRenderer.Length - 1 do
            self.m_SkinnedMeshRenderer[i].enabled = iShow
        end
    end
end

--- 透過開關 Renderer 設定武器模型可見
--- 不會讓子物件消失
function ModelController:SetWeaponVisible(iShow)
    if not self:IsModelObject() then
        return
    end
    
    local _WeaponData = self.m_WeaponData
    
    -- 使用武器 Renderer 開關顯示
    if not Extension.IsUnityObjectNull(_WeaponData[WEAPON_HAND_STR.Left].gameObject) then
        -- 左手武器
        if _WeaponData[WEAPON_HAND_STR.Left].Renderers and _WeaponData[WEAPON_HAND_STR.Left].Renderers.Length > 0 then
            for i = 0, _WeaponData[WEAPON_HAND_STR.Left].Renderers.Length - 1 do
                _WeaponData[WEAPON_HAND_STR.Left].Renderers[i].enabled = iShow
            end
        end

        -- 右手武器
        if _WeaponData[WEAPON_HAND_STR.Right].Renderers and _WeaponData[WEAPON_HAND_STR.Right].Renderers.Length > 0 then
            for i = 0, _WeaponData[WEAPON_HAND_STR.Right].Renderers.Length - 1 do
                _WeaponData[WEAPON_HAND_STR.Right].Renderers[i].enabled = iShow
            end
        end
    else
        -- 右手武器
        if _WeaponData[WEAPON_HAND_STR.Right].Renderers and _WeaponData[WEAPON_HAND_STR.Right].Renderers.Length > 0 then
            for i = 0, _WeaponData[WEAPON_HAND_STR.Right].Renderers.Length - 1 do
                _WeaponData[WEAPON_HAND_STR.Right].Renderers[i].enabled = iShow
            end
        end
    end
end

--region 臉部細節相關

local m_DeltaType = 
{
    PosX = 0,
    PosY = 1,
    PosZ = 2,
    Rotation = 3,
    Scale = 4,
}

local function DisplaceFacialPos(iTransform,iAxis,iBone,iValue)
    local _vC = math.abs(iValue)
    local _delta = 0

    if iValue > 0 then
        _delta = iBone.maximum[iAxis] - iBone.original[iAxis]
    else
        _delta = iBone.minimum[iAxis] - iBone.original[iAxis]
    end

    local _localPos = Vector3( 0, 0, 0 )
    _localPos[iAxis] = _delta * _vC - (iTransform.localPosition[iAxis] - iBone.original[iAxis])
    iTransform.localPosition = iTransform.localPosition + _localPos
end

local function FacialAdjustRotation(iTransform,iBone,iValue)
    local _vC = math.abs(iValue)
    local _deltaRotation = nil

    if iValue > 0 then
        _deltaRotation = Vector3( iBone.maximum.x - iBone.original.x,iBone.maximum.y - iBone.original.y,iBone.maximum.z - iBone.original.z)
    else
        _deltaRotation = Vector3( iBone.minimum.x - iBone.original.x,iBone.minimum.y - iBone.original.y,iBone.minimum.z - iBone.original.z)
    end

    iTransform.localRotation = Quaternion.Euler( iBone.original + _deltaRotation * _vC )

end

local function FacialAdjustScale(iTransform,iBone,iValue)
    local _vC = math.abs(iValue)
    local _deltaScale = nil

    if iValue > 0 then
        _deltaScale = Vector3( iBone.maximum.x - iBone.original.x,iBone.maximum.y - iBone.original.y,iBone.maximum.z - iBone.original.z)
    else
        _deltaScale = Vector3( iBone.minimum.x - iBone.original.x,iBone.minimum.y - iBone.original.y,iBone.minimum.z - iBone.original.z)
    end
    _deltaScale = _deltaScale *  _vC 

    iTransform.localScale = Vector3( iBone.original.x + _deltaScale.x,iBone.original.y + _deltaScale.y,iBone.original.z + _deltaScale.z)

end

function ModelController:FaceAdjust(iIndex, ivalue)
    local _facialFeatureData = FacialFeatureData.GetGenderFacialFeature(self.m_AppearanceInfo.m_Gender)

    local _data = _facialFeatureData[iIndex]

    for k,v in pairs(_data) do

        if type(v) == "table" then
            if self.m_table_Bones[k] then
                if v.type == m_DeltaType.PosX then
                    DisplaceFacialPos(self.m_table_Bones[k],"x",v,ivalue)
                elseif v.type == m_DeltaType.PosY then
                    DisplaceFacialPos(self.m_table_Bones[k],"y",v,ivalue)

                elseif v.type == m_DeltaType.PosZ then
                    DisplaceFacialPos(self.m_table_Bones[k],"z",v,ivalue)
                elseif v.type == m_DeltaType.Rotation then
                    FacialAdjustRotation(self.m_table_Bones[k],v,ivalue)
                elseif v.type == m_DeltaType.Scale then
                    FacialAdjustScale(self.m_table_Bones[k],v,ivalue)
                end
            end
        end
        
    end

end

function ModelController:FaceSetAll()

    if self.m_AppearanceInfo.m_FacialFeatureValues then
        for key,value in pairs(self.m_AppearanceInfo.m_FacialFeatureValues) do
            local _adjust = EFacialAdjustList[key]
            if _adjust then
                self:FaceAdjust(_adjust,value)
            end
        end
    end

end
--endregion

--- 設定是不是 演武用MC
function ModelController.SetIsWugongDemo(iSelf, iBool)

    if(iSelf == nil) then
        return
    end

    iSelf.m_IsWugongDemoMC = iBool

end

--- 設定演武用 目標
function ModelController.SetWugongDemoTarget(iSelf, iTarget)

    if(iSelf == nil) then
        return
    end

    iSelf.m_WugongDemoTarget = iTarget

end
