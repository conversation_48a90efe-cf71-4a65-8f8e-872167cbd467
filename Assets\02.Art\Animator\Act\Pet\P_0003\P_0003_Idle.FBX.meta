fileFormatVersion: 2
guid: bc9448e1d9e4d3847a93fa25240feb86
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bone001
    100002: Bone002
    100004: Bone003
    100006: Bone004
    100008: <PERSON>00<PERSON>
    100010: Bone006
    100012: Bone012
    100014: <PERSON>01<PERSON>
    100016: <PERSON>014
    100018: Bone017
    100020: Bone019
    100022: Bone019(mirrored)
    100024: Bone020
    100026: Bone020(mirrored)
    100028: Bone021
    100030: Bone021(mirrored)
    100032: Bone022
    100034: Bone022(mirrored)
    100036: Bone023
    100038: Bone023(mirrored)
    100040: Bone024
    100042: Bone024(mirrored)
    100044: Bone025
    100046: Bone025(mirrored)
    100048: Bone026
    100050: Bone026(mirrored)
    100052: Bone027
    100054: Bone027(mirrored)
    100056: Bone028
    100058: Bone028(mirrored)
    100060: Bone029
    100062: Bone029(mirrored)
    100064: Bone030
    100066: <PERSON>0<PERSON>(mirrored)
    100068: Bone031
    100070: Bone031(mirrored)
    100072: Bone032
    100074: Bone032(mirrored)
    100076: <PERSON><PERSON><PERSON>
    100078: <PERSON><PERSON><PERSON>(mirrored)
    100080: Bone034
    100082: Bone035
    100084: Bone036
    100086: Bone037
    100088: Bone038
    100090: Bone039
    100092: Bone040
    100094: Bone041
    100096: Bone042
    100098: Bone043
    100100: Bone045
    100102: Bone047
    100104: Bone049
    100106: P_0003
    100108: //RootNode
    100110: Scale
    400000: Bone001
    400002: Bone002
    400004: Bone003
    400006: Bone004
    400008: Bone005
    400010: Bone006
    400012: Bone012
    400014: Bone013
    400016: Bone014
    400018: Bone017
    400020: Bone019
    400022: Bone019(mirrored)
    400024: Bone020
    400026: Bone020(mirrored)
    400028: Bone021
    400030: Bone021(mirrored)
    400032: Bone022
    400034: Bone022(mirrored)
    400036: Bone023
    400038: Bone023(mirrored)
    400040: Bone024
    400042: Bone024(mirrored)
    400044: Bone025
    400046: Bone025(mirrored)
    400048: Bone026
    400050: Bone026(mirrored)
    400052: Bone027
    400054: Bone027(mirrored)
    400056: Bone028
    400058: Bone028(mirrored)
    400060: Bone029
    400062: Bone029(mirrored)
    400064: Bone030
    400066: Bone030(mirrored)
    400068: Bone031
    400070: Bone031(mirrored)
    400072: Bone032
    400074: Bone032(mirrored)
    400076: Bone033
    400078: Bone033(mirrored)
    400080: Bone034
    400082: Bone035
    400084: Bone036
    400086: Bone037
    400088: Bone038
    400090: Bone039
    400092: Bone040
    400094: Bone041
    400096: Bone042
    400098: Bone043
    400100: Bone045
    400102: Bone047
    400104: Bone049
    400106: P_0003
    400108: //RootNode
    400110: Scale
    2100000: P_0003
    4300000: P_0003
    7400000: P_0003_Idle
    9500000: //RootNode
    13700000: P_0003
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: P_0003_Idle
      takeName: P_0003_Idle
      firstFrame: 0
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e80295348b5dc25488de14e252c96b85,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
