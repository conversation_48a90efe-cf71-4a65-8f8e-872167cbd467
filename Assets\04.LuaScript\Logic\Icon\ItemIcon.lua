---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---ItemIcon 繼承自 BasicIcon
---@class ItemIcon
---author KK
---version 1.0
---since [HEM 2.0]
---date 2022.9.30
ItemIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function ItemIcon.Init( iParent )
    ItemIcon.LoadResources( "ItemIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

local function OnItemPointerEnter(iSelf)
    if iSelf.m_NeedSelectAction and iSelf.m_IsSelect == false and iSelf.m_Idx ~= 0 then
        iSelf:SetObjectActive(iSelf.m_Trans_Select, true)
    end
end

local function OnItemPointerExit(iSelf)
    if iSelf.m_NeedSelectAction and iSelf.m_IsSelect == false and iSelf.m_Idx ~= 0 then
        iSelf:SetObjectActive(iSelf.m_Trans_Select, false)
    end
end

function ItemIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam, iMultSelectGroup )
    local _ItemId = 0
    if type(iData) == "number" then
        _ItemId = iData
    elseif type(iData) == "table" then
        _ItemId = iData.m_ItemIdx
    end

    local function OnItemClick()
        if iOnClick then
            iOnClick(iOnClickParam)
        end
    end

    local _Icon = BasicIcon:New( m_Tmp, EIconType.Item, 0, iParent, iWidth, OnItemClick, nil, nil, iMultSelectGroup )
    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    setmetatable( _Icon, { __index = self } )

    
    _Icon:SetNeedOpenHint(true)
         :SetNeedLongPress(true)
         :SetLongPressCallback(BasicIcon.OpenBasicHint)
         :SetClickTwice(true)
         :AddEvent(OnItemPointerEnter, EventTriggerType.PointerEnter)
         :AddEvent(OnItemPointerExit, EventTriggerType.PointerExit)

    -- 選擇框
    if _Icon.m_Trans_Select == nil then
        _Icon.m_Trans_Select = _Icon.transform:Find( "Image_Select" ):GetComponent( typeof( Image ) )
        _Icon.m_Tween_Select = _Icon.m_Trans_Select:GetComponent( typeof( LeanTweenVisual ) )
        _Icon.m_Tween_Select.enabled = false

        _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
    end

    ---附註顯示的圖示 ( 我的最愛、裝備裝、新物品 )
    if _Icon.m_Img_SubImage == nil  then
        _Icon.m_Img_SubImage = _Icon.transform:Find( "Img_SubImage" ):GetComponent( typeof( Image ) )
    end

    if iParent then
        _Icon.transform:SetParent( iParent )
    end

    ---鎖定Icon
    if _Icon.m_Image_Lock == nil then
        _Icon.m_Image_Lock = _Icon.transform:Find( "Image_Lock" ):GetComponent( typeof( Image ) )
    end

    ---設定數量
    if _Icon.m_TMP_Count == nil then
        _Icon.m_TMP_Count = _Icon.transform:Find( "TMP_Count" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end

    ---設定遮罩
    if _Icon.m_Image_Mask == nil then
        _Icon.m_Image_Mask = _Icon.transform:Find( "Image_Mask" ):GetComponent( typeof( Image ) )
        _Icon.m_Text_Mask =  _Icon.m_Image_Mask.transform:Find("TMP_MaskText"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end

    ---特效背景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectBack == nil then
        _Icon.m_Trans_EffectBack = _Icon.transform:Find( "Trans_EffectBack" )
    end

    ---特效前景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectFront == nil then
        _Icon.m_Trans_EffectFront = _Icon.transform:Find( "Trans_EffectFront" )
    end

    ---不被IconMgr IconMgr.CancelAllClick() 清除選擇，體現在開Hint時不會被統一清除
    _Icon.m_NeedClearSelect = true

    _Icon:SetObjectActive(_Icon.m_Image_Mask, false)

    ---登記觀察者 我的最愛愛心
    ---道具類icon 需要登記 我的最愛愛心
    GStateObserverManager.Register(EStateObserver.UpdateMyFavoriteItem, _Icon)
    ---道具類icon 物品狀態
    GStateObserverManager.Register(EStateObserver.UpdateItemState, _Icon)

    _Icon:RefreshIcon(_ItemId)

    return _Icon
end

--- 設定稀有度外框
function ItemIcon:SetRank( iRank )
    --- 新增物品稀有度到 8 Modify by hui 2025.03.31
    if iRank > ERank.Rainbow then
        iRank = ERank.Rainbow
    end

    local _self, _IsNewFrame = self:SetFrameType(iRank)
    if _IsNewFrame == true then
        self.m_Trans_EffectBack.transform:SetAsFirstSibling()
    end

    return self
end

--- 設定外框圖
function ItemIcon:SetFrameImage(iRank)
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            -- 存下所有可變色區塊
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end

    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].sprite = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].sprite
        self.m_FrameImage[key1].material = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].material
    end

    return self
end

---設定其他外框圖片
---@param iType number 類型
---@return ItemIcon
function ItemIcon:SetOtherFrameImage(iType)
    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].material = IconMgr.m_FrameIcon.m_OtherFrameIcon[iType][key1].material
    end

    self.m_IsAutoUseFrame = true

    return self
end

---設定物品數量
---@param iCount number 數量 or 要顯示的字串
---@param iStyle string 請給 style (優先度高於原始設定)
---@param iWithoutSizeController boolean 不受尺寸控制
function ItemIcon:SetCount( iCount, iStyle, iWithoutSizeController )
    if iCount ~= nil then
        if type(iCount) == "number" then
            if ItemData.CheckIsEquipmentByItemID(self.m_Idx) == true and self.m_SaveData then
                local _Durability = Mathf.RoundToInt(self.m_SaveData.m_Durability / self.m_SaveData.m_DurabilityMax * 100)
                self.m_TMP_Count.text = _Durability .. "%"
                if _Durability == 0 then
                    self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, "R")
                end
            else
                self.m_TMP_Count.text = tostring( iCount > 0 and GValue.CountToString(iCount, EValueShowType.Short) or "" )
            end

            if iCount > IconSetting.m_ITEMMAXCOUNT then
                self.m_TMP_Count.text = GString.GetTextWithSize(self.m_TMP_Count.text, IconSetting.IconTextSizeSmall)
            end

            -- 改用是否有idx決定要不要顯示數量
            self:SetObjectActive(self.m_TMP_Count, self.m_Idx > 0)

            self.m_Count = iCount

            self.m_TMP_Count.alignment = TMPro.TextAlignmentOptions.BottomRight;
        elseif type(iCount) == "string" then
            self.m_TMP_Count.text = iCount

            self:SetObjectActive(self.m_TMP_Count, not string.IsNullOrEmpty(iCount))

            self.m_TMP_Count.alignment = TMPro.TextAlignmentOptions.Bottom;

            if iStyle == nil then
                self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, "WO_01")
            end
        end
    else
        self:SetObjectActive(self.m_TMP_Count, false)
    end

    if iStyle then
        self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, iStyle)
    end

    if iWithoutSizeController then
        ---計算 Scale, 採用縮放的方式才不會影響子物件定位點導致顯示異常
        local _Scale = 1 / self.transform.localScale.x
        self.m_TMP_Count.transform.localScale = Vector3( _Scale, _Scale, _Scale )
    else
        self.m_TMP_Count.transform.localScale = Vector3.one
    end

    return self
end

--- 設定第二圖片 (優先度：新獲得>裝備中>最愛)
---@param iIsShow boolean 是否需要顯示 (true = 根據下面的圖名設定圖片；false = 強關；nil = 預設)
---@param iTextureName string 想要顯示的圖
function ItemIcon:SetSubImage(iIsShow, iTextureName)
    if iIsShow == false then
        self:SetObjectActive(self.m_Img_SubImage, false)
        return self
    end

    if iIsShow == true then
        if not string.IsNullOrEmpty(iTextureName) then
            SpriteMgr.Load(iTextureName, self.m_Img_SubImage)
            self:SetObjectActive(self.m_Img_SubImage, true)
        else
            self:SetObjectActive(self.m_Img_SubImage, false)
            D.LogError("設定圖片有誤: " .. tostring(iTextureName))
        end
    else
        -- 優先度：新獲得 > 裝備中 > 最愛
        if self.m_SaveData ~= nil then
            if self.m_SaveData.m_IsNew == true then
                SpriteMgr.Load(IconSetting.m_IconNewItemImageName, self.m_Img_SubImage)
                self:SetObjectActive(self.m_Img_SubImage, true)
            elseif self.m_SaveData.m_IsWear == true then
                SpriteMgr.Load(IconSetting.m_IconWearImageName, self.m_Img_SubImage)
                self:SetObjectActive(self.m_Img_SubImage, true)
            else
                self:SetHeart()
            end
        else
            -- 沒有 save 也不會有新獲得獲裝備裝的情況
            self:SetHeart()
        end
    end

    return self
end

---設定物品我的最愛 愛心提示
---@param iNeedShowHeart boolean 如果不需要判斷是否為最愛道具
---@param iIsFavorite boolean 如果是最愛道具顯示愛心
function ItemIcon:SetHeart()
    local _IsFavoriteItem = ItemData.CheckItemIsFavorite(self.m_Idx)

    ---防呆 iIsFavorite為空值直接不顯示愛心
    if _IsFavoriteItem == nil  then
        self:SetObjectActive(self.m_Img_SubImage, false)
    end

    if  _IsFavoriteItem then
        self:SetObjectActive(self.m_Img_SubImage, true)
        SpriteMgr.Load(IconSetting.m_IconHeartImageName, self.m_Img_SubImage)
    else
        self:SetObjectActive(self.m_Img_SubImage, false)
    end

    return self
end

---重新判斷並修正物品狀態文字
function ItemIcon:ResetItemState()
    ---有m_SaveData的道具才會有 m_ItemState 這項數據
    if self.m_SaveData ~= nil then
        self:SetItemStatusTMP(self.m_SaveData.m_ItemState)
    end
end

---設定顯示遮罩
---@param iIsShow boolean 設定遮罩 true = 顯示遮罩；false = 強制關閉遮罩；nil = 預設
---@param iTextID int 字串編號 遮罩打開再給編號即可
function ItemIcon:SetMask( iIsShow, iTextID )
    if iIsShow == false then
        self:SetObjectActive(self.m_Image_Mask, false)
        return self
    end

    local _ShowText = ""

    -- 物品表條件判斷
    local function SetItemData()
        local _SetItemDataResult, _SetItemDataShowText
        self:SetObjectActive(self.m_Image_Lock, self.m_ItemData ~= nil and self.m_ItemData.m_TransactionLock or false)
        return _SetItemDataResult, _SetItemDataShowText
    end

    -- C 端條件判斷
    local function SetCondition()
        local _SetConditionResult, _SetConditionShowText
        if self.m_SaveData == nil then
            do return end
        end

       local _CanUse, _Reason = BagMgr.CheckUseItem_MAX( self.m_SaveData, 1, true )
        if not _CanUse then
            if _Reason == REASON_USE_FAILED.LessLV then
                _SetConditionResult = true
                _SetConditionShowText = 3400
            elseif _Reason == REASON_USE_FAILED.OverLV then
                _SetConditionResult = true
                _SetConditionShowText = 3400
            else
                _SetConditionResult = false
            end
        else
            _SetConditionResult = false
        end

        return _SetConditionResult, _SetConditionShowText
    end

    -- save 條件判斷
    local function SetSaveState()
        local _SetSaveStateResult, _SetSaveStateShowText
        if self.m_SaveData ~= nil then
            -- 綁定、騎乘綁定狀態，顯示鎖頭
            if self.m_SaveData.m_ItemState == table.GetKey(EItemStatus, EItemStatus.Binding) or
                self.m_SaveData.m_ItemState == table.GetKey(EItemStatus, EItemStatus.RideLock) then
                self:SetObjectActive(self.m_Image_Lock, true)
                _SetSaveStateResult = true
                _SetSaveStateShowText = IconSetting.IconStatusId[EItemStatus[self.m_SaveData.m_ItemState]]
            else
                self:SetObjectActive(self.m_Image_Lock, false)
                _SetSaveStateShowText = IconSetting.IconStatusId[EItemStatus[self.m_SaveData.m_ItemState]]
                _SetSaveStateResult = not string.IsNullOrEmpty(_SetSaveStateShowText)
            end

        else
            self:SetObjectActive(self.m_Image_Lock, false)
        end
        return _SetSaveStateResult, _SetSaveStateShowText
    end

    if iIsShow then
        if self.m_SelectTextIdIsPriotity then
            -- 非得顯示預設設字
            _ShowText = TextData.Get(iTextID)
            iIsShow = not string.IsNullOrEmpty(_ShowText)
        else
            -- 檢查物品條件，不能使用的就不顯示兩階段文字
            -- local _IsShow, _TextID = SetCondition()
            -- if _IsShow then
            --     _ShowText = TextData.Get(_TextID)
            --     iIsShow = not string.IsNullOrEmpty(_ShowText)
            -- else
                _ShowText = TextData.Get(iTextID)
                -- iIsShow = not string.IsNullOrEmpty(_ShowText)
            -- end
        end
    else
        iIsShow, iTextID = SetSaveState()
        if not iIsShow then
            iIsShow, iTextID = SetCondition()
            if not iIsShow then
                iIsShow, iTextID = SetItemData()
            end
        end

        _ShowText = TextData.Get(iTextID)
        iIsShow = not string.IsNullOrEmpty(_ShowText)

        if iIsShow == false or iIsShow == nil then
            iIsShow = self.m_Idx ~= 0 and self.m_Count == 0

            if iIsShow == true and self.m_SaveData == nil then
                iIsShow = false
            end
        end
    end

    self:SetObjectActive(self.m_Image_Mask, iIsShow)

    self.m_Text_Mask.text = iIsShow == true and _ShowText or ""

    return self
end

--- 設定選擇
function ItemIcon:ShowSelect( iIsSelect )
    self.m_IsSelect = iIsSelect
    self:SetObjectActive(self.m_Trans_Select, self.m_IsSelect)

    if self.m_IsSelect == true then
        -- 點第一下解除新物品標記
        if self.m_SaveData ~= nil then
            self.m_SaveData.m_IsNew = false
            self:SetSubImage()
        end
        self.m_Trans_Select.sprite = IconMgr.m_IconSelectImage
        self.m_Trans_Select.color = Color.White
        self.m_ClickOnce = true
    end
    self.m_Tween_Select.enabled = false
    self:SetMask( self.m_IsSelect == false and nil or self.m_IsSelect, self.m_OnSelectTextId)
end

function ItemIcon:ShowSettingSelect( iIsSettingSelect )
    if iIsSettingSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSettingImage
    end
    self.m_ClickOnce = nil
    self:SetMask( nil, self.m_OnSelectTextId)
    self:SetObjectActive(self.m_Trans_Select, iIsSettingSelect)
    self.m_Tween_Select.enabled = iIsSettingSelect
    if iIsSettingSelect == true then
        self.m_Tween_Select:buildAllTweensAgain()
    end
end

--- 取消選擇
---@param iDoSelectAction boolean 是否執行選擇函式
function ItemIcon:CancelSelect(iDoSelectAction)
    self:SetMask()
    UIMgr.OpenIconName(false)
    self:ShowSelect( false )
    self.m_ClickOnce = nil

    -- 給 icon 底層因為判斷不選取，不在次觸發 action 用
    if iDoSelectAction ~= false then
        self:DoSelectAction(false)
    end
end

---重設物品 Icon
---@param iData number 物品 Idx
---@param iData SaveItemData 物品 Save
function ItemIcon:RefreshIcon( iData )
    local _ItemId = 0
    if type(iData) == "number" then
        _ItemId = iData
    elseif type(iData) == "table" then
        _ItemId = iData.m_ItemIdx
    else
        do return end
    end

    local function ResetIcon()
        self.m_ItemData = nil
        self.m_SaveData = nil
        self:RefreshBasicIcon(0)
        self:SetName()
            :SetOnSelectText()
            :SetRank(ERank.None)
            :SetCount(0)
            :SetSubImage()
            :SetMask()
    end

    if _ItemId == 0 then
        ResetIcon()
        do return end
    end

    local _ItemData = ItemData.GetItemDataByIdx( _ItemId )
    if _ItemData == nil then
        ResetIcon()
        do return end
    end

    self.m_ItemData =_ItemData

    self:RefreshBasicIcon(_ItemId, self.m_ItemData:GetItemTextureName())

    if type(iData) == "table" then
        self.m_SaveData = iData
        self:SetName(iData:GetItemSaveName())
            :SetRank(self.m_ItemData.m_Rarity)
            :SetOnSelectText()
            :SetCount( iData.m_Count )
            :SetSubImage()
            ---刷新使用限制 狀態
            :SetItemStatusTMP(iData.m_ItemState)
    else
        self.m_SaveData = nil
        self:SetName(self.m_ItemData:ItemName())
            :SetOnSelectText()
            :SetRank(self.m_ItemData.m_Rarity)
            :SetCount( 0 )
            :SetSubImage()
            :SetMask()
    end

    -- self:ShowSelect( table.Contains(IconMgr.m_SelectingIcon, self) )
    self:ShowSelect( false )
end

---設定選中顯示名稱
function ItemIcon:SetOnSelectText()
    --- 選擇中顯示的文字
    if self.m_Idx > 0 then
        if self.m_ItemData and self.m_ItemData.m_Type > 0 then
            self.m_OnSelectTextId = TabData.GetTabDataByIdx(self.m_ItemData.m_Type).m_SelectDefaultTextId
        else
            self.m_OnSelectTextId = IconSetting.m_SelectTextId[EItemType.None]
        end
    else
        self.m_OnSelectTextId = IconSetting.m_SelectTextId[EItemType.None]
    end

    return self
end

---顯示物品狀態(使用限制)
---@param iEItemStatus string 物品使用限制 在本地是轉成string後 紀錄在m_ItemState
---@return _StatusString string 物品狀態文字
function ItemIcon:SetItemStatusTMP(iEItemStatus)
    return self:SetMask( IconSetting.IconStatusId[EItemStatus[iEItemStatus]] and IconSetting.IconStatusId[EItemStatus[iEItemStatus]] ~= 0 and true or nil,
        IconSetting.IconStatusId[EItemStatus[iEItemStatus]])
end

--region 開關 Hint
function ItemIcon:OpenHint()
    if not self.m_NeedOpenHint or self.m_Idx == 0 then
        return
    end

    IconMgr.CancelAllClick()
    HintMgr_Controller.OpenHint(EHintType.ItemHint,self)
end

function ItemIcon:CloseHint()
    UIMgr.Close( ItemHint_Controller)
end
--endregion 開關 Hint

---ItemIcon 登記的觀察者 數據有變化是要做甚麼
---@param iEStateObserver EStateObserver 有變化的項目是甚麼 EStateObserver
function ItemIcon:OnStateChanged(iEStateObserver)
    ---道具類 我得最愛愛心顯示需求
    if iEStateObserver == EStateObserver.UpdateMyFavoriteItem then
        if Extension.IsUnityObjectNull(self.transform) then
            GStateObserverManager.UnRegister(EStateObserver.UpdateMyFavoriteItem, self)
        else
            self:SetSubImage()
        end
    ---道具類 物品狀態(使用限制)顯示需求
    elseif iEStateObserver == EStateObserver.UpdateItemState then
        if Extension.IsUnityObjectNull(self.transform) then
            GStateObserverManager.UnRegister(EStateObserver.UpdateItemState, self)
        else
            self:ResetItemState()
        end
    end
end

--- 設定 UI Controller
---@param iUIController UIControllerBase UIController
function ItemIcon:SetIconUIController(iUIController)
    if iUIController ~= nil then
        self.m_UIController = iUIController
    end
end

--- 設定稀有度特殊效果 ( 用完記得歸還 )
function ItemIcon:SetRankFrameEffect(iIsActive, iUIController)
    if iIsActive == true and self.m_IsShowRankFrame == true then
        do return end
    end

    if iUIController ~= nil then 
        self:SetIconUIController(iUIController)
    end

    if iIsActive == true then
        self.m_IsShowRankFrame = true
        local _EffectID = 0
        if IconSetting.EIconRankFrameEffectId[self.m_ItemData.m_Rarity] ~= nil then
            _EffectID = IconSetting.EIconRankFrameEffectId[self.m_ItemData.m_Rarity]
        else
            -- D.LogError("self.m_ItemData.m_Rarity: " .. self.m_ItemData.m_Rarity)
            return
        end

        EffectMgr.PlayEffectWithPos(
            EEffectType.UI, _EffectID, self.m_Trans_EffectFront, Vector3.zero, true, 
            function(iHash)
                self.iRankFrameHash = iHash
            end, nil, nil, nil)

    else
        if self.iRankFrameHash ~= nil then
            self.m_UIController = nil
            EffectMgr.ReturnEffect(self.iRankFrameHash)
            self.iRankFrameHash = nil
        end

        if self.m_IsShowRankGrandPrize == true then
            self:SetRankGrandPrizeEffect(false, iUIController)
        end
        self.m_IsShowRankFrame = false
    end
end

--- 設定稀有度大獎特殊效果 ( 用完記得歸還 )
---@param iIsActive boolean 開關 (false 就是歸還)
---@param iUIController UIControllerBase UIController
function ItemIcon:SetRankGrandPrizeEffect(iIsActive, iUIController)
    if iIsActive == true and self.m_IsShowRankGrandPrize == true then
        do return end
    end

    if iUIController ~= nil then 
        self:SetIconUIController(iUIController)
    end
    
    if iIsActive == true then
        self.m_IsShowRankGrandPrize = true
        local _EffectID = 0
        if IconSetting.EIconRankGrandPrizeEffectId[self.m_ItemData.m_Rarity] ~= nil then
            _EffectID = IconSetting.EIconRankGrandPrizeEffectId[self.m_ItemData.m_Rarity]
        else
            -- D.LogError("self.m_ItemData.m_Rarity: " .. self.m_ItemData.m_Rarity)
            return
        end

        EffectMgr.PlayEffectWithPos(
            EEffectType.UI, _EffectID, self.m_Trans_EffectBack, Vector3.zero, true, 
            function(iHash)
                self.iRankGrandPrizeHash = iHash
            end, nil, nil, nil)
    else
        if self.iRankGrandPrizeHash ~= nil then
            EffectMgr.ReturnEffect(self.iRankGrandPrizeHash)
        end
        self.m_IsShowRankGrandPrize = false
    end

    self:SetRankFrameEffect(iIsActive, iUIController)
end
