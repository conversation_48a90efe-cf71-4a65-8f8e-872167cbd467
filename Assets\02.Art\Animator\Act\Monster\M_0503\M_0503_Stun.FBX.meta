fileFormatVersion: 2
guid: f30f0bc56c03b99499b9c08d953712e9
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 chin
    100004: Bip001 Footsteps
    100006: Bip001 Head
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Foot
    100016: Bip001 L Forearm
    100018: Bip001 L Hand
    100020: Bip001 L Thigh
    100022: Bip001 L Toe0
    100024: Bip001 L Toe01
    100026: Bip001 L UpperArm
    100028: Bip001 L_ear
    100030: Bip001 L_ear02
    100032: Bip001 Neck
    100034: Bip001 nose
    100036: Bip001 Pelvis
    100038: Bip001 R Calf
    100040: Bip001 R Clavicle
    100042: Bip001 R Finger0
    100044: Bip001 R Foot
    100046: Bip001 R Forearm
    100048: Bip001 R Hand
    100050: Bip001 R Thigh
    100052: Bip001 R Toe0
    100054: Bip001 R Toe01
    100056: Bip001 R UpperArm
    100058: Bip001 R_ear
    100060: Bip001 R_ear02
    100062: Bip001 Spine
    100064: Bip001 Spine1
    100066: Bone_F_tail_001
    100068: Bone_F_tail_002
    100070: Bone_L_Finger_001
    100072: Bone_L_Finger_002
    100074: Bone_L_Finger_003
    100076: Bone_L_Finger_004
    100078: Bone_L_Finger_005
    100080: Bone_L_Finger_006
    100082: Bone_L_Finger_007
    100084: Bone_L_Finger_008
    100086: Bone_R_Finger_001
    100088: Bone_R_Finger_002
    100090: Bone_R_Finger_003
    100092: Bone_R_Finger_004
    100094: Bone_R_Finger_005
    100096: Bone_R_Finger_006
    100098: Bone_R_Finger_007
    100100: Bone_R_Finger_008
    100102: bones_Tongue_001
    100104: bones_Tongue_002
    100106: Dummy_Tongue_000
    100108: //RootNode
    400000: Bip001
    400002: Bip001 chin
    400004: Bip001 Footsteps
    400006: Bip001 Head
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Foot
    400016: Bip001 L Forearm
    400018: Bip001 L Hand
    400020: Bip001 L Thigh
    400022: Bip001 L Toe0
    400024: Bip001 L Toe01
    400026: Bip001 L UpperArm
    400028: Bip001 L_ear
    400030: Bip001 L_ear02
    400032: Bip001 Neck
    400034: Bip001 nose
    400036: Bip001 Pelvis
    400038: Bip001 R Calf
    400040: Bip001 R Clavicle
    400042: Bip001 R Finger0
    400044: Bip001 R Foot
    400046: Bip001 R Forearm
    400048: Bip001 R Hand
    400050: Bip001 R Thigh
    400052: Bip001 R Toe0
    400054: Bip001 R Toe01
    400056: Bip001 R UpperArm
    400058: Bip001 R_ear
    400060: Bip001 R_ear02
    400062: Bip001 Spine
    400064: Bip001 Spine1
    400066: Bone_F_tail_001
    400068: Bone_F_tail_002
    400070: Bone_L_Finger_001
    400072: Bone_L_Finger_002
    400074: Bone_L_Finger_003
    400076: Bone_L_Finger_004
    400078: Bone_L_Finger_005
    400080: Bone_L_Finger_006
    400082: Bone_L_Finger_007
    400084: Bone_L_Finger_008
    400086: Bone_R_Finger_001
    400088: Bone_R_Finger_002
    400090: Bone_R_Finger_003
    400092: Bone_R_Finger_004
    400094: Bone_R_Finger_005
    400096: Bone_R_Finger_006
    400098: Bone_R_Finger_007
    400100: Bone_R_Finger_008
    400102: bones_Tongue_001
    400104: bones_Tongue_002
    400106: Dummy_Tongue_000
    400108: //RootNode
    7400000: M_0503_Stun
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 158f5cf950cd6d44db427b5e3b517cfc,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
