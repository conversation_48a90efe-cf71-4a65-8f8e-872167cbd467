fileFormatVersion: 2
guid: bfcc3b7e60b76ad4c87a9c2e40ac42c9
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 chest
    100004: Bip001 chest_Spring meat_001
    100006: Bip001 chest_Spring meat_002
    100008: Bip001 Chin
    100010: 'Bip001 F hair '
    100012: Bip001 F hair 02
    100014: Bip001 F hair oop
    100016: Bip001 F hair oop02
    100018: Bip001 Footsteps
    100020: Bip001 Head
    100022: Bip001 L Calf
    100024: Bip001 L Clavicle
    100026: Bip001 L eye 01
    100028: Bip001 L eye 02
    100030: Bip001 L Finger0
    100032: Bip001 L Foot
    100034: Bip001 L Forearm
    100036: Bip001 L hair 01
    100038: Bip001 L hair 0102
    100040: Bip001 L Hand
    100042: Bip001 L HorseLink
    100044: Bip001 L Thigh
    100046: Bip001 L Thigh_Spring meat_001
    100048: Bip001 L Toe0
    100050: Bip001 L UpperArm
    100052: Bip001 L UpperArm_Spring meat_001
    100054: Bip001 Neck
    100056: Bip001 Neck1
    100058: Bip001 Neck2
    100060: Bip001 Neck_Spring meat_002
    100062: Bip001 Neck_Spring meat_003
    100064: Bip001 Pelvis
    100066: Bip001 R Calf
    100068: Bip001 R Clavicle
    100070: Bip001 R eye 01
    100072: Bip001 R eye 02
    100074: Bip001 R Finger0
    100076: Bip001 R Foot
    100078: Bip001 R Forearm
    100080: Bip001 R Forearm_Spring meat_001
    100082: Bip001 R Forearm_Spring meat_002
    100084: 'Bip001 R hair '
    100086: Bip001 R hair 02
    100088: Bip001 R Hand
    100090: Bip001 R HorseLink
    100092: Bip001 R Thigh
    100094: Bip001 R Thigh_Spring meat_001
    100096: Bip001 R Toe0
    100098: Bip001 R UpperArm
    100100: Bip001 R UpperArm_Spring meat_001
    100102: Bip001 shell
    100104: Bip001 shell02
    100106: Bip001 Spine
    100108: Bip001 Spine1
    100110: Bip001 Tail
    100112: Bip001 Tail hair
    100114: Bip001 Tail hair 01
    100116: Bip001 Tail hair 0102
    100118: Bip001 Tail hair 02
    100120: Bip001 Tail hair02
    100122: Bip001 Tail hair03
    100124: Bip001 Tail1
    100126: Bip001 Tail2
    100128: Bip001 Tail3
    100130: Bip001 Tail4
    100132: Bone001 L hair 001
    100134: Bone001 L hair 002
    100136: Bone001 L HorseLink hair 001
    100138: Bone001 L HorseLink hair 002
    100140: Bone001 R hair 001
    100142: Bone001 R hair 002
    100144: Bone001 R HorseLink hair 001
    100146: Bone001 R HorseLink hair 002
    100148: //RootNode
    100150: Bip001 SpineX
    400000: Bip001
    400002: Bip001 chest
    400004: Bip001 chest_Spring meat_001
    400006: Bip001 chest_Spring meat_002
    400008: Bip001 Chin
    400010: 'Bip001 F hair '
    400012: Bip001 F hair 02
    400014: Bip001 F hair oop
    400016: Bip001 F hair oop02
    400018: Bip001 Footsteps
    400020: Bip001 Head
    400022: Bip001 L Calf
    400024: Bip001 L Clavicle
    400026: Bip001 L eye 01
    400028: Bip001 L eye 02
    400030: Bip001 L Finger0
    400032: Bip001 L Foot
    400034: Bip001 L Forearm
    400036: Bip001 L hair 01
    400038: Bip001 L hair 0102
    400040: Bip001 L Hand
    400042: Bip001 L HorseLink
    400044: Bip001 L Thigh
    400046: Bip001 L Thigh_Spring meat_001
    400048: Bip001 L Toe0
    400050: Bip001 L UpperArm
    400052: Bip001 L UpperArm_Spring meat_001
    400054: Bip001 Neck
    400056: Bip001 Neck1
    400058: Bip001 Neck2
    400060: Bip001 Neck_Spring meat_002
    400062: Bip001 Neck_Spring meat_003
    400064: Bip001 Pelvis
    400066: Bip001 R Calf
    400068: Bip001 R Clavicle
    400070: Bip001 R eye 01
    400072: Bip001 R eye 02
    400074: Bip001 R Finger0
    400076: Bip001 R Foot
    400078: Bip001 R Forearm
    400080: Bip001 R Forearm_Spring meat_001
    400082: Bip001 R Forearm_Spring meat_002
    400084: 'Bip001 R hair '
    400086: Bip001 R hair 02
    400088: Bip001 R Hand
    400090: Bip001 R HorseLink
    400092: Bip001 R Thigh
    400094: Bip001 R Thigh_Spring meat_001
    400096: Bip001 R Toe0
    400098: Bip001 R UpperArm
    400100: Bip001 R UpperArm_Spring meat_001
    400102: Bip001 shell
    400104: Bip001 shell02
    400106: Bip001 Spine
    400108: Bip001 Spine1
    400110: Bip001 Tail
    400112: Bip001 Tail hair
    400114: Bip001 Tail hair 01
    400116: Bip001 Tail hair 0102
    400118: Bip001 Tail hair 02
    400120: Bip001 Tail hair02
    400122: Bip001 Tail hair03
    400124: Bip001 Tail1
    400126: Bip001 Tail2
    400128: Bip001 Tail3
    400130: Bip001 Tail4
    400132: Bone001 L hair 001
    400134: Bone001 L hair 002
    400136: Bone001 L HorseLink hair 001
    400138: Bone001 L HorseLink hair 002
    400140: Bone001 R hair 001
    400142: Bone001 R hair 002
    400144: Bone001 R HorseLink hair 001
    400146: Bone001 R HorseLink hair 002
    400148: //RootNode
    400150: Bip001 SpineX
    7400000: Boss_0001_Idle
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Boss_0001_Idle
      takeName: Boss_0001_Idle
      firstFrame: 0
      lastFrame: 80
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 61dfe1e270380ae49b42b773ef3053cc,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
