fileFormatVersion: 2
guid: 70dad4960f99d554e9693eac5292140f
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bip001 Head
  - first:
      1: 100002
    second: Bip001 Spine1
  - first:
      1: 100004
    second: //RootNode
  - first:
      1: 100006
    second: WildRabbit_Head_JawSHJnt
  - first:
      1: 100008
    second: WildRabbit_l_Clavicle_01_01SHJnt
  - first:
      1: 100010
    second: WildRabbit_l_Ears_01_01SHJnt
  - first:
      1: 100012
    second: WildRabbit_l_Ears_01_02SHJnt
  - first:
      1: 100014
    second: WildRabbit_l_Ears_01_03SHJnt
  - first:
      1: 100016
    second: WildRabbit_l_FrontLeg_AnkleSHJnt
  - first:
      1: 100018
    second: WildRabbit_l_FrontLeg_BallSHJnt
  - first:
      1: 100020
    second: WildRabbit_l_FrontLeg_HipSHJnt
  - first:
      1: 100022
    second: WildRabbit_l_FrontLeg_KneeSHJnt
  - first:
      1: 100024
    second: WildRabbit_l_FrontLeg_ToeSHJnt
  - first:
      1: 100026
    second: WildRabbit_l_HindLeg_AnkleSHJnt
  - first:
      1: 100028
    second: WildRabbit_l_HindLeg_BallSHJnt
  - first:
      1: 100030
    second: WildRabbit_l_HindLeg_HipSHJnt
  - first:
      1: 100032
    second: WildRabbit_l_HindLeg_KneeSHJnt
  - first:
      1: 100034
    second: WildRabbit_l_HindLeg_ToeSHJnt
  - first:
      1: 100036
    second: WildRabbit_MAINSHJnt
  - first:
      1: 100038
    second: WildRabbit_Neck_01SHJnt
  - first:
      1: 100040
    second: WildRabbit_Neck_02SHJnt
  - first:
      1: 100042
    second: WildRabbit_Neck_TopSHJnt
  - first:
      1: 100044
    second: WildRabbit_Nose_01_01SHJnt
  - first:
      1: 100046
    second: WildRabbit_Nose_01_02SHJnt
  - first:
      1: 100048
    second: WildRabbit_r_Clavicle_01_01SHJnt
  - first:
      1: 100050
    second: WildRabbit_r_Ears_01_01SHJnt
  - first:
      1: 100052
    second: WildRabbit_r_Ears_01_02SHJnt
  - first:
      1: 100054
    second: WildRabbit_r_Ears_01_03SHJnt
  - first:
      1: 100056
    second: WildRabbit_r_FrontLeg_AnkleSHJnt
  - first:
      1: 100058
    second: WildRabbit_r_FrontLeg_BallSHJnt
  - first:
      1: 100060
    second: WildRabbit_r_FrontLeg_HipSHJnt
  - first:
      1: 100062
    second: WildRabbit_r_FrontLeg_KneeSHJnt
  - first:
      1: 100064
    second: WildRabbit_r_FrontLeg_ToeSHJnt
  - first:
      1: 100066
    second: WildRabbit_r_HindLeg_AnkleSHJnt
  - first:
      1: 100068
    second: WildRabbit_r_HindLeg_BallSHJnt
  - first:
      1: 100070
    second: WildRabbit_r_HindLeg_HipSHJnt
  - first:
      1: 100072
    second: WildRabbit_r_HindLeg_KneeSHJnt
  - first:
      1: 100074
    second: WildRabbit_r_HindLeg_ToeSHJnt
  - first:
      1: 100076
    second: WildRabbit_ROOTSHJnt
  - first:
      1: 100078
    second: WildRabbit_Spine_01SHJnt
  - first:
      1: 100080
    second: WildRabbit_Spine_02SHJnt
  - first:
      1: 100082
    second: WildRabbit_Spine_03SHJnt
  - first:
      1: 100084
    second: WildRabbit_Spine_TopSHJnt
  - first:
      1: 100086
    second: WildRabbit_Tail_01_01SHJnt
  - first:
      4: 400000
    second: Bip001 Head
  - first:
      4: 400002
    second: Bip001 Spine1
  - first:
      4: 400004
    second: //RootNode
  - first:
      4: 400006
    second: WildRabbit_Head_JawSHJnt
  - first:
      4: 400008
    second: WildRabbit_l_Clavicle_01_01SHJnt
  - first:
      4: 400010
    second: WildRabbit_l_Ears_01_01SHJnt
  - first:
      4: 400012
    second: WildRabbit_l_Ears_01_02SHJnt
  - first:
      4: 400014
    second: WildRabbit_l_Ears_01_03SHJnt
  - first:
      4: 400016
    second: WildRabbit_l_FrontLeg_AnkleSHJnt
  - first:
      4: 400018
    second: WildRabbit_l_FrontLeg_BallSHJnt
  - first:
      4: 400020
    second: WildRabbit_l_FrontLeg_HipSHJnt
  - first:
      4: 400022
    second: WildRabbit_l_FrontLeg_KneeSHJnt
  - first:
      4: 400024
    second: WildRabbit_l_FrontLeg_ToeSHJnt
  - first:
      4: 400026
    second: WildRabbit_l_HindLeg_AnkleSHJnt
  - first:
      4: 400028
    second: WildRabbit_l_HindLeg_BallSHJnt
  - first:
      4: 400030
    second: WildRabbit_l_HindLeg_HipSHJnt
  - first:
      4: 400032
    second: WildRabbit_l_HindLeg_KneeSHJnt
  - first:
      4: 400034
    second: WildRabbit_l_HindLeg_ToeSHJnt
  - first:
      4: 400036
    second: WildRabbit_MAINSHJnt
  - first:
      4: 400038
    second: WildRabbit_Neck_01SHJnt
  - first:
      4: 400040
    second: WildRabbit_Neck_02SHJnt
  - first:
      4: 400042
    second: WildRabbit_Neck_TopSHJnt
  - first:
      4: 400044
    second: WildRabbit_Nose_01_01SHJnt
  - first:
      4: 400046
    second: WildRabbit_Nose_01_02SHJnt
  - first:
      4: 400048
    second: WildRabbit_r_Clavicle_01_01SHJnt
  - first:
      4: 400050
    second: WildRabbit_r_Ears_01_01SHJnt
  - first:
      4: 400052
    second: WildRabbit_r_Ears_01_02SHJnt
  - first:
      4: 400054
    second: WildRabbit_r_Ears_01_03SHJnt
  - first:
      4: 400056
    second: WildRabbit_r_FrontLeg_AnkleSHJnt
  - first:
      4: 400058
    second: WildRabbit_r_FrontLeg_BallSHJnt
  - first:
      4: 400060
    second: WildRabbit_r_FrontLeg_HipSHJnt
  - first:
      4: 400062
    second: WildRabbit_r_FrontLeg_KneeSHJnt
  - first:
      4: 400064
    second: WildRabbit_r_FrontLeg_ToeSHJnt
  - first:
      4: 400066
    second: WildRabbit_r_HindLeg_AnkleSHJnt
  - first:
      4: 400068
    second: WildRabbit_r_HindLeg_BallSHJnt
  - first:
      4: 400070
    second: WildRabbit_r_HindLeg_HipSHJnt
  - first:
      4: 400072
    second: WildRabbit_r_HindLeg_KneeSHJnt
  - first:
      4: 400074
    second: WildRabbit_r_HindLeg_ToeSHJnt
  - first:
      4: 400076
    second: WildRabbit_ROOTSHJnt
  - first:
      4: 400078
    second: WildRabbit_Spine_01SHJnt
  - first:
      4: 400080
    second: WildRabbit_Spine_02SHJnt
  - first:
      4: 400082
    second: WildRabbit_Spine_03SHJnt
  - first:
      4: 400084
    second: WildRabbit_Spine_TopSHJnt
  - first:
      4: 400086
    second: WildRabbit_Tail_01_01SHJnt
  - first:
      74: 7400000
    second: P_0005_Battle_01
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: P_0005_Battle_01
      takeName: P_0005_Battle_01
      internalID: 0
      firstFrame: 1
      lastFrame: 33
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 732e08c3a248c8e4fb59baa7ea7f19bb,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
