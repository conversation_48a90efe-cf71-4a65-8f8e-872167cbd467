---=====================================================================
---             CHINESE GAMER PROPRIETARY INFORMATION
---
---This software is supplied under the terms of a license agreement or
---nondisclosure agreement with CHINESE GAMER and may not
---be copied or disclosed except in accordance with the terms of that
---agreement.
---
---                Copyright © 2023 by CHINESE GAMER.
---                     All Rights Reserved.
---
---   -------------------------------------------------------------
---
---=====================================================================

---任務書相關Data
---@class MissionMgr
---author WereHsu
---version 1.0
---since [黃易群俠傳M] 0.90
---date 2023.03.17
MissionMgr = {}
local this = MissionMgr

local MISSION_FLAG_START = 2001
local MISSION_FLAG_END = 5000
---刷新標記時刷任務的速度
local MISSION_UPDATE_SPEED = 20

--- 最大可接任務數
this.MaxAvilMissionCount = 10

---左頁籤任務排序
this.EMissionSort = {
    EMissionType.Main,
    EMissionType.Branch,
    EMissionType.Team,
    EMissionType.Special,
    EMissionType.More,
    EMissionType.RoyolTop,
    EMissionType.Stage,
    EMissionType.Raid,
    EMissionType.Activity
}

---可接任務排序
this.AvalMissionSort = {
    EMissionType.Main,
    EMissionType.Special,
    EMissionType.Raid,
    EMissionType.Branch,
    EMissionType.Team,
    EMissionType.Stage
}

---任務種類ICON
this.MissionClassityIcon = {
    [EMissionType.Main] = "MainBk_008",
    [EMissionType.Special] = "MainBk_014",
    [EMissionType.Branch] = "MainBk_010",
    [EMissionType.Team] = "MainBk_011",
    [EMissionType.Raid] = "MainBk_012"
}

---Prize取的物件的ItemKind對應的資料
this.m_KindIDToItemID = {
    [1] = 95001,
    [2] = 95002,
    [3] = 95003,
    [4] = 95004,
    [6] = 95005,
    [7] = 95006,
    [8] = 95007
}

--- 章節編號
this.SPECIAL_CHAPTER_IDX = 255
--- 章節串表編號
this.SPECIAL_DATA_CHAPTER_IDX = 31

local m_TrackMissionList = nil

---@type number|Mission
---玩家已取得的所有任務
local m_MissionList = {}

---@type table<number, MissionData>
---玩家可取得的所有任務
local m_AllMissionList = nil

--- 新 以章節排序任務的 table
local m_MissionTable = {}

--- 新 從永標找任務資料
local m_MissionByStaticFlag = {}

---刷新所有資料的Coroutine
local _RefreshMissionCo = nil

---每次成功登入時設定
function MissionMgr.InitAllMission()
    this.GetAllMission()
    this.ArangeMissionByChapter()
    this.SetMissionByStaticFlag()
end

---每次登出時重設
function MissionMgr.ResetAllMissionStep()
    if _RefreshMissionCo then
        coroutine.stop(_RefreshMissionCo)
        _RefreshMissionCo = nil
    end
    if m_AllMissionList then
        for _, v in pairs(m_AllMissionList) do
            v:ResetStep()
        end
    end
end

function MissionMgr.Update()
    if _RefreshMissionCo and coroutine.status(_RefreshMissionCo) == "suspended" then
        coroutine.resume(_RefreshMissionCo)
    end
end

---設定所有任務的狀態
function MissionMgr.SetAllMissionStatus(isImmediately, iRefreshDoneDelegate)
    if _RefreshMissionCo then
        coroutine.stop(_RefreshMissionCo)
        _RefreshMissionCo = nil
    end

    local _Basefunction = function()
        local _counter = 0
        m_TrackMissionList = ClientSaveMgr.GetDataTable(EClientSaveDataType.MissionTrack)
        for _, v in pairs(m_AllMissionList) do
            v:SetNowStep()
            local _strFlag = tostring(v.FlagID)
            if m_TrackMissionList[_strFlag] or v.Classity == EMissionType.Main then
                v.m_bTrack = true
            else
                v.m_bTrack = false
            end
            if not isImmediately then
                _counter = _counter + 1
                if _counter > MISSION_UPDATE_SPEED then
                    coroutine.yield()
                    _counter = 0
                end
            end
        end

        for i = 1, table.Count(m_MissionTable) do
            MissionMgr.ChapterMissionSorting(i)
        end

        if iRefreshDoneDelegate then
            iRefreshDoneDelegate()
        end
    end

    if isImmediately then
        _Basefunction()
    else
        _RefreshMissionCo = coroutine.start(_Basefunction)
    end
end

function MissionMgr.CheckChapterCompleteHint(iFlagID)
    -- 這邊順道做完成章節提示
    local _StaticMission = MissionMgr.GetMissionByStaticFlag()
    if (_StaticMission[iFlagID] ~= nil) then
        local _MissionKind = _StaticMission[iFlagID].Kind
        local _ChapterMissionData = MissionMgr.GetMissionTable()
        local _Chapters = MissionMgr.GetAllChapter()
        local _TotalNumber = table.Count(_ChapterMissionData[_MissionKind])
        local _DoneNumber = 0

        for _Key, _Value in pairs(_ChapterMissionData[_MissionKind]) do
            if _Value.Finish == EMissionStatus.Done or _Value.AFlag == _StaticMission[iFlagID].AFlag then
                _DoneNumber = _DoneNumber + 1
            end
        end

        if (_DoneNumber == _TotalNumber) and _MissionKind ~= this.SPECIAL_DATA_CHAPTER_IDX then
            FastClickHint_Controller.ToOpenQueue(
                FastClickHint_Model.EWhichType_Page.Chapter,
                TextData.Get(_Chapters[_MissionKind].m_ChapterNumberID),
                TextData.Get(_Chapters[_MissionKind].m_ChapterName),
                function()
                    UIMgr.Open(ExploreDairy_Controller, 1, _StaticMission[iFlagID], EMissionUIDefaultPage.Chapter)
                end
            )
        end
    end
end

-- ---每次收到新任務要重刷
-- function MissionMgr.GetAllMission()

--     local _MissionList = {}

--     for key,value in pairs(PlayerData_Flags.m_Table_MovingFlag) do

--         --local _FlagID = value.m_FlagID
--         _MissionList[value.m_FlagID] = MissionMgr.GetMissionByMovingFlag(value.m_FlagID)
--         --[[
--         if _FlagID >= MISSION_FLAG_START and _FlagID < MISSION_FLAG_END then
--             local _PlotData = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK,_FlagID)
--             if _PlotData then
--                 --將Data轉化為Mission
--                 local _Mission = this.SetMissionData(_PlotData)
--                 local _strFlag = "".._FlagID

--                 --有動標的話就是未完成
--                 if _Mission.Finish ~= EventTipType.CanComplete then
--                     _Mission.Finish = EventTipType.Undone
--                 end
--                 if _Mission.Classity == EMissionType.Main then
--                     if m_TrackMissionList[_strFlag] == nil then
--                         m_TrackMissionList[_strFlag] = _FlagID
--                     end
--                 end

--                 if m_TrackMissionList[_strFlag] then
--                     _Mission.m_bTrack = true
--                 end

--                 _MissionList[_FlagID] = _Mission

--             else
--                 D.LogError("動標不含任務資料，動標:".._FlagID)
--             end

--         end]]
--     end
--     ClientSaveMgr.Save(EClientSaveDataType.MissionTrack)

--     if PlayerData.m_LoginPlayerData.m_GuildID then
--         --組織動標要接協定
--         --先測試看看其他能不能用再來接
--     end

--     m_MissionList = _MissionList

--     return m_MissionList
-- end

function MissionMgr.GetMissionByMovingFlag(iFlagID)
    if iFlagID >= MISSION_FLAG_START and iFlagID < MISSION_FLAG_END then
        local _PlotData = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK, iFlagID)
        if _PlotData and _PlotData.StepBegin > 0 then
            --將Data轉化為Mission
            local _Mission = m_AllMissionList[iFlagID] --this.SetMissionData(_PlotData)
            local _strFlag = "" .. iFlagID

            --有動標的話就是未完成
            if _Mission.Finish ~= EMissionStatus.CanComplete then
                _Mission.Finish = EMissionStatus.Undone
            end
            --

            --[[

            if _Mission.Classity == EMissionType.Main then
                if m_TrackMissionList[_strFlag] == nil then
                    m_TrackMissionList[_strFlag] = table.Count(m_TrackMissionList) + 1 --iFlagID
                end
            end
            
            if m_TrackMissionList[_strFlag] then
                _Mission.m_bTrack = true
            end]] return _Mission
        else
            D.Log("<color=Red>動標不含任務資料，動標:" .. iFlagID .. "</color>")
        end
    end
    return nil
end

---更新追蹤的任務列表
---FIXME: 換日卡頓
function MissionMgr.UpdateAndSaveTrackMission(iFlagID)
    local _strFlag = "" .. iFlagID
    if m_TrackMissionList[_strFlag] == nil then
        local _VTable = {}
        local _Count = 1
        for _key, _value in pairs(m_TrackMissionList) do
            -- 跳過不是數字的
            if (type(_value) == "number") then
                table.insert(_VTable, _value)
            end

            _Count = _Count + 1
        end

        -- 照大小給他排序一下
        table.sort(
            _VTable,
            function(k1, k2)
                return k1 < k2
            end
        )

        -- 重新排序
        local _Count = 2
        for _k, _v in pairs(_VTable) do
            for _key, _value in pairs(m_TrackMissionList) do
                if (_value == _v) then
                    m_TrackMissionList[_key] = _Count
                    _Count = _Count + 1
                end
            end
        end

        -- 把他擺到第一位
        m_TrackMissionList[_strFlag] = 1
    else
        m_TrackMissionList[_strFlag] = nil
    end

    if m_TrackMissionList[_strFlag] then
        m_AllMissionList[iFlagID].m_bTrack = true
    else
        m_AllMissionList[iFlagID].m_bTrack = false
    end

    ClientSaveMgr.Save(EClientSaveDataType.MissionTrack)
end

---取得所有可接取任務
function MissionMgr.GetAllMission()
    local _MissionList = DPlotData.GetAssetNameData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK)

    local _ReturnMissionList = {}

    ---依照特定順序篩選前十個可以接取的任務
    local function GetMission(itable)
        for key, value in pairs(itable) do
            if (value.StepBegin > 0) then
                local _Mission = MissionData.New(value)
                if _Mission then
                    _ReturnMissionList[_Mission.FlagID] = _Mission
                end
            end
        end
    end

    GetMission(_MissionList)

    m_AllMissionList = _ReturnMissionList
end

local NowSelectedTab = EMissionType.Main
local NowSelectedMissionList = {}
---切換選取頁籤
function MissionMgr.SwitchMissionTab(iEMissionType)
    NowSelectedTab = iEMissionType
    NowSelectedMissionList = this.GetTabMissionList(NowSelectedTab)
end

---排序特定完成狀態的任務
local function SortByStatus(iMissionList, iEventTipType)
    --排序當前區域>其他區域 高等>低等
    local _SortList = {}
    for _, value in pairs(iMissionList) do
        if value.Finish == iEventTipType then
            table.insert(_SortList, value)
        end
    end

    --先排序等級
    table.sort(
        _SortList,
        function(k1, k2)
            return k1.SuitableLv > k2.SuitableLv
        end
    )

    local _ReturnTable = {}
    for key, value in pairs(_SortList) do
        local _ID = value.m_Step.NPCBegin + value.m_Step.NPCCount - 1
        if _ID > 0 then
            local _EventDPlotNPC = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DNPC, _ID)
            local _isSameScene = false
            for _, v in pairs(_EventDPlotNPC.NPCData) do
                if v.SceneID == SceneMgr.GetSceneID() then
                    _isSameScene = true
                end
            end
            if _isSameScene then
                table.insert(_ReturnTable, value)
                _SortList[key] = nil
            end
        end
    end

    for _, value in pairs(_SortList) do
        table.insert(_ReturnTable, value)
    end

    return _ReturnTable
end

--- 新 種類排序
local function SortByClassity(iMissionList, iEventClassity)
    local _SortList = {}
    for _, value in pairs(iMissionList) do
        if value.Classity == iEventClassity then
            table.insert(_SortList, value)
        end
    end

    return _SortList
end

---刷新選取後的任務資料列表
function MissionMgr.GetTabMissionList(iEMissionType)
    local _SelectedList = {}

    --記得改表還有要排序
    -- 再排序當前區域>其他區域 高等>低等

    for _, value in pairs(m_MissionList) do
        if value.Kind == iEMissionType then
            table.insert(_SelectedList, value)
        end
    end

    for _, value in pairs(m_AllMissionList) do
        if value.Kind == iEMissionType then
            table.insert(_SelectedList, value)
        end
    end
    --排序 可完成 >進行中 >可接取
    --table.sort(_SelectedList,function (k1,k2) return k1.Finish > k2.Finish end)
    local _ReturnList = {}
    local _CanComplete = SortByStatus(_SelectedList, EMissionStatus.CanComplete)
    local _Undone = SortByStatus(_SelectedList, EMissionStatus.Undone)
    local _CanAccept = SortByStatus(_SelectedList, EMissionStatus.CanAccept)

    for _, value in pairs(_CanComplete) do
        table.insert(_ReturnList, value)
    end
    for _, value in pairs(_Undone) do
        table.insert(_ReturnList, value)
    end
    for _, value in pairs(_CanAccept) do
        table.insert(_ReturnList, value)
    end

    return _ReturnList
end

--- 找出排序值後轉成排序的順序 table 做 return ( InsertSort 是排序順序值
local function SortByInsertOrder(iTable)
    local _SortTable = {}
    local _Other = {}
    local _Return = {}
    for _Key, _Value in pairs(iTable) do
        local _strFlag = "" .. _Value.FlagID
        if m_TrackMissionList[_strFlag] ~= nil then
            _Value.InsertSort = m_TrackMissionList[_strFlag]
            table.insert(_SortTable, _Value)
        else
            table.insert(_Other, _Value)
        end
    end

    table.sort(
        _SortTable,
        function(k1, k2)
            if (k1.InsertSort == nil) then
                return false
            end
            if (k2.InsertSort == nil) then
                return false
            end
            return k1.InsertSort < k2.InsertSort
        end
    )

    for _k, _v in pairs(_SortTable) do
        table.insert(_Return, _v)
    end

    for _k, _v in pairs(_Other) do
        table.insert(_Return, _v)
    end

    return _Return
end

---刷新追蹤中的任務資料列表
function MissionMgr.GetTracingMissionList()
    local _SelectedList = {}
    local _PlayerLV = PlayerData.GetLv()
    for _, value in pairs(m_MissionList) do
        if value.m_bTrack then
            table.insert(_SelectedList, value)
        end
    end

    --- 條件 1.進行中主線 2.追蹤中支線 3.可接非主線
    for _, value in pairs(m_AllMissionList) do
        -- 說是新的有填章節 舊的不顯示
        if value.Kind ~= 0 then
            local _IsTrackedBranch = value.m_bTrack  and (value.Classity == EMissionType.Branch or value.Classity == EMissionType.Special)
            local _IsMainOrAcceptable = (value.Classity == EMissionType.Main and value.Finish == EMissionStatus.Undone) or value.Finish == EMissionStatus.CanAccept

            if _IsTrackedBranch or _IsMainOrAcceptable then
                table.insert(_SelectedList, value)
            end
        end
    end

    --排序 可完成 >進行中 >可接取
    local _ReturnList = {}
    local _CanComplete = SortByStatus(_SelectedList, EMissionStatus.CanComplete)
    local _Undone = SortByStatus(_SelectedList, EMissionStatus.Undone)
    local _CanAccept = SortByStatus(_SelectedList, EMissionStatus.CanAccept)

    for _, value in pairs(_CanComplete) do
        table.insert(_ReturnList, value)
    end
    for _, value in pairs(_Undone) do
        table.insert(_ReturnList, value)
    end
    for _, value in pairs(_CanAccept) do
        if _PlayerLV >= value.SuitableLv then
            table.insert(_ReturnList, value)
        end
    end

    -- 新排序 一般: (1)主線、(2)支線、(3)特殊、(4)活動、(5)幫會
    local _SortOrder = {
        EMissionType.Main,
        EMissionType.Branch,
        EMissionType.Special,
        EMissionType.Activity,
        EMissionType.Team
    }
    local _Result = {}

    for _key, _value in pairs(_SortOrder) do
        local _ClassTable = SortByClassity(_ReturnList, _value)

        -- 分支追蹤要做整理
        if (_value == EMissionType.Branch) then
            _ClassTable = SortByInsertOrder(_ClassTable)
        end

        for _, _value2 in pairs(_ClassTable) do
            table.insert(_Result, _value2)
        end
    end

    return _Result
end

---取得選取後頁籤的任務數量
function MissionMgr.GetNowMissionCount()
    return table.Count(NowSelectedMissionList)
end

---取得排序後的任務
function MissionMgr.GetSortedMission(iIndex)
    if NowSelectedMissionList[iIndex] then
        return NowSelectedMissionList[iIndex]
    end
end

---原本是要用這個排任務順序，但現在似乎沒啥用處
function MissionMgr.SetMissionSortType(iClassity)
    if iClassity == EMissionType.Main then
        return 1
    elseif iClassity == EMissionType.Hero then
        return 10
    elseif iClassity == EMissionType.Sharpen then
        return 11
    elseif iClassity == EMissionType.Activity then
        return 2
    elseif iClassity == EMissionType.RoyolTop then
        return 9
    elseif iClassity == EMissionType.Branch then
        return 6
    elseif iClassity == EMissionType.Raid then
        return 4
    elseif iClassity == EMissionType.Stage then
        return 5
    elseif iClassity == EMissionType.Team then
        return 7
    elseif iClassity == EMissionType.Special then
        return 3
    elseif iClassity == EMissionType.More then
        return 8
    else
        return 0
    end
end

---@return MissionData
function MissionMgr.SetMissionData(iPlotData)
    ---@type MissionData
    local _Mission = {}
    _Mission.FlagID = iPlotData.FlagID
    _Mission.Status = 1
    _Mission.EventID = 0
    _Mission.Classity = iPlotData.Classity
    _Mission.IsCanGet = false
    _Mission.SceneID = 0
    _Mission.SuitableLv = iPlotData.SuitableLv
    --_Mission.Sort = this.SetMissionSortType(_Mission.Classity)
    _Mission.TitleStrID = iPlotData.TitleStrID
    _Mission.OutlineStrID = iPlotData.OutLineStrID
    _Mission.CountFlag = iPlotData.CountFlag
    _Mission.POSX = 0
    _Mission.POSY = 0

    -- 新 增加記下第幾章節 這個在最新的變成比較重要的
    _Mission.Kind = iPlotData.Kind

    -- 新 增加記下這個任務的永標 來查找這任務是不是已完成
    _Mission.AFlag = iPlotData.AFlag

    -- 新 增加記下這個任務的前置永標 來查找這任務是不是不達條件接取
    _Mission.ExFlag = iPlotData.ExFlag

    --檢查當前主動標的狀態
    local _Status = PlayerData.GetMovingFlag(_Mission.FlagID).m_Status

    --取得當前步驟ID
    local _Index = (iPlotData.StepBegin + _Status) - 1
    local _Step = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DSTEP, _Index)
    _Mission.m_Step = _Step
    local _StepNPCCount = 0
    local _EventDPlotNPC = {}

    --取得步驟相關字串
    if _Step then
        _Mission.TipStrID = _Step.TipStrID
        _StepNPCCount = _Step.NPCBegin + _Step.NPCCount - 1
        _EventDPlotNPC = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DNPC, _StepNPCCount)
        if _EventDPlotNPC then
            _Mission.EventID = _EventDPlotNPC.NPCData[1].EventID
            _Mission.POSX = _EventDPlotNPC.NPCData[1].POSX
            _Mission.POSY = _EventDPlotNPC.NPCData[1].POSY
            _Mission.SceneID = _EventDPlotNPC.NPCData[1].SceneID
        end
    end

    --取得最後步驟
    _Index = (iPlotData.StepBegin + iPlotData.StepCount) - 1
    _Step = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DSTEP, _Index)
    if (_Step == nil) then
        return
    end
    _StepNPCCount = _Step.NPCBegin + _Step.NPCCount - 1
    _EventDPlotNPC = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DNPC, _StepNPCCount)
    local _finishEventID = nil

    if _EventDPlotNPC then
        _finishEventID = _EventDPlotNPC.NPCData[1].EventID
    end

    if _finishEventID then
        --判斷是否可完成
        _Mission.Finish = EventMgr:GetMissionProgressByEventID(_finishEventID)
        if EventMgr.HUDEvtTip[_Mission.Finish] ~= EMissionStatus.CanComplete and _Status > 0 then
            _Mission.Finish = EMissionStatus.Undone
        end
    else
        _Mission.Finish = 0
    end

    -- 已完成任務
    if (PlayerData_Flags[EFlags.Static].IsHaveFlag(_Mission.AFlag)) then
        _Mission.Finish = EMissionStatus.Done
    end

    -- 為啥不能接 來個理由
    if (_Mission.Finish == 0) then
        -- 等級不夠
        if (_Mission.SuitableLv > PlayerData.GetLv()) then
            _Mission.Finish = EMissionStatus.NoLevel
        elseif (_Mission.ExFlag ~= 0) then
            -- 可接吧
            -- 前置任務沒有完成
            _Mission.Finish = EMissionStatus.NoCondition
        else
            _Mission.Finish = EMissionStatus.CanAccept
        end
    end

    --取得獎勵物品
    _Mission.Item = {}

    local _EventDPlotPrize = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DPRIZE, _Mission.FlagID)

    if _EventDPlotPrize then
        _Mission.Item = _EventDPlotPrize.PrizeData
    end

    -- 如果沒有的話塞
    if (_Mission.EventID == 0 or _Mission.Finish == EMissionStatus.CanAccept) then
        _Mission.EventID = iPlotData.EventFlag
    end

    return _Mission
end

--- 新 取得 QuestPrize 的數量
function MissionMgr.GetChapterCount()
    return QuestPrize.GetCount()
end

--- 新 取得所有章節串檔資料
function MissionMgr.GetAllChapter()
    return QuestPrize.GetTable()
end

--- 新 取出 QuestPrize 中未完成的任務資料
function MissionMgr.GetUnDoneChapter()
    local _UnDoneChapter = {}
    local _QuestPrizeData = QuestPrize.GetTable()

    for key, value in pairs(_QuestPrizeData) do
        local _Flag = PlayerData_Flags[EFlags.Static].IsHaveFlag(value.m_PrizeFlag)
        if _Flag == false and value.m_MissionChapter ~= this.SPECIAL_CHAPTER_IDX then
            table.insert(_UnDoneChapter, value)
        end
    end

    return _UnDoneChapter
end

--- 新 取出 QuestPrize 中已完成的任務資料
function MissionMgr.GetDoneChapter()
    local _DoneChapter = {}
    local _QuestPrizeData = QuestPrize.GetTable()

    for key, value in pairs(_QuestPrizeData) do
        if PlayerData_Flags[EFlags.Static].IsHaveFlag(value.m_PrizeFlag) and value.m_MissionChapter ~= this.SPECIAL_CHAPTER_IDX then
            table.insert(_DoneChapter, value)
        end
    end

    return _DoneChapter
end

--- 取出任務資料中的特殊任務
function MissionMgr.GetSpecialMission()
    local _SpecialMission = {}
    for _, value in pairs(m_AllMissionList) do
        if value.Classity == EMissionType.Special and value.Finish > EMissionStatus.CanAccept then
            table.insert(_SpecialMission, value)
        end
    end
    return _SpecialMission
end

--- 新 以章節來排序任務
function MissionMgr.ArangeMissionByChapter()
    local _ArangedTable = {}

    for key, value in pairs(m_AllMissionList) do
        if _ArangedTable[value.Kind] == nil then
            _ArangedTable[value.Kind] = {}
        end
        table.insert(_ArangedTable[value.Kind], value)
    end

    m_MissionTable = _ArangedTable
end

--- 新 回傳章節任務排序後 Table
function MissionMgr.GetMissionTable()
    return m_MissionTable
end

--- 新 回傳指定章節的任務內容
---@param number iChapter 章節
---@param number iIndex 任務 index
function MissionMgr.GetPickMission(iChapter, iIndex)
    if (m_MissionTable[iChapter]) then
        return m_MissionTable[iChapter][iIndex]
    end
end

--- 新 誰有最多任務 回傳最多任務的章節
function MissionMgr.WhoGotTheMostMission()
    local _Most = {}
    _Most.Num = 0
    _Most.Key = 0

    for key, value in pairs(m_MissionTable) do
        local _NowNum = table.Count(value)

        if (_NowNum > _Most.Num and key ~= 0) then
            _Most.Num = _NowNum
            _Most.Key = key
        end
    end

    return _Most.Key
end

--- 新 整理章節任務
---@param number iChapter 哪張章節需要整理
function MissionMgr.ChapterMissionSorting(iChapter)
    -- 組合 table
    local function TableConcat(t1, t2)
        for i = 1, #t2 do
            t1[#t1 + 1] = t2[i]
        end
        return t1
    end

    -- 沒東西 return
    if (m_MissionTable[iChapter] == nil) then
        return
    end

    -- 先用 Flag 排一次
    table.sort(
        m_MissionTable[iChapter],
        function(k1, k2)
            return k1.FlagID < k2.FlagID
        end
    )

    -- 排序等級
    table.sort(
        m_MissionTable[iChapter],
        function(k1, k2)
            return k1.SuitableLv < k2.SuitableLv
        end
    )

    -- 主支線排序
    local _MainMission = {}
    local _SubMission = {}

    for key, value in pairs(m_MissionTable[iChapter]) do
        if (value.Classity == EMissionType.Main) then
            table.insert(_MainMission, value)
        else
            table.insert(_SubMission, value)
        end
    end

    local _Combine = TableConcat(_MainMission, _SubMission)

    m_MissionTable[iChapter] = _Combine

    -- 任務
    local _UnDoneMission = {}
    local _DoneMission = {}
    for key, value in pairs(m_MissionTable[iChapter]) do
        if (value.Finish ~= EMissionStatus.Done) then
            table.insert(_UnDoneMission, value)
        else
            table.insert(_DoneMission, value)
        end
    end

    local _Result = TableConcat(_UnDoneMission, _DoneMission)

    m_MissionTable[iChapter] = _Result
end

--- 設定查永標用 table
function MissionMgr.SetMissionByStaticFlag()
    for key, value in pairs(m_AllMissionList) do
        if (m_MissionByStaticFlag[value.AFlag] == nil) then
            m_MissionByStaticFlag[value.AFlag] = value
        end
    end
end

function MissionMgr.GetMissionByStaticFlag()
    return m_MissionByStaticFlag
end

function MissionMgr.GetMissionByFlagId(iId)
    if (iId ~= nil and iId ~= 0) then
        return m_AllMissionList[iId]
    end
end

--- 檢查追蹤任務列表有沒有東西
function MissionMgr.CheckTrackMission(iId)
    local _strFlag = "" .. iId
    local _Result = false
    if (m_TrackMissionList[_strFlag] ~= nil and iId ~= 0) then
        _Result = true
    end

    return _Result
end
