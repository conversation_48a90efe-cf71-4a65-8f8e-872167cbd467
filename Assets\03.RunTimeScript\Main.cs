//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2021 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------
//
//=====================================================================

using AssetsBuildSystem;
using GameTools;
using GameTools.FTP;
using GameTools.Json;
using GameTools.Log;
using GameTools.Project;
using GameTools.Sprite;
using System.Collections.Generic;
using UnityEngine;
using static GameTools.ExAPI;
using static UICommonQuery;

namespace LuaFramework
{
    /// <summary>
    /// 程序進入點，處理遊戲初始化與toLua初始化
    /// <AUTHOR>
    /// @version 1.0
    /// @since [ProjectBase] 0.1
    /// @date 2021.10.8
    /// </summary>
    public class Main : MonoSingleton<Main>
    {
        /// <summary>
        /// 執行檔更新資訊
        /// </summary>
        public class ExeUpdateInfo
        {
            /// <summary>
            /// 平台
            /// </summary>
            public RuntimePlatform m_Platform;
            /// <summary>
            /// 平台的版本 ex: Android 的 Google or MyCard
            /// </summary>
            public string m_Version;
            /// <summary>
            /// 更新網址
            /// </summary>
            public string m_Address;
        }

        public enum EStateOfGame : byte
        {
            None,
            /// <summary>
            /// 播放CG
            /// </summary>
            PlayCG,
            /// <summary>
            /// CG 播放中
            /// </summary>
            PlayCGing,
            /// <summary>
            /// 檢查網路
            /// </summary>
            CheckNetwork,
            /// <summary>
            /// 讀取執行檔設定
            /// </summary>
            LoadExeConfig,
            /// <summary>
            /// 等待讀取執行檔設定
            /// </summary>
            WaitingLoadExeConfig,
            /// <summary>
            /// 讀取執行檔更新資訊
            /// </summary>
            LoadExeUpdateInfo,
            /// <summary>
            /// 等待讀取執行檔更新資訊
            /// </summary>
            WaitingLoadExeUpdateInfo,
            /// <summary>
            /// 選擇FTP
            /// </summary>
            FTPSelect,
            /// <summary>
            /// 等待選擇FTP
            /// </summary>
            WaitingFTPSelect,
            /// <summary>
            /// 等待玩家確定下載
            /// </summary>
            WaittingPermissionDownload,
            /// <summary>
            /// Lua script & AssetBundles 下載中
            /// </summary>
            WaitingLuaAndAssetsDownload,
            /// <summary>
            /// Lua script & AssetBundles 下載
            /// *** 注意 這狀態要在 WaitingLuaAndAssetsDownload 後面 ***
            /// </summary>
            LuaAndAssetsDownload,
            /// <summary>
            /// 資源初始化
            /// </summary>
            ResourceInitializing,
            /// <summary>
            /// 等待資源初始化完成
            /// </summary>
            WaitingResourceInitializing,
            /// <summary>
            /// 初始化網路連線
            /// </summary>
            NetWorkInitializing,
            /// <summary>
            /// Lua 初始化
            /// </summary>
            //LuaInitializing,
            /// <summary>
            /// Game.lua 初始化
            /// </summary>
            GameLuaInit,
            /// <summary>
            /// 等待 Game.lua 初始化完成
            /// </summary>
            WaitingGameLuaInit,
            /// <summary>
            /// 處理 toLua Update, 若unity的 update 也有需要的話也統一在這處理
            /// </summary>
            Update,

            /// <summary>
            /// 檢查檔案完整性
            /// </summary>
            FileCheck,
            /// <summary>
            /// 等待檔案完整性檢查完成
            /// </summary>
            WaitingFileCheck,
            Error,
        }

        /// <summary>
        /// 執行檔資料設定
        /// </summary>
        public static string EXE_CONFIG_FILENAME
        {
            get
            {
#if UNITY_STANDALONE
                return "ExeConfig_PC.dat";
#elif UNITY_ANDROID
                return "ExeConfig_Android.dat";
#elif UNITY_IOS
    return "ExeConfig_iOS.dat";
#endif
            }
        }

        /// <summary>
        /// 下載的資源存檔資料夾路徑（會放在執行檔或專案的根目錄中）
        /// </summary>
        public static string DOWNLOAD_RESOURCE_PATH
        {
            get
            {
#if UNITY_EDITOR
                // 編輯器模式
                return System.IO.Directory.GetParent ( Application.dataPath ).FullName;//底層會加上 "/AssetBundles/"
#elif UNITY_STANDALONE
                // PC 平台
                return System.IO.Path.GetDirectoryName(Application.dataPath);
#else
                // 安卓跟 IOS 平台
                return Application.persistentDataPath;
#endif
            }
        }

        private static EStateOfGame State;
        public static EStateOfGame m_State
        {
            get
            {
                return State;
            }
            private set
            {
                if( value == State )
                    return;

                D.Log(
                    string.Format( $"Main State {State} -> {value}" ),
                    value == EStateOfGame.Error ? Color.red : Color.yellow
                );

                State = value;
            }
        }
        /// <summary>
        /// 是否正在檢查檔案完整性，檢查完畢會進入第二次下載與初始化
        /// </summary>
        public static bool m_IsFileChecking = false;

        [SerializeField]
        private UILoading m_UILoading;

        [SerializeField]
        private FTPSelector m_FTPSelector;

        [SerializeField]
        private SpaceShip_Game m_SetSpaceShipGame;
        /// <summary>
        /// 給 FTPSelector 使用
        /// </summary>
        private SettingData m_FTPSettingData = new SettingData();

        private static string m_ShowMsg;

        [SerializeField]
        private CGPlayer m_CGPlayer;

        [SerializeField]
        private UICommonQuery m_UICommonQuery;

        void Start()
        {
//#if UNITY_STANDALONE && !UNITY_STANDALONE_WIN && !UNITY_EDITOR
//            Screen.SetResolution( 1280, 720, false );
//#elif UNITY_STANDALONE_WIN && !UNITY_EDITOR
//            Screen.SetResolution( 1680, 720, false );
//#endif
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
            Screen.orientation = ScreenOrientation.AutoRotation;
            Screen.autorotateToLandscapeLeft = true;
            Screen.autorotateToLandscapeRight = true;
            Screen.autorotateToPortrait = false;
            Screen.autorotateToPortraitUpsideDown = false;

            Application.targetFrameRate = 60;

            D.Inst.Do();
            //未來這邊有分版本在依照 define 給值
            ProjectMgr.Init( new ProjectData_Default() );

            ResourceMgr.Inst.Do();
            //產檔時，測試下載流程時開啟，僅可測試下載流程，進入遊戲所用的Addressable因Editor與產檔機不同，會無法正常開啟
#if UNITY_EDITOR
            //ResourceMgr.m_EditorUseRemoteAssets = true;
#endif
            ResourceMgr.RUNTIME_ASSET_SAVE_DIR = DOWNLOAD_RESOURCE_PATH;

#if toLua
            ResourceMgr.LuaCallAPI = toLuaMgr.Inst.Call;
            ResourceMgr.LuaCallReturnTableAPI = toLuaMgr.Inst.CallFromArray;
#endif
            ResourceMgr.PROFILE_NAME = "HEM";

            toLuaMgr.Inst.RunLuaInit();
            //Sprite Setting
            SpriteMgr.Inst.Do();

            ResTextGetter.Switch();
            StorageUnitGetter.Switch();
            m_UILoading.SetDebugInfo( ResTextGetter.GetResText( "GameInit" ) );

            //FPT Setting

            // 這裡的 key 請看 ProjectData_Default.cs
            // key 會是 $"{m_Version}-{m_Name}"
            // 有多筆的都先取第一筆
#if Debug
            string _CDNKey = "Debug-內部38.89";
#elif QA
            string _CDNKey = "QA-1";
#else
            string _CDNKey = "Release-台灣";
#endif

            if( ProjectMgr.GetData().m_Dic_CDN.TryGetValue( _CDNKey, out CDNData _Data ) )
            {
#if Release
                FTPFileDownloadMgr.DOWNLOAD_ROOT = $"https://{_Data.m_Address}/";
#else
                // 使用 Application.productName 可能會出現不同作業平台轉碼問題, 爆炸再看要怎麼處理
                FTPFileDownloadMgr.DOWNLOAD_ROOT = $"https://{_Data.m_Address}/{Application.productName}/";
#endif
            }
            else
            {
                m_ShowMsg = $"Can`t get CDNData Error : {_CDNKey}";
                m_State = EStateOfGame.Error;
                return;
            }

            FTPFileDownloadMgr.CallBackAPI = ResourceMgr.InvokeCallback;
            //Addressable
            ABDownLoadMgr.CallBackAPI = ResourceMgr.InvokeCallback;
            AddressableLoader.CallBackAPI = ResourceMgr.InvokeCallback;

            //如果需要開啟撥放 CG 的話, 請把下面的註解拿掉, 且註解掉 CGMovieFinish();
            //m_State = EStateOfGame.PlayCG;
            CGMovieFinish();
        }

        void Update()
        {
            switch( m_State )
            {
                case EStateOfGame.None:
                {

                    break;
                }
                case EStateOfGame.PlayCG:
                {
                    //目前遊戲無撥放CG需求 故關閉
                    //m_CGPlayer.gameObject.SetActive( true );
                    //m_CGPlayer.PlayCG("HEM.mp4", CGMovieFinish);
                    m_State = EStateOfGame.CheckNetwork;
                    break;
                }
                case EStateOfGame.PlayCGing:
                {
                    break;
                }
                case EStateOfGame.CheckNetwork:
                {
                    if( Extension.HaveNetwrok() )
                    {
                        m_State = EStateOfGame.LoadExeConfig;
                    }
                    else
                    {
                        m_State = EStateOfGame.Error;
                        m_ShowMsg = ResTextGetter.GetResText( "ChechNetwork" );
                    }
                    break;
                }
                case EStateOfGame.LoadExeConfig:
                {//讀取執行檔設定
                    m_State = EStateOfGame.WaitingLoadExeConfig;
                    CoroutineMgr.StartCoroutineEx( FTPFileDownloadMgr.IELoadJSONFromWeb( EXE_CONFIG_FILENAME, (Callback_SystemObj)LoadExeConfigFinish ) );

                    break;
                }
                case EStateOfGame.WaitingLoadExeConfig:
                {//等待讀取執行檔設定
                    break;
                }
                case EStateOfGame.LoadExeUpdateInfo:
                {
                    m_State = EStateOfGame.WaitingLoadExeUpdateInfo;
                    CoroutineMgr.StartCoroutineEx( FTPFileDownloadMgr.IELoadJSONFromWeb( "ExeUpdateInfo.dat", (Callback_SystemObj)LoadExeUpdateInfoFinish ) );

                    break;
                }
                case EStateOfGame.WaitingLoadExeUpdateInfo:
                {//等待讀取新版本資料
                    break;
                }
                case EStateOfGame.FTPSelect:
                {
#if Release
                    m_State = EStateOfGame.LuaAndAssetsDownload;
#else
                    if( m_FTPSelector != null )
                    {
                        m_State = EStateOfGame.WaitingFTPSelect;
                        m_FTPSettingData.m_ProjectName = Application.productName;
                        m_FTPSettingData.m_BundleVersion = Application.version;
                        m_FTPSettingData.m_RunTimeResourcesVersion = ResourceMgr.RUNTIME_RESOURCE_VERSION;
                        m_FTPSettingData.m_Dic_CDNData = ProjectMgr.GetData().m_Dic_CDN;
                        //m_FTPSettingData.m_List_ExeConfig 在 LoadExeConfigFinish 有設定

                        m_FTPSelector.Init( m_FTPSettingData, FTPSelectFinish );
                    }
                    else
                    {
                        FTPSelectFinish( null );
                    }
#endif
                    break;
                }
                case EStateOfGame.WaitingFTPSelect:
                {
                    break;
                }
                case EStateOfGame.LuaAndAssetsDownload:
                {//下載資源
                    List<ABDownLoadMgr.DLInfo> _List = new List<ABDownLoadMgr.DLInfo>
                    {
                        new ABDownLoadMgr.DLInfo
                        {
                            m_TitleLog = "[AssetBundle]",
                            m_Version = ResourceMgr.RUNTIME_RESOURCE_VERSION,
                            //依照玩家選項決定是否下載全部
                            m_IsDownloadAll = true,
                            m_RemotePath = ResourceMgr.RUNTIME_FTP_RESOURCE_PATH,
                            m_ABListFileName = AssetsBuildSystem.Ex.ASSET_BUNDLE_LIST_FILENAME,
                            m_RuntimeSavePath = ResourceMgr.RUNTIME_ASSET_BUNDLE_SAVE_DIR,
                        },
#if toLua
                        new ABDownLoadMgr.DLInfo
                        {
                            m_TitleLog = "[LuaScript]",
                            m_Version = ResourceMgr.RUNTIME_RESOURCE_VERSION,
                            //依照玩家選項決定是否下載全部
                            m_IsDownloadAll = true,
                            m_RemotePath = ResourceMgr.RUNTIME_FTP_LUA_SCRIPT_PATH,
                            m_ABListFileName = AssetsBuildSystem.Ex.LUA_SCRIPT_ASSET_BUNDLE_LIST_FILENAME,
                            m_RuntimeSavePath = ResourceMgr.RUNTIME_LUA_SCRIPT_SAVE_DIR,
                        }
#endif
                    };

#if UNITY_EDITOR
                    if( ResourceMgr.m_EditorUseRemoteAssets )
                    {
                        m_State = EStateOfGame.WaittingPermissionDownload;
                        ABDownLoadMgr.Inst.Run( _List, LuaAndAssetsDownloadFinish, true, LuaAndAssetsCompareFinish );
                    }
                    else
                    {
                        SystemObject _SObj = new SystemObject();
                        _SObj.m_Data = true;
                        LuaAndAssetsDownloadFinish( _SObj );
                    }
#else
                    m_State = EStateOfGame.WaittingPermissionDownload;
                    ABDownLoadMgr.Inst.Run( _List, LuaAndAssetsDownloadFinish, true, LuaAndAssetsCompareFinish );
#endif
                    break;
                }
                case EStateOfGame.WaittingPermissionDownload:
                {
                    break;
                }
                case EStateOfGame.WaitingLuaAndAssetsDownload:
                {//Lua script下載中
                    if( ABDownLoadMgr.Inst.m_TotleProgress > 0 )
                    {
                        if( ABDownLoadMgr.Inst.m_TotleProgress > 0 )
                        {
                            string _CurMB = new ByteSizeLib.ByteSize( ABDownLoadMgr.Inst.m_DownloadedBytes ).MebiBytes.ToString( "0.00" );
                            string _TotalMB = new ByteSizeLib.ByteSize( ABDownLoadMgr.Inst.m_TotalDLBytes ).MebiBytes.ToString( "0.00" );

                            //下載資源 : {0} / {1}
                            string _Msg = ResTextGetter.GetResText( "DLSize" );
                            _Msg = string.Format( _Msg, _CurMB, _TotalMB ) + " " + StorageUnitGetter.GetStorageUnit( 2 );
                            //m_UILoading.SetDownLoadInfo( _Msg, ABDownLoadMgr.Inst.m_TotleProgress );
                            m_UILoading.SetDetail( _Msg );

                        }
                    }
                    //檢查飛船是否加載完成，若完成則進行update
                    if( m_SetSpaceShipGame.IsInitComplete() )
                        m_SetSpaceShipGame.OnUpdate();

                    break;
                }
                case EStateOfGame.ResourceInitializing:
                {
                    m_SetSpaceShipGame.CheckGameClose();
#if UNITY_EDITOR
                    if ( ResourceMgr.m_EditorUseRemoteAssets )
                    {
                        m_State = EStateOfGame.WaitingResourceInitializing;
                        string _CataLogPath = ResourceMgr.GetCataLogPath();
                        AddressableLoader.Init( _CataLogPath, (Callback_SystemObj)InitCataLogFinish, ResourceMgr.RUNTIME_ASSET_SAVE_DIR );
                    }
                    else
                    {
                        m_State = EStateOfGame.WaitingResourceInitializing;
                        InitCataLogFinish( new SystemObject() { m_Data = true } );
                    }
#else
                    m_State = EStateOfGame.WaitingResourceInitializing;
                    string _CataLogPath = ResourceMgr.GetCataLogPath();
                    AddressableLoader.Init( _CataLogPath, (Callback_SystemObj)InitCataLogFinish, ResourceMgr.RUNTIME_ASSET_SAVE_DIR );
#endif
                    break;
                }
                case EStateOfGame.WaitingResourceInitializing:
                {
                    break;
                }
                case EStateOfGame.NetWorkInitializing:
                {
                    ClientSocket.Inst.Do();
                    m_State = EStateOfGame.GameLuaInit;
                    break;
                }
                case EStateOfGame.GameLuaInit:
                {
                    m_State = EStateOfGame.WaitingGameLuaInit;
                    InitGameLua();
                    break;
                }
                case EStateOfGame.WaitingGameLuaInit:
                {
                    if( toLuaMgr.Inst.m_IsGameInit && toLuaMgr.Inst.CallFunction<bool>( "DownloadFileMgr.IsDownloadAllJsonFile" )
                                                    && toLuaMgr.Inst.CallFunction<bool>( "DataMgr.IsAllDataInitialized" ) )
                    {
                        D.Log( "Load Json Finish " );
                        toLuaMgr.Inst.CallFunction( "Game.OpenLoginUI" );
                        //m_UILoading.SetUIActive( false );
                        m_State = EStateOfGame.Update;
                    }
                    break;
                }
                case EStateOfGame.Update:
                {
                    toLuaMgr.Inst.CallFunction( "Game.Update" );
                    break;
                }
                case EStateOfGame.Error:
                {
                    UICommonQuery.Info _Info = new UICommonQuery.Info();
                    _Info.m_Title = "";
                    _Info.m_Msg = m_ShowMsg;
                    _Info.m_Dic_UseBtn.TryAdd( EBtnType.Right, ResTextGetter.GetResText( "Confirm" ) );
                    _Info.m_Onclick = OnReStart;
                    m_UICommonQuery.SetMsg( _Info );

                    m_State = EStateOfGame.None;
                    break;
                }
                case EStateOfGame.FileCheck:
                {
                    m_State = EStateOfGame.WaitingFileCheck;
                    //關閉LuaMgr
                        toLuaMgr.Inst.RunLuaInit();
                    //重新生成 ExeCommonQuery_View
                    GameObject _ExeCommonQueryPrefab = Resources.Load<GameObject>("UI/ExeCommonQuery_View");
                    if (_ExeCommonQueryPrefab != null)
                    {
                        GameObject _ExeCommonQueryInstance = Instantiate(_ExeCommonQueryPrefab);
                        _ExeCommonQueryInstance.transform.SetParent(GameObject.Find("Peak").transform, false);
                        _ExeCommonQueryInstance.transform.localPosition = Vector3.zero;
                        _ExeCommonQueryInstance.transform.localScale = Vector3.one;
                        _ExeCommonQueryInstance.transform.localRotation = Quaternion.identity;
                        m_UICommonQuery = _ExeCommonQueryInstance.GetComponent<UICommonQuery>();
                        _ExeCommonQueryInstance.SetActive(false);
                        _ExeCommonQueryInstance.name = "ExeCommonQuery_View";

                        // Loading 的 SortingLayer 在遊戲中會被更改，這邊把 ExeCommonQuery_View 的 SortingLayer 設定回 Loading 上面
                        _ExeCommonQueryInstance.GetComponent<Canvas>().sortingOrder = m_UILoading.gameObject.GetComponent<Canvas>().sortingOrder + 1;
                        // Loading 名稱在遊戲中會被更改，這邊把 他改回Loading_View 讓初始化找的到
                        m_UILoading.gameObject.name = "Loading_View";
                    }
                    else
                    {
                        Debug.LogError("Failed to load ExeCommonQuery_View prefab.");
                    }
                    //檢查檔案
                    m_UILoading.SetUIActive( true );
                    m_UILoading.SetDebugInfo("驗證AssetsBundle");
                    CoroutineMgr.StartCoroutineEx(
                        ABDownLoadMgr.CreateABList_FileCheck(
                            ResourceMgr.RUNTIME_RESOURCE_VERSION
                            ,ResourceMgr.RUNTIME_ASSET_SAVE_DIR + "/AssetBundles"
                            , ResourceMgr.RUNTIME_ASSET_BUNDLE_SAVE_DIR + AssetsBuildSystem.Ex.ASSET_BUNDLE_LIST_FILENAME
                            , OnFileCheckProgress, OnFileCheckAssetsBundleFinish, "LuaScript"));


                    break;
                }
                case EStateOfGame.WaitingFileCheck:
                {
                    break;
                }
            }
        }

        private void FixedUpdate()
        {
            switch( m_State )
            {
                case EStateOfGame.Update:
                {
                    toLuaMgr.Inst.CallFunction( "Game.FixedUpdate" );
                    break;
                }
            }
        }

        private void LateUpdate()
        {
            switch( m_State )
            {
                case EStateOfGame.Update:
                {
                    toLuaMgr.Inst.CallFunction( "Game.LateUpdate" );
                    break;
                }
            }
        }

        /// <summary>
        /// 重新啟動遊戲
        /// </summary>
        public void Restart()
        {
            m_State = EStateOfGame.None;
        }

        private void CGMovieFinish()
        {
            if( m_CGPlayer != null )
            {
                m_CGPlayer.gameObject.SetActive( false );
            }
            m_State = EStateOfGame.CheckNetwork;
        }

        /// <summary>
        /// 讀取 ExeConfig 完成
        /// 並確認有沒有新的遊戲版本
        /// </summary>
        /// <param name="iObj"> string </param>
        private void LoadExeConfigFinish( SystemObject iObj )
        {
            string _Result = (string)iObj.m_Data;
            if( string.IsNullOrEmpty( _Result ) )
            {
                m_ShowMsg = $"{EXE_CONFIG_FILENAME} 無法讀取";
                m_State = EStateOfGame.Error;
                return;
            }

            List<ExeConfig> _List_ExeConfig = JsonTools.FromJson<List<ExeConfig>>( _Result, null );
            if( _List_ExeConfig == null )
            {
                m_ShowMsg = $"{EXE_CONFIG_FILENAME} 資料異常";
                m_State = EStateOfGame.Error;
                return;
            }

            ExeConfig _Config = null;
            float _CurVersion = float.TryParse( Application.version, out _CurVersion ) ? _CurVersion : 0;
            //float _NewVersion = 0;
            bool _HaveNewVersion = true;
            for( int i = 0; i < _List_ExeConfig.Count; i++ )
            {
                float _TmpVersion = float.TryParse( _List_ExeConfig[ i ].m_ExeVer, out _TmpVersion ) ? _TmpVersion : 0;
                if( _TmpVersion == _CurVersion )
                {
                    _HaveNewVersion = false;
                    _Config = _List_ExeConfig[ i ];
                }
                // 用有更大版本號時更新會有，外部玩家在1.1版時，正在送審1.2，因送審會用同一台FTP，所以玩家就會被要求更新不存在的1.2版
                // 因此將判斷改為與 HEM 1.0 相同，當ExeConfig上不存在該版本時跳出要求更新，企劃會在送審時加入新版本，外部更新時將舊版本刪除
                /* if ( _TmpVersion > _CurVersion )
                 {
                     D.Log("_TmpVersion{0} > _CurVersion{1}", _List_ExeConfig[i].m_ExeVer, _CurVersion);
                     m_ShowMsg = string.Format( ResTextGetter.GetResText( "HaveNewVersion" ), _List_ExeConfig[ i ].m_ExeVer );
                     _NewVersion = _TmpVersion;
                     _HaveNewVersion = true;
                     break;
                 }*/
            }

#if UNITY_EDITOR
            _HaveNewVersion = false;
#endif

            if( !_HaveNewVersion )
            {
                ResourceMgr.RUNTIME_RESOURCE_VERSION = _Config.m_ResourceVer;
                if( !ResourceMgr.RUNTIME_RESOURCE_VERSION.Contains( Application.version ) )
                {
                    m_ShowMsg = $"{EXE_CONFIG_FILENAME} 資源版本異常\nVersion: {ResourceMgr.RUNTIME_RESOURCE_VERSION}";
                    m_State = EStateOfGame.Error;
                }
                else
                {
                    m_FTPSettingData.m_List_ExeConfig = _List_ExeConfig;
                    m_State = EStateOfGame.FTPSelect;
                }
            }
            else
            {
                D.Log( "No match config version" );
                m_ShowMsg = string.Format( ResTextGetter.GetResText( "HaveNewVersion" ) );
                m_State = EStateOfGame.LoadExeUpdateInfo;
            }
        }

        #region 檢查是否有新版Exe
        private ExeUpdateInfo m_ExeUpdateInfo;
        private void LoadExeUpdateInfoFinish( SystemObject iObj )
        {
            string _Result = (string)iObj.m_Data;
            if( string.IsNullOrEmpty( _Result ) )
            {
                m_ShowMsg = ResTextGetter.GetResText( "ExeUpdateInfoError" );
                m_State = EStateOfGame.Error;
                return;
            }

            List<ExeUpdateInfo> _List = JsonTools.FromJson<List<ExeUpdateInfo>>( _Result, null );
            if( _List == null )
            {
                m_ShowMsg = ResTextGetter.GetResText( "ExeUpdateInfoError" );
                m_State = EStateOfGame.Error;
                return;
            }


#if UNITY_ANDROID
#if MyCard
            string _Version = "MyCard";
#else
            string _Version = "Google";
#endif
#elif UNITY_IOS
            string _Version = "Apple";
#elif UNITY_STANDALONE_WIN
            string _Version = "Windows";
#endif
            m_ExeUpdateInfo = _List.Find( x => x.m_Platform == Application.platform && x.m_Version == _Version );

            UICommonQuery.Info _Info = new UICommonQuery.Info();
            _Info.m_Title = "";
            //在確認有沒有新版本的時候就設定好了
            _Info.m_Msg = m_ShowMsg;
            _Info.m_Dic_UseBtn.TryAdd( EBtnType.Right, ResTextGetter.GetResText( "Confirm" ) );
            _Info.m_Dic_UseBtn.TryAdd( EBtnType.Left, ResTextGetter.GetResText( "Cancel" ) );
            _Info.m_Onclick = OnOpenExeWeb;
            m_UICommonQuery.SetMsg( _Info );
        }

        private void OnOpenExeWeb( UICommonQuery.EBtnType iBtnType )
        {
            switch( iBtnType )
            {
                case EBtnType.Right:    //確認
                    m_State = EStateOfGame.None;
                    Application.OpenURL( m_ExeUpdateInfo.m_Address );
                    break;
                case EBtnType.Left:     //取消
                    OnApplicationQuit();
                    break;
            }
        }
        #endregion

        private void FTPSelectFinish( SelectData iSettingData )
        {
            //將 SettingData 回塞
            if( iSettingData != null )
            {
                switch( iSettingData.m_CDNData.m_Version )
                {
                    case "Debug":
                        // 使用 Application.productName 可能會出現不同作業平台轉碼問題, 爆炸再看要怎麼處理
                        FTPFileDownloadMgr.DOWNLOAD_ROOT = $"https://{iSettingData.m_CDNData.m_Address}/{Application.productName}/";
                        break;
                    default:
                        FTPFileDownloadMgr.DOWNLOAD_ROOT = $"https://{iSettingData.m_CDNData.m_Address}";
                        break;
                }

                ResourceMgr.RUNTIME_RESOURCE_VERSION = iSettingData.m_RunTimeResourcesVersion;
            }

            m_UILoading.SetVersionInfo( Application.version, ResourceMgr.RUNTIME_RESOURCE_VERSION );
            m_State = EStateOfGame.LuaAndAssetsDownload;
        }

        private void LuaAndAssetsCompareFinish( SystemObject iObj )
        {
            if( (bool)iObj.m_Data )
            {
                D.Log( "LuaAndAssetsCompareFinish" );
                // 資源比對結束，不須下載
                if (m_IsFileChecking && ABDownLoadMgr.Inst.m_TotalDLBytes <= 0)
                {
                    string _Msg = ResTextGetter.GetResText( "FileCheckCorrect" );
                    UICommonQuery.Info _Info = new UICommonQuery.Info();
                    _Info.m_Title = "";
                    _Info.m_Msg = _Msg;
                    _Info.m_Dic_UseBtn.TryAdd( EBtnType.Right, ResTextGetter.GetResText( "Confirm" ) );
                    _Info.m_Onclick = OnPermissionDownload;
                    m_UICommonQuery.SetMsg( _Info );
                }
                else
                {
                    string _Msg = ResTextGetter.GetResText( "CheckDownload" );
                    string _TotalMB = new ByteSizeLib.ByteSize( ABDownLoadMgr.Inst.m_TotalDLBytes ).MebiBytes.ToString( "0.##" );
                    _Msg = string.Format( _Msg, _TotalMB, StorageUnitGetter.GetStorageUnit( 2 ) );

                    UICommonQuery.Info _Info = new UICommonQuery.Info();
                    _Info.m_Title = "";
                    _Info.m_Msg = _Msg;
                    _Info.m_Dic_UseBtn.TryAdd( EBtnType.Right, ResTextGetter.GetResText( "Confirm" ) );
                    _Info.m_Dic_UseBtn.TryAdd( EBtnType.Left, ResTextGetter.GetResText( "Cancel" ) );
                    _Info.m_Onclick = OnPermissionDownload;

                    m_UICommonQuery.SetMsg( _Info );
                }
            }
            else
            {
                //資源比對錯誤, 應該是不會跳
                m_ShowMsg = ResTextGetter.GetResText( "CheckAssets" );
                m_State = EStateOfGame.Error;
            }
        }

        private void OnPermissionDownload( UICommonQuery.EBtnType iBtnType )
        {
            switch( iBtnType )
            {
                case EBtnType.Right:    //確認
                    m_SetSpaceShipGame.Init();
                    ABDownLoadMgr.Inst.m_Flow = EFlow.Download;
                    m_State = EStateOfGame.WaitingLuaAndAssetsDownload;
                    break;
                case EBtnType.Left:     //取消
                    string _Msg = ResTextGetter.GetResText( "ReStart" );
                    UICommonQuery.Info _Info = new UICommonQuery.Info();
                    _Info.m_Title = "";
                    _Info.m_Msg = _Msg;
                    _Info.m_Dic_UseBtn.TryAdd( EBtnType.Right, ResTextGetter.GetResText( "Confirm" ) );
                    _Info.m_Onclick = OnReStart;
                    m_UICommonQuery.SetMsg( _Info );
                    break;
            }
        }

        private void OnReStart( UICommonQuery.EBtnType iBtnType )
        {
            switch( iBtnType )
            {
                case EBtnType.Right:    //確認
                    OnApplicationQuit();
                    break;
            }
        }

        private void LuaAndAssetsDownloadFinish( SystemObject iObj )
        {
            if( (bool)iObj.m_Data )
            {
                m_UILoading.DownloadFinish();
                D.Log( "LuaAndAssetsDownloadFinish" );
                toLuaMgr.Inst.Do();
                m_State = EStateOfGame.ResourceInitializing;
            }
            else
            {
                m_ShowMsg = ResTextGetter.GetResText( "DLAssetsError" );
                m_State = EStateOfGame.Error;
            }
        }

        private void InitCataLogFinish( SystemObject iObj )
        {
            if( (bool)iObj.m_Data )
            {
                D.Log( "InitCataLogFinish" );
                SpriteMgr.Switch( "GameSpriteData", SpriteSwitchFinish );
            }
            else
            {
                m_ShowMsg = ResTextGetter.GetResText( "InitAssetsError" );
                m_State = EStateOfGame.Error;
            }
        }

        private void SpriteSwitchFinish( bool iResult )
        {
            if( iResult )
            {
                m_State = EStateOfGame.NetWorkInitializing;
            }
            else
            {
                //m_ShowMsg = $"切換 SpriteAtlas 錯誤";
                m_ShowMsg = ResTextGetter.GetResText( "InitAssetsError" );
                m_State = EStateOfGame.Error;
            }
        }

        private void InitGameLua()
        {
            try
            {
                //加載 .Lua
                toLuaMgr.Inst.DoFile( "Logic/Game" );
                //初始化 .Lua
                toLuaToCSharpDelegate _CSharpDelegate = new toLuaToCSharpDelegate( GameLuaInitFinish );
                toLuaMgr.Inst.CallFunction( "Game.Init", _CSharpDelegate );
#if UNITY_EDITOR
                toLuaMgr.Inst.DoString( "xpcall(require, D.LogError, \"Common/LuaDebug\")" );
#endif
            }
            catch( System.Exception _E )
            {
                D.LogException( "[Main]", _E );
                m_ShowMsg = ResTextGetter.GetResText( "InitAssetsError" );
                State = EStateOfGame.Error;
                throw;
            }
        }

        public void GameLuaInitFinish()
        {
            toLuaMgr.Inst.m_IsGameInit = true;
        }

        void OnApplicationFocus( bool iIsFocus )
        {
            if( m_State >= EStateOfGame.Update && toLuaMgr.Inst.m_IsGameInit )
            {
                toLuaMgr.Inst.CallFunction( "Game.OnApplicationFocus", iIsFocus );
            }
        }

        void OnApplicationPause( bool iIsPause )
        {
            if( m_State >= EStateOfGame.Update && toLuaMgr.Inst.m_IsGameInit )
            {
                toLuaMgr.Inst.CallFunction( "Game.OnApplicationPause", iIsPause );
            }
        }

        void OnApplicationQuit()
        {
            // 先中斷連線
            ClientSocket.Disconnect();

            if( m_State >= EStateOfGame.Update && toLuaMgr.Inst.m_IsGameInit )
            {
                toLuaMgr.Inst.CallFunction( "Game.OnApplicationQuit" );
                toLuaMgr.Inst.Close();
                DestroyImmediate( toLuaMgr.Inst.gameObject );
            }

#if UNITY_EDITOR
            if( ResourceMgr.m_EditorUseRemoteAssets )
            {// 把模式改回 0 避免下次 Play 時有 Error
                UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings.ActivePlayModeDataBuilderIndex = (int)0;
            }

            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }

        /// <summary>
        /// 檔案完整性檢查進度
        /// </summary>
        private void OnFileCheckProgress(long iProgress)
        {
            m_UILoading.SetDetail(string.Format(ResTextGetter.GetResText("FileCheckSize"), iProgress/1024/1024));
        }

        /// <summary>
        /// 檢查檔案完整性完成
        /// </summary>
        private void OnFileCheckAssetsBundleFinish()
        {
            D.Log( "OnFileCheckAssetsBundleFinish" );
            m_UILoading.SetDebugInfo("驗證LuaScript");
            CoroutineMgr.StartCoroutineEx(
                ABDownLoadMgr.CreateABList_FileCheck(ResourceMgr.RUNTIME_RESOURCE_VERSION
                ,ResourceMgr.RUNTIME_ASSET_SAVE_DIR + "/AssetBundles/LuaScript"
                ,ResourceMgr.RUNTIME_ASSET_SAVE_DIR + "/AssetBundles/LuaScript/" + AssetsBuildSystem.Ex.LUA_SCRIPT_ASSET_BUNDLE_LIST_FILENAME
                ,OnFileCheckProgress,OnFileCheckFinish));
        }
        /// <summary>
        /// 檢查檔案完整性完成
        /// </summary>
        private void OnFileCheckFinish()
        {
            D.Log( "FileCheckFinish" );
            m_IsFileChecking = true;
            m_State = EStateOfGame.LuaAndAssetsDownload;
        }
        /// <summary>
        /// 設定遊戲狀態，給初始化後使用
        /// </summary>
        /// <param name="iState"></param>
        public static void SetStateFileCheck()
        {
            m_State = EStateOfGame.FileCheck;
        }
    }
}
