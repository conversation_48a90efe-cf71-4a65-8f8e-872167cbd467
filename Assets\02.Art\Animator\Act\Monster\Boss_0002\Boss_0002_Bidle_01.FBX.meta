fileFormatVersion: 2
guid: 7cd066d222ed2564183c7fd1ed7eed6a
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 L Calf
    100008: Bip001 L Clavicle
    100010: Bip001 L Finger0
    100012: Bip001 L Finger1
    100014: Bip001 L Finger2
    100016: Bip001 L Finger3
    100018: Bip001 L Foot
    100020: Bip001 L Forearm
    100022: Bip001 L Hand
    100024: Bip001 L Thigh
    100026: Bip001 L Toe0
    100028: Bip001 L Toe1
    100030: Bip001 L Toe2
    100032: Bip001 L Toe3
    100034: Bip001 L UpperArm
    100036: Bip001 Neck
    100038: Bip001 Pelvis
    100040: Bip001 R Calf
    100042: Bip001 R Clavicle
    100044: Bip001 R Finger0
    100046: Bip001 R Finger1
    100048: Bip001 R Finger2
    100050: Bip001 R Finger3
    100052: Bip001 R Foot
    100054: Bip001 R Forearm
    100056: Bip001 R Hand
    100058: Bip001 R Thigh
    100060: Bip001 R Toe0
    100062: Bip001 R Toe1
    100064: Bip001 R Toe2
    100066: Bip001 R Toe3
    100068: Bip001 R UpperArm
    100070: Bip001 Spine
    100072: Bip001 Spine1
    100074: Bip001 Tail
    100076: Bip001 Tail1
    100078: Bip001 Tail2
    100080: Bip001 Tail3
    100082: Bip001 Tail4
    100084: Bip001 TailNub
    100086: Bone001
    100088: Bone002
    100090: Bone003
    100092: Bone004
    100094: Bone006
    100096: Bone007
    100098: Bone008
    100100: Bone009
    100102: //RootNode
    100104: L Beard01
    100106: L Beard02
    100108: L Beard03
    100110: L Beard04
    100112: L Beard05
    100114: L Beard06
    100116: L Beard07
    100118: L Beard08
    100120: L Beard09
    100122: L Beard10
    100124: L Beard11
    100126: L Beard12
    100128: L Finger01
    100130: L Finger02
    100132: L Finger03
    100134: L Finger04
    100136: L Toe03
    100138: L Toe04
    100140: mouth
    100142: mouth2
    100144: R Beard01
    100146: R Beard02
    100148: R Beard03
    100150: R Beard04
    100152: R Beard05
    100154: R Beard06
    100156: R Beard07
    100158: R Beard08
    100160: R Beard09
    100162: R Beard10
    100164: R Beard11
    100166: R Beard12
    100168: R Finger01
    100170: R Finger02
    100172: R Finger03
    100174: R Finger04
    100176: R Toe005
    100178: R Toe006
    100180: R Toe01
    100182: R Toe02
    100184: R Toe03
    100186: R Toe04
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 L Calf
    400008: Bip001 L Clavicle
    400010: Bip001 L Finger0
    400012: Bip001 L Finger1
    400014: Bip001 L Finger2
    400016: Bip001 L Finger3
    400018: Bip001 L Foot
    400020: Bip001 L Forearm
    400022: Bip001 L Hand
    400024: Bip001 L Thigh
    400026: Bip001 L Toe0
    400028: Bip001 L Toe1
    400030: Bip001 L Toe2
    400032: Bip001 L Toe3
    400034: Bip001 L UpperArm
    400036: Bip001 Neck
    400038: Bip001 Pelvis
    400040: Bip001 R Calf
    400042: Bip001 R Clavicle
    400044: Bip001 R Finger0
    400046: Bip001 R Finger1
    400048: Bip001 R Finger2
    400050: Bip001 R Finger3
    400052: Bip001 R Foot
    400054: Bip001 R Forearm
    400056: Bip001 R Hand
    400058: Bip001 R Thigh
    400060: Bip001 R Toe0
    400062: Bip001 R Toe1
    400064: Bip001 R Toe2
    400066: Bip001 R Toe3
    400068: Bip001 R UpperArm
    400070: Bip001 Spine
    400072: Bip001 Spine1
    400074: Bip001 Tail
    400076: Bip001 Tail1
    400078: Bip001 Tail2
    400080: Bip001 Tail3
    400082: Bip001 Tail4
    400084: Bip001 TailNub
    400086: Bone001
    400088: Bone002
    400090: Bone003
    400092: Bone004
    400094: Bone006
    400096: Bone007
    400098: Bone008
    400100: Bone009
    400102: //RootNode
    400104: L Beard01
    400106: L Beard02
    400108: L Beard03
    400110: L Beard04
    400112: L Beard05
    400114: L Beard06
    400116: L Beard07
    400118: L Beard08
    400120: L Beard09
    400122: L Beard10
    400124: L Beard11
    400126: L Beard12
    400128: L Finger01
    400130: L Finger02
    400132: L Finger03
    400134: L Finger04
    400136: L Toe03
    400138: L Toe04
    400140: mouth
    400142: mouth2
    400144: R Beard01
    400146: R Beard02
    400148: R Beard03
    400150: R Beard04
    400152: R Beard05
    400154: R Beard06
    400156: R Beard07
    400158: R Beard08
    400160: R Beard09
    400162: R Beard10
    400164: R Beard11
    400166: R Beard12
    400168: R Finger01
    400170: R Finger02
    400172: R Finger03
    400174: R Finger04
    400176: R Toe005
    400178: R Toe006
    400180: R Toe01
    400182: R Toe02
    400184: R Toe03
    400186: R Toe04
    7400000: Boss_0002_Bidle_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Boss_0002_Bidle_01
      takeName: Boss_0002_Bidle_01
      firstFrame: 0
      lastFrame: 80
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 3ea3c70de92b4204aab9bc4e44195188,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
