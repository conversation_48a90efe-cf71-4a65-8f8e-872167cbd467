---=====================================================================
---              中華網龍 PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with 中華網龍 and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by 中華網龍.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---探索日誌
---@class ExploreDairy_Controller
---author 張瑋
---telephone #1234
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.9.4
ExploreDairy_Controller = {}

--- 任務頁面
require("UI/MissionBook/MissionBook_Controller")
--- 成就頁面
require("UI/Achievement/Achievement_Controller")
--- 圖鑑頁面
require("UI/Collect/Collect_Controller")

---探索日誌有幾個子頁
EExploreDairySubIndex =
{
	--- 任務 1
	Mission = 1,
	--- 成就 2
	Achievement = 2,
	--- 圖鑑收藏 3
	Collect = 3,
}

ExploreDairy_Controller[EExploreDairySubIndex.Mission] = MissionBook_Controller
ExploreDairy_Controller[EExploreDairySubIndex.Achievement] = Achievement_Controller
ExploreDairy_Controller[EExploreDairySubIndex.Collect] = Collect_Controller

local this = ExploreDairy_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("ExploreDairy_View", "ExploreDairy_Controller", EUIOrderLayers.FullPage, false, "bg_005")
--this.m_UIView = "ExploreDairy_View"
--this.m_UIController = "ExploreDairy_Controller"
--this.m_NowShowPage = EMissionType.Main
--this.m_UIOrderLayerKey = EUIOrderLayers.FullPage
--this.m_IsAutomaticStackingFullPage = true
--this.m_RawImage_BG = "bg_000"

local COLOR_BLUE  = Extension.GetColor("#eaf4f6")
local COLOR_GREY  = Extension.GetColor("#3b3143")
local CHECK_ICON = "Activity_icon_02"

local BtnStatus = {
	Disable = 1,
	Enable = 2,
	Collected = 3,
}

local function GetPointBtnColor(iStatus)

	if(iStatus == BtnStatus.Disable) then
		return Extension.GetColor("#3b3143")
	elseif(iStatus == BtnStatus.Enable) then
		return Extension.GetColor("#0197bd")
	elseif(iStatus == BtnStatus.Collected) then
		return Extension.GetColor("#e39633")
	end

end

local function GetPageNameString(iIndex)
	
	return TextData.Get(20600005) .. "-" .. TextData.Get(20600005 + iIndex)

end

---時空幣 道具ID
local _SpaceCoint = 95011

--- 現在頁面
this.m_CurrentPage = EExploreDairySubIndex.Mission

--- 開啟參數
this.m_OpenPageParam = nil

--- 頁面GObj
this.m_PageGameObject = {}

--- 複製物件 Table
this.m_ReuseItemsTable = {}

---Bar 上面的動態特效相關設定
this.m_Bar_TweenEffectID = 0
---初始位置
local m_Start_BarEffectObj = Vector3(0,0,0) 
---終點位置
local m_End_BarEffectObj = Vector3(0,0,0) 
---移動速度
local m_Speed_EffectObj = 311
---一個週期耗費時間
local m_EffectPeriod = 0
---tween 是否在執行中
this.m_IsPlayingBarEffect = false
---按鈕的名稱
local _IconTable = {"icon_011_D", "MainIcon_092_D", "MainIcon_070_D"}

---初始化
function ExploreDairy_Controller.Init()
	local _CurrentResourceTable = 
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }

	this.m_Transform_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&Main_Panel")
	this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_Transform_MainPanel, 0, GetPageNameString(1), _CurrentResourceTable)
    this.m_FullPageMenuLeft = LeftGroupTab.New(this.m_Transform_MainPanel, 2, _IconTable, function(iIndex)
		ExploreDairy_Controller.OnPageButtonClick(iIndex)
	end)
	--[[this.m_Btn_Back = this.m_ViewRef.m_Dic_Trans:Get("&Button_Back")
	Button.AddListener(this.m_Btn_Back, EventTriggerType.PointerClick, function()
		UIMgr.CloseToPreviousPage(ExploreDairy_Controller)
	end)]]

    --- 第一頁 任務
    this.m_PageGameObject[1] = this.m_ViewRef.m_Dic_Trans:Get("&MissionBook_View").gameObject
	--- 第二頁 成就
    this.m_PageGameObject[2] = this.m_ViewRef.m_Dic_Trans:Get("&Achievement_View").gameObject
	--- 第三頁 圖鑑
    this.m_PageGameObject[3] = this.m_ViewRef.m_Dic_Trans:Get("&Collect_View").gameObject


	-- 看看哪個先開啟
	for i = 1, table.Count(this.m_PageGameObject) do

		-- 先全部關掉
		--this.m_PageGameObject[i]:SetActive(false)

		-- 初始化各分頁
		if ExploreDairy_Controller[i] ~= nil then
			ExploreDairy_Controller[i].Init(this)
		end
	end

	

	--- 進度條
	this.m_ProgressBar = this.m_ViewRef.m_Dic_Slider:Get("&Progress_Bar")
	this.m_ProgressBar.onValueChanged:AddListener(this.CheckReUseItemState)
	---進度條動態效果使用的圖片
	this.m_ProgressBar_Effect = this.m_ViewRef.m_Dic_Image:Get("&GObj_FillEffect")
	ExploreDairy_Controller.SetUpBarTweenParamter()
	
	this.m_ReuseItem = this.m_ViewRef.m_Dic_Trans:Get("&Point").gameObject

	this.m_StageNum = ExploreDairy_Model.GetRewardStageNumber()

	--[[this.m_Btn_TopBar_Close = this.m_ViewRef.m_Dic_Trans:Get("&Button_Close_Blue").gameObject
    
    Button.AddListener(this.m_Btn_TopBar_Close, EventTriggerType.PointerClick, function() 
       UIMgr.Close(ExploreDairy_Controller) 
    end)]]

	this.m_Points_Txt = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_Points")

	-- 獎勵頁面
	this.m_RewardsGroup = this.m_ViewRef.m_Dic_Trans:Get("&RewardsGroup")

	this.m_RewardContent = this.m_ViewRef.m_Dic_Trans:Get("&RewardContent")

	this.m_CloseReward = this.m_ViewRef.m_Dic_Trans:Get("&ClickCloseReward").gameObject
	Button.AddListener(this.m_CloseReward, EventTriggerType.PointerClick, function() 
		this.m_RewardsGroup.gameObject:SetActive(false)
	 end)

	-- 獎勵 Icon
	this.m_RewarsIcons = {}

	-- 點數Group
	this.m_PointGroup = this.m_ViewRef.m_Dic_Trans:Get("&PointGroup")
	this.m_RewardPoints = this.m_PointGroup:Find("PointsToPoints"):GetComponent("TMPro.TextMeshProUGUI")

	ExploreDairy_Controller.ReuseItemInit()
	ExploreDairy_Controller.UpdateProgressBar()

end

--- 初始化 重複使用道具
function ExploreDairy_Controller.ReuseItemInit()

	-- 取進度條長度
	local _ProgressBarWidht = ExploreDairy_Controller.GetProgressBarWidth()

	-- 取進度條位置
	local _ProgressBarPosition = ExploreDairy_Controller.GetProgressBarPosition()

	-- 重複使用道具 Rect
	local _ReuseItemRect = this.m_ReuseItem:GetComponent("RectTransform")

	-- 沒取到請離開
	if(_ProgressBarWidht == nil or _ProgressBarPosition == nil) then

		return

	end

	-- 設定每個點的位置
	for i = 1, this.m_StageNum do

		-- 開始產生物件
        this.m_ReuseItemsTable[i] = {}
		
        -- 產生的物件
        local _ItemObject = nil

		-- 要產生的位置
        local _CurrentPositionValue = _ProgressBarWidht * ((i - 1) / (this.m_StageNum - 1))

		-- 第一個 用本人
		if i == 1 then

			_ItemObject = _ReuseItemRect

		-- 其他 產生物件
		else

			_ItemObject = _ReuseItemRect:Instantiate(_ReuseItemRect)

		end

		_ItemObject.name = tostring(i)
        _ItemObject:SetParent(this.m_ProgressBar.transform:Find("PointArea").transform)
		_ItemObject.localPosition = Vector3(_CurrentPositionValue + _ItemObject.localPosition.x, 0, 0)

		-- 點圖
		local _Dot_Img = _ItemObject.transform:Find("Dot_Img"):GetComponent( typeof( Image ) )

		-- 亮點圖
		local _LightDot_Img = _ItemObject.transform:Find("Image_LightDot").gameObject

		-- 獎勵領取需要點數字
		local _ProgreePoint_Txt = _ItemObject.transform:Find("ProgressPoint_Txt"):GetComponent("TMPro.TextMeshProUGUI")

		-- 獎勵領取按鈕
		local _GetReward_Btn = Button.New(_ItemObject.transform:Find("GetReward_Btn"))

		-- 獎勵領取字串
		local _Reward_Text = _GetReward_Btn.transform:Find("Reward_Txt"):GetComponent("TMPro.TextMeshProUGUI")

		-- 獎勵領取圖
		local _Reward_Img = _GetReward_Btn.transform:Find("Reward_Img"):GetComponent("Image")

		-- 獎勵未領取提示
		local _Reward_Unclaim_Hint = _GetReward_Btn.transform:Find("Reward_Unclaim_Hint").gameObject

		-- 要的東西都裝起來
		this.m_ReuseItemsTable[i].m_GObj 				= _ItemObject
		this.m_ReuseItemsTable[i].m_Dot_Img 			= _Dot_Img
		this.m_ReuseItemsTable[i].m_LightDot_Img		= _LightDot_Img
		this.m_ReuseItemsTable[i].m_ProgreePoint_Txt 	= _ProgreePoint_Txt
		this.m_ReuseItemsTable[i].m_GetReward_Btn 		= _GetReward_Btn
		this.m_ReuseItemsTable[i].m_Reward_Text 		= _Reward_Text
		this.m_ReuseItemsTable[i].m_Reward_Img 			= _Reward_Img
		this.m_ReuseItemsTable[i].m_Reward_Unclaim_Hint = _Reward_Unclaim_Hint
		this.m_ReuseItemsTable[i].m_Data 				= {}

		Button.AddListener(_GetReward_Btn, EventTriggerType.PointerClick, function() 
			ExploreDairy_Controller.RewardColletBtnOnClick(i)
		end)

	end

	--- 更新一下
	ExploreDairy_Controller.UpdateNeedProgreePoint()

end

--- 按鈕點下
---@param number iIdx 那顆按鈕
function ExploreDairy_Controller.RewardColletBtnOnClick(iIdx)

	local _Id = 0 

	if(this.m_ReuseItemsTable[iIdx].m_Data ~= nil) then

		_Id = this.m_ReuseItemsTable[iIdx].m_Data.m_Idx

	end

	-- 送協定
	ExploreDairyMgr.CollectAllRewards()
	--SendProtocol_003._014(_Id)

end

--- 檢查那些按鈕狀態
---@param number iValue 跑條 0~1
function ExploreDairy_Controller.CheckReUseItemState(iValue)

	local _Step = 1 / (this.m_StageNum - 1)
	local _NowWhere = math.floor(iValue / _Step)
	local _RewardState = ExploreDairy_Model.GetNowPageStageState()
	
	for i = 1, this.m_StageNum do

		local _Continue = false

		if( not table.IsNullOrEmpty(this.m_ReuseItemsTable[i].m_Data)) then
			this.m_ReuseItemsTable[i].m_GetReward_Btn:SetEnable()
			_Continue = true
		end

		if (_NowWhere >= i - 1 and _Continue) then

			if(_RewardState[i] == true) then

				this.m_ReuseItemsTable[i].m_GetReward_Btn:SetDisable()
				this.m_ReuseItemsTable[i].m_GetReward_Btn.gameObject:GetComponent(typeof(Image)).color = GetPointBtnColor(BtnStatus.Collected)
				SpriteMgr.Load( CHECK_ICON, this.m_ReuseItemsTable[i].m_Reward_Img)

			end
			this.m_ReuseItemsTable[i].m_Dot_Img.color = COLOR_BLUE
			this.m_ReuseItemsTable[i].m_LightDot_Img:SetActive(true)
			this.m_ReuseItemsTable[i].m_Reward_Unclaim_Hint:SetActive(_RewardState[i] == false)

		elseif(_Continue) then
			this.m_ReuseItemsTable[i].m_Dot_Img.color = COLOR_GREY
			this.m_ReuseItemsTable[i].m_LightDot_Img:SetActive(false)
			this.m_ReuseItemsTable[i].m_Reward_Unclaim_Hint:SetActive(false)
			this.m_ReuseItemsTable[i].m_GetReward_Btn:SetDisable()

		end

	end

end

local function f_calculatePercentage(iCurrent, iStart, iEnd)
    local _percentage = (iCurrent - iStart) / (iEnd - iStart)
    return _percentage
end

local function f_calculatePercentageInRange(iStart, iEnd, iPercentage)
    -- 計算範圍差值
    local _range = iEnd - iStart
    -- 計算百分比值
    local _calculatedValue = _range * iPercentage
    -- 將計算結果加回起始點
    local _finalValue = iStart + _calculatedValue
    return _finalValue
end

--- 更新進度條
function ExploreDairy_Controller.UpdateProgressBar(iValue)

	local _StartPoints = 0 -- 起始點數
	local _NowPoints = ExploreDairy_Model.GetExplorePoints() -- 目前點數
	local _NextPoints = 0 -- 下一個獎勵的點數需求
	local _Percentage = 0 -- 目前點數在目前獎勵到下一個獎勵達成率
	local _Stage = ExploreDairy_Model.OnStageNum() -- 獎品在哪個階段

	-- 更新顯示點數
	this.m_Points_Txt.text = _NowPoints

	-- 如果還在第一階段
	if(_Stage ~= 1) then

		_StartPoints = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(((_Stage - 1) * (ExploreDairy_Model.GetRewardStageNumber() - 1))).m_RewardPoints

	else

		_StartPoints = 0

	end

	-- 正在哪個獎勵點上
	local _NowOnReward = ExploreDairy_Model.GetNowOnReward() - ((_Stage - 1) * (ExploreDairy_Model.GetRewardStageNumber() - 1 ) + 1)
	if(_Stage == 1) then

		_NowOnReward = _NowOnReward + 1

	end
	
	local _GoPoints = 0 -- 開始的獎勵點

	-- 看一下目前在的獎勵點是不是已經是最大了 和 初始點如果是 0
	if(_NowOnReward ~= ExploreDairy_Model.GetRewardStageNumber()) then

		local _Index = ExploreDairy_Model.GetNowOnReward()

		if(_Stage ~= 1) then
			_Index = _Index - 1
		end

		if(ExploreDairy_Model.GetNowOnReward() ~= 0) then
			_GoPoints = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(_Index).m_RewardPoints
		end

		-- 要小於最大數量
		if(_Index + 1 <= ExplorePoint_Prize.GetCount()) then
			_NextPoints = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(_Index + 1).m_RewardPoints
		end

	else

		_NextPoints = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(ExploreDairy_Model.GetNowOnReward() + 1).m_RewardPoints

	end

	-- 取一下兩個小點的達成率
	_Percentage = f_calculatePercentage(_NowPoints, _GoPoints, _NextPoints)

	-- 換算一下輸出到 value
	local _Result = f_calculatePercentageInRange((_NowOnReward) / (this.m_StageNum - 1), (_NowOnReward + 1) / (this.m_StageNum - 1), _Percentage)

	this.m_ProgressBar.value = _Result

	ExploreDairy_Controller.UpdateNeedProgreePoint()
	ExploreDairy_Controller.CheckReUseItemState(_Result)
	ExploreDairy_Controller.SetUpBarTweenParamter()

	return true
end

--- 取進度調的長度
function ExploreDairy_Controller.GetProgressBarWidth()

	if(this.m_ProgressBar) then
		return  this.m_ProgressBar.transform.rect.width 
	end

	return nil

end

--- 取進度調的位置
function ExploreDairy_Controller.GetProgressBarPosition()

	if(this.m_ProgressBar) then
		return  this.m_ProgressBar.transform.localPosition
	end

	return nil

end

---Update
function ExploreDairy_Controller.Update()

	if this.m_IsPlayingBarEffect == false then
		ExploreDairy_Controller.PlayBarEffect()
	end
end

--- 開啟介面
---@param number iParams iParams[1]開哪個頁面
---@param table iParams iParams[2]任務資料
function ExploreDairy_Controller.Open(iParams)

	if this.m_Group_Resources ~= nil then
		this.m_Group_Resources.m_Resources:OnUpdate()
	end

	this.m_CurrentPage = EExploreDairySubIndex.Mission

	if not table.IsNullOrEmpty(iParams) then

		if(iParams[1] ~= nil and iParams[1] ~= 0) then
			this.m_CurrentPage = iParams[1]
		end

	end

	this.m_OpenPageParam = iParams[2]

	--ExploreDairy_Controller.OpenPage(this.m_CurrentPage, this.m_OpenPageParam)

	MissionBook_Controller.SetMissionUIDefaultPage(EMissionUIDefaultPage.UnClaim)

	if not table.IsNullOrEmpty(iParams) then

		if iParams[2] ~= 0 then
			if this.m_CurrentPage == EExploreDairySubIndex.Mission then
				MissionBook_Controller.OpenMissionByData(iParams[2],iParams[3])
			end
		end
		
	end

	-- 註冊更新
	PlayerData_Flags[EFlags.StaticNum].SetNotify(ExploreDairy_Model.GetExplorePointsFlag() ,ExploreDairy_Controller.UpdateProgressBar)

	--- 登記觀察者 貨幣變化
	GStateObserverManager.Register(EStateObserver.UpdateDiamond, ExploreDairy_Controller)
	GStateObserverManager.Register(EStateObserver.UpdateCoin, ExploreDairy_Controller)
	GStateObserverManager.Register(EStateObserver.UpdateHEMCoin, ExploreDairy_Controller)

	return true

end

function ExploreDairy_Controller.Close()

	PlayerData_Flags[EFlags.StaticNum].RemoveNotify(ExploreDairy_Model.GetExplorePointsFlag() ,ExploreDairy_Controller.UpdateProgressBar)
	local _NowChapter = MissionBook_Controller.GetNowClickChapter()
	if(MissionBook_Controller.m_ScrollView_ChapterList[_NowChapter].m_Unit.m_IsOpen) then
		MissionBook_Controller.ChapterClick(_NowChapter)
	end
	MissionBook_Controller.Close()
	--- 登記觀察者 貨幣變化
	GStateObserverManager.UnRegister(EStateObserver.UpdateDiamond, ExploreDairy_Controller)
	GStateObserverManager.UnRegister(EStateObserver.UpdateCoin, ExploreDairy_Controller)
	GStateObserverManager.UnRegister(EStateObserver.UpdateHEMCoin, ExploreDairy_Controller)

end

function ExploreDairy_Controller.OpenPage(iIndex, iParam)
	--開啟各母分頁
	if ExploreDairy_Controller[iIndex] ~= nil then
		ExploreDairy_Controller[iIndex].Open(iParam)
	end
	this.m_CurrentPage = iIndex
end

function ExploreDairy_Controller.ToDoAfterOpenUISucceed()
	this.m_FullPageMenuLeft:OnPointerClickByIndex(this.m_CurrentPage)
end

function ExploreDairy_Controller.OnPageButtonClick(iPageIndex)
	if iPageIndex ~= 1 then
		local _NowChapter = MissionBook_Controller.GetNowClickChapter()
		MissionBook_Controller.ChapterClick(_NowChapter)
	end

	--開啟相對應母分頁物件
	for i = 1, table.Count(this.m_PageGameObject) do
		this.m_PageGameObject[i]:SetActive(i == iPageIndex)
	end

	ExploreDairy_Controller.OpenPage(iPageIndex, this.m_OpenPageParam)	
	this.m_OpenPageParam = nil

	--設定 Title 文字
	this.m_FullPageTitleBar:SetTitleText(GetPageNameString(this.m_CurrentPage))

end

--- 更新所需獎勵領取點數
function ExploreDairy_Controller.UpdateNeedProgreePoint()

	-- 頁面計算上還需要 -1 才會準
	local _Page = ExploreDairy_Model.OnStageNum() - 1

	for i = 1, ExploreDairy_Model.GetRewardStageNumber() do

		local _PrizeData = {}

		-- 在第一個點的時候需要特殊取資料 他需要取到上一頁的最後一個點數
		-- GetRewardStageNumber() 都需要 -1 才能做計算
		if(i == 1) then

			_PrizeData = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(ExploreDairy_Model.GetRewardStageNumber() - 1 + ((_Page - 1) * (ExploreDairy_Model.GetRewardStageNumber() - 1)))

		else
			local _Stage = ExploreDairy_Model.OnStageNum() -- 獎品在哪個階段

			-- 如果還在第一階段 第一階段第一個點不需要顯示還要設 0
			if(_Stage ~= 1) then

				_PrizeData = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(i - 1 + ((_Page) * (ExploreDairy_Model.GetRewardStageNumber() - 1)))

			else

				_PrizeData = ExplorePoint_Prize.GetExplorePoint_PrizeByIdx(i + 1 + ((_Page) * ExploreDairy_Model.GetRewardStageNumber() - 1) - 1)

			end

		end

		if(_PrizeData ~= nil) then

			if(_PrizeData.m_RewardKind == EExploreReward.Item) then

				-- 道具資料
				local _ItemData = ItemData.GetItemDataByIdx(_PrizeData.m_ItemIdx)

				-- 道具圖片
				SpriteMgr.Load( _ItemData:GetItemTextureName(), this.m_ReuseItemsTable[i].m_Reward_Img)

			else

				-- 鑽石圖片
				SpriteMgr.Load( "Icon111002", this.m_ReuseItemsTable[i].m_Reward_Img)

			end

			-- 帶入資料
			this.m_ReuseItemsTable[i].m_Data = _PrizeData

			-- 獎品內容數量
			this.m_ReuseItemsTable[i].m_Reward_Text.text = _PrizeData.m_Amount

			-- 需求點數
			this.m_ReuseItemsTable[i].m_ProgreePoint_Txt.text = _PrizeData.m_RewardPoints

		else

			-- 清空資料
			this.m_ReuseItemsTable[i].m_Data = {}

			-- 點數設 0
			this.m_ReuseItemsTable[i].m_ProgreePoint_Txt.text = 0

			-- 按鈕關閉
			this.m_ReuseItemsTable[i].m_GetReward_Btn.gameObject:SetActive(false)

		end

	end

end

--- 顯示獎勵結果
---@param table iData 物品編號 + 數量 可以給複數
---iData.ID
---iData.Number
---@param Str iPoint 如果有加積分才填沒有就不用
function ExploreDairy_Controller.ShowReward(iData, iPoint)

	---沒積分 套用共用獎勵視窗 - 多道具版本
	if iPoint == nil  then
		---生成獎勵視窗需要data
		local _RewardData = {}
		---20620002 積分獎勵使用的字串
		_RewardData.m_RewardTitle = 20600002
		_RewardData.m_RewardSubDatas = {}
 
		_RewardData.m_RewardSubDatas[1] = {}
		for i = 1, table.Count(iData) do
			local _Data = {}
			_Data.m_ItemID =  iData[i].ID == 0 and _SpaceCoint or iData[i].ID
			_Data.m_ItemAmount = iData[i].Number
			local _ItemData = ItemData.GetItemDataByIdx(_Data.m_ItemID)
			_Data.m_Rarity = _ItemData.m_Rarity
			
			table.insert(_RewardData.m_RewardSubDatas[1],_Data)
		end
		 
		CommonRewardMgr.AddNewReward(CommonRewardType.MultiItem, _RewardData )

	---有積分 套用共用獎勵視窗 - 多道具+文字 版本
	else
		---生成獎勵視窗需要data
		local _RewardData = {}
		---20601502 章節完成 字串ID
		_RewardData.m_RewardTitle = 20601502
		_RewardData.m_RewardSubDatas = {}
		_RewardData.m_RewardSubDatas_Text = {}
 
		_RewardData.m_RewardSubDatas[1] = {}
		for i = 1, table.Count(iData) do
			local _Data = {}
			_Data.m_ItemID =  iData[i].ID == 0 and _SpaceCoint or iData[i].ID
			_Data.m_ItemAmount = iData[i].Number
			local _ItemData = ItemData.GetItemDataByIdx(_Data.m_ItemID)
			_Data.m_Rarity = _ItemData.m_Rarity
			
			table.insert(_RewardData.m_RewardSubDatas[1],_Data)
		end

		local _subData = {}
			---文字說明部分只會有一項 積分獎勵
			for j = 1, 1 do

				local _RewardSubData = CommonRewardMgr.GetRewardSubDataType(CommonRewardSubDataType.TextType)
				_RewardSubData.m_Title =  TextData.Get(20113305)
				_RewardSubData.m_Number = ExploreDairy_Model.GetExplorePoints() - iPoint
				_RewardSubData.m_AddtoNumber = ExploreDairy_Model.GetExplorePoints() 
				table.insert(_subData, _RewardSubData)
			end
			table.insert(_RewardData.m_RewardSubDatas_Text,_subData)
		 
		CommonRewardMgr.AddNewReward(CommonRewardType.ItemAndText, _RewardData )

	end

end

--- 背包物品有更新會呼叫
function ExploreDairy_Controller:OnStateChanged(iState)

	if iState == EStateObserver.UpdateDiamond or
    iState == EStateObserver.UpdateCoin or
    iState == EStateObserver.UpdateHEMCoin then
        -- 更新錢包
        this.m_Group_Resources.m_Resources:OnUpdate()
    end

end

---設定積分bar  tween相關參數 並中斷當前tween
function ExploreDairy_Controller.SetUpBarTweenParamter()

	LeanTween.cancel(this.m_Bar_TweenEffectID)
	this.m_IsPlayingBarEffect = false

	---設定初始 結束 位置
	--- 0.7 是根據UI 排版 剛好可以讓圖片被隱藏起來的距離
	m_Start_BarEffectObj = Vector3 (this.m_ProgressBar.fillRect.rect.width * -0.7,0,0) 
	m_End_BarEffectObj = Vector3 (this.m_ProgressBar.fillRect.rect.width * 0.7,0,0) 
	m_EffectPeriod = (m_End_BarEffectObj-m_Start_BarEffectObj).x /m_Speed_EffectObj
	
end

local function OnTweenBarEffect(iValue)
	this.m_ProgressBar_Effect.gameObject.transform.localPosition = m_Start_BarEffectObj+ (m_End_BarEffectObj-m_Start_BarEffectObj)*iValue
end

local function OnTweenEffectComplete()
	this.m_IsPlayingBarEffect = false
end

---執行tween 演出bar條特效
function ExploreDairy_Controller.PlayBarEffect()
	this.m_IsPlayingBarEffect = true
	this.m_Bar_TweenEffectID = LeanTween.value( this.m_ProgressBar_Effect.gameObject, System.Action_float(OnTweenBarEffect), 0, 1, m_EffectPeriod)
			:setOnComplete(System.Action(OnTweenEffectComplete)).id
end
