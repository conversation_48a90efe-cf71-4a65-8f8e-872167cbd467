---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---場景地塊控制
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.5.11
---@class FloorBlockController
FloorBlockController = {}
--FloorBlockController.__index = FloorBlockController

---@class EColliderCondition 碰撞條件
EColliderCondition = {
	TriggerEnter = 1,
	TriggerStay = 2,
	TriggerExit = 3,
	CollisionEnter = 4,
	CollisionStay = 5,
	CollisionExit = 6,
	TriggerEnter2D = 7,
	TriggerStay2D = 8,
	TriggerExit2D = 9,
	CollisionEnter2D = 10,
	CollisionStay2D = 11,
	CollisionExit2D = 12,
}

---@type Transform 地板地塊父物件
local m_FloorBlockParent

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 地塊物件池
local m_GObjPool_BlockObj = {}

---@type table<number,FloorBlockController> 地塊物件List
local m_Dic_BlockObj = {}

function FloorBlockController.Init()
	m_FloorBlockParent = GameObject.New("FloorBlockParent").transform
	m_GObjPool_BlockObj = Extension.GetGameObjPool(
		20,
		0,
		FloorBlockController.BlockObj_Reset,
		FloorBlockController.BlockObj_Init
	)
end

--- 傳點效果 ID
FloorBlockController.EventMapTransferEffect =
{
	[4] = {m_EffectId = 60023, m_Scale = 1},
	[5] = {m_EffectId = 60023, m_Scale = 0.6},-- 60023 預設大小是原場景中特效大小的0.6倍
	[6] = {m_EffectId = 60022, m_Scale = 1},
	[7] = {m_EffectId = 20052, m_Scale = 0.6},-- Kind 7 改用特效20052 rm#127332
}

---@param iEventBlockData EventBlockData
---@param iIdx number
function FloorBlockController:New(iEventBlockData, iIdx)
	local _myself = {}
	setmetatable(_myself, {__index = self})

	_myself.m_EvBlockData = iEventBlockData
	_myself.gameObject = m_GObjPool_BlockObj:Get()
	---@type EventMapTransfer 地板傳送
	_myself.m_EventMapTransfer = iEventBlockData.m_EventMapTransferAy[iIdx]
	_myself.gameObject.name = iIdx .. " " .. tostring(_myself.m_EventMapTransfer.m_EventID)
	_myself.gameObject:SetActive(true)

	_myself.transform = _myself.gameObject:GetComponent("Transform")

	local _SceneHeight = SceneMgr.GetSceneHeight()

	local _Origin = Vector3(_myself.m_EventMapTransfer.m_X1 / 100, _SceneHeight, _myself.m_EventMapTransfer.m_Y1 / 100)
	local _Direction = Vector3(0, -1, 0)
	local _MaxDistance = MaxFloorRayCastLength
	local _LayerMask = 2 ^ Layer.SceneGround -- Bit shift the index of the layer (24) to get a bit mask
	local _Flag, _Hit = Physics.Raycast(_Origin, _Direction, nil, _MaxDistance, _LayerMask)
	local _FloorY = _Flag and _Hit.point.y or _SceneHeight
	_myself.transform.localPosition = Vector3(_Origin.x, _FloorY, _Origin.z)

	_myself.m_BoxCollider = Extension.AddMissingComponent(_myself.gameObject, typeof(BoxCollider))
	_myself.m_BoxCollider.size = Vector3(_myself.m_EventMapTransfer.m_X2, 1, _myself.m_EventMapTransfer.m_Y2)
	_myself.m_BoxCollider.isTrigger = true

	local _ColliderDectect = Extension.AddMissingComponent(_myself.gameObject, typeof(ColliderDetect))
	_ColliderDectect.SetCondition(
		_ColliderDectect,
		EColliderCondition.TriggerEnter,
		FloorBlockController.OnTriggerEnter
	)

	_ColliderDectect.SetCondition(
		_ColliderDectect,
		EColliderCondition.TriggerExit,
		FloorBlockController.OnTriggerExit
	)

	-- 2025.01.10 Modify by 蛋糕 #116530
	--            4.一般場景傳送點 5.內景傳送點 7.小型觸發點 改用效果編號 60023
	--            6.副本傳送點 改用效果編號 60022
	if
		_myself.m_EventMapTransfer.m_Looks == 4
		or _myself.m_EventMapTransfer.m_Looks == 5
		or _myself.m_EventMapTransfer.m_Looks == 6
		or _myself.m_EventMapTransfer.m_Looks == 7
	then
		if _myself.m_GObj_Effect ~= nil then
			_myself.m_GObj_Effect:Destroy()
		end

		---效果ID
		local _EffectID = FloorBlockController.EventMapTransferEffect[_myself.m_EventMapTransfer.m_Looks].m_EffectId
		--- 效果縮放比例
		local _Scale = FloorBlockController.EventMapTransferEffect[_myself.m_EventMapTransfer.m_Looks].m_Scale
		-- if _myself.m_EventMapTransfer.m_Looks == 4 then
		-- 	_EffectID = 60023
		-- elseif _myself.m_EventMapTransfer.m_Looks == 5 then
		-- 	_EffectID = 60023
		-- 	-- 60023 預設大小是原場景中特效大小的3倍
		-- 	_Scale = 0.33
		-- elseif _myself.m_EventMapTransfer.m_Looks == 6 then
		-- 	_EffectID = 60022
		-- elseif _myself.m_EventMapTransfer.m_Looks == 7 then
		-- 	_EffectID = 60023
		-- 	-- 60023 預設大小是原場景中特效大小的3倍
		-- 	_Scale = 0.33
		-- end

		-- 播放特效
		EffectMgr.PlayEffectWithParent( EEffectType.Floor, _EffectID,
			_myself.transform, true, function(iHashCode)
					_myself:SetHashCode(iHashCode)
				end,nil,nil ,nil,_Scale)

		-- 添加 MapIcon 顯示
		MapMgr.AddTeleportPoint(_myself.transform.localPosition)
	else
		if _myself.m_GObj_Effect ~= nil then
			_myself.m_GObj_Effect:Destroy()
		end
	end

	-- if _myself._EffectIdx ~= 0 then
	-- 	EffectMgr.PlayEffectWithPos(_myself._EffectIdx)
	-- end

	m_Dic_BlockObj[iIdx] = _myself
end

function FloorBlockController:SetHashCode(iHashCode)
	--EffectMgr.ReturnEffect
	self._EffectIdx = iHashCode
end

function FloorBlockController.ResetAll()
	for index, value in ipairs(m_Dic_BlockObj) do
		-- 地塊有含特效
		if value._EffectIdx then
			-- 還特效
			EffectMgr.ReturnEffect(value._EffectIdx)
		end

		m_GObjPool_BlockObj:Store(value.gameObject)
	end

	m_Dic_BlockObj = {}
end

function FloorBlockController.BlockObj_Init(iGObj)
	if iGObj ~= nil then
		iGObj:SetActive(false)

		local _Trans = iGObj:GetComponent("Transform")
		_Trans:SetParent(m_FloorBlockParent.transform)
		_Trans.localPosition = Vector3.zero
		_Trans.localScale = Vector3.one

		Extension.AddMissingComponent(iGObj, typeof(BoxCollider))
		local _ColliderDectect = Extension.AddMissingComponent(iGObj, typeof(ColliderDetect))
		_ColliderDectect.ClearCondition(_ColliderDectect, EColliderCondition.TriggerEnter)
	end
end

function FloorBlockController.BlockObj_Reset(iGObj)
	if iGObj ~= nil then
		iGObj:SetActive(false)

		local _Table_IDs = {}
		for _Str in string.gmatch(iGObj.name, "[^%s]+") do
			table.insert(_Table_IDs, _Str)
		end
		local _BlockId = tonumber(_Table_IDs[1])
		iGObj.name = "StoredFloorBlock"

		local _ColliderDectect = Extension.AddMissingComponent(iGObj, typeof(ColliderDetect))
		_ColliderDectect.ClearCondition(_ColliderDectect, EColliderCondition.TriggerEnter)

		local _FBC = m_Dic_BlockObj[_BlockId]
		if _FBC ~= nil and _FBC.m_GObj_Effect ~= nil then
			EffectMgr.SetEffectToPool(tonumber(_FBC.m_GObj_Effect.name), _FBC.m_GObj_Effect)
		end
	end
end

---顯示或隱藏資訊
function FloorBlockController.BlockObj_Seal(iPacket)
	local _Count = iPacket:ReadByte()
	local _EventID = iPacket:ReadUInt32()
	local _IsShow = iPacket:ReadByte()

	local i = 0
	for k,v in pairs(m_Dic_BlockObj) do
		if string.match(v.gameObject.name, tostring(_EventID)) then
			v.gameObject:SetActive(_IsShow == 1)
			i = i + 1
		end
		if i >= _Count then
			break
		end
	end
end

---進入地塊時 檢查當前的地塊 事件是否在觸發中 若否則觸發事件
---@param iOther UnityEngine.Collider
function FloorBlockController.OnTriggerEnter(iSelf, iOther)
	if not iOther.CompareTag(iOther, "Player") then
		return
	end

	local _Table_IDs = {}
	for _Str in string.gmatch(iSelf.name, "[^%s]+") do
		table.insert(_Table_IDs, _Str)
	end
	local _BlockId = tonumber(_Table_IDs[1])
	local _EventId = tonumber(_Table_IDs[2])
	--D.Log("BlockId: " .. _BlockId .. " EventId: " .. _EventId)

	---目前已經觸發的事件地塊ID
	if EventMgr.m_BlockID_Cur == _BlockId and EventMgr.m_EventID_Cur == _EventId then
		return
	end

	EventMgr.SetEventID(_EventId)
	EventMgr.SetBlockID(_BlockId)
	EventMgr.StartEvent(_EventId, 0, _BlockId, 0, EventTriType.Collision)
end

---離開地塊時 將當前的地塊 判斷目前事件ID 來決定是否將當前觸發的事件ID 歸零
---@param iSelf CharacterController 從Collider出去的可觸發事件的物件
---@param iOther UnityEngine.Collider
function FloorBlockController.OnTriggerExit(iSelf, iOther)
	if not iOther.CompareTag(iOther, "Player") then
		return
	end

	local _Table_IDs = {}
	for _Str in string.gmatch(iSelf.name, "[^%s]+") do
		table.insert(_Table_IDs, _Str)
	end
	local _BlockId = tonumber(_Table_IDs[1])
	local _EventId = tonumber(_Table_IDs[2])

	---如果地塊紀錄的事件 和EventMgr 目前記錄的事件相同 則清空事件紀錄
	if EventMgr.m_BlockID_Cur == _BlockId and _EventId == EventMgr.m_EventID_Cur   then
		EventMgr.ClearNowEventID()
	end

end

--region Debug
function FloorBlockController.Update()
	-- if Input.GetKeyDown(KeyCode.F12) and Extension.IsEditor then
	-- 	FloorBlockController.ResetAll()
	-- elseif Input.GetKeyDown(KeyCode.F11) and Extension.IsEditor then
	-- 	local _BlockData = EventBlockData:GetEventBlockDataByIdx(SceneMgr.GetSceneID())
	-- 	for i = 1, _BlockData.m_BlockCount do
	-- 		FloorBlockController:New(_BlockData, i)
	-- 	end
	-- end
end
--endregion
function FloorBlockController.OnUnrequire()
	if m_FloorBlockParent.gameObject~= nil then
		m_FloorBlockParent.gameObject:Destroy()
	end
	return true
end