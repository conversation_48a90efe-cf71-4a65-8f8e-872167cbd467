﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---@class InnerBookData
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.5.22
InnerBookData = {}
InnerBookData.__index = InnerBookData

local this = InnerBookData

--region 串檔 coroutine 專區

--- 需要等待的串檔
this.m_NeedWaitData = { }

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

--- 串檔總數
this.m_DataCount = 0

--- 新串了多少串檔
this.m_NewDataCount = 0

---Stream讀了多少次
this.m_StreamReadByteTimes = 0

--endregion 串檔 coroutine 專區
this.m_ASSET_NAME = "InnerBook_C"
local m_Dic = {}

function InnerBookData:New(iReader)
    ---@type InnerBookData
    local _Data = {}
    setmetatable ( _Data, InnerBookData )

    --- 流水號
    ---@type ushort
    _Data.m_Num = iReader:ReadUInt16()
    --- 心法編號
    ---@type byte
    _Data.m_MethodID = iReader:ReadByte()
    --- 心法名稱字串
    ---@type uint
    _Data.m_MethodString = iReader:ReadUInt32()
    --- 篇章名稱字串
    ---@type uint
    _Data.m_ChapterString = iReader:ReadUInt32()
    --- icon編號
    ---@type uint
    _Data.m_IconID = iReader:ReadUInt32()
    --- 心法階段
    ---@type byte
    _Data.m_Step = iReader:ReadByte()
    --- 升級道具需求數
    ---@type byte
    _Data.m_NeedCount = iReader:ReadByte()
    --- 進階道具物品編號
    ---@type uint
    _Data.m_ItemID = iReader:ReadUInt32()
    ---心法屬性
    ---@type Attribute
    _Data.m_Attribute = {}
    for i = 1, 2 do
        _Data.m_Attribute[i] = {}
        ---@type byte
        _Data.m_Attribute[i].Value = iReader:ReadByte()
    end
    --- 內功武功編號
    ---@type ushort
    _Data.m_WugongID = iReader:ReadUInt16()
    --- 靈氣獲取每運行5分鐘
    ---@type byte
    _Data.m_SpiritPerFiveMin = iReader:ReadByte()
    --- 心法被動效果屬性修正值
    ---@type ushort
    _Data.m_FixValue = iReader:ReadUInt16()
    --- 屬性修正值公式ID
    ---@type ushort
    _Data.m_FormulaID = iReader:ReadUInt16()
    --- 靈氣解放開放
    ---@type ushort
    _Data.m_Unlock = iReader:ReadUInt16()
    --- 靈氣解放屬性資料
    ---@type SpiritData
    _Data.m_SpiritData = {}
    for i = 1, 2 do
        _Data.m_SpiritData[i] = {}
        ---靈氣解放屬性修正值
        ---@type word
        _Data.m_SpiritData[i].m_AttrFix = iReader:ReadUInt16()
        ---靈氣解放屬性數值
        ---@type word
        _Data.m_SpiritData[i].m_AttrValue = iReader:ReadUInt16()
    end
    --- 靈氣解放消耗每小時
    ---@type ushort
    _Data.m_HourCost = iReader:ReadUInt16()

    return _Data.m_MethodID, _Data
end

---初始化
function InnerBookData.Init()
    return DataReader.LoadFile(this.m_ASSET_NAME, InnerBookData.OnLoadData)
end

---讀檔
function InnerBookData.OnLoadData(iFile)
    local _Reader = DataReader.New(iFile)
    this.m_DataCount = _Reader:ReadUInt32()
    DataMgr.NewData(this, _Reader, nil, InnerBookData.FunctionAddSingleDataByIndex, InnerBookData.DoAfterDictionaryInitialized)
end

---外掛串表讀檔
function InnerBookData.OnLoadExTableData(iIdx , iData)
    return iData
end

---取得InnerBookData
---@param m_Num InnerBookDatam_Num
---@return InnerBookData
function InnerBookData.GetInnerBookDataByIdx(iIdx)
    if iIdx > 0 and m_Dic[iIdx] ~= nil then
        return m_Dic[iIdx]
    else
        D.Log("Cant Find InnerBookData m_Num: " .. iIdx)
        return nil
    end
end

---============分隔線以上為自動填入區段，功能請勿於此線以上撰寫================
---======================== I am Spliter! ===============================

function InnerBookData.FunctionAddSingleDataByIndex(iCountIndex, iDataIndex, iData)
    if m_Dic[iData.m_MethodID] == nil then
        m_Dic[iData.m_MethodID] = {}
    end
    table.insert(m_Dic[iData.m_MethodID], iData)
end

function InnerBookData.GetDataCount()
    return table.Count(m_Dic)
end

--- 初始化後處理
function InnerBookData.DoAfterDictionaryInitialized()
    local _TempData = {}
    for k, v in pairs(m_Dic) do
        _TempData[k] = {}
        for _k, _v in ipairs(v) do
            _TempData[k][_k - 1] = _v
        end
    end
    m_Dic = _TempData
end