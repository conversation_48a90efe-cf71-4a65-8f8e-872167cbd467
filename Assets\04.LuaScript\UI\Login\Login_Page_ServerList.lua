---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---登入頁面 伺服器選單
---<AUTHOR>
---@version 1.0
---@since [HEM2.0]
---date 2023.06.17
Login_Page_ServerList = {}
local this = Login_Page_ServerList

---現在 ServerList 選擇的伺服器
this.m_NowSelectServerIndex = 1
---現在 ServerList 選擇的分流
this.m_NowSelectDivertIndex = 1

this.m_NowGroupID = 1

---擇現在的伺服器
local function SelectDefultServer()
    GroupButton.OnPointerClickByIndex(this.m_GObjectServerScrollView,Login_Model.m_ServerIdx)
	GroupButton.OnPointerClickByIndex(this.m_GObjectServerDivertScrollView,Login_Model.m_DivertIdx)
	this.m_NowSelectServerIndex = Login_Model.m_ServerIdx
	this.m_NowSelectDivertIndex = Login_Model.m_DivertIdx
	this.m_NowGroupID = Login_Model.m_GroupId
end

---點擊更換Server OK
local function OnClick_ChangeServerConfirm()
	this.SetSelectServer()
	Login_Model.SetServerData()
	this.m_Controller.m_Text_ServerName.text = Login_Model.GetServerData().m_ServerName
	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_LastSelectServerID",Login_Model.m_ServerData.m_ID)
	this.m_Controller.SetPage(Login_Model.m_IsQuickLogin and this.m_Controller.m_PageType.QuickLogin or this.m_Controller.m_PageType.ChangeAccount)
	GroupButton.ResetAllButtonsState(this.m_GObjectServerScrollView)
	GroupButton.ResetAllButtonsState(this.m_GObjectServerDivertScrollView)
end

---初始化伺服器選單
function Login_Page_ServerList.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

	--region Server List	
	this.m_Page_ServerList = this.m_ViewRef.m_Dic_Trans:Get("&Page_ServerList").gameObject

	this.GroupServerName = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_ServerName").gameObject
	this.GroupServerNameGroupBtnCtrl = this.GroupServerName:GetComponent("UIGroupButtonCtrl")
	this.GroupDivert = this.m_ViewRef.m_Dic_Trans:Get("&Divert_Content").gameObject
	this.GroupDivertroupBtnCtrl = this.GroupDivert:GetComponent("UIGroupButtonCtrl")

	this.m_Evt_ChangeServerList_Confirm = this.m_ViewRef.m_Dic_Trans:Get("&Button_ChangeServer_Confirm")
	Button.AddListener(this.m_Evt_ChangeServerList_Confirm, EventTriggerType.PointerClick,function()
		this.GroupDivert:SetActive(false)
		OnClick_ChangeServerConfirm()
		end)
	--endregion

	--分類ServerList
	Login_Model.InitServerList()
	this.m_NowGroupID = Login_Model.m_GroupId
	
	this.m_GObjectServerScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_ServerName")
	this.m_ServerReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Evt_Server").gameObject
	this.m_ServerScrollView = ScrollView.Init(this.m_GObjectServerScrollView, false, this.m_ServerReuseItme,
		Login_Page_ServerList.GetServerListCurrentCount, Login_Page_ServerList.AfterReuseItemInit,Login_Page_ServerList.AfterReuseItemIndexUpdate, true)

	this.m_GObjectServerDivertScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Divert")
	this.m_ServerDivertReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Evt_Divert").gameObject
	this.m_ServerDivertScrollView = ScrollView.Init(this.m_GObjectServerDivertScrollView, false, this.m_ServerDivertReuseItme,
	Login_Page_ServerList.GetServerDivertListMaxCount, Login_Page_ServerList.AfterServerDivertReuseItemInit,Login_Page_ServerList.AfterServerDivertReuseItemIndexUpdate, true)
end

---設定所選擇的 Server
function Login_Page_ServerList.SetSelectServer()
	Login_Model.m_ServerIdx = this.m_NowSelectServerIndex
	Login_Model.m_DivertIdx = this.m_NowSelectDivertIndex
	Login_Model.m_GroupId = this.m_NowGroupID
end

--region ServerName ScrollView

function Login_Page_ServerList.GetServerListCurrentCount()
	local _ServerGroupCount = 0
	if table.Count(Login_Model.m_ServerList) > 0 then
		_ServerGroupCount = table.Count(Login_Model.m_ServerList)
	end
	return _ServerGroupCount
end

local function OnServerButtonClickDelegate(iItem)
	D.Log("onclick servername idx: " .. iItem.m_Index)
	if this.m_NowSelectServerIndex ~= iItem.m_Index then
		this.m_NowSelectServerIndex = iItem.m_Index
		this.m_NowGroupID = iItem.m_Group         
	end
	ScrollView.Update(this.m_ServerDivertScrollView)
	GroupButton.ResetAllSelect(this.m_GObjectServerDivertScrollView)
	Login_Page_ServerList.SetDivertActive(true)
	if this.m_NowSelectServerIndex == Login_Model.m_ServerIdx and this.m_NowSelectDivertIndex == Login_Model.m_DivertIdx then
		GroupButton.OnPointerClickByIndex(this.m_GObjectServerDivertScrollView, Login_Model.m_DivertIdx)
	end
end

function Login_Page_ServerList.AfterReuseItemInit(iItem, iIdx)
	if iItem ~= nil then
		iItem.m_Table_Button = Button.New(iItem.m_GObj)
		iItem.m_Table_Button:AddListener(EventTriggerType.PointerClick, OnServerButtonClickDelegate, iItem)
			iItem.m_TMPServerName = iItem.m_GObj.transform:Find("Text_Name"):GetComponent("TMPro.TextMeshProUGUI")
			iItem.m_GObj_Recommend = iItem.m_GObj.transform:Find("Text_Recommend").gameObject
			iItem.m_TMP_Time = iItem.m_GObj.transform:Find("Text_Time").gameObject:GetComponent("TMPro.TextMeshProUGUI")
	end
end

function Login_Page_ServerList.AfterReuseItemIndexUpdate(iItem, iIdx)
	if iItem ~= nil then
		local _GroupId, _ServerData = Login_Model.GetServerByIdx(iIdx)
		iItem.m_GObj:SetActive(_ServerData ~= nil)
		if _ServerData ~= nil then
			iItem.m_Group = _GroupId
			iItem.m_TMPServerName.text = table.First(_ServerData).m_ServerName
			iItem.m_TMP_Time.text = tostring(System.DateTime.Now)
			iItem.m_GObj_Recommend:SetActive(false)
		end
	end
end

function Login_Page_ServerList.SetDivertActive(iIsActive)
	this.GroupDivert:SetActive(iIsActive)
end

--endregion

--region ServerDivert ScrollView

function Login_Page_ServerList.GetServerDivertListMaxCount()
	local _ServerGroupCount = 0
	for key, value in pairs(Login_Model.m_ServerList) do
		if Login_Model.m_ServerList[key] ~= nil and table.Count(Login_Model.m_ServerList[key]) > _ServerGroupCount then
			_ServerGroupCount = table.Count(Login_Model.m_ServerList[key])
		end
	end
		
	return _ServerGroupCount
end

local function ServerDivertButtonOnClickDelegate(iItem)
	D.Log("onclick Divert idx: " .. iItem.m_Index)
	if this.m_NowSelectDivertIndex ~= iItem.m_Index then
		this.m_NowSelectDivertIndex = iItem.m_Index
	end
end

function Login_Page_ServerList.AfterServerDivertReuseItemInit(iItem, iIdx)
	if iItem ~= nil then
		iItem.m_Table_Button = Button.New(iItem.m_GObj)
		iItem.m_Table_Button:AddListener(EventTriggerType.PointerClick, ServerDivertButtonOnClickDelegate, iItem)
			iItem.m_TMPDivertName = iItem.m_GObj.transform:Find("Text_Name").gameObject:GetComponent("TMPro.TextMeshProUGUI")
			iItem.m_Text_Status = iItem.m_GObj.transform:Find("Text_Status").gameObject:GetComponent("TMPro.TextMeshProUGUI")
	end
end

function Login_Page_ServerList.AfterServerDivertReuseItemIndexUpdate(iItem, iIdx)
	if iItem ~= nil then
		local _DivertData = Login_Model.GetServerDivertByIdx(this.m_NowGroupID,iIdx)
		local _DivertMaxCount = this.GetServerDivertListMaxCount()
		local _IsActive = _DivertData ~= nil and iIdx <= _DivertMaxCount
		iItem.m_GObj:SetActive(_IsActive)
		if _DivertData ~= nil then
			iItem.m_TMPDivertName.text = _DivertData.m_ServerName
			iItem.m_Text_Status.text = ""
		end
	end
end

---開啟伺服器選單
function Login_Page_ServerList.Open()
    this.m_Page_ServerList:SetActive(true)
    SelectDefultServer()
	return true
end

---關閉伺服器選單
function Login_Page_ServerList.Close()
    this.m_Page_ServerList:SetActive(false)
	return true
end

--endregion
