//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2021 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------
//
//=====================================================================

using LuaInterface;
using Pathfinding;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;
using GameTools.Verify;
using GameTools.Utilities;
using System;
using GameTools.Log;
using UnityEngine.EventSystems;
using UnityEngine.Events;
using LuaFramework;

/// <summary>
/// 傳入 Lua, 然後讓 Lua 調用 C#方法
/// </summary>
public class toLuaToCSharpDelegate
{
    private Action m_Action;

    public toLuaToCSharpDelegate( Action iAction )
    {
        m_Action = iAction;
    }

    public void Invoke()
    {
        m_Action.Invoke();
    }
}

/// <summary>
/// 擴充函式, 包含Lua使用 與 C# 使用的擴充方式
/// 有需要從 Lua Helper 搬
/// <AUTHOR>
/// @version 1.0
/// @since [ProjectBase] 0.1
/// @date 2021.10.12
/// </summary>
public static class Extension
{
    public static void Quit()
    {
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }

    #region Platform相關
    public static bool IsApplePlatform
    {
        get
        {
#if UNITY_IOS
            return true;
#else
            return false;
#endif
        }
    }

    public static bool IsAndroidPlatform
    {
        get
        {
#if UNITY_ANDROID
            return true;
#else
            return false;
#endif
        }
    }

    public static bool IsStandalone
    {
        get
        {
#if UNITY_STANDALONE || UNITY_EDITOR
            return true;
#else
            return false;
#endif
        }
    }

    public static bool IsMacOS
    {
        get
        {
#if UNITY_EDITOR_OSX
            return true;
#else
            return false;
#endif
        }
    }

    public static bool IsEditor
    {
        get
        {
#if UNITY_EDITOR
            return true;
#else
            return false;
#endif
        }
    }

    public static int GetDeviceVersion()
    {
#if UNITY_ANDROID

        IntPtr _Class = AndroidJNI.FindClass("android/os/Build$VERSION");
        IntPtr _FieldID = AndroidJNI.GetStaticFieldID(_Class, "SDK_INT", "I");
        int _SdkLevel = AndroidJNI.GetStaticIntField(_Class, _FieldID);
        return _SdkLevel;

#elif UNITY_IOS
        string _SystemVersion = UnityEngine.iOS.Device.systemVersion;
        string[] _SeparatedVersion = _SystemVersion.Split('.');
        int _MajorVersion = int.Parse(_SeparatedVersion[0]);
        return _MajorVersion;

#endif

        return 0;
    }
#endregion

#region Camera相關
    public static void SetCullDistance(float[] iDistance)
    {
        Camera _mainCamera = Camera.main;
        _mainCamera.layerCullDistances = iDistance;
    }
#endregion

#region Text相關
    public static Encoding GetEncoding(int codepage)
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        return Encoding.GetEncoding(codepage);
    }

    public static byte[] ToUTF8Bytes(string value)
    {
        return UTF8Encoding.UTF8.GetBytes(value);
    }

    public static byte[] ToDefultBytes(string value)
    {
        return Encoding.Convert(Encoding.UTF8, Encoding.Default, Encoding.UTF8.GetBytes(value));
    }

    public static string BytesToString(Encoding iEncoding, byte[] iBytes)
    {
        return iEncoding.GetString(iBytes).Trim();
    }

    public static string BytesToString(int iCodepage, byte[] iBytes)
    {
        return GetEncoding(iCodepage).GetString(iBytes).Trim();
    }

    public static byte[] StringToBytes(Encoding iEncoding, string iString)
    {
        return iEncoding.GetBytes(iString);
    }

    public static byte[] StringToBytes(int iCodepage, string iString)
    {
        return GetEncoding(iCodepage).GetBytes(iString);
    }

    public static string StringTrim(string text)
    {
        return text.Trim();
    }

#endregion

#region GameObject

    public static Component AddMissingComponent(GameObject go, System.Type type)
    {
#if UNITY_FLASH
        object comp = go.GetComponent(type);
#else
        Component comp = go.GetComponent(type);
#endif
        if (comp == null)
        {
            string _AssemblyName = type.AssemblyQualifiedName;
            comp = go.AddComponent(System.Type.GetType(_AssemblyName));
        }
#if UNITY_FLASH
        return (Component)comp;
#else
        return comp;
#endif
    }

    public static bool IsUnityObjectNull(UnityEngine.Object iObject)
    {
        return iObject == null;
    }

#endregion

#region Transform
    public static void GetTransformPositionValue(Transform transform, out float x, out float y, out float z)
    {
        if (!transform)
        {
            x = 0;
            y = 0;
            z = 0;
            return;
        }
        var position = transform.position;
        x = position.x;
        y = position.y;
        z = position.z;
    }

    public static void GetTransformLocalPositionValue(Transform transform, out float x, out float y, out float z)
    {
        if (!transform)
        {
            x = 0;
            y = 0;
            z = 0;
            return;
        }
        var position = transform.localPosition;
        x = position.x;
        y = position.y;
        z = position.z;
    }
    public static void GetTransformLocalEulerAngleValue(Transform transform, out float x, out float y, out float z)
    {
        if (!transform)
        {
            x = 0;
            y = 0;
            z = 0;
            return;
        }
        var eulerAngle = transform.localEulerAngles;
        x = eulerAngle.x;
        y = eulerAngle.y;
        z = eulerAngle.z;
    }
#endregion

#region Color
    /// <summary>
    /// 回傳 Unity color 名稱
    /// </summary>
    /// <param name="iColor"> Unity color </param>
    /// <returns> color string </returns>
    public static string GetStr(this Color iColor)
    {
        //if (iColor == Color.black)
        //    return "black";

        //if (iColor == Color.blue)
        //    return "blue";

        //if (iColor == Color.clear)
        //    return "clear";

        //if (iColor == Color.cyan)
        //    return "cyan";

        //if (iColor == Color.gray)
        //    return "gray";

        //if (iColor == Color.green)
        //    return "green";

        //if (iColor == Color.grey)
        //    return "grey";

        //if (iColor == Color.magenta)
        //    return "magenta";

        //if (iColor == Color.red)
        //    return "red";

        //if (iColor == Color.yellow)
        //    return "yellow";

        //else
        //{
            return ColorUtility.ToHtmlStringRGBA(iColor);
        //}
    }

    /// <summary>
    /// Hex to Color
    /// </summary>
    /// <param name="iHex"></param>
    /// <returns>Color</returns>
    public static Color GetColor(string iHex)
    {
        if (!ColorUtility.TryParseHtmlString(iHex, out Color _Color))
        {
            return Color.white;
        }
        return _Color;
    }
#endregion

#region Dictionary
    /// <summary>
    /// add or Set value
    /// </summary>
    public static void AddEx<T, U>(this Dictionary<T, U> iDic, T iKey, U iValue)
    {
        if (iDic.ContainsKey(iKey))
            iDic[iKey] = iValue;
        else
            iDic.Add(iKey, iValue);
    }
#endregion

#region 物件池

    /// <summary>
    ///
    /// </summary>
    /// <param name="iInitalBufferSize"></param>
    /// <param name="iMaxCached"></param>
    /// <param name="iResetAction">每次 pop 跟 push 都會做一次</param>
    /// <param name="iOnetimeInitAction">只有第一次生成會做</param>
    /// <returns></returns>
    public static ObjectPool<GameObject> GetGameObjPool(
        int iInitalBufferSize,
        int iMaxCached,
        LuaInterface.LuaFunction iResetAction,
        LuaInterface.LuaFunction iOnetimeInitAction
    )
    {
        return new ObjectPool<GameObject>(
            iInitalBufferSize,
            iMaxCached,
            (iObj) =>
            {
                iResetAction?.Invoke<GameObject, object>(iObj);
            },
            (iObj) =>
            {
                iOnetimeInitAction?.Invoke<GameObject, object>(iObj);
            }
        );
    }

    public static GameObjectPool CreatePrefabObjPool(GameObject iPrefab, Vector3 iPos, Quaternion iRotation)
    {
        return new GameObjectPool(iPrefab, iPos, iRotation);
    }
#endregion

#region Astar
    public static void StartPath(Seeker iSeeker, Vector3 iStart, Vector3 iEnd, LuaFunction iCallBack, params object[] args)
    {
        iSeeker.CancelCurrentPathRequest();
        iSeeker.StartPath(iStart, iEnd, path => { iCallBack.Call(path, args); });
    }

    public static void StartPathImmediately(Seeker iSeeker, Vector3 iStart, Vector3 iEnd, LuaFunction iCallBack, params object[] args)
    {
        iSeeker.CancelCurrentPathRequest();
        Pathfinding.ABPath path = ABPath.Construct(iStart, iEnd, null);
        AstarPath.StartPath(path, true);
        AstarPath.BlockUntilCalculated(path);
        iSeeker.RunModifiers(Seeker.ModifierPass.PostProcess, path);
        //Debug.Log("path vectorPath Count:" + path.vectorPath.Count);
        iCallBack.Call(path, args);
        path.Claim(path);
        path.Release(path);
        //iSeeker.CancelCurrentPathRequest();
        //Pathfinding.Path m_Path = iSeeker.StartPath(ABPath.Construct(iStart, iEnd, null));
        //AstarPath.BlockUntilCalculated(m_Path);
        //AstarPath.BlockUntilCalculated(m_Path);
        //iSeeker.RunModifiers(Seeker.ModifierPass.PostProcess, m_Path);
        //m_Path.Claim(m_Path);
        //iCallBack.Call(m_Path, args);
        //m_Path.Release(m_Path);
    }

    public static List<Vector3> GetPath(Seeker iSeeker, Vector3 iStart, Vector3 iEnd)
    {
        iSeeker.CancelCurrentPathRequest();
        Pathfinding.Path m_Path = iSeeker.StartPath(iStart, iEnd);
        iSeeker.RunModifiers(Seeker.ModifierPass.PostProcess, m_Path);
        m_Path.Claim(m_Path);
        m_Path.Release(m_Path);
        return m_Path.vectorPath;
    }

#endregion

#region PostProcess
    public static DepthOfField TryGetdepthOfField(PostProcessVolume _pVol)
    {
        DepthOfField _return;

        if (_pVol.profile.TryGetSettings(out _return))
            return _return;

        return null;
    }
#endregion

    public static R InvokeEx<R>(this LuaFunction luaFunction, object[] args)
    {
        if (args == null || args.Length == 0)
        {
            return luaFunction.Invoke<R>();
        }
        else
        {
            switch (args.Length)
            {
                case 1:
                    return luaFunction.Invoke<object, R>(args[0]);
                case 2:
                    return luaFunction.Invoke<object, object, R>(args[0], args[1]);
                case 3:
                    return luaFunction.Invoke<object, object, object, R>(args[0], args[1], args[2]);
                case 4:
                    return luaFunction.Invoke<object, object, object, object, R>(args[0], args[1], args[2], args[3]);
                case 5:
                    return luaFunction.Invoke<object, object, object, object, object, R>(args[0], args[1], args[2], args[3], args[4]);
                case 6:
                    return luaFunction.Invoke<object, object, object, object, object, object, R>(args[0], args[1], args[2], args[3], args[4], args[5]);
                case 7:
                    return luaFunction.Invoke<object, object, object, object, object, object, object, R>(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);
                case 8:
                    return luaFunction.Invoke<object, object, object, object, object, object, object, object, R>(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7]);
                case 9:
                    return luaFunction.Invoke<object, object, object, object, object, object, object, object, object, R>(args[0], args[1], args[2], args[3], args[4], args[5], args[6], args[7], args[8]);
                default:
                    return default;
            }
        }
    }

#region IO
    public static long GetFileCRC(this FileStream iFS)
    {
        CRC _Crcunitl = new CRC();
        byte[] _Ay_Byte = new byte[iFS.Length];

        iFS.Read(_Ay_Byte, 0, _Ay_Byte.Length);
        iFS.Seek(0, SeekOrigin.Begin);
        _Crcunitl.Update(_Ay_Byte, 0, (uint)iFS.Length);
        long _Value = _Crcunitl.GetDigest();
        iFS.Close();
        return _Value;
    }

    /// <summary>
    /// 將 System Object 輸出成 MD5
    /// 需要在資料結構上加上 [System.Serializable] 才不會跳錯誤
    /// </summary>
    /// <returns> MD5 String </returns>
    //public static string GetMD5( this System.Object iObj )
    //{
    //    string _Result = string.Empty;
    //    using ( MD5 _MD5 = MD5.Create() )
    //    {
    //        BinaryFormatter _BF = new BinaryFormatter();
    //        using ( MemoryStream _MS = new MemoryStream() )
    //        {
    //            _BF.Serialize( _MS, iObj );
    //            byte[] _Bytes = _MS.ToArray();
    //            _Result = BitConverter.ToString( _MD5.ComputeHash( _Bytes ) );
    //        }
    //    }

    //    return _Result;
    //}
#endregion

    /// <summary>
    /// 檢查是否有網路
    /// </summary>
    /// <returns></returns>
    public static bool HaveNetwrok()
    {
        if ( Application.internetReachability == NetworkReachability.NotReachable )
        {
            D.Log( "沒網路!" );
            return false;
        }

        return true;
    }

#region IAP
    /// <summary>
    /// 金流插件初始化方法(參數透過 lua Unity IAP 初始化時自動填入)
    /// </summary>
    /// <param name="success">要求訂單成功回呼函式</param>
    /// <param name="fail">要求訂單失敗回乎函式</param>
    /// <param name="query">有正在執行中的訂單回呼函式</param>
    /// <param name="productName">商品清單</param>
    /// <param name="publicKey">Google public key</param>
    public static void InitIAP(LuaFunction success, LuaFunction fail, LuaFunction query, string[] productName, string publicKey)
    {
        CashFlowManager.Init(success, fail, query, productName, publicKey);
    }
    /// <summary>
    /// 要求購買商品方法
    /// </summary>
    /// <param name="index">商品索引</param>
    public static void BuyPoint(int index)
    {
        CashFlowManager.BuyPoint(index);
    }
    /// <summary>
    /// 完成訂單
    /// </summary>
    /// <param name="transactionIdentifier"></param>
    public static void FinishBuy(string transactionIdentifier)
    {
        CashFlowManager.FinishBuy(transactionIdentifier);
    }
    /// <summary>
    /// 處理尚未完成的訂單
    /// </summary>
    public static void QueryReceipt()
    {
        CashFlowManager.QueryReceipt();
    }
#endregion


#region Main

    /// <summary>
    /// 設定遊戲狀態，僅限於檔案檢查使用
    /// </summary>
    public static void SetStateFileCheck()
    {
        Main.SetStateFileCheck() ;
    }
#endregion
}
