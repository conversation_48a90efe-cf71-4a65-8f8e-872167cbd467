---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require( "Common/ArtDefine" )
require( "Logic/Icon/BasicIcon" )
require( "Logic/Icon/ItemIcon" )
require( "Logic/Icon/SkillIcon" )
require( "Logic/Icon/BuffIcon" )
require( "Logic/Icon/AtkIcon" )
require( "Logic/Icon/HeadIcon" )
require( "Logic/Icon/PetIcon" )
require( "Logic/Icon/CommonIcon" )
require( "Logic/Icon/InnerSkillIcon" )
require( "Logic/SystemSettingData/IconSetting" )

---Icon生成
---<AUTHOR>
---@version 1.0
---@since [HEM2.0]
---@date 2022.9.02
---@class IconMgr
IconMgr = {}
local this = IconMgr

---@class EIconType
EIconType =
{
    None = "None",
    Item = "Item",
    Skill = "Skill",
    Buff = "Buff",
    Head = "Head",
    Pet = "Pet",
    Common = "Common",
    InnerSkill = "InnerSkill",
}

this.gameObject = nil
this.transform = nil

---從 resource build 內讀取 ItemIcon 預製物
---@type ItemIcon
this.m_Tmp_ItemIcon = nil
---從 resource build 內讀取 SkillIcon 預製物
---@type SkillIcon
this.m_Tmp_SkillIcon = nil

this.m_Tmp_LastItemIcon = nil

this.m_FrameIcon = {}
local FRAME_ICON_NAME = "FrameIcon"

--- 物品選擇框圖片
this.m_IconSelectImage = {}
--- 物品設定框圖片
this.m_IconSettingImage = {}

--- 是否要關閉全部的選擇框 (給多選刪除用的)
this.m_DoNotCancelAll = false

--- 選擇中的 Icon
--- 在單選模式下，[1]為當前選擇的Icon，[2]為下一個被選擇的Icon，[2]僅會存在於CancelSelect前，過後清除
--- 在多選模式下，table內所有索引都是選擇中的Icon
this.m_SelectingIcon = {}

--- 存全部的頭像 Icon
this.m_HeadIconList = {}

---@type IconMgr.ESelectType 選擇種類
this.ESelectType =
{
    --- 選一個
    One = 0,
    --- 選多個
    Mult = 1,
}
--- 選擇種類
this.m_SelectType = this.ESelectType.One
--- 多選群組
this.m_MultSelectGroup = {}

--- 設定 IconMgr 選擇狀態
---@param iMultSelectGroup ItemSetting.EMultSelectGroup 設定多選群組 ( 未給值則設為單選 )
function IconMgr.SetSeletType(iMultSelectGroup)
    if iMultSelectGroup == nil then
        this.m_SelectType = this.ESelectType.One
        this.m_MultSelectGroup = {}
    else
        this.m_SelectType = this.ESelectType.Mult
        table.insert(this.m_MultSelectGroup, iMultSelectGroup)
    end
end

function IconMgr.Init()
    this.gameObject = GameObject( "IconMgr" )
    this.transform = this.gameObject.transform

    this.transform:SetParent( UIMgr.transform )
    this.transform.localScale = Vector3.one

    ResourceMgr.Load( FRAME_ICON_NAME, function(iAsset)
        this.m_FrameIcon.m_GameObject = iAsset
        if this.m_FrameIcon == nil then
            D.LogError( "Can`t found asset: " .. FRAME_ICON_NAME )
            return
        end

        this.m_FrameIcon.m_GameObject.name = FRAME_ICON_NAME
        this.m_FrameIcon.m_GameObject.transform:SetParent( this.transform )
        this.m_FrameIcon.m_GameObject.transform.localScale = Vector3.one
        this.m_FrameIcon.m_ItemFrameIcon = {}
        this.m_FrameIcon.m_SkillFrameIcon = {}
        this.m_FrameIcon.m_OtherFrameIcon = {}
        this.m_FrameIcon.m_GameObject:SetActive(true)

        for i = 0, this.m_FrameIcon.m_GameObject.transform.childCount - 1 do
            local _Child = this.m_FrameIcon.m_GameObject.transform:GetChild(i)
            _Child.gameObject:SetActive(false)
        end

        for key, value in pairs(IconSetting.EIconFrameItemType) do
            this.m_FrameIcon.m_ItemFrameIcon[key] = {}
            this.m_FrameIcon.m_ItemFrameIcon[key].m_GameObject = this.m_FrameIcon.m_GameObject.transform:Find( value ).gameObject
            -- 1 是最底下那層，不用變色，把其他外框顏色記下來
            for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
                this.m_FrameIcon.m_ItemFrameIcon[key][key1] = this.m_FrameIcon.m_ItemFrameIcon[key].m_GameObject.transform:Find( "Image_Square_" .. key1 ):
                    GetComponent( typeof( Image ) )
            end
        end

        for key, value in pairs(IconSetting.EIconFrameSkillType) do
            this.m_FrameIcon.m_SkillFrameIcon[key] = {}
            this.m_FrameIcon.m_SkillFrameIcon[key].m_GameObject = this.m_FrameIcon.m_GameObject.transform:Find( value ).gameObject
            if key == SkillData.ESkillType.InternalSkill then
                for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
                    this.m_FrameIcon.m_SkillFrameIcon[key][key1] = this.m_FrameIcon.m_SkillFrameIcon[key].m_GameObject.transform:Find( "Image_Square_" .. key1 ):
                        GetComponent( typeof( Image ) )
                end
            end
        end

        for key, value in pairs(IconSetting.EIconFrameOtherType) do
            this.m_FrameIcon.m_OtherFrameIcon[key] = {}
            this.m_FrameIcon.m_OtherFrameIcon[key].m_GameObject = this.m_FrameIcon.m_GameObject.transform:Find( value ).gameObject
            for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
                this.m_FrameIcon.m_OtherFrameIcon[key][key1] = this.m_FrameIcon.m_OtherFrameIcon[key].m_GameObject.transform:Find( "Image_Square_" .. key1 ):
                    GetComponent( typeof( Image ) )
            end
        end

        ItemIcon.Init( this.transform )
        SkillIcon.Init( this.transform )
        BuffIcon.Init( this.transform )
        AtkIcon.Init( this.transform )
        HeadIcon.Init( this.transform )
        PetIcon.Init( this.transform )
        CommonIcon.Init( this.transform )
        InnerSkillIcon.Init( this.transform )
    end )

    SpriteMgr.Load(IconSetting.m_IconSelectImageName, function(iAsset) this.m_IconSelectImage = iAsset end)
    SpriteMgr.Load(IconSetting.m_IconSetImageName, function(iAsset) this.m_IconSettingImage = iAsset end)
end

---建立新的 ItemIcon
---@param iData number 物品Idx ( 給 0 會拿到空白 Icon)
---@param iData SaveItemData 物品 save ( iData 給一種就好 )
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@param iMultSelectGroup IconSetting.EMultSelectGroup 多選群組
---@return ItemIcon
function IconMgr.NewItemIcon( iData, iParent, iWidth, iOnClick, iOnClickParam, iMultSelectGroup )
    ---後續如果有不同的物品Icon, 則依照Type回傳
    return ItemIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam, iMultSelectGroup )
end

---建立新的 SkillIcon
---@param iIdx number 技能Idx
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@param iOnPointerDown function 按下事件
---@param iOnPointerUp function 放開事件
---@param iOnDrag function 拖曳事件
---@return SkillIcon
function IconMgr.NewSkillIcon( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
    ---後續如果有不同的技能Icon, 則依照Type回傳
    return SkillIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
end

---建立新的 AtkIcon
---@param iIdx number 技能Idx
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@param iOnPointerDown function 按下事件
---@param iOnPointerUp function 放開事件
---@param iOnDrag function 拖曳事件
---@return AtkIcon
function IconMgr.NewAtkIcon( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
    ---後續如果有不同的技能Icon, 則依照Type回傳
    return AtkIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
end

---建立新的 BuffIcon
---@param iIdx number 技能Idx
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@return BuffIcon
function IconMgr.NewBuffIcon(iIdx, iParent, iWidth, iOnClick, iOnClickParam)
    return BuffIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam)
end

---建立新的 HeadIcon
---@param iIdx number 玩家 Id
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@return HeadIcon
function IconMgr.NewHeadIcon(iIdx, iParent, iWidth, iOnClick, iOnClickParam)
    local _TempHeadIcon = HeadIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam)
    table.insert(this.m_HeadIconList, _TempHeadIcon)
    return _TempHeadIcon
end

---建立新的 PetIcon
---@param iData number 寵物Idx ( 給 0 會拿到空白 Icon)
---@param iData SaveItemData 寵物 save ( iData 給一種就好 )
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@return PetIcon
function IconMgr.NewPetIcon( iData, iParent, iWidth, iOnClick, iOnClickParam )
    ---後續如果有不同的物品Icon, 則依照Type回傳
    return PetIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam )
end

---建立新的 CommonIcon
---@param iData number IconID ( 給 0 會拿到空白 Icon)
---@param iData table CommonIcon 資料(table): [1]:IconID, [2]:圖檔名稱, [3]:Icon名, [4]:稀有度, [5]:數量
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@return CommonIcon
function IconMgr.NewCommonIcon( iData, iParent, iWidth, iOnClick, iOnClickParam )
    ---後續如果有不同類型的通用Icon, 則依照Type回傳
    return CommonIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam )
end

---建立新的 InnerSkillIcon
---@param iIdx number 技能Idx
---@param iParent Transform 父物件Transform
---@param iWidth number Icon寬 (因為Icon為正方形,所以給寬就好)
---@param iOnClick function 點擊事件
---@param iOnClickParam table 點擊事件參數
---@return InnerSkillIcon
function IconMgr.NewInnerSkillIcon( iIdx, iParent, iWidth, iOnClick, iOnClickParam )
    ---後續如果有不同的技能Icon, 則依照Type回傳
    return InnerSkillIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam )
end

function IconMgr.SelectIcon(iIcon, iIsMulti)
    iIcon:ShowSelect( true )
    if iIsMulti then
        UIMgr.OpenIconName(true, iIcon)
        table.insert(this.m_SelectingIcon, iIcon)
        iIcon:DoSelectAction(true)
    else
        if IconMgr.m_SelectType == IconMgr.ESelectType.One then
            local _IsSelectSelf = false
            -- 有上一個的話就取消選擇上一個
            if this.m_SelectingIcon[1] then
                -- 檢查上一個點的是不是自己 ( 不然等等又把上一個設定成自己，下面又取消，永遠選不到 )
                _IsSelectSelf = this.m_SelectingIcon[1] == iIcon
                -- 暫存下一個，提供在取消時需要做額外判斷使用
                this.m_SelectingIcon[2] = iIcon

                if not _IsSelectSelf then
                    this.m_SelectingIcon[1]:CancelSelect()
                end

                this.m_SelectingIcon[2] = nil
            end

            if not _IsSelectSelf then
                this.m_SelectingIcon[1] = iIcon
                UIMgr.OpenIconName(true, iIcon)
            end

            iIcon:DoSelectAction(not _IsSelectSelf)
        end
    end
end

function IconMgr.CancelSelectIcon(iIcon)
    if table.Contains(this.m_SelectingIcon, iIcon) then
        iIcon:CancelSelect()
        table.removeByKey(this.m_SelectingIcon, table.GetKey(this.m_SelectingIcon, iIcon))
    end
end

--- 移除所有選擇框
---@param iForce boolean 是否強制取消
function IconMgr.CancelAllClick(iForce)
    UIMgr.OpenIconName(false)

    -- 關閉全部選擇這邊先註解起來，會影響刪除物品多選時，開 hint，會把選擇框取消 modify by hui 20240726
    if this.m_DoNotCancelAll and iForce ~= true then
        do return end
    end

    local _Count = table.Count(this.m_SelectingIcon)
    for _Key, _Value in pairs(this.m_SelectingIcon) do
        if _Value.m_NeedClearSelect then
            _Value:CancelSelect()
            table.removeByKey(IconMgr.m_SelectingIcon, _Key)
        end
    end
end

function IconMgr.RemoveIcon(iIcon)
    if iIcon == this.m_SelectingIcon[1] then
        iIcon:CancelSelect()
    end

    for k,v in pairs(this.m_HeadIconList) do
        if v == iIcon then
            this.m_HeadIconList[k] = nil
            break
        end
    end

    GameObject.Destroy(iIcon.gameObject)
end

--region HeadIcon
--- 刷全部的 HeadIcon
function IconMgr.UpdateHeadIcon(iPlayerId)
    for k,v in pairs(this.m_HeadIconList) do
    --for i = 1, table.Count(this.m_HeadIconList) do ---會跳號造成錯誤
        if v.m_Idx == iPlayerId then
            v:RefreshIcon()
        end
    end
end
--endregion HeadIcon

function IconMgr.OnUnrequire()
    if this.gameObject then
        GameObject.Destroy(this.gameObject)
        this.gameObject = nil
    end

    return true
end
return IconMgr
