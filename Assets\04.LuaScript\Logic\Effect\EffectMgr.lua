---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/SystemSettingData/EffectSetting")
require("Logic/Effect/EffectData")

---特效管理
---@class EffectMgr
---author 詩惠
---version 1.0
---since [ProjectBase] 0.1
---date 2022.12.30

EffectMgr = {}
local this = EffectMgr
---是否顯示特效除錯訊息
this.m_IS_EFFRCTMGR_LOG = false
---是否顯示特效 (給系統設定用的，設 false 會不顯示全部特效)
this.m_IS_SHOWEFFECT = true

this.Shader_ALPHA = "_Alpha"

---@type table<number, EffectData> 正在使用的特效
local _Table_PlayingEffect = {}

local m_MaterialPropertyBlock
function EffectMgr.GetPlayingEffectCount()
    return table.Count(_Table_PlayingEffect)
end

--- 特效系統是否用 pool
-- local _UsePool = true

---@type table<number, table<number, EffectData>>
local _Table_PoolObj = {}

--- 特效資源名稱
local _String_ParticleName = "Particle_{0}"
--- 取得特效名稱
function EffectMgr.GetParticleName(iEffectId)
    -- 補足5位數
    if type(iEffectId) == "number" then
        return GString.Format(_String_ParticleName, GValue.Zero_stuffing(iEffectId, 5))
    else
        return GString.Format(_String_ParticleName, iEffectId)
    end
end
--- 尋路特效編號
this.m_Guide_Path = 00061

---@class EEffectType 特效顯示種類 與特效顯示layer有關
EEffectType = {
    ---UI特效
    UI = Layer.UI,
    ---地塊特效
    Floor = Layer.FloorTrigger,
    ---模型特效
    Model = Layer.FX,
    ---HUD特效
    HUD = Layer.HUD,
     ---會飛的特效 程式控制 從生成位置飛到玩家位置
     FlyToPlayer = Layer.PlayerSelf

}

--- 串聯效果觸發起點
---@class ETandemPoint
ETandemPoint = {
    Self = 1,
    Target = 2
}

--- 串聯點類型
---@class ETandemPointType
ETandemPointType = {
    Non = 0,
    Obj = 1,
    Pos = 2
}

function EffectMgr.ShowLog(iStr, iColorStr, iIsError)
    if this.m_IS_EFFRCTMGR_LOG and (iIsError ~= nil or not iIsError) then
        iColorStr = string.IsNullOrEmpty(iColorStr) and "yellow" or iColorStr
        D.Log(GString.Format("<color={0}>[EffectMgr]{1}</color>\n{2}", iColorStr, iStr, debug.traceback()))
    end

    if iIsError then
        D.LogError(GString.Format("<color={0}>[EffectMgr]{1}</color>\n{2}", iColorStr, iStr, debug.traceback()))
    end
end

local EFFECT_ROOT_NAME = "Effect_Root"
local EFFECT_ACTIVE_NAME = "Effect_Active"
local EFFECT_STORED_NAME = "Effect_Stored"
local EFFECT_FLYING_NAME = "Effect_Flying"

---初始化
function EffectMgr.Init()
    this.m_EffectRoot = GameObject.New(EFFECT_ROOT_NAME).transform
    this.m_EffectActive = GameObject.New(EFFECT_ACTIVE_NAME).transform
    this.m_EffectStored = GameObject.New(EFFECT_STORED_NAME).transform
    this.m_EffectFlyObj = GameObject.New(EFFECT_FLYING_NAME).transform
    this.m_EffectActive:SetParent(this.m_EffectRoot)
    this.m_EffectStored:SetParent(this.m_EffectRoot)
    this.m_EffectFlyObj:SetParent(this.m_EffectRoot)

    m_MaterialPropertyBlock = MaterialPropertyBlock.New()

end

--- 設定測試資訊
--- editor 會修改物件名稱，顯示使用/pool 中的物件數量
local function SetRootObjectName()
    if ProjectMgr.IsEditor() then
        this.m_EffectActive.name = EFFECT_ACTIVE_NAME .. "_" .. this.m_EffectActive.childCount .."_" .. table.Count(_Table_PlayingEffect)
        this.m_EffectStored.name = EFFECT_STORED_NAME .. "_" .. this.m_EffectStored.childCount
    end
end

--- 更新計時
local m_EffectUpdateTimeCount = 0
--- 特效刷新函式
function EffectMgr.Update()
    m_EffectUpdateTimeCount = m_EffectUpdateTimeCount + Time.deltaTime
    if m_EffectUpdateTimeCount >= EffectSetting.m_RefreshEffectPoolSecond then
        m_EffectUpdateTimeCount = 0
        for key, value in pairs(_Table_PlayingEffect) do
            -- 地塊特效不檢查回收
            if value.Type ~= EEffectType.Floor
                    and value.endTime
                    and value.endTime ~= -1
                    and value.endTime <= Time.time then

                if Extension.IsUnityObjectNull(value.EffectGObj) then
                    _Table_PlayingEffect[key] = nil
                else
                    this.ShowLog("回收: " .. value.EffectGObj.name)
                    this.ReturnEffect(key)
                end

            end
        end

        -- 考慮要不要這麼即時刷新
        -- SetRootObjectName()
    end
end

---Reset 特效
---@param iHashCode int 特效 HashCode
---@param iType EEffectType 種類
---@param iParent Transform 父物件
---@param iIsImportantEffect boolean 是否重要
---@param iPlayCompleteDelegate function 播放結束 CallBack
---@param iActive boolean 是否顯示
---@param iTime uint 指定播放秒數 ( 只給 loop 的特效使用 )
---@param iSpeedRate number 速度
---@param iIsFollow boolean 是否跟隨 (若未設定，且 EEffectType 非 UI 則必定跟隨)
---@param iIsFollowParent boolean 是否跟隨父物件 (目前僅用在彈藥特效的浮空跟隨)
local function ResetPlayingEffect(iHashCode, iType, iParent, iIsImportantEffect, iPlayCompleteDelegate, iActive, iTime, iSpeedRate, iIsFollow, iIsFollowParent)
    if _Table_PlayingEffect[iHashCode] ~= nil then
        local _EffectObjTrans = _Table_PlayingEffect[iHashCode].EffectGObj.transform
        if iType == EEffectType.FlyToPlayer then
            _EffectObjTrans:SetParent(iParent)
            -- 類別 EEffectType.FlyToPlayer 目前只用再掉落物 如果不手動將垂直位置上移 掉落物會有一半被 "地面" 擋住無法顯示
            _EffectObjTrans.localPosition = Vector3.zero +Vector3(0,0.3,0)

            ---掉落物類的特效有軌跡的particle 因為物件第一次生成時會生在位置 (0,0,0) 再被移動到父物件下並調整座標 此動作會產生不必要的軌跡
            if _Table_PlayingEffect[iHashCode].EffectGObj.activeSelf ==false then
                _Table_PlayingEffect[iHashCode].EffectGObj:SetActive(true)
            end
        elseif iType == EEffectType.UI then
            _EffectObjTrans:SetParent(iParent)
            _EffectObjTrans.localPosition = Vector3.zero
        elseif iIsFollow == true or (iIsFollow == nil and iType ~= EEffectType.UI) then
            _EffectObjTrans:SetParent(iParent)
            _EffectObjTrans.localPosition = Vector3.zero --+ _prefabPos
        else
            _EffectObjTrans:SetParent(this.m_EffectActive)
            _EffectObjTrans.position = iParent.transform.position
        end

        _EffectObjTrans.localRotation = Vector3.zero
        _EffectObjTrans.localScale = Vector3.one

        if iIsFollowParent == false then
            if _Table_PlayingEffect[iHashCode].FloatingObjFollower == nil then
                _Table_PlayingEffect[iHashCode].FloatingObjFollower = _Table_PlayingEffect[iHashCode].EffectGObj:GetComponent(typeof(FloatingObjFollower))
                if   _Table_PlayingEffect[iHashCode].FloatingObjFollower then
                    -- 設定浮空物跟隨的目標
                    _Table_PlayingEffect[iHashCode].FloatingObjFollower.m_FollowTarget = iParent
                else
                    D.LogError (_Table_PlayingEffect[iHashCode].ParticleName .. " 沒有掛 FloatingObjFollower，請掛上並設定")
                    if Extension.IsUnityObjectNull(_Table_PlayingEffect[iHashCode].EffectGObj) then
                        GameObject.Destroy(_Table_PlayingEffect[iHashCode].EffectGObj)
                    end
                    _Table_PlayingEffect[iHashCode] = nil
                    do return end
                end

                -- 浮空跟隨不可在跟隨目標的物件層級底下，否則不起作用，因此需要移動層級
                _EffectObjTrans:SetParent(this.m_EffectActive)

            end
        end

        if ProjectMgr.IsEditor() then
            _Table_PlayingEffect[iHashCode].EffectGObj.name = _Table_PlayingEffect[iHashCode].ParticleName
        end

        if iType then
            _Table_PlayingEffect[iHashCode].EffectLifeTime:SetLayer(iType)
            _Table_PlayingEffect[iHashCode].Type = iType
        end

        if iSpeedRate then
            _Table_PlayingEffect[iHashCode].EffectLifeTime:SetSpeed(iSpeedRate)
        else
            _Table_PlayingEffect[iHashCode].EffectLifeTime:ReSetSpeed()
        end

        _Table_PlayingEffect[iHashCode].PlayCompleteDelegate = iPlayCompleteDelegate

        -- 設定死亡時間
        local _tempLifeTime = _Table_PlayingEffect[iHashCode].EffectLifeTime.m_LifeTime
        if _tempLifeTime > 0 then
            _Table_PlayingEffect[iHashCode].endTime = Time.time + _tempLifeTime
        elseif _tempLifeTime == -1 or _Table_PlayingEffect[iHashCode].EffectLifeTime.m_IsLoop then
            if iTime ~= nil and iTime > 0 then
                _Table_PlayingEffect[iHashCode].endTime = Time.time + iTime
            else
                _Table_PlayingEffect[iHashCode].endTime = _tempLifeTime
            end
        end

        _Table_PlayingEffect[iHashCode].EffectGObj:SetActive(iActive)
        if iActive then
            _Table_PlayingEffect[iHashCode].EffectLifeTime:Play();
        else
            _Table_PlayingEffect[iHashCode].EffectLifeTime:Stop();
        end

        _Table_PlayingEffect[iHashCode].IsImportantEffect = iIsImportantEffect
        if _Table_PlayingEffect[iHashCode].m_FadingOut then
            for i = 0, _Table_PlayingEffect[iHashCode].EffectLifeTime.m_ParticleSystemRenderer.Length - 1 do
                _Table_PlayingEffect[iHashCode].EffectLifeTime.m_ParticleSystemRenderer[i].material:SetFloat(this.Shader_ALPHA, 1)
            end
        end
        _Table_PlayingEffect[iHashCode].m_FadingOut = nil
    end
end

---獲得特效
---@param iEffectIdx uint 特效編號
---@param iLoadCompleteDelegate function 載入完成 CallBack
local function GetEffect(iEffectIdx, iLoadCompleteDelegate)
    local _HashCode = 0
    local _ParticleName
    _ParticleName = this.GetParticleName(iEffectIdx)
    this.ShowLog("_ParticleName: " .. _ParticleName)

    local function SetData(iObject, iHashCode)
        _HashCode = iHashCode
        _Table_PlayingEffect[_HashCode] = {}
        _Table_PlayingEffect[_HashCode].ParticleName = _ParticleName .. "_" .. _HashCode
        _Table_PlayingEffect[_HashCode].EffectId = iEffectIdx
        _Table_PlayingEffect[_HashCode].EffectGObj = iObject
        _Table_PlayingEffect[_HashCode].Time = Time.time

        if _Table_PlayingEffect[_HashCode].EffectLifeTime == nil then
            _Table_PlayingEffect[_HashCode].EffectLifeTime = iObject:GetComponent(typeof(EffectLifeTime))

            if _Table_PlayingEffect[_HashCode].EffectLifeTime then
                _Table_PlayingEffect[_HashCode].EffectLifeTime:InitComponent()
                -- 拋物線型的特效才會去抓需要的物件
                if _Table_PlayingEffect[_HashCode].EffectLifeTime.m_EffectType == EEffectLifeTimeType.Parabola then
                    _Table_PlayingEffect[_HashCode].m_Node1 = _Table_PlayingEffect[_HashCode].EffectGObj.transform:Find( "node1" )
                    _Table_PlayingEffect[_HashCode].m_Node2 = _Table_PlayingEffect[_HashCode].EffectGObj.transform:Find( "node2" )
                    _Table_PlayingEffect[_HashCode].m_Control1 = _Table_PlayingEffect[_HashCode].EffectGObj.transform:Find( "control1" )
                    _Table_PlayingEffect[_HashCode].m_Control2 = _Table_PlayingEffect[_HashCode].EffectGObj.transform:Find( "control2" )
                end

                if _Table_PlayingEffect[_HashCode].EffectLifeTime.m_IsHasModelInvisible then
                    _Table_PlayingEffect[_HashCode].m_InvisibleCtrlAy = _Table_PlayingEffect[_HashCode].EffectLifeTime.m_InvisibleCtrl
                end
            else
                _Table_PlayingEffect[_HashCode] = nil
                if Extension.IsUnityObjectNull(iObject) then
                    GameObject.Destroy(iObject)
                end
                D.LogError (_ParticleName .. " 沒有掛 EffectLifeTime，請掛上並設定")
                do return end
            end
        end

        if iLoadCompleteDelegate then
            iLoadCompleteDelegate(_HashCode)
        end

        SetRootObjectName()
    end

    local _IsBattleEditorDebug = ProjectMgr.IsEditor() and BattleMgr.m_IS_BATTLEEDITOR_DEBUG
    local _UsingTempObj
    local _DebugTraceback = debug.traceback()
    -- Pool 內有特效
    if _Table_PoolObj[iEffectIdx] and table.Count(_Table_PoolObj[iEffectIdx]) > 0 and not _IsBattleEditorDebug then
        local _TempObj = table.First(_Table_PoolObj[iEffectIdx])
        table.remove(_Table_PoolObj[iEffectIdx], 1)
        _HashCode = _TempObj.EffectData.EffectGObj:GetHashCode()
        _UsingTempObj = _Table_PoolObj[iEffectIdx][_HashCode]
        _Table_PoolObj[iEffectIdx][_HashCode] = nil
        SetData(_UsingTempObj.EffectData.EffectGObj, _HashCode)
    else
        ResourceMgr.Load(_ParticleName, function(iObj)
            if not Extension.IsUnityObjectNull(iObj) then
                _UsingTempObj = iObj
                _HashCode = _UsingTempObj:GetHashCode()
                SetData(_UsingTempObj, _HashCode)
                _Table_PlayingEffect[_HashCode].m_IsBattleEditorEffect = _IsBattleEditorDebug
            else
                D.LogError("找不到特效: " .. _ParticleName .. "\n" .. _DebugTraceback)
            end
        end)
    end
end

---還回特效
---@param iHashCode number 特效 HashCode
function EffectMgr.ReturnEffect(iHashCode)
    local _PlayingEffect = _Table_PlayingEffect[iHashCode]
    if not _PlayingEffect then
        do
            return
        end
    end
    this.ShowLog("ReturnEffect Hash: " .. iHashCode)

    -- 如果有改他的 lifetime 請改回去
    if _PlayingEffect.m_OriginaLifeTime then
        _PlayingEffect.EffectLifeTime.m_LifeTime = _PlayingEffect.m_OriginaLifeTime
        _PlayingEffect.m_OriginaLifeTime = nil
    end

    -- 先刪掉關聯的特效
    if _PlayingEffect.m_RelationEffectHash then
        EffectMgr.ReturnEffect(_PlayingEffect.m_RelationEffectHash)
        _PlayingEffect.m_RelationEffectHash = nil
    end

    if _PlayingEffect.PlayCompleteDelegate then
        _PlayingEffect.PlayCompleteDelegate(iHashCode)
    end

    if _Table_PlayingEffect[iHashCode].m_UpdateFunc then
        HEMTimeMgr.UnregisterFixUpdate(_Table_PlayingEffect[iHashCode].m_UpdateFunc)
        _Table_PlayingEffect[iHashCode].m_UpdateFunc = nil
    end

    -- local _TempObj
    if not Extension.IsUnityObjectNull(_Table_PlayingEffect[iHashCode].EffectGObj) then
        ResetPlayingEffect(iHashCode, _Table_PlayingEffect[iHashCode].Type, this.m_EffectStored, false, nil, false, nil, nil, true)

        local _IsBattleEditorDebug = _Table_PlayingEffect[iHashCode].m_IsBattleEditorEffect
        if _IsBattleEditorDebug == true then
            local _TempObj = _Table_PlayingEffect[iHashCode].EffectGObj
            if not Extension.IsUnityObjectNull(_TempObj) then
                GameObject.Destroy(_TempObj)
            end
            _Table_PlayingEffect[iHashCode] = nil
        else
            --local tempIndex = table.GetKey(_Table_PlayingEffect, _Table_PlayingEffect[iHashCode])
            local _HashCode = _Table_PlayingEffect[iHashCode].EffectGObj:GetHashCode()
            if not _Table_PoolObj[_Table_PlayingEffect[iHashCode].EffectId] then
                _Table_PoolObj[_Table_PlayingEffect[iHashCode].EffectId] = {}
            end
            _Table_PoolObj[_Table_PlayingEffect[iHashCode].EffectId][_HashCode] = {}
            _Table_PoolObj[_Table_PlayingEffect[iHashCode].EffectId][_HashCode].EffectData = _Table_PlayingEffect[iHashCode]
            _Table_PoolObj[_Table_PlayingEffect[iHashCode].EffectId][_HashCode].Time = System.DateTime.Now
            _Table_PlayingEffect[iHashCode] = nil
            -- 特效池是否滿了
            local _ObjectCount = 0
            for key, value in pairs(_Table_PoolObj) do
                _ObjectCount = _ObjectCount + table.Count(value)
            end
            local _Level = table.GetKey(EDeviceLevel, SettingMgr.m_DeviceLevel)
            if _ObjectCount + table.Count(_Table_PlayingEffect) > EffectSetting.m_MaxEffectPoolCount[_Level] then
                local _MinTime = System.DateTime.Now;
                local _MinTimeEffectId = 0;
                local _MinTimeKey = 0;
                local _TempTable = {};
                for key1, value1 in pairs(_Table_PoolObj) do
                    for key2, value2 in pairs(value1) do
                        local _Campare = System.DateTime.Compare(value2.Time , _MinTime)
                        if _Campare <= 0 then
                            _MinTimeEffectId = key1
                            _MinTimeKey = key2
                            _MinTime = value2.Time
                        end
                    end
                end
                if _MinTimeEffectId ~= 0 and _MinTimeKey ~= 0 then
                    local _TempObj = _Table_PoolObj[_MinTimeEffectId][_MinTimeKey]
                    if not Extension.IsUnityObjectNull(_TempObj.EffectData.EffectGObj) then
                        GameObject.Destroy(_TempObj.EffectData.EffectGObj)
                    end
                    _Table_PoolObj[_MinTimeEffectId][_MinTimeKey] = nil
                    -- 如果特效都用完了，就把他 uload
                    if table.Count(_Table_PoolObj[_MinTimeEffectId]) == 0 then
                        -- local _ParticleName = this.GetParticleName(_MinTimeEffectId)
                        -- ResourceMgr.Unload(_ParticleName)
                        _Table_PoolObj[_MinTimeEffectId] = nil
                    end
                    --_TempObj = nil
                else
                    this.ShowLog("刪除特效有問題 EffextMgr.ReturnEffect", "red", true)
                end
            end
        end
    else
        _Table_PlayingEffect[iHashCode] = nil
    end

    SetRootObjectName()
end

-- 清除特效池，請慎用!! ( 只給關閉戰鬥編輯器時使用，不開放給 runtime 使用 )
function EffectMgr.ClearEffectPool()
    for _Key1, _Value1 in pairs(_Table_PoolObj) do
        for _Key2, _Value2 in pairs(_Value1) do
            if not Extension.IsUnityObjectNull(_Value2.EffectData.EffectGObj) then
                GameObject.Destroy(_Value2.EffectData.EffectGObj)
            end
        end
    end

    table.Clear(_Table_PoolObj)
end

--- 設定關聯的特效 (要一起刪的) (只能設定一個；目前只給雙手特效用)
--- 慎用 請不要互相關聯，會進無線迴圈
function EffectMgr.SetReturnRelationEffect(iMainEffectHash, iRelationEffectHash)
    _Table_PlayingEffect[iMainEffectHash].m_RelationEffectHash = iRelationEffectHash
end

---檢查是否可以撥放特效
---@param iType EEffectType 特效顯式種類
---@param iEffectID number 特效編號
---@param iPos Vector 座標
---@param iIsImportantEffect boolean 是否是很重要的特效
---@return boolean 是否可以撥放特效
local function CheckCanPlayEffect(iType, iEffectID, iPos, iIsImportantEffect)
    local _Result = true

    if iType == nil or iEffectID == nil or iEffectID == 0 or iPos == nil then
        this.ShowLog(
            "播放特效失敗 EffectMgr.PlayEffect iType or iEffectID or iParent 為空 或 0, iEffectID: " .. iEffectID,
            "red"
        )
        _Result = false
    end

    if _Result and iType ~= EEffectType.Floor and not this.m_IS_SHOWEFFECT then
        this.ShowLog("已關閉全部的特效顯示")
        _Result = false
    end

    -- 可播放特效是否滿了
    this.ShowLog("table.Count(_Table_PlayingEffect): "..tostring(table.Count(_Table_PlayingEffect)), "orange")
    if _Result and table.Count(_Table_PlayingEffect) >= EffectSetting.m_MaxEffectPlayingCount[table.GetKey(EDeviceLevel, SettingMgr.m_DeviceLevel)] then
        local _MinTime = Time.time;
        local _MinTimeKey = 0;
        local _ImprotantMinTime = Time.time;
        local _ImprotantMinTimeKey = 0;
        for key, value in pairs(_Table_PlayingEffect) do
            if value.Type ~= EEffectType.Floor and value.Time < _MinTime then
                if value.IsImportantEffect then
                    _ImprotantMinTime = value.Time
                    _ImprotantMinTimeKey = key
                else
                    _MinTime = value.Time
                    _MinTimeKey = key
                end
            end
        end

        -- 有沒有不重要特效可以刪
        if _MinTimeKey == 0 then
            -- 要播的特效是重要特效
            if iIsImportantEffect then
                -- 找不到最舊的重要特效 -> 不播了
                if _ImprotantMinTimeKey == 0 then
                    _Result = false
                    this.ShowLog("特效池滿了，且找不到特效可以移除 (特效系統有問題RRR，請洽相關人員)", "gray", true)
                -- 移除最舊的重要特效
                else
                    this.ReturnEffect(_ImprotantMinTimeKey)
                end
            -- 不是重要特效 -> 不播了
            else
                _Result = false
            end
        else
            this.ReturnEffect(_MinTimeKey)
        end
    end

    return _Result
end

--- 播放特效 ( 給3D用 指定放置在某個父物件底下，並可指定面相、播放速度倍率、尺寸 )
---@param iType EEffectType 特效顯式種類
---@param iEffectID number 特效編號
---@param iParent Transform 父物件
---@param iIsImportantEffect boolean 是否是很重要的特效 ( 無視播放數量，必定撥放；慎用 )
---@param iLoadCompleteDelegate function 完成載入 CallBack ( 會回傳 Hash，需要控制可取用 )
---@param iPlayCompleteDelegate class 播放結束 Callback
---@param iTime uint 指定播放秒數 ( 只給 loop 的特效使用 )
---@param iSpeedRate number 播放速度倍率
---@param iScale Vector3 尺寸
---@param iEularAngle Vector3 指定面相
---@param iIsFollow boolean 是否跟隨 (若未設定，且 EEffectType 非 UI 則必定跟隨)
---@param iIsFollowParent boolean 是否跟隨父物件 (目前僅用在彈藥特效的浮空跟隨)
function EffectMgr.PlayEffectWithParent(
    iType, iEffectID, iParent, iIsImportantEffect, iLoadCompleteDelegate, iPlayCompleteDelegate,
    iTime, iSpeedRate, iScale, iEularAngle, iIsFollow, iIsFollowParent)
    if not CheckCanPlayEffect(iType, iEffectID, iParent.localPosition, iIsImportantEffect) then
        do
            return
        end
    end

    GetEffect(
        iEffectID,
        function(iHashCode)
            ResetPlayingEffect(iHashCode, iType, iParent, iIsImportantEffect,
                iPlayCompleteDelegate, true, iTime, iSpeedRate, iIsFollow, iIsFollowParent)

            if iScale then
                _Table_PlayingEffect[iHashCode].EffectGObj.transform.localScale = Vector3.New(iScale, iScale, iScale)
            end

            if iEularAngle then
                local _RealEuler = Vector3.zero
                _RealEuler:Copy(iEularAngle)
                _Table_PlayingEffect[iHashCode].EffectGObj.transform.localEulerAngles = _RealEuler
            end

            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
        end
    )
end

--- 播放特效 ( 給UI用 指定放置在某個父物件底下，並指定座標 )
---@param iType EEffectType 特效顯式種類
---@param iEffectID number 特效編號
---@param iParent Transform 父物件
---@param iPos Vector 座標
---@param iIsImportantEffect boolean 是否是很重要的特效
---@param iLoadCompleteDelegate function 完成載入 CallBack ( 會回傳 HashCode，用來控制 EffectMgr.ReturnEffect(iIdx) )
---@param iPlayCompleteDelegate class 播放結束 Callback
---@param iTime uint 指定播放秒數 ( 只給 loop 的特效使用 )
---@param iSpeedRate number 播放速度倍率
function EffectMgr.PlayEffectWithPos(
    iType, iEffectID, iParent, iPos, iIsImportantEffect, iLoadCompleteDelegate, iPlayCompleteDelegate, iTime, iSpeedRate)
    if not CheckCanPlayEffect(iType, iEffectID, iPos, iIsImportantEffect) then
        do
            return
        end
    end

    GetEffect(
        iEffectID,
        function(iResultIdx)
            ResetPlayingEffect(iResultIdx, iType, iParent, iIsImportantEffect, iPlayCompleteDelegate, true, iTime, iSpeedRate, nil, nil)

            -- TouchEffect 是特例，會傳 vector2 過來，需特別調整父物件
            if typeof(iPos) == typeof(UnityEngine.Vector2) then
                if _Table_PlayingEffect[iResultIdx].EffectGObj:GetComponent(typeof(UnityEngine.RectTransform)) then
                    _Table_PlayingEffect[iResultIdx].EffectGObj.transform:SetParent(iParent)
                    _Table_PlayingEffect[iResultIdx].EffectGObj:GetComponent(typeof(UnityEngine.RectTransform)).anchoredPosition = iPos
                else
                    this.ShowLog("Prefab 找不到 RectTransform, iEffectID: " .. iEffectID, "red", true)
                end
            elseif typeof(iPos) == typeof(UnityEngine.Vector3) then
                if _Table_PlayingEffect[iResultIdx].EffectGObj.transform then
                    _Table_PlayingEffect[iResultIdx].EffectGObj.transform.localPosition = iPos
                else
                    this.ShowLog("Prefab 上找不到 transform, iEffectID: " .. iEffectID, "red", true)
                end
            elseif iPos == nil then
                -- 座標規0 (在 ResetUsingEffect 做過)
            else
                this.ShowLog("給的座標類型不對: " .. tostring(typeof(iPos)), "red", true)
            end

            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iResultIdx)
            end
        end
    )
end

--- 回收 Floor 特效
function ReturnFloorEffect()
    for key, value in pairs(_Table_PlayingEffect) do
        -- 在場景上的全部 floor 特效都要回收
        if value.Type == EEffectType.Floor then
            this.ShowLog("回收: " .. value.EffectGObj.name)
            this.ReturnEffect(key)
        end
    end
end

--- 跨場景釋放特效
function EffectMgr.ReleaseEffect()
    ReturnFloorEffect()
end

---@type UnityEngine.LineRenderer 尋路特效
local m_NodeLineRenderer = nil
local m_NowPathNode = {}
---設置尋路特效
function EffectMgr.SetPathFindEffect(iNodes, isEnable)
    if not isEnable then
        --把物件關起來
        if this.m_GuildPath_Effect and not Extension.IsUnityObjectNull(m_NodeLineRenderer) then
            m_NodeLineRenderer.gameObject:SetActive(false)
        end
        return
    end

    local _SetNode = function(iNodes)
        if not this.m_GuildPath_Effect then
            return
        else
            local _GObj = _Table_PlayingEffect[this.m_GuildPath_Effect]
            if _GObj and not Extension.IsUnityObjectNull(_Table_PlayingEffect[this.m_GuildPath_Effect].EffectGObj) then
                _GObj = _Table_PlayingEffect[this.m_GuildPath_Effect].EffectGObj
                if Extension.IsUnityObjectNull(m_NodeLineRenderer) then
                    m_NodeLineRenderer = _GObj:GetComponent(typeof(UnityEngine.LineRenderer))
                end
                m_NodeLineRenderer.positionCount = #iNodes

                iNodes = table.Reverse(iNodes)
                m_NowPathNode = iNodes
                for i = 0, #iNodes - 1 do
                    iNodes[#iNodes - i] = iNodes[#iNodes - i] + (Vector3.up)
                    m_NodeLineRenderer:SetPosition(i, iNodes[#iNodes - i])
                end

                m_NodeLineRenderer.gameObject:SetActive(true)

            end
        end
    end

    if this.m_GuildPath_Effect and _Table_PlayingEffect[this.m_GuildPath_Effect] and not Extension.IsUnityObjectNull(m_NodeLineRenderer) then
        _SetNode(iNodes)
    elseif (this.m_GuildPath_Effect == nil and Extension.IsUnityObjectNull(m_NodeLineRenderer)) or (this.m_GuildPath_Effect ~= this.m_Guide_Path and _Table_PlayingEffect[this.m_GuildPath_Effect] == nil) then
        this.m_GuildPath_Effect = this.m_Guide_Path
        GetEffect(
            this.m_Guide_Path,
            function(iHashCode)
                ResetPlayingEffect(iHashCode, EEffectType.Floor, this.m_EffectActive, true, nil, isEnable)
                this.m_GuildPath_Effect = iHashCode

                _Table_PlayingEffect[iHashCode].EffectGObj.transform.localEulerAngles = Vector3(90, 0, 0)
                _SetNode(iNodes)
            end
        )
    end
end

---設定玩家目前尋路位置
function EffectMgr.SetNodeLastPosition(iNodeCount, iLastPosition)
    if Extension.IsUnityObjectNull(m_NodeLineRenderer) then
        return
    end

    ---節點數是否變化
    local _SetOtherPoint = m_NodeLineRenderer.positionCount ~= iNodeCount

    m_NodeLineRenderer.positionCount = iNodeCount

    if iNodeCount > 0 then
        if _SetOtherPoint then
            if m_NowPathNode then
                for i = 0, iNodeCount - 1 do
                    if i > 0 and m_NowPathNode[iNodeCount - i] then
                        m_NodeLineRenderer:SetPosition(i, m_NowPathNode[iNodeCount - i])
                    else
                        m_NodeLineRenderer:SetPosition(i, iLastPosition)
                    end
                end
            end
        else
            m_NodeLineRenderer:SetPosition(0, iLastPosition)
        end
    end
end

local _NodeData = {}
_NodeData.m_Point = nil
_NodeData.m_PointType = ETandemPointType.Non
_NodeData.__index = _NodeData
_NodeData.SetPoint = function(iSelf, iPoint, iPointType)
    iSelf.m_Point = iPoint
    iSelf.m_PointType = iPointType
end

--- 取得串聯結構
function EffectMgr.NewTandemNode()
    ---@class TandemData
    local _TandemData = {}

    _TandemData[1] = setmetatable({}, {__index = _NodeData})
    _TandemData[2] = setmetatable({}, {__index = _NodeData})

    return _TandemData
end

--- 串聯型特效(LineRenderer)
---@param iEffectName string 特效資源名稱
---@param iLifeTime number 持續時間
---@param iFollow boolean 是否跟隨
---@param iNodes TandemData
function EffectMgr.SetTandemEffect(iEffectName, iLifeTime, iNodes)
    GetEffect(iEffectName, function(iHashCode)
        ResetPlayingEffect(iHashCode, EEffectType.Model, this.m_EffectActive, false, nil, true, iLifeTime)
        --_Table_PlayingEffect[iHashCode].EffectGObj.transform.localEulerAngles = Vector3(0, 0, 0)
        local _LightningObj = _Table_PlayingEffect[iHashCode].EffectGObj
        local _LightningCtrl = _LightningObj:GetComponent(typeof(DigitalRuby.LightningBolt.LightningBoltScript))

        if iNodes[1].m_PointType == ETandemPointType.Obj then
            _LightningCtrl.StartObject = iNodes[1].m_Point.transform.gameObject
            _LightningCtrl.StartPosition = Vector3(0, iNodes[1].m_Point.m_Height/2, 0)
        elseif iNodes[1].m_PointType == ETandemPointType.Pos then
            _LightningCtrl.StartPosition = iNodes[1].m_Point
        end

        if iNodes[2].m_PointType == ETandemPointType.Obj then
            _LightningCtrl.EndObject = iNodes[2].m_Point.transform.gameObject
            _LightningCtrl.EndPosition = Vector3(0, iNodes[2].m_Point.m_Height/2, 0)
        elseif iNodes[2].m_PointType == ETandemPointType.Pos then
            _LightningCtrl.EndPosition = iNodes[2].m_Point
        end
    end)
end

---@param iModules WeaponFX.Module 通用變色資料
---@param iEnhanecColor EnhanceColorData 顏色
local function SetModuleColor(iModules, iEnhanecColor)
    function GetColorIndex (iRarity)
        local _Result = 1
        if iRarity == WeaponFX.ERarityColors.ColorA then
            _Result = 1
        elseif iRarity == WeaponFX.ERarityColors.ColorB then
            _Result = 2
        elseif iRarity == WeaponFX.ERarityColors.ColorC then
            _Result = 3
        elseif iRarity == WeaponFX.ERarityColors.ColorD then
            _Result = 4
        elseif iRarity == WeaponFX.ERarityColors.ColorE then
            _Result = 5
        elseif iRarity == WeaponFX.ERarityColors.ColorF then
            _Result = 6
        elseif iRarity == WeaponFX.ERarityColors.ColorG then
            _Result = 7
        elseif iRarity == WeaponFX.ERarityColors.ColorH then
            _Result = 8
        end
        return _Result
    end

    if iModules then
        for i = 0, iModules.Count - 1 do
            local _TempModules = iModules[i]
            local _tempColor = iEnhanecColor.m_TColor[GetColorIndex (_TempModules.color)]
            if _TempModules.type == WeaponFX.ModuleType.MATERIAL then
                for i = 0, _TempModules.changeMaterialColor.Count - 1 do
                    local _TempChangeMaterialColor = _TempModules.changeMaterialColor[i]
                    if _TempChangeMaterialColor then
                        m_MaterialPropertyBlock:Clear()
                        _TempChangeMaterialColor:GetPropertyBlock(m_MaterialPropertyBlock)
                        m_MaterialPropertyBlock:SetVector("_Color", Vector4.New(_tempColor.r * 3, _tempColor.g * 3, _tempColor.b * 3, _tempColor.a * 3))
                        _TempChangeMaterialColor:SetPropertyBlock(m_MaterialPropertyBlock)
                    end
                end
            elseif _TempModules.type == WeaponFX.ModuleType.PARTICLE_MAIN then
                for i = 0, _TempModules.changeStartColor.Count - 1 do
                   local _TempChangeStartColor = _TempModules.changeStartColor[i]
                   _TempChangeStartColor.main.startColor = ParticleSystem.MinMaxGradient.New(_tempColor)
                end
            elseif _TempModules.type == WeaponFX.ModuleType.PARTICLE_TRAIL then
                for i = 0, _TempModules.changeTrailColor.Count - 1 do
                   local _TempChangeTrailColor = _TempModules.changeTrailColor[i]
                   _TempChangeTrailColor.trails.colorOverLifetime = ParticleSystem.MinMaxGradient.New(_tempColor)
                end
            elseif _TempModules.type == WeaponFX.ModuleType.TRAIL_RENDERER then
                local _TempChangeTrailRenderer = _TempModules.changeTrailRenderer
                local _TempGradient = _TempChangeTrailRenderer.colorGradient
                _TempGradient.mode = UnityEngine.GradientMode.Blend
                local _GradientColorKeyAy = {
                    UnityEngine.GradientColorKey.New(iEnhanecColor.m_TColor[GetColorIndex (WeaponFX.ERarityColors.ColorE)], 0),
                    UnityEngine.GradientColorKey.New(iEnhanecColor.m_TColor[GetColorIndex (WeaponFX.ERarityColors.ColorF)], 0.741)
                }
                _TempGradient.colorKeys = _GradientColorKeyAy
                _TempChangeTrailRenderer.colorGradient = _TempGradient

                m_MaterialPropertyBlock:Clear()

                _TempChangeTrailRenderer:GetPropertyBlock(m_MaterialPropertyBlock)
                local _Pow = Mathf.Pow(2, 3.1)
                local _TempColor = iEnhanecColor.m_TColor[GetColorIndex (WeaponFX.ERarityColors.ColorG)]
                local _TempVector4WithPow = Vector4.New(_TempColor.r * _Pow, _TempColor.g * _Pow, _TempColor.b * _Pow, _TempColor.a * _Pow)
                m_MaterialPropertyBlock:SetVector("_Color", _TempVector4WithPow)
                _TempChangeTrailRenderer:SetPropertyBlock(m_MaterialPropertyBlock)
            end
        end
    end
end

---武器特殊變色
---@param iHashCode number HashCode
---@param iEnhanecColor EnhanceColorData 顏色
---@param iWeaponType EWeaponType 武器種類
---@param iRenderer UnityEngine.Renderer Rendere
---@param iMesh UnityEngine.MeshFilter Mesh
function EffectMgr.SetWeaponEffectFX(iHashCode, iEnhanecColor, iWeaponType, iRenderer, iMesh)
    local _TempWeaponFx = _Table_PlayingEffect[iHashCode].EffectGObj:GetComponent(typeof(WeaponFX))

    --[[if iWeaponType == EWeaponType.Knife then
    end]]

    --[[local _TempColor = EnhanceColor.New (iEnhanecColor.m_TColor[1], iEnhanecColor.m_TColor[2], iEnhanecColor.m_TColor[3], iEnhanecColor.m_TColor[4],
        iEnhanecColor.m_TColor[5], iEnhanecColor.m_TColor[6], iEnhanecColor.m_TColor[7], iEnhanecColor.m_TColor[8])]]

    if _TempWeaponFx.particleMeshes then
        for i = 0, _TempWeaponFx.particleMeshes.Count - 1 do
            local _tempParticle = _TempWeaponFx.particleMeshes[i]
            if _tempParticle then
                if not _tempParticle.transform.gameObject.activeSelf then
                    _tempParticle.transform.gameObject:SetActive(true)
                end

                if not iRenderer then
                    _tempParticle.transform.gameObject:SetActive(false)
                elseif Extension.IsUnityObjectNull(_tempParticle.shape.meshRenderer) then
                    -- Particle Shape 沒有給預設 Mesh，需要自己塞
                    _tempParticle.shape.meshRenderer = iRenderer
                end
            end
        end
    end

    if _TempWeaponFx.flowMeshes then
        for i = 0, _TempWeaponFx.flowMeshes.Count - 1 do
            local _tempFlowMeshes = _TempWeaponFx.flowMeshes[i]
            if _tempFlowMeshes then
                _tempFlowMeshes.sharedMesh = iMesh
            end
        end
    end

    if iEnhanecColor then
        SetModuleColor(_TempWeaponFx.modules, iEnhanecColor)
    end

    local _WeaponParticle
    if iWeaponType and (iWeaponType - 1) < _TempWeaponFx.weaponParticles.Count then
        for i = 0, _TempWeaponFx.weaponParticles.Count - 1 do
            if  _TempWeaponFx.weaponParticles[i].root then
                _TempWeaponFx.weaponParticles[i].root:SetActive(false)
            end

            if  _TempWeaponFx.weaponParticles[iWeaponType - 1].root then
                _TempWeaponFx.weaponParticles[iWeaponType - 1].root:SetActive(true)
            end

            _WeaponParticle = _TempWeaponFx.weaponParticles[iWeaponType - 1]
        end
    end

    if _WeaponParticle then
        for i = 0, _WeaponParticle.modules.Count - 1 do
            SetModuleColor(_WeaponParticle.modules, iEnhanecColor)
        end
    end
end

---防具特殊變色
---@param iHashCode number HashCode
---@param iEnhanecColor EnhanceColorData 顏色
function EffectMgr.SetParticleEffectFX(iHashCode, iEnhanecColor)
    local _TempWeaponFx = _Table_PlayingEffect[iHashCode].EffectGObj:GetComponent(typeof(WeaponFX))
    if _TempWeaponFx then
        SetModuleColor(_TempWeaponFx.modules, iEnhanecColor)
    end
end

function EffectMgr.SetHologramEffectFX(iHashCode, iRenderer, iMesh)
    if Extension.IsUnityObjectNull(iMesh) or Extension.IsUnityObjectNull(iRenderer) then
        D.LogError("[EffectMgr] EffectLifeTime 勾選 IsHasHologram 功能但沒取到自身武器的 MeshRenderer 或 Mesh 組件")
        return
    end

    local _TempWeaponFx = _Table_PlayingEffect[iHashCode].EffectGObj:GetComponentInChildren(typeof(HologramBehaviour))
    if Extension.IsUnityObjectNull(_TempWeaponFx) then
        D.LogError("[EffectMgr] EffectLifeTime 勾選 IsHasHologram 功能但未添加 HologramBehaviour 組件")
        return
    end

    _TempWeaponFx:ResetMaterialBlock(iRenderer, iMesh)
end

--- 設定 Tween 效果 (針對投射物寫的)
function EffectMgr.SetTween(iHash, iTickAction)
    _Table_PlayingEffect[iHash].EffectGObj:SetActive(false)
    if iTickAction.TickData.m_RangeType == TickData.ERangeType.Track then

        -- 如果飛行時間不是 0 的話把他設進去 lifetime 中，如果是 0 就讓他用原本 prefab 上的 lifetime
        if(iTickAction.TickData.m_FlyTime ~= 0) then
            -- 記一下原本的值
            _Table_PlayingEffect[iHash].m_OriginaLifeTime = _Table_PlayingEffect[iHash].EffectLifeTime.m_LifeTime

            --- 帶入新的值
            _Table_PlayingEffect[iHash].EffectLifeTime.m_LifeTime = iTickAction.TickData.m_FlyTime * 0.001
        end

        if _Table_PlayingEffect[iHash].EffectLifeTime.m_EffectType == EEffectLifeTimeType.Track or
            _Table_PlayingEffect[iHash].EffectLifeTime.m_LeanTweenVisual ~= nil or _Table_PlayingEffect[iHash].EffectLifeTime.m_LeanTweenPath ~= nil then
            _Table_PlayingEffect[iHash].m_UpdateFunc = function()
                local _Target
                if iTickAction.TargetKind == ETargetKind.Npc then
                    _Target = iTickAction.TargetNpc
                elseif iTickAction.TargetKind == ETargetKind.Player then
                    _Target = iTickAction.TargetPlayer
                end


                local _TargetBodyPart

                ---有找到特定位置就用特定位置 沒找到就用RoleController的物件本身
                if _Target:GetModelController() and _Target:GetModelController():GetBone(iTickAction.TickData.m_HitEffectPos) then
                    _TargetBodyPart = _Target:GetModelController():GetBone(iTickAction.TickData.m_HitEffectPos)
                    ---測試時再開
                    ---D.Log("瞄準部位 = " .. iTickAction.TickData.m_HitEffectPos .. " 物件名稱 = " .. _TargetBodyPart.gameObject.name)
                else
                    _TargetBodyPart = _Target
                end

                _Table_PlayingEffect[iHash].EffectLifeTime:MoveToTarget(_TargetBodyPart.gameObject)
            end

            if iTickAction.TargetKind == ETargetKind.Npc or iTickAction.TargetKind == ETargetKind.Player then
                HEMTimeMgr.RegisterFixUpdate(_Table_PlayingEffect[iHash].m_UpdateFunc)
            end
        else
            D.LogError("追蹤用投射物，EffectLifeTime 請不要設定拋物線類型、LeanTweenVisual、LeanTweenPath，特效編號: " .. _Table_PlayingEffect[iHash].EffectId)
            this.ReturnEffect(iHash)
            do return end
        end

    elseif iTickAction.TickData.m_RangeType == TickData.ERangeType.Penetrate or iTickAction.TickData.m_RangeType == TickData.ERangeType.AppointDelate
        or iTickAction.TickData.m_RangeType == TickData.ERangeType.AppointMagicDelate then

            if _Table_PlayingEffect[iHash].m_Node1 == nil or _Table_PlayingEffect[iHash].m_Node2 == nil or _Table_PlayingEffect[iHash].m_Control1 == nil
            or _Table_PlayingEffect[iHash].m_Control2 == nil then
            -- 擋一下檢查
            D.LogError("需要定 tween (Node1、Node2) 的特效，但卻找不到 tween，特效編號: " .. _Table_PlayingEffect[iHash].EffectId)
            this.ReturnEffect(iHash)
            do return end
        end

        _Table_PlayingEffect[iHash].m_Node1.position = _Table_PlayingEffect[iHash].EffectGObj.transform.position

        if iTickAction.TargetFloorScenePos then
            _Table_PlayingEffect[iHash].m_Node2.position = iTickAction.TargetFloorScenePos
        else
            D.LogError("飛行物有問題 TickId: " .. iTickAction.TickData.m_Idx)
        end
        _Table_PlayingEffect[iHash].m_Control1.localPosition = _Table_PlayingEffect[iHash].EffectLifeTime:AddPos(_Table_PlayingEffect[iHash].m_Node2.localPosition, 0)
        _Table_PlayingEffect[iHash].m_Control2.localPosition = _Table_PlayingEffect[iHash].EffectLifeTime:AddPos(_Table_PlayingEffect[iHash].m_Node2.localPosition, 1)
    else
        local _EffectPos = nil
        if iTickAction.TargetKind == ETargetKind.Npc then
            _EffectPos = iTickAction.TargetNpc.gameObject.transform.position
        elseif iTickAction.TargetKind == ETargetKind.Player then
            _EffectPos = iTickAction.TargetPlayer.gameObject.transform.position
        elseif iTickAction.TargetKind == ETargetKind.Floor then
            _EffectPos = iTickAction.TargetFloorScenePos
        end

        _Table_PlayingEffect[iHash].EffectGObj.transform.position = _EffectPos
    end

    _Table_PlayingEffect[iHash].EffectGObj:SetActive(true)
end

local _AnimatorIdx = Animator.StringToHash("Idx")
local _AnimatorIsPlay = Animator.StringToHash("IsPlay")
local _AnimatorIsEnd = Animator.StringToHash("IsEnd")

--- 設定 Tween 效果 (針對 loop 2 寫的)
function EffectMgr.SetLoop2Effect(iHash, iSkillData, iStackCount)
    if _Table_PlayingEffect[iHash] == nil then
        return
    end

    if iStackCount == 1 then
        -- 第一次呼叫
        _Table_PlayingEffect[iHash].m_Loop2EffectData = {}
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_SkillData = iSkillData
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator = _Table_PlayingEffect[iHash].EffectGObj:GetComponent("Animator")
        if not Extension.IsUnityObjectNull(_Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator) then
            _Table_PlayingEffect[iHash].m_Loop2EffectData.m_AnimationController = Extension.AddMissingComponent(_Table_PlayingEffect[iHash].EffectGObj, typeof(AnimationController))
            _Table_PlayingEffect[iHash].m_Loop2EffectData.m_AnimationController:SetSelf(iHash);
            _Table_PlayingEffect[iHash].m_Loop2EffectData.m_AnimationController:AnimationEventAddListener("OnEnd", EffectMgr.OnEnd)
            _Table_PlayingEffect[iHash].m_Loop2EffectData.m_AnimationController:AnimationEventAddListener("OnParticle", EffectMgr.OnParticle)
        end

        local _Temp = _Table_PlayingEffect[iHash].EffectLifeTime
        for i = 1, _Temp.m_Loop2Oboj.Count do
            local _TempObj = _Table_PlayingEffect[iHash].EffectLifeTime.m_Loop2Oboj[i - 1]
            -- 扣掉啟動那招
            _TempObj.gameObject:SetActive(i <= iSkillData.m_MaxSkillCount - 1)
        end
    elseif iStackCount ~= nil then
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator:SetInteger(_AnimatorIdx, (iStackCount - 1))
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator:SetTrigger(_AnimatorIsPlay)
    else
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator:SetInteger(_AnimatorIdx, 0)
        _Table_PlayingEffect[iHash].m_Loop2EffectData.m_Animator:SetTrigger(_AnimatorIsEnd)
    end
end

function EffectMgr.OnEnd(iSelf, iIndex, iActIndex)
    if iActIndex == -1 then
        -- 真的播完了
        for i = 0, _Table_PlayingEffect[iSelf].EffectLifeTime.m_Loop2Oboj.Count - 1 do
            local _TempObj = _Table_PlayingEffect[iSelf].EffectLifeTime.m_Loop2Oboj[i]
            _TempObj.gameObject:SetActive(true)
        end

        _Table_PlayingEffect[iSelf].m_Loop2EffectData = nil
        this.ReturnEffect(iSelf)
        do return end
    end

    if _Table_PlayingEffect[iSelf] then
        --- 循環數量
        local _LoopCount = _Table_PlayingEffect[iSelf].m_Loop2EffectData.m_SkillData.m_MaxSkillCount - 1

        if iIndex == nil then
            do return end
        end

        for i = 1, _Table_PlayingEffect[iSelf].EffectLifeTime.m_Loop2Oboj.Count do
            local _TempObj = _Table_PlayingEffect[iSelf].EffectLifeTime.m_Loop2Oboj[i - 1]
            if i > _LoopCount then
                _TempObj.gameObject:SetActive(false)
            elseif i <= iIndex then
                _TempObj.gameObject:SetActive(false)
            else
                _TempObj.gameObject:SetActive(true)
            end
        end
    end
end

function EffectMgr.OnParticle(iSelf, iIndex, iActIndex, iPlayingSkillActData)
    if _Table_PlayingEffect[iSelf] then
        --- 循環數量
        local _LoopCount = _Table_PlayingEffect[iSelf].m_Loop2EffectData.m_SkillData.m_MaxSkillCount - 1

        if iIndex == nil then
            do return end
        end

        if iIndex == _LoopCount then
            -- 技能設完了，可以 return 特效
            EffectMgr.OnEnd(iSelf, nil, -1)
        end
    end
end

---取得特效物件
---@return GameObject 回傳特效的物件本身
function EffectMgr.GetEffectObjByHashCode(iHashCode)
    if _Table_PlayingEffect[iHashCode]~=nil then
        return _Table_PlayingEffect[iHashCode].EffectGObj
    end
    return nil
end

---取得特效
---@return PlayingEffectData
function EffectMgr.GetEffectByHashCode(iHashCode)
    return _Table_PlayingEffect[iHashCode]
end


---特效物件 是animaotar type的特效
---@return bool  true > 是animator類的特效
function EffectMgr.IsEffectAnimator(iHashCode)
    if _Table_PlayingEffect[iHashCode]~=nil then
        return ( _Table_PlayingEffect[iHashCode].EffectLifeTime.m_Animators.Length > 0 )
    end
    return false
end

---開始淡出特效 (shader 需要特製，詳情請看 #117117)
function EffectMgr.StartFadeOut(iHashCode)
    if _Table_PlayingEffect[iHashCode]~=nil and _Table_PlayingEffect[iHashCode].m_FadingOut == nil then
        _Table_PlayingEffect[iHashCode].m_FadingOut = true
        local _Temp = _Table_PlayingEffect[iHashCode].EffectLifeTime
        for i = 0, _Temp.m_ParticleSystemRenderer.Length - 1 do
            local function SetAlpha(iValue)
            	_Temp.m_ParticleSystemRenderer[i].material:SetFloat(this.Shader_ALPHA, iValue)
            end
            LeanTween.value( _Temp.gameObject, System.Action_float(
                function(iValue)
                	SetAlpha(iValue)
                end), 1, 0, EffectSetting.m_DeBuffEffectFadeOutTime)
        end
    end
end

---歸還特定NPC的永久存在特效
---@param iHashTable table NPC的ModelController 紀錄特效物件用的table
---@param iLongTimeEffectID number 對應NPC的長期特效ID
function EffectMgr.ReturnNPCLongTimeEffect(iHashTable,iLongTimeEffectID)

    for key,value in pairs(iHashTable) do
        if _Table_PlayingEffect[value] ~= nil and _Table_PlayingEffect[value].EffectId == iLongTimeEffectID then
            this.ReturnEffect(value)
        end
    end
end

function EffectMgr.OnUnrequire()
    if this.m_EffectRoot.gameObject~=nil then
        this.m_EffectRoot.gameObject:Destroy()
    end
    return true
end
return EffectMgr
