---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---處理Login相關Data
---@class Login_Model
---author 默默
---version 1.0
---since [黃易群俠傳M] 0.50
---date 2022.8.1
Login_Model = {}
Login_Model.__index = Login_Model
local this = Login_Model

---@class EServerVerion Server版號
EServerVerion =
{
    TW = 148,
    CN = 174,
    TH = 182,
}

---@class EServerPlatformCode Server對應平台的識別碼
EServerPlatformCode =
{
    PC = *********,
    Android = *********,
    iOS = *********,
}

--登入

---是否已登入
this.m_IsLogin = false 

---是否快速登入
this.m_IsQuickLogin = true 

---是否是註冊中
this.m_IsRegister = false

this.m_ServerId = 0

---因為換語言要重新登入
this.m_ReLogin_Language = false

---帳號類型
---@type EAccountType
this.m_EAccountType = EAccountType.ChineseGamer 
---帳號
this.m_Str_Account = "" 
---密碼
this.m_Str_Password = "" 
---新密碼
this.m_Str_Password_New = "" 

---傳1-1會用到的參數
---@type ELoginFunc
this.m_LoginFunc = ELoginFunc.NormalLogin 

---是否勾選過使用者協議
this.m_IsAgreementPermitted = false

---公告
this.m_IsShowBulletin = false ---下次是否顯示公告
this.m_BulletinData = nil ---公告資料
---使用者條款
this.m_Str_UserContract = nil ---使用者條款

this.m_Announcement = nil

--region ServerList 相關

---下載下來的 ServerLigeht
this.m_OriginalSeverLight = {} 

---伺服器擁擠成度
this.m_ServerCrowd = {}

---下載下來的 ServerList
this.m_OriginalServerList = {} 
---選取的Server tab筆數 從1開始!
this.m_ServerIdx = 1 
---選取第幾筆分流資料 從1開始!
this.m_DivertIdx = 1 
this.m_GroupId = 0
this.m_LoopList_ServerName = nil
this.m_LoopList_Divert = nil
this.m_ServerData = nil
--endregion

---下載下來的 BulletinData
this.m_OriginalBulletinData = {}

---下載下來的 UserContract
this.m_OriginalUserContract = {}

--- 最大帳號紀錄數量
this.MAX_ACCOUNT_RECORD_NUM = 10
---取得預設的ServerID
local function GetDefultServerID(iServerCount)
    local _LastSelectServerID = 0
    if ProjectMgr.IsDebug() then
        --如果是 DeBug 版本
        _LastSelectServerID = 2101
    else
        --亂數產生伺服器群組索引
        local _GroupIndex = math.floor(Random.Range(1,iServerCount))
        --伺服器分流清單
        local _DivertList
        --伺服器群組ID Ex: 702 => _GroupId = 7
        local _GroupId = 0
        --由伺服器群組索引取得 伺服器群組ID 和 伺服器分流清單
        _GroupId, _DivertList = Login_Model.GetServerByIdx(_GroupIndex)
        --取得 伺服器分流清單的總數 m_Idx 不算 所以要 -1 
        local _DivertCount = table.Count(_DivertList) - 1
        --亂數產生伺服器分流索引
        local _DivertIndex = math.floor(Random.Range(1,_DivertCount))
        --回傳所得到的 ServerID
        _LastSelectServerID = _DivertList[_DivertIndex].m_ID
    end

    return _LastSelectServerID
end

---取得上次選取的 ServerID 如果沒有 就做一個給他
local function GetLastSelectServerID()
    --取得檔案資料
    local _DataTable = ClientSaveMgr.GetDataTable(EClientSaveDataType.Device)
    local _LastSelectServerID = rawget(_DataTable,"m_LastSelectServerID")
    --取得 ServerList
    local _ServerList = Login_Model.m_ServerList
    --伺服器群組總數
    local _ServerCount = table.Count(_ServerList)
    

    --region 檢查上次的紀錄還有沒有在清單內

    --取得伺服器群組ID
    local _SeverGroupId = math.floor(_LastSelectServerID / 100)
    --取得伺服器分流清單總數
    local _DivertCount = table.Count(Login_Model.m_ServerList[_SeverGroupId]) - 1
    --沒有的話 讓他等於0
    if _DivertCount <= 0 then
        _LastSelectServerID = 0
    end

    --endregion

    if _ServerCount ~= 0 then
        --如果總數不為零
        if _LastSelectServerID == 0 then
            _LastSelectServerID = GetDefultServerID(_ServerCount)
        end
    else
        _LastSelectServerID = Login_Model.m_ServerList[1][1].m_ID
        if _LastSelectServerID == nil or _LastSelectServerID == 0 then
            GetDefultServerID(_ServerCount)
        end
    end
    
    --存到 m_LastSelectServerID
    ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device, "m_LastSelectServerID", _LastSelectServerID)
    return _LastSelectServerID
end
--- 設定常用帳號列表
---@param iRecordAccount string 帳號
local function SetRecordList(iRecordAccount)
    -- 排除空帳號
    if not string.IsNullOrEmpty(iRecordAccount) then
        -- 檢查帳號是否存在列表中 已存在帳號要移除
        local _AccountKey = table.GetKey( this.m_Table_RecordAccounts, iRecordAccount)
        if _AccountKey ~= nil then
            table.remove(this.m_Table_RecordAccounts ,_AccountKey)
        end
        -- 記錄帳號
        table.insert(this.m_Table_RecordAccounts, 1, this.m_Str_Account)
    end
    -- 如果超過儲存上限將超過上限的資料移除
    local _RemoveCount = table.Count(this.m_Table_RecordAccounts) - this.MAX_ACCOUNT_RECORD_NUM
    if _RemoveCount > 0 then
        for i = 1, _RemoveCount do
            table.remove(this.m_Table_RecordAccounts,this.MAX_ACCOUNT_RECORD_NUM + i)
        end
    end
end

---初始化登入介面相關所需資料
function Login_Model.Init()
    this.m_IsLogin = false
    this.m_IsQuickLogin = ClientSaveMgr.GetDataValue(EClientSaveDataType.Account,"m_IsRecordAccount")

    this.m_EAccountType = EAccountType.None
    this.m_Str_Account = ""
    this.m_Str_Password = ""
    
    if this.m_IsQuickLogin then
        this.m_EAccountType = ClientSaveMgr.GetDataValue(EClientSaveDataType.Account,"m_AccountType")
        this.m_Str_Account = ClientSaveMgr.GetDataValue(EClientSaveDataType.Account,"m_LastAccount")
        this.m_Str_Password = ClientSaveMgr.GetDataValue(EClientSaveDataType.Account,"m_LastPassword")
    end

    this.m_IsAgreementPermitted = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_IsAgreementPermitted")
    this.m_IsShowBulletin = not ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_IsShowBulletinAgain")

    this.m_Table_RecordAccounts = ClientSaveMgr.GetDataValue(EClientSaveDataType.Account,"m_RecordAccounts")
end

---存帳號設定 沒有就清除存檔
---@param iIsSaveLoginData boolean 是否要存帳號設定
function Login_Model.SaveLoginData(iIsSaveLoginData)
    -- 是否快速登入 不存就等於沒有快速登入
    this.m_IsQuickLogin = iIsSaveLoginData
    ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account,"m_IsRecordAccount", iIsSaveLoginData)
    -- 重設帳號
    if iIsSaveLoginData then
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_AccountType", this.m_EAccountType)
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_LastAccount", this.m_Str_Account)
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_LastPassword", this.m_Str_Password)
    else
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_AccountType", -1)
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_LastAccount", "")
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account, "m_LastPassword", "")
    end
    
    --if not this.m_IsQuickLogin then
    SetRecordList(this.m_Str_Account)
    ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Account,"m_RecordAccounts", this.m_Table_RecordAccounts)
    --end  
end

---儲存勾選使用者協議
function Login_Model.SaveAgreementPermitted(iIsAgree)
    local _IsAgreementPermitted = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_IsAgreementPermitted")
    if _IsAgreementPermitted ~= iIsAgree then
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_IsAgreementPermitted", iIsAgree)
    end
end

--region ServerList

---初始化 ServerList
function Login_Model.InitServerList()
    Login_Model.InitServerListToLua()
    Login_Model.GetLastSelectSeverData()
end

---分類ServerList
---結構: table<群組ID,table<第幾筆,ServerData>>
function Login_Model.InitServerListToLua()
    this.m_ServerList = {}
    local _ServerIdx = 0 ---第幾筆Data (因為table會亂排(?)所以特別開個變數存)
    for i = 1, table.Count(this.m_OriginalServerList) do
        local _ServerData = Login_Model.GetServerList(i)
        local _GroupId = math.floor(_ServerData.m_ID / 100)
        if this.m_ServerList[_GroupId] == nil then
            this.m_ServerList[_GroupId] = {}
            _ServerIdx = _ServerIdx + 1
            this.m_ServerList[_GroupId].m_Idx = _ServerIdx
        end
        local _DivertIdx = table.Count(this.m_ServerList[_GroupId])
        this.m_ServerList[_GroupId][_DivertIdx] = _ServerData
    end
end

---取得上次所選取的 SeverData
function Login_Model.GetLastSelectSeverData()
    --取得上次所選取的 SeverID 一定會 > 0 因為沒有會自動產生
    local _LastSeverID = GetLastSelectServerID()
    --取得伺服器群組ID
    local _SeverGroupId = math.floor(_LastSeverID / 100)
    --取得伺服器分流清單總數
    local _DivertCount = table.Count(this.m_ServerList[_SeverGroupId]) - 1
    
    for i = 1, _DivertCount do
        local _CheckSeverData = this.m_ServerList[_SeverGroupId][i]
        if _CheckSeverData.m_ID == _LastSeverID then
            this.m_ServerData = _CheckSeverData
            this.m_ServerIdx = this.m_ServerList[_SeverGroupId].m_Idx
            this.m_GroupId = _SeverGroupId
            this.m_DivertIdx = i
            return
        end
    end
end

function Login_Model.GetServerList(iIdx)
    return this.m_OriginalServerList[iIdx]
end

function Login_Model.GetServerByID(iID)
    local _GroupId = math.floor(iID / 100)
    local _TServerDataList = this.m_ServerList[_GroupId]
    if _TServerDataList ~= nil then
        for key, value in pairs(_TServerDataList) do
            if value.m_ID == iID then
                return value, _TServerDataList
            end
        end
    end
    return nil
end

---@param iIdx number 第幾筆資料
function Login_Model.GetServerByIdx(iIdx)
    for _GroupId, _ServerList in pairs(this.m_ServerList) do
        if iIdx == _ServerList.m_Idx then
            return _GroupId, _ServerList
        end
    end
    return nil
end
---取得分流資料
---@param iGroupID number 第幾組
---@param iIdx number 第幾筆資料
function Login_Model.GetServerDivertByIdx(iGroupID, iIdx)
    for key, value in pairs(this.m_ServerList[iGroupID]) do
        if key == iIdx then
            return value
        end
    end
    return nil
end

---設定 ServerData
function Login_Model.SetServerData()
    local _TServerList = nil
    for key, value in pairs(this.m_ServerList) do
        if value.m_Idx == this.m_ServerIdx then
            _TServerList = value
        end
    end
    if _TServerList ~= nil then
        this.m_ServerData = _TServerList[this.m_DivertIdx]
    else
        this.m_ServerData = nil
    end
end

function Login_Model.GetServerData()
    return this.m_ServerData
end

--endregion

--region 登入
function Login_Model.CGLogin()
    Login_Model.m_EAccountType = EAccountType.ChineseGamer
    Login_Controller.SetPage(Login_Controller.m_PageType.CGLogin)
end

function Login_Model.AppleLogin()
    --Login_Model.m_EAccountType = EAccountType.Apple
end

function Login_Model.GoogleLogin()
    --Login_Model.m_EAccountType = EAccountType.Google
end

function Login_Model.FBLogin()
    --Login_Model.m_EAccountType = EAccountType.FB
end

function Login_Model.SendLoginRequest()
    this.m_LoginFunc = ELoginFunc.NormalLogin
    Login_Model.Login(
        math.floor(Login_Model.GetServerData().m_ID / 100),
        Login_Model.GetServerData().m_Ip,
        Login_Model.GetServerData().m_Port,
        Login_Model.m_EAccountType,
        Login_Model.m_Str_Account,
        Login_Model.m_Str_Password
    )
end

function Login_Model.SendRegisterRequest()
    this.m_LoginFunc = ELoginFunc.Register
    Login_Model.Login(
        math.floor(Login_Model.GetServerData().m_ID / 100),
        Login_Model.GetServerData().m_Ip,
        Login_Model.GetServerData().m_Port,
        Login_Model.m_EAccountType,
        Login_Model.m_Str_Account,
        Login_Model.m_Str_Password
    )
end

function Login_Model.Login(iServerId, iIp, iPort, iEAccountType, iAccount, iPassword)
    this.m_ServerId = tonumber(iServerId)
    this.m_EAccountType = iEAccountType
    this.m_Str_Account = iAccount
    this.m_Str_Password = iPassword
    ClientSocket.Connect(iIp, iPort)
end

function Login_Model.SendChangePassword(iStr_Password_New)
    this.m_Str_Password_New = iStr_Password_New
    SendProtocol_001._014(this.m_Str_Password,iStr_Password_New)
end

function Login_Model.SetChangePasswordSuccess()
    this.m_Str_Password = this.m_Str_Password_New
    this.m_Str_Password_New = ""

    Login_Controller.SetPage(Login_Controller.m_PageType.CGLogin)
end

--endregion

--[[
---取得 server Version
---@param iVersion EServerVerion
---@return byte 登入用參數
function Login_Model.GetServerVesion( iVersion )
    if iVersion == EServerVerion.TW then
        return 172
    elseif iVersion == "CN" then
        return 174
    elseif iVersion == "TH" then
        return 182
    end
end--]]

---@return ulong 取得serverCode = ServerPlatformCode + VersionCode(小版號) * 1000 + FTP_CODE(=1)
function Login_Model.GetConnectServerCode()
    local _ServerCode = Login_Model.GetServerPlatformCode()
        + Game.m_Bundle_Version_Code * 1000
        + SERVER_FTP_CODE
    return _ServerCode
end


---@return ulong 取得ServerPlatformCode
function Login_Model.GetServerPlatformCode()
    if ProjectMgr.IsPC() then
        return *********
    elseif ProjectMgr.IsAndroid() then
        return *********
    elseif ProjectMgr.IsiOS() then
        return *********
    end
end

--- 取得玩家帳號
---@return string 玩家帳號
function Login_Model.GetPlayerAccount()
    return this.m_Str_Account
end
