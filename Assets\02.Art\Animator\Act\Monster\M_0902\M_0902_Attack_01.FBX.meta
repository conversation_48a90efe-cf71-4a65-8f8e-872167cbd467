fileFormatVersion: 2
guid: b4ec60b94d66a904ead3c7b28934f812
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bip001
  - first:
      1: 100002
    second: Bip001 Head
  - first:
      1: 100004
    second: Bip001 L Clavicle
  - first:
      1: 100006
    second: Bip001 L UpperArm
  - first:
      1: 100008
    second: Bip001 Neck
  - first:
      1: 100010
    second: Bip001 Pelvis
  - first:
      1: 100012
    second: Bip001 R Clavicle
  - first:
      1: 100014
    second: Bip001 R UpperArm
  - first:
      1: 100016
    second: Bip001 Spine
  - first:
      1: 100018
    second: Bip001 Spine1
  - first:
      1: 100020
    second: <PERSON>_ L_Finger_0
  - first:
      1: 100022
    second: Bone_ L_Finger_01
  - first:
      1: 100024
    second: Bone_ R_Finger_0
  - first:
      1: 100026
    second: Bone_ R_Finger_01
  - first:
      1: 100028
    second: Bone_F_skirt_001
  - first:
      1: 100030
    second: Bone_<PERSON>_skirt_002
  - first:
      1: 100032
    second: <PERSON>_<PERSON>_skirt_003
  - first:
      1: 100034
    second: <PERSON>_<PERSON>_ Forearm
  - first:
      1: 100036
    second: <PERSON>_L_Finger_1
  - first:
      1: 100038
    second: <PERSON>_L_Finger_11
  - first:
      1: 100040
    second: Bone_L_Finger_2
  - first:
      1: 100042
    second: Bone_L_Finger_21
  - first:
      1: 100044
    second: Bone_L_Finger_3
  - first:
      1: 100046
    second: Bone_L_Finger_31
  - first:
      1: 100048
    second: Bone_L_Finger_4
  - first:
      1: 100050
    second: Bone_L_Finger_41
  - first:
      1: 100052
    second: Bone_L_Hand
  - first:
      1: 100054
    second: Bone_L_shoulder_01
  - first:
      1: 100056
    second: Bone_L_wing_01
  - first:
      1: 100058
    second: Bone_R_ Forearm
  - first:
      1: 100060
    second: Bone_R_Finger_1
  - first:
      1: 100062
    second: Bone_R_Finger_11
  - first:
      1: 100064
    second: Bone_R_Finger_2
  - first:
      1: 100066
    second: Bone_R_Finger_21
  - first:
      1: 100068
    second: Bone_R_Finger_3
  - first:
      1: 100070
    second: Bone_R_Finger_31
  - first:
      1: 100072
    second: Bone_R_Finger_4
  - first:
      1: 100074
    second: Bone_R_Finger_41
  - first:
      1: 100076
    second: Bone_R_Hand
  - first:
      1: 100078
    second: Bone_R_shoulder_01
  - first:
      1: 100080
    second: Bone_R_wing_01
  - first:
      1: 100082
    second: Bone_skirt_01
  - first:
      1: 100084
    second: Dummy_L_ Forearm_002
  - first:
      1: 100086
    second: Dummy_L_ UpperArm_001
  - first:
      1: 100088
    second: Dummy_L_shoulder_002
  - first:
      1: 100090
    second: Dummy_L_wing_001
  - first:
      1: 100092
    second: Dummy_R_ Forearm_001
  - first:
      1: 100094
    second: Dummy_R_ UpperArm_002
  - first:
      1: 100096
    second: Dummy_R_shoulder_001
  - first:
      1: 100098
    second: Dummy_R_wing_001
  - first:
      1: 100100
    second: Dummy_wing_001
  - first:
      1: 100102
    second: //RootNode
  - first:
      4: 400000
    second: Bip001
  - first:
      4: 400002
    second: Bip001 Head
  - first:
      4: 400004
    second: Bip001 L Clavicle
  - first:
      4: 400006
    second: Bip001 L UpperArm
  - first:
      4: 400008
    second: Bip001 Neck
  - first:
      4: 400010
    second: Bip001 Pelvis
  - first:
      4: 400012
    second: Bip001 R Clavicle
  - first:
      4: 400014
    second: Bip001 R UpperArm
  - first:
      4: 400016
    second: Bip001 Spine
  - first:
      4: 400018
    second: Bip001 Spine1
  - first:
      4: 400020
    second: Bone_ L_Finger_0
  - first:
      4: 400022
    second: Bone_ L_Finger_01
  - first:
      4: 400024
    second: Bone_ R_Finger_0
  - first:
      4: 400026
    second: Bone_ R_Finger_01
  - first:
      4: 400028
    second: Bone_F_skirt_001
  - first:
      4: 400030
    second: Bone_F_skirt_002
  - first:
      4: 400032
    second: Bone_F_skirt_003
  - first:
      4: 400034
    second: Bone_L_ Forearm
  - first:
      4: 400036
    second: Bone_L_Finger_1
  - first:
      4: 400038
    second: Bone_L_Finger_11
  - first:
      4: 400040
    second: Bone_L_Finger_2
  - first:
      4: 400042
    second: Bone_L_Finger_21
  - first:
      4: 400044
    second: Bone_L_Finger_3
  - first:
      4: 400046
    second: Bone_L_Finger_31
  - first:
      4: 400048
    second: Bone_L_Finger_4
  - first:
      4: 400050
    second: Bone_L_Finger_41
  - first:
      4: 400052
    second: Bone_L_Hand
  - first:
      4: 400054
    second: Bone_L_shoulder_01
  - first:
      4: 400056
    second: Bone_L_wing_01
  - first:
      4: 400058
    second: Bone_R_ Forearm
  - first:
      4: 400060
    second: Bone_R_Finger_1
  - first:
      4: 400062
    second: Bone_R_Finger_11
  - first:
      4: 400064
    second: Bone_R_Finger_2
  - first:
      4: 400066
    second: Bone_R_Finger_21
  - first:
      4: 400068
    second: Bone_R_Finger_3
  - first:
      4: 400070
    second: Bone_R_Finger_31
  - first:
      4: 400072
    second: Bone_R_Finger_4
  - first:
      4: 400074
    second: Bone_R_Finger_41
  - first:
      4: 400076
    second: Bone_R_Hand
  - first:
      4: 400078
    second: Bone_R_shoulder_01
  - first:
      4: 400080
    second: Bone_R_wing_01
  - first:
      4: 400082
    second: Bone_skirt_01
  - first:
      4: 400084
    second: Dummy_L_ Forearm_002
  - first:
      4: 400086
    second: Dummy_L_ UpperArm_001
  - first:
      4: 400088
    second: Dummy_L_shoulder_002
  - first:
      4: 400090
    second: Dummy_L_wing_001
  - first:
      4: 400092
    second: Dummy_R_ Forearm_001
  - first:
      4: 400094
    second: Dummy_R_ UpperArm_002
  - first:
      4: 400096
    second: Dummy_R_shoulder_001
  - first:
      4: 400098
    second: Dummy_R_wing_001
  - first:
      4: 400100
    second: Dummy_wing_001
  - first:
      4: 400102
    second: //RootNode
  - first:
      74: 7400000
    second: M_0902_Attack_01
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0902_Attack_01
      takeName: M_0902_Attack_01
      internalID: 0
      firstFrame: 0
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 8f371ef3a78978e47a0fda238d618546,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
