---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Class Describes 腳色屬性面板的控制器 下面有子介面 屬姓/頭像/生活技能/其他(貨幣)
---@class RoleAttribute_Controller
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.25
-------------------------------------------------------------

RoleAttribute_Controller = {}
--屬性介面
require("UI/RoleAttribute/RoleAttribute_Page_Attribute")
---屬性子介面
require("UI/RoleAttribute/RoleAttribute_Page_Title")
---永丹子介面
require("UI/RoleAttribute/RoleAttribute_Page_Yudan")
-- 永丹介面
require("UI/RoleAttribute/RoleAttribute_Page_Attribute/RoleAttribute_Page_Attribute_Detail/RoleAttribute_Page_Attribute_Detail_Medicine")
---頭像子介面
require("UI/RoleAttribute/RoleAttribute_Page_Avatar")
--生活技能介面
require("UI/RoleAttribute/RoleAttribute_Page_LifeSkill")
--資源介面
require("UI/RoleAttribute/RoleAttribute_Page_Currency")

---屬性介面 下面的子介面
ERoleControllerSubPageType = 
{
    ---屬性子介面
    Attribute=1,
    ---稱號系統子介面
    Title = 2,
    ---永丹子介面
    Yudan = 3,
    ---頭像子介面
    Avatar = 4,
    ---其他(貨幣)子介面
    Currency = 5,
    ---生活技能子介面
    LivingSkill = 6,
    ---for迴圈數量使用
    Max=7,
}

RoleAttribute_Controller[ERoleControllerSubPageType.Attribute] = RoleAttribute_Page_Attribute
RoleAttribute_Controller[ERoleControllerSubPageType.Title] = RoleAttribute_Page_Title
RoleAttribute_Controller[ERoleControllerSubPageType.Yudan] = RoleAttribute_Page_Attribute_Detail_Medicine
RoleAttribute_Controller[ERoleControllerSubPageType.Avatar] = RoleAttribute_Page_Avatar
RoleAttribute_Controller[ERoleControllerSubPageType.Currency] = RoleAttribute_Page_Currency
RoleAttribute_Controller[ERoleControllerSubPageType.LivingSkill] = RoleAttribute_Page_LifeSkill

local this = RoleAttribute_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("RoleAttribute_View", "RoleAttribute_Controller", EUIOrderLayers.HalfPage_Left)

--子頁籤要開啟的對應物件
this.m_SubPageGameObject = {}

local SELECTED_ROLEATTRIBUTE_SPRITE =  "MainIcon_091_L"

---初始化
function RoleAttribute_Controller.Init()
    --取背景寬度
    this.m_Trans_BG = this.m_ViewRef.m_Dic_Trans:Get("&Empty_Panel")
    this.m_Trans_BG_Width = this.m_Trans_BG.gameObject.transform:Find("BackGround"):GetComponent("RectTransform").rect.width
    --設定子分頁
    this.m_SubPageGameObject[ERoleControllerSubPageType.Attribute] = this.m_ViewRef.m_Dic_Trans:Get("&RoleAttributePage").gameObject
    this.m_SubPageGameObject[ERoleControllerSubPageType.Title] = this.m_ViewRef.m_Dic_Trans:Get("&TitlePage").gameObject
    this.m_SubPageGameObject[ERoleControllerSubPageType.Yudan] = this.m_ViewRef.m_Dic_Trans:Get("&YudanPage").gameObject
    this.m_SubPageGameObject[ERoleControllerSubPageType.Avatar] = this.m_ViewRef.m_Dic_Trans:Get("&AvatarPage").gameObject
    this.m_SubPageGameObject[ERoleControllerSubPageType.Currency] = this.m_ViewRef.m_Dic_Trans:Get("&CurrencyPage").gameObject
    this.m_SubPageGameObject[ERoleControllerSubPageType.LivingSkill] = this.m_ViewRef.m_Dic_Trans:Get("&LiveSkillPage").gameObject
    
    --取得分頁按鍵 GroupButton
    this.m_GroupBtn_SubPage = this.m_ViewRef.m_Dic_Trans:Get("&SubPageButtonSet").gameObject
    local _UIGroupBtnCtrl = this.m_GroupBtn_SubPage:GetComponent("UIGroupButtonCtrl")

    this.m_SubPageButtons = {}
    --附加切換子分頁方法
    for i = 1, _UIGroupBtnCtrl.ButtonCount do
        GroupButton.AddListenerByIndex(this.m_GroupBtn_SubPage,i,EventTriggerType.PointerClick,function()
			RoleAttribute_Controller.OpenSubPage( i )
		end)

        this.m_SubPageButtons[i] = {}

        local _ItemStr = "&SubBtn" .. i

        if this.m_ViewRef.m_Dic_Trans:Get(_ItemStr) ~= nil  then
            this.m_SubPageButtons[i].m_ButtonObj = this.m_ViewRef.m_Dic_Trans:Get(_ItemStr)
        end
        
    end

    --各分頁初始化
    for i = 1, ERoleControllerSubPageType.Max-1 do
       if RoleAttribute_Controller[i]~=nil then
        RoleAttribute_Controller[i].Init(this)
       end
    end

    --設定關閉按鍵
    this.m_CloseRoleAttributeBtn = this.m_ViewRef.m_Dic_Trans:Get("&CloseRoleAttributeBtn").gameObject
    Button.AddListener(this.m_CloseRoleAttributeBtn, EventTriggerType.PointerClick, function() RoleAttribute_Controller.ClosePage() end)

end

---Update
function RoleAttribute_Controller.Update()
       --子分頁數量
       for i = 1, ERoleControllerSubPageType.Max-1 do
        if RoleAttribute_Controller[i]~=nil and RoleAttribute_Controller[i].m_Page_GameObject.activeSelf ==true  then
            RoleAttribute_Controller[i].Update()
        end
    end
end

---開啟屬性頁面
--- iParam[1] bool 判斷是不是要同步開裝備頁面
--- iParam[2] mumber 判斷要開啟哪個子頁面
--- iParam[3之後] 子頁面是否有參數需要處理 可為空
function RoleAttribute_Controller.Open(iParam)
    
    ---判斷是否需要同時開啟裝備頁面
    local _OpenEquip = table.remove(iParam ,1)
    if _OpenEquip then
        UIMgr.Open(Equipment_Controller)
    end

    local _SubPageIndex = table.remove(iParam ,1)
    ---每次打開屬性介面 有指定分頁就開指定分頁 沒有就開屬性分頁
    local _SubPageIndex = (_SubPageIndex == nil) and ERoleControllerSubPageType.Attribute or _SubPageIndex

    RoleAttribute_Controller.OpenSubPage(_SubPageIndex,iParam)

    return true
end

---子分頁開啟方法
---@param iIndex number 要開啟哪個子頁面
---@param iParam table 開啟參數(可為空)
function RoleAttribute_Controller.OpenSubPage(iIndex,iParam)
    for i = 1, #this.m_SubPageGameObject do
        if i ==iIndex then
            if RoleAttribute_Controller[i]~=nil then
                RoleAttribute_Controller[i].Open(iParam)
                Button.SetSelect(this.m_SubPageButtons[i].m_ButtonObj,true)
            end
        else
            if RoleAttribute_Controller[i]~=nil then
                RoleAttribute_Controller[i].Close() 
                Button.SetSelect(this.m_SubPageButtons[i].m_ButtonObj,false)
            end
        end
    end
end

---關閉屬性介面
function RoleAttribute_Controller.ClosePage()

    --關閉屬性介面
    UIMgr.Close(RoleAttribute_Controller)

    --點擊某特定子頁後 關掉屬性面板 再重開 必須要每個子頁都能被打開
    local _UIGroupBtnCtrl = this.m_GroupBtn_SubPage:GetComponent("UIGroupButtonCtrl")
    for i = 1, _UIGroupBtnCtrl.ButtonCount do
        _UIGroupBtnCtrl:SetInteractableByIndex(i,true)

        Button.SetEnable(this.m_SubPageButtons[i].m_ButtonObj)
    end

    ---確保加點結果介面被關閉
    RoleAttribute_Page_Attribute.ShowAddResultPage(false)
    RoleAttribute_Page_Attribute.ShowDetailPage(false)
    RoleAttribute_Page_Attribute.ShowIdlePointPanel(false)

    --同步關閉裝備介面
    UIMgr.Close(Equipment_Controller)

    ---頭像分頁相關
    ---歸還所有特效
    RoleAttribute_Page_Avatar.RecycleAllEffectToHeadAndFrame()
    
    ---刷新主介面頭像顯示
    Main_SubCtrl_RoleAttribute.UpdatePlayerIcon(PlayerData.m_LoginPlayerData.m_HeadIconID_Server,PlayerData.m_LoginPlayerData.m_FrameIconID_Sever)
end


function RoleAttribute_Controller.OnDestroy()
    local _IsCanDestroy = true

    for i = 1, #this.m_SubPageGameObject do
        if RoleAttribute_Controller[i] ~= nil and RoleAttribute_Controller[i].OnDestroy ~= nil then
            _IsCanDestroy =  RoleAttribute_Controller[i].OnDestroy()
        end
        if not _IsCanDestroy then
            break
        end
    end

    return _IsCanDestroy
end

