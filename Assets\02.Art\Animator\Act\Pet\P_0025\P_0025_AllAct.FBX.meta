fileFormatVersion: 2
guid: ec2a4a8f14d743c44b5ef94a546590e3
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Bone001
  - first:
      1: 100004
    second: Bone002
  - first:
      1: 100006
    second: Bone003
  - first:
      1: 100008
    second: Bone005
  - first:
      1: 100010
    second: Bone007
  - first:
      1: 100012
    second: Bone008
  - first:
      1: 100014
    second: Bone009
  - first:
      1: 100016
    second: Bone011
  - first:
      1: 100018
    second: Bone012
  - first:
      1: 100020
    second: Bone013
  - first:
      1: 100022
    second: Bone015
  - first:
      1: 100024
    second: Bone016
  - first:
      1: 100026
    second: Bone018
  - first:
      1: 100028
    second: Bone019
  - first:
      1: 100030
    second: Bone020
  - first:
      1: 100032
    second: Bone021
  - first:
      1: 100034
    second: Bone023
  - first:
      1: 100036
    second: Bone024
  - first:
      1: 100038
    second: Bone025
  - first:
      1: 100040
    second: Bone027
  - first:
      1: 100042
    second: Bone028
  - first:
      1: 100044
    second: Bone029
  - first:
      1: 100046
    second: Bone030
  - first:
      1: 100048
    second: Bone032
  - first:
      1: 100050
    second: Bone033
  - first:
      1: 100052
    second: Bone034
  - first:
      1: 100054
    second: CP_2011
  - first:
      1: 100056
    second: IK Chain001
  - first:
      1: 100058
    second: IK Chain002
  - first:
      1: 100060
    second: IK Chain003
  - first:
      1: 100062
    second: IK Chain004
  - first:
      1: 100064
    second: IK Chain005
  - first:
      1: 100066
    second: IK Chain006
  - first:
      1: 100068
    second: IK Chain007
  - first:
      1: 100070
    second: IK Chain008
  - first:
      1: 100072
    second: root
  - first:
      1: 100074
    second: Bip001
  - first:
      1: 100076
    second: Bip001 Head
  - first:
      1: 100078
    second: Bip001 L Hand
  - first:
      1: 100080
    second: Bip001 R Hand
  - first:
      1: 100082
    second: P-0025
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Bone001
  - first:
      4: 400004
    second: Bone002
  - first:
      4: 400006
    second: Bone003
  - first:
      4: 400008
    second: Bone005
  - first:
      4: 400010
    second: Bone007
  - first:
      4: 400012
    second: Bone008
  - first:
      4: 400014
    second: Bone009
  - first:
      4: 400016
    second: Bone011
  - first:
      4: 400018
    second: Bone012
  - first:
      4: 400020
    second: Bone013
  - first:
      4: 400022
    second: Bone015
  - first:
      4: 400024
    second: Bone016
  - first:
      4: 400026
    second: Bone018
  - first:
      4: 400028
    second: Bone019
  - first:
      4: 400030
    second: Bone020
  - first:
      4: 400032
    second: Bone021
  - first:
      4: 400034
    second: Bone023
  - first:
      4: 400036
    second: Bone024
  - first:
      4: 400038
    second: Bone025
  - first:
      4: 400040
    second: Bone027
  - first:
      4: 400042
    second: Bone028
  - first:
      4: 400044
    second: Bone029
  - first:
      4: 400046
    second: Bone030
  - first:
      4: 400048
    second: Bone032
  - first:
      4: 400050
    second: Bone033
  - first:
      4: 400052
    second: Bone034
  - first:
      4: 400054
    second: CP_2011
  - first:
      4: 400056
    second: IK Chain001
  - first:
      4: 400058
    second: IK Chain002
  - first:
      4: 400060
    second: IK Chain003
  - first:
      4: 400062
    second: IK Chain004
  - first:
      4: 400064
    second: IK Chain005
  - first:
      4: 400066
    second: IK Chain006
  - first:
      4: 400068
    second: IK Chain007
  - first:
      4: 400070
    second: IK Chain008
  - first:
      4: 400072
    second: root
  - first:
      4: 400074
    second: Bip001
  - first:
      4: 400076
    second: Bip001 Head
  - first:
      4: 400078
    second: Bip001 L Hand
  - first:
      4: 400080
    second: Bip001 R Hand
  - first:
      4: 400082
    second: P-0025
  - first:
      43: 4300000
    second: CP_2011
  - first:
      43: 4300002
    second: P-0025
  - first:
      74: 7400000
    second: idle
  - first:
      74: 7400002
    second: run
  - first:
      74: 7400004
    second: ready
  - first:
      74: 7400006
    second: hit
  - first:
      74: 7400008
    second: weak
  - first:
      74: 7400010
    second: atk1
  - first:
      74: 7400012
    second: win
  - first:
      74: 7400014
    second: die
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: CP_2011
  - first:
      137: 13700002
    second: P-0025
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: run
      takeName: Take 001
      internalID: 0
      firstFrame: 25
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ready
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: hit
      takeName: Take 001
      internalID: 0
      firstFrame: 50
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: weak
      takeName: Take 001
      internalID: 0
      firstFrame: 65
      lastFrame: 105
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk1
      takeName: Take 001
      internalID: 0
      firstFrame: 110
      lastFrame: 150
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: win
      takeName: Take 001
      internalID: 0
      firstFrame: 155
      lastFrame: 180
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: die
      takeName: Take 001
      internalID: 0
      firstFrame: 185
      lastFrame: 195
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bone018/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008
        weight: 1
      - path: Bip001/Bone001/Bone007/Bone008/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012
        weight: 1
      - path: Bip001/Bone001/Bone011/Bone012/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone015
        weight: 1
      - path: Bip001/Bone001/Bone015/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone019
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020
        weight: 1
      - path: Bip001/Bone001/Bone019/Bone020/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone023
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024
        weight: 1
      - path: Bip001/Bone001/Bone023/Bone024/Bone025
        weight: 1
      - path: Bip001/Bone001/Bone027
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029
        weight: 1
      - path: Bip001/Bone001/Bone027/Bone033/Bone032/Bone034/Bone028/Bone029/Bone030
        weight: 1
      - path: P-0025
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: f30d98e219ece994791ff7567e636741,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
