---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---屬性分頁 稱號子分頁
---@class RoleAttribute_Page_Title
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.6.12
RoleAttribute_Page_Title = {}
local this = RoleAttribute_Page_Title
---是否顯示篩選面板
local m_ShowTitleFilterPanel = true
---單一稱號 稱號傭有加成最多有幾個
local SINGLE_TITLE_EFFECT_AMOUNT = 3

---稱號類別篩選面板 類別
ETITLESUBPAGETYPE = 
{
    ---顯示全部
    ShowAll = 1,
    ---顯示一般
    ShowNormal = 2,
    ---顯示活動
    ShowActivity = 3,
    ---顯示特殊
    ShowSpecial = 4,
    ---顯示其他
    ShowOther = 5,
}

---稱號類別篩選面板要用的文字
local TableTitlePageTypeTextID = 
{
    ---顯示全部
    [ETITLESUBPAGETYPE.ShowAll] = 319,
    ---顯示一般
    [ETITLESUBPAGETYPE.ShowNormal] = 510100,
    ---顯示活動
    [ETITLESUBPAGETYPE.ShowActivity] = 510101,
    ---顯示特殊
    [ETITLESUBPAGETYPE.ShowSpecial] = 510102,
    ---顯示其他
    [ETITLESUBPAGETYPE.ShowOther] = 510103,
}
---分類後的所有稱號資訊
this.m_TitleTableSorted = {}

---Overview頁面 目前顯示的全部
this.m_CurrentOverviewTitleTable = {}

---稱號 有  權限時 TMP Style
local TITLE_CAN_USE = "PO" 

---稱號 沒有 權限時 TMP Style
local TITLE_CANNOT_USE = "Gray" 

---Overview頁面 最後一次點點擊的 稱號串表ID
this.m_LastClickTitleID = 0

---單一稱號資訊面板按鍵文字ID
--- 未獲得
local NO_QUALIFY = 510114
---已獲得
local IS_QUALIFIED = 510112
---已裝備
local IS_EQUIPED = 510113

---裝備稱號 點擊時間限制 (兩次有效的點擊必須間隔0.5秒)
local CLICK_PERIOD = 0.5
---剩多久可以點擊 裝備稱號
local m_ClickWaitTime = 0

---目前的TitleOverviewPageType
local m_CurrentTitleGrop = 1

local m_OverviewTypeBtn_Table = {}
---稱號overview區域 scrollview的item們
local m_ItemTable = {}
---稱號overview區域 目前會生成的 重複利用數量
local m_Scrollview_TitleOverView_Amount = 13 

---初始化選稱號篩面板裡面的按鍵
local function InitialOverviewTypeBtn()

    for i = 1, table.Count(ETITLESUBPAGETYPE) do
        ---要記下來的基礎屬性物件
        local _ItemObject = nil
        -- 第一個 用本人
        if i == 1 then
            _ItemObject = this.m_Btn_Fillter_SelectType
        -- 其他 產生新的物件
        else
            _ItemObject = this.m_Btn_Fillter_SelectType:Instantiate(this.m_Btn_Fillter_SelectType)
        end

        _ItemObject:SetParent(this.m_TitleGropFilterPanel.transform)

        ---只宣告按鍵 按鍵方法由物件上層的GroupButton設定
        local _ButtonEx = Button.New(_ItemObject)
       
        ---設定按鍵文字
        _ButtonEx:SetText(TextData.Get(TableTitlePageTypeTextID[i]))
        
        local _BtnInfo = {}
        _BtnInfo.m_GameObject = _ItemObject
        _BtnInfo.m_TitleGroupIndex = i
        table.insert(m_OverviewTypeBtn_Table,_BtnInfo)
    end

end

---初始化
function RoleAttribute_Page_Title.Init(iController)

    --設定RolAttribute_Controller
    this.m_Controller = iController.m_UIController
    --設定ViewRef
    this.m_ViewRef = iController.m_ViewRef
    --頭銜介面本體 &DetailAttrPanel
    this.m_Page_GameObject = this.m_ViewRef.m_Dic_Trans:Get("&TitlePage").gameObject
    
    RoleAttribute_Page_Title.RenewOverviewData(ETITLESUBPAGETYPE.ShowAll)

    ---物件 - Overview區域 稱號按鍵被點擊時的選擇框 &TitleOverviewSelectOutline
    --this.m_GObj_TitleOverviewSelectOutline = this.m_ViewRef.m_Dic_Trans:Get("&TitleOverviewSelectOutline").gameObject

    ---稱號 Overview 用的ScrollView 元件單位
    this.m_TitleOverViewUnit = this.m_ViewRef.m_Dic_Trans:Get("&TitleOverViewUnit").gameObject

    ---稱號 Overview 用的ScrollView 項目掛載的 parent
    this.m_ContentTitleOverviewList = this.m_ViewRef.m_Dic_Trans:Get("&ContentTitleOverview")
    
    ---稱號 Overview 用的UIScrollView 掛腳本的物件
    this.m_ScrollView_TitleOverview_Obj = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_TitleOverview").gameObject
    --ScrollView 腳本 本身初始化
    this.m_ScrollView_TitleOverview = ScrollView.Init(this.m_ScrollView_TitleOverview_Obj ,false,this.m_TitleOverViewUnit, this.TitleOverView_ReturnCount,this.TitleOverView_AfterReuseItemInit,this.TitleOverView_AfterReuseItemIndexUpdate,true)
    
    ---按鍵 - 開啟TitleOverView要顯示那些title 的選擇小介面
    this.m_Btn_Fillter_TitlePanel = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Fillter_TitlePanel").gameObject
    Button.AddListener(this.m_Btn_Fillter_TitlePanel, EventTriggerType.PointerDown, 
                function() 
                    m_ShowTitleFilterPanel = not m_ShowTitleFilterPanel
                    RoleAttribute_Page_Title.ShowTitleOverView_SelectPanel(m_ShowTitleFilterPanel)
                end)
    ---物件 - TitleOverView要顯示那些title 的選擇小介面
    this.m_TitleGropFilterPanel = this.m_ViewRef.m_Dic_Trans:Get("&TitleGropFilterPanel").gameObject
    
    ---按鍵 - TitleOverView要顯示那些title 的選擇小介面裡面的按鍵
    this.m_Btn_Fillter_SelectType = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Fillter_SelectType")

    ---強制清空 m_OverviewTypeBtn_Table 避免切換語言時有空資訊 無法開啟
    m_OverviewTypeBtn_Table = {}
    InitialOverviewTypeBtn()

    this.m_GroupBtn_FilterPanel = GroupButton.New(this.m_TitleGropFilterPanel)
    this.m_GroupBtn_FilterPanel:ReacquireAllButtonsUnderGroup()

    for i = 1, this.m_GroupBtn_FilterPanel.m_UIGroupBtnCtrl.ButtonCount do

        GroupButton.AddListenerByIndex(this.m_GroupBtn_FilterPanel,i,EventTriggerType.PointerClick,function()
            
            RoleAttribute_Page_Title.RenewOverviewData(i)
            m_CurrentTitleGrop = i
            ScrollView.UpdateToFirst(this.m_ScrollView_TitleOverview)

            m_ShowTitleFilterPanel = not m_ShowTitleFilterPanel
            this.m_TitleGropFilterPanel:SetActive(false)
        end)
    end


    ---按鍵 - 詳細資訊 開啟詳細資訊面板的按鍵
    this.m_Btn_CallDetailBtn_Title = this.m_ViewRef.m_Dic_Trans:Get("&Btn_CallDetailBtn_Title").gameObject
    Button.AddListener(this.m_Btn_CallDetailBtn_Title, EventTriggerType.PointerDown, 
                function() 
                    RoleAttribute_Page_Title.ShowDetailInfo_AllPanel(true)
                end)

    ---按鍵 - 詳細資訊 關閉詳細資訊面板的按鍵
    this.m_Button_Close_DetailInfo_All = this.m_ViewRef.m_Dic_Trans:Get("&Button_Close_DetailInfo_All").gameObject
    Button.AddListener(this.m_Button_Close_DetailInfo_All, EventTriggerType.PointerDown, 
                    function() 
                    RoleAttribute_Page_Title.ShowDetailInfo_AllPanel(false)
                end)

    ---物件 - 稱號詳細資訊面板
    this.m_Gobj_DetailInfo_All = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_DetailInfo_All").gameObject


    ---稱號累積效果資訊 用的ScrollView 元件單位
    this.m_TitleEffectUnit = this.m_ViewRef.m_Dic_Trans:Get("&TitleEffectUnit").gameObject

    ---稱號累積效果資訊 用的ScrollView 項目掛載的 parent
    this.m_ContentTitleEffectList = this.m_ViewRef.m_Dic_Trans:Get("&ContentTitleDetailInfoList")
    
    ---稱號累積效果資訊 用的UIScrollView 掛腳本的物件
    this.m_ScrollView_TitleEffect_Obj = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_TitleDetailInfo").gameObject
    --ScrollView 腳本 本身初始化
    this.m_ScrollView_TitleEffect = ScrollView.Init(this.m_ScrollView_TitleEffect_Obj,false,this.m_TitleEffectUnit, this.TitleEffect_ReturnCount,this.TitleEffect_AfterReuseItemInit,this.TitleEffect_AfterReuseItemIndexUpdate,true)

    ---讓稱號累積效果的scrollview 回到最上面的按鍵
    this.m_Btn_ScrollToTop_TitleEffect = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ScrollToTop_TitleEffect").gameObject
    Button.AddListener(this.m_Btn_ScrollToTop_TitleEffect, EventTriggerType.PointerClick, 
                             function() 
                                RoleAttribute_Page_Title.ClickToTopItem_TitleEffect()
                             end)

    ---物件 - 單一稱號面板( 顯示效果以及取得方法的)
    this.m_Gobj_DetailInfo_Single = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_DetailInfo_Single").gameObject

    ---物件 - 單一稱號面板 (各條效果物件)
    this.m_DetailInfo_Single_Table = {}
    local _ObjNamePre = "&TitleEffect_Single"
    for i = 1, SINGLE_TITLE_EFFECT_AMOUNT do
        local _ObjName = _ObjNamePre..i
        this.m_DetailInfo_Single_Table[i] = {}
        if this.m_ViewRef.m_Dic_Trans:Get(_ObjName) == nil  then
        end
        this.m_DetailInfo_Single_Table[i].m_GObj_EffectSet = this.m_ViewRef.m_Dic_Trans:Get(_ObjName).gameObject
    end

    ---TMP - 單一稱號面板 取得方法的TMP  --&TMP_GetMethodContent
    this.m_TMP_GetMethodContent = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_GetMethodContent")

     ---TMP - 單一稱號面板 取得方法的TMP  --&TMP_GetMethodContent
     this.m_TMP_TitleName_Single = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_Title_DeailInfo_Single")
    
    ---按鍵 - 單一稱號面板 確定使用當前面板顯示的 稱號
    this.m_Button_ConfirmEquipTitle = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_ConfirmEquipTitle").gameObject)
    Button.AddListener(this.m_Button_ConfirmEquipTitle, EventTriggerType.PointerDown, 
                    function() 
                    if m_ClickWaitTime > 0 then
                        MessageMgr.AddCenterMsg(false, TextData.Get(9729))
                    else
                        ---協定 3-12-2 送出換稱號請求
                        SendProtocol_003._012(2,this.m_LastClickTitleID)
                        RoleAttribute_Page_Title.SetCDTimeOfTitleBtn_ConfirmEquipTitle()
                    end
                    
                end)

    ---是否需要重新跟PlayerData_Title要稱號資訊
    this.m_NeedRenewTitleCondition = false
   
    ---默認點擊第一項 並將 m_ShowTitleFilterPanel 切換成false
    GroupButton.OnPointerClickByIndex(this.m_GroupBtn_FilterPanel,ETITLESUBPAGETYPE.ShowAll)
end

---Update
function RoleAttribute_Page_Title.Update()

    ---稱號累加效果總攬 回到頂端按鍵是否顯示
    if this.m_Gobj_DetailInfo_All.activeSelf then
        this.m_Btn_ScrollToTop_TitleEffect:SetActive(this.m_ContentTitleEffectList.localPosition.y > 0.1)
    end

    ---單一稱號效果介面 還需要多久可以點擊按鍵
    if m_ClickWaitTime > 0 then
        m_ClickWaitTime = m_ClickWaitTime - HEMTimeMgr.m_DeltaTime
    end

    ---是否要跟PlayerData_Title 重新要稱號資訊
    if this.m_NeedRenewTitleCondition == true  then
        this.m_NeedRenewTitleCondition = false
        ScrollView.Update(this.m_ScrollView_TitleOverview)
        RoleAttribute_Page_Title.CloseAllContentTitleEffectList()
        ScrollView.Update(this.m_ScrollView_TitleEffect)
        RoleAttribute_Page_Title.RenewDetailInfo_Single()
    end  
end

---屬性-稱號介面開啟方法
--- iParam[1] 想要要聚焦的特定稱號 可為nil
function RoleAttribute_Page_Title.Open(iParam)
    
    ---顯示介面主體
    this.m_Page_GameObject:SetActive(true)

    if iParam~=nil then
        ---是否需要聚焦點擊某個特定titleID 
        local _TitleID = iParam[1]
        if _TitleID ~= nil then
            ---想要開啟時順便開特定稱號時 將資料篩選設定成顯示全部
            RoleAttribute_Page_Title.RenewOverviewData(ETITLESUBPAGETYPE.ShowAll)
            ---要聚焦的稱號 在table中的index 
            local _FocusIndex = RoleAttribute_Page_Title.GetTableIndexByTitleID(_TitleID)
            ---要聚焦的稱號 物件
            local _FocusObj
            ---如果有index 嘗試找物件
            if _FocusIndex ~= nil then
                _FocusObj = ScrollView.MoveToIndex(this.m_ScrollView_TitleOverview,_FocusIndex)
            end
             ---如果有物件 觸發對應按鍵點擊方法
            if _FocusObj ~= nil  then
                Button.OnPointerClick(_FocusObj.m_GObj)
            end
        end
    end
    
    
    ---關閉裝備介面
    UIMgr.Close(Equipment_Controller)

end

---關閉稱號面板 同時關閉各個附屬面板
function RoleAttribute_Page_Title.Close()
    this.m_Page_GameObject:SetActive(false)
    RoleAttribute_Page_Title.ShowTitleOverView_SelectPanel(false)
    RoleAttribute_Page_Title.ShowDetailInfo_Single(false)
    RoleAttribute_Page_Title.ShowDetailInfo_AllPanel(false)
end


---稱號Overview 的Scrollview
function RoleAttribute_Page_Title.TitleOverView_ReturnCount()
    return table.Count(this.m_CurrentOverviewTitleTable)
end

function RoleAttribute_Page_Title.TitleOverView_AfterReuseItemInit(iItem,iRowIdx)
    
    if iItem == nil  or iRowIdx > RoleAttribute_Page_Title.TitleOverView_ReturnCount() then
        return
    end
    
    table.insert(m_ItemTable,iItem)
    iItem.m_Button = Button.New(iItem.m_GObj.transform)

    if iRowIdx == m_Scrollview_TitleOverView_Amount then
        this.m_GroupBtns_Title = GroupButton.New(this.m_ContentTitleOverviewList) 
        this.m_GroupBtns_Title:ReacquireAllButtonsUnderGroup()

        for i = 1, this.m_GroupBtns_Title.m_UIGroupBtnCtrl.ButtonCount do
		    GroupButton.AddListenerByIndex(this.m_GroupBtns_Title,i,EventTriggerType.PointerClick,function()
                
                local _TitleData =  TitleData.Get ( this.m_CurrentOverviewTitleTable[m_ItemTable[i].m_Index])
                ---開啟並顯示稱號訊息
                this.m_LastClickTitleID = _TitleData.m_Idx
                RoleAttribute_Page_Title.ShowDetailInfo_Single(true)
                RoleAttribute_Page_Title.RenewDetailInfo_Single()
		    end)
	    end
    end
end

function RoleAttribute_Page_Title.TitleOverView_AfterReuseItemIndexUpdate(iItem,iRowIdx)
    if iItem == nil  or iRowIdx > RoleAttribute_Page_Title.TitleOverView_ReturnCount() then
        return
    end

    RoleAttribute_Page_Title.SetTitleItem(iItem,iRowIdx)
    iItem.m_GObj:SetActive(iRowIdx <= this.TitleOverView_ReturnCount())
end

---開啟/關閉 TitleOverView要顯示那些title 的選擇小介面
---@param iShowPanel bool 是否顯示面板
function RoleAttribute_Page_Title.ShowTitleOverView_SelectPanel(iShowPanel)
    this.m_TitleGropFilterPanel:SetActive(iShowPanel)
    m_ShowTitleFilterPanel = iShowPanel
end

---設定 稱號overview 區域 各位置的顯示
function RoleAttribute_Page_Title.SetTitleItem(iItem,iRowIdx)

    local _Title= {}
    local _TitleData =  TitleData.Get (this.m_CurrentOverviewTitleTable[iRowIdx])
    local _TitleCondition = PlayerData_Title.GetTitleCondition(_TitleData.m_Idx)

    _Title.gameObject = iItem.m_GObj
    --Image 背景
    _Title.m_Img_BackGround =  _Title.gameObject.transform:Find( "Img_BackGround").gameObject
    --TMP稱號名稱
    _Title.m_TMP_TitleName =  _Title.gameObject.transform:Find( "TMP_TitleName").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
    ---TMP稱號是否裝備中
    _Title.m_GObj_IsTitleEuiped =  _Title.gameObject.transform:Find( "TMP_IsTitleEuiped").gameObject

    ---是否擁有稱號
    local _IsTitleQualified = _TitleCondition.m_IsQualified
    ---是否裝備稱號
    local _IsTitleEquiped = _TitleCondition.m_IsEquiped
    ---不同狀況要套不同TMP style
    local _StyleString = _IsTitleQualified and TITLE_CAN_USE or TITLE_CANNOT_USE

    local _TitleNameString = TextData.Get(_TitleData.m_TextID_Name)

    _Title.m_TMP_TitleName.text = GString.StringWithStyle(_TitleNameString,_StyleString)

    _Title.m_GObj_IsTitleEuiped:SetActive(_IsTitleEquiped)

    iItem.m_Button:SetSelect(this.m_LastClickTitleID == _TitleData.m_Idx)
end

---稱號詳細資訊 的Scrollview
function RoleAttribute_Page_Title.TitleEffect_ReturnCount()
    local _EnhanceCount = table.Count(PlayerData_Title.m_TitleEnhanceAbility_Index)
    return (_EnhanceCount > 0) and _EnhanceCount or 1
end

function RoleAttribute_Page_Title.TitleEffect_AfterReuseItemInit(iItem,iRowIdx)
   
    if iItem == nil  or iRowIdx > RoleAttribute_Page_Title.TitleEffect_ReturnCount() then
        return
    end
    RoleAttribute_Page_Title.SetTitleEffectItem(iItem,iRowIdx)
end

function RoleAttribute_Page_Title.TitleEffect_AfterReuseItemIndexUpdate(iItem,iRowIdx)
    
    if iItem == nil  or iRowIdx > RoleAttribute_Page_Title.TitleEffect_ReturnCount() then
        return
    end
    RoleAttribute_Page_Title.SetTitleEffectItem(iItem,iRowIdx)
    iItem.m_GObj:SetActive(iRowIdx <= this.TitleEffect_ReturnCount())

end

---設定稱號效果的scrollview
function RoleAttribute_Page_Title.SetTitleEffectItem(iItem,iRowIdx)

    local _TitleEffect= {}
    local _TitleEffectData =  PlayerData_Title.GetTitlelEnhanceFromIndex(iRowIdx)

    _TitleEffect.gameObject = iItem.m_GObj
    --TMP稱號加成效果名稱
    _TitleEffect.m_TMP_TitleName =  _TitleEffect.gameObject.transform:Find( "TMP_EffectName").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
    ---TMP稱號加成數值
    _TitleEffect.m_TMP_EffectValue =  _TitleEffect.gameObject.transform:Find( "TMP_EffectValue").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

    ---特殊狀況 如果都沒有加成 只顯示一個無
    if _TitleEffectData == nil  then
        _TitleEffect.m_TMP_TitleName.text = TextData.Get(510111)
        _TitleEffect.m_TMP_EffectValue.text = ""
        return
    end

    local _AttrString, _ValueString = GValue.AttributeToString(_TitleEffectData.m_EAttributeType , _TitleEffectData.m_Value,false)

    _TitleEffect.m_TMP_TitleName.text = _AttrString
    _TitleEffect.m_TMP_EffectValue.text = _ValueString
           
end

---稱號累積效果的scrollview 回到最上面
function RoleAttribute_Page_Title.ClickToTopItem_TitleEffect()
    ScrollView.UpdateToFirst(this.m_ScrollView_TitleEffect)
end


---開啟/關閉 詳細資訊面板 
---@param iShowPanel bool 是否顯示面板
function RoleAttribute_Page_Title.ShowDetailInfo_AllPanel(iShowPanel)
    this.m_Gobj_DetailInfo_All:SetActive(iShowPanel)
end

---開啟/關閉 單一稱號資訊面板
---@param iShowPanel bool 是否顯示面板
function RoleAttribute_Page_Title.ShowDetailInfo_Single(iShowPanel)
    this.m_Gobj_DetailInfo_Single:SetActive(iShowPanel)
end

---刷新單一稱號資訊面板 顯示資訊
function RoleAttribute_Page_Title.RenewDetailInfo_Single()
    local _TitleData = TitleData.Get(this.m_LastClickTitleID)

    if _TitleData == nil then
        RoleAttribute_Page_Title.ShowDetailInfo_Single(false)
        return 
    end

    this.m_TMP_TitleName_Single.text = TextData.Get(_TitleData.m_TextID_Name)

    for i = 1, table.Count(this.m_DetailInfo_Single_Table) do

        local _EffectSet = {}
        _EffectSet.gameObject = this.m_DetailInfo_Single_Table[i].m_GObj_EffectSet
        _EffectSet.m_TMP_EffectName =  _EffectSet.gameObject.transform:Find( "TMP_EffectName").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        _EffectSet.m_TMP_EffectValue =  _EffectSet.gameObject.transform:Find( "TMP_EffectValue").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

        if _TitleData.m_GetPropertiesAy[i].m_Attr_ID == 0 and i == 1 then
            ---如果第一個效果ID 就為0 表示此稱號沒有任何效果
            _EffectSet.gameObject:SetActive(true)
            _EffectSet.m_TMP_EffectName.text = TextData.Get(510111)

            _EffectSet.m_TMP_EffectValue.gameObject:SetActive(false)
            
        elseif _TitleData.m_GetPropertiesAy[i].m_Attr_ID == 0 then
            _EffectSet.gameObject:SetActive(false)
        else
            local _AttrString, _ValueString = GValue.AttributeToString(_TitleData.m_GetPropertiesAy[i].m_Attr_ID , _TitleData.m_GetPropertiesAy[i].m_Value,false)
            _EffectSet.m_TMP_EffectName.text = _AttrString
            _EffectSet.m_TMP_EffectValue.text = _ValueString

            _EffectSet.gameObject:SetActive(true)
            _EffectSet.m_TMP_EffectValue.gameObject:SetActive(true)
        end
    end

    this.m_TMP_GetMethodContent.text = TextData.Get(_TitleData.m_TexIDt_Describe)

    local _TitleCondition = PlayerData_Title.GetTitleCondition(_TitleData.m_Idx)
    ---是否擁有稱號
    local _IsTitleQualified = _TitleCondition.m_IsQualified
    ---是否裝備稱號
    local _IsTitleEquiped = _TitleCondition.m_IsEquiped

    ---根據權限/是否裝備 設定按鍵文字/顯示
    if _IsTitleEquiped then
        Button.SetText(this.m_Button_ConfirmEquipTitle,TextData.Get(IS_EQUIPED))
        this.m_Button_ConfirmEquipTitle:SetEnable()

    elseif _IsTitleQualified then
        Button.SetText(this.m_Button_ConfirmEquipTitle,TextData.Get(IS_QUALIFIED))
        this.m_Button_ConfirmEquipTitle:SetEnable()
    else
        Button.SetText(this.m_Button_ConfirmEquipTitle,TextData.Get(NO_QUALIFY))
        this.m_Button_ConfirmEquipTitle:SetDisable()
    end

end



---刷新Overview要顯示的稱號資料 沒有填寫類別是默認顯示全部
---@param iETITLESUBPAGETYPE ETITLESUBPAGETYPE 要顯示的稱號分頁類別
function RoleAttribute_Page_Title.RenewOverviewData(iETITLESUBPAGETYPE)

    this.m_CurrentOverviewTitleTable = {}

    for key, value in pairs(PlayerData_Title.m_PlayerTitleCondition) do
        
        if iETITLESUBPAGETYPE == ETITLESUBPAGETYPE.ShowAll or iETITLESUBPAGETYPE == nil then
            table.insert(this.m_CurrentOverviewTitleTable , value.m_Idx)
        elseif iETITLESUBPAGETYPE == ETITLESUBPAGETYPE.ShowNormal then
            if value.m_UIPage == ETitlePageGroup.Normal then
                table.insert(this.m_CurrentOverviewTitleTable , value.m_Idx)
            end    
        elseif iETITLESUBPAGETYPE == ETITLESUBPAGETYPE.ShowActivity then
            if value.m_UIPage == ETitlePageGroup.Activity then
                table.insert(this.m_CurrentOverviewTitleTable , value.m_Idx)
            end    
        elseif iETITLESUBPAGETYPE == ETITLESUBPAGETYPE.ShowSpecial then
            if value.m_UIPage == ETitlePageGroup.Special then
                table.insert(this.m_CurrentOverviewTitleTable , value.m_Idx)
            end    
        elseif iETITLESUBPAGETYPE == ETITLESUBPAGETYPE.ShowOther then
            if value.m_UIPage == ETitlePageGroup.Other then
                table.insert(this.m_CurrentOverviewTitleTable , value.m_Idx)
            end    
        end
    end
end

---透過titleID 取得並回傳在table的index 
---@param iTitleID number 稱號的串表編號
---@return _FocusTableIndex number 在顯示table中排的順位
function RoleAttribute_Page_Title.GetTableIndexByTitleID(iTitleID)

    local _FocusTableIndex = 1
    for key, value in pairs(this.m_CurrentOverviewTitleTable) do
        if value == iTitleID then
            _FocusTableIndex = key
        end
    end

    return _FocusTableIndex
end


---設定裝備稱號 間隔時間
function RoleAttribute_Page_Title.SetCDTimeOfTitleBtn_ConfirmEquipTitle()
    m_ClickWaitTime = CLICK_PERIOD
end


---是否要重新跟PlayerData_Title 重新要資料 
function RoleAttribute_Page_Title.SetReAskData()
    this.m_NeedRenewTitleCondition = true
end

---關閉ContentList所有的顯示 避免玩家的加成變少時沒有刷新後面物件並關閉
function RoleAttribute_Page_Title.CloseAllContentTitleEffectList()
    for i = 1, this.m_ContentTitleEffectList.childCount do
        this.m_ContentTitleEffectList:GetChild(i-1).gameObject:SetActive(false)
    end
end
