﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class GizmosDebugWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(GizmosDebug), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetDrawLine", SetDrawLine);
		<PERSON><PERSON>unction("SetDrawSetting", SetDrawSetting);
		<PERSON><PERSON>unction("SetDrawArea", SetDrawArea);
		<PERSON><PERSON>RegFunction("GizmosDrawLine", GizmosDrawLine);
		<PERSON><PERSON>RegFunction("GizmosDrawWireSphere", GizmosDrawWireSphere);
		<PERSON><PERSON>RegFunction("GizmosDrawWireRig", GizmosDrawWireRig);
		<PERSON><PERSON>RegFunction("GizmosDrawWireCube", GizmosDrawWireCube);
		L.RegFunction("GizmosDrawWireRect", GizmosDrawWireRect);
		<PERSON><PERSON>Function("GizmoDrawFanRig", GizmoDrawFanRig);
		<PERSON><PERSON>un<PERSON>("__eq", op_Equality);
		<PERSON><PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("m_width", get_m_width, set_m_width);
		L.RegVar("m_Duration", get_m_Duration, set_m_Duration);
		L.RegVar("m_RigTheta", get_m_RigTheta, set_m_RigTheta);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDrawLine(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetDrawLine(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDrawSetting(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			obj.SetDrawSetting(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDrawArea(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
				obj.SetDrawArea(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				obj.SetDrawArea(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 6)
			{
				GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
				UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 4);
				float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
				UnityEngine.Transform arg4 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 6);
				obj.SetDrawArea(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: GizmosDebug.SetDrawArea");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmosDrawLine(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			obj.GizmosDrawLine(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmosDrawWireSphere(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			obj.GizmosDrawWireSphere(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmosDrawWireRig(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			GizmosDebug.WireData arg0 = (GizmosDebug.WireData)ToLua.CheckObject<GizmosDebug.WireData>(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.GizmosDrawWireRig(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmosDrawWireCube(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			obj.GizmosDrawWireCube(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmosDrawWireRect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			GizmosDebug.WireData arg0 = (GizmosDebug.WireData)ToLua.CheckObject<GizmosDebug.WireData>(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.GizmosDrawWireRect(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GizmoDrawFanRig(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			GizmosDebug obj = (GizmosDebug)ToLua.CheckObject<GizmosDebug>(L, 1);
			GizmosDebug.WireData arg0 = (GizmosDebug.WireData)ToLua.CheckObject<GizmosDebug.WireData>(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.GizmoDrawFanRig(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			float ret = obj.m_width;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			float ret = obj.m_Duration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_RigTheta(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			int ret = obj.m_RigTheta;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_RigTheta on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_width = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Duration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.m_Duration = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Duration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_RigTheta(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			GizmosDebug obj = (GizmosDebug)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.m_RigTheta = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_RigTheta on a nil value");
		}
	}
}

