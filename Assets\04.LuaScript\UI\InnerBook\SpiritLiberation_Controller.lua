﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功系統-靈脈覺醒頁面
---@class SpiritLiberation_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.21
SpiritLiberation_Controller = {}
local this = SpiritLiberation_Controller

---靈氣解放字串
local _SpiritLiberationStr = 20112028

---未解鎖字串
local _NotUnlockStr = 20111038

---取消運行字串
local _CancelRunStr = 21002006

local _MethodCount = 4

---初始化
function SpiritLiberation_Controller.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --靈氣累積值
    this.m_Text_SpiritValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Liberation_SpiritValue")

    --靈氣解放元件
    this.m_Group_Spirit = this.m_ViewRef.m_Dic_Trans:Get("&Group_SpiritLiberation")
    this.m_Trans_Spirit = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Spirit")
    this.m_SpiritData = {}
    for i = 1, _MethodCount do
        local _SpiritObject = nil
        if i == 1 then
            _SpiritObject =  this.m_Trans_Spirit
        else
            _SpiritObject =  this.m_Trans_Spirit:Instantiate( this.m_Trans_Spirit )
            _SpiritObject:SetParent(this.m_Group_Spirit)
        end
        _SpiritObject:SetSiblingIndex(i - 1)

        local _SpiritInfo = {}
        _SpiritInfo.m_GameObject = _SpiritObject.gameObject
        _SpiritInfo.m_Trans_Panel = _SpiritObject:Find("Panel_Spirit").transform
        local _Image_Skill = _SpiritInfo.m_Trans_Panel:Find("Image_Skill").transform
        _SpiritInfo.m_SkillIcon = IconMgr.NewInnerSkillIcon(0, _Image_Skill, 108)
        _SpiritInfo.m_Text_SkillName = _SpiritInfo.m_Trans_Panel:Find("Text_SkillName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Text_SkillStateName = _SpiritInfo.m_Trans_Panel:Find("Text_SkillStateName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Text_Effect = _SpiritInfo.m_Trans_Panel:Find("Text_Effect").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Group_Effect = {}
        for k = 1, 2 do
            _SpiritInfo.m_Group_Effect[k] = {}
            _SpiritInfo.m_Group_Effect[k].m_Text_EffectName = _SpiritInfo.m_Trans_Panel:Find("Group_Effect" .. k .. "/Text_EffectName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
            _SpiritInfo.m_Group_Effect[k].m_Image_EffectValueBG = _SpiritInfo.m_Trans_Panel:Find("Group_Effect" .. k .. "/Image_ValueBG").gameObject:GetComponent(typeof( UIRenderChangeColor ))
            _SpiritInfo.m_Group_Effect[k].m_Text_EffectValue = _SpiritInfo.m_Trans_Panel:Find("Group_Effect" .. k .. "/Image_ValueBG/Text_EffectValue").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        end
        _SpiritInfo.m_Text_SpiritCost = _SpiritInfo.m_Trans_Panel:Find("Text_SpiritCost").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Text_SpiritCostValue = _SpiritInfo.m_Trans_Panel:Find("Image_ValueBG/Text_SpiritCostValue").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Text_SpiritCostValue_Render = _SpiritInfo.m_Trans_Panel:Find("Image_ValueBG/Text_SpiritCostValue").gameObject:GetComponent(typeof(UIRenderTMPTextChangeStyle))
        _SpiritInfo.m_Panel_Lock = _SpiritObject:Find("Panel_Lock").gameObject
        _SpiritInfo.m_Text_Lock = _SpiritInfo.m_Panel_Lock.transform:Find("Text_Lock").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _SpiritInfo.m_Btn_Liberation = Button.New(_SpiritObject:Find("Btn_Liberation"))
        _SpiritInfo.m_Btn_Liberation:AddListener(EventTriggerType.PointerClick, function() SpiritLiberation_Controller.OnClick_Liberation(i) end)

        table.insert(this.m_SpiritData, _SpiritInfo)
    end

    SpiritLiberation_Controller.SetEffectInfo()
end

function SpiritLiberation_Controller.Open()
    SpiritLiberation_Controller.UpdateEffectInfo()
    GStateObserverManager.Register(EStateObserver.UpdateSpiritLiberation, this)
end

function SixSpirit_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.UpdateSpiritLiberation, this)
end

---設定靈氣效果資訊
function SpiritLiberation_Controller.SetEffectInfo()
    for k, v in pairs(this.m_SpiritData) do
        local _Data = InnerBookData.GetInnerBookDataByIdx(k)
        _Data = _Data[InnerBook_Model.m_MethodMaxStep]

        v.m_SkillIcon:RefreshIcon(_Data.m_WugongID)
        v.m_Text_SkillName.text = TextData.Get(_Data.m_MethodString)
        v.m_Text_SkillStateName.text = TextData.Get(_Data.m_ChapterString)
        v.m_Text_Effect.text = TextData.Get(21002152)
        for _k, _v in pairs(_Data.m_SpiritData) do
            local _StatusData = StatusNameData.GetStatusNameDataByIdx(_v.m_AttrFix)
            v.m_Group_Effect[_k].m_Text_EffectName.text = TextData.Get(_StatusData.m_TextIdx)
            v.m_Group_Effect[_k].m_Text_EffectValue.text = _v.m_AttrValue
        end
        v.m_Text_SpiritCost.text = TextData.Get(21002153)
        v.m_Text_SpiritCostValue.text = _Data.m_HourCost
    end
end

---更新靈氣效果資訊
function SpiritLiberation_Controller.UpdateEffectInfo()
    local _SpiritAccStr = GString.Format(TextData.Get(10101011), InnerBook_Model.m_SpiritValue, InnerBook_Model.m_MaxSpiritValue)
    this.m_Text_SpiritValue.text = _SpiritAccStr

    for k, v in pairs(this.m_SpiritData) do
        local _Data = InnerBookData.GetInnerBookDataByIdx(k)
        _Data = _Data[InnerBook_Model.m_MethodMaxStep]
        
        if InnerBook_Model.m_SpiritValue >= _Data.m_HourCost then
            v.m_Text_SpiritCostValue_Render:Trigger(ESelectionState.Normal)
        else
            v.m_Text_SpiritCostValue_Render:Trigger(ESelectionState.Selected)
        end

        if InnerBook_Model.m_MethodData[k].m_Step == InnerBook_Model.m_MethodMaxStep then
            if InnerBook_Model.m_CurrentLiberationSpirit == k then
                for _k, _v in pairs(v.m_Group_Effect) do
                    _v.m_Image_EffectValueBG:Trigger(ESelectionState.Selected)
                end
                v.m_Btn_Liberation:ChangeStateTransitionGroup(0)
                v.m_Btn_Liberation:SetText(TextData.Get(_CancelRunStr))
            else
                for _k, _v in pairs(v.m_Group_Effect) do
                    _v.m_Image_EffectValueBG:Trigger(ESelectionState.Normal)
                end
                v.m_Btn_Liberation:ChangeStateTransitionGroup(1)
                v.m_Btn_Liberation:SetText(TextData.Get(_SpiritLiberationStr))
            end
            v.m_Panel_Lock:SetActive(false)
            v.m_Btn_Liberation:SetEnable()
        else
            v.m_Panel_Lock:SetActive(true)
            v.m_Btn_Liberation:SetDisable(false)
            local _MethodName = GString.Format(TextData.Get(10101014), TextData.Get(_Data.m_MethodString), TextData.Get(_Data.m_ChapterString))
            v.m_Text_Lock.text = GString.StringWithStyle(_MethodName, InnerBook_Model.m_NormalStyle) .. "\n" .. TextData.Get(21002151)
            v.m_Btn_Liberation:SetText(TextData.Get(_NotUnlockStr))
        end
    end
end

---點擊靈氣解放
function SpiritLiberation_Controller.OnClick_Liberation(iIdx)
    if InnerBook_Model.m_CurrentLiberationSpirit ~= iIdx then
        local _Data = InnerBookData.GetInnerBookDataByIdx(iIdx)
        _Data = _Data[InnerBook_Model.m_MethodMaxStep]
        CommonQueryMgr.AddNewInform(212, {}, {TextData.Get(_Data.m_MethodString), TextData.Get(_Data.m_ChapterString)},
        function()
            SendProtocol_003._017(10, {m_MethodID = iIdx})
        end)
    else
        SendProtocol_003._017(10, {m_MethodID = 0})
    end
end

---狀態改變通知
function SpiritLiberation_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.UpdateSpiritLiberation then
        SpiritLiberation_Controller.UpdateEffectInfo()
    end
end