fileFormatVersion: 2
guid: 7172c2edf4f2a6a499a100164fb2f678
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001 Head
    100002: Bip001 Spine1
    100004: //RootNode
    100006: Viper_Back_01_01SHJnt
    100008: Viper_Back_01_02SHJnt
    100010: Viper_Back_01_03SHJnt
    100012: Viper_Back_01_04SHJnt
    100014: Viper_Back_01_05SHJnt
    100016: Viper_Back_01_06SHJnt
    100018: Viper_Back_01_07SHJnt
    100020: Viper_Back_01_08SHJnt
    100022: Viper_Back_01_09SHJnt
    100024: Viper_Back_01_10SHJnt
    100026: Viper_Front_01_01SHJnt
    100028: Viper_Front_01_02SHJnt
    100030: Viper_Front_01_03SHJnt
    100032: Viper_Front_01_04SHJnt
    100034: Viper_Front_01_05SHJnt
    100036: Viper_Front_01_07SHJnt
    100038: Viper_Front_01_08SHJnt
    100040: Viper_Front_01_09SHJnt
    100042: Viper_Front_01_10SHJnt
    100044: Viper_Front_01_11SHJnt
    100046: Viper_Head_JawEndSHJnt
    100048: Viper_Head_JawSHJnt
    100050: Viper_Head_TopSHJnt
    100052: Viper_l_Fang_01_01SHJnt
    100054: Viper_MAINSHJnt
    100056: Viper_Neck_01SHJnt
    100058: Viper_Neck_02SHJnt
    100060: Viper_r_Fang_01_01SHJnt
    100062: Viper_ROOTSHJnt
    100064: Viper_Tongue_01_01SHJnt
    100066: Viper_Tongue_01_02SHJnt
    100068: Viper_Tongue_01_03SHJnt
    100070: Viper_Tongue_01_04SHJnt
    400000: Bip001 Head
    400002: Bip001 Spine1
    400004: //RootNode
    400006: Viper_Back_01_01SHJnt
    400008: Viper_Back_01_02SHJnt
    400010: Viper_Back_01_03SHJnt
    400012: Viper_Back_01_04SHJnt
    400014: Viper_Back_01_05SHJnt
    400016: Viper_Back_01_06SHJnt
    400018: Viper_Back_01_07SHJnt
    400020: Viper_Back_01_08SHJnt
    400022: Viper_Back_01_09SHJnt
    400024: Viper_Back_01_10SHJnt
    400026: Viper_Front_01_01SHJnt
    400028: Viper_Front_01_02SHJnt
    400030: Viper_Front_01_03SHJnt
    400032: Viper_Front_01_04SHJnt
    400034: Viper_Front_01_05SHJnt
    400036: Viper_Front_01_07SHJnt
    400038: Viper_Front_01_08SHJnt
    400040: Viper_Front_01_09SHJnt
    400042: Viper_Front_01_10SHJnt
    400044: Viper_Front_01_11SHJnt
    400046: Viper_Head_JawEndSHJnt
    400048: Viper_Head_JawSHJnt
    400050: Viper_Head_TopSHJnt
    400052: Viper_l_Fang_01_01SHJnt
    400054: Viper_MAINSHJnt
    400056: Viper_Neck_01SHJnt
    400058: Viper_Neck_02SHJnt
    400060: Viper_r_Fang_01_01SHJnt
    400062: Viper_ROOTSHJnt
    400064: Viper_Tongue_01_01SHJnt
    400066: Viper_Tongue_01_02SHJnt
    400068: Viper_Tongue_01_03SHJnt
    400070: Viper_Tongue_01_04SHJnt
    7400000: M_0801_Stun
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0801_Stun
      takeName: M_0801_Stun
      firstFrame: 1
      lastFrame: 90
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 2bdecec6365c3b0438b0024c62c5587e,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
