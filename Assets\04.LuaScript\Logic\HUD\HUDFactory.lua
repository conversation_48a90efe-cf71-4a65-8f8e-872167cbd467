---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/HUD/HUDModule")
require("Logic/HUD/HUDObjPool")

---@type HUDSetting.m_ConstString
local m_DefaultString = HUDSetting.m_ConstString
---
local m_HUDBaseObjPool = {}

---HUD 工廠，用來組裝 HUD
---@class HUDFactory
---author 尊傑
---date 2024.10.07
HUDFactory = {}
--- 限制一幀要操作的歸還量
HUDFactory.RETRURNLIMIT = 20

function HUDFactory.Init()
    local _HUDCanvas = GameObject.Find("HUDCanvas").transform

    HUDFactory.m_HUD_Active = GameObject.New(m_DefaultString.m_HUD_ACTIVE_NAME).transform
    HUDFactory.m_HUD_Active.gameObject.layer = Layer.HUD
    HUDFactory.m_HUD_Active:SetParent(_HUDCanvas)
    HUDFactory.m_HUD_Active.localPosition = Vector3.zero
    HUDFactory.m_HUD_Active.localScale = Vector3.one

    HUDFactory.m_HUD_Stored = GameObject.New(m_DefaultString.m_HUD_STORED_NAME).transform
    HUDFactory.m_HUD_Stored:SetParent(_HUDCanvas)
    HUDFactory.m_HUD_Stored.gameObject.layer = Layer.HUD
    HUDFactory.m_HUD_Stored.gameObject:SetActive(false)
    HUDFactory.m_HUD_Stored.localPosition = Vector3.zero
    HUDFactory.m_HUD_Stored.localScale = Vector3.one
    HUDFactory.m_StoredObj_Name = "Store Unit"

    ResourceMgr.Load("HUD_HPBar", function(iAssets)
        HUDHPPool:Init(iAssets)
    end)

    ResourceMgr.Load("HUD_NameLine", function(iAssets)
        HUDNamePool:Init(iAssets)
    end)

    ResourceMgr.Load("HUD_Title", function(iAssets)
        HUDTitlePool:Init(iAssets)
    end)

    ResourceMgr.Load("HUD_Icon", function(iAssets)
        HUDIconPool:Init(iAssets)
    end)

    ResourceMgr.Load("HUD_BubbleBox", function(iAssets)
        HUDBubblePool:Init(iAssets)
    end)

    ResourceMgr.Load("HUD_Emoji", function(iAssets)
        HUDEmojiPool:Init(iAssets)
    end)

    -- 核心技不用 Pool，因為樣式多變、且目前只有玩家使用
    --HUDCoreSkillPool:Init(iAssets)
end

local _Dash = "_"
local _Root = "_Root"
---取得HUD Prefab組成
---@param iHUDCtrler HUDController
---@param isInit boolean 是否為初始化
function HUDFactory.GetHUD(iHUDCtrler, isInit)
    if isInit then
        local _HUDBaseObj = nil
        if table.Count(m_HUDBaseObjPool) > 0 then
            _HUDBaseObj = table.remove(m_HUDBaseObjPool, 1)
        else
            _HUDBaseObj = Extension.AddMissingComponent(GameObject.New(), typeof( UnityEngine.Canvas )).transform
        end

        _HUDBaseObj.name = tostring(iHUDCtrler.m_HUDData.m_SID) .. _Dash .. iHUDCtrler.m_HUDData.m_Name .. _Root
        _HUDBaseObj:SetParent(HUDFactory.m_HUD_Active)
        _HUDBaseObj.gameObject.layer = Layer.HUD
        _HUDBaseObj.localPosition = Vector3.zero
        _HUDBaseObj.localScale = Vector3.one

        iHUDCtrler.m_RootObj = _HUDBaseObj.gameObject
        iHUDCtrler.m_RootTrans = _HUDBaseObj

        iHUDCtrler.m_Canvas = Extension.AddMissingComponent(iHUDCtrler.m_RootObj, typeof( UnityEngine.Canvas ))
        iHUDCtrler.m_Canvas.overrideSorting = true
        iHUDCtrler.m_Canvas.sortingOrder = iHUDCtrler.m_RoleType == ERoleType.Self and HUDSetting.m_MaxOrder or 0

        iHUDCtrler.m_CanvasGroup = Extension.AddMissingComponent(iHUDCtrler.m_RootObj, typeof( UnityEngine.CanvasGroup ))
        iHUDCtrler.m_CanvasGroup.alpha = 0
        iHUDCtrler.m_CanvasGroup.interactable = false
        iHUDCtrler.m_CanvasGroup.blocksRaycasts = false
        iHUDCtrler.m_CanvasGroup.ignoreParentGroups = false

        iHUDCtrler.m_Module = {}
        iHUDCtrler.m_Module[EHUDModule.HP] = HUDModule_HP.New()
        iHUDCtrler.m_Module[EHUDModule.Name] = HUDModule_Name.New()
        iHUDCtrler.m_Module[EHUDModule.Title] = HUDModule_Title.New()
        iHUDCtrler.m_Module[EHUDModule.Icon] = HUDModule_Icon.New()
        iHUDCtrler.m_Module[EHUDModule.BubbleBox] = HUDModule_BubbleBox.New()
        iHUDCtrler.m_Module[EHUDModule.CoreSkill] = HUDModule_CoreSkill.New()
        iHUDCtrler.m_Module[EHUDModule.Emoji] = HUDModule_Emoji.New()
    else
        local _NeedSort = false
        for k, v in pairs(iHUDCtrler.m_Module) do
            -- 沒被 CullingGroup 排除，且需要顯示此物件
            if iHUDCtrler.m_HUDActive and v.m_hasComponent then
                local _isModify = v.Get(iHUDCtrler)
                _NeedSort =  _NeedSort or _isModify
            else
                HUDFactory.ReturnHUD(iHUDCtrler, false, k)
            end
        end

        if _NeedSort then
            iHUDCtrler:Sort()
        end
    end
end

---歸還 HUD Prefab
---@param iHUDCtrler HUDController
---@param iNeedReturnRoot boolean 是否需要歸還根物件
---@param iHUDType EHUDModule 要歸還的類型，如果是 nil 就全歸還
function HUDFactory.ReturnHUD(iHUDCtrler, iNeedReturnRoot, iHUDType)
    if iHUDType then
        local _Module = iHUDCtrler.m_Module[iHUDType]
        -- 播放中的泡泡框不歸還，可以避免被遮擋再顯示時進度消失
        if iHUDType == EHUDModule.BubbleBox then
            if _Module.m_hasComponent then
                if _Module.Get(iHUDCtrler) then
                    iHUDCtrler:Sort()
                end

                _Module.m_Obj:SetActive(false)
                return
            end
        end

        if not _Module.m_Obj then
            return
        end

        HUDStoreAction[iHUDType](_Module.m_Obj)
        _Module:Reset()
    else
        for k, v in pairs(iHUDCtrler.m_Module) do
            if v.m_Obj then
                HUDStoreAction[k](v.m_Obj)
            end

            v:Reset()
        end
    end

    if iNeedReturnRoot then
        iHUDCtrler.m_RootTrans:SetParent(HUDFactory.m_HUD_Stored)
        iHUDCtrler.m_RootTrans.localPosition = Vector3.zero
        iHUDCtrler.m_RootTrans.localScale = Vector3.one

        iHUDCtrler.m_RootObj.name = HUDFactory.m_StoredObj_Name

        table.insert(m_HUDBaseObjPool, iHUDCtrler.m_RootTrans)
        iHUDCtrler.m_RootObj = nil
        iHUDCtrler.m_RootTrans = nil
    end
end

function HUDFactory.OnUnrequire()
    if HUDFactory.m_HUD_Active.gameObject~=nil then
        HUDFactory.m_HUD_Active.gameObject:Destroy()
    end
        if HUDFactory.m_HUD_Stored.gameObject~=nil then
        HUDFactory.m_HUD_Stored.gameObject:Destroy()
    end
    return true
end