---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---請輸入結構描述, 用途
---寵物介面 突破頁面 Controller 控制寵物突破用的，很多代碼來自基本頁面 UI 很多地方都是一樣的，寵物介面下的一個頁面。
---@class PetUpgrade_Controller
---author Chang Wei
---telephone #2892
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.19
PetUpgrade_Controller = {}
local this = PetUpgrade_Controller

--- 普通心
local NORMAL_HEART = "MainBtn_036_A"

--- 紅心
local RED_HEART = "MainBtn_036"

--- 能量突破 未完成 顏色
local POWER_UP_UNDONE = Extension.GetColor("#434249")

--- 能量突破 完成 顏色
local POWER_UP_DONE = Extension.GetColor("#0cc9d9")

--- 數值和技能強化 未完成 顏色
local UPGRADE_UNDONE = Extension.GetColor("#324A70")

--- 數值和技能強化 完成 顏色
local UPGRADE_DONE = Extension.GetColor("#0CC9D9")

--- 數值未滿的字串樣式
local POWER_NOTMAX = "PO"

--- 數值滿了的字串樣式
local POWER_MAX = "O"

--- 最大突破數
local MAX_POWER_UP = 10

--- 最大數值提升數
local MAX_ATTRIBUTE_POWER_UP = 12

--- 第一技能
local PET_FIRSTSKILL = 4

--- 第二技能
local PET_SECONDSKILL = 5

--- 第三技能
local PET_THIRDSKILL = 6

--- 寵物改名 CommonQuery
local PET_CHANGE_NAME_COMMONQUERY_NUMBER = 510

--- 寵物裝備種類編號
local PET_EQUIPMENT_TYPE_ID = 74

--- 寵物詞有數量字大小
local PET_CARRY_STRING_SIZE = 32

--- 寵物能量突破 Icon 大小
local UPGRADE_ICON_SIZE = 100

--- 攻擊倍率
local UPGRADE_ATTACK = 1

--- 攻速倍率
local UPGRADE_ATTSPEED = 2

--- 爆擊倍率
local UPGRADE_CRITICAL = 3

--- 寵物技能 Icon 大小
local PET_SKILL_ICON = 80

--- 寵物最大星等
local PET_MAX_STAR = 5

--- 突破一次會給 10 點
local UPGRADE_POINT = 10

--- 技能最多強化次數
local MAX_SKILL_LEVEL_UP = 2

--- 屬性每階段強化所需點數
local ATTRIBUTE_LEVEL_TABLE = {1,1,1,1,2,2,2,2,2,3,3,4,"max"}

--- 技能每階段強化所需點數
local SKILL_LEVEL_TABLE = {4,10,"max"}

--- 技能升級左邊
local SKILL_UPGRADE_DETAIL_LEFT = 1

--- 技能升級右邊
local SKILL_UPGRADE_DETAIL_RIGHT = 2

---==========字串==========---
--- 自律者數量： 字串
local PET_NUMBER = 20323101

--- 最愛設定中 : 字串
local PET_LOVE = 20102021

--- 沒有持有 5 星寵物 : 字串
local NO_FIVE_STAR_PET = 20323317

--- 能量突破 : 字串
local POWER_UP_STR = 20323013

--- MAX : 字串
local MAX_POWER_STR = 20323303

--- 強化 : 字串
local UPGRADE_STR = 20103013

--- 已達上限 : 字串
local UPGRADE_LIMIT_STR = 20111011

--- 未持有任何同名自律者 : 字串
local NO_PET_FOR_UPGRADE = 20323309

--- 強化成功 : 字串
local UPGRADE_SUCCESS_STR = 20111042

--- 冷卻時間 : 字串
local COOLDOWN_STR = 20111040

--- 剩餘能量點 : 字串
local UPGRADE_POINT_STR = 20323311

--- 全部寵物重複道具 pool
local m_Table_PetUnit = {}

--- 列表排序
---@type number
this.m_PetUpgradeArrange = 0

--- 點選中寵物 SID
---@type number
this.m_NowUpgradePetSID = 0

--- 選擇寵物視窗目前點選格子 Idx
---@type number
this.m_WindowNowClick = 0

--- 連續升級次數
---@type number
this.m_ContinuousUpGradeTime = 0

--- 點選中寵物 Data
---@type table
this.m_NowUpgradePetData = {}

--- 列表顯示用資料
---@type table
this.m_ShowUpgradePetListData = {}

--- 目前被點選的格子寵物資料
---@type table
this.m_WindowNowClickPetData = {}

--- 要投點的寵物技能
---@type number
this.m_PetSkillUpgradeSlot = 0

--- 寵物技能目標等級
---@type number
this.m_PetSkillUpgradeTargetLevel = 0

--- 頁面 Init 了沒
---@type bool
this.m_IsInit = false

--- 抓那些 UI 的東西
local function InitialUI()

    -- 初始排序
    this.m_PetUpgradeArrange = EPetArrange[1].Power

    -- 資訊介面 寵物種類
    this.m_UpgradePetType = this.m_ViewRef.m_Dic_Trans:Get("&Image_UpgradePetTypeIcon"):GetComponent(typeof(Image))

    -- 資訊介面 寵物名字
    this.m_UpgradeName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_PetUpgradeName")

    -- 資訊介面 寵物戰力 + 前面的 Icon
    this.m_UpgradePowerSet = this.m_ViewRef.m_Dic_Trans:Get("&Image_UpgradeBattlePoint")

    -- 資訊介面 寵物戰力
    this.m_UpgradePower = this.m_ViewRef.m_Dic_TMPText:Get("&Text_UpgradePetPower")

    -- 寵物突破條那欄
    local _PetPowerLevel = this.m_ViewRef.m_Dic_Trans:Get("&Image_PetUpgradeFrameBG")

    -- 寵物 Icon Parent
    this.m_UpgradeStateParent = _PetPowerLevel.transform:Find("Image_IconFrame")

    -- 寵物 Icon
    this.m_UpgradePetIcon = IconMgr.NewPetIcon(0, this.m_UpgradeStateParent.gameObject.transform, UPGRADE_ICON_SIZE)
    this.m_UpgradePetIcon:SetClickTwice(false)

    -- 突破能量等級 text
    this.m_UpgradeLevel = this.m_UpgradeStateParent.transform:Find("Text_PowerUpLevel/Text_LevelNumber"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 突破階段圖片
    local _PowerLevelImageParent = this.m_UpgradeStateParent.transform:Find("Panel_PowerImage")
    this.m_PowerLevelImage = {}
    for i = 1, MAX_POWER_UP do
        local _FindStr = "Image_PowerLever_"  .. i
        this.m_PowerLevelImage[i] = _PowerLevelImageParent.transform:Find(_FindStr):GetComponent(typeof(Image))
    end

    -- 確認突破
    this.m_BtnPowerUp = this.m_ViewRef.m_Dic_Trans:Get("&Button_ConfirmPowerUp")
    Button.AddListener(this.m_BtnPowerUp, EventTriggerType.PointerClick, function()PetUpgrade_Controller.PowerUpButton()end)

    -- 按鈕字串
    this.m_BtnPowerUpTmp = this.m_BtnPowerUp.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 目前可以使用的點數
    this.m_NowUsablePoint = this.m_ViewRef.m_Dic_TMPText:Get("&Text_UpgradePointNumber")

    -- 寵物可強化列表
    this.m_UpgradeContent = this.m_ViewRef.m_Dic_Trans:Get("&UpgradeContent")

    -- 寵物技能強化需要開啟的確認視窗
    this.m_SkillUpgradeWindow = this.m_ViewRef.m_Dic_Trans:Get("&Panel_SkillUpgradeWindow")

    -- 按鈕 Group
    local _YesNoUpgradeBtnGrp = this.m_SkillUpgradeWindow.transform:Find("Window_PetSelect/ButtonGroup")

    -- 確定使這隻寵物合成
    this.m_YesUpgradeBtn = _YesNoUpgradeBtnGrp.transform:Find("Button_Confirm")
    Button.AddListener(this.m_YesUpgradeBtn, EventTriggerType.PointerClick, function()

        local _CostPoints = SKILL_LEVEL_TABLE[this.m_PetSkillUpgradeTargetLevel]

        SendProtocol_020._020_07(this.m_NowUpgradePetData.m_SID, this.m_NowUpgradePetData.m_ID, this.m_PetSkillUpgradeSlot, this.m_PetSkillUpgradeTargetLevel, _CostPoints)

        -- 關視窗
        this.m_SkillUpgradeWindow.gameObject:SetActive(false)
    end)

    -- 取消使用
    this.m_NoUpgradeBtn = _YesNoUpgradeBtnGrp.transform:Find("Button_Cancel")
    Button.AddListener(this.m_NoUpgradeBtn, EventTriggerType.PointerClick, function()
        this.m_SkillUpgradeWindow.gameObject:SetActive(false)
    end)

    -- 寵物確認視窗內容物
    this.m_SkillDetail = {}

    -- 左邊格子內容
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_LEFT] = {}
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_LEFT].m_Main_Text = this.m_ViewRef.m_Dic_Trans:Get("&Text_NowSkillDetail"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_LEFT].m_CoolDown_Text = this.m_ViewRef.m_Dic_Trans:Get("&Text_NowSkillCD"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_LEFT].m_SkillLine = this.m_SkillUpgradeWindow.transform:Find("Window_PetSelect/Image_AreaOne/Image_DownLine")

    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_RIGHT] = {}
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_RIGHT].m_Main_Text = this.m_ViewRef.m_Dic_Trans:Get("&Text_NewSkillDetail"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_RIGHT].m_CoolDown_Text = this.m_ViewRef.m_Dic_Trans:Get("&Text_NewSkillCD"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    this.m_SkillDetail[SKILL_UPGRADE_DETAIL_RIGHT].m_SkillLine = this.m_SkillUpgradeWindow.transform:Find("Window_PetSelect/Image_AreaTwo/Image_DownLine")

    -- 強化列表內容物 數值類的 View 裡面的東西都一樣 技能類同樣 放一起比較好 Init
    this.m_UpgradeThing = {}
    for i = UPGRADE_ATTACK, PET_SECONDSKILL do
        this.m_UpgradeThing[i] = {}
    end

    -- 攻擊倍率
    this.m_UpgradeThing[UPGRADE_ATTACK].m_GObject = this.m_UpgradeContent.transform:Find("Image_AttackGroup")

    -- 攻速倍率
    this.m_UpgradeThing[UPGRADE_ATTSPEED].m_GObject = this.m_UpgradeContent.transform:Find("Image_AttackSpeed")

    -- 爆擊倍率
    this.m_UpgradeThing[UPGRADE_CRITICAL].m_GObject = this.m_UpgradeContent.transform:Find("Image_Critical")

    for i = UPGRADE_ATTACK, UPGRADE_CRITICAL do

        -- 不是技能
        this.m_UpgradeThing[i].m_IsSkill = false

        -- 屬性現在倍率 字串
        this.m_UpgradeThing[i].NowNumber = this.m_UpgradeThing[i].m_GObject.transform:Find("Text_Now"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 現在屬性到下一級屬性中間的箭頭
        this.m_UpgradeThing[i].Arrow = this.m_UpgradeThing[i].m_GObject.transform:Find("Image_Arrow")

        -- 屬性現在倍率數值
        this.m_UpgradeThing[i].Value = 0

        -- 下一級屬性倍率
        this.m_UpgradeThing[i].NewNumber = this.m_UpgradeThing[i].m_GObject.transform:Find("Text_New"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 屬性等級圖
        this.m_UpgradeThing[i].SkillPowerBar = {}
        for j = 1, MAX_ATTRIBUTE_POWER_UP do
            local _FindStr = "Image_Bar_" .. j
            this.m_UpgradeThing[i].SkillPowerBar[j] = this.m_UpgradeThing[i].m_GObject.transform:Find("Panel_SkillPower/" .. _FindStr):GetComponent(typeof(Image))
        end

        -- 點數 group
        this.m_UpgradeThing[i].m_PointCostGroup = this.m_UpgradeThing[i].m_GObject.transform:Find("Image_Point")

        -- 屬性升級所需點數
        this.m_UpgradeThing[i].m_PointCost = this.m_UpgradeThing[i].m_GObject.transform:Find("Image_Point/Text_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 屬性升級按鈕
        this.m_UpgradeThing[i].m_BtnUpgrade = this.m_UpgradeThing[i].m_GObject.transform:Find("Button_Upgrade")
        this.m_UpgradeThing[i].m_BtnUpgradeTmp = this.m_UpgradeThing[i].m_BtnUpgrade.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        Button.AddListener(this.m_UpgradeThing[i].m_BtnUpgrade, EventTriggerType.PointerClick, function()

            -- 顯示文字
            local _NameTable = {this.m_UpgradeThing[i].m_Name.text, TextData.Get(UPGRADE_POINT_STR)}

            -- 有沒有需要 % 攻速排第2個 他不用 %
            local _NeedPercent = { i ~= UPGRADE_ATTSPEED, false}

            -- 寵物資料
            local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(this.m_NowUpgradePetData.m_SID)

            -- 企劃給的計算式 數值編號 - 1 + 寵物星等 基礎值
            local _NowValue = 0
            local _NewValue = 0

            if(i == UPGRADE_ATTACK) then

                _NowValue = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_Atk) / 10
                _NewValue = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_Atk) / 10

            elseif(i == UPGRADE_ATTSPEED) then

                _NowValue = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_AtkSpeed
                _NewValue = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_AtkSpeed

            else

                _NowValue = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_Criticle) / 10
                _NewValue = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + this.m_UpgradeThing[i].m_NowLevel).m_Criticle) / 10

            end

            -- 兩個內容物
            local _Type2Data = CommonQueryMgr.GetNewCommonQueryData()
            -- 強化目前的數值 和 目前剩餘能量
            local _NumberBefore = {_NowValue, tonumber(this.m_NowUsablePoint.text)}
            _Type2Data:BuildDescriptionBoxTable(_NameTable, _NumberBefore, {_NewValue, tonumber(this.m_NowUsablePoint.text) - ATTRIBUTE_LEVEL_TABLE[this.m_UpgradeThing[i].m_NowLevel + 1]}, _NeedPercent)

            -- 離滿等還要多久
            local _MaxLevel = MAX_ATTRIBUTE_POWER_UP - this.m_UpgradeThing[i].m_NowLevel

            -- 每次會提升的倍率
            -- 每次升等需要的突破點數 和 會增加的倍率
            local _IncreasePower = {}
            local _LevelPointTable = {}
            local _LevelCount = 0
            local _MinusPoint = 0
            local _PetPoint = tonumber(this.m_NowUsablePoint.text)
            local _IsConfirmBtnOK = true
            for j = this.m_UpgradeThing[i].m_NowLevel + 1, MAX_ATTRIBUTE_POWER_UP do

                _MinusPoint = _MinusPoint + ATTRIBUTE_LEVEL_TABLE[j]
                if(_PetPoint < _MinusPoint and j == this.m_UpgradeThing[i].m_NowLevel + 1) then
                    _IsConfirmBtnOK = false
                end

                _LevelCount = _LevelCount + 1

                -- 會增加的值 - 他前一格的值 取升級這一次會加多少倍率
                local _NewPoint = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + j)

                if(i == UPGRADE_ATTACK) then

                    local _CountPower = (_NewPoint.m_Atk) / 10

                    table.insert(_IncreasePower, _CountPower)

                elseif(i == UPGRADE_ATTSPEED) then

                    local _CountPower = (_NewPoint.m_AtkSpeed)

                    table.insert(_IncreasePower, _CountPower)

                else

                    local _CountPower = (_NewPoint.m_Criticle) / 10

                    table.insert(_IncreasePower, _CountPower)

                end

                local _PointCount = _PetPoint - _MinusPoint
                table.insert(_LevelPointTable, _PointCount)

            end

            local _CombinedTable = {_IncreasePower, _LevelPointTable}

            -- 建每次改的數值
            _Type2Data:BuildTimeTable(_MaxLevel, 1, {}, _CombinedTable, {}, true)
            _Type2Data:BuildConfirmBtnStatus(_IsConfirmBtnOK)

            local _CommonQueryIDX = 515 --使用詢問視窗類別編號

            CommonQueryMgr.AddNewInform(
                    _CommonQueryIDX,
                    {this.m_UpgradeThing[i].m_Name.text},
                    {},
                    function ()
                        local _ConfirmQty = CommonQuery_Type2_Controller.GetCurrentTimes()
                        if _ConfirmQty > 0 then

                            local _TargetLevel = this.m_UpgradeThing[i].m_NowLevel + 1 -- _ConfirmQty + this.m_UpgradeThing[i].m_NowLevel 先留一下 這個原本要用來一次升多級
                            local _CostPoints = ATTRIBUTE_LEVEL_TABLE[_TargetLevel]
                            this.m_ContinuousUpGradeTime = _ConfirmQty

                            SendProtocol_020._020_07(this.m_NowUpgradePetData.m_SID, this.m_NowUpgradePetData.m_ID, i, _TargetLevel, _CostPoints)

                        end
                    end,
                    {},
                    nil,
                    {},
                    nil,
                    {},
                    _Type2Data)

        end)

        -- 屬性名稱
        this.m_UpgradeThing[i].m_Name = this.m_UpgradeThing[i].m_GObject.transform:Find("Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 屬性現在等級
        this.m_UpgradeThing[i].m_NowLevel = 0

    end

    -- 寵物技能 有三招 但只能升級兩招
    local _Count = 0
    for i = PET_FIRSTSKILL, PET_SECONDSKILL do
        _Count = _Count + 1
        local _NameStr = "Panel_Skill_" .. _Count

        -- 稍微分一下
        this.m_UpgradeThing[i].m_IsSkill = true

        -- 找本體
        this.m_UpgradeThing[i].m_GObject = this.m_UpgradeContent.transform:Find(_NameStr)

        -- 現在這個寵物技能
        this.m_UpgradeThing[i].m_NowIconParent = this.m_UpgradeThing[i].m_GObject.transform:Find("Panel_IconGroup/Panel_SkillNow")
        this.m_UpgradeThing[i].m_NowSkill = IconMgr.NewSkillIcon(0, this.m_UpgradeThing[i].m_NowIconParent, PET_SKILL_ICON)
        this.m_UpgradeThing[i].m_NowSkillData = {}

        -- 升級後的寵物技能
        this.m_UpgradeThing[i].m_NewIconParent = this.m_UpgradeThing[i].m_GObject.transform:Find("Panel_IconGroup/Panel_SkillNew")
        this.m_UpgradeThing[i].m_NewSkill = IconMgr.NewSkillIcon(0, this.m_UpgradeThing[i].m_NewIconParent, PET_SKILL_ICON)
        this.m_UpgradeThing[i].m_NewSkillData = {}

        -- 技能名稱
        this.m_UpgradeThing[i].m_Name = this.m_UpgradeThing[i].m_GObject.transform:Find("Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 技能升級等級
        this.m_UpgradeThing[i].m_SkillBar = {}
        this.m_UpgradeThing[i].m_SkillBar[1] = this.m_UpgradeThing[i].m_GObject.transform:Find("Panel_SkillPower/Image_Bar_1"):GetComponent(typeof(Image))
        this.m_UpgradeThing[i].m_SkillBar[2] = this.m_UpgradeThing[i].m_GObject.transform:Find("Panel_SkillPower/Image_Bar_2"):GetComponent(typeof(Image))

        -- 點數 group
        this.m_UpgradeThing[i].m_PointCostGroup = this.m_UpgradeThing[i].m_GObject.transform:Find("Image_Point")

        -- 技能升級所需點數
        this.m_UpgradeThing[i].m_PointCost = this.m_UpgradeThing[i].m_GObject.transform:Find("Image_Point/Text_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 技能升級按鈕
        this.m_UpgradeThing[i].m_BtnUpgrade = this.m_UpgradeThing[i].m_GObject.transform:Find("Button_Upgrade")
        this.m_UpgradeThing[i].m_BtnUpgradeTmp = this.m_UpgradeThing[i].m_BtnUpgrade.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        Button.AddListener(this.m_UpgradeThing[i].m_BtnUpgrade, EventTriggerType.PointerClick, function()

            -- 把技能確認視窗打開
            this.m_SkillUpgradeWindow.gameObject:SetActive(true)

            -- 填入內容
            local _Style = ""
            local _SkillCD = 0
            for k = SKILL_UPGRADE_DETAIL_LEFT, SKILL_UPGRADE_DETAIL_RIGHT do
                if(k == SKILL_UPGRADE_DETAIL_LEFT)then
                    _Style = "<style=W>"
                    _SkillCD = this.m_UpgradeThing[i].m_NowSkillData.m_SingleCD
                    this.m_SkillDetail[k].m_Main_Text.text = GString.StyleChangeBatch(TextData.Get(this.m_UpgradeThing[i].m_NowSkillData.m_InfoStrId), _Style)
                else
                    _Style = "<style=G>"
                    _SkillCD = this.m_UpgradeThing[i].m_NewSkillData.m_SingleCD
                    this.m_SkillDetail[k].m_Main_Text.text = GString.StyleChangeBatch(TextData.Get(this.m_UpgradeThing[i].m_NewSkillData.m_InfoStrId), _Style)
                end

                this.m_SkillDetail[k].m_CoolDown_Text.text = TextData.Get(COOLDOWN_STR).." : ".._SkillCD

                -- 要是技能的 CD 是 0 的話 下線不要顯示了
                if(_SkillCD == 0) then
                    this.m_SkillDetail[k].m_SkillLine.gameObject:SetActive(false)
                else
                    this.m_SkillDetail[k].m_SkillLine.gameObject:SetActive(true)
                end

            end

            -- 那個要升級
            this.m_PetSkillUpgradeSlot = i

            --
            this.m_PetSkillUpgradeTargetLevel = this.m_UpgradeThing[i].m_NowLevel + 1
        end)

    end

    -- 寵物突破確定視窗
    this.m_PetUpgradeConfirmWindow = this.m_ViewRef.m_Dic_Trans:Get("&Image_UpgradeBlackBackGround")

    -- ScrollView 中會重複用到的 GameObject
    this.m_RepeatItem = this.m_ViewRef.m_Dic_Trans:Get("&Panel_UpgradeItem")

    -- 道具 Pool
    this.m_ItemPool = Extension.CreatePrefabObjPool(this.m_RepeatItem.gameObject, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

    -- pool 道具要出生的地點
    this.m_WindowContent = this.m_ViewRef.m_Dic_Trans:Get("&PetUpgradeWindowContentAera")

    -- 按鈕 Group
    local _YesNoBtnGrp = this.m_PetUpgradeConfirmWindow.transform:Find("Window_PetSelect/ButtonGroup")

    -- 確定使這隻寵物合成
    this.m_YesUseThisPetBtn = _YesNoBtnGrp.transform:Find("Button_Confirm")
    Button.AddListener(this.m_YesUseThisPetBtn, EventTriggerType.PointerClick, function()

        -- 送協定
        SendProtocol_020._020_06(this.m_NowUpgradePetSID, this.m_NowUpgradePetData.m_ID, this.m_WindowNowClickPetData.m_PetSlot)

        -- 關視窗
        this.m_PetUpgradeConfirmWindow.gameObject:SetActive(false)
    end)

    -- 取消使用 先關掉後面應該還要加清除的動作 TOADD
    this.m_NoUseBtn = _YesNoBtnGrp.transform:Find("Button_Cancel")
    Button.AddListener(this.m_NoUseBtn, EventTriggerType.PointerClick, function()
        this.m_PetUpgradeConfirmWindow.gameObject:SetActive(false)
    end)

    -- 選擇使用寵物視窗上 目前這隻寵物擁有的突破點數
    this.m_WindowNowPoint = this.m_PetUpgradeConfirmWindow.transform:Find("Window_PetSelect/Image_PointBG/Text_NowPoint"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 選擇使用寵物視窗上 目前這隻寵物獲得突破後會有多少點數
    this.m_WindowNewPoint = this.m_PetUpgradeConfirmWindow.transform:Find("Window_PetSelect/Image_PointBG/Text_NewPoint"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 寵物最愛按鈕
    this.m_PetLoveBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_UpgradePetLove")
    Button.AddListener(this.m_PetLoveBtn, EventTriggerType.PointerClick, PetUpgrade_Controller.SetPetLove)

    -- 寵物改名
    this.m_PetChangeNameBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_UpgradeChangeName")
    Button.AddListener(this.m_PetChangeNameBtn, EventTriggerType.PointerClick, PetUpgrade_Controller.PetChangeName)

    -- 寵物還沒有突破一次要蓋遮罩
    this.m_PetNoPowerUp = this.m_ViewRef.m_Dic_Trans:Get("&Image_Unlock")

    -- 沒有選擇寵物的話需要開啟的遮罩
    this.m_PickPetCover = this.m_ViewRef.m_Dic_Trans:Get("&Image_UpgradePickPetCover")

end

--- 設定要突破使用的寵物確認視窗
---@param table iUsablePets 可使用的寵物
function PetUpgrade_Controller.SetUpgradePetWindow(iUsablePets)

    -- 先全部設 false 後面再看要開幾個
    for _k, _v in pairs(m_Table_PetUnit) do
        _v.gameObject:SetActive(false)
    end

    for _k, _data in pairs(iUsablePets) do

        local _Unit
        local _MainUnit = {}

        -- 看看有沒有東西 有就直接拉來用
        if(m_Table_PetUnit[_k]) then
            _MainUnit = m_Table_PetUnit[_k]
        else
            -- 從池生個新的
            _Unit = this.m_ItemPool:Get()

            -- 整個東西
            _MainUnit.gameObject = _Unit

            -- 東西按鈕
            _MainUnit.Button = _Unit.gameObject.transform:Find("Button_Click")
            Button.AddListener(_MainUnit.Button, EventTriggerType.PointerClick, function()

                -- 關閉上一個被點選的格子 檢查一下是不是 0
                if(this.m_WindowNowClick ~= 0) then
                    m_Table_PetUnit[this.m_WindowNowClick].SelectGObj.gameObject:SetActive(false)
                end

                -- 設定是點了哪個格子
                this.m_WindowNowClick = _k

                -- 打開這個格子的被選框
                m_Table_PetUnit[this.m_WindowNowClick].SelectGObj.gameObject:SetActive(true)

                -- 設定被點選格子寵物資料
                this.m_WindowNowClickPetData = _MainUnit.PetData

                -- 有點選東西了 按鈕要設回 Enable
                Button.SetEnable(this.m_YesUseThisPetBtn)

             end)

            -- 找寵物名
            _MainUnit.Text_PetName = _MainUnit.Button.gameObject.transform:Find("Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))

            -- 寵物星星
            _MainUnit.Stars = {}
            local _StarParent = _MainUnit.Button.gameObject.transform:Find("Panel_Stars")
            for i = 1, PET_MAX_STAR do

                _MainUnit.Stars[i] = _StarParent.gameObject.transform:Find("Star_".. i):GetComponent(typeof(Image))

            end

            -- 寵物 Icon
            local _IconParent = _MainUnit.Button.gameObject.transform:Find("Panel_PetIcon")
            _MainUnit.PetIcon = IconMgr.NewPetIcon(0, _IconParent.gameObject.transform, UPGRADE_ICON_SIZE)

            -- 寵物裝備 Icon
            local _EquipIconParent = _MainUnit.Button.gameObject.transform:Find("Panel_PetItem")
            _MainUnit.PetEquipIcon = IconMgr.NewItemIcon(0, _EquipIconParent.gameObject.transform, PET_SKILL_ICON)

            -- 選擇框框
            _MainUnit.SelectGObj = _MainUnit.Button.gameObject.transform:Find("Image_Select")

            -- 遮罩 要是這隻寵物現在不能被突破就要被開啟
            _MainUnit.Cover = _Unit.gameObject.transform:Find("Image_Block")

            -- 設定這傢伙出生點
            _MainUnit.gameObject.transform:SetParent(this.m_WindowContent.transform)
            _MainUnit.gameObject.transform.localScale = Vector3.one

        end

        -- 開始填資料
        _MainUnit.PetData = _data

        -- 寵物名稱
        _MainUnit.Text_PetName.text = _data.m_PetName

        -- 寵物星數
        for i = 1, PET_MAX_STAR do

            local _ChangeColor = Extension.GetColor(PetStarColor.STAR_DARK)
            if(_data.m_StarLv >= i) then
                _ChangeColor = Extension.GetColor(PetStarColor.STAR_LIGHT)
            end
            _MainUnit.Stars[i].color = _ChangeColor

        end

        -- 寵物 Icon
        _MainUnit.PetIcon:RefreshIcon(_data.m_PetID)

        -- 道具
        _MainUnit.PetEquipIcon:RefreshIcon(_data.m_PetEquipment[1])

        -- 是不是最愛寵物
        if(_data.m_IsPetLoved)then
            _MainUnit.Cover.gameObject:SetActive(true)
        else
            _MainUnit.Cover.gameObject:SetActive(false)
        end

        -- 存起來
        m_Table_PetUnit[_k] = _MainUnit

        -- 打開顯示
        m_Table_PetUnit[_k].gameObject:SetActive(true)
    end

end

---初始化
function PetUpgrade_Controller.Init(iController)

    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    InitialUI()

    -- Init 了
    this.m_IsInit = true
end

---Update
function PetUpgrade_Controller.Update()

end

--- 開啟介面
function PetUpgrade_Controller.Open(iParam)

    -- 重置顯示
    PetUpgrade_Controller.ResetDisplay(false)

    -- 設定寵物顯示
    if (Extension.IsUnityObjectNull(this.m_PetShowRT)) then
        --- 把 RenderTexture 讀進來
        ResourceMgr.Load("PetRenderTexture", function(iAsset)
            this.m_PetShowRT = iAsset
            AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
            AppearanceMgr.SetClearFlagToSolidColor()
            AppearanceMgr.EnableCamera(true)
        end)
    else
        AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
        AppearanceMgr.SetClearFlagToSolidColor()
        AppearanceMgr.EnableCamera(true)
    end

    -- 背包道具
    local _BagItems = Bag_Model.BagSortAndReturn(EBagType.Consumables)

    -- 寵物道具
    this.m_PetItems =  {}

    if(_BagItems ~= nil) then
        for _k, _v in pairs(_BagItems) do

            local _ItemData = ItemData.GetItemDataByIdx(_v.m_ItemIdx)

            if(_ItemData.m_Type == PET_EQUIPMENT_TYPE_ID) then
                table.insert(this.m_PetItems, _v)
            end

        end
    end

    -- 選擇使用第一個選項
    PetList_Controller.SetPanelOption1On(true)

    -- 寵物塞選
    PetUpgrade_Controller.Option1Click(this.m_PetUpgradeArrange)

    -- 寵物資料 打開就是顯是第一個
    if (next(this.m_ShowUpgradePetListData)) then
        PetUpgrade_Controller.PetListClick(this.m_ShowUpgradePetListData[1])
    end

    return true
end

--- 頁面關閉
function PetUpgrade_Controller.Close()

end

--- Destroy 時執行
function PetUpgrade_Controller.OnDestroy()

    --- 列表排序
    ---@type number
    this.m_PetUpgradeArrange = 0

    --- 點選中寵物 SID
    ---@type number
    this.m_NowUpgradePetSID = 0

    --- 連續升級次數
    ---@type nubmer
    this.m_ContinuousUpGradeTime = 0

    --- 點選中寵物 Data
    ---@type table
    this.m_NowUpgradePetData = {}

    --- 列表顯示用資料
    ---@type table
    this.m_ShowUpgradePetListData = {}

    --- 背包內Icons
    ---@type table
    this.m_EquipmentIcons = {}

    --- 頁面 Init 了沒
    ---@type bool
    this.m_IsInit = false
    return true
end

--- 選擇方案一有更動 這邊是寵物排序要重新給寵物列表
function PetUpgrade_Controller.Option1Click(iNumber)

    -- 整理完基本排序的
    local _MyPetData = Pet_Model.ArrangePetListOrder(iNumber)

    -- 沒東西就 return
    if(table.IsNullOrEmpty(_MyPetData)) then
        PetList_Controller.SetShowData({})
        return nil
    end

    -- 換頁紀錄一下
    this.m_PetUpgradeArrange = iNumber

    -- 這邊要剃除沒有五星的寵物
    local _FiveStarPets = {}
    for _, _pet in pairs(_MyPetData) do

        if(_pet.m_StarLv == PET_MAX_STAR) then

            table.insert(_FiveStarPets, _pet)

        end

    end

    _MyPetData = {}  -- 清空原表
    for _, _pet in pairs(_FiveStarPets) do
        table.insert(_MyPetData, _pet)  -- 把其它寵物都加進來
    end

    -- 設定寵物列表要用的資料
    local _ShowData = {}
    for _k, _v in pairs(_MyPetData) do

        -- 創建 table
        local _Table = PetList_Controller.CreateEmptyListDataTable()
        _Table.m_ID = _v.m_PetID
        _Table.m_Name = _v.m_PetName
        _Table.m_Type = _v.m_BasicData.m_PetType
        _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_v.m_BasicData.m_Pet2DIcon, 6)
        _Table.m_Star = _v.m_StarLv
        _Table.m_IsShowLikePet = true
        _Table.m_IsPetLoved = _v.m_IsPetLoved
        _Table.m_IsShowNumber = false
        _Table.m_Number = _v.m_Power
        _Table.m_DataType = EPetDataType.PET
        _Table.m_SID = _v.m_PetSlot

        table.insert(_ShowData, _Table)
    end

    -- 空空的就關掉吧
    if(not next(_ShowData)) then
        this.m_UpgradeContent.gameObject:SetActive(false)
        local _String = TextData.Get(NO_FIVE_STAR_PET)
        PetList_Controller.SetExtraInfo(true, _String)
    else
        this.m_UpgradeContent.gameObject:SetActive(true)
        PetList_Controller.SetExtraInfo(false)
    end

    -- 設定列表中要出現的資料
    PetList_Controller.SetShowData(_ShowData)

    -- 下拉視窗的文字
    PetList_Controller.SetDropDownListText(iNumber)

    -- 儲存列表資料
    this.m_ShowUpgradePetListData = _ShowData
end

--- 寵物點選 有成功設訂的話 return true
function PetUpgrade_Controller.PetListClick(iData)

    -- 把有些被關閉的元件打開
    PetUpgrade_Controller.ResetDisplay(true)

    -- 設定寵物種類
    local _TypeStr = Pet_Model.GetStatusImageString(iData.m_Type)
    SpriteMgr.Load(_TypeStr, this.m_UpgradePetType)

    -- 設定寵物名字
    this.m_UpgradeName.text = iData.m_Name

    -- 設定寵物戰力
    this.m_UpgradePower.text = iData.m_Number

    -- 寵物 Icon
    this.m_UpgradePetIcon:RefreshIcon(iData.m_ID)

    -- 清掉上一個點選的框
    if (this.m_NowUpgradePetSID ~= 0) then
        PetList_Controller.DeleteSelectedItem(this.m_NowUpgradePetSID)
    end

    -- 紀錄一下SID
    this.m_NowUpgradePetSID = iData.m_SID

    -- 點中寵物資料
    this.m_NowOnSelectPetData = iData

    -- 設定選擇到表中
    PetList_Controller.SetSelectedItem(iData.m_SID, iData.m_ID)

    -- 刷一下
    PetList_Controller.Refresh()

    -- 點中寵物資料
    this.m_NowUpgradePetData = iData

    -- 寵物資料
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(iData.m_SID)

    -- 寵物最愛
    if (_PetServerData.m_IsPetLoved) then
        SpriteMgr.Load(RED_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))
    else
        SpriteMgr.Load(NORMAL_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))
    end

    -- 如果滿破要顯示 MAX
    if (_PetServerData.m_OverfulfilTime == MAX_POWER_UP) then

        Button.SetDisable(this.m_BtnPowerUp, false)
        this.m_BtnPowerUpTmp.text = TextData.Get(MAX_POWER_STR)

    else

        Button.SetEnable(this.m_BtnPowerUp)
        this.m_BtnPowerUpTmp.text = TextData.Get(POWER_UP_STR)

    end

    -- 能量突破階段 數字
    this.m_UpgradeLevel.text = _PetServerData.m_OverfulfilTime

    -- 有沒有強化過 沒有就開起來
    if (_PetServerData.m_OverfulfilTime == 0) then
        this.m_PetNoPowerUp.gameObject:SetActive(true)
    else
        this.m_PetNoPowerUp.gameObject:SetActive(false)
    end

    -- 能量突破階段 圖片
    for i = 1, MAX_POWER_UP do
        local _ChangeColor = POWER_UP_UNDONE

        if (_PetServerData.m_OverfulfilTime >= i) then
            _ChangeColor = POWER_UP_DONE
        end

        this.m_PowerLevelImage[i].color = _ChangeColor
    end

    -- 目前有的突破能量
    this.m_NowUsablePoint.text = _PetServerData.m_OverfulfilPoint

    -- 企劃給的計算式 數值編號 - 1 + 寵物星等 這會是白字的部分
    local _PetAttribute = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv)

    if (not next(_PetAttribute)) then
        return
    end

    -- 數值編號 - 1 + 寵物星等 + 強化次數 這個要一個一個算 (後面還要加裝備數值)
    local _PetBonusAtk = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATTACK]).m_Atk) / 10
    local _PetBonusAtkSpd = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATKSPEED]).m_AtkSpeed
    local _PetBonusCrit = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.CRITICAL]).m_Criticle) / 10

    -- 要看滿級沒有
    local _MaxStringStyel = {}
    for i = EPetStrengthen.ATTACK, EPetStrengthen.CRITICAL do

        if (_PetServerData.m_PetStrengthen[i] >= MAX_ATTRIBUTE_POWER_UP) then

            table.insert(_MaxStringStyel, POWER_MAX)

        else

            table.insert(_MaxStringStyel, POWER_NOTMAX)

        end

    end

    -- 下面要 for 比較好用
    local _NowLoopTable = {GString.StringWithStyle(_PetBonusAtk .. "%", _MaxStringStyel[EPetStrengthen.ATTACK]),
                            GString.StringWithStyle(_PetBonusAtkSpd, _MaxStringStyel[EPetStrengthen.ATKSPEED]),
                            GString.StringWithStyle(_PetBonusCrit .. "%", _MaxStringStyel[EPetStrengthen.CRITICAL])}

    local _OnlyValueTable = {_PetBonusAtk .. "%", _PetBonusAtkSpd, _PetBonusCrit .. "%"}

    local _NewBonusAtk = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATTACK]).m_Atk) / 10
    local _NewBonusAtkSpd = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATKSPEED]).m_AtkSpeed
    local _NewBonusCrit = (PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.CRITICAL]).m_Criticle) / 10

    local NextLoopTable = {_NewBonusAtk .. "%", _NewBonusAtkSpd, _NewBonusCrit .. "%"}

    -- 前面 3 個是屬性
    for i = UPGRADE_ATTACK, UPGRADE_CRITICAL do

        -- 屬性現在倍率
        this.m_UpgradeThing[i].NowNumber.text = _NowLoopTable[i]
        this.m_UpgradeThing[i].Value = _OnlyValueTable[i]

        -- 下一級屬性倍率
        if (_MaxStringStyel[i] == POWER_NOTMAX) then
            this.m_UpgradeThing[i].NewNumber.text = NextLoopTable[i]
            this.m_UpgradeThing[i].Arrow.gameObject:SetActive(true)
        else
            this.m_UpgradeThing[i].NewNumber.text = ""
            this.m_UpgradeThing[i].Arrow.gameObject:SetActive(false)
        end

        -- 屬性等級圖
        for j = 1, MAX_ATTRIBUTE_POWER_UP do
            if (_PetServerData.m_PetStrengthen[i] >= j) then
                this.m_UpgradeThing[i].SkillPowerBar[j].color = UPGRADE_DONE
            else
                this.m_UpgradeThing[i].SkillPowerBar[j].color = UPGRADE_UNDONE
            end
        end

        -- 屬性升級所需點數 升下一級需要多少點數參照 + 1
        if (ATTRIBUTE_LEVEL_TABLE[_PetServerData.m_PetStrengthen[i] + 1] ~= "max") then

            -- 按鈕改變
            Button.SetEnable(this.m_UpgradeThing[i].m_BtnUpgrade)
            this.m_UpgradeThing[i].m_BtnUpgradeTmp.text = TextData.Get(UPGRADE_STR)

            -- 點數顯數
            this.m_UpgradeThing[i].m_PointCostGroup.gameObject:SetActive(true)
            this.m_UpgradeThing[i].m_PointCost.text = ATTRIBUTE_LEVEL_TABLE[_PetServerData.m_PetStrengthen[i] + 1]

        else

            -- 按鈕改變
            Button.SetDisable(this.m_UpgradeThing[i].m_BtnUpgrade, false)
            this.m_UpgradeThing[i].m_BtnUpgradeTmp.text = TextData.Get(UPGRADE_LIMIT_STR)

            this.m_UpgradeThing[i].m_PointCostGroup.gameObject:SetActive(false)

        end

        this.m_UpgradeThing[i].m_NowLevel = _PetServerData.m_PetStrengthen[i]
    end

    --技能
    local _Count = 0
    for i = PET_FIRSTSKILL, PET_SECONDSKILL do
        _Count = _Count + 1

        -- 寵物技能資料 看有沒有撈到
        local _SkillIdx = _PetServerData.m_PetSkill[_Count]
        local _PetSkillData = SkillData.GetSkillDataByIdx(_SkillIdx)

        -- 找目前的技能是啥 要看看有沒有等級
        if(_PetServerData.m_PetStrengthen[i] ~= 0) then
            for i = 1, _PetServerData.m_PetStrengthen[i] do

                _PetSkillData = SkillData.GetSkillDataByIdx(_PetSkillData.m_NextSkillId)

            end
        end

        -- 還沒滿等的技能
        if(_PetServerData.m_PetStrengthen[i] < MAX_SKILL_LEVEL_UP) then

            -- 開啟下一級 Icon 關閉
            this.m_UpgradeThing[i].m_NewIconParent.gameObject:SetActive(true)

            -- 重新找技能的 ID
            _SkillIdx = _PetServerData.m_PetSkill[_Count]

            -- 強化了幾次
            for j = 1, MAX_SKILL_LEVEL_UP do

                -- 技能 Bar
                this.m_UpgradeThing[i].m_SkillBar[j].color = UPGRADE_UNDONE

                -- 要不要亮
                if(_PetServerData.m_PetStrengthen[i] >= j) then
                    this.m_UpgradeThing[i].m_SkillBar[j].color = UPGRADE_DONE
                end

            end

        -- 技能滿級
        else

            -- 開啟下一級 Icon 關閉
            this.m_UpgradeThing[i].m_NewIconParent.gameObject:SetActive(false)

            for j = 1, MAX_SKILL_LEVEL_UP do
                this.m_UpgradeThing[i].m_SkillBar[j].color = UPGRADE_DONE
            end

        end

        -- 看看有沒有資料
        if(_PetSkillData) then

            -- 刷新技能 Icon
            this.m_UpgradeThing[i].m_NowSkill:RefreshIcon(_PetSkillData.m_Idx)

            -- 存下現在技能資料
            this.m_UpgradeThing[i].m_NowSkillData = _PetSkillData

            -- 填入名字
            this.m_UpgradeThing[i].m_Name.text = SkillData.GetSkillDataByIdx(_PetSkillData.m_Idx).m_Name

        end

        -- 有需要顯示才查
        if(this.m_UpgradeThing[i].m_NewIconParent.gameObject.activeSelf and _PetSkillData) then

            local _NewSkillData = SkillData.GetSkillDataByIdx(_PetSkillData.m_NextSkillId)

            if(_NewSkillData) then
                -- 升級後的寵物技能
                this.m_UpgradeThing[i].m_NewSkill:RefreshIcon(_NewSkillData.m_Idx)

                -- 存下下一級技能的資料
                this.m_UpgradeThing[i].m_NewSkillData = _NewSkillData

            end

        end

        -- 技能升級所需點數 升下一級需要多少點數參照 所以 = 1
        this.m_UpgradeThing[i].m_PointCost.text = SKILL_LEVEL_TABLE[_PetServerData.m_PetStrengthen[i] + 1]

        -- 屬性升級所需點數 升下一級需要多少點數參照 + 1
        if(SKILL_LEVEL_TABLE[_PetServerData.m_PetStrengthen[i] + 1] ~= "max") then

            -- 按鈕改變
            Button.SetEnable(this.m_UpgradeThing[i].m_BtnUpgrade)
            this.m_UpgradeThing[i].m_BtnUpgradeTmp.text = TextData.Get(UPGRADE_STR)

            -- 點數顯數
            this.m_UpgradeThing[i].m_PointCostGroup.gameObject:SetActive(true)
            this.m_UpgradeThing[i].m_PointCost.text = SKILL_LEVEL_TABLE[_PetServerData.m_PetStrengthen[i] + 1]

        else

            -- 按鈕改變
            Button.SetDisable(this.m_UpgradeThing[i].m_BtnUpgrade, false)
            this.m_UpgradeThing[i].m_BtnUpgradeTmp.text = TextData.Get(UPGRADE_LIMIT_STR)

            this.m_UpgradeThing[i].m_PointCostGroup.gameObject:SetActive(false)

        end

        -- 目前等級
        this.m_UpgradeThing[i].m_NowLevel = _PetServerData.m_PetStrengthen[i]

    end
end

--- 設定寵物最愛
function PetUpgrade_Controller.SetPetLove()

    -- 讀取最愛寵物
    local _LovePet = ClientSaveMgr.GetDataValue(EClientSaveDataType.LovePet, LOVE_PET)
    if(_LovePet == nil)then
		_LovePet = {}
	end

    local _SIdStr = tostring(this.m_NowUpgradePetSID)

    -- 查一下寵物有沒有被最愛
    if(_LovePet[_SIdStr] ~= nil) then

        PetMgr.SetPetLoveData(this.m_NowUpgradePetSID, false)
        _LovePet[_SIdStr] = nil

        SpriteMgr.Load(NORMAL_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))

    else

        PetMgr.SetPetLoveData(this.m_NowUpgradePetSID, true)
        _LovePet[_SIdStr] = this.m_NowUpgradePetSID

        SpriteMgr.Load(RED_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))

        -- 中央訊息
        MessageMgr.AddCenterMsg(false, TextData.Get(PET_LOVE))

    end

    -- 改ClientSave
	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.LovePet,LOVE_PET, _LovePet)

    -- 刷新寵物列表
    PetUpgrade_Controller.Option1Click(this.m_PetUpgradeArrange)

end

--- 協定後刷新
function PetUpgrade_Controller.RefreshAfterProtocol()

    -- 刷新寵物列表
    PetUpgrade_Controller.Option1Click(this.m_PetUpgradeArrange)

    -- 用 SID 找列表中資料來做選中寵物的資料刷新
    local _RefreshOnThisPet = {}
    for _k, _v in pairs(this.m_ShowUpgradePetListData) do
        if(_v.m_SID == this.m_NowUpgradePetSID) then
            _RefreshOnThisPet = _v
            break
        end
    end

    -- 重新刷新一下被選中寵物的顯示資料
    PetUpgrade_Controller.PetListClick(_RefreshOnThisPet)

end

--- 突破確認按鈕
function PetUpgrade_Controller.PowerUpButton()

    -- 選起來的寵物
    local _PickPet = {}

    -- 關閉上一個被點選的格子 檢查一下是不是 0
    if(this.m_WindowNowClick ~= 0) then
        m_Table_PetUnit[this.m_WindowNowClick].SelectGObj.gameObject:SetActive(false)
    end

    -- 重製值
    this.m_WindowNowClick = 0

    -- 找點選中寵物的資料
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(this.m_NowUpgradePetSID)

    -- 找玩家身上有的寵物
    local _MyPetData = Pet_Model.ArrangePetListOrder(EPetArrange[1].Power)

    -- 找到符合條件的塞進去
    for _k, _v in pairs(_MyPetData) do

        -- 符合條件 寵物 ID 一樣 S ID 不同
        if(_v.m_PetID == _PetServerData.m_PetID and _v.m_PetSlot ~= _PetServerData.m_PetSlot) then
            table.insert(_PickPet, _v)
        end

    end

    -- 目前至隻寵物有的突破點數
    this.m_WindowNowPoint.text = _PetServerData.m_OverfulfilPoint

    -- 這隻寵物如果突破成功會有的突破點
    if(next(_PickPet)) then
        this.m_WindowNewPoint.text = _PetServerData.m_OverfulfilPoint + UPGRADE_POINT
    end

    -- 如果 _PickPet 裡面有東西才開視窗
    if(next(_PickPet)) then

        -- 設回空值
        this.m_WindowNowClickPetData = {}

        -- 沒點選寵物時按鈕要是灰的
        Button.SetDisable(this.m_YesUseThisPetBtn, true)

        -- 選犧牲寵物的視窗打開
        this.m_PetUpgradeConfirmWindow.gameObject:SetActive(true)

        -- 設定突破確認視窗
        PetUpgrade_Controller.SetUpgradePetWindow(_PickPet)

    else

        -- 中央訊息
        MessageMgr.AddCenterMsg(false, TextData.Get(NO_PET_FOR_UPGRADE))

    end


end

--- 連續強化
function PetUpgrade_Controller.ContinuousUpGrade(iType, iNowLevel)

    -- 減掉次數
    this.m_ContinuousUpGradeTime = this.m_ContinuousUpGradeTime - 1

    -- 要多於 0
    if(this.m_ContinuousUpGradeTime > 0) then

        local _TargetLevel = iNowLevel + 1
        local _CostPoints = ATTRIBUTE_LEVEL_TABLE[_TargetLevel]
        SendProtocol_020._020_07(this.m_NowUpgradePetData.m_SID, this.m_NowUpgradePetData.m_ID, iType, _TargetLevel, _CostPoints)

    else

        -- 中央訊息告知強化成功
        MessageMgr.AddCenterMsg(false, TextData.Get(UPGRADE_SUCCESS_STR))

    end

end

--- 顯示重置
---@param bool iIsShow 是否顯示
function PetUpgrade_Controller.ResetDisplay(iIsShow)

    -- 開啟關閉寵物種類
    if(this.m_UpgradePetType.gameObject.activeSelf ~= iIsShow) then
        this.m_UpgradePetType.gameObject:SetActive(iIsShow)
    end

    -- 改名按鈕
    if(this.m_PetChangeNameBtn.gameObject.activeSelf ~= iIsShow) then
        this.m_PetChangeNameBtn.gameObject:SetActive(iIsShow)
    end

    -- 寵物戰力
    if(this.m_UpgradePowerSet.gameObject.activeSelf ~= iIsShow) then
        this.m_UpgradePowerSet.gameObject:SetActive(iIsShow)
    end

    -- 寵物最愛按鈕
    if(this.m_PetLoveBtn.gameObject.activeSelf ~= iIsShow) then
        this.m_PetLoveBtn.gameObject:SetActive(iIsShow)
    end

    -- 屬性強化內容
    if(this.m_UpgradeContent.gameObject.activeSelf ~= iIsShow) then
        this.m_UpgradeContent.gameObject:SetActive(iIsShow)
    end

    -- 寵物選擇遮罩
    if(this.m_PickPetCover.gameObject.activeSelf == iIsShow) then
        this.m_PickPetCover.gameObject:SetActive(not iIsShow)
    end

    -- 突破按鈕的狀態
    if(iIsShow) then

        Button.SetEnable(this.m_BtnPowerUp)

    else

        Button.SetDisable(this.m_BtnPowerUp, false)

        -- 把資料清空
        this.m_UpgradePetIcon:RefreshIcon(0)
        this.m_UpgradeName.text = ""
        this.m_UpgradeLevel.text = 0

        -- 能量突破階段 圖片
        for i = 1, MAX_POWER_UP do
            local _ChangeColor = POWER_UP_UNDONE

            this.m_PowerLevelImage[i].color = _ChangeColor
        end

    end

    -- 突破能量歸零
    this.m_NowUsablePoint.text = 0


end
