fileFormatVersion: 2
guid: f62941739dd3a3340a86fee3d7470639
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: <PERSON>_<PERSON>_JawEndSHJnt
  - first:
      1: 100004
    second: <PERSON>_<PERSON>_JawSHJnt
  - first:
      1: 100006
    second: <PERSON>_Head_TopSHJnt
  - first:
      1: 100008
    second: <PERSON>_<PERSON>_Clavicle_01_01SHJnt
  - first:
      1: 100010
    second: <PERSON>_l_Ear_01_01SHJnt
  - first:
      1: 100012
    second: <PERSON>_l_Ear_01_02SHJnt
  - first:
      1: 100014
    second: <PERSON>_l_Ear_01_03SHJnt
  - first:
      1: 100016
    second: <PERSON>_<PERSON>_FrontLeg_AnkleSHJnt
  - first:
      1: 100018
    second: <PERSON>_l_FrontLeg_BallSHJnt
  - first:
      1: 100020
    second: <PERSON>_l_FrontLeg_HipSHJnt
  - first:
      1: 100022
    second: <PERSON>_<PERSON>_FrontL<PERSON>_KneeSHJnt
  - first:
      1: 100024
    second: <PERSON>_<PERSON>_FrontLeg_ToeSHJnt
  - first:
      1: 100026
    second: <PERSON>_<PERSON>_HindLeg_AnkleSHJnt
  - first:
      1: 100028
    second: <PERSON>_l_HindLeg_BallSHJnt
  - first:
      1: 100030
    second: Wolf_l_HindLeg_HipSHJnt
  - first:
      1: 100032
    second: Wolf_l_HindLeg_Knee1SHJnt
  - first:
      1: 100034
    second: Wolf_l_HindLeg_Knee2SHJnt
  - first:
      1: 100036
    second: Wolf_l_HindLeg_ToeSHJnt
  - first:
      1: 100038
    second: Wolf_MAINSHJnt
  - first:
      1: 100040
    second: Wolf_Neck_01SHJnt
  - first:
      1: 100042
    second: Wolf_Neck_02SHJnt
  - first:
      1: 100044
    second: Wolf_Neck_TopSHJnt
  - first:
      1: 100046
    second: Wolf_r_Clavicle_01_01SHJnt
  - first:
      1: 100048
    second: Wolf_r_Ear_01_01SHJnt
  - first:
      1: 100050
    second: Wolf_r_Ear_01_02SHJnt
  - first:
      1: 100052
    second: Wolf_r_Ear_01_03SHJnt
  - first:
      1: 100054
    second: Wolf_r_FrontLeg_AnkleSHJnt
  - first:
      1: 100056
    second: Wolf_r_FrontLeg_BallSHJnt
  - first:
      1: 100058
    second: Wolf_r_FrontLeg_HipSHJnt
  - first:
      1: 100060
    second: Wolf_r_FrontLeg_KneeSHJnt
  - first:
      1: 100062
    second: Wolf_r_FrontLeg_ToeSHJnt
  - first:
      1: 100064
    second: Wolf_r_HindLeg_AnkleSHJnt
  - first:
      1: 100066
    second: Wolf_r_HindLeg_BallSHJnt
  - first:
      1: 100068
    second: Wolf_r_HindLeg_HipSHJnt
  - first:
      1: 100070
    second: Wolf_r_HindLeg_Knee1SHJnt
  - first:
      1: 100072
    second: Wolf_r_HindLeg_Knee2SHJnt
  - first:
      1: 100074
    second: Wolf_r_HindLeg_ToeSHJnt
  - first:
      1: 100076
    second: Wolf_ROOTSHJnt
  - first:
      1: 100078
    second: Wolf_Spine_01SHJnt
  - first:
      1: 100080
    second: Wolf_Spine_02SHJnt
  - first:
      1: 100082
    second: Wolf_Spine_03SHJnt
  - first:
      1: 100084
    second: Wolf_Spine_04SHJnt
  - first:
      1: 100086
    second: Wolf_Spine_TopSHJnt
  - first:
      1: 100088
    second: Wolf_Tail_01_01SHJnt
  - first:
      1: 100090
    second: Wolf_Tail_01_02SHJnt
  - first:
      1: 100092
    second: Wolf_Tail_01_03SHJnt
  - first:
      1: 100094
    second: Wolf_Tail_01_04SHJnt
  - first:
      1: 100096
    second: Wolf_Tail_01_05SHJnt
  - first:
      1: 100098
    second: Wolf_Tongue_01_01SHJnt
  - first:
      1: 100100
    second: Wolf_Tongue_01_02SHJnt
  - first:
      1: 100102
    second: Wolf_Tongue_01_03SHJnt
  - first:
      1: 100104
    second: Wolf_Tongue_01_04SHJnt
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Wolf_Head_JawEndSHJnt
  - first:
      4: 400004
    second: Wolf_Head_JawSHJnt
  - first:
      4: 400006
    second: Wolf_Head_TopSHJnt
  - first:
      4: 400008
    second: Wolf_l_Clavicle_01_01SHJnt
  - first:
      4: 400010
    second: Wolf_l_Ear_01_01SHJnt
  - first:
      4: 400012
    second: Wolf_l_Ear_01_02SHJnt
  - first:
      4: 400014
    second: Wolf_l_Ear_01_03SHJnt
  - first:
      4: 400016
    second: Wolf_l_FrontLeg_AnkleSHJnt
  - first:
      4: 400018
    second: Wolf_l_FrontLeg_BallSHJnt
  - first:
      4: 400020
    second: Wolf_l_FrontLeg_HipSHJnt
  - first:
      4: 400022
    second: Wolf_l_FrontLeg_KneeSHJnt
  - first:
      4: 400024
    second: Wolf_l_FrontLeg_ToeSHJnt
  - first:
      4: 400026
    second: Wolf_l_HindLeg_AnkleSHJnt
  - first:
      4: 400028
    second: Wolf_l_HindLeg_BallSHJnt
  - first:
      4: 400030
    second: Wolf_l_HindLeg_HipSHJnt
  - first:
      4: 400032
    second: Wolf_l_HindLeg_Knee1SHJnt
  - first:
      4: 400034
    second: Wolf_l_HindLeg_Knee2SHJnt
  - first:
      4: 400036
    second: Wolf_l_HindLeg_ToeSHJnt
  - first:
      4: 400038
    second: Wolf_MAINSHJnt
  - first:
      4: 400040
    second: Wolf_Neck_01SHJnt
  - first:
      4: 400042
    second: Wolf_Neck_02SHJnt
  - first:
      4: 400044
    second: Wolf_Neck_TopSHJnt
  - first:
      4: 400046
    second: Wolf_r_Clavicle_01_01SHJnt
  - first:
      4: 400048
    second: Wolf_r_Ear_01_01SHJnt
  - first:
      4: 400050
    second: Wolf_r_Ear_01_02SHJnt
  - first:
      4: 400052
    second: Wolf_r_Ear_01_03SHJnt
  - first:
      4: 400054
    second: Wolf_r_FrontLeg_AnkleSHJnt
  - first:
      4: 400056
    second: Wolf_r_FrontLeg_BallSHJnt
  - first:
      4: 400058
    second: Wolf_r_FrontLeg_HipSHJnt
  - first:
      4: 400060
    second: Wolf_r_FrontLeg_KneeSHJnt
  - first:
      4: 400062
    second: Wolf_r_FrontLeg_ToeSHJnt
  - first:
      4: 400064
    second: Wolf_r_HindLeg_AnkleSHJnt
  - first:
      4: 400066
    second: Wolf_r_HindLeg_BallSHJnt
  - first:
      4: 400068
    second: Wolf_r_HindLeg_HipSHJnt
  - first:
      4: 400070
    second: Wolf_r_HindLeg_Knee1SHJnt
  - first:
      4: 400072
    second: Wolf_r_HindLeg_Knee2SHJnt
  - first:
      4: 400074
    second: Wolf_r_HindLeg_ToeSHJnt
  - first:
      4: 400076
    second: Wolf_ROOTSHJnt
  - first:
      4: 400078
    second: Wolf_Spine_01SHJnt
  - first:
      4: 400080
    second: Wolf_Spine_02SHJnt
  - first:
      4: 400082
    second: Wolf_Spine_03SHJnt
  - first:
      4: 400084
    second: Wolf_Spine_04SHJnt
  - first:
      4: 400086
    second: Wolf_Spine_TopSHJnt
  - first:
      4: 400088
    second: Wolf_Tail_01_01SHJnt
  - first:
      4: 400090
    second: Wolf_Tail_01_02SHJnt
  - first:
      4: 400092
    second: Wolf_Tail_01_03SHJnt
  - first:
      4: 400094
    second: Wolf_Tail_01_04SHJnt
  - first:
      4: 400096
    second: Wolf_Tail_01_05SHJnt
  - first:
      4: 400098
    second: Wolf_Tongue_01_01SHJnt
  - first:
      4: 400100
    second: Wolf_Tongue_01_02SHJnt
  - first:
      4: 400102
    second: Wolf_Tongue_01_03SHJnt
  - first:
      4: 400104
    second: Wolf_Tongue_01_04SHJnt
  - first:
      74: 7400000
    second: MS_Dog_Expr_01
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: MS_Dog_Expr_01
      takeName: Take 001
      internalID: 0
      firstFrame: 1
      lastFrame: 210
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e14fa0df045793d46936fbccb7ac21a8,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
