---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---AtkIcon 繼承自 BasicIcon
---@class AtkIcon
---author KK
---version 1.0
---since [HEM 2.0]
---date 2022.9.30
AtkIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function AtkIcon.Init( iParent )
    AtkIcon.LoadResources( "AtkIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

local function OnDrag(iSelf, iSender)
    if iSelf.m_Idx ~= 0 then
        iSelf:OnDrag(iSender)
    end
end

---建立新的 AtkIcon
function AtkIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
    local _Icon
    local _SkillData = SkillData.GetSkillDataByIdx( iIdx )
    


    _Icon = BasicIcon:New( m_Tmp, EIconType.Skill, 0, iParent, iWidth, 
    AtkIcon.OnClick, 
    AtkIcon.OnPointerDown,
    AtkIcon.OnPointerUp)

    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    _Icon:SetNeedOpenHint(true)
         :SetNeedLongPress(true)
         :SetClickTwice(true)
         :AddEvent(OnDrag, EventTriggerType.Drag)
    
    setmetatable( _Icon, { __index = self } )

    ---Loop CD
    if _Icon.m_LoopCD_Ctrl == nil then
        _Icon.m_LoopCD_Ctrl = _Icon.transform:Find( "Image_LoopCD" ):GetComponent( typeof( ProgressCtrl ) )
        _Icon:SetObjectActive(_Icon.m_LoopCD_Ctrl, false)
    end

    if _Icon.m_OverCharge == nil then
        _Icon.m_OverCharge = {}
        _Icon.m_OverCharge.m_Trans_OverCharge = _Icon.transform:Find( "Trans_OverCharge" ).transform

        for i = 1, BattleSetting.m_OverChargeMaxCount do
            _Icon.m_OverCharge[i] = {}
            _Icon.m_OverCharge[i].m_Trans_OverCharge = _Icon.m_OverCharge.m_Trans_OverCharge:Find( "Trans_OverCharge_" .. i ).transform
            _Icon.m_OverCharge[i].m_Image_OverCharge = _Icon.m_OverCharge[i].m_Trans_OverCharge:Find( "Image_OverCharge_" .. i ):GetComponent( typeof( Image ) )
            _Icon:SetObjectActive(_Icon.m_OverCharge[i].m_Trans_OverCharge, false)
        end

        _Icon:SetObjectActive(_Icon.m_OverCharge.m_Trans_OverCharge, false)
    end

    if _Icon.m_TMP_HotKeyText == nil then
        _Icon.m_TMP_HotKeyText = _Icon.transform:Find( "TMP_HotkeyText" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        -- 固定尺寸，不接受控制 (不然好醜)
        local _TempScale = 1 / _Icon.m_IconFixScale
        _Icon.m_TMP_HotKeyText.transform.localScale = Vector3( _TempScale, _TempScale, _TempScale )
        _Icon:SetObjectActive(_Icon.m_TMP_HotKeyText, false)
    end

    _Icon.m_SkillData = _SkillData
    _SkillData = nil

    _Icon.m_OnClickFunc = iOnClick
    _Icon.m_OnClickParam = iOnClickParam
    _Icon.m_OnPointerDownFunc = iOnPointerDown
    _Icon.m_OnPointerUpFunc = iOnPointerUp
    _Icon.m_OnUpdateFunc = iOnDrag
    ---依照 技能 設定相關資料
    _Icon:RefreshIcon(iIdx)

    --- 充能技能函示
    _Icon.OverChargeFunc = function()
        for i = 1, _Icon.m_SkillData.m_PowerCount do
            local _OverChargeData = BattleMgr.m_OverChargeData[_Icon.m_Idx].m_OverChargeData
            if _OverChargeData == nil then
                do return end
            end

            _Icon.m_OverCharge[i].m_Image_OverCharge.fillAmount = _Icon.m_SkillData.m_PowerCount - _OverChargeData.m_UseCount >= i and 1 or 0
            
            -- 正在 CD 的那顆
            if _Icon.m_SkillData.m_PowerCount - _OverChargeData.m_UseCount + 1 == i then
                local _fillAmount = 1 - (
                    (BattleMgr.m_OverChargeData[_Icon.m_Idx].m_OverChargeCDTime - HEMTimeMgr.m_ServerTime).TotalMilliseconds / _Icon.m_SkillData.m_TimeParm)
                _Icon.m_OverCharge[i].m_Image_OverCharge.fillAmount = _fillAmount
            end
        end
    end
    
    --- 登記觀察者 更新武功
    GStateObserverManager.Register(EStateObserver.UpdateWugong, _Icon)

    --- 登記觀察者 更新按鈕
    GStateObserverManager.Register(EStateObserver.UpdateInputKey, _Icon)

    return _Icon
end

function AtkIcon:CheckCanUse()
    if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
        BattleMgr.ShowLog("CD 中")
        return false
    end
    
    return true
end

function AtkIcon:OnClick(iEventData)
    if not self:CheckCanUse() then
        return
    end
    
    if self.m_OnClickFunc then
        self.m_OnClickFunc(self.m_OnClickParam)
    end
end

function AtkIcon:OnPointerDown(iEventData)
    if not self:CheckCanUse() then
        return
    end

    local _TempPos = iEventData.position

    if self.m_SkillData.m_UseType == EHotKeyUseKind.Power then
        self.m_OnPointerDownFunc(_TempPos)
    elseif self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
        if self.m_OnPointerDownFunc(_TempPos) then
            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.OnPointerDown( self.gameObject.transform.position, _TempPos)
            end
        end
    elseif self.m_SkillData.m_UseType == EHotKeyUseKind.AppointPos then
        if self.m_OnPointerDownFunc(_TempPos) then
            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.OnPointerDown( self.gameObject.transform.position, _TempPos)
            end
        end
    end
end

function AtkIcon:OnPointerUp(iEventData)
    if self.m_SkillData.m_UseType == EHotKeyUseKind.Power then
        self.m_OnPointerUpFunc()
    elseif self.m_SkillData then
        if self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
            if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
                D.Log("CD 中")
                return
            end
            
            self.m_OnPointerUpFunc(iEventData.position)
        elseif self.m_SkillData.m_UseType == EHotKeyUseKind.AppointPos then
            if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
                D.Log("CD 中")
                return
            end
            
            self.m_OnPointerUpFunc(iEventData.position)
        end
    end
end

function AtkIcon:OnDrag(iEventData)
    if self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
        -- 戰鬥按紐搖桿
        local _Pos = Vector2.New(iEventData.position.x, iEventData.position.y)
        
        if(_Pos ~= nil) then
            if not (HotKey_Controller and HotKey_Controller.m_Trans_HoverObj) then
                do return end
            end
            local _IsSuccess
            _IsSuccess, _Pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
                HotKey_Controller.m_Trans_HoverObj, _Pos, UIMgr.m_UICamera, _Pos )
        end
            
        if self.m_OnUpdateFunc(_Pos) then
            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.OnUpdateHover(_Pos)
            end
        end
    elseif self.m_SkillData.m_UseType == EHotKeyUseKind.AppointPos then
        -- 戰鬥按紐搖桿
        local _Pos = Vector2.New(iEventData.position.x, iEventData.position.y)
        
        if(_Pos ~= nil) then
            if not (HotKey_Controller and HotKey_Controller.m_Trans_HoverObj) then
                do return end
            end
            local _IsSuccess
            _IsSuccess, _Pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
                HotKey_Controller.m_Trans_HoverObj, _Pos, UIMgr.m_UICamera, _Pos )
        end
            
        if self.m_OnUpdateFunc(_Pos) then
            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.OnUpdateHover(_Pos)
            end
        end
    end
end

---重設技能 Icon
---@param iIdx number 技能 Idx
function AtkIcon:RefreshIcon(iIdx)
    if AtkIcon.m_IconHotkeyText == nil then
        -- 初始化字串 icon 說明字串
        AtkIcon.m_IconHotkeyText = {}
        for _Key, _Value in pairs(IconSetting.m_IconHotkeyText) do
            AtkIcon.m_IconHotkeyText[_Key] = TextData.Get(_Value)
            if ProjectMgr.IsPC() then
                AtkIcon.m_IconHotkeyText[_Key] = AtkIcon.m_IconHotkeyText[_Key] .. IconSetting.m_IconHotkeyPCKeyText
            end
        end
    end

    self.m_SkillData = iIdx ~= 0 and SkillData.GetSkillDataByIdx( iIdx ) or nil
    ---依照 物品 設定相關資料
    if self.m_SkillData then
        self.m_KeyCode = EHotkeyArea.BattleAtkArea .. "_" .. self.m_SkillData.m_SkillType
        local _keyCodeName = InputMgr.GetInputKeyCodeName(HotKey_Controller, self.m_KeyCode)

        local _TempString = GString.Format(AtkIcon.m_IconHotkeyText[self.m_SkillData.IconHotkeyTextId], _keyCodeName)
        self.m_TMP_HotKeyText.text = 
            AtkIcon.m_IconHotkeyText[self.m_SkillData.m_UseType] ~= nil and _TempString or "nil"

        if self.m_SkillData.m_SkillType == SkillData.ESkillType.NormalAtk then
            -- 普攻的文字需要往下移 (因為他的底框比其他 icon 大很多)
            self.m_TMP_HotKeyText.transform.localPosition = IconSetting.m_IconHotkeyText_NormalPos
        end

        self:SetObjectActive(self.m_TMP_HotKeyText, true)
        self:RefreshBasicIcon(iIdx, self.m_SkillData:GetIconTextureName(true), true)
        self:SetName(self.m_SkillData:SkillName())
            :SetOnSelectText()
            :SetFrameType(self.m_SkillData.m_SkillType)
            :SetOverCharge()
    else
        InputMgr.UnRegistKey(HotKey_Controller, self.m_KeyCode, self)

        self.m_KeyCode = nil

        self.m_TMP_HotKeyText.text = ""
        self:SetObjectActive(self.m_TMP_HotKeyText, false)
        self:RefreshBasicIcon(iIdx)
        self:SetName()
            :SetOnSelectText()
            :SetFrameType(SkillData.ESkillType.Skill_1)
            :SetOverCharge()
    end
end

--- 覆寫獲取外框物件
function AtkIcon:GetFrameObj(iRank)
    local _FrameObjTable = IconMgr.m_FrameIcon.m_SkillFrameIcon[iRank]
    if not _FrameObjTable then
        return nil
    end

    return _FrameObjTable.m_GameObject
end

--- 覆寫設定外框圖
function AtkIcon:SetFrameImage(iRarity)
    if iRarity == SkillData.ESkillType.CoreSkill then
        if self.m_FrameIcon then
            for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
                local _Image = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
                local _WeaponType = PlayerData.GetWeaponType()
                -- 核心技能 外圍變色
                _Image.color = IconSetting.ESkillCoreFrameColor[_WeaponType][key1]
            end
        end
    end

    return self
end

---刷新CD遮罩
---覆寫BasicIcon
function AtkIcon:UpdateCD()
    if self.m_Idx == 0 then
        self:SetObjectActive(self.m_Image_CD, false)
        do return end
    end

    if self.m_SkillData and SkillData.CheckIsBroken(self.m_SkillData.m_SkillType) then
        -- 技能壞了，不需要顯示不能施放的遮罩
        self:SetObjectActive(self.m_Image_CD, false)
        do return end
    end

    -- CD
    local _Time, _Percent = CDMgr.GetInfo( self.m_Type, self.m_Idx )

    -- CD 遮罩顏色
    local _Color = Color.Black
    
    local _SkillData = SkillData.GetSkillDataByIdx(self.m_Idx)
    if _SkillData then
        if _SkillData.m_UseType == EHotKeyUseKind.Loop_2 then
            -- 循環類招式 2 等待放招的技能特別處理
            if _SkillData.m_SkillOrder ~= 1 then
                if ( _SkillData.m_TimeParm * 0.001 - _Time ) <= BattleSetting.m_Loop2CDTime then
                    _Time = BattleSetting.m_Loop2CDTime - ( _SkillData.m_TimeParm * 0.001 - _Time )
                    _Percent = _Time / BattleSetting.m_Loop2CDTime
                    self:SetObjectActive(self.m_LoopCD_Ctrl, false)
                    self.m_TMP_CD.text = string.format( "%.1f", _Time )
                    self:SetObjectActive(self.m_TMP_CD, _Time > 0)
                else
                    -- self.m_TMP_CD.text = string.format( "%.1f", _Time )
                    self:SetObjectActive(self.m_TMP_CD, false)
                    self:SetObjectActive(self.m_LoopCD_Ctrl, _Percent > 0)
                    self.m_LoopCD_Ctrl:FillProgress(_Percent)
                    
                    return
                end
            else
                self:SetObjectActive(self.m_LoopCD_Ctrl, false)
                self.m_TMP_CD.text = string.format( "%.1f", _Time )
                self:SetObjectActive(self.m_TMP_CD, _Time > 0)
            end
        -- elseif _SkillData.m_PowerCount > 0 then
        --     -- 充能型技能需要特別處理
        --     if BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeData ~= nil then
        --         if self.m_SkillData.m_PowerCount == BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeData.m_UseCount then
        --             local _OverChargeTime = (BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime - HEMTimeMgr.m_ServerTime).TotalMilliseconds * 0.001
        --             if _OverChargeTime > _Time then
        --                 _Time = _OverChargeTime
        --                 _Percent = _Time / (self.m_SkillData.m_TimeParm * 0.001)
        --             end
        --         end

        --         self.m_TMP_CD.text = string.format( "%.1f", _Time )
        --         self:SetObjectActive(self.m_TMP_CD, _Time > 0)
        --     end
        elseif _SkillData.m_CheckBuffId ~= 0 and _SkillData.m_CheckBuffStacks ~= 0 then
            ---需要消耗buff層數的技能 需要特別處理
            ---取出目前特定buff疊加到多少層數 是否超過可釋放門檻
            local _BuffLayer, _EnoughLayer = BuffMgr.CheckBuffLayer(_SkillData.m_CheckBuffId,_SkillData.m_CheckBuffStacks)
            ---如果冷卻中 優先判斷冷卻中
            if _Time > 0 then
                self.m_TMP_CD.text = string.format( "%.1f", _Time )
                self:SetObjectActive(self.m_TMP_CD, true)
            else
                ---不再冷卻中 則依據當前buff層 來判斷是否可以施放
                ---TMP冷卻時間 物件關閉
                self:SetObjectActive(self.m_TMP_CD, false)
                ---根據層數是否充足設定遮罩用percent
                if _EnoughLayer then
                    _Percent = 0
                else
                    _Percent = 100
                end
            end
        else
            self.m_TMP_CD.text = string.format( "%.1f", _Time )
            self:SetObjectActive(self.m_TMP_CD, _Time > 0)
        end
    end

    _Color.a = IconSetting.m_CDMaskAlpha
    
    self.m_Image_CD.fillAmount = _Percent
     self:SetObjectActive(self.m_Image_CD, _Percent > 0)
    self.m_Image_CD.color = _Color
end

function AtkIcon:SetOverCharge()
    if self.m_SkillData ~= nil then
        if self.m_SkillData.m_PowerCount > 1 then
            for i = 1, BattleSetting.m_OverChargeMaxCount do
                self:SetObjectActive(self.m_OverCharge[i].m_Trans_OverCharge, i <= self.m_SkillData.m_PowerCount)
            end
            self:SetObjectActive(self.m_OverCharge.m_Trans_OverCharge, true)
            self.m_OverCharge.m_Trans_OverCharge.transform.localEulerAngles = Vector3.New(0, 0, BattleSetting.m_OverChargeRotate[self.m_SkillData.m_PowerCount])
            
            BattleMgr.m_OverChargeData[self.m_Idx] = self
        else
            self:SetObjectActive(self.m_OverCharge.m_Trans_OverCharge, false)
        end
    else
        self:SetObjectActive(self.m_OverCharge.m_Trans_OverCharge, false)
    end
end

--- 更新充能資料
function AtkIcon:UpdateOverCharge()
    if BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime == nil then
        BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime = HEMTimeMgr.m_ServerTime:AddMilliseconds(self.m_SkillData.m_TimeParm)
    end

    local _OverChargeData = BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeData

    if BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime ~= nil then
        if _OverChargeData.m_Kind == 2 then
            BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime = HEMTimeMgr.m_ServerTime:AddMilliseconds(self.m_SkillData.m_TimeParm)
        end
        -- HEMTimeMgr.RegisterFixUpdate(self.OverChargeFunc)
    end

    -- m_UseCount == 0 就是充能滿的
    if _OverChargeData.m_UseCount == 0 then
        HEMTimeMgr.UnregisterFixUpdate(self.OverChargeFunc)
        BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeCDTime = nil
        BattleMgr.m_OverChargeData[self.m_Idx].m_OverChargeData = nil
        BattleMgr.m_OverChargeData[self.m_Idx].m_IsCD = false
    elseif BattleMgr.m_OverChargeData[self.m_Idx].m_IsCD == false then
        BattleMgr.m_OverChargeData[self.m_Idx].m_IsCD = true
        HEMTimeMgr.RegisterFixUpdate(self.OverChargeFunc)
    end
end


---AtkIcon 登記的觀察者 數據有變化是要做甚麼
---@param iEStateObserver EStateObserver 有變化的項目是甚麼 EStateObserver
function AtkIcon:OnStateChanged(iEStateObserver)
    ---更新武功
    if iEStateObserver == EStateObserver.UpdateWugong then
        self:RefreshIcon(self.m_Idx)
    elseif iEStateObserver == EStateObserver.UpdateInputKey then
        self:RefreshIcon(self.m_Idx)
    end
end
