fileFormatVersion: 2
guid: e4720274fb47dfd4db682a3f8c05d478
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Chicken_Chest_01_01SHJnt
  - first:
      1: 100002
    second: Chicken_Chest_01_02SHJnt
  - first:
      1: 100004
    second: Chicken_Chest_01_03SHJnt
  - first:
      1: 100006
    second: Chicken_Comb_01_01SHJnt
  - first:
      1: 100008
    second: Chicken_Comb_01_02SHJnt
  - first:
      1: 100010
    second: Chicken_Head_JawEndSHJnt
  - first:
      1: 100012
    second: Chicken_Head_JawSHJnt
  - first:
      1: 100014
    second: Chicken_Head_TopSHJnt
  - first:
      1: 100016
    second: Chicken_l_Leg_AnkleSHJnt
  - first:
      1: 100018
    second: Chicken_l_Leg_BallSHJnt
  - first:
      1: 100020
    second: Chicken_l_Leg_HipSHJnt
  - first:
      1: 100022
    second: Chicken_l_Leg_KneeSHJnt
  - first:
      1: 100024
    second: Chicken_l_Leg_ToeSHJnt
  - first:
      1: 100026
    second: Chicken_l_Toe_01_01SHJnt
  - first:
      1: 100028
    second: Chicken_l_Toe_01_02SHJnt
  - first:
      1: 100030
    second: Chicken_l_Toe_01_03SHJnt
  - first:
      1: 100032
    second: Chicken_l_Toe_02_01SHJnt
  - first:
      1: 100034
    second: Chicken_l_Toe_02_02SHJnt
  - first:
      1: 100036
    second: Chicken_l_Toe_02_03SHJnt
  - first:
      1: 100038
    second: Chicken_l_Toe_03_01SHJnt
  - first:
      1: 100040
    second: Chicken_l_Toe_03_02SHJnt
  - first:
      1: 100042
    second: Chicken_l_Toe_03_03SHJnt
  - first:
      1: 100044
    second: Chicken_l_Wattle_01_01SHJnt
  - first:
      1: 100046
    second: Chicken_l_Wattle_01_02SHJnt
  - first:
      1: 100048
    second: Chicken_MAINSHJnt
  - first:
      1: 100050
    second: Chicken_Neck_01_01SHJnt
  - first:
      1: 100052
    second: Chicken_Neck_01_02SHJnt
  - first:
      1: 100054
    second: Chicken_Neck_01_03SHJnt
  - first:
      1: 100056
    second: Chicken_r_Leg_AnkleSHJnt
  - first:
      1: 100058
    second: Chicken_r_Leg_BallSHJnt
  - first:
      1: 100060
    second: Chicken_r_Leg_HipSHJnt
  - first:
      1: 100062
    second: Chicken_r_Leg_KneeSHJnt
  - first:
      1: 100064
    second: Chicken_r_Leg_ToeSHJnt
  - first:
      1: 100066
    second: Chicken_r_Toe_01_01SHJnt
  - first:
      1: 100068
    second: Chicken_r_Toe_01_02SHJnt
  - first:
      1: 100070
    second: Chicken_r_Toe_01_03SHJnt
  - first:
      1: 100072
    second: Chicken_r_Toe_02_01SHJnt
  - first:
      1: 100074
    second: Chicken_r_Toe_02_02SHJnt
  - first:
      1: 100076
    second: Chicken_r_Toe_02_03SHJnt
  - first:
      1: 100078
    second: Chicken_r_Toe_03_01SHJnt
  - first:
      1: 100080
    second: Chicken_r_Toe_03_02SHJnt
  - first:
      1: 100082
    second: Chicken_r_Toe_03_03SHJnt
  - first:
      1: 100084
    second: Chicken_r_Wattle_01_01SHJnt
  - first:
      1: 100086
    second: Chicken_r_Wattle_01_02SHJnt
  - first:
      1: 100088
    second: Chicken_ROOTSHJnt
  - first:
      1: 100090
    second: Chicken_Tail_01_01SHJnt
  - first:
      1: 100092
    second: Chicken_Tail_01_02SHJnt
  - first:
      1: 100094
    second: Chicken_Tail_01_03SHJnt
  - first:
      1: 100096
    second: //RootNode
  - first:
      4: 400000
    second: Chicken_Chest_01_01SHJnt
  - first:
      4: 400002
    second: Chicken_Chest_01_02SHJnt
  - first:
      4: 400004
    second: Chicken_Chest_01_03SHJnt
  - first:
      4: 400006
    second: Chicken_Comb_01_01SHJnt
  - first:
      4: 400008
    second: Chicken_Comb_01_02SHJnt
  - first:
      4: 400010
    second: Chicken_Head_JawEndSHJnt
  - first:
      4: 400012
    second: Chicken_Head_JawSHJnt
  - first:
      4: 400014
    second: Chicken_Head_TopSHJnt
  - first:
      4: 400016
    second: Chicken_l_Leg_AnkleSHJnt
  - first:
      4: 400018
    second: Chicken_l_Leg_BallSHJnt
  - first:
      4: 400020
    second: Chicken_l_Leg_HipSHJnt
  - first:
      4: 400022
    second: Chicken_l_Leg_KneeSHJnt
  - first:
      4: 400024
    second: Chicken_l_Leg_ToeSHJnt
  - first:
      4: 400026
    second: Chicken_l_Toe_01_01SHJnt
  - first:
      4: 400028
    second: Chicken_l_Toe_01_02SHJnt
  - first:
      4: 400030
    second: Chicken_l_Toe_01_03SHJnt
  - first:
      4: 400032
    second: Chicken_l_Toe_02_01SHJnt
  - first:
      4: 400034
    second: Chicken_l_Toe_02_02SHJnt
  - first:
      4: 400036
    second: Chicken_l_Toe_02_03SHJnt
  - first:
      4: 400038
    second: Chicken_l_Toe_03_01SHJnt
  - first:
      4: 400040
    second: Chicken_l_Toe_03_02SHJnt
  - first:
      4: 400042
    second: Chicken_l_Toe_03_03SHJnt
  - first:
      4: 400044
    second: Chicken_l_Wattle_01_01SHJnt
  - first:
      4: 400046
    second: Chicken_l_Wattle_01_02SHJnt
  - first:
      4: 400048
    second: Chicken_MAINSHJnt
  - first:
      4: 400050
    second: Chicken_Neck_01_01SHJnt
  - first:
      4: 400052
    second: Chicken_Neck_01_02SHJnt
  - first:
      4: 400054
    second: Chicken_Neck_01_03SHJnt
  - first:
      4: 400056
    second: Chicken_r_Leg_AnkleSHJnt
  - first:
      4: 400058
    second: Chicken_r_Leg_BallSHJnt
  - first:
      4: 400060
    second: Chicken_r_Leg_HipSHJnt
  - first:
      4: 400062
    second: Chicken_r_Leg_KneeSHJnt
  - first:
      4: 400064
    second: Chicken_r_Leg_ToeSHJnt
  - first:
      4: 400066
    second: Chicken_r_Toe_01_01SHJnt
  - first:
      4: 400068
    second: Chicken_r_Toe_01_02SHJnt
  - first:
      4: 400070
    second: Chicken_r_Toe_01_03SHJnt
  - first:
      4: 400072
    second: Chicken_r_Toe_02_01SHJnt
  - first:
      4: 400074
    second: Chicken_r_Toe_02_02SHJnt
  - first:
      4: 400076
    second: Chicken_r_Toe_02_03SHJnt
  - first:
      4: 400078
    second: Chicken_r_Toe_03_01SHJnt
  - first:
      4: 400080
    second: Chicken_r_Toe_03_02SHJnt
  - first:
      4: 400082
    second: Chicken_r_Toe_03_03SHJnt
  - first:
      4: 400084
    second: Chicken_r_Wattle_01_01SHJnt
  - first:
      4: 400086
    second: Chicken_r_Wattle_01_02SHJnt
  - first:
      4: 400088
    second: Chicken_ROOTSHJnt
  - first:
      4: 400090
    second: Chicken_Tail_01_01SHJnt
  - first:
      4: 400092
    second: Chicken_Tail_01_02SHJnt
  - first:
      4: 400094
    second: Chicken_Tail_01_03SHJnt
  - first:
      4: 400096
    second: //RootNode
  - first:
      74: 7400000
    second: Die
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Die
      takeName: Take 001
      internalID: 0
      firstFrame: 1
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: bd02583cf8bd2d145ab8217d0969ea9b,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
