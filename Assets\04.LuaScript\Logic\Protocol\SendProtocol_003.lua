﻿SendProtocol_003 = {}

---3-1 投點屬性種類(1)[1.力量2.敏捷3.體魄4.真原5.精神]+目標等級value(2)+花費總點數point(2)
---@param iMainAttributeType EBaseAttributes 主屬性種類
---@param iGoalValue uint 要提到到幾等
---@param iConsumePoint uint 總共花費幾點
function SendProtocol_003._001(iMainAttributeType, iGoalValue,iConsumePoint)
    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte(iMainAttributeType)
    _Packet:WriteUInt16(iGoalValue)
    _Packet:WriteUInt16(iConsumePoint)
    
    NetworkMgr.Send(3, 1, _Packet)

end

--- 3-5-1 習武 + kind(1) + 習武老師編號(2) + 武功索引(2) + 武功編號(2) + 物品索引(2)[使用記憶卡或隨身碟 =0不使用] + 物品編號(4) + 連續升幾級(1)
---@param number iTeacherID     習武老師編號
---@param number iWugongIndex   武功索引
---@param number iWugongID      武功編號
---@param number iItemIndex     物品索引
---@param number iItemID        物品編號
---@param number iContinuousLVing   連續升幾級
function SendProtocol_003._005(iTeacherID, iWugongIndex, iWugongID, iItemIndex, iItemID, iContinuousLVing)

    local _Packet = Network.Packet.GetTempPacket()
    local _Kind = 1
    _Packet:WriteByte(_Kind)
    _Packet:WriteUInt16(iTeacherID)
    _Packet:WriteUInt16(iWugongIndex)
    _Packet:WriteUInt16(iWugongID)
    _Packet:WriteUInt16(iItemIndex)
    _Packet:WriteUInt32(iItemID)
    _Packet:WriteByte(iContinuousLVing)

    NetworkMgr.Send(3, 5, _Packet)

end

--- 3-5-2 UI習武 + kind(1) + 武功索引(2) + 武功編號(2) + 物品索引(2)[使用記憶卡或隨身碟 =0不使用] + 物品編號(4) + 連續升幾級(1)
---@param number iWugongIndex   武功索引
---@param number iWugongID      武功編號
---@param number iItemIndex     物品索引
---@param number iItemID        物品編號
---@param number iContinuousLVing   連續升幾級
function SendProtocol_003._005_2(iWugongIndex, iWugongID, iItemIndex, iItemID, iContinuousLVing)

    local _Packet = Network.Packet.GetTempPacket()
    local _Kind = 2
    _Packet:WriteByte(_Kind)
    _Packet:WriteUInt16(iWugongIndex)
    _Packet:WriteUInt16(iWugongID)
    _Packet:WriteUInt16(iItemIndex)
    _Packet:WriteUInt32(iItemID)
    _Packet:WriteByte(iContinuousLVing)
    
    NetworkMgr.Send(3, 5, _Packet)

end

--- 12.稱號相關 + kind(1)
--- 1.---
--- 2.裝備某稱號+編號(2)
---@param iKind number 要送的協定index
---@param iTitleID number 要替換成哪個ID
function SendProtocol_003._012(iKind, iTitleID)
    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte(iKind)

    if iKind == 2 then
        _Packet:WriteUInt16(iTitleID)
    end
    D.Log("送出協定 3-12 iKind = " .. iKind .. " 稱號ID = " .. iTitleID)
    NetworkMgr.Send(3, 12, _Packet)
end


--- 3-10 查詢玩家魅力值 + 名字長度(4) + 名字(?)
---@param iName string 要查詢魅力值的玩家角色
function SendProtocol_003._010(iName)
    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteString(iName)
    
    NetworkMgr.Send(3, 10, _Packet)
end

--- 3-13 頭圖 + kind(1) [1.設定頭像(2) 2.設定頭框(1)]
---@param iAvatarType EAvatarListDataType 要修改的類別
---@param iID numbre 要替換成哪個ID
function SendProtocol_003._013(iAvatarType, iID)
    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte(iAvatarType)

    if EAvatarListDataType.HeadImage then
        _Packet:WriteUInt16(iID)
    elseif EAvatarListDataType.FrameImage then
        _Packet:WriteByte(iID)
    end
    
    NetworkMgr.Send(3, 13, _Packet)

end

--- 3-14 我的探索積分可以領獎+獎勵編號(2)
---@param iID number 獎勵編號
function SendProtocol_003._014(iID)

    local _Packet = Network.Packet.GetTempPacket()

    _Packet:WriteUInt16(iID)

    NetworkMgr.Send(3, 14, _Packet)

end

--- 3-15 任務章節領獎+章節(1)
---@param iID number 章節
function SendProtocol_003._015(iID)

    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte(iID)
    
    NetworkMgr.Send(3, 15, _Packet)

end

--- 3-16 再造增幅系統
---@param iKind number 功能
function SendProtocol_003._016(iKind, iPacket)

    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte( iKind )

    -- 1.升級改造 + 部位(1)
    if iKind == 1 then
        _Packet:WriteByte(iPacket.m_PartIdx)
    
    -- 2.調校<<數值(1)>>*6
    elseif iKind == 2 then
        for i = 1, ReengineeringGrowth_Model.SIX_PART do
            _Packet:WriteByte(iPacket.m_AdjustValue[i])
        end

    -- 3.神經覺醒+覺醒位置(1)	
    elseif iKind == 3 then
        _Packet:WriteByte(iPacket.m_PosIdx)
        
    -- 4.加速改造 + 部位(1)
    elseif iKind == 4 then
        _Packet:WriteByte(iPacket.m_PartIdx)

    -- 5.確認神經覺醒位置(1)+選擇(1)[0.取消 1.確認]
    elseif iKind == 5 then
        _Packet:WriteByte(iPacket.m_PosIdx)
        _Packet:WriteByte(iPacket.m_Choose)

    -- 6.查詢改造加速目標時間+部位(1)
    elseif iKind == 6 then
        _Packet:WriteByte(iPacket.m_PartIdx)

    end
    
    NetworkMgr.Send(3, 16, _Packet)
end

--- 3-17 內功系統
function SendProtocol_003._017(iKind, iPacket)
    local _Packet = Network.Packet.GetTempPacket()
    _Packet:WriteByte( iKind )

    -- 1.進階心法+心法編號(1)+心法階級(1)
    if iKind == 1 then
        _Packet:WriteByte(iPacket.m_MethodID)
        _Packet:WriteByte(iPacket.m_Step)

    -- 2.升級心法+心法編號(1)+心法等級(1)
    elseif iKind == 2 then
        _Packet:WriteByte(iPacket.m_MethodID)
        _Packet:WriteByte(iPacket.m_Lv)

    -- 3.運行心法+心法編號(1)
    elseif iKind == 3 then
        _Packet:WriteByte(iPacket.m_MethodID)

    -- 4.貫通靈脈穴位+靈脈編號(1)
    elseif iKind == 4 then
        _Packet:WriteByte(iPacket.m_MeridianID)

    -- 5.靈脈進階+靈脈編號(1)
    elseif iKind == 5 then
        _Packet:WriteByte(iPacket.m_MeridianID)

    -- 6.靈脈推演+靈脈編號(1)+階段位置(1)+物品編號(4)
    elseif iKind == 6 then
        _Packet:WriteByte(iPacket.m_MeridianID)
        _Packet:WriteByte(iPacket.m_Step)
        _Packet:WriteUInt32(iPacket.m_ItemID)

    -- 7.確認推演靈功+靈脈編號(1)+階段位置(1)+靈功編號(2)
    elseif iKind == 7 then
        _Packet:WriteByte(iPacket.m_MeridianID)
        _Packet:WriteByte(iPacket.m_Step)
        _Packet:WriteUInt16(iPacket.m_WugongID)

    -- 8.裝備靈功+靈脈編號(1)+階段位置(1)
    elseif iKind == 8 then
        _Packet:WriteByte(iPacket.m_MeridianID)
        _Packet:WriteByte(iPacket.m_Step)

    -- 9.靈功升級+靈脈編號(1)+階段位置(1)+靈功編號(2)+目標等級(1)
    elseif iKind == 9 then
        _Packet:WriteByte(iPacket.m_MeridianID)
        _Packet:WriteByte(iPacket.m_Step)
        _Packet:WriteUInt16(iPacket.m_WugongID)
        _Packet:WriteByte(iPacket.m_Lv)

    -- 10.靈氣解放+心法編號(1)
    elseif iKind == 10 then
        _Packet:WriteByte(iPacket.m_MethodID)
    end

    NetworkMgr.Send(3, 17, _Packet)
end