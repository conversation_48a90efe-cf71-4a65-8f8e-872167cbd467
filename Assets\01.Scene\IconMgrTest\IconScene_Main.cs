﻿//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2022 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------------------------------------    
//
//=====================================================================

using GameTools.Sprite;
using UnityEngine;

/// <summary>
/// Icon生成測試
/// <AUTHOR>
/// @version 1.0
/// @since [ProjectBase] 0.1
/// @date 2022.6.8
/// </summary>
public class IconScene_Main : MonoBehaviour
{
	// Use this for initialization
	void Start()
	{
		ResourceMgr.Inst.Do();
        SpriteMgr.Inst.Do();
		TextureMgr.Inst.Do();
		toLuaMgr.Inst.RunLuaInit();
        toLuaMgr.Inst.DoFile("Common/LuaDebug");

        toLuaMgr.Inst.DoFile("Common/GDefine");
        toLuaMgr.Inst.DoFile("Common/GFunction");
        toLuaMgr.Inst.DoFile("Common/Stack");
        toLuaMgr.Inst.DoFile("Common/ExAPI");

        toLuaMgr.Inst.DoFile("Logic/Data/DataMgr");
		toLuaMgr.Inst.CallFunction( "DataMgr.ReadData");
		
		toLuaMgr.Inst.DoFile("Logic/UIMgr");
		toLuaMgr.Inst.CallFunction( "UIMgr.Init");

		toLuaMgr.Inst.DoFile("Logic/Icon/IconMgr");
        toLuaMgr.Inst.CallFunction( "IconMgr.Init");

#if UNITY_EDITOR
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(65030, IconMgr.m_Trans_Roots, 160):SetCount(1)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(67643, IconMgr.m_Trans_Roots, 170):SetCount(2):SetRarity(ERank.Green)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(67750, IconMgr.m_Trans_Roots, 180):SetCount(3)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(55186, IconMgr.m_Trans_Roots, 190):SetCount(4)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(66022, IconMgr.m_Trans_Roots, 200):SetCount(5)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(55200, IconMgr.m_Trans_Roots, 210):SetCount(6)");
		toLuaMgr.Inst.DoString("IconMgr.GetItemIcon(0, IconMgr.m_Trans_Roots, 220):SetCount(7)");
#endif
    }

	// Update is called once per frame
	void Update()
	{
        // toLuaMgr.Inst.CallFunction("CameraMgr.Update");
	}
}
