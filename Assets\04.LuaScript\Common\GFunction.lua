---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---共用函式
---@class GFunction
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.6.20
GFunction = {}

---釋放 指定 module 的 require 和 Lua 全域資料 取用完 請自行引用 collectgarbage()
---@param imoduleName string 需要釋放的 module 名稱
function GFunction.Unrequire(imoduleName)
    local _moduleName = string.gsub(imoduleName,"/",".")
    for key, value in pairs(package.preload) do
        if string.find(tostring(key),_moduleName) == 1 then
            package.preload[_moduleName] = nil
        end
    end

    for key, value in pairs(package.loaded) do
        if string.find(tostring(key),_moduleName) == 1 then
            package.loaded[_moduleName] = nil
        end
    end

    local _Table_String = string.split(_moduleName, ".")

    _moduleName = _Table_String[table.Count(_Table_String)]

    for key, value in pairs(_G) do
        if string.find(tostring(key),_moduleName) == 1 then
            _G[_moduleName] = nil
        end
    end
end

---依照指定位數補零 e.g. GFunction.Zero_stuffing(32, 5) -> 00032
function GFunction.Zero_stuffing(iNum, iDight)
	local _Number = iNum
	local _NumberDight = 0
	--取得number為幾位數
	while _Number > 0 do
		_Number = math.floor(_Number / 10)
		_NumberDight = _NumberDight + 1
	end

	if _NumberDight < iDight then
		local _ZeroStuff = ""
		--不足位數補0
		for i = _NumberDight, iDight - 1 do
			_ZeroStuff = _ZeroStuff .. "0"
		end
		return _ZeroStuff .. tostring(iNum)
	else
		--位數足夠 直接回傳
		return tostring(iNum)
	end
end

--endregion

--region UI元件生成
---生成一個全新的文字(TextMeshProUGUI)物件
---@param iName string RawImage的gameObject名稱
---@param iParent Transform RawImage的父物件
---@param iFontName string 字型assetName
---@return table
function GFunction.NewTMP(iName, iParent, iFontName)
	local _TTMP = {}

	_TTMP.gameObject = GameObject.New(iName)
	if iParent ~= nil then
		_TTMP.gameObject.transform:SetParent(iParent)
		_TTMP.gameObject.transform.localScale = Vector3.one
	end
	_TTMP.m_RectTransform = Extension.AddMissingComponent(_TTMP.gameObject, typeof(UnityEngine.RectTransform))
	_TTMP.m_TMP = Extension.AddMissingComponent(_TTMP.gameObject, typeof(TMPro.TextMeshProUGUI))
	local _Font = ResourceMgr.LoadFromResources("Font\\TMPAsset\\" .. iFontName)
	if _Font ~= nil then
		_TTMP.m_TMP.font = _Font
	else
		D.LogError("[TextMeshProUGUI生成]找不到字型: " .. iFontName)
		_TTMP.gameObject:SetActive(false)
	end
	return _TTMP
end
--endregion

--region 條件判斷
---@return boolean 是否穿戰鬥服
function GFunction.IsWearUniform(iUniformColor)
	return iUniformColor.WeaponColorIndex > 0
		or iUniformColor.ClothColorAIndex > 0
		or iUniformColor.ClothColorBIndex > 0
		or iUniformColor.ClothColorCIndex > 0
		or iUniformColor.ClothColorDIndex > 0
end

--endregion

---CSharpDictionary 轉 Table
---@param iCSharpDic Dictionary 要轉成 Table 的 C# 字典
function GFunction.DictionaryToTable(iCSharpDic)
    local _CurrentDic = {}
    if iCSharpDic then
        local iter = iCSharpDic:GetEnumerator()
        while iter:MoveNext() do
            local k = iter.Current.Key
            local v = iter.Current.Value
            _CurrentDic[k] = v
        end
    end
    return _CurrentDic
end

--region table相關

---判斷某項目是否在 table 中
---@param iTable table 搜索的目標 Table
---@param iValue any 要判斷的值
---@return boolean
function table.Contains( iTable, iValue )
	return table.GetKey( iTable, iValue) ~= nil
end

---找某項目在 table 內的 Key
---@param iTable table 搜索的目標 Table
---@param iValue any 要找的值
---@return key
function table.GetKey( iTable, iValue )
	if table.IsNullOrEmpty(iTable) then
		return nil
	end

	for _Key, _Value in pairs( iTable ) do
		if _Value == iValue then
			return _Key
		end
	end

	return nil
end

---table 內所有的 Key
---@param iTable table 搜索的目標 Table
---@return key[]
function table.GetKeys( iTable )
	if table.IsNullOrEmpty(iTable) then
		return nil
	end

    local _Keys = {}

	for _Key, _Value in pairs( iTable ) do
		table.insert(_Keys, _Key)
	end

	return _Keys
end

function table.ContainsKey( iTable, iKey )
    if iTable == nil or iKey == nil then
        return false
    end
    
    return iTable[iKey] ~= nil
end

---回傳 table 的長度 ( [#] 在table內的 Key 不連續時，有可能會壞掉)
---@param iTable table 要判斷 Table
---@return number
function table.Count( iTable )
	if iTable == nil then return 0 end

	local result = 0
	for _Key, _Value in pairs( iTable ) do
		result = result + 1
	end

	return result
end

---判斷 table 是否為 nil 或是 Count == 0
---@param iTable table 要判斷 Table
---@return boolean
function table.IsNullOrEmpty( iTable )
	return iTable == nil or table.Count(iTable) == 0
end

---table 擴充 依照 Key 排序並回傳迭代
function table.pairsByKeys( t, f )
	local a = {}
	for n in pairs(t) do table.insert(a, n) end
	table.sort(a, f)
	local i = 0      -- iterator variable
	local iter = function ()   -- iterator function
		i = i + 1
		if a[i] == nil then return nil
		else return a[i], t[a[i]]
		end
	end
	return iter
end

function table.First(iTable)
	if table.IsNullOrEmpty(iTable) then
		return nil
	end
	for index, value in pairs(iTable) do
		return value
	end
end

---清除此Table，會留下空{}
function table.Clear(iTable)
    if iTable == nil then
		return {}
	end
    for k in pairs(iTable) do
		iTable[k] = nil
    end
end

---清除指定元素
function table.removeByKey(iTable, iKey)
	if not iTable or not iKey then
		return
	end

    if type(iKey) == "number" then
        table.remove(iTable, iKey)
    else
        iTable[iKey] = nil
    end
end

---清除索引，會連key都移除
function table.removeKey(iTable, iKey)
	if not iTable or not iKey then
		return
	end

	if iTable[iKey] then
		table.Clear(iTable[iKey])
		iTable[iKey] = nil
	end
end

---內容反轉
function table.Reverse(iTable)
    if iTable == nil then return {} end

    local result = {};
    for i = 1, #iTable do
        table.insert(result, iTable[#iTable + 1 - i]);
    end

    return result;
end
---內容合併
function table.AddRange(sourceTable, addTable)
	for k, v in pairs(addTable) do
		table.insert(sourceTable, v)
	end
end

function table.FirstAndRemove(iTable)
    if table.IsNullOrEmpty(iTable) then
		return nil
	end
	for index, value in pairs(iTable) do
        iTable[index] = nil
		return value
	end
end
--endregion

--region RectTransform相關
---@param iRectTrans transform 要更動的rect
---@param iEAnchorPresets EAnchorPresets 更改的錨點
function GFunction.SetAnchor( iRectTrans, iEAnchorPresets )
	if iRectTrans ~= nil and iEAnchorPresets ~= nil then
		iRectTrans.anchorMin = iEAnchorPresets[ "Min" ]
		iRectTrans.anchorMax = iEAnchorPresets[ "Max" ]
	end
end
--endregion

--放這是因為怕未來如果 toLua 更新放 Vector3.lua 會被蓋掉
--region Vector3 相關

-- 根據天子S專案是說 someVector == Vector3.zero 在 Lua 出乎意料的耗時間 but not sure
function Vector3:IsZeroVector3()
    return  (self.x > -1e-10 and self.x < 1e-10)
            and (self.y > -1e-10 and self.y < 1e-10)
            and (self.z > -1e-10 and self.z < 1e-10)
end

--- Reset all vector3 table value(x, y, z) to 0, avoid using Vector3.zero
function Vector3:Reset()
	if(self ~= nil) then
		self.x = 0
		self.y = 0
		self.z = 0
	else
		error("self SHOULD NOT be nil",2)
	end
end

---Copy source to self
function Vector3:Copy(iSource)
    if(self == nil or iSource == nil) then
		error("self or source SHOULD NOT be nil",2)
        do
			return
		end
    end
    self.x = iSource.x;
    self.y = iSource.y;
    self.z = iSource.z;
end

--- Returns the distance squared between two specified points.
---@return number
function Vector3.SqrDistance(iVector3_A, iVector3_B)
	if(iVector3_A == nil or iVector3_B == nil) then
		error("Any argument SHOULD NOT be nil",2)
        return
    end

	local distanceX = (iVector3_A.x - iVector3_B.x)
	local distanceY = (iVector3_A.y - iVector3_B.y)
	local distanceZ = (iVector3_A.z - iVector3_B.z)
	return ((distanceX * distanceX) + (distanceY * distanceY) + (distanceZ * distanceZ))
end

--- Returns the squared 2D distance between two points.
---@return number
function Vector3.SqrDistance2D(iVector3_A,iVector3_B)
	if(iVector3_A == nil or iVector3_B == nil) then
		error("Any argument SHOULD NOT be nil",2)
		return
    end

	local distanceX = (iVector3_A.x - iVector3_B.x)
	local idstanceZ = (iVector3_A.z - iVector3_B.z)
	return ((distanceX * distanceX) + (idstanceZ * idstanceZ))
end

---由 Transform 得到 position
function Vector3.GetTransformPosition(iVec3, iTransform)
    iVec3 = iVec3 and iVec3 or {}
	iVec3.x, iVec3.y, iVec3.z = Extension.GetTransformPositionValue(iTransform,0,0,0)
    
    return iVec3
end

---由 Transform 得到 LocalEulerAngles
function GFunction.GetTransformLocalEulerAngles(iTransform)
    local _FakeV3 = {}
	_FakeV3.x, _FakeV3.y, _FakeV3.z = Extension.GetTransformLocalEulerAngleValue(iTransform,0,0,0)

    return _FakeV3
end
---由 Transform 得到 Local Position
function Vector3:GetTransformLocalPositionValue (iTransform)
	self.x, self.y, self.z = Extension.GetTransformLocalPositionValue(iTransform,0,0,0)
end

---取得座標點的地板位置(慎用)
---@param iVec3 需要檢查Floor的世界座標
---@return Vector3 正確的位置座標
function Vector3:GetFloorPosition()
    local _X = self.x / 0.64 --別問為什麼是0.64
    local _Z = self.z / 0.64
    local _X_Floor = Mathf.Floor(_X)
    local _Z_Floor = Mathf.Floor(_Z)
    local _X_Ceil = math.ceil(_X)
    local _Z_Ceil = math.ceil(_Z)
    if _X_Floor == _X_Ceil then
        _X_Ceil = _X_Ceil + 1
    end
    if _Z_Floor == _Z_Ceil then
        _Z_Ceil = _Z_Ceil + 1
    end
    local _FF = MoveMgr.GetHeight(_X_Floor, _Z_Floor)
    local _FC = MoveMgr.GetHeight(_X_Floor, _Z_Ceil)
    local _CF = MoveMgr.GetHeight(_X_Ceil, _Z_Floor)
    local _CC = MoveMgr.GetHeight(_X_Ceil, _Z_Ceil)
    local _Y = _FF * (_X_Ceil - _X) * (_Z_Ceil - _Z) --"也許可能"取左右一單位長度的高度後取平均
        + _FC * (_X_Ceil - _X) * (_Z - _Z_Floor)
        + _CF * (_X - _X_Floor) * (_Z_Ceil - _Z)
        + _CC * (_X - _X_Floor) * (_Z - _Z_Floor)
    _Y = Mathf.KeepDigit(_Y , 2)
    
    self.y = _Y

    return self
    --return Vector3(self.x, _Y, self.z)
end

---Copy source to self
function Vector2:Copy(iSource)
    if(self == nil or iSource == nil) then
		error("self or source SHOULD NOT be nil",2)
        do
			return
		end
    end
    self.x = iSource.x;
    self.y = iSource.y;
end

function GFunction.ChangeV3ToV2(iVector3)
    return Vector2.New(iVector3.x, iVector3.z)
end

---計算兩點是否在範圍內，並回傳距離(忽略Y值)
---@param iStartVect Vector3 起始點
---@param iEndVect Vector3 結束點
---@param iRange number 限制長度
---@return boolean, number 是否在範圍內, 距離多少
function GFunction.IsInRange_2D(iStartVect, iEndVect, iRange)
    local _Distance = math.sqrt((iStartVect.x - iEndVect.x) ^ 2 + (iStartVect.z - iEndVect.z) ^ 2) --Vector2.Distance(Vector2(iStartVect.x, iStartVect.z), Vector2(iEndVect.x, iEndVect.z))
    return _Distance <= iRange, _Distance
end
--endregion

---畫面轉標轉換成網格座標
---@param iVec3 Vector3
function GFunction.ScenePosToGrid(iVec3)
    local _Pos = Vector2.New(GFunction.TransferPosToServerFormat(iVec3.x), GFunction.TransferPosToServerFormat(iVec3.z))
    _Pos.x = GFunction.bitRightShift(_Pos.x, 6)
    _Pos.y = GFunction.bitRightShift(_Pos.y, 6)
    return _Pos
end

--用來處理部分 Server 送過來的資料
--region Analyse Server Data
--- 畫面座標轉換成伺服器座標
---@param iVec3 Vector3 要轉換的畫面座標
---@return Vector2 伺服器座標
function GFunction.ScenePosToServerPos(iVec3)
    local _Vec2 = Vector2.New()
    _Vec2.x = GFunction.TransferPosToServerFormat(iVec3.x)
    _Vec2.y = GFunction.TransferPosToServerFormat(iVec3.z)
    return _Vec2
end

---將數值轉成伺服器數值格式
---@param iValue number
---@return number 轉換後的數值 (整數)
function GFunction.TransferPosToServerFormat(iValue)
    if iValue == nil then
        return 0
    end
    return Mathf.RoundToInt(iValue * 100)
end

---將數值轉成客戶端數值格式
---@param iValue number 伺服器的數值
---@return number 轉換後的數值 (浮點數)
function GFunction.TransferPosToUnityFormat(iValue)
    return iValue / 100
end

--- 伺服器位置轉換畫面位置並重新計算高度
---@param iX number X座標
---@param iZ number Z座標
---@return Vector3 畫面上的座標
function GFunction.ServerPosToScenePos(iX, iZ)
    local _Pos = Vector3.New(GFunction.TransferPosToUnityFormat(iX), 0, GFunction.TransferPosToUnityFormat(iZ))
    _Pos:GetFloorPosition()
    return _Pos
end

--- 伺服器位置轉換畫面位置並自訂高度
---@param iPos Vector2 伺服器位置
---@param iHeight number 要設定的高度
---@return Vector3 畫面上的座標
function GFunction.ServerPosToScenePosWithHeight(iPos, iHeight)
    return Vector3.New(GFunction.TransferPosToUnityFormat(iPos.x), iHeight, GFunction.TransferPosToUnityFormat(iPos.y))
end

--- 伺服器位置轉換畫面位置, 但不設定高度
---@param iVec2 Vector2
---@return Vector3 畫面上的座標(高度為0)
function GFunction.ServerPosToScenePosWithoutHeight(iVec2)
    return Vector3.New(GFunction.TransferPosToUnityFormat(iVec2.x), 0, GFunction.TransferPosToUnityFormat(iVec2.y));
end

--- 兩座標不計算高度後的距離
---@param iPos1 Vector3
---@param iPos2 Vector3
---@return number 計算後的距離
function GFunction.DistanceWithoutHeight(iPos1, iPos2)
    local temp1 = Vector3.New(iPos1.x, 0, iPos1.z)
    local temp2 = Vector3.New(iPos2.x, 0, iPos2.z)
    return  Vector3.Distance(temp1, temp2)
end

---將 MoveSpeedFromServer 轉為 m/s ( 每秒幾公尺 )
---@param iServerSpeedTable_Index number ServerSpeedTable索引
---@return number m/s每秒幾公尺
function GFunction.AnalyseServerSpeed(iServerSpeedTable_Index) 
    --因應改版，直接變成數值*25

    --if MoveSpeedFromServer[iServerSpeedTable_Index] ~= nil then
        local _ServerSpeed = iServerSpeedTable_Index * 25  --MoveSpeedFromServer[iServerSpeedTable_Index]
        --由 0.5s 轉成 1s 所以先乘以 2
        local _Speed = _ServerSpeed * 2 / 100.0
        return _Speed
    --else
        --error("invalid server speed table index",2)
    --end
end

---伺服器給的時間除以 1000
---@param iTime number 伺服器給的時間
function GFunction.FromServerTime(iTime)
    return iTime * 0.001
end

--endregion

--region 預測射線相關 Predict Ray
--- 檢查 x, y 向量長度是否大於 0
---@param iVec3 Vector3
function GFunction.GetBoolLen(iVec3)
    --local tmp = Vector3.New(iVec3.x, iVec3.y, iVec3.z)
    iVec3.y = 0
    if iVec3:Magnitude() > 0 then
        return true
    else
        return false
    end
end

---計算一個Vector3與x軸正方向的夾角，並將該角度四捨五入為整數值返回
---@param iVec3 Vector3  要計算的 Vector3
---@return number 整數結果
function GFunction.GetIntAngle(iVec3)
    --local tmp = Vector3.New(iVec3.x, iVec3.y, iVec3.z)
    --tmp:SetNormalize()
    return Mathf.RoundToInt(math.atan2(iVec3.z, iVec3.x) * Mathf.Rad2Deg)
end

--endregion

--region 遊戲使用的Idx相關
function GFunction.GetGameIdxType( iIdx )
	if iIdx == nil or iIdx == EGameIdxType.None then
		return EGameIdxType.None
	elseif iIdx >= EGameIdxType.Skill and iIdx < EGameIdxType.Equip_Skill then
		---武功區間 1~2000
		return EIconType.Skill
	elseif iIdx >= EGameIdxType.Equip_Skill and iIdx < EGameIdxType.Reserved then
		---裝備技能區間 2001~2500
		return EIconType.Skill
	elseif iIdx >= EGameIdxType.Reserved and iIdx < EGameIdxType.Special_Btn then
		---預留區間 2501~9000
		return EIconType.None
	elseif iIdx >= EGameIdxType.Special_Btn and iIdx < EGameIdxType.Item then
		---特殊按鈕區間 9001~10000 *組合招區間9007~9011(企劃不填表要直接寫死)
		return EIconType.None
	else
		---物品區間 10001~
		return EIconType.Item
	end
end
--endregion

--region 位元操作

--- 模擬位元操作符 <<
function GFunction.bitLeftShift(iValue, iShiftTimes)
    local _Value = iValue
    for i = 1, iShiftTimes do
        _Value = _Value * 2
    end
    return _Value
end

--- 模擬位元操作符 >>
function GFunction.bitRightShift(iValue, iShiftTimes)
    local _Value = iValue
    for i = 1, iShiftTimes do
        _Value = math.floor(_Value / 2)
    end
    return _Value
end

--- 模擬位元操作 OR，無法進行負數運算
function GFunction.bitOR(iA, iB)
    local _result = 0
    local _bitval = 1
    while iA + iB > 0 do
        local _A, _B  = iA % 2, iB % 2
        if _A + _B > 0 then
            _result = _result + _bitval
        end
        iA = math.floor((iA - _A)/2)
        iB = math.floor((iB - _B)/2)
        _bitval = _bitval * 2
    end
    return _result
end

--- 模擬位元操作 AND，無法進行負數運算
function GFunction.bitAND(iA, iB)
    local result = 0
    local bitval = 1
    while iA > 0 and iB > 0 do
      if iA % 2 == 1 and iB % 2 == 1 then -- test the rightmost bits
          result = result + bitval      -- set the current bit
      end
      bitval = bitval * 2 -- shift left
      iA = math.floor(iA/2) -- shift right
      iB = math.floor(iB/2)
    end
    return result
end

---模擬位元操作 XOR，無法進行負數運算
function GFunction.bitXOR(iA,iB)
    local _result = 0
    local _bitval = 1
    while iA > 0 and iB > 0 do
        local _A, _B = iA % 2, iB % 2
        if _A ~= _B then
            _result = _result + _bitval
        end
        _bitval = _bitval * 2
        iA = math.floor(iA/2)
        iB = math.floor(iB/2)
    end
    if iA < iB then
        iA = iB
    end
    while iA > 0 do 
        local _A = iA % 2
        if _A > 0 then 
            _result = _result + _bitval
        end
        _bitval = _bitval * 2
        iA = math.floor(iA/2)
    end
    return _result
end

---非負數型態，若要完全模擬 Lua5.4 '~'運算符的話要另外寫
--function GFunction.bitNOT(iValue)
--    local _result = 0
--    local _bitval = 1
--    while iValue > 0 do
--        local _v = iValue % 2
--        if _v < 1 then
--            _result = _result + _bitval
--        end
--        iValue = (iValue - _v )/2
--        _bitval = _bitval * 2
--    end
--    return _result
--end

---檢查某一位是否為 1, 拿來給障礙點網格判斷的
function GFunction.GetBit(iBits, iIndex)
    --return iBits & (1 << iIndex) ~= 0
    return GFunction.bitAND(iBits, GFunction.bitLeftShift(1, iIndex)) ~= 0
end
--endregion

--region 障礙點相關

--- 判斷到底有沒有碰到牆
---@param iA CollisionFlags
---@param iB CollisionFlags
---@return number 返回 iA iB 兩個 AND 結果
function GFunction.HitCollisionBitManipulation(iA, iB)
    local _A = 0
    local _B = 0
    if iA == CollisionFlags.None then
        _A = 0
    elseif iA == CollisionFlags.Sides or  iA == CollisionFlags.CollidedSides then
        _A = 1
    elseif iA == CollisionFlags.Above or  iA == CollisionFlags.CollidedAbove then
        _A = 2
    elseif iA == CollisionFlags.Below or  iA == CollisionFlags.CollidedBelow then
        _A = 4
    end

    if iB == CollisionFlags.None then
        _B = 0
    elseif iB == CollisionFlags.Sides or  iB == CollisionFlags.CollidedSides then
        _B = 1
    elseif iB == CollisionFlags.Above or  iB == CollisionFlags.CollidedAbove then
        _B = 2
    elseif iB == CollisionFlags.Below or  iB == CollisionFlags.CollidedBelow then
        _B = 4
    end
    return  GFunction.bitAND(_A, _B)
end

--- 地面網格能不能走的判斷
---@param iX number
---@param iY number 
---@return boolean  是不是不能走
function GFunction.NotWalkableGrid(iX, iY)
    local m_ObstacleData = SceneMgr.GetObstacleData() -- Maybe have better way can avoid keep access from SceneMgr
    local _Res = true
    if m_ObstacleData ~= nil then
        local _Gx = 0
        local _Gy = 0
        _Gx, _Gy = math.floor(iX), math.floor(iY)
        local _index = math.floor( (_Gy * m_ObstacleData.m_ByteSizeX) + (_Gx / 8))
        if _index >= 0 and _index < m_ObstacleData.m_Nodes.Length then
            _Res = GFunction.GetBit(m_ObstacleData.m_Nodes[_index], (_Gx % 8))
        end
    end

    return _Res
end

--- 地面網格能不能走的判斷, 會代入設定位置
---@param iX number
---@param iY number 
---@return boolean 是不是不能走
function GFunction.NotWalkableGridWithPos(iX, iY)
    local m_ObstacleData = SceneMgr.GetObstacleData() -- Maybe have better way can avoid keep access from SceneMgr
    local _Res = true
    if m_ObstacleData ~= nil then
        local _Gx
        local _Gy
        _Gx, _Gy = Mathf.Floor(iX), Mathf.Floor(iY)
        local _index = Mathf.Floor( (_Gy * m_ObstacleData.m_ByteSizeX + _Gx / 8))
        if _index >= 0 and _index < m_ObstacleData.m_Nodes.Length then
            _Res = GFunction.GetBit(m_ObstacleData.m_Nodes[_index], Mathf.Floor(_Gx % 8))
        end
        if not _Res then
            -- m_WalkableStopX = iX
            -- m_WalkableStopY = iY
        end
    end
    return _Res
end

--- 地面網格能不能走的判斷,
---@param iX number
---@param iY number
---@return boolean 能不能走
function GFunction.Walkable(iX, iY)
    local m_ObstacleData = SceneMgr.GetObstacleData()
    if m_ObstacleData ~= nil then
        local _Gx
        local _Gy
        _Gx, _Gy = GFunction.bitRightShift(iX, 6), GFunction.bitRightShift(iY, 6)
        local _index = Mathf.Floor(_Gy * m_ObstacleData.m_ByteSizeX + _Gx / 8)
        if _index >= 0 and _index < m_ObstacleData.m_Nodes.Length then
            return not GFunction.GetBit(m_ObstacleData.m_Nodes[_index], Mathf.Floor(_Gx % 8))
        end
    end
    return false
end

--- Simulator overload Walkable   參數換成 Vector3
---@param iPos Vector3
function GFunction.WalkableV3(iPos)
    local _ServerPos = GFunction.ScenePosToServerPos(iPos)
    return GFunction.Walkable(_ServerPos.x, _ServerPos.y)
end

---@param iStartPos Vector2
---@param iEndPos Vector2
---@param iIgnoreSame boolean 忽略相同節點
function GFunction.NotWalkableBetweenGridVec2(iStartPos, iEndPos, iIgnoreSame)
    iIgnoreSame = iIgnoreSame or true
    local _X0
    local _Y0
    local _X1
    local _Y1
    _X0 = GFunction.bitRightShift( GFunction.TransferPosToServerFormat( iStartPos.x ), 6)
    _Y0 = GFunction.bitRightShift( GFunction.TransferPosToServerFormat( iStartPos.y ),6)
    _X1 = GFunction.bitRightShift( GFunction.TransferPosToServerFormat( iEndPos.x ), 6)
    _Y1 = GFunction.bitRightShift( GFunction.TransferPosToServerFormat( iEndPos.y ),6)
    if iIgnoreSame and _X0 == _X1 and _Y0 == _Y1 then
        return false
    end
    local _S = ServerDrawLine:New(GFunction.NotWalkableGrid)
    return _S:isGridLineBlock(_X0, _Y0, _X1, _Y1)
end

---@param iStartPos Vector3
---@param iEndPos Vector3
---@param iIgnoreSame boolean 忽略相同節點
function GFunction.NotWalkableBetweenGridVec3(iStartPos, iEndPos, iIgnoreSame)
    if iIgnoreSame == nil then
        iIgnoreSame = true    
    end
    local _X0
    local _Y0
    local _X1
    local _Y1
    _X0 = GFunction.bitRightShift(math.floor(GFunction.TransferPosToServerFormat( iStartPos.x )), 6)
    _Y0 = GFunction.bitRightShift( math.floor(GFunction.TransferPosToServerFormat( iStartPos.z )),6)
    _X1 = GFunction.bitRightShift(math.floor(GFunction.TransferPosToServerFormat( iEndPos.x )), 6)
    _Y1 = GFunction.bitRightShift( math.floor(GFunction.TransferPosToServerFormat( iEndPos.z )),6)
    if iIgnoreSame and _X0 == _X1 and _Y0 == _Y1 then
        return false
    end
    local _S = ServerDrawLine:New(GFunction.NotWalkableGrid)
    return _S:isGridLineBlock(_X0, _Y0, _X1, _Y1)
end

---兩座標之間是否可以移動
---@param iStartPos Vector2 起始座標
---@param iEndPos Vector2 結束座標
---@param iIgnoreSame boolean 忽略相同節點
function GFunction.NotWalkableBetweenPosV2(iStartPos, iEndPos,iIgnoreSame)
    if iIgnoreSame == nil then
        iIgnoreSame = true
    end

    if iIgnoreSame and iStartPos.x ==iEndPos.x and iStartPos.y ==iEndPos.y then
        return false
    end

    local _Geometry = Geometry:New(GFunction.NotWalkableGrid)
    local _Hit = false
    local _StopPos = Vector2.New()
    
    _Hit, _StopPos= _Geometry:DrawLineAllDotsGrid(64, iStartPos, iEndPos) -- GRID_SIZE= 64

    return _Hit
end

---兩座標之間是否可以移動
---@param iStartPos Vector3 起始座標
---@param iEndPos Vector3 結束座標
---@param iIgnoreSame boolean 忽略相同節點
function GFunction.NotWalkableBetweenPosV3(iStartPos, iEndPos,iIgnoreSame)
    if iIgnoreSame == nil then
        iIgnoreSame = true
    end

    return GFunction.NotWalkableBetweenPosV2(Vector2.New(GFunction.TransferPosToServerFormat(iStartPos.x), GFunction.TransferPosToServerFormat(iStartPos.z)), 
    Vector2.New(GFunction.TransferPosToServerFormat(iEndPos.x), GFunction.TransferPosToServerFormat(iEndPos.z)),iIgnoreSame)
end

--- 檢查 SkillId 資料是否正確
---@param iSkillActKind EStateType 取 skillactdata 種類
---@param iSkillId number SkillId
---@return boolean 資料是否正確
---@return SkillData SkillData
---@return SkillActData SkillActData
function GFunction.CheckSkillData(iSkillActKind, iSkillId)
    local _Result = false
    local _SkillData, _SkillActData
    if iSkillId ~= 0 then
        _SkillData = SkillData.GetSkillDataByIdx(iSkillId)
        if _SkillData then
            _SkillActData = SkillActData.GetSkillActDataByIdx(iSkillActKind, _SkillData.m_ActId)
            if _SkillActData then
                if table.Count(_SkillActData.s_ActData) > 0 then
                    _Result = true
                else
                    D.LogError("資料有誤 SkillActData Id: ".. _SkillData.m_ActId .. " ( SkillData: ".. iSkillId .." )")
                end
            else
            D.LogError("找不到 SkillActData Id: ".. _SkillData.m_ActId .. " ( SkillData: ".. iSkillId .." )")
            end
        else
            D.LogError("找不到 SkillData Id: ".. iSkillId)
        end 
    else
        D.LogError("查詢的技能編號為 0")
    end

    return _Result, _SkillData, _SkillActData
end

--- 檢查 SkillId 資料是否正確 演武模式專用
---@param iSkillActKind EStateType 取 skillactdata 種類
---@param iSkillId number SkillId
---@return boolean 資料是否正確
---@return SkillData 混合演武串表跟技能串表的部分資料後在輸出
---@return SkillActData SkillActData
function GFunction.CheckSkillData_DemoSkill(iSkillActKind, iSkillId)
    local _Result = false
    local _SkillData, _SkillActData, _WugongPreviewData, _DemoSkillData
    if iSkillId ~= 0 then
        _SkillData = SkillData.GetSkillDataByIdx(iSkillId)
        if _SkillData then
            _DemoSkillData = {}

            _WugongPreviewData = WugongPreviewData.GetWugongPreviewDataByIdx(_SkillData.m_WugongDemoMode)

            ---武功演出分別需要部分的 SkiilData串表 跟 WugongPreview串表資料 分別記錄後續要使用欄位內容
            _DemoSkillData.m_Idx = iSkillId
            _DemoSkillData.m_TargetKind = _SkillData.m_TargetKind
            _DemoSkillData.m_LimitRange = _SkillData.m_LimitRange
            _DemoSkillData.m_UseType = _SkillData.m_UseType

            _DemoSkillData.m_OriginalSource = _SkillData.m_OriginalSource

            _DemoSkillData.m_SkillOrderIdx = _SkillData.m_SkillOrderIdx

            _DemoSkillData.m_SkillTime = _SkillData.m_SkillTime

            --- WugongPreview串表資料 目前不是全部都有填資料 如果沒有資料 就用原本skilldata的資訊
            if _WugongPreviewData == nil  then
                _DemoSkillData.m_TickAy = _SkillData.m_TickAy
                _DemoSkillData.m_MoveType = _SkillData.m_MoveType
            else
                _DemoSkillData.m_TickAy = _WugongPreviewData.m_TickAy
                _DemoSkillData.m_MoveType = _WugongPreviewData.m_MoveType
            end
            
            ---演出要使用的 SkillActID 有WugongPreview串表資料 就用WugongPreview的資訊 如果沒有資料 就用原本skilldata的資訊
            local _UsedSkillActID = _WugongPreviewData == nil and _SkillData.m_ActId or _WugongPreviewData.m_SkillActID

            _SkillActData = SkillActData.GetSkillActDataByIdx(iSkillActKind, _UsedSkillActID)
            if _SkillActData then
                if table.Count(_SkillActData.s_ActData) > 0 then
                    _Result = true
                else
                    D.LogError("資料有誤 SkillActData Id: ".. _SkillData.m_ActId .. " ( SkillData: ".. iSkillId .." )")
                end
            else
            D.LogError("找不到 SkillActData Id: ".. _SkillData.m_ActId .. " ( SkillData: ".. iSkillId .." )")
            end
        else
            D.LogError("找不到 SkillData Id: ".. iSkillId)
        end 
    else
        D.LogError("查詢的技能編號為 0")
    end

    return _Result, _SkillData, _SkillActData
end

--endregion

---@param try fun() @必填
---@param catch fun()
---@param finally fun()
function TryCatch(try, catch, finally)
    assert(try)
    -- try to call it
    local ok, errors = xpcall(try,function()
        if catch ~= nil then
            catch(debug.traceback())
        else
            D.LogError(debug.traceback())
        end
    end)

    -- run the finally function
    if finally then
        finally(ok)
    end

    return ok
end

GFunction.m_TempStrList = {}

-- 用於獲取iFast和iLast之間的字符串
function GFunction.GetCharBetweenValue(iValue, iFast, iLast)
    local _FirstStrIndex = string.find(iValue, iFast, 1, true)
    if _FirstStrIndex then
        local _LasttStrIndex = string.find(iValue, iLast, _FirstStrIndex + 1, true)
        if _LasttStrIndex then
            return string.sub(iValue, _FirstStrIndex + 1, _LasttStrIndex - 1)
        end
    end
    return ""
end

-- 用於獲取包含指定範圍內的所有子字符串的列表
function GFunction.GetCharBetweenValueList(iValue, iFast, iLast, iClear)
    if iClear == nil or iClear then
        GFunction.m_TempStrList = {}
    end

    local _StrTemp = GFunction.GetCharBetweenValue(iValue, iFast, iLast)
    local _StartIndex = -1

    if _StrTemp ~= "" then
        table.insert(GFunction.m_TempStrList, _StrTemp)

        _StartIndex = string.find(iValue, iLast, string.find(iValue, iFast, 1, true) + 1, true)
        if _StartIndex then
            _StartIndex = _StartIndex + 1
            if #iValue >= _StartIndex then
                GFunction.GetCharBetweenValueList(string.sub(iValue, _StartIndex), iFast, iLast, false)
            end
        end
    end

    return GFunction.m_TempStrList
end

-- 處理ServerMsg函數，根據前綴替換特定內容
function GFunction.ServerMsg(_Msg, _ServerMsg)
    local _StrList = GFunction.GetCharBetweenValueList(_ServerMsg, '%', '%')
    local ReplaceStr = ""

    for i, str in ipairs(_StrList) do
        local prefix = string.sub(str, 1, 1)
        if prefix == "S" or prefix == "N" then
            _StrList[i] = string.sub(str, 2)
        elseif prefix == "O" then
            _StrList[i] = ItemData.GetItemName(tonumber(string.sub(str, 2)))
        elseif prefix == "G" then
            --_StrList[i] = GuildSys.Inst.GetGuildData(tonumber(string.sub(str, 2))).Name
        elseif prefix == "T" then
            --_StrList[i] = GText.Get(SiegeSys.Inst.GetNowSceneSiegeTurretData(tonumber(string.sub(str, 2))).NameStrID)
        elseif prefix == "B" then
            _StrList[i] = BuffData.GetBuffDataByIdx(tonumber(string.sub(str, 2))):BuffName()
        end

        ReplaceStr = "{" .. (i - 1) .. "}"
        _Msg = string.gsub(_Msg, ReplaceStr, _StrList[i])
    end

    return _Msg
end

---取得今天星期幾
function GFunction.GetTodayOfWeek()
    local _Timestamp = os.time({year = HEMTimeMgr.m_ServerTime.Year, month = HEMTimeMgr.m_ServerTime.Month, day = HEMTimeMgr.m_ServerTime.Day})
    local _Weekday = os.date("%w", _Timestamp)
    _Weekday = tonumber(_Weekday)
    _Weekday = (_Weekday == 0) and 7 or _Weekday
    return _Weekday
end

--- 稀有度轉換
function GFunction.GetRank(iRank)
    local _Rank = table.GetKey(ERank, iRank)
    if _Rank == nil then
		return ERank.White
    else
        return ERank[_Rank]
    end
end

local rexfrl = "%$%d"
--取內功說明字串
function GFunction.GetSkillUpLevelFormatString(_WugongID, _WugongLV)
    local _WugongData = WugongData.GetWugongDataByIdx(_WugongID)

    local _UpLvInfoAy = _WugongData.m_UpLvInfoAy

    local resultString = ""
    if _WugongLV < 100 then
        resultString = TextData.Get(_UpLvInfoAy[1])
    else
        resultString = TextData.Get(_UpLvInfoAy[2])
    end

    local _idx = 0
    -- 替換每個 $1~$4
    resultString = string.gsub(resultString, rexfrl, function (iMatch)
        _idx = _idx + 1
        local _val = 0
        if _UpLvInfoAy[_idx + 2] and _UpLvInfoAy[_idx + 2] ~= 0 then
            _val = FormulaData.GetValue(_UpLvInfoAy[_idx + 2], _WugongLV)
        end

        local _str = tostring(_val)
        -- $1 = 公式表_時間單位(秒)
        if iMatch == "$1" then
            if _val >= 60000 then
                _str = GString.Format(TextData.Get(706), _val / 60000) .. GString.Format(TextData.Get(707), (_val % 60000) * 0.001)
            else
                _str = string.format("%d%s", _val * 0.001, GText.Get(8004))
            end

        -- $2 = 公式表_千分比(%)
        elseif iMatch == "$2" then
            _str = string.format("%.1f%%", _val * 0.1)

        -- $3 = 公式表_常數
        elseif iMatch == "$3" then
            _str = tostring(_val)

        -- $4 = 公式表_百分比(%)
        elseif iMatch == "$4" then
            _str = string.format("%d%%", math.floor(_val * 0.01))
        end

        return _str
    end)

    return resultString
end

-- ---查找对象---
-- function find(str)
-- 	return GameObject.Find(str);
-- end

-- function destroy(obj)
-- 	GameObject.Destroy(obj);
-- end

-- function newObject(prefab)
-- 	return GameObject.Instantiate(prefab);
-- end

-- ---创建面板---
-- function createPanel(name)
-- 	PanelManager:CreatePanel(name);
-- end

-- function child(str)
-- 	return transform:Find(str);
-- end

-- function subGet(childNode, typeName)
-- 	return child(childNode):GetComponent(typeName);
-- end

-- function findPanel(str)
-- 	local obj = find(str);
-- 	if obj == nil then
-- 		error(string.Concat(str, " is null"));
-- 		return nil;
-- 	end
-- 	return obj:GetComponent("BaseLua");
-- end

-- function inheritsFrom( baseClass )
--   --- The following lines are equivalent to the SimpleClass example:

--   --- Create the table and metatable representing the class.
--   local new_class = {}
--   local class_mt = { __index = new_class }

--   --- Note that this function uses class_mt as an upvalue, so every instance
--   --- of the class will share the same metatable.
--   ---
--   function new_class:create()
--     local newinst = {}
--     setmetatable( newinst, class_mt )
--     return newinst
--   end

--   --- The following is the key to implementing inheritance:

--   --- The __index member of the new class's metatable references the
--   --- base class.  This implies that all methods of the base class will
--   --- be exposed to the sub-class, and that the sub-class can override
--   --- any of these methods.
--   ---
--   if baseClass then
--     setmetatable( new_class, { __index = baseClass } )
--   end

--   return new_class
-- end

-- function Contains(compare, ...)
--   local compares = {...}
--   for k, v in pairs(compares) do
--     if compare == v then
--       return true;
--     end
--   end

--   return false;
-- end

-- function Between(value, min, max)
--   if value == nil then
--     return false;
--   end

--   if value >= min and value <= max then
--     return true
--   else
--     return false;
--   end
-- end

-- function ConvertPositionToUIScene(canvasRectTransform, vector2Pos, sceneCamera)
--   local result, pos = RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRectTransform, vector2Pos, sceneCamera, nil);
--   return pos;
-- end

-- ---table
-- function table.Clear(t)
--   if t == nil then return {} end
--   for k in pairs(t) do
--     t[k] = nil;
--   end
-- end

-- function table.Count(t)
--   if t == nil then return 0 end

--   local result = 0;
--   for k, v in pairs(t) do
--     result = result + 1;
--   end

--   return result;
-- end

-- function table.Reverse(t)
--   if t == nil then return {} end

--   local result = {};
--   for i = 0, #t - 1 do
--     table.insert(result, t[#t-i]);
--   end

--   return result;
-- end

-- function table.RemoveByValue(t, v)
--   if t == nil then return false end

--   local k = table.GetKey(t, v);

--   if k == nil then return false end

--   table.remove(t, k);

--   return true;
-- end

-- function table.Copy(t)
--   if t == nil then return nil end
--   local result = {};
--   for k,v in pairs(t)do
--     result[k] = v;
--   end
--   return result;
-- end

-- function table.Dump(node)
--   --- to make output beautiful
--   local function tab(amt)
--     local str = ""
--     for i=1,amt do
--       str = string.Concat(str, "\t");
--     end
--     return str
--   end

--   local cache, stack, output = {},{},{}
--   local depth = 1
--   local output_str = "{\n"

--   while true do
--     local size = 0
--     for k,v in pairs(node) do
--       size = size + 1
--     end

--     local cur_index = 1
--     for k,v in pairs(node) do
--       if (cache[node] == nil) or (cur_index >= cache[node]) then
--         if (string.find(output_str,"}",output_str:len())) then
--           output_str = string.Concat(output_str, ",\n");
--         elseif not (string.find(output_str,"\n",output_str:len())) then
--           output_str = string.Concat(output_str, "\n");
--         end

--         --- This is necessary for working with HUGE tables otherwise we run out of memory using concat on huge strings
--         table.insert(output,output_str)
--         output_str = ""

--         local key
--         if (type(k) == "number" or type(k) == "boolean") then
--           key = string.Concat("[", tostring(k), "]");
--         else
--           key = string.Concat("['", tostring(k), "']");
--         end

--         if (type(v) == "number" or type(v) == "boolean") then
--           output_str = string.Concat(output_str, tab(depth), key, " = ", tostring(v));
--         elseif (type(v) == "table") then
--           output_str = string.Concat(output_str, tab(depth), key, " = {\n");
--           table.insert(stack,node)
--           table.insert(stack,v)
--           cache[node] = cur_index+1
--           break
--         else
--           output_str = string.Concat(output_str, tab(depth), key, " = '", tostring(v), "'");
--         end

--         if (cur_index == size) then
--           output_str = string.Concat(output_str, "\n", tab(depth-1), "}");
--         else
--           output_str = string.Concat(output_str, ",");
--         end
--       else
--         --- close the table
--         if (cur_index == size) then
--           output_str = string.Concat(output_str, "\n", tab(depth-1), "}");
--         end
--       end

--       cur_index = cur_index + 1
--     end

--     if (size == 0) then
--       output_str = string.Concat(output_str, "\n", tab(depth-1), "}");
--     end

--     if (#stack > 0) then
--       node = stack[#stack]
--       stack[#stack] = nil
--       depth = cache[node] == nil and depth + 1 or depth - 1
--     else
--       break
--     end
--   end

--   --- This is necessary for working with HUGE tables otherwise we run out of memory using concat on huge strings
--   table.insert(output, output_str)
--   output_str = table.concat(output)

--   print(output_str)
-- end

-- ---math
-- math.pi = 3.1415926;
-- math.rad2deg = 57.29578;
-- math.deg2rad = 0.********;

-- function math.round(x)
--   if x % 1 < 0.5 then
--     return math.floor(x);
--   else
--     return math.ceil(x);
--   end
-- end

-- function math.BankersRound(x, count)
--   if count == nil then
--     count = 0;
--   end

--   local modValue = math.pow(0.1, count);
--   local decimal = x % modValue;

--   if decimal < 0.5 * modValue then
--     return x - decimal;
--   elseif decimal > 0.5 * modValue then
--     return x + (modValue - decimal);
--   else
--     if count % 2 == 0 then
--       return x - decimal;
--     else
--       return x + (modValue - decimal);
--     end
--   end
-- end

-- function math.clamp(x, min, max)
--   if x < min then
--     return min
--   end

--   if x > max then
--     return max
--   end

--   return x
-- end

-- function math.trunc(x)
--   if x < 0 then
--     return x - (x % -1);
--   else
--     return x - (x % 1);
--   end
-- end

-- function math.logN(base, x)
--   return math.log(x) / math.log(base);
-- end

-- function IntPower(base, exponent)
--   local y = math.abs(exponent);
--   local lBase = base;
--   local result = 1;

--   while y > 0 do
--     while y % 2 == 0 do
--       y = bit.rshift(y, 1);
--       lBase = lBase * lBase
--     end
--     y = y - 1;
--     result = result * lBase;
--   end

--   if exponent < 0 then
--     result = 1.0 / result;
--   end

--   return result;
-- end

-- function SimpleRoundTo(x, digit)
--   if digit == nil then
--     digit = -2;
--   end

--   local factor = IntPower(10, digit);
--   if x < 0 then
--     return math.floor((x / factor) - 0.5) * factor;
--   else
--     return math.floor((x / factor) + 0.5) * factor;
--   end
-- end

-- ---flag
-- local flagValue = { 1, 2, 4, 8, 16, 32, 64, 128 }

-- function CheckFlag(flag, flagIndex)
--   if type(flag) == "number" then
--     if flagIndex < 1 or flagIndex > 8 then return false end

--     return flagValue[flagIndex] == bit.band(flag, flagValue[flagIndex]);
--   elseif type(flag) == "table" then
--     if flagIndex < 1 then return false end

--     local tableIndex = math.floor((flagIndex - 1) / 8) + 1;

--     if flag[tableIndex] == nil then return false end

--     flagIndex = ((flagIndex - 1) % 8) + 1;

--     return flagValue[flagIndex] == bit.band(flag[tableIndex], flagValue[flagIndex]);
--   end

--   return false;
-- end

-- function SetFlag(flag, flagIndex, value)
--   if type(flag) == "number" then
--     if flagIndex < 1 or flagIndex > 8 then return flag end

--     if value then
--       flag = bit.bor(flag, flagValue[flagIndex]);
--     else
--       flag = bit.band(flag, bit.bnot(flagValue[flagIndex]));
--     end
--   elseif type(flag) == "table" then
--     if flagIndex < 1 then return flag end

--     local tableIndex = math.floor((flagIndex - 1) / 8) + 1;
--     local tableValue = flag[tableIndex] or 0;

--     flagIndex = ((flagIndex - 1) % 8) + 1;

--     if value then
--       flag[tableIndex] = bit.bor(tableValue, flagValue[flagIndex]);
--     else
--       flag[tableIndex] = bit.band(tableValue, bit.bnot(flagValue[flagIndex]));
--     end
--   end

--   return flag;
-- end

function GFunction.CheckDate(YY,MM,DD)
	-- D.Log("檢查日期 " .. YY .. " " .. MM .. " " .. DD)
	return YY >= 1 and MM >= 1 and MM <= 12 and DD >= 1 and DD <= DateTime.DaysInMonth(YY, MM)
end

function GFunction.CompareDate(iDate1, iDate2)
	return System.DateTime.Compare(iDate1, iDate2)
end
