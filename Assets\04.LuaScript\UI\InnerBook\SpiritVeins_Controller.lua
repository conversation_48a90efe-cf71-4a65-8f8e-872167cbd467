﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功-靈脈貫通頁面
---@class SpiritVeins_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.21
SpiritVeins_Controller = {}
local this = SpiritVeins_Controller

---靈脈最高階段
local _SpiritMaxStep = 9

---穴位效果階段
local _Acupoint = {2, 5, 9}

---初始化
function SpiritVeins_Controller.Init(iController)   
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --穴位效果
    this.m_Group_AcupointEffect = this.m_ViewRef.m_Dic_Trans:Get("&Group_AcupointEffect")
    this.m_Trans_AcupointEffect = this.m_ViewRef.m_Dic_Trans:Get("&Trans_AcupointEffect")
    this.m_AcupointEffectData = {}
    for i = 1, 3 do
        local _EffectObject = nil
        if i == 1 then
            _EffectObject =  this.m_Trans_AcupointEffect
        else
            _EffectObject =  this.m_Trans_AcupointEffect:Instantiate( this.m_Trans_AcupointEffect )
            _EffectObject:SetParent(this.m_Group_AcupointEffect)
        end
        _EffectObject:SetSiblingIndex(i - 1)
       
        local _EffectInfo = {}
        _EffectInfo.m_GameObject = _EffectObject.gameObject
        _EffectInfo.m_Image_Acupoint = _EffectObject:Find("Image_Acupoint").gameObject:GetComponent(typeof( UIImageChange ))
        _EffectInfo.m_Text_Step = _EffectObject:Find("Image_Acupoint/Text_Step").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _EffectInfo.m_Text_Step_Render = _EffectObject:Find("Image_Acupoint/Text_Step").gameObject:GetComponent(typeof( UIRenderTMPTextChangeStyle ))
        _EffectInfo.m_Text_Step.text = _Acupoint[i]
        _EffectInfo.m_Text_SpiritEffect = _EffectObject:Find("Text_SpiritEffect").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _EffectInfo.m_Image_ValueBG = _EffectObject:Find("Image_ValueBG").gameObject:GetComponent(typeof( UIRenderChangeColor ))
        _EffectInfo.m_Text_SpiritValue = _EffectObject:Find("Image_ValueBG/Text_SpiritValue").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

        table.insert(this.m_AcupointEffectData, _EffectInfo)
    end

    --心法屬性加成效果
    this.m_Image_Att = {}
    for i = 1, 2 do
        this.m_Image_Att[i] = this.m_ViewRef.m_Dic_Trans:Get("&Image_Att_" .. i):GetComponent(typeof(Image))
    end
    this.m_Text_AttEffect = this.m_ViewRef.m_Dic_TMPText:Get("&Text_AttEffect")
    this.m_Image_AttValueBG = this.m_ViewRef.m_Dic_Trans:Get("&Image_AttValueBG"):GetComponent(typeof(UIRenderChangeColor))
    this.m_Text_AttValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_AttValue")

    --靈氣消耗
    this.m_Text_SpiritCostValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritCostValue")
    this.m_Text_SpiritCostValue_Render = this.m_ViewRef.m_Dic_Trans:Get("&Text_SpiritCostValue"):GetComponent(typeof(UIRenderTMPTextChangeStyle))

    --靈氣累積值
    this.m_Text_SpiritAccValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritAccValue")

    --靈脈貫通
    this.m_Btn_Veins = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Veins"))
    this.m_Btn_Veins:AddListener(EventTriggerType.PointerClick, function() SpiritVeins_Controller.OnClick_Veins() end)
end

function SpiritVeins_Controller.Open()
    InnerBook_Controller.OnClick_Meridian(1)
    GStateObserverManager.Register(EStateObserver.UpdateSpiritVeins, this)
end

function SpiritVeins_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.UpdateSpiritVeins, this)
end

---設定靈脈效果資訊
function SpiritVeins_Controller.SetEffectInfo(iIdx)
    local _Step = InnerBook_Model.m_CurrentMeridian.m_Step
    local _Point = InnerBook_Model.m_CurrentMeridian.m_Point
    local _Data = InnerMeridianData.GetInnerMeridianDataByIdx(iIdx)
    local _MeridianData = _Data[_Step]

    --穴位效果
    for i = 1, 3 do
        local _StatusData = StatusNameData.GetStatusNameDataByIdx(_MeridianData.m_Effect[i].m_AttrFix)
        this.m_AcupointEffectData[i].m_Text_SpiritEffect.text = TextData.Get(_StatusData.m_TextIdx)
        
        if _Point >= _Acupoint[i] then
            this.m_AcupointEffectData[i].m_Image_Acupoint:Trigger(ESelectionState.Selected)
            this.m_AcupointEffectData[i].m_Text_Step_Render:Trigger(ESelectionState.Selected)
            this.m_AcupointEffectData[i].m_Image_ValueBG:Trigger(ESelectionState.Selected)
            this.m_AcupointEffectData[i].m_Text_SpiritValue.text = GString.StringWithStyle(_MeridianData.m_Effect[i].m_AttrValue, InnerBook_Model.m_NormalStyle)
        else
            this.m_AcupointEffectData[i].m_Image_Acupoint:Trigger(ESelectionState.Normal)
            this.m_AcupointEffectData[i].m_Text_Step_Render:Trigger(ESelectionState.Normal)
            this.m_AcupointEffectData[i].m_Image_ValueBG:Trigger(ESelectionState.Normal)
            if _Step > 1 then
                local _PreData = _Data[_Step - 1]
                local _NowValue = GString.StringWithStyle(_PreData.m_Effect[i].m_AttrValue .. " -> ", InnerBook_Model.m_NormalStyle)
                local _NextValue = GString.StringWithStyle(_MeridianData.m_Effect[i].m_AttrValue, InnerBook_Model.m_EmphasizeStyle)
                this.m_AcupointEffectData[i].m_Text_SpiritValue.text = _NowValue .. _NextValue
            else
                local _NowValue = GString.StringWithStyle("0 -> ", InnerBook_Model.m_NormalStyle)
                local _NextValue = GString.StringWithStyle(_MeridianData.m_Effect[i].m_AttrValue, InnerBook_Model.m_EmphasizeStyle)
                this.m_AcupointEffectData[i].m_Text_SpiritValue.text = _NowValue .. _NextValue
            end
        end
    end

    --心法屬性加成效果
    local _StatusData = StatusNameData.GetStatusNameDataByIdx(_MeridianData.m_MethodAttrFix)
    for i = 1, 2 do
        SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Normal][i][InnerBook_Model.m_MeridianMark[i][iIdx]], function(iSprite)
            this.m_Image_Att[i].sprite = iSprite
        end)
    end
    this.m_Text_AttEffect.text = TextData.Get(_StatusData.m_TextIdx)
    this.m_Text_AttValue.text = _MeridianData.m_MethodAttrValue
    if InnerBook_Model.m_RunningMethod ~= nil and
       InnerBook_Model.m_MeridianMark[1][iIdx] == InnerBook_Model.m_RunningMethod.m_Attribute[1].Value and
       InnerBook_Model.m_MeridianMark[2][iIdx] == InnerBook_Model.m_RunningMethod.m_Attribute[2].Value then
        this.m_Image_AttValueBG:Trigger(ESelectionState.Selected)
    else
        this.m_Image_AttValueBG:Trigger(ESelectionState.Normal)
    end

    --靈氣消耗
    local _NeedCount = _MeridianData.m_AuraCost + _MeridianData.m_AuraCostAdd * _Point
    this.m_Text_SpiritCostValue.text = _NeedCount
    if InnerBook_Model.m_SpiritValue >= _NeedCount then
        this.m_Text_SpiritCostValue_Render:Trigger(ESelectionState.Normal)
        this.m_Btn_Veins:SetEnable()
    else
        this.m_Text_SpiritCostValue_Render:Trigger(ESelectionState.Selected)
        this.m_Btn_Veins:SetDisable(false)
    end

    --靈氣累積
    this.m_Text_SpiritAccValue.text = InnerBook_Model.m_SpiritValue

    --靈脈貫通or進階
    if _Point < _SpiritMaxStep then
        this.m_Btn_Veins:SetText(TextData.Get(20112026))
    else
        this.m_Btn_Veins:SetText(TextData.Get(21002094))
    end

    InnerBook_Controller.SetCommonMeridianInfo(iIdx, _Step, _Point)
end

---點擊靈脈貫通 or 進階
function SpiritVeins_Controller.OnClick_Veins()
    --貫通靈脈
    if InnerBook_Model.m_CurrentMeridian.m_Point < 9 then
        SendProtocol_003._017(4, {m_MeridianID = InnerBook_Model.m_MeridianIdx})

    --靈脈進階
    else
        SendProtocol_003._017(5, {m_MeridianID = InnerBook_Model.m_MeridianIdx})
    end
end

---狀態改變通知
function SpiritVeins_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.UpdateSpiritVeins then
        local _Param = {...}
        local _Idx = _Param[1]
        SpiritVeins_Controller.SetEffectInfo(_Idx)
    end
end