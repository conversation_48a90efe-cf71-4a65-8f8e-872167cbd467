---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---金流
---@class Mall_Controller
---author whight
---version 1.0
---date 2025.1.13
---
require("UI/Mall/Mall_Model")

Mall_Controller = {}
local this = Mall_Controller

setmetatable( this, { __index = UIControllerBase } )
this:New("Mall_View", "Mall_Controller", EUIOrderLayers.FullPage, false, "bg_005")
---橫幅廣告
this.m_UBanner = nil
---當前頁面種類
this.m_SelKind = EProductKind.Timing
---開啟UI預設種類
this.m_OpenKind = 0
---當前子頁籤序
this.m_SubSelIndex = 0

---每列有幾個物品
---@type number
this.m_PerRowCount = {}

---ScrollView 生成了幾行
---@type number
this.m_ScrollViewRowCount = {}

---SideButton
this.m_MainBtn = {}
this.m_SubBtn = {}
---商品物件 Table

---Product
this.m_ProductBtn = {}
this.m_ProductBBtn = {}
this.m_ProductDBtn = {}

this.DetailIconUnit = {}
this.m_FullPageMenuLeft = {}

local _MainTabIdx = 1
local _SubTabIdx = 1
local _LaftTabInit = false

local DETAIL_SIZE = 110
local DETAIL_ItemSIZE = 70

---子頁籤圖片大小
local TAB_PIC_SIZE = 40
---子頁籤圖片偏移量
local TAB_PIC_OFFSET = -0.2

---商顛詢問編號
local BUY_ITEM_MSG = 600
---時空幣不足詢問編號
local BUY_ITEM_MSG_NOT_ENOUGH = 601
---儲值訊息編號
local DEPOSIT_MSG = 602
---金額不足字串編號
local MONEY_NOT_ENOUGH_STR = 9098
---幣值圖
local CurrencyItemID = {
    [EStoreCurrency.Diamond] = DiamondID,
    [EStoreCurrency.HEMCoin] = HEMCoinID,
    [EStoreCurrency.Money]   = MoneyID
}

---是否已經初始化過 ScrollView
this.m_IsInitScrollView = {}
---設定子頁籤文字
---@param iObj any 按鈕物件
---@param iState string 觸發圖片字尾
---@param iforce boolean 強制更換
local function SetSubTagTitle(iObj, iState, iforce)
    if not iforce and this.m_SubSelIndex == tonumber(iObj.gameObject.name) then
        return
    end

    local _TabTxt = " " .. GString.GetTMPEmoji( Mall_Model.m_Mall_STab_Data[Mall_Model.m_Mall_MTab_Data[_MainTabIdx].m_Tags[tonumber(iObj.gameObject.name)]][1].m_Pic .. iState)
    _TabTxt = GString.GetTextWithSize(_TabTxt, TAB_PIC_SIZE)  --設定Emoji Size
    _TabTxt = " " .. GString.GetTextWithVoffset(_TabTxt, TAB_PIC_OFFSET)  --設定Emoji 偏移
    _TabTxt = _TabTxt .. TextData.Get(Mall_Model.m_Mall_STab_Data[Mall_Model.m_Mall_MTab_Data[_MainTabIdx].m_Tags[tonumber(iObj.gameObject.name)]][1].m_Note)

    iObj.Txt.text = _TabTxt
end
---重置子頁籤內容
---@param iIndex number 第n個子頁籤
local function ResetBtnSelectState(iIndex)
    if iIndex == 0 then
        return
    end
    SetSubTagTitle(this.m_SubBtn[iIndex], EButtonTriggerTypeSuffix.Normal, true)
end

---初始子頁籤
local function InitSubTab()
    if #this.m_SubBtn >= Mall_Model.SubCount(_MainTabIdx) then
        return
    end

    -----子TAB生成按紐
    local _DataSub = Mall_Model.SubCount(_MainTabIdx)
    for i = 1, _DataSub do
        local _Unit
        local _SubBtnUnit = {}
        if this.m_SubBtn[i] then
            _SubBtnUnit = this.m_SubBtn[i]
        else
            _Unit = this.m_SubObj:Get()
            _SubBtnUnit.gameObject = _Unit
            _SubBtnUnit.gameObject.name = i --改記INDEX 後續方便使用
            _SubBtnUnit.gameObject.transform:SetParent(this.SubTrans.transform.parent)
            _SubBtnUnit.gameObject.transform.localPosition = Vector3.zero
            _SubBtnUnit.gameObject.transform.localScale = Vector3.one
            _SubBtnUnit.gameObject:SetActive(true)

            _SubBtnUnit.m_Btn = _SubBtnUnit.gameObject.transform:GetComponent(typeof( ButtonEx ))
            Button.AddListener(_SubBtnUnit.m_Btn, EventTriggerType.PointerClick, function() SetSubTagTitle(_SubBtnUnit, EButtonTriggerTypeSuffix.Selected, false) end)
            ---以下兩觸發方式會吃掉Scroll 拖曳
            Button.AddListener(_SubBtnUnit.m_Btn, EventTriggerType.PointerEnter, function() SetSubTagTitle(_SubBtnUnit, EButtonTriggerTypeSuffix.Highlighted, false) end)
            Button.AddListener(_SubBtnUnit.m_Btn, EventTriggerType.PointerExit, function() SetSubTagTitle(_SubBtnUnit, EButtonTriggerTypeSuffix.Normal, false) end)

            Button.AddScrollViewEvent(_SubBtnUnit.m_Btn)

            _SubBtnUnit.Red = _SubBtnUnit.gameObject.transform:Find("Image_RedPoint"):GetComponent(typeof( Image ))
            _SubBtnUnit.Txt = _SubBtnUnit.gameObject.transform:Find("TMP_TabText_1"):GetComponent("TMPro.TextMeshProUGUI")

            this.m_SubBtn[i] = _SubBtnUnit
            GroupButton.AddButtonToList(this.m_GObjSubBtn.gameObject, _SubBtnUnit.gameObject)
        end
    end

    -- 初始化GroupButton 各自的 Event
    for i = 1, GroupButton.GetCount(this.m_GObjSubBtn) do
        GroupButton.AddListenerByIndex(this.m_GObjSubBtn, i, EventTriggerType.PointerClick, function()
            ---擋重複觸發
            if this.m_SubSelIndex == i - 1 then
                return
            end
            ResetBtnSelectState(this.m_SubSelIndex)
            this.m_SubSelIndex = i - 1
            this.OnGroupTabSubBtnClick(i - 1)
        end)
    end
end
---初始化主頁籤
local function InitMainTab()
    if #this.m_MainBtn >= Mall_Model.MainCount() then
        return
    end

    for k, _data in pairs(Mall_Model.m_Mall_MTab_Data) do
        local _TypeUnit = this.m_FullPageMenuLeft:GetButtonTableByIndex(k)
        _TypeUnit.m_Data = _data

        _TypeUnit.m_Image_Label.gameObject:SetActive(true)
        _TypeUnit.m_TMP_Label.text = TextData.Get(_TypeUnit.m_Data.m_Note)
        _TypeUnit.m_Gobj_RedPoint.gameObject:SetActive(Mall_Model.CheckMainTabRedDot(k))

        _TypeUnit.gameObject:SetActive(Mall_Model.CheckMainTabActive(k))
    end
end

local function InitProduct()
    ---一般排版
    this.m_GObjectScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollDetailUsual")
    this.m_ReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row_Usual").gameObject
    this.m_ItemDetial = this.m_ViewRef.m_Dic_Trans:Get("&Btn_DetailU").gameObject
    this.m_Content_U = this.m_ViewRef.m_Dic_Trans:Get("&ContentU")
    this.m_Content_Width = this.m_ViewRef.m_Dic_Trans:Get("&Content_Width")
    this.m_IsInitScrollView[EStorePnl.Pnl1] = false
    this.m_PerRowCount [EStorePnl.Pnl1] = 0
    this.m_ScrollViewRowCount[EStorePnl.Pnl1] = 0
    ---組合排版
    this.m_GObjectScrollView_Package = this.m_ViewRef.m_Dic_Trans:Get("&ScrollDetailPackage")
    this.m_ReuseItme_Package = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row_Package").gameObject
    this.m_ItemDetialP = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Detail").gameObject
    this.m_Content_P = this.m_ViewRef.m_Dic_Trans:Get("&ContentP")

    this.m_DIconTrans = this.m_ViewRef.m_Dic_Trans:Get("&DObj").gameObject
    this.m_DIcon = Extension.CreatePrefabObjPool(this.m_DIconTrans, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

    this.m_IsInitScrollView[EStorePnl.Pnl2] = false
    this.m_PerRowCount[EStorePnl.Pnl2] = 0
    this.m_ScrollViewRowCount[EStorePnl.Pnl2] = 0
    ---金流排版
    this.m_GObjectScrollView_Half = this.m_ViewRef.m_Dic_Trans:Get("&ScrollDetailhalf")
    this.m_ReuseItme_Half = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row_half").gameObject
    this.m_ItemDetialH = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Diamond").gameObject
    this.m_Content_H = this.m_ViewRef.m_Dic_Trans:Get("&Contenthalf")
    this.m_IsInitScrollView[EStorePnl.Pnl3] = false
    this.m_PerRowCount[EStorePnl.Pnl3] = 0
    this.m_ScrollViewRowCount[EStorePnl.Pnl3] = 0
end

---初始化
function Mall_Controller.Init()
    ---資源列
    local _CurrentResourceTable =
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
    this.m_Transform_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&Main_Panel")
    this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_Transform_MainPanel, 0, TextData.Get(20900004), _CurrentResourceTable)

    this.m_UBanner = this.m_ViewRef.m_Dic_RawImage:Get("&Banner")

    --- 左側選單
    local _DataMain = Mall_Model.MainCount()
    local _IconTable = {}
    for i = 1, _DataMain do
        table.insert(_IconTable, Mall_Model.m_Mall_MTab_Data[i].m_Pic)
    end

    this.m_FullPageMenuLeft = LeftGroupTab.New(this.m_Transform_MainPanel, 1, _IconTable, function(iIndex)
        if _LaftTabInit then
            this.OnGroupTabMainBtnClick(iIndex)
        else
            _LaftTabInit = true
            this.SetMainTagPnl()
        end
    end)

    ---子頁籤按紐
    this.SubTrans = this.m_ViewRef.m_Dic_Trans:Get("&Button_Sub_1").gameObject
    this.m_SubObj = Extension.CreatePrefabObjPool(this.SubTrans, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    this.m_GObjSubBtn = this.m_ViewRef.m_Dic_Trans:Get("&Sub_Obj")

    InitProduct()

    GStateObserverManager.Register(EStateObserver.PlayerLevelRefresh, this)
    GStateObserverManager.Register(EStateObserver.UpdateOnHourTime, this)
    GStateObserverManager.Register(EStateObserver.UpdateDiamond, this)
    GStateObserverManager.Register(EStateObserver.UpdateCoin, this)
    GStateObserverManager.Register(EStateObserver.UpdateHEMCoin, this)
end

---初始化物品元件
---@param iPnl number 版型種類
---@param iUnit any 物品物件
---@param iParent any 父物件
---@return any _ItemUnit 回傳元件
local function InitItem(iPnl, iUnit, iParent)
    local _ItemUnit = {}
    if iPnl == EStorePnl.Pnl1 then
        _ItemUnit.gameObject    = iUnit.gameObject
        _ItemUnit.m_Name        = iUnit.transform:Find("LayoutGroup_Item/TMP_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_Count       = iUnit.transform:Find("LayoutGroup_Item/TMP_Count/Text_Limit"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_Time        = iUnit.transform:Find("TMP_Time"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_Discount    = iUnit.transform:Find("TMP_Discount"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_DisBK       = iUnit.transform:Find("DisBK"):GetComponent(typeof( Image ))
        _ItemUnit.m_Trade       = iUnit.transform:Find("Trade"):GetComponent(typeof( Image ))
        _ItemUnit.Button_Buy    = iUnit.transform:Find("Block_BuyButton")
        _ItemUnit.BtnBuy        = iUnit.transform:Find("Block_BuyButton/Button_Buy")
        _ItemUnit.Group_Image   = iUnit.transform:Find("Group_Image")
        _ItemUnit.m_Red         = iUnit.transform:Find("Image_RedPoint_1")

        _ItemUnit.m_CoinIcon    = _ItemUnit.Button_Buy:Find("Button_Buy/IMG_Money"):GetComponent(typeof( Image ))
        _ItemUnit.m_Money       = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_Money"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _ItemUnit.m_MoneyDis    = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_MoneyDis"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
    elseif iPnl == EStorePnl.Pnl2 then
        _ItemUnit.gameObject    = iUnit.gameObject
        _ItemUnit.Group_Image   = iUnit.transform:Find("Group_Image")
        _ItemUnit.m_Time        = iUnit.transform:Find("TMP_Time"):GetComponent("TMPro.TextMeshProUGUI")
        _ItemUnit.m_DisBK       = iUnit.transform:Find("DisBK"):GetComponent(typeof( Image ))
        _ItemUnit.m_Discount    = iUnit.transform:Find("TMP_Discount"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_Title       = iUnit.transform:Find("TMP_Title"):GetComponent("TMPro.TextMeshProUGUI")
        _ItemUnit.m_Name        = iUnit.transform:Find("TMP_Name"):GetComponent("TMPro.TextMeshProUGUI")
        _ItemUnit.m_Count       = iUnit.transform:Find("TMP_Count"):GetComponent("TMPro.TextMeshProUGUI")
        _ItemUnit.Button_Buy    = iUnit.transform:Find("Block_BuyButton")
        _ItemUnit.BtnBuy        = iUnit.transform:Find("Block_BuyButton/Button_Buy")
        _ItemUnit.Button_Find   = iUnit.transform:Find("Button_Find")
        _ItemUnit.m_Red         = iUnit.transform:Find("Image_RedPoint_1")

        _ItemUnit.m_CoinIcon    = _ItemUnit.Button_Buy:Find("Button_Buy/IMG_Money"):GetComponent(typeof( Image ))
        _ItemUnit.m_Money       = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_Money"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _ItemUnit.m_MoneyDis    = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_MoneyDis"):GetComponent(typeof( TMPro.TextMeshProUGUI ))

        _ItemUnit.m_ScrollView  = iUnit.transform:Find("Detail_Obj")
        _ItemUnit.m_Content     = iUnit.transform:Find("Detail_Obj/Content")

        _ItemUnit.m_ScrollViewComponent = iUnit.transform:Find("Detail_Obj")
    elseif iPnl == EStorePnl.Pnl3 then
        _ItemUnit.gameObject    = iUnit.gameObject
        _ItemUnit.Group_Image   = iUnit.transform:Find("Group_Image")
        _ItemUnit.m_Name        = iUnit.transform:Find("TMP_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_Discount    = iUnit.transform:Find("TMP_Discount"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _ItemUnit.m_CoinIcon    = iUnit.transform:Find("IMG_Money"):GetComponent(typeof( Image ))
        _ItemUnit.Button_Buy    = iUnit.transform:Find("Block_BuyButton")
        _ItemUnit.BtnBuy        = iUnit.transform:Find("Block_BuyButton/Button_Buy")
        _ItemUnit.m_DisBK       = iUnit.transform:Find("DisBK"):GetComponent(typeof( Image ))
        _ItemUnit.m_Icon        = iUnit.transform:Find("Icon"):GetComponent(typeof( Image ))

        _ItemUnit.m_Money       = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_Money"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _ItemUnit.m_MoneyDis    = _ItemUnit.Button_Buy:Find("Button_Buy/TMP_MoneyDis"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
    end

    _ItemUnit.gameObject.transform:SetParent(iParent)
    return _ItemUnit
end
---設定 Item 的 Active
---@param iPnl number 版型種類
---@param iObject any 物件
---@param iBool boolean true 或 false
local function ItemSetActive(iPnl, iObject, iBool)
    if iPnl == EStorePnl.Pnl1 then
        if iObject.Item_Icon then
            if iObject.Item_Icon.gameObject then
                iObject.Item_Icon.gameObject:SetActive(iBool)
            end
        end
        iObject.m_Name.gameObject:SetActive(iBool)
        iObject.m_Count.gameObject:SetActive(iBool)
        iObject.m_Time.gameObject:SetActive(iBool)
        iObject.m_Discount.gameObject:SetActive(iBool)
        iObject.m_DisBK.gameObject:SetActive(iBool)
        iObject.m_Trade.gameObject:SetActive(iBool)
        iObject.Button_Buy.gameObject:SetActive(iBool)
        iObject.Group_Image.gameObject:SetActive(iBool)
        iObject.m_Red.gameObject:SetActive(iBool)
    elseif iPnl == EStorePnl.Pnl2 then
        iObject.m_Name.gameObject:SetActive(iBool)
        iObject.m_Count.gameObject:SetActive(iBool)
        iObject.m_Time.gameObject:SetActive(iBool)
        iObject.m_Discount.gameObject:SetActive(iBool)
        iObject.m_DisBK.gameObject:SetActive(iBool)
        iObject.m_Title.gameObject:SetActive(iBool)
        iObject.Button_Buy.gameObject:SetActive(iBool)
        iObject.Button_Find.gameObject:SetActive(iBool)
        iObject.Group_Image.gameObject:SetActive(iBool)
        iObject.m_Red.gameObject:SetActive(iBool)
        iObject.m_ScrollView.gameObject:SetActive(iBool)
    elseif iPnl == EStorePnl.Pnl3 then
        iObject.m_Name.gameObject:SetActive(iBool)
        iObject.Button_Buy.gameObject:SetActive(iBool)
        iObject.Group_Image.gameObject:SetActive(iBool)
        iObject.m_Discount.gameObject:SetActive(iBool)
        iObject.m_DisBK.gameObject:SetActive(iBool)
        iObject.m_CoinIcon.gameObject:SetActive(iBool)
        iObject.m_Icon.gameObject:SetActive(iBool)
    end
end
---生成物品橫排
---@param iPnl number 版型種類
---@param iProductBtn any 所有商品格
---@param iItemDetl any 單格
---@param iRowPnl any 橫列
local function MakeItemRow(iPnl, iProductBtn, iItemDetl, iRowPnl)
    ---ScrollView Content 寬度
    local _ViewRect = this.m_Content_Width.transform.rect.width
    ---每個商品的寬度
    local _ItemRect = iItemDetl.gameObject.transform.rect.width
    ---計算一排顯示幾個商品
    this.m_PerRowCount[iPnl] = Mathf.Floor(_ViewRect / _ItemRect)
    if not iProductBtn[1] then
        iProductBtn[1] = {}
    end
    ---如果 ScrollView 還沒生成
    if not this.m_IsInitScrollView[iPnl] then
        ---生成第一排就好
        for i = 1, this.m_PerRowCount[iPnl] do
            local _Unit = iRowPnl.transform:Find("Item_" .. i)
            if not _Unit then
                _Unit = iItemDetl.gameObject:Instantiate(iRowPnl.transform)
            end
            iProductBtn[1][i] = _Unit
            iProductBtn[1][i] = InitItem(iPnl, iProductBtn[1][i], iRowPnl.transform)
            ItemSetActive(iPnl, iProductBtn[1][i], true)
            iProductBtn[1][i].gameObject.name = "Item_"..i
        end
    end
    -----這邊是如果有切換解析度 整理物件數量用的
    for i = 1, this.m_ScrollViewRowCount[iPnl] do
        if iProductBtn[i] then
            ---目前一排有幾個物品物件
            local _TableCount = table.Count(iProductBtn[i])
            ---如果不夠 複製補足
            if _TableCount < this.m_PerRowCount[iPnl] then
                for j = _TableCount + 1, this.m_PerRowCount[iPnl] do
                    local _Unit = iProductBtn[i][1].gameObject.transform.parent:Find("Item_" .. j)
                    if not _Unit then
                        _Unit = iItemDetl.gameObject:Instantiate(iRowPnl.transform)
                    end
                    local _ItemUnit = InitItem(iPnl, _Unit, iProductBtn[i][1].gameObject.transform.parent)
                    local _Parent = _ItemUnit.gameObject.transform:Find("Icon")
                    ItemSetActive(iPnl, _ItemUnit, false)
                    table.insert(iProductBtn[i],_ItemUnit)
                    iProductBtn[i][j] = _ItemUnit
                    iProductBtn[i][j].gameObject.name = "Item_"..j
                    iProductBtn[i][j].Item_Icon = IconMgr.NewItemIcon(0, _Parent, DETAIL_SIZE)
                    iProductBtn[i][j].Item_Icon:SetClickTwice(false)
                    iProductBtn[i][j].Item_Icon.gameObject:SetActive(false)
                end
                ---如果多了就刪掉
            elseif _TableCount > this.m_PerRowCount[iPnl] then
                for j = this.m_PerRowCount[iPnl] + 1, _TableCount do
                    if iProductBtn[i][j].Item_Icon then
                        iProductBtn[i][j].Item_Icon.gameObject:Destroy()
                    end
                    iProductBtn[i][j].Item_Icon = nil
                    iProductBtn[i][j].gameObject:Destroy()
                    iProductBtn[i][j] = nil
                end
            end
        end
    end
end

---重設 ScrollView 位置
local function ResetScrollViewPos(iContent)
    local _Pos = iContent.transform.localPosition
    _Pos.y = 0
    iContent.transform.localPosition = _Pos
end

--生成 ItemIcon
local function MakeItemIcon(iBtn)
    for i = 1, table.Count(iBtn) do
        local _Parent = iBtn[i].gameObject.transform:Find("Icon")
        iBtn[i].Item_Icon = IconMgr.NewItemIcon(0, _Parent, DETAIL_SIZE)
        iBtn[i].Item_Icon:SetClickTwice(false)
        iBtn[i].Item_Icon.gameObject:SetActive(false)
    end
end

---開啟介面
---@param iParamTable table 參數表 [1]:商品類別(EProductKind)
function Mall_Controller.Open(iParam)
    Mall_Model.GetMallData()
    --預設開啟種類
    for i = 1, table.Count(Mall_Model.m_Mall_STab_Data) do
        if #Mall_Model.m_Mall_STab_Data[i] ~= 0 then
            this.m_SelKind = i
            break
        end
    end
    --取得直跳種類頁面
    if not table.IsNullOrEmpty(iParam) then
        this.m_SelKind = iParam[1]
        this.m_OpenKind = this.m_SelKind
    end

    _MainTabIdx = Mall_Model.GetMainByGroup(this.m_SelKind)

    if this.m_FullPageMenuLeft.m_IsInited then
        this.SetMainTagPnl()
    end

    TextureMgr.Load(Mall_Model.GetBanner(), true, function(iTex)
        if iTex == nil then return end
        this.m_UBanner.texture = iTex
    end)
    return true
end
---設定主頁籤資訊&明細列內容數
function Mall_Controller.SetMainTagPnl()
    InitMainTab()

    MakeItemRow(EStorePnl.Pnl1, this.m_ProductBtn, this.m_ItemDetial, this.m_ReuseItme)
    MakeItemRow(EStorePnl.Pnl2, this.m_ProductBBtn, this.m_ItemDetialP, this.m_ReuseItme_Package)
    MakeItemRow(EStorePnl.Pnl3, this.m_ProductDBtn, this.m_ItemDetialH, this.m_ReuseItme_Half)

    this.SetMainTags()
end

function Mall_Controller.Close()
    UIMgr.Close( ItemHint_Controller)
end

---刪除介面
function Mall_Controller.OnDestroy()
    _LaftTabInit = false
    this.m_IsInitScrollView = { }
    this.m_MainBtn = {}
    this.m_SubBtn = {}
    this.m_ScrollViewRowCount = {}
    this.m_PerRowCount = {}
    this.m_ProductBtn = {}
    this.m_ProductBBtn = {}
    this.m_ProductDBtn = {}
    this.DetailIconUnit = {}
    this.m_FullPageMenuLeft = {}
    GStateObserverManager.UnRegister(EStateObserver.PlayerLevelRefresh, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateOnHourTime, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateDiamond, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateCoin, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateHEMCoin, this)
    return true
end

---主頁籤設定
function Mall_Controller.SetMainTags()
    LeftGroupTab.OnPointerClickByIndex(this.m_FullPageMenuLeft, _MainTabIdx)
end
---按下主頁籤
function Mall_Controller.OnGroupTabMainBtnClick(iIndex)
    _MainTabIdx = iIndex
    --清除選取
    this.m_SubSelIndex = 0
    InitSubTab()
    this.SetSubTags()
end

---子頁籤設定
function Mall_Controller.SetSubTags()
    local _FirstTab = 0
    for i = 1, #this.m_SubBtn do
        this.m_SubBtn[i].gameObject:SetActive(Mall_Model.m_Mall_MTab_Data[_MainTabIdx].m_Tags[i] ~= 0)
        local _SubTag = Mall_Model.m_Mall_MTab_Data[_MainTabIdx].m_Tags[i]
        if _SubTag ~= 0 then
            if #Mall_Model.m_Mall_STab_Data[_SubTag] ~= 0 then
                SetSubTagTitle(this.m_SubBtn[i], EButtonTriggerTypeSuffix.Normal, false)
                this.m_SubBtn[i].Red.gameObject:SetActive(Mall_Model.CheckSubTabRedDot(_SubTag))
                if this.m_OpenKind ~= 0 then
                    _FirstTab = this.m_OpenKind
                    this.m_OpenKind = 0
                end
                if _FirstTab == 0 then
                    _FirstTab = _SubTag
                end
            else
                this.m_SubBtn[i].gameObject:SetActive(false)
            end
        end
    end
    _SubTabIdx = Mall_Model.GetSubByGroup(_MainTabIdx, _FirstTab)
    GroupButton.OnPointerClickByIndex(this.m_GObjSubBtn, _SubTabIdx + 1)
end
---按下子頁籤
function Mall_Controller.OnGroupTabSubBtnClick(iIndex)
    local _SubTagIdx = Mall_Model.m_Mall_MTab_Data[_MainTabIdx].m_Tags[iIndex]
    this.m_SelKind = _SubTagIdx
    Mall_Model.SetRedDot(this.m_SelKind)

    this.m_GObjectScrollView.gameObject:SetActive(Mall_Model.ShowPnl(this.m_SelKind) == EStorePnl.Pnl1)
    this.m_GObjectScrollView_Package.gameObject:SetActive(Mall_Model.ShowPnl(this.m_SelKind) == EStorePnl.Pnl2)
    this.m_GObjectScrollView_Half.transform.parent.gameObject:SetActive(Mall_Model.ShowPnl(this.m_SelKind) == EStorePnl.Pnl3)
    this.InitScrollView(Mall_Model.ShowPnl(this.m_SelKind))

    this.ScrollViewUpdate(Mall_Model.ShowPnl(this.m_SelKind))
end

function Mall_Controller.InitScrollView(iStorePnl)
    if iStorePnl == EStorePnl.Pnl1 and not this.m_IsInitScrollView[iStorePnl] then
        this.m_ScrollView = ScrollView.Init(this.m_GObjectScrollView, false, this.m_ReuseItme,
                Mall_Model.ProductPnlNum, this.AfterReuseItemInit,
                this.AfterReuseItemIndexUpdate, true)
    elseif iStorePnl == EStorePnl.Pnl2 and not this.m_IsInitScrollView[iStorePnl] then
        this.m_ScrollView_Package = ScrollView.Init(this.m_GObjectScrollView_Package, false, this.m_ReuseItme_Package,
                Mall_Model.ProductPnlNum, this.AfterPackageItemInit,
                this.AfterPackageItemIndexUpdate, true)
    elseif iStorePnl == EStorePnl.Pnl3 and not this.m_IsInitScrollView[iStorePnl] then
        this.m_ScrollView_Half = ScrollView.Init(this.m_GObjectScrollView_Half, false, this.m_ReuseItme_Half,
                Mall_Model.ProductPnlNum, this.AfterHalfItemInit,
                this.AfterHalfItemIndexUpdate, true)
    end
    this.m_IsInitScrollView[iStorePnl] = true
end
function Mall_Controller.ScrollViewUpdate(iStorePnl)
    if iStorePnl == EStorePnl.Pnl1 and this.m_IsInitScrollView[iStorePnl] then
        ScrollView.Update(this.m_ScrollView)
        ResetScrollViewPos(this.m_Content_U)
    elseif iStorePnl == EStorePnl.Pnl2 and this.m_IsInitScrollView[iStorePnl] then
        ScrollView.Update(this.m_ScrollView_Package)
        ResetScrollViewPos(this.m_Content_P)
    elseif iStorePnl == EStorePnl.Pnl3 and this.m_IsInitScrollView[iStorePnl] then
        ScrollView.Update(this.m_ScrollView_Half)
        ResetScrollViewPos(this.m_Content_H)
    end
end
---一般排版初始化
function Mall_Controller.AfterReuseItemInit(iItem, iRowIdx)

    if not this.m_ProductBtn[iRowIdx] then
        this.m_ProductBtn[iRowIdx] = {}
    end
    ---把物件結構弄好 存到 m_Table_ItemUnit
    for i = 1, this.m_PerRowCount[EStorePnl.Pnl1] do
        if not this.m_ProductBtn[iRowIdx][i] then
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i).gameObject
            this.m_ProductBtn[iRowIdx][i] = InitItem(EStorePnl.Pnl1, _Unit, iItem.m_GObj.transform)
        end

    end
    this.m_ScrollViewRowCount[EStorePnl.Pnl1] = this.m_ScrollViewRowCount[EStorePnl.Pnl1] + 1
    MakeItemIcon(this.m_ProductBtn[iRowIdx])
end
---點擊放大鏡
function Mall_Controller.ProductFindClick(iItem, iData)
    if(iItem == nil or iData == nil) then
        return
    end
end
---點擊商品
function Mall_Controller.ProductOnClick(iItem, iData, iListIdx)
    if(iItem == nil or iData == nil) then
        return
    end
    --- 商品數量
    ---記錄目前要買的物品 ID
    Mall_Model.m_BuyItem = iData
    Mall_Model.m_BuyItemListIdx = iListIdx

    --金流品項
    if Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.CashFlow then
        ---送金流
        UnityIAP.PurchaseProduct(Main_Model.GetFlowIdx(iData), function()
            D.Log("Buy Point")
        end)
    elseif Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.Store then   --商城品項
        ---送GS
        local _IsBindLock = false
        local _ItemData = ItemData.GetItemDataByIdx(Mall_Model.m_BuyItem.m_ItemIdx)
        if _ItemData then
            _IsBindLock = table.Contains(Mall_Model.m_Table_TradeItemLimit, _ItemData.m_Limit_Item)
        end

        local _Max = Mall_Model.ItemMaxNum(iData)

        local _MsgID = BUY_ITEM_MSG
        if _Max == 0 then
            _MsgID = BUY_ITEM_MSG_NOT_ENOUGH
        end

        ---設定 CommonQuery 的資料
        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
        ---Icon 區
        _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon, {Mall_Model.m_BuyItem.m_ItemIdx}, {1}, {false}, _IsBindLock)
        ---資源消耗
        _Type2Data:BuildConsumeTable({CurrencyItemID[iData.m_CostType]},{Mall_Model.CostOrSPrice(iData)})
        ---次數區
        _Type2Data:BuildTimeTable(_Max, 1, {1}, {},{Mall_Model.CostOrSPrice(iData)})

        CommonQueryMgr.AddNewInform(_MsgID,
                {},
                {},
                Mall_Controller.ProductSend,
                {},
                Mall_Controller.CancelBuy,
                {},
                nil,
                {},
                _Type2Data)
    end

end
---送出購買商品
function Mall_Controller.ProductSend()
    local _BuyNum = CommonQuery_Type2_Controller.GetCurrentTimes()

    if _BuyNum > Mall_Model.ItemMaxNum(Mall_Model.m_BuyItem) then
        ---時空幣購買不足自動跳儲值
        if  Mall_Model.m_BuyItem.m_CostType == EStoreCurrency.Diamond then
            Mall_Model.m_BuyItemCnt = _BuyNum
            ---計算不足額自動選取儲值金額
            this.m_GetMoneyItem = CashFlowData.GetDataByCash(Mall_Model.Deficit())
            local _ShowDiscount = Mall_Model.FirstBuy(this.m_GetMoneyItem)
            local _Type7Data = CustomCommonQueryData_Type7.NewData(_ShowDiscount, 20900010, this.m_GetMoneyItem.m_ItemPic, TextData.Get(this.m_GetMoneyItem.m_NameTIdx), ECurrencyIcon[EStoreCurrency.Diamond], Mall_Model.PayPriceF(EProductKind.Deposit, Main_Model.GetFlowIdx(this.m_GetMoneyItem)))

            CommonQueryMgr.AddNewInform(DEPOSIT_MSG,
                    {},
                    {Mall_Model.CostOrSPrice(Mall_Model.m_BuyItem), Mall_Model.Deficit()},
                    Mall_Controller.ProductBuy,
                    {},
                    Mall_Controller.CancelBuy,
                    {},
                    nil,
                    {},
                    _Type7Data)
        else
            MessageMgr.AddCenterMsg(false, TextData.Get(MONEY_NOT_ENOUGH_STR))
        end
    else
        local _Packet = {}

        _Packet.m_Idx = Mall_Model.m_BuyItem.m_Idx
        _Packet.m_Count = _BuyNum

        SendProtocol_012._005(1, _Packet)
    end
end
---購買金流物品+送出購買商品
function Mall_Controller.ProductBuy()
    UnityIAP.PurchaseProduct(Main_Model.GetFlowIdx(this.m_GetMoneyItem), function()
        D.Log("Buy Point")

        local _Packet = {}
        _Packet.m_Idx = Mall_Model.m_BuyItem.m_Idx
        _Packet.m_Count = Mall_Model.m_BuyItemCnt

        SendProtocol_012._005(1, _Packet)
    end)

    this.m_GetMoneyItem = nil
end
---購買完成+清購買資料
function Mall_Controller.FinishBuy()
    this.CancelBuy()
    --刷新後維持在本頁面
    this.m_OpenKind = this.m_SelKind
    _MainTabIdx = Mall_Model.GetMainByGroup(this.m_SelKind)
    this.SetMainTags()
end
---取消購買
function Mall_Controller.CancelBuy()
    Mall_Model.m_BuyItem = nil
    Mall_Model.m_BuyItemListIdx = 0
    Mall_Model.m_BuyItemCnt = 0
end
---一般排版刷新
function Mall_Controller.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    if iItem ~= nil then
        if  iRowIdx > Mall_Model.ProductPnlNum() then
            iItem.m_GObj.gameObject:SetActive(false)
            return
        else
            iItem.m_GObj.gameObject:SetActive(true)
        end
        for i = 1, this.m_PerRowCount[EStorePnl.Pnl1] do
            ---算一下第幾個 Index
            local _Index = (iRowIdx - 1) * this.m_PerRowCount[EStorePnl.Pnl1] + i
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i)
            local _ObjectIdx = tonumber(_Unit.transform.parent.name)
            local _ItemUnit = this.m_ProductBtn[_ObjectIdx][i]
            _ItemUnit.Item_Icon:SetMask(false)

            local _ItemCount = Mall_Model.ProductNum()
            if _Index <= _ItemCount and _ItemCount > 0 then
                _ItemUnit.Item_Idx = i -- _ItemIdx
                local _Data = Mall_Model.m_Mall_Product_Data[this.m_SelKind][_Index]
                if _Data ~= nil then
                    ItemSetActive(EStorePnl.Pnl1, _ItemUnit, true)
                    local _ItemData = ItemData.GetItemDataByIdx(_Data.m_ItemIdx)
                    if _ItemData == nil then
                        if ProjectMgr.IsDebug() then --Debug無物品顯示
                            _ItemUnit.m_Name.text = "物品表無此商品" .. _Data.m_ItemIdx
                        end
                        _ItemUnit.Item_Icon:RefreshIcon(0)
                        _ItemUnit.m_Trade.gameObject:SetActive(false)
                        Button.ClearListener(this.m_ProductBtn[_ObjectIdx][i].BtnBuy)
                    else
                        _ItemUnit.m_Name.text = TextData.Get(_ItemData.m_Idx_ItemNameText)
                        _ItemUnit.Item_Icon:RefreshIcon(_ItemData.m_Idx)
                        _ItemUnit.m_Trade.gameObject:SetActive(table.Contains(Mall_Model.m_Table_TradeItemLimit, _ItemData.m_Limit_Item))

                        Button.ClearListener(this.m_ProductBtn[_ObjectIdx][i].BtnBuy)
                        Button.AddListener(this.m_ProductBtn[_ObjectIdx][i].BtnBuy, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductOnClick(iItem, _Data, _Index)
                        end)
                        if Mall_Model.SellOut(_Data) then
                            Button.SetDisable(this.m_ProductBtn[_ObjectIdx][i].BtnBuy, false)
                        else
                            Button.SetEnable(this.m_ProductBtn[_ObjectIdx][i].BtnBuy)
                        end
                    end
                    _ItemUnit.m_Time.text = Mall_Model.DateTransStr(_Data)
                    _ItemUnit.m_Discount.text = Mall_Model.OnSellStr(_Data)
                    _ItemUnit.m_Count.text = Mall_Model.LimitedQuantityStr(_Data)
                    _ItemUnit.m_Money.text = Mall_Model.CostOrSPrice(_Data)
                    _ItemUnit.m_MoneyDis.text = Mall_Model.DashCostStr(_Data)
                    _ItemUnit.m_MoneyDis.gameObject:SetActive(_ItemUnit.m_MoneyDis.text ~= "")

                    Mall_Model.CurrencyPic(_Data, _ItemUnit.m_CoinIcon)

                    _ItemUnit.m_Time.gameObject:SetActive(_ItemUnit.m_Time.text ~= "")
                    _ItemUnit.m_DisBK.gameObject:SetActive(_ItemUnit.m_Discount.text ~= "")
                    _ItemUnit.m_Red.gameObject:SetActive(Mall_Model.CheckProductRedDot(this.m_SelKind, _Index))
                else
                    ItemSetActive(EStorePnl.Pnl1, _ItemUnit, false)
                end
            else
                ItemSetActive(EStorePnl.Pnl1, _ItemUnit, false)
            end
        end
    end
end
---組合排版初始化
function Mall_Controller.AfterPackageItemInit(iItem, iRowIdx)
    if not this.m_ProductBBtn[iRowIdx] then
        this.m_ProductBBtn[iRowIdx] = {}
    end
    ---把物件結構弄好 存到 m_Table_ItemUnit
    for i = 1, this.m_PerRowCount[EStorePnl.Pnl2] do
        if not this.m_ProductBBtn[iRowIdx][i] then
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i).gameObject
            this.m_ProductBBtn[iRowIdx][i] = InitItem(EStorePnl.Pnl2, _Unit, iItem.m_GObj.transform)
        end
    end
    this.m_ScrollViewRowCount[EStorePnl.Pnl2] = this.m_ScrollViewRowCount[EStorePnl.Pnl2] + 1
end
---組合排版更新
function Mall_Controller.AfterPackageItemIndexUpdate(iItem, iRowIdx)
    if iItem ~= nil then
        if  iRowIdx > Mall_Model.ProductPnlNum() then
            iItem.m_GObj.gameObject:SetActive(false)
            return
        else
            iItem.m_GObj.gameObject:SetActive(true)
        end
        for i = 1, this.m_PerRowCount[EStorePnl.Pnl2] do
            ---算一下第幾個 Index
            local _Index = (iRowIdx - 1) * this.m_PerRowCount[EStorePnl.Pnl2] + i
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i)
            local _ObjectIdx = tonumber(_Unit.transform.parent.name)
            local _ItemUnit = this.m_ProductBBtn[_ObjectIdx][i]

            local _ItemCount = Mall_Model.ProductNum()
            if _Index <= _ItemCount and _ItemCount > 0 then
                _ItemUnit.Item_Idx = i
                local _Data = Mall_Model.m_Mall_Product_Data[this.m_SelKind][_Index]
                if _Data ~= nil then
                    ItemSetActive(EStorePnl.Pnl2, _ItemUnit, true)
                    local _ItemData
                    --金流品項
                    if Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.CashFlow then
                        _ItemUnit.m_Title.text = Mall_Model.ProductTitle(_Data)

                        _Data.m_Loop = 0    --金流無Loop
                        _ItemUnit.m_Time.text = Mall_Model.DateTransStr(_Data)
                        _ItemUnit.m_Discount.text = Mall_Model.OnSellPackageStr(_Data)
                        _ItemUnit.m_Count.text = Mall_Model.CashLimitedQuantityStr(_Data)
                        _ItemUnit.m_Money.text = Mall_Model.PayPrice(_Index)
                        if _ItemUnit.m_Discount.text ~= "" then
                            _ItemUnit.m_Name.text = TextData.Get(_Data.m_FirstPurchaseBonusDescTIdx)
                        else
                            _ItemUnit.m_Name.text = TextData.Get(_Data.m_NameTIdx)
                        end
                        _ItemUnit.m_MoneyDis.text = ""
                        _ItemUnit.m_CoinIcon.gameObject:SetActive(false)

                        _ItemUnit.m_Time.gameObject:SetActive(_ItemUnit.m_Time.text ~= "")
                        _ItemUnit.m_DisBK.gameObject:SetActive(_ItemUnit.m_Discount.text ~= "")
                        _ItemUnit.m_Red.gameObject:SetActive(Mall_Model.CheckProductRedDot(this.m_SelKind, _Index))

                        Button.ClearListener(this.m_ProductBBtn[_ObjectIdx][i].Button_Find)
                        Button.AddListener(this.m_ProductBBtn[_ObjectIdx][i].Button_Find, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductFindClick(iItem, _Data)
                        end)

                        Button.ClearListener(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy)
                        Button.AddListener(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductOnClick(iItem, _Data, _Index)
                        end)
                    elseif Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.Store then  --商城品項
                        _ItemData = ItemData.GetItemDataByIdx(_Data.m_ItemIdx)
                        if _ItemData == nil then
                            if ProjectMgr.IsDebug() then --Debug無物品顯示
                                _ItemUnit.m_Name.text = "物品表無此商品" .. _Data.m_ItemIdx
                            end
                            _ItemUnit.m_Red.gameObject:SetActive(false)
                        else
                            _ItemUnit.m_Name.text = TextData.Get(_ItemData.m_Idx_ItemNameText)
                        end
                        _ItemUnit.m_Title.text = Mall_Model.ProductTitle(_Data)
                        _ItemUnit.m_Time.text = Mall_Model.DateTransStr(_Data)
                        _ItemUnit.m_Discount.text = Mall_Model.OnSellPackageStr(_Data)
                        _ItemUnit.m_Count.text = Mall_Model.LimitedQuantityStr(_Data)
                        _ItemUnit.m_Money.text = Mall_Model.CostOrSPrice(_Data)
                        _ItemUnit.m_MoneyDis.text = Mall_Model.DashCostStr(_Data)
                        _ItemUnit.m_MoneyDis.gameObject:SetActive(_ItemUnit.m_MoneyDis.text ~= "")

                        _ItemUnit.m_CoinIcon.gameObject:SetActive(true)
                        Mall_Model.CurrencyPic(_Data, _ItemUnit.m_CoinIcon)

                        _ItemUnit.m_Time.gameObject:SetActive(_ItemUnit.m_Time.text ~= "")
                        _ItemUnit.m_DisBK.gameObject:SetActive(_ItemUnit.m_Discount.text ~= "")
                        _ItemUnit.m_Red.gameObject:SetActive(Mall_Model.CheckProductRedDot(this.m_SelKind, _Index))

                        Button.ClearListener(this.m_ProductBBtn[_ObjectIdx][i].Button_Find)
                        Button.AddListener(this.m_ProductBBtn[_ObjectIdx][i].Button_Find, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductFindClick(iItem, _Data)
                        end)

                        Button.ClearListener(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy)
                        Button.AddListener(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductOnClick(iItem, _Data, _Index)
                        end)
                        if Mall_Model.SellOut(_Data) then
                            Button.SetDisable(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy, false)
                        else
                            Button.SetEnable(this.m_ProductBBtn[_ObjectIdx][i].BtnBuy)
                        end
                    end

                    if this.DetailIconUnit[_Index] == nil then
                        this.DetailIconUnit[_Index] = {}
                    end

                    this.SetDetialIcon(_Index, this.m_DIcon, _ItemUnit.m_Content)
                else
                    ItemSetActive(EStorePnl.Pnl2, _ItemUnit, false)
                end
            else
                ItemSetActive(EStorePnl.Pnl2, _ItemUnit, false)
            end

        end
    end
end
---設定禮包內容
function Mall_Controller.SetDetialIcon(iIdx, iIcon, iContent)
    ---取禮包資料
    local _FuDaiData = Mall_Model.GetRewardPackage(this.m_SelKind, iIdx)

    local _Count = 0
    if _FuDaiData ~= nil then
        ---禮包內容數
        _Count = Mall_Model.GetRewardNum(this.m_SelKind, _FuDaiData)
        for i = 1, _Count do
            Mall_Controller.SetRowItem(iIdx, i, iIcon, iContent)
        end
    end
    if #this.DetailIconUnit[iIdx] > _Count then
        for i = _Count + 1, #this.DetailIconUnit[iIdx]  do
            IconMgr.RemoveIcon(this.DetailIconUnit[iIdx][i].ItemIcon)
            iIcon:Store(this.DetailIconUnit[iIdx][i].gameObject)
            this.DetailIconUnit[iIdx][i] = nil
        end
    end
end
---設定內容ICON
function Mall_Controller.SetRowItem(iIdx, iDIdx, iIcon, iContent)
    if Mall_Model.m_Mall_Product_Data[this.m_SelKind][iIdx] == nil then
        return
    end
    ---取得禮包資料
    local _FuDaiData = Mall_Model.GetRewardPackage(this.m_SelKind, iIdx)
    if _FuDaiData == nil then
        return
    end
    local _ItemID = 0
    local _ItemNum = 0
    if Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.Store then  --商城品項
        if _FuDaiData.m_Rewards[iDIdx].m_ItemType ~= EFuDaiRewardType.Pet then
            _ItemID = _FuDaiData.m_Rewards[iDIdx].m_ItemIdx
        else
            _ItemID = PetData.GetPetDataByIdx(_FuDaiData.m_Rewards[iDIdx].m_ItemIdx).m_PartItemID
        end
        _ItemNum = _FuDaiData.m_Rewards[iDIdx].m_Num
    elseif Mall_Model.m_Mall_STab_Data[this.m_SelKind][1].m_CallUI == EStoreSwitch.CashFlow then  --金流品項
        _ItemID = _FuDaiData.m_Contents[iDIdx].m_ItemIdx
        _ItemNum = _FuDaiData.m_Contents[iDIdx].m_Count
    end

    if _ItemID == 0 then
        return
    end

    local _Unit
    local _IconUnit = {}

    if this.DetailIconUnit[iIdx][iDIdx] ~= nil then
        _IconUnit = this.DetailIconUnit[iIdx][iDIdx]
        _IconUnit.ItemIcon:RefreshIcon( _ItemID )
        _IconUnit.ItemIcon:SetCount( _ItemNum )
    else
        _Unit = iIcon:Get()
        _IconUnit.gameObject = _Unit
        _IconUnit.base = _Unit.transform:Find("Image_Bg" )
        _IconUnit.ItemIcon = IconMgr.NewItemIcon(_ItemID, _IconUnit.base, DETAIL_ItemSIZE, nil, false)
        _IconUnit.ItemIcon:SetClickTwice( false )
        _IconUnit.ItemIcon:SetCount( _ItemNum )
    end

    --設定物件名稱
    _IconUnit.Name = "Unit_" .. _ItemID

    _IconUnit.gameObject.transform:SetParent(iContent)
    _IconUnit.gameObject.transform.localScale = Vector3.one
    _IconUnit.gameObject:SetActive(_IconUnit.ItemIcon.m_Idx ~= 0)

    this.DetailIconUnit[iIdx][iDIdx] = _IconUnit
end
---金流排版初始化
function Mall_Controller.AfterHalfItemInit(iItem, iRowIdx)
    if not this.m_ProductDBtn[iRowIdx] then
        this.m_ProductDBtn[iRowIdx] = {}
    end
    ---把物件結構弄好 存到 m_Table_ItemUnit
    for i = 1, this.m_PerRowCount[EStorePnl.Pnl3] do
        if not this.m_ProductDBtn[iRowIdx][i] then
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i).gameObject
            this.m_ProductDBtn[iRowIdx][i] = InitItem(EStorePnl.Pnl3, _Unit, iItem.m_GObj.transform)
        end
    end
    this.m_ScrollViewRowCount[EStorePnl.Pnl3] = this.m_ScrollViewRowCount[EStorePnl.Pnl3] + 1
    MakeItemIcon(this.m_ProductDBtn[iRowIdx])
end
---金流排版刷新
function Mall_Controller.AfterHalfItemIndexUpdate(iItem, iRowIdx)
    if iItem ~= nil then
        if  iRowIdx > Mall_Model.ProductPnlNum() then
            iItem.m_GObj.gameObject:SetActive(false)
            return
        else
            iItem.m_GObj.gameObject:SetActive(true)
        end
        for i = 1, this.m_PerRowCount[EStorePnl.Pnl3] do
            ---算一下第幾個 Index
            local _Index = (iRowIdx - 1) * this.m_PerRowCount[EStorePnl.Pnl3] + i
            local _Unit = iItem.m_GObj.transform:Find("Item_"..i)
            local _ObjectIdx = tonumber(_Unit.transform.parent.name)
            local _ItemUnit = this.m_ProductDBtn[_ObjectIdx][i]

            local _ItemCount = Mall_Model.ProductNum()
            if _Index <= _ItemCount and _ItemCount > 0 then
                _ItemUnit.Item_Idx = i
                local _Data = Mall_Model.m_Mall_Product_Data[this.m_SelKind][_Index]
                if _Data ~= nil then
                    ItemSetActive(EStorePnl.Pnl3, _ItemUnit, true)

                    _ItemUnit.m_Name.text = TextData.Get(_Data.m_NameTIdx)
                    SpriteMgr.Load( _Data.m_ItemPic, _ItemUnit.m_Icon)
                    _ItemUnit.m_Icon.gameObject:GetComponent("Image").color = Color.White

                    _ItemUnit.m_Money.text = Mall_Model.PayPrice(_Index)
                    _ItemUnit.m_MoneyDis.gameObject:SetActive(false)

                    Button.ClearListener(this.m_ProductDBtn[_ObjectIdx][i].BtnBuy)
                    if _Data.m_Kind == EPurchaseKind.OncePurchase or _Data.m_Kind == EPurchaseKind.OnceGiftpack then
                        _ItemUnit.m_DisBK.gameObject:SetActive(Mall_Model.FirstBuy(_Data))
                        _ItemUnit.m_Discount.gameObject:SetActive(Mall_Model.FirstBuy(_Data))
                        if not PlayerData.IsHaveStaticFlag(_Data.m_FirstPurchaseFlag) then
                            Button.AddListener(this.m_ProductDBtn[_ObjectIdx][i].BtnBuy, EventTriggerType.PointerClick, function()
                                Mall_Controller.ProductOnClick(iItem, _Data, _Index)
                            end)
                        end
                    else
                        _ItemUnit.m_DisBK.gameObject:SetActive(Mall_Model.FirstBuy(_Data))
                        _ItemUnit.m_Discount.gameObject:SetActive(Mall_Model.FirstBuy(_Data))
                        Button.AddListener(this.m_ProductDBtn[_ObjectIdx][i].BtnBuy, EventTriggerType.PointerClick, function()
                            Mall_Controller.ProductOnClick(iItem, _Data, _Index)
                        end)
                    end
                else
                    ItemSetActive(EStorePnl.Pnl3, _ItemUnit, false)
                end
            else
                ItemSetActive(EStorePnl.Pnl3, _ItemUnit, false)
            end
        end
    end
end

---角色基礎屬性有更新會呼叫
---@param iEStateObserver EStateObserver 觀察者模式
function Mall_Controller:OnStateChanged(iEStateObserver, ...)
    if iEStateObserver == EStateObserver.PlayerLevelRefresh or iEStateObserver == EStateObserver.UpdateOnHourTime then
        Mall_Model.GetMallData()
        if UIMgr.IsVisible(Mall_Controller) then
            this.SetMainTags()
        end
        elseif iEStateObserver == EStateObserver.UpdateDiamond or iEStateObserver == EStateObserver.UpdateCoin or iEStateObserver == EStateObserver.UpdateHEMCoin then
        this.m_Group_Resources.m_Resources:OnUpdate()
    end
end