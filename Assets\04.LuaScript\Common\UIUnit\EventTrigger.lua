---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---EventTrigger 接口
---@class lua
---author 鼎翰
---telephone #2917
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.23
EventTrigger = {}

---回傳 EventTrigger
---@param iGObjEventTrigger GameObject 要加的按鈕 GameObject 
function EventTrigger.New(iGObjEventTrigger, iIsNeedScrollViewScroll)
	local _Obj = type(iGObjEventTrigger) == "userdata" and iGObjEventTrigger or iGObjEventTrigger.gameObject
    ---取得按鈕的 Script
	if Extension.IsUnityObjectNull(_Obj) then
		return
	end
	---做個功能 EventTrigger 的 table 給他
	local _EventTrigger = {}
	_EventTrigger.gameObject = _Obj.gameObject
	_EventTrigger.transform = _EventTrigger.gameObject.transform
	---取 _Script_ButtonEx 
	local _IsExists, _Script_EventTrigger = _EventTrigger.gameObject:TryGetComponent(typeof(UnityEngine.EventSystems.EventTrigger), nil)
    --沒取到 加 Component EventTrigger
    if not _IsExists  then
        _Script_EventTrigger = _EventTrigger.gameObject:AddComponent(typeof(UnityEngine.EventSystems.EventTrigger))
    end
    --設定 EventTrigger
	_EventTrigger.m_Script_EventTrigger = _Script_EventTrigger
	---存 EventTrigger.triggers 的 Index
	_EventTrigger.m_Table_EventIndex = {}
    --取得要繼承的 Table 或 userdata
    local _Parents = {}
    if type(iGObjEventTrigger) == "table" and iGObjEventTrigger.m_ButtonEx ~= nil then
		_EventTrigger.m_ButtonEx = iGObjEventTrigger.m_ButtonEx
        _Parents = {Button, EventTrigger}
    else
        _Parents = {EventTrigger}
    end
	
	setmetatable(_EventTrigger, {__index = function(t, ikey)
		if ikey == "m_ButtonEx" and table.Count(_Parents) <= 1 then
			return nil
		end
		for key, value in pairs(_Parents) do
			if table.ContainsKey(value, ikey) then
				return value[ikey]
			end
		end
		return nil
	end})

	if iIsNeedScrollViewScroll == nil then
		iIsNeedScrollViewScroll = false
	end

	if iIsNeedScrollViewScroll then
		_EventTrigger:AddScrollViewEvent(_EventTrigger.gameObject)
	end

	return _EventTrigger
end

---依照類型取得相對應的 Entry
---@param iTableEventTrigger table 要加的 EventTrigger
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@return UnityEngine.EventSystems.EventTrigger.Entry
local function GetCurrentEntry(iTableEventTrigger, iEventTriggerType)
	if not table.ContainsKey(iTableEventTrigger.m_Table_EventIndex, iEventTriggerType) then
		local _Entry = Entry.New()
		_Entry.eventID = iEventTriggerType
		iTableEventTrigger.m_Script_EventTrigger.triggers:Add(_Entry)
		iTableEventTrigger.m_Table_EventIndex[iEventTriggerType] = iTableEventTrigger.m_Script_EventTrigger.triggers:IndexOf(_Entry)
	end

	return iTableEventTrigger.m_Script_EventTrigger.triggers[iTableEventTrigger.m_Table_EventIndex[iEventTriggerType]]
end

local function IsHaveSameEvent(icallback)
	local _HaveEvent = false
	local _Count = icallback:GetPersistentEventCount()
	for i = 1, _Count do
		local _EventName = icallback:GetPersistentMethodName(i)
		if _EventName == "" then
			_HaveEvent = true
		end
	end
	return _HaveEvent
end

---依照類型取得相對應的 Entry
---@param iTableEventTrigger table EventTrigger 本人
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@param iUnityAction UnityAction 
---@return UnityEngine.EventSystems.EventTrigger.Entry
local function AddListenerByType(iTableEventTrigger, iEventTriggerType, iUnityAction)
	local _CurrentEntry = GetCurrentEntry(iTableEventTrigger, iEventTriggerType)
	_CurrentEntry.callback:AddListener(iUnityAction)
end

---依照類型取得相對應的 Entry
---@param iTableEventTrigger table EventTrigger 本人
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@return UnityEngine.EventSystems.EventTrigger.Entry
local function RemoveAllListenersByType(iTableEventTrigger, iEventTriggerType)
	GetCurrentEntry(iTableEventTrigger, iEventTriggerType).callback:RemoveAllListeners()
end

---依照類型加 Event
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@param iLuaFunction LuaFunction 要加的 LuaFunction
function EventTrigger:AddListener(iEventTriggerType, iLuaFunction, iParam)
	if type(self) == "userdata" or self.m_Script_EventTrigger == nil then
		self = EventTrigger.New(self)
	end
	local _EventAction
	if iParam then
		if type(iParam) ~= "table" then
			if type(iParam) == "number" and EParamNumber[iParam] == nil then
				EParamNumber[iParam] = {iParam}
			end

			if EParamNumber[iParam] then
				iParam = EParamNumber[iParam]
			else
				iParam = {iParam}
			end
		end
		
		_EventAction = UnityAction_BaseEventData(iLuaFunction, iParam)
	else
		_EventAction = UnityAction_BaseEventData(iLuaFunction)
	end

	AddListenerByType(self, iEventTriggerType, _EventAction)
end

---依照類型刪除 Event
---@param iEventTriggerType EventTriggerType 要刪除的 TriggerType
function EventTrigger:RemoveAllListenersByType(iEventTriggerType)
    if type(self) == "userdata" or self.m_Script_EventTrigger == nil then
		self = EventTrigger.New(self)
	end

	RemoveAllListenersByType(self, iEventTriggerType)
end

---刪除 EventTrigger.triggers 所有 UnityAction
function EventTrigger:RemoveAllListenersInTriggers()
    if type(self) == "userdata" or self.m_Script_EventTrigger == nil then
		self = EventTrigger.New(self)
	end
	for key, value in pairs(self.m_Table_EventIndex) do
		self.m_Script_EventTrigger.triggers[value].callback:RemoveAllListeners()
	end
end

---按鈕 觸發 Event
---@param iEventTriggerType EventTriggerType 要觸發的 TriggerType
function EventTrigger:OnEventTrigger(iEventTriggerType)
    if type(self) == "userdata" or self.m_Script_EventTrigger == nil then
		self = EventTrigger.New(self)
	end
	
	---目前要的 EventData
	local CurrentEventData = nil
	---是否要用 BaseEventData
	local _IsBaseEventData = iEventTriggerType == EventTriggerType.UpdateSelected or iEventTriggerType == EventTriggerType.Select or iEventTriggerType == EventTriggerType.Deselect
							or iEventTriggerType == EventTriggerType.Submit or iEventTriggerType == EventTriggerType.Cancel
	---是否要用 AxisEventData
	local _IsAxisEventData = iEventTriggerType == EventTriggerType.Move

	if _IsBaseEventData then
		CurrentEventData = UnityEngine.EventSystems.BaseEventData.New(UnityEngine.EventSystems.EventSystem.current)
	elseif _IsAxisEventData then
		CurrentEventData = UnityEngine.EventSystems.AxisEventData.New(UnityEngine.EventSystems.EventSystem.current)
	else
		CurrentEventData = UnityEngine.EventSystems.PointerEventData.New(UnityEngine.EventSystems.EventSystem.current)
	end

	GetCurrentEntry(self, iEventTriggerType).callback:Invoke(CurrentEventData)
end

local function OnBeginDrag(iSelf, iEventData)
	iSelf.m_ScriptScrollView:OnBeginDrag(iEventData)
end

local function OnDrag(iSelf, iEventData)
	iSelf:OnEventTrigger(EventTriggerType.PointerUp)
	iSelf.m_ScriptScrollView:OnDrag(iEventData)
end

local function OnEndDrag(iSelf, iEventData)
	iSelf.m_ScriptScrollView:OnEndDrag(iEventData)
end

local function OnPotentialDrag(iSelf, iEventData)
	iSelf.m_ScriptScrollView:OnInitializePotentialDrag(iEventData)
end

local function OnScroll(iSelf, iEventData)
	iSelf.m_ScriptScrollView:OnScroll(iEventData)
end

---最多要找幾層的父物件
local _CheckParentMaxCount = 10

---EventTrigger 父物件有 ScrollView 需要加 Event
function EventTrigger:AddScrollViewEvent(iGameObject)
	if type(self) == "userdata" or self.m_Script_EventTrigger == nil then
		self = EventTrigger.New(self)
	end

	local _ScriptScrollView = nil

	coroutine.start(function()
		local _CheckParentCount = 0

		while iGameObject ~= nil and _ScriptScrollView == nil and _CheckParentCount <= _CheckParentMaxCount do
			--先檢查 UIScrollView
			_ScriptScrollView = iGameObject:GetComponent("UIScrollView")
			if _ScriptScrollView == nil  then
				_ScriptScrollView = iGameObject:GetComponent("ScrollRect")
			end
			if _ScriptScrollView == nil  then
				iGameObject = iGameObject.transform.parent
			end
			_CheckParentCount = _CheckParentCount + 1
			coroutine.wait(0.1)
		end

		---找不到就不找了
		if _ScriptScrollView == nil then
			coroutine.stop()
			return
		end
		
		if _ScriptScrollView ~= nil and self.m_ScriptScrollView == nil then
			self.m_ScriptScrollView = _ScriptScrollView
		end

		local _EventBeginDragAction = UnityAction_BaseEventData(OnBeginDrag, self)
		AddListenerByType(self, EventTriggerType.BeginDrag, _EventBeginDragAction)

		local _EventActionOnDrag = UnityAction_BaseEventData(OnDrag, self)
		AddListenerByType(self, EventTriggerType.Drag, _EventActionOnDrag)

		local _EventActionOnEndDrag = UnityAction_BaseEventData(OnEndDrag, self)
		AddListenerByType(self, EventTriggerType.EndDrag, _EventActionOnEndDrag)

		local _EventActionOnInitializePotentialDrag = UnityAction_BaseEventData(OnPotentialDrag, self)
		AddListenerByType(self, EventTriggerType.InitializePotentialDrag, _EventActionOnInitializePotentialDrag)

		local _EventActionOnScroll = UnityAction_BaseEventData(OnScroll, self)
		AddListenerByType(self, EventTriggerType.Scroll, _EventActionOnScroll)
		coroutine.stop()
	end)
end
