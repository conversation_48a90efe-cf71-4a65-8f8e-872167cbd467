---攻擊
AtkState = {}
local this = AtkState

function AtkState:New(iStateType, iStateCtr)
    local _myself = {
        m_StateType = iStateType,
        m_StateCtr = iStateCtr
    }
    setmetatable(_myself, {__index = self})

    return _myself
end

function AtkState:OnEnter()
    local _RC = self.m_StateCtr.m_RoleController
    if _RC.m_IsSelf then
        ---進入攻擊狀態後 同時檢查 血量 攻擊 防禦 狀態 
        AutoDrink_Model.AutoDrink(EBaseValues.HP)
        AutoDrink_Model.AutoDrink(EPotionType.AtkPotion)
        AutoDrink_Model.AutoDrink(EPotionType.DefPotion)
    else
        --FIXME: 因為不會脫離攻擊階段 其他物件會持續走路，先讓其他物件進來就不走
        _RC:GetModelController().m_Animator:SetFloat(AnimationHash.Float_Movement, 0)
    end

    if _RC.m_RoleType == ERoleType.NPC then
        --TODO: 判斷是否為寵物、判斷是否為施放技能而非普攻
        BubbleMgr.StartBubbleFight(_RC.m_SID, _RC.m_TalkAction.m_FightTalkID, _RC.m_TalkAction.m_TalkMode)
    end
end

function AtkState:OnUpdate()
    ---玩家自己
    ---@type RoleController
    local _RC = self.m_StateCtr.m_RoleController
    local _PlayingSkillActData = _RC.m_NowPlayingSkillActData
    if not _PlayingSkillActData then
        BattleMgr.ShowLog("等待 S 傳來使用技能")
        return
    end
    
    if _RC.m_IsSelf then
        if _PlayingSkillActData.m_SkillTimeCount and _PlayingSkillActData.m_SkillTimeCount < _PlayingSkillActData.m_SkillEndTime then
            _PlayingSkillActData.m_SkillTimeCount = _PlayingSkillActData.m_SkillTimeCount + Time.deltaTime
            
            local _TempSkillData = BattleMgr.m_NowSkillData
            local _TempTickIdx = BattleMgr.m_NowTickIdx
            if _TempSkillData and _TempSkillData.m_TickAy[_TempTickIdx] and _TempSkillData.m_TickAy[_TempTickIdx].m_TickId ~= 0 then
                if _PlayingSkillActData.m_SkillTimeCount >= (_TempSkillData.m_TickAy[BattleMgr.m_NowTickIdx].m_TickTime * 0.001) / _RC.m_AtkSpeed then
                    local _TickData = TickData.GetTickDataByIdx(_TempSkillData.m_TickAy[_TempTickIdx].m_TickId)
                    local _TargetType = 0 -- 沒有目標
                    local _TickTargetCamp = _TickData.m_TargetCamp
                    ---根據tick的設定來決定施放的目標
                    local _CastTarget = _RC

                    if BattleMgr.m_Target ~= _RC then
                        --只有自己/範圍目標為自己
                        if _TickTargetCamp == 2 or _TickData.m_RangeTarget == 0 then
                            _CastTarget = _RC
                        --友方、自己與友方
                        elseif _TickTargetCamp == 3 or _TickTargetCamp == 13 then
                            if not BattleMgr.m_Target.m_Relation[ERelatType.Enemy] then
                                _CastTarget = BattleMgr.m_Target
                            end
                        else
                            _CastTarget = BattleMgr.m_Target
                        end
                    end
                    
                    --- 位移路徑是否遭阻擋,計算後的位移終點
                    local _destination
                    --- 玩家現位置
                    local _PlayerPos = MoveMgr.m_MoveStart
                    ---位移種類為否則不需位移
                    if _TickData.m_MoveType and BattleMgr.m_NowSkillMove then
                        _destination = BattleMgr.m_NowSkillMove
                        MoveMgr.DebugLog("送真正戰鬥位移終點 x: ".._destination.x.." y:".._destination.z)
                        --被反映不即時所以加在這
                        MoveMgr.ForceUpdatePos(_destination)
                        SearchMgr.AutoSearchTarget()
                    else
                        _destination = Vector3.New()
                        _destination:Copy(_PlayerPos)
                        MoveMgr.DebugLog("不用動的Tick x: ".._destination.x.." y:".._destination.z)

                    end

                    -- Debug功能，顯示技能範圍
                    if not ProjectMgr.IsRelease() and BattleMgr.m_IsShowSkillArea.Client then
                        local _Offset_Y = 0.2 -- Y軸偏移，讓線段高一點
                        local _selfPos = Vector3(_PlayerPos.x, _PlayerPos.y + _Offset_Y, _PlayerPos.z)

                        if _TickData.m_RangeType == TickData.ERangeType.Single then
                            --單體
                            if _TickData.m_BasicDistance > 0 then
                                CameraMgr.DrawWire(BattleMgr.m_IsShowSkillArea.Client, _selfPos, _TickData.m_BasicDistance/100, nil)
                            end

                        elseif _TickData.m_RangeType == TickData.ERangeType.Straight or _TickData.m_RangeType == TickData.ERangeType.Penetrate then
                            -- 線形
                            if _TickData.m_BasicDistance > 0 and _TickData.m_BasicWidth > 0 then
                                -- 算出矩形大小
                                local _Size = Vector3(_TickData.m_BasicWidth/100, _Offset_Y, _TickData.m_BasicDistance/100)
                                -- 畫它
                                CameraMgr.DrawWire(BattleMgr.m_IsShowSkillArea.Client, _selfPos, nil, _Size,nil, _RC.m_ModelObject.transform)
                            end

                        elseif _TickData.m_RangeType == TickData.ERangeType.Circle then
                            --圓形
                            if _TickData.m_BasicDistance > 0 then
                                CameraMgr.DrawWire(BattleMgr.m_IsShowSkillArea.Client, _selfPos, _TickData.m_BasicDistance/100, nil)
                            end

                            if _TickData.m_BasicWidth > 0 then
                                -- 有目標
                                if _CastTarget and _CastTarget.transform then
                                    local _CastPos = _CastTarget.transform.position
                                    _CastPos.y = _CastPos.y + _Offset_Y
                                    CameraMgr.DrawWire(BattleMgr.m_IsShowSkillArea.Client, _CastPos, _TickData.m_BasicWidth/100, nil) 
                                end
                            end
                        elseif _TickData.m_RangeType == TickData.ERangeType.FanShap then
                            if _TickData.m_BasicDistance > 0 then
                                CameraMgr.DrawWire(BattleMgr.m_IsShowSkillArea.Client, _selfPos, _TickData.m_BasicDistance/100, nil,_TickData.m_BasicWidth,_RC.m_ModelObject.transform)
                            end
                        end
                    end

                    local _TickPos

                    if _TickData.m_RangeType == TickData.ERangeType.Straight or _TickData.m_RangeType == TickData.ERangeType.FanShap or 
                        _TickData.m_RangeType == TickData.ERangeType.Penetrate then
                        _TargetType = ETargetKind.Floor

                        --- 這幾種類型都直接取最遠距離
                        BattleMgr.m_NowRangePos = BattleMgr.GetFarPos(_TempSkillData)
                        
                        local _TempPos = GFunction.ScenePosToServerPos(BattleMgr.m_NowRangePos)
                        _TickPos = BattleMgr.m_NowRangePos
			
                        if _CastTarget == nil then
                            _CastTarget = {}
                        end
                        _CastTarget.x = _TempPos.x
                        _CastTarget.y = _TempPos.y
                    elseif _TickData.m_RangeType == TickData.ERangeType.Appoint or _TickData.m_RangeType == TickData.ERangeType.AppointMagic 
                        or _TickData.m_RangeType == TickData.ERangeType.AppointDelate or _TickData.m_RangeType == TickData.ERangeType.AppointMagicDelate then
                        _TargetType = ETargetKind.Floor
                        -- 這邊拿來算對地板施放的技能座標
                        local _TempPos = GFunction.ScenePosToServerPos(BattleMgr.m_NowRangePos)
                        _TickPos = BattleMgr.m_NowRangePos
                        if _CastTarget == nil then
                            _CastTarget = {}
                        end
                        _CastTarget.x = _TempPos.x
                        _CastTarget.y = _TempPos.y
                    else
                        if _CastTarget then
                            _TickPos = _CastTarget.transform.position

                            if _CastTarget.m_PlayerID then
                                _TargetType = ETargetKind.Player
                                _CastTarget = _CastTarget.m_PlayerID
                            elseif _CastTarget.m_NPCID then
                                _TargetType = ETargetKind.Npc
                                _CastTarget = _CastTarget.m_SID
                            end
                        
                        end
                    end
    
                    if not ProjectMgr.IsRelease() and BattleMgr.m_IsShowSkillArea.Client and BattleMgr.m_BasePoint ~= nil then
                        BattleMgr.m_BasePoint.m_TickPos[_TempTickIdx].position = _TickPos
                        BattleMgr.m_BasePoint.m_TickPos[_TempTickIdx].gameObject:SetActive(true)
                        HEMTimeMgr.DoFunctionDelay(0.5,
                            function() if BattleMgr.m_BasePoint ~= nil then BattleMgr.m_BasePoint.m_TickPos[_TempTickIdx].gameObject:SetActive(false) end end)
                    end
    
                    if not ProjectMgr.IsRelease() and BattleMgr.m_IsShowSkillArea.Client and BattleMgr.m_BasePoint ~= nil then
                        BattleMgr.m_BasePoint.m_MovePos.position = _destination
                        BattleMgr.m_BasePoint.m_MovePos.gameObject:SetActive(true)
                        HEMTimeMgr.DoFunctionDelay(0.5,
                            function() if BattleMgr.m_BasePoint ~= nil then BattleMgr.m_BasePoint.m_MovePos.gameObject:SetActive(false) end end)
                    end

                    _destination = GFunction.ScenePosToServerPos(_destination)

                    SendProtocol_008._008(BattleMgr.m_NowTickIdx, _TargetType, _CastTarget, _TickData.m_MoveType, _destination)
                    
                    BattleMgr.m_NowTickIdx = BattleMgr.m_NowTickIdx + 1
                end
            end
        else
            --[[if BattleMgr.m_NowSkillData ~= nil and BattleMgr.m_NowSkillData.m_UseType ~= EHotKeyUseKind.Power then
                AutoBattleMgr.PlayerStartMove()
            end]]
            -- 技能結束回一般狀態
            if AutoBattleMgr.GetStateType() == EAutoBattleStateType.UseSkillState then
                AutoBattleMgr.ChangeState(EAutoBattleStateType.NormalState)
            end
            
            -- 結束這個 Skill
            if RoleMgr.IsJoystickOnDrag() then
                _RC.m_StateController:ChangeState(EStateType.Move)
            else
                _RC.m_StateController:ChangeState(EStateType.Idle)
            end

            HotKeyMgr.SetCancelSkill(false)
            HotKeyMgr.SetNowClickPCHotkey(nil)
            
            -- 檢查是否在自動戰鬥中，是的話就繼續下次普攻
            if BattleMgr.m_IsAutoNormalBattle and (SearchMgr.m_AutoSearch or table.count(SearchMgr.GetSearchList(ESearchKind.AttackMeRole)) > 0) then
                local _NormalAtkHotKeyUnit = HotKeyMgr.GetHotKeyUnit( EHotkeyArea.BattleAtkArea, PlayerData.GetWeaponType(), BattleMgr.m_PreUseSkillId )
                if _NormalAtkHotKeyUnit then
                    _NormalAtkHotKeyUnit:UseSkill()
                end
            else
                BattleMgr.SetAutoNormalBattle(false)
            end
        
            BattleMgr.m_AutoNormalBattleTime = HEMTimeMgr.time
        end
    else
        -- 除自己以外的
        _PlayingSkillActData.m_SkillTimeCount = _PlayingSkillActData.m_SkillTimeCount + Time.deltaTime
        if _PlayingSkillActData.m_SkillTimeCount >= _PlayingSkillActData.m_SkillEndTime then
            -- 結束這個技能就回到 idle
            _RC.m_StateController:ChangeState(EStateType.Idle)
        end
    end
    
    if _PlayingSkillActData.m_LoopStartTime and _PlayingSkillActData.m_NowActionData then
        local _RealSec = _PlayingSkillActData.m_NowActionData.m_LoopDuration * 0.001
        local _TimePass = HEMTimeMgr.time - _PlayingSkillActData.m_LoopStartTime
        -- 到達循環結束時間
        if _TimePass >= _RealSec then
            -- 強制觸發OnEnd
            _RC.OnEnd(_RC)

            --D.Log("Trigger On End At Lua." .. " Time Pass: " .. _TimePass .. " | Loop Duration: " .. _RealSec);
        end
    end
end

function AtkState:OnExit()
    if BattleMgr.m_IS_BATTLEEDITOR_DEBUG then
        BattleMgr.SetBattleEditorDebug(false)
    end

    if self.m_StateCtr.m_RoleController.m_IsSelf then
        BattleMgr.ResetData()
        MoveMgr.PlayerRestartVectorMove()

        if RoleMgr.IsJoystickOnDrag() then
            BattleMgr.SetAutoNormalBattle(false)
        end
    end

    self.m_StateCtr.m_RoleController:CancelSkill()
end
