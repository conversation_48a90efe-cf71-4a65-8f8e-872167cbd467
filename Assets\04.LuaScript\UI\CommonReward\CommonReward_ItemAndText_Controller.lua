---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---獎勵視窗 同時需要顯示獲得道具跟文字說明時使用
---@class CommonReward_ItemAndText_Controller
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.25
CommonReward_ItemAndText_Controller = {}
local this = CommonReward_ItemAndText_Controller

setmetatable( this, { __index = CommonRewardBase_Controller } )
this:New("CommonReward_ItemAndText_View", "CommonReward_ItemAndText_Controller", EUIOrderLayers.Peak)

---使用到的面板數量
local m_RewardPanelID = 0

---Scrollview 上限
local m_Height_Scrollview_Limit = 249.73

---物品icon 的高度
local m_IconHeight = 120
---物品icon 排與排的間距
local m_IconSpace = 11
---每一橫排顯示幾個物品icon 
local _Amount_PerLine = 5
---顯示所有物品需要幾排 (每次要重算)
local m_ShowItemNeedLine = 1

--- &Panel_Reward_ItemType 初始所佔的高度 (容許兩排icon)
local _Reward_ItemType_H_Initial = 380

---UIVisibleEffect 相關演出參數
local m_VisibleBatchTime_Start = 0.5
local m_VisibleBatchRate = 0.1
local m_VisiblePanelBatchRate = 1


function CommonReward_ItemAndText_Controller.Init()
    this:InitCommonReward()
    ---用來掛生成物的父物件
    this.m_Panel_RewardGroup = this.m_ViewRef.m_Dic_Trans:Get("&Panel_RewardGroup")
end

---面板顯示時，讓面板子物件也跑批次顯示
local function PanelBatchDelegate(iID, iPanel)
    if iPanel.m_VisibleBatch then
        if this.m_IsSkipped then
            iPanel.m_VisibleBatch:Stop(true)
        else
            iPanel.m_VisibleBatch:Start(m_VisibleBatchTime_Start)
        end
    end
end

---重開時要清理這裡的資料
local function CleanUpAndReset()
    this.m_IsSkipped = false

    this.m_Panel_Title.gameObject:SetActive(false)
    this.m_Panel_Title.anchoredPosition = Vector2.zero
    this.m_Panel_Title.gameObject:SetActive(true)

    
    for i = 1, m_RewardPanelID do
        
        --檢查是否有Icon有特效並歸還
        for _, _GObjData in pairs(this.m_NowPanels[CommonRewardSubDataType.ItemType][i].m_NowGObjs) do
            if _GObjData and _GObjData.m_Icon then
                if type(_GObjData.m_Icon.SetRankFrameEffect) == "function" then
                    _GObjData.m_Icon:SetRankFrameEffect(false, CommonReward_ItemAndText_Controller)
                end
                --TODO:如果有使用也要歸還
                --_GObjData.m_Icon:SetRankGrandPrizeEffect(false, CommonReward_ItemAndText_Controller)
            end
        end

        this.m_NowPanels[CommonRewardSubDataType.ItemType][i].gameObject:SetActive(false)
        this:CleanUpGObjInPanel(CommonRewardSubDataType.ItemType, i)
    end
    
    for i = 1, m_RewardPanelID do
        this.m_NowPanels[CommonRewardSubDataType.TextType][i].gameObject:SetActive(false)
        this:CleanUpGObjInPanel(CommonRewardSubDataType.TextType, i)
    end
    
    
end

---設定scrollview 的高度
---@param iScrollView GameObject 要被修改高度的ScrollView
---@param iLine numveber 需要要幾行來填icon們
local function SetScrollViewHeight(iScrollView ,iLine)
   
    local _RectTrans_ScrollView = iScrollView:GetComponent("RectTransform") 
    ---計算需要高度
    local _PreferedHeight = iLine*m_IconHeight + (iLine-1)*m_IconSpace
    ---取得ScrollView的高度/寬度
    local _SizeDelta = _RectTrans_ScrollView.sizeDelta
    ---設定高度 並確保不要超過最大值
    _SizeDelta.y = _PreferedHeight > m_Height_Scrollview_Limit and m_Height_Scrollview_Limit or _PreferedHeight
    _RectTrans_ScrollView.sizeDelta = _SizeDelta

end

---設定UI介面 道具部分
---@param iData table 要顯示的物品結構們
local function SetUpUIData_Item(iData)
    this.m_Text_RewardBigTitle.text = TextData.Get(iData.m_RewardTitle) 
    local _IsRewardMoreThanLineAllow = false

    local _Rect = nil
    for k, _RewardSubData in pairs(iData.m_RewardSubDatas)do 
        if this.m_NowPanels[CommonRewardSubDataType.ItemType] == nil or this.m_NowPanels[CommonRewardSubDataType.ItemType][k] == nil then 
            m_RewardPanelID = this:GetNewPanel(CommonRewardSubDataType.ItemType)
        end

        local _RewardPanelData = this.m_NowPanels[CommonRewardSubDataType.ItemType][k]

        ---顯示分頁
        if _RewardPanelData then

            _RewardPanelData.gameObject:SetActive(false)

            ---取得掛scrollview 的content
            _RewardPanelData.m_ItemDataScrollview_Scroll = _RewardPanelData.gameObject.transform:Find("ItemDataScrollview")
            _RewardPanelData.m_ItemDataScrollview_Viewport = _RewardPanelData.m_ItemDataScrollview_Scroll:Find("Viewport")
            _RewardPanelData.m_ItemDataScrollview_Content =  _RewardPanelData.m_ItemDataScrollview_Viewport:Find("ItemDataScrollview_Content")

            this.m_ItemDataScrollview_Scroll = _RewardPanelData.m_ItemDataScrollview_Scroll
            this.m_GridLayoutGroup = _RewardPanelData.m_ItemDataScrollview_Content:GetComponent(typeof(GridLayoutGroup))
            _Rect = _RewardPanelData.gameObject:GetComponent("RectTransform")

            ---調整物件UI 要掛到哪邊去
            _RewardPanelData.gameObject.transform:SetParent(this.m_Panel_RewardGroup)
            _RewardPanelData.gameObject.transform.localScale = Vector3.one
            --- 獲得以下獎勵 字串編號 20601503
            local _Str = iData.m_RewardTitle ~= nil and iData.m_RewardTitle or 20601503
            _RewardPanelData.m_Title.text = TextData.Get(_Str)

            m_ShowItemNeedLine = Mathf.Ceil(table.Count(_RewardSubData)/_Amount_PerLine)

            _IsRewardMoreThanLineAllow = table.Count(_RewardSubData) >= _Amount_PerLine

            for _k,_v in pairs(_RewardSubData)do
                
                local _GObjID = this:GetNewGObjInPanel(CommonRewardSubDataType.ItemType, m_RewardPanelID)
                local _GObjData = _RewardPanelData.m_NowGObjs[_GObjID]
                if _GObjData then
                    
                    ---沒填type默認使用ItemIcon
                    _v.m_IconType = (_v.m_IconType ~= nil) and _v.m_IconType or EIconType.Item

                    ---必須需判斷要使用 ItemIcon 或者 CommonIcon
                    if _v.m_IconType == EIconType.Item then
                        ---判斷是要刷新icon 或者 需要建立新的
                        if _GObjData.m_Icon  == nil  then
                            local _ItemIcon = IconMgr.NewItemIcon(_v.m_ItemID, _GObjData.m_IconParent, 108, nil, false)
                            _ItemIcon.m_TMP_Count.text = ""
                            _ItemIcon:SetClickTwice( false ) 
                            _ItemIcon:SetCount( _v.m_ItemAmount)
                            -- 判斷物品品階
                            if _ItemIcon.m_ItemData:GetERank() >= ERank.Purple then
                            
                                -- 設定獎品特效
                                _ItemIcon:SetRankFrameEffect(true, CommonReward_MultiItem_Controller)
                            end
                            
                            _GObjData.m_Icon = _ItemIcon
                        else
                            _GObjData.m_Icon:RefreshIcon(_v.m_ItemID)
                            _GObjData.m_Icon:SetCount( _v.m_ItemAmount)
                        end
                    elseif _v.m_IconType == EIconType.Common then

                        if _GObjData.m_Icon_Common  == nil  then
                            local _CommonIcon = IconMgr.NewCommonIcon(0, _GObjData.m_IconParent, 108, nil, false)
                            local _TitleData = TitleData.Get(_v.m_TitleID)
                            local _IconData = CommonIcon:SetIconData(_v.m_TitleID, _v.m_ACHIEVEMENT_ICONID, TextData.Get(_TitleData.m_TextID_Name))
                            _CommonIcon:RefreshIcon(_IconData, EIconType.Common)
                            _CommonIcon:SetLongPressCallback(CommonReward_ItemAndText_Controller.CommonIconOpenHint)
                            _CommonIcon:SetClickTwice(false)
                            _IconData.m_CommonIconData = _CommonIcon
                            
                            _GObjData.m_Icon_Common = _CommonIcon
                        else
                            local _TitleData = TitleData.Get(_v.m_TitleID)
                            local _IconData = CommonIcon:SetIconData(_v.m_TitleID, _v.m_ACHIEVEMENT_ICONID, TextData.Get(_TitleData.m_TextID_Name))
                            _GObjData.m_Icon_Common:RefreshIcon(_IconData, EIconType.Common)
                        end
                    elseif _v.m_IconType == EIconType.InnerSkill then

                        ---判斷是要刷新icon 或者 需要建立新的
                        if _GObjData.m_Icon  == nil  then
                            local _ItemIcon = IconMgr.NewInnerSkillIcon(0, _GObjData.m_IconParent, 108, nil, false)
                            _GObjData.m_Icon = _ItemIcon
                        end
                        if _v.m_IconTextureName == nil and _v.m_Step == nil then
                            _GObjData.m_Icon:RefreshIcon(_v.m_WugongID)
                        else
                            _GObjData.m_Icon:RefreshIcon_Method(_v.m_IconTextureName, _v.m_Step)
                        end
                    end
                    --一般技能先註解
                    --[[elseif _v.m_IconType == EIconType.Skill then

                        ---判斷是要刷新icon 或者 需要建立新的
                        if _GObjData.m_Icon  == nil  then
                            local _ItemIcon = IconMgr.NewSkillIcon(_v.m_WugongID, _GObjData.m_IconParent, 108, nil, false)
                            _ItemIcon:SetClickTwice( false )
                            _GObjData.m_Icon = _ItemIcon
                        else
                            _GObjData.m_Icon:RefreshIcon(_v.m_WugongID)
                        end]]

                    ---將物件設定到scrollview 下面
                    _GObjData.gameObject.transform:SetParent(_RewardPanelData.m_ItemDataScrollview_Content)

                    if _GObjData.gameObject ~=nil then
                        _GObjData.gameObject:SetActive(true)
                    end
                end
            end

            ---@type UIVisibleBatch
            _RewardPanelData.m_VisibleBatch = UIVisibleEffect.AddUIVisibleBatch(_RewardPanelData.m_NowGObjs,m_VisibleBatchRate)
        end
    end

    ---目前規劃只會有兩排 _IsRewardMoreThanLineAllow = true -> 有兩排道具Icon false -> 只有一排道具Icon
    if _IsRewardMoreThanLineAllow then
        local _sizeDelta =  _Rect.sizeDelta
        _sizeDelta.y = _Reward_ItemType_H_Initial
        _Rect.sizeDelta = _sizeDelta
    else
        local _sizeDelta =  _Rect.sizeDelta
        _sizeDelta.y = _Reward_ItemType_H_Initial - m_IconHeight
        _Rect.sizeDelta = _sizeDelta
    end

    

    this.m_PanelVisibleBatch = UIVisibleEffect.AddUIVisibleBatch(this.m_NowPanels[CommonRewardSubDataType.ItemType], m_VisiblePanelBatchRate, PanelBatchDelegate)
    this.m_PanelVisibleBatch:Start(CommonRewardMgr.TITLE_SHOW_DURATION)
end

---設定UI介面 文字部分
---@param iData table 要顯示的物品結構們
local function SetUpUIData_Text(iData)
    
    this.m_AllAddNumberBatch ={}
    for k, _RewardSubData in pairs(iData.m_RewardSubDatas_Text)do 
        if this.m_NowPanels[CommonRewardSubDataType.TextType] == nil or this.m_NowPanels[CommonRewardSubDataType.TextType][k] == nil then 
            m_RewardPanelID = this:GetNewPanel(CommonRewardSubDataType.TextType)
        end

        local _RewardPanelData = this.m_NowPanels[CommonRewardSubDataType.TextType][k]

        ---顯示分頁
        if _RewardPanelData then

            _RewardPanelData.gameObject:SetActive(false)


            ---調整物件UI 要掛到哪邊去
            _RewardPanelData.gameObject.transform:SetParent(this.m_Panel_RewardGroup)
            _RewardPanelData.gameObject.transform.localScale = Vector3.one
            ---探索積分 字串編號 20113305
            _RewardPanelData.m_Title.text = ""

            for _k,_v in pairs(_RewardSubData)do
                
                local _GObjID = this:GetNewGObjInPanel(CommonRewardSubDataType.TextType, m_RewardPanelID)
                local _GObjData = _RewardPanelData.m_NowGObjs[_GObjID]
                if _GObjData then

                    _GObjData.m_Title.text = _v.m_Title
                    if  _v.m_Content == nil or _v.m_Content == "" then

                        _GObjData.m_ValueFrom.gameObject:SetActive(true)
                        _GObjData.m_ValueTo.gameObject:SetActive(true)
                        _GObjData.m_ValueArrow.gameObject:SetActive(true)
                        _GObjData.m_Content.gameObject:SetActive(false)

                        
                        _GObjData.m_ValueFrom.text = _v.m_Number
                        _GObjData.m_ValueTo.text = _v.m_AddtoNumber
                    else

                        _GObjData.m_ValueFrom.gameObject:SetActive(false)
                        _GObjData.m_ValueTo.gameObject:SetActive(false)
                        _GObjData.m_ValueArrow.gameObject:SetActive(false)
                        _GObjData.m_Content.gameObject:SetActive(true)

                        _GObjData.m_Content.text =  _v.m_Content
                    end
                    
                    if _GObjData.gameObject ~=nil then
                        _GObjData.gameObject:SetActive(true)
                    end
                end
                
            end

            ---@type UIVisibleBatch
            _RewardPanelData.m_VisibleBatch = UIVisibleEffect.AddUIVisibleBatch(_RewardPanelData.m_NowGObjs, m_VisibleBatchRate)
        end
    end


    this.m_PanelVisibleBatch = UIVisibleEffect.AddUIVisibleBatch(this.m_NowPanels[CommonRewardSubDataType.TextType], m_VisiblePanelBatchRate, PanelBatchDelegate)
    this.m_PanelVisibleBatch:Start(CommonRewardMgr.TITLE_SHOW_DURATION)
end

---開啟 CommonReward_ItemAndText_Controller
---@param iParam table 需要帶入 用來顯示的參數
function CommonReward_ItemAndText_Controller.Open(iParam)
    
    CleanUpAndReset()

    if iParam ~= nil then
        local _CommonRewardData = iParam[1]
        SetUpUIData_Item(_CommonRewardData)
        SetUpUIData_Text(_CommonRewardData)
        
        SetScrollViewHeight( this.m_ItemDataScrollview_Scroll, m_ShowItemNeedLine)
        return true
    end
    
    return false

end

function CommonReward_ItemAndText_Controller.Close()

end

---UI刪掉時要把東西清一清
function CommonReward_ItemAndText_Controller.OnDestroy()
    this:OnUIDestroy()
    m_RewardPanelID = 0
    return true
end



---CommonHint 需要的圖片們
local _TitleAttributeMark = "<sprite name=Symbol_1>"
local _TitleDescriptionMark = "<sprite name=Symbol_3>"

--- 從CommonIcon 開稱號的方法
function CommonReward_ItemAndText_Controller.CommonIconOpenHint(iIconData)

    local _CommonHintData = {}

        local _TitleData = TitleData.Get(iIconData.m_Idx)

        local _HintBoxData = {}
        local _HintIdx = 1

        local function CreateHintBoxData(iHintBoxIdx, iPropertiesAy, iTitleStr)
            _HintBoxData[iHintBoxIdx] = CommonHint_Model.NewHintBox( iHintBoxIdx, EHintBoxType.KindAndValue, _TitleAttributeMark .. TextData.Get(iTitleStr) )
            local _AttributeList = {}
            for k, v in pairs(iPropertiesAy) do
                if v.m_Attr_ID ~= 0 then
                    -- 區塊內容
                    local _AttrString, _ValueString = GValue.AttributeToString(v.m_Attr_ID, v.m_Value, false)
                    CommonHint_Model.NewHintBoxContent_KindAndValue( _AttributeList, k, _AttrString, _ValueString )
                end
            end
            _HintBoxData[iHintBoxIdx].SetKindAndValueToHintBox(_AttributeList)

            return table.Count(_AttributeList) > 0
        end

        local function CreateTextHintBoxData(iHintBoxIdx, iTitleStr, iDescription)
            _HintBoxData[iHintBoxIdx] = CommonHint_Model.NewHintBox( iHintBoxIdx, EHintBoxType.NormalText, _TitleDescriptionMark .. TextData.Get(iTitleStr) )
            _HintBoxData[iHintBoxIdx].SetHintDescription(TextData.Get(iDescription))
        end

        local _GetPropertiesAy = CreateHintBoxData(_HintIdx, _TitleData.m_GetPropertiesAy, 510105)
        if _GetPropertiesAy then
            _HintIdx = _HintIdx + 1
        end
        local _EquipePropertiesAy = CreateHintBoxData(_HintIdx, _TitleData.m_EquipePropertiesAy, 510108)
        if _EquipePropertiesAy then
            _HintIdx = _HintIdx + 1
        end
        CreateTextHintBoxData(_HintIdx, 510104, _TitleData.m_TexIDt_Describe)

        -- CommonIcon 資料
        _CommonHintData[EHintBoxContentType.IconData] = iIconData.m_CommonIconData
        -- HintBox 內容
        _CommonHintData[EHintBoxContentType.HintContentData] = _HintBoxData
        HintMgr_Controller.OpenHint(EHintType.CommonHint, _CommonHintData)
end

--[[

-- 以下整串塞到 Main_Controller.Update() 可測試

if Input.GetKeyDown(KeyCode.N) then
    local _RewardData = {}
    
    _RewardData.m_RewardTitle = 530120
    local _ItemTable = {}

    for i = 1, 20 do
        _ItemTable[i] = {}
        _ItemTable[i].m_ItemID = 60501
        _ItemTable[i].m_ItemAmount = i
        local _ItemData = ItemData.GetItemDataByIdx(_ItemTable[i].m_ItemID)
        _ItemTable[i].m_Rarity = _ItemData.m_Rarity 
    end
    _RewardData.m_RewardSubDatas = {}
    _RewardData.m_RewardSubDatas_Text = {}

    for i = 1, 1 do
        _RewardData.m_RewardSubDatas[i] = {}
        for j = 1, 20 do
            table.insert(_RewardData.m_RewardSubDatas[i],_ItemTable[j])
        end

    end

    local _StringTable = {}
    for i = 1, 5 do
        _StringTable[i] = "文字"..i
    end

    for i = 1, 1 do
        _RewardData.m_RewardSubDatas_Text[i] = {}
        for j = 1, 5 do
            table.insert(_RewardData.m_RewardSubDatas_Text[i],_StringTable[j])
        end

    end

    CommonRewardMgr.AddNewReward(CommonRewardType.ItemAndText, _RewardData )
end
]]
