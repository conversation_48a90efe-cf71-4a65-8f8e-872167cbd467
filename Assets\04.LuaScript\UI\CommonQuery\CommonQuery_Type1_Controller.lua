---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---詢問視窗 大分類 1 純字串視窗
---@class CommonQuery_Type1_Controller
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.9
CommonQuery_Type1_Controller = {}
local this = CommonQuery_Type1_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("CommonQuery_Type1_View", "CommonQuery_Type1_Controller", EUIOrderLayers.Peak)


ECommonQueryType1_SubType =
{
    ---一般模式
    General =1,
    ---只有確認按鍵
    OnlyConfirm = 2,
    ---有時間條
    WithTimeBar = 3,
    ---顯示在最上方
    TopPanel = 4,
}


---初始面板高度
local m_InitialPanelHeight
---初始面板位置 y
local m_InitialPanel_Pos_Y

---初始 m_ContentArea 高度
local m_Initial_ContentArea_Height
---有時間提示區 內文區高度
local m_WithTimeHint_Height_WordArea = 171

---門檻行數以下 不調整視窗大小 超過則是要調整 是否有時間倒數的行數限制不同
local m_NoTimeHint_Limit = 4
local m_NoTimeHint_ContentHeight
local m_WithTimeHint_Limit = 1
local m_WithTimeHint_ContentHeight

---初始化
function CommonQuery_Type1_Controller.Init()

    ---變體123的母物件
    this.m_Obj_Type123_Parent = this.m_ViewRef.m_Dic_Trans:Get("&Trans_SubType123")
    ---subType 1,2,3 用的UI
    this.m_GObj_BackGroundParent = this.m_ViewRef.m_Dic_Trans:Get("&GObj_BackGroundParent").gameObject
    --面板初始的高度
    local _Panel_RectTransform =this.m_GObj_BackGroundParent:GetComponent("RectTransform")
    m_InitialPanelHeight = _Panel_RectTransform.rect.height
    m_InitialPanel_Pos_Y = this.m_GObj_BackGroundParent.transform.localPosition.y

    this.m_Text_TitleContent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TitleContent")

    this.m_ContentArea = this.m_ViewRef.m_Dic_Trans:Get("&ContentArea").gameObject
    local _Panel_RectTransform =this.m_ContentArea:GetComponent("RectTransform")
    m_Initial_ContentArea_Height = _Panel_RectTransform.rect.height



    ---內文區 Box
    this.m_WordBox = this.m_ViewRef.m_Dic_Trans:Get("&WordBox").gameObject
    this.m_Text_Content = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content")

    this.m_LayoutGroup_Button = this.m_ViewRef.m_Dic_Trans:Get("&LayoutGroup_Button").gameObject

    this.m_Btn_Cancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Cancel"))
    this.m_Btn_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm"))
    this.m_Btn_Special = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Special"))

    ---計時區域UI 父物件
    this.m_TimeHintBox = this.m_ViewRef.m_Dic_Trans:Get("&TimeHintBox").gameObject
    this.m_Slider_LitmitTime = this.m_ViewRef.m_Dic_Slider:Get("&Slider_LimitTime")
    this.m_Object_LitmitTime = this.m_ViewRef.m_Dic_Trans:Get("&Slider_LimitTime").gameObject
    this.m_Text_TimeHint = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TimeHint")


    ---是否需要更新計時器時間
    this.m_IsNeedUpdateCounterTime = false
    ---當前計時器類型
    this.m_CounterType = 0;
    ---當前計時器時間
    this.m_CounterTime = 0
    ---當前計時器時間上限
    this.m_MaxCounterTime = 5


    ---變體4的母物件
    this.m_Obj_Type4_Parent = this.m_ViewRef.m_Dic_Trans:Get("&Trans_SubType4")
    ---變體4的UI
    this.m_Img_Type4BackGround =  this.m_ViewRef.m_Dic_Trans:Get("&Image_Type4BG")
    this.m_Text_Type4Content = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Type4_Content")
    this.m_Btn_Type4_Cancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Type4_Cancel"))
    this.m_Btn_Type4_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Type4_Confirm"))
    this.m_Btn_Type4_Special = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Type4_Special"))

    ---分隔線
    this.m_Img_SeperateLine = this.m_ViewRef.m_Dic_Trans:Get("&Img_SeperateLine").gameObject


     m_NoTimeHint_ContentHeight = m_NoTimeHint_Limit*this.m_Text_Content.fontSize + (m_NoTimeHint_Limit-1)*(this.m_Text_Content.fontSize/2)
     m_WithTimeHint_ContentHeight = m_WithTimeHint_Limit*this.m_Text_Content.fontSize + (m_WithTimeHint_Limit-1)*(this.m_Text_Content.fontSize/2)
end

---Update
function CommonQuery_Type1_Controller.Update()

    ---時間條 Slider 剩餘時間文字 刷新
    if this.m_IsNeedUpdateCounterTime then
        this.m_CounterTime = this.m_CounterTime + HEMTimeMgr.m_DeltaTime
        if this.m_CounterType == 1 then --正數計時器 時間到呼叫確認CallBack
            CommonQuery_Type1_Controller.m_Slider_LitmitTime.value = this.m_CounterTime / this.m_MaxCounterTime
            this.m_Text_TimeHint.text = "剩餘 " .. string.format("%.1f",(this.m_MaxCounterTime - this.m_CounterTime)) .." 秒"
            if(CommonQuery_Type1_Controller.m_Slider_LitmitTime.value > 0.99) then
                this.m_IsNeedUpdateCounterTime = false
                local res, info  = pcall(CommonQuery_Type1_Controller.m_Type0_CallBack_Confirm)
                if not res then
                    D.LogError(info)
                end
            end
        elseif this.m_CounterType == 2 then --倒數計時器 時間到呼叫取消CallBack
            CommonQuery_Type1_Controller.m_Slider_LitmitTime.value = 1-(this.m_CounterTime / this.m_MaxCounterTime)
            this.m_Text_TimeHint.text = "剩餘 " .. string.format("%.1f",(this.m_MaxCounterTime - this.m_CounterTime)) .." 秒"
            if(CommonQuery_Type1_Controller.m_Slider_LitmitTime.value < 0.01) then
                this.m_IsNeedUpdateCounterTime = false
                local res, info  = pcall(CommonQuery_Type1_Controller.m_Type0_CallBack_Cancel)
                if not res then
                    D.LogError(info)
                end
            end
        end
    end

end

function CommonQuery_Type1_Controller.Open()
    CommonQuery_Type1_Controller.Settinginformation()
    return true
end

---設定中央詢問視窗 需要顯示的元件
function CommonQuery_Type1_Controller.Settinginformation()
    local _viewData = CommonQueryMgr.GetCurrentCommonQueryData()
    if not _viewData then
        D.LogError("中央詢問視窗沒資料!!")
        return
    end

    ---設定確認方法
    if _viewData.m_CallBack_Confirm ~= nil then
        CommonQuery_Type1_Controller.m_Type0_CallBack_Confirm = function()
            if(type(_viewData.m_CallBack_ConfirmArgs)=="table" and next(_viewData.m_CallBack_ConfirmArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Confirm,unpack(_viewData.m_CallBack_ConfirmArgs))
            else
                pcall(_viewData.m_CallBack_Confirm)
            end
            CommonQuery_Type1_Controller.DoConfirmBtn()
        end
    else
        CommonQuery_Type1_Controller.m_Type0_CallBack_Confirm = function()
            CommonQuery_Type1_Controller.DoConfirmBtn()
        end
    end

    ---設定取消方法
    if _viewData.m_CallBack_Cancel ~= nil then
        CommonQuery_Type1_Controller.m_Type0_CallBack_Cancel = function()
            if(type(_viewData.m_CallBack_CancelArgs)=="table" and next(_viewData.m_CallBack_CancelArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Cancel,unpack(_viewData.m_CallBack_CancelArgs))
            else
                pcall(_viewData.m_CallBack_Cancel)
            end
            CommonQuery_Type1_Controller.DoCancelBtn()
        end
    else
        CommonQuery_Type1_Controller.m_Type0_CallBack_Cancel = function()
            CommonQuery_Type1_Controller.DoCancelBtn()
        end
    end

    ---設定特殊方法
    if _viewData.m_CallBack_Special ~= nil then
        CommonQuery_Type1_Controller.m_Type0_CallBack_Special = function()
            if(type(_viewData.m_CallBack_SpecialArgs)=="table" and next(_viewData.m_CallBack_SpecialArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Special,unpack(_viewData.m_CallBack_SpecialArgs))
            else
                pcall(_viewData.m_CallBack_Special)
            end
            CommonQuery_Type1_Controller.DoSpecialBtn()
        end
    else
        CommonQuery_Type1_Controller.m_Type0_CallBack_Special = function()
            CommonQuery_Type1_Controller.DoSpecialBtn()
        end
    end

    local _Assign_Btn_Confirm
    local _Assign_Btn_Cancel
    local _Assign_Btn_Special
    local _AssignTMP

    ---設定方法要加到哪個按鍵的
    ---設定內文要設定到哪個TMP
    ---設定 Type123 或者Type4的母物件要開啟/關閉
    if _viewData.m_CommonqueryBoxType_SubType == ECommonQueryType1_SubType.TopPanel then
        _Assign_Btn_Confirm = this.m_Btn_Type4_Confirm
        _Assign_Btn_Cancel = this.m_Btn_Type4_Cancel
        _Assign_Btn_Special = this.m_Btn_Type4_Special

        _AssignTMP =  this.m_Text_Type4Content
        this.m_Obj_Type4_Parent.gameObject:SetActive(true)
        this.m_Obj_Type123_Parent.gameObject:SetActive(false)
    else
        _Assign_Btn_Confirm = this.m_Btn_Confirm
        _Assign_Btn_Cancel = this.m_Btn_Cancel
        _Assign_Btn_Special = this.m_Btn_Special

        _AssignTMP =  this.m_Text_Content

        this.m_Obj_Type4_Parent.gameObject:SetActive(false)
        this.m_Obj_Type123_Parent.gameObject:SetActive(true)
    end

    ---是否要要內文 填入其他資訊
    if table.IsNullOrEmpty(_viewData.m_ContentParma) then
        _AssignTMP.text = TextData.Get(_viewData.m_ContentID)
    else
        _AssignTMP.text = GString.Format(TextData.Get(_viewData.m_ContentID) , unpack(_viewData.m_ContentParma, 1, table.maxn(_viewData.m_ContentParma)))
    end

    ---設定標題
    if _viewData.m_TitleContentID ~=0 then --有標題
        if table.IsNullOrEmpty(_viewData.m_TitleParma)then
            this.m_Text_TitleContent.text = TextData.Get(_viewData.m_TitleContentID)
        else
            this.m_Text_TitleContent.text = GString.Format(TextData.Get(_viewData.m_TitleContentID) , unpack(_viewData.m_TitleParma, 1, table.maxn(_viewData.m_TitleParma)))
        end

        this.m_Text_TitleContent.gameObject:SetActive(true)
    else
        this.m_Text_TitleContent.gameObject:SetActive(false)
    end

    ---設定計時
    if _viewData.m_CountingType ~= 0 and  _viewData.m_CountingTime ~= 0 then --有計時
        this.m_CounterType = _viewData.m_CountingType
        this.m_TimeHintBox:SetActive(true)
        CommonQuery_Type1_Controller.m_Object_LitmitTime:SetActive(true)
        this.m_Text_TimeHint.gameObject:SetActive(true)
        this.m_MaxCounterTime = _viewData.m_CountingTime
        this.m_CounterTime = 0
        this.m_IsNeedUpdateCounterTime = true
    else
        this.m_TimeHintBox:SetActive(false)
        CommonQuery_Type1_Controller.m_Object_LitmitTime:SetActive(false)
        this.m_Text_TimeHint.gameObject:SetActive(false)
        this.m_IsNeedUpdateCounterTime = false
    end

    if _viewData.m_Str_BTNCancel ~= 0 then --取消按鈕
        _Assign_Btn_Cancel:SetText(TextData.Get(_viewData.m_Str_BTNCancel))
        _Assign_Btn_Cancel:ClearListener(EventTriggerType.PointerClick)
        _Assign_Btn_Cancel:AddListener(EventTriggerType.PointerClick, CommonQuery_Type1_Controller.m_Type0_CallBack_Cancel)
        _Assign_Btn_Cancel.ClickAudioIndex = _viewData.m_Sound_BTNCancel

        _Assign_Btn_Cancel.gameObject:SetActive(true)
    else
        _Assign_Btn_Cancel.gameObject:SetActive(false)
    end

    if _viewData.m_Str_BTNConfirm ~= 0 then --確認按鈕
        _Assign_Btn_Confirm:SetText(TextData.Get(_viewData.m_Str_BTNConfirm))
        _Assign_Btn_Confirm:ClearListener(EventTriggerType.PointerClick)
        _Assign_Btn_Confirm:AddListener(EventTriggerType.PointerClick, CommonQuery_Type1_Controller.m_Type0_CallBack_Confirm)
        _Assign_Btn_Confirm.ClickAudioIndex = _viewData.m_Sound_BTNConfirm

        _Assign_Btn_Confirm.gameObject:SetActive(true)
    else
        _Assign_Btn_Confirm.gameObject:SetActive(false)
    end

    if _viewData.m_Str_BTNSpecial ~= 0 then --特殊按鈕
        _Assign_Btn_Special:SetText(TextData.Get(_viewData.m_Str_BTNSpecial))
        _Assign_Btn_Special:ClearListener(EventTriggerType.PointerClick)
        _Assign_Btn_Special:AddListener(EventTriggerType.PointerClick, CommonQuery_Type1_Controller.m_Type0_CallBack_Special)
        _Assign_Btn_Special.ClickAudioIndex = _viewData.m_Sound_BTNSpecial

        _Assign_Btn_Special.gameObject:SetActive(true)
    else
        _Assign_Btn_Special.gameObject:SetActive(false)
    end


    ---推遲0.05秒等相關UI參數才會刷新 才能正確設定位置
    LeanTween.delayedCall(0.05, System.Action(
        function()

            local _RectTransform =this.m_GObj_BackGroundParent:GetComponent("RectTransform")
            local _RectTransformSize = _RectTransform.sizeDelta

            ---特定高度內不調整高度
            ---調整版面高度 m_InitialPanelHeight
            ---目前的文字 所需要的高度
            local _CurrentHeight_Content = this.m_Text_Content.gameObject:GetComponent("RectTransform").sizeDelta.y
            ---臨界點的高度
            local _Threshold_Height = this.m_IsNeedUpdateCounterTime and m_WithTimeHint_ContentHeight or m_NoTimeHint_ContentHeight
            ---是否使用較高的高度(視窗變大)
            local _UseLargerHeight = _CurrentHeight_Content > _Threshold_Height

            ---內文區高度
            local _RectTransform_Word = this.m_WordBox:GetComponent("RectTransform")
            local _NewSize = _RectTransform_Word.sizeDelta

            ---m_ContentArea ( 內文區 + 倒數提示區) 高度
            local _RectTransform_ContentArea = this.m_ContentArea:GetComponent("RectTransform")
            local _RectTransformSize_ContentArea = _RectTransform_ContentArea.sizeDelta

            if _UseLargerHeight then
                _RectTransformSize.y = m_InitialPanelHeight + (_CurrentHeight_Content - _Threshold_Height)

                _RectTransform.sizeDelta = _RectTransformSize

                if this.m_TimeHintBox.activeSelf == true then
                    _NewSize.y = m_WithTimeHint_Height_WordArea + (_CurrentHeight_Content - _Threshold_Height)
                elseif this.m_TimeHintBox.activeSelf == false then
                    _NewSize.y = m_Initial_ContentArea_Height + (_CurrentHeight_Content - _Threshold_Height)
                end
                _RectTransform_Word.sizeDelta = _NewSize

                _RectTransformSize_ContentArea.y = m_Initial_ContentArea_Height +  (_CurrentHeight_Content - _Threshold_Height)
                _RectTransform_ContentArea.sizeDelta = _RectTransformSize_ContentArea

                ---調整座標位置使之置中
                this.m_GObj_BackGroundParent.transform.localPosition = Vector3(
                    this.m_GObj_BackGroundParent.transform.localPosition.x,
                    (m_InitialPanel_Pos_Y + (_CurrentHeight_Content - _Threshold_Height)/2 ),
                    this.m_GObj_BackGroundParent.transform.localPosition.z)
            else
                ---有可能 遇到先前開過較多行 因此必須恢復成 不使用超過門檻行數 的高度
                _RectTransformSize.y = m_InitialPanelHeight
                _RectTransform.sizeDelta = _RectTransformSize

                --- 使用原本的視窗大小時  只需要微調內文區的大小
                if this.m_TimeHintBox.activeSelf == true then
                    _NewSize.y = m_WithTimeHint_Height_WordArea
                elseif this.m_TimeHintBox.activeSelf == false then
                    _NewSize.y = m_Initial_ContentArea_Height
                end
                _RectTransform_Word.sizeDelta = _NewSize

                _RectTransformSize_ContentArea.y = m_Initial_ContentArea_Height
                _RectTransform_ContentArea.sizeDelta = _RectTransformSize_ContentArea

                this.m_GObj_BackGroundParent.transform.localPosition = Vector3(
                    this.m_GObj_BackGroundParent.transform.localPosition.x,
                    m_InitialPanel_Pos_Y ,
                    this.m_GObj_BackGroundParent.transform.localPosition.z)
            end

        end)
    )
end


---預設 點擊確定按鈕
function CommonQuery_Type1_Controller.DoConfirmBtn()
    D.Log("CommonQuery_Type1_Controller Confirm Btn Click",Color.White)
    if CommonQueryMgr~=nil then
        CommonQueryMgr.ShowNextCommonQueryMgrData()
    end
end

---預設 點擊取消按鈕
function CommonQuery_Type1_Controller.DoCancelBtn()
    D.Log("CommonQuery_Type1_Controller Cancel Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊特殊按鈕
function CommonQuery_Type1_Controller.DoSpecialBtn()
    D.Log("CommonQuery_Type1_Controller Special Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end
