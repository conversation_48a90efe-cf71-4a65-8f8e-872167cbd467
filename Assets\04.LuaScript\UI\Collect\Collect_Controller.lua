---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---圖鑑系統 UI顯示 操作腳本  
---@class lua
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.4.16
Collect_Controller = {}
local this = Collect_Controller

---現在選擇的分頁
local m_NowType = 1
---當前分頁的成就資料
local m_CurrentData = {}
---可以領取的獎勵資料
local m_CanReceiveData = {}
---現在要領取的蒐集獎勵Idx
local m_NowReceiveIdx = 0
---是否是全部領取
local m_isAllReceive = false
---可以領取的獎勵數量
local m_CanReceiveDataCount = 0
---已經領取的獎勵數量
local m_ReceivedCount = 0
---分頁類型按鈕Table
local m_Table_Type = {}

---每個單一圖鑑最多需要6個icon
local m_MaxIconCountInOneCollect = 6

---要使用petIcon的分頁
local m_PetPageIndex = 4
---根據要看哪個分頁 決定要顯示的icon類型
local m_UsePetIconType = false


local ACHIEVEMENT_ICONID = 101019

---蒐集類別
ECollectType = 
{
    ---武器 1
    Weapon =1,
    ---裝備 2
    Equipment = 2,
    ---飾品 3
    Decoracy = 3,
    ---寵物 4
    Pet = 4,
    ---特殊 5
    SpecialItem = 5,
    ---活動 6
    Actiity = 6,
}

---通用詢問視窗邊號
local m_SendItemCommonQueryID = 213

---使用的字串ID 們
---物品 字串ID
local m_Item_StringID = 431746
---放入圖鑑 字串ID
local m_PutInCollect_StringID = 20605050
--- 取消 字串ID
local m_Cancel_StringID = 478
--- 確認 字串ID
local m_Confirm_StringID = 477
---跟隨中 字串ID
local m_Following_StringID = 20323103
---最愛設定中 字串ID
local m_IsFavorite_StringID = 20102021
---派遣中 字串ID
local m_InDeliver_StringID = 20323104
---派遣完成 字串ID 
local m_FiniDeliver_StringID = 20323411
---背包空間不足
local m_LackBagGrid_StringID = 9093
---收藏失敗 寵物已經被設定為最愛字串ID
local m_Fail_DueToFavorite_StringID = 20605057
---收藏獎勵 字串ID
local m_CollectReward_StringID = 20601505
---探索積分 字串ID 
local m_Scroe_StringID = 20113305

---相同自律者 提示文字
local m_SamePetHint_StringID = 20605052
---相同道具 提示文字
local m_SameItemHint_StringID = 20605053

---region 蒐集類別的按鈕

--- 取得圖鑑類別有幾種
local function GetCount_Type()
    return CollectTitle.GetDataCount()
end

---圖鑑類別 按鍵初始化
local function AfterReuseItemInit_Type(iItem, iIdx)
    if iItem ~= nil then
        iItem.m_Btn_Type = Button.New(iItem.m_GObj.transform:Find("Button_Type"))
        iItem.m_Btn_Type:AddListener(EventTriggerType.PointerClick, function()
            Collect_Controller.OnClick_GroupType(iIdx)
        end)

        iItem.m_TextInfo = iItem.m_Btn_Type.transform:Find("Text_Info"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        table.insert(m_Table_Type, iItem)
    end
end

---圖鑑類別 按鍵更新訊息
local function AfterReuseItemIndexUpdate_Type(iItem, iIdx)
    if iItem ~= nil then
        local _CollectData = CollectTitle.GetCollectTitleByIdx(iIdx)
        local _Data = CollectData.GetDataByType(iIdx)
        if _CollectData ~= nil then
            iItem.m_TextInfo.text = TextData.Get(_CollectData.m_PageStringID)
        end
        iItem.m_GObj:SetActive(_CollectData ~= nil and _Data ~= nil)
    end
end

---初始化 圖鑑類別按鍵們
local function InitGroupBtnType()
    local _ScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Collect_ButtonType")
    local _ReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Unit_CollectButtonType").gameObject
    this.m_ScrollView_Type = ScrollView.Init(_ScrollView, true, _ReuseItme,
        GetCount_Type, AfterReuseItemInit_Type, AfterReuseItemIndexUpdate_Type,
        true, true)
    this.m_Content_Type = this.m_ViewRef.m_Dic_Trans:Get("&Content_Collect_ButtonType")
    this.m_Content_Type.gameObject.transform.localPosition = Vector3.zero
end

--end region 蒐集類別的按鈕


---region 蒐集細節UI顯示

---取得目前類別 有幾筆資料需要顯示
local function GetCount_Collect()
    Collect_Controller.SetCurrentData()
    return table.Count(m_CurrentData)
end

---物品icon的方法
local _OnClickItemIconBtnFunc = function(iTable)
    local _data = m_CurrentData[iTable[2]]

    if _data ~= nil then
        ---取得對應CollectData中的流水號 以及蒐集編號
        local _ItemID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
        local _PreFlag = _data.m_CollectDataItemSet[iTable[1]].m_CollectedFlag
        

        if PlayerData.IsHaveStaticFlag(_PreFlag) then
           --- 測試再開啟
           --- D.Log("玩家已提交此項物品")
        else
            --if  math.floor(_data.m_CollectionNumber * 0.001) <= ECollectType.Decoracy or math.floor(_data.m_CollectionNumber * 0.001) >= ECollectType.SpecialItem then

                local _ItemAmount = BagMgr.GetItemInBagAmount( _ItemID, {EItemStatus.Normal})

                ---檢查玩家是否有這種道具 
                if _ItemAmount > 0 then

                    ---取得ItemSaveData SID 資料
                    local _ItemSIDTable = BagMgr.GetAllSameItemDataByItemID(_ItemID)
                    ---生成 index 對應到 SID 的table
                    local _SelectControllIndexToSID ={}
                    for key, value in pairs(_ItemSIDTable) do
                        table.insert(_SelectControllIndexToSID,value)
                    end
                    ---生成詢問視窗類別2 資料
                    local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
                    _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon,{_ItemID},{1},{true},false)

                    ---如果玩家只有一個這種物品 或者 是特殊物品 直接跳圖鑑蒐藏詢問
                    if math.floor(_data.m_CollectionNumber * 0.001) >= ECollectType.SpecialItem or _ItemAmount == 1 then
                        CommonQueryMgr.AddNewInform(
                            m_SendItemCommonQueryID,
                    {},
                    {TextData.Get(m_Item_StringID)},function() 

                        local _ProtocolData = {}
                        _ProtocolData.m_Kind = 1
                        _ProtocolData.m_Idx = _data.m_Idx
                        _ProtocolData.m_CollectionNumber = _data.m_CollectionNumber
                        _ProtocolData.m_CollectIndex = iTable[1]
                        _ProtocolData.m_SID = _SelectControllIndexToSID[1]
                        _ProtocolData.m_ItemID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
                        SendProtocol_021._018(_ProtocolData)
                        
                        end,nil,
                    nil,nil,nil,nil,_Type2Data)
                    
                    else
                        --- 如果玩家有超過兩個這種物品 要開選擇清單
                        --- 道具類 沒有強化 會出現一堆相同的icon 以及描述
                        local _TmpList = {}
                        local _SelectListCount = 0

                        for key, value in pairs(_ItemSIDTable) do
                            -- 計算Index
                            _SelectListCount = _SelectListCount+1
                
                            -- 初始化
                            local _TmpData = {}
                            local _ItemCount = 1
                            local _ItemName = ItemData.GetItemName(_ItemID)
                            _ItemCount = 1
                
                            -- 填寫必要資料
                            _TmpData = SelectList_Model.NewListData(SelectList_Model.TYPE.Item, _ItemID, _ItemCount, _ItemName, _SelectListCount, 1)
                            -- 塞進table
                            table.insert(_TmpList, _TmpData)
                        end

                         ---關閉SelectList並重置資料
                        local function _CloseSelectList()
                            UIMgr.Close(SelectList_Controller)
                            SelectList_Model.CloseSelectList()
                        end

                        ---開啟前檢查SelectList是否存在, 有則需要重開
                        if(SelectList_Controller and UIMgr.IsVisible(SelectList_Controller)) then
                            _CloseSelectList()
                        end

                        local _SelectListTitle =  TextData.Get(m_PutInCollect_StringID)

                        local _SelectListContent = TextData.Get(m_SameItemHint_StringID)
                        ---開啟選擇清單
                        SelectList_Model.OpenSelectList(_SelectListTitle,_SelectListContent,_TmpList,nil,nil,
                        _CloseSelectList(),
            function ()
                                CommonQueryMgr.AddNewInform(m_SendItemCommonQueryID,{},{TextData.Get(m_Item_StringID)},
                                function() 
                                    local _ProtocolData = {}
                                    _ProtocolData.m_Kind = 1
                                    _ProtocolData.m_Idx = _data.m_Idx
                                    _ProtocolData.m_CollectionNumber = _data.m_CollectionNumber
                                    _ProtocolData.m_CollectIndex = iTable[1]
                                    _ProtocolData.m_SID = _SelectControllIndexToSID[SelectList_Model.m_NowLastSelect]
                                    _ProtocolData.m_ItemID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
                                    SendProtocol_021._018(_ProtocolData) end,nil,nil,nil,nil,nil,_Type2Data)
                                end,
                                {m_Cancel_StringID, m_Confirm_StringID},1,SelectList_Model.SHOWAREA.Left)
                    end
                end
            --end
        end
    end
end

---寵物系列的icon 點擊方法
local _OnClickPetIconBtnFunc = function(iTable)
        local _data = m_CurrentData[iTable[2]]
        if _data ~= nil then
            ---取得對應CollectData中的流水號 以及蒐集編號
            local _PetID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
            local _PreFlag = _data.m_CollectDataItemSet[iTable[1]].m_CollectedFlag
            
            ---找到玩家寵物 並且抓取出相同編號的寵物
            local _PetTable = PetMgr.GetPetListByPetID(_PetID)
            local _SelectControllIndexToSID ={}
            for key, value in pairs(_PetTable) do
                table.insert(_SelectControllIndexToSID,value.m_PetSlot)
            end


            if PlayerData.IsHaveStaticFlag(_PreFlag) then
                --測試用 在打開
                --D.Log("玩家已提交此寵物收藏 寵物ID = " .. _PetID)
            else

                ---根據投入的寵物資料 以及要使用對應字串的位置 生成不同字串
                local _GetPetStatusString = function(iPetData , iShowInCenterMsg)
                    local _PetStatus = iPetData.m_PetState
                    local _PetIsSummoned = PetMgr.GetSummonedPetPlot() == iPetData.m_PetSlot
                    local _IsEnoughBagGrid = true
                    local _EquipedItemID = iPetData.m_PetEquipment
                    local _StatusString = ""
                    local _PetEquipmentGridNeed = 0
                    for key,value in pairs(_EquipedItemID) do
                        if value ~= 0 then
                            _PetEquipmentGridNeed = _PetEquipmentGridNeed +1
                            end
                    end
                    _IsEnoughBagGrid = BagMgr.GetBagNowGrid(EBagType.Equipment) >= _PetEquipmentGridNeed

                    ---要在中央訊息區使用
                    if iShowInCenterMsg == false then
                        if _PetIsSummoned then
                            _StatusString = TextData.Get(m_Following_StringID)
                        elseif iPetData.m_IsPetLoved  then
                            _StatusString = TextData.Get(m_IsFavorite_StringID)
                        elseif iPetData.m_IsPetLoved == false and _PetStatus == 1  then
                            _StatusString = TextData.Get(m_InDeliver_StringID)
                        elseif iPetData.m_IsPetLoved == false and _PetStatus == 2  then
                            _StatusString = TextData.Get(m_FiniDeliver_StringID)
                        elseif not _IsEnoughBagGrid   then
                            _StatusString = TextData.Get(m_LackBagGrid_StringID)
                        end
                    ---要在選擇清單介面使用
                    else
                        if _PetIsSummoned then
                            _StatusString = TextData.Get(m_Following_StringID)
                        elseif iPetData.m_IsPetLoved  then
                            _StatusString = TextData.Get(m_Fail_DueToFavorite_StringID)
                        elseif iPetData.m_IsPetLoved == false and _PetStatus == 1  then
                            _StatusString = TextData.Get(m_InDeliver_StringID)
                        elseif iPetData.m_IsPetLoved == false and _PetStatus == 2  then
                            _StatusString = TextData.Get(m_FiniDeliver_StringID)
                        elseif not _IsEnoughBagGrid   then
                            _StatusString = TextData.Get(m_LackBagGrid_StringID)
                        end
                    end
                    return _StatusString
                end

                
                if table.Count(_PetTable) == 0 then
                    -- 測試用在打開
                    --D.Log("玩家未提交 且 也沒有此類寵物 可提交")
                else
                    local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
                    _Type2Data:BuildIconBoxTable(EIconBoxDataType.PetIcon,{_PetID},{""},{true},false)

                    ---如果只有一隻寵物直接送
                    if table.Count(_PetTable) == 1 then

                        ---判斷寵物是否可以被提交收藏
                        local _PetStatusStr = _GetPetStatusString(_PetTable[1],true)
                        if _PetStatusStr ~= "" then
                            MessageMgr.AddCenterMsg(false, _PetStatusStr)
                            return
                        end
                        
                        CommonQueryMgr.AddNewInform(m_SendItemCommonQueryID,{},{TextData.Get(20323011)},
                        function() 
                                            local _ProtocolData = {}
                                            _ProtocolData.m_Kind = 1
                                            _ProtocolData.m_Idx = _data.m_Idx
                                            _ProtocolData.m_CollectionNumber = _data.m_CollectionNumber
                                            _ProtocolData.m_CollectIndex = iTable[1]
                                            _ProtocolData.m_SID = _SelectControllIndexToSID[1]
                                            _ProtocolData.m_ItemID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
                                            SendProtocol_021._018(_ProtocolData)
                                        end,nil,nil,nil,nil,nil,_Type2Data)
                    else
                        --- 如果不只一隻寵物開啟選擇清單
                        local _TmpList = {}
                        local _SelectListCount = 0

                        ---生成選擇清單
                        for key, value in pairs(_PetTable) do
                            -- 計算Index
                            _SelectListCount = _SelectListCount+1
                
                            -- 初始化
                            local _TmpData = {}
                            local _PetCount = 1
                            local _PetName = PetData.GetPetName(value.m_PetID, true)

                            -- 填寫必要資料
                            _TmpData = SelectList_Model.NewListData(SelectList_Model.TYPE.Pet, value, _PetCount, _PetName, _SelectListCount, 1)
                            
                            ---寵物才需要添加的內容
                            ---寵物星等
                            _TmpData.m_PetStar = value.m_StarLv
                            ---是否為我的最愛
                            _TmpData.m_PetIsLoved = value.m_IsPetLoved
                            ---寵物狀態文字
                            _TmpData.m_StatusString = _GetPetStatusString(value,false)
                            ---寵物裝備
                            _TmpData.m_PetEquipment = value.m_PetEquipment
                            -- 塞進table
                            table.insert(_TmpList, _TmpData)
                        end

                         ---關閉SelectList並重置資料
                         local function _CloseSelectList()
                            UIMgr.Close(SelectList_Controller)
                            SelectList_Model.CloseSelectList()
                        end

                        ---開啟前檢查SelectList是否存在, 有則需要重開
                        if(SelectList_Controller and UIMgr.IsVisible(SelectList_Controller)) then
                            _CloseSelectList()
                        end

                        local _SelectListTitle =  TextData.Get(m_PutInCollect_StringID)

                        
                        local _SelectListContent = TextData.Get(m_SamePetHint_StringID)
                        ---開啟選擇清單
                        SelectList_Model.OpenSelectList(_SelectListTitle,_SelectListContent,_TmpList,nil,nil,
                        _CloseSelectList(),
            function ()
            
                    CommonQueryMgr.AddNewInform(m_SendItemCommonQueryID,{},{TextData.Get(20323011)},
                        function() 
                            local _ProtocolData = {}
                            _ProtocolData.m_Kind = 1
                            _ProtocolData.m_Idx = _data.m_Idx
                            _ProtocolData.m_CollectionNumber = _data.m_CollectionNumber
                            _ProtocolData.m_CollectIndex = iTable[1]
                            _ProtocolData.m_SID = _SelectControllIndexToSID[SelectList_Model.m_NowLastSelect]
                            _ProtocolData.m_ItemID = _data.m_CollectDataItemSet[iTable[1]].m_ItemID
                            SendProtocol_021._018(_ProtocolData) end,nil,nil,nil,nil,nil,_Type2Data)
                        end,{m_Cancel_StringID, m_Confirm_StringID},1,SelectList_Model.SHOWAREA.Left)
                    end
                end
            end
        end
end

local _ClickCommonIconFunc = function(iTable)
    ---目前不會混搭(寵物分頁 提交區域Icon 只會出現寵物) 所以用 m_UsePetIconType 判斷點擊方法

    if m_UsePetIconType then
        _OnClickPetIconBtnFunc(iTable)
    else
        _OnClickItemIconBtnFunc(iTable)
    end

end

---各個圖鑑收藏細節 Scrollview 使用物件 初始化
local function AfterReuseItemInit_Collect(iItem, iIdx)
    if iItem ~= nil then
        iItem.m_Name = iItem.m_GObj.transform:Find("Text_Name"):GetComponent(typeof( TMPro.TextMeshProUGUI ))

        ---獎勵 道具Icon
        iItem.Image_Icon = {}
        iItem.AwardIcon = {}
        for i = 1, 2 do
            local _OnClickParam = {iIdx, i}
            iItem.Image_Icon[i] = iItem.m_GObj.transform:Find("Trans_AwardIcon/AwardIcon_" .. i)
            iItem.AwardIcon[i] = IconMgr.NewCommonIcon(0, iItem.Image_Icon[i], 69.12)
            iItem.AwardIcon[i].m_OnLongClickParam = _OnClickParam
            iItem.AwardIcon[i]:SetLongPressCallback(Collect_Controller.LongPressOpenHint_Reward)
            iItem.AwardIcon[i]:SetClickTwice(false)
        end
        ---積分
        iItem.m_Score = iItem.m_GObj.transform:Find("Text_Score"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        iItem.m_Group_State = iItem.m_GObj.transform:Find("Group_State")

        iItem.m_Btn_Receive = Button.New(iItem.m_GObj.transform:Find("Button_Receive"))
        iItem.m_Btn_Receive:AddListener(EventTriggerType.PointerClick, function()
            Collect_Controller.OnClick_Receive(iIdx)
        end)

        ---ItemIcon們用一個父物件統一控制開關
        iItem.m_Obj_ItemIcons = iItem.m_GObj.transform:Find("ItemIconParent").gameObject

       ---需要提交的道具類 icon 
       iItem.m_RequireItem = {}
       for i = 1, m_MaxIconCountInOneCollect do
            iItem.m_RequireItem[i] = {}
            local _path = "ItemIconParent/IconParent"..i
            iItem.m_RequireItem[i].m_IconParent = iItem.m_GObj.transform:Find(_path)
            iItem.m_RequireItem[i].m_Img_CanCollect = iItem.m_GObj.transform:Find(_path .. "/Img_CanCollect").gameObject
            iItem.m_RequireItem[i].m_Img_AlreadyCollect = iItem.m_GObj.transform:Find(_path .. "/Img_AlreadyCollect").gameObject
           
            local _OnClickParam = {iIdx, i}
            iItem.m_RequireItem[i].m_Icon =  IconMgr.NewCommonIcon(0, iItem.m_RequireItem[i].m_IconParent, 69.12,_ClickCommonIconFunc,{i,iIdx})
            iItem.m_RequireItem[i].m_Icon:SetLongPressCallback(Collect_Controller.LongPressOpenHint_SendObject)
            iItem.m_RequireItem[i].m_Icon:SetClickTwice(false)
            iItem.m_RequireItem[i].m_Icon.gameObject.transform:SetSiblingIndex(0)
            iItem.m_RequireItem[i].m_Icon.m_OnLongClickParam = _OnClickParam
       end

        --換顏色
        iItem.m_Image_BG = iItem.m_GObj.transform:Find("Image_BG"):GetComponent(typeof( UIRenderChangeColor ))
        iItem.m_Image_Halo = iItem.m_Image_BG.transform:Find("Image_Common_Halo"):GetComponent(typeof( UIRenderChangeColor ))
        iItem.m_Image_Line = iItem.m_Image_BG.transform:Find("Image_Common_Line"):GetComponent(typeof( UIImageChange ))
        iItem.m_Image_Score = iItem.m_GObj.transform:Find("Image_Score"):GetComponent(typeof( UIRenderChangeColor ))
    end
end

---各個圖鑑收藏細節 Scrollview 使用物件 資料刷新
local function AfterReuseItemIndexUpdate_Collect(iItem, iIdx)
    if iItem ~= nil then
        local _data = m_CurrentData[iIdx]
        if _data ~= nil then
            ---圖鑑蒐藏對應名稱
            iItem.m_Name.text = TextData.Get(_data.m_CollectionConditionStringID)
            
            ---根據是否使用寵物icon 來刷新對應icon資料
            if m_UsePetIconType then

                for i = 1, m_MaxIconCountInOneCollect do
                    iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(false)
                    iItem.m_RequireItem[i].m_Img_AlreadyCollect:SetActive(false)

                     ---ItemID不為0刷新icon 為0則不顯示
                     if _data.m_CollectDataItemSet[i].m_ItemID ~= 0 then
                        iItem.m_RequireItem[i].m_IconParent.gameObject:SetActive(true)
                        iItem.m_RequireItem[i].m_Icon:RefreshIcon(_data.m_CollectDataItemSet[i].m_ItemID, EIconType.Pet)
                    else
                        iItem.m_RequireItem[i].m_IconParent.gameObject:SetActive(false)
                    end

                    if PlayerData.IsHaveStaticFlag(_data.m_CollectDataItemSet[i].m_CollectedFlag) then
                        iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(false)
                        iItem.m_RequireItem[i].m_Img_AlreadyCollect:SetActive(true)
                        ---解鎖的不要押黑
                        iItem.m_RequireItem[i].m_Icon:ShowMask(false)
                        --iItem.m_RequireItem[i].m_Icon:SetObjectActive(iItem.m_RequireItem[i].m_Icon.m_Image_Mask,false)
                    else
                        --iItem.m_RequirePet[i].m_Img_AlreadyCollect:SetActive(false)
                        ---檢查玩家是否有對應寵物   
                        local _HasIdlePet =  table.Count(PetMgr.CheckCollectAvaible(_data.m_CollectDataItemSet[i].m_ItemID)) > 0
                        iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(_HasIdlePet)
                        ---未解鎖的都要押黑
                        iItem.m_RequireItem[i].m_Icon:ShowMask(true)
                        --iItem.m_RequireItem[i].m_Icon:SetObjectActive(iItem.m_RequireItem[i].m_Icon.m_Image_Mask,true)
                    end
                end

            else
                for i = 1, m_MaxIconCountInOneCollect do
                    iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(false)
                    iItem.m_RequireItem[i].m_Img_AlreadyCollect:SetActive(false)

                     ---ItemID不為0刷新icon 為0則不顯示
                     if _data.m_CollectDataItemSet[i].m_ItemID ~= 0 then
                        iItem.m_RequireItem[i].m_IconParent.gameObject:SetActive(true)
                        iItem.m_RequireItem[i].m_Icon:RefreshIcon(_data.m_CollectDataItemSet[i].m_ItemID, EIconType.Item)
                    else
                        iItem.m_RequireItem[i].m_IconParent.gameObject:SetActive(false)
                    end

                    if PlayerData.IsHaveStaticFlag(_data.m_CollectDataItemSet[i].m_CollectedFlag) then
                        iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(false)
                        iItem.m_RequireItem[i].m_Img_AlreadyCollect:SetActive(true)
                        ---解鎖的不要押黑
                        iItem.m_RequireItem[i].m_Icon:ShowMask(false)
                        --iItem.m_RequireItem[i].m_Icon:SetObjectActive(iItem.m_RequireItem[i].m_Icon.m_Image_Mask,false)
                    else
                        iItem.m_RequireItem[i].m_Img_AlreadyCollect:SetActive(false)
                        ---檢查背包是否有對應道具 
                        local _HasItem = (BagMgr.GetItemInBagAmount(_data.m_CollectDataItemSet[i].m_ItemID) > 0 )
                        iItem.m_RequireItem[i].m_Img_CanCollect:SetActive(_HasItem)
                        ---未解鎖的都要押黑
                        iItem.m_RequireItem[i].m_Icon:ShowMask(true)
                        --iItem.m_RequireItem[i].m_Icon:SetObjectActive(iItem.m_RequireItem[i].m_Icon.m_Image_Mask,true)
                    end
                end
            end


            ---刷新獎勵
            for i = 1, table.Count(_data.m_CollectDataRewardSet) do
                --種類1 物品
                if _data.m_CollectDataRewardSet[i].m_RewardType == 1 then
                    iItem.AwardIcon[i].gameObject:SetActive(_data.m_CollectDataRewardSet[i].m_RewardID ~= 0) 
                    if _data.m_CollectDataRewardSet[i].m_RewardID ~= 0 then
                        iItem.AwardIcon[i]:RefreshIcon(_data.m_CollectDataRewardSet[i].m_RewardID, EIconType.Item)
                        iItem.AwardIcon[i]:SetCount(_data.m_CollectDataRewardSet[i].m_RewardNum)
                    end
                --種類2 稱號
                elseif _data.m_CollectDataRewardSet[i].m_RewardType ==  2 then
                    local _TitleData = TitleData.Get(_data.m_CollectDataRewardSet[i].m_RewardID)
                    local _IconData = CommonIcon:SetIconData(_data.m_CollectDataRewardSet[i].m_RewardID, ACHIEVEMENT_ICONID, TextData.Get(_TitleData.m_TextID_Name))
                    iItem.AwardIcon[i]:RefreshIcon(_IconData, EIconType.Common)
                    m_CurrentData[iIdx].m_CommonIconData = iItem.AwardIcon[i]
                    iItem.AwardIcon[i].gameObject:SetActive(true)
                else
                    iItem.AwardIcon[i]:RefreshIcon(0)
                    iItem.AwardIcon[i].gameObject:SetActive(false)
                end
            end
            ---積分
            iItem.m_Score.text = "+" .. _data.m_ExplorationPoint

            ---判斷已完成 / 可領獎 / 未完成 狀態
            local _CanReceive = Collect_Controller.CheckIsAchieved(_data)
            local _Received = PlayerData.IsHaveStaticFlag(_data.m_ReceiveRewardFlag)

            if _CanReceive and not _Received then
                table.insert(m_CanReceiveData, _data)
            end

            ---根據狀態 開關顯示圖片/按鍵 以及底板配置
            iItem.m_Btn_Receive.gameObject:SetActive(_CanReceive and not _Received)
            iItem.m_Group_State.gameObject:SetActive(_Received)

            Collect_Controller.SetCollectState(iItem,_CanReceive, _Received)
        end
        iItem.m_GObj:SetActive(_data ~= nil)
    end
end

local function InitCollect()
    m_CanReceiveData = {}
    local _ScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_CollectDetail")
    local _ReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Unit_CollectDetail").gameObject
    this.m_ScrollView_Collect = ScrollView.Init(_ScrollView, true, _ReuseItme,
        GetCount_Collect, AfterReuseItemInit_Collect, AfterReuseItemIndexUpdate_Collect,
        true, true)
    this.m_Btn_AllReceive = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_AllReceive_Collect"))
    this.m_Btn_AllReceive:AddListener(EventTriggerType.PointerClick, function()
        Collect_Controller.OnClick_AllReceive()
    end)
end


---end region 蒐集細節UI顯示

---初始化
function Collect_Controller.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    InitGroupBtnType()
    InitCollect()
end

---開啟頁面 
---@param iType number 要開啟哪一個CollectTitle類別
function Collect_Controller.Open(iType)
    m_isAllReceive = false
    m_ReceivedCount = 0
    m_NowType = iType or 1
    Collect_Controller.SetTypeBtnGroupIndex()
    Collect_Controller.UpdateScrollView()
end

---根據目前type 設定類別按鍵們 各自使用的groupindex
function Collect_Controller.SetTypeBtnGroupIndex()
    for k, v in pairs(m_Table_Type) do
        v.m_Btn_Type:ChangeStateTransitionGroup(0)
    end
    m_Table_Type[m_NowType].m_Btn_Type:ChangeStateTransitionGroup(1)
end

---設定目前類別的圖鑑資料
function Collect_Controller.SetCurrentData()
    local _Data = CollectData.GetDataByType(m_NowType)
    m_CurrentData = Collect_Controller.SortCollectData(_Data)
end

function Collect_Controller.UpdateScrollView() 
    m_UsePetIconType = (m_NowType == m_PetPageIndex)
    m_CanReceiveData = {}
    Collect_Controller.SetCurrentData()
    ScrollView.Update(this.m_ScrollView_Collect)
    if table.Count(m_CanReceiveData) > 0 then
        this.m_Btn_AllReceive:SetEnable()
    else
        this.m_Btn_AllReceive:SetDisable(false)
    end
end

---依分類編號和是否領獎永標篩選要顯示的成就之後再依狀態排序
---@param iData table 所有可能需要顯示的資料們
function Collect_Controller.SortCollectData(iData)

    --- 將沒有前置永標的資料過濾掉
    local _CurrentData = {} 
    for key, value in pairs(iData) do
        if Collect_Controller.GetAllowThisCollect(value) then
           table.insert(_CurrentData, value)
        end
    end

    local _CanReceiveData = {}
    local _NotCompletedData = {}
    local _CompletedData = {}
    local _SortData = {}
    for k, v in pairs(_CurrentData) do
        local _CanReceive = Collect_Controller.CheckIsAchieved(v)
        local _Received = PlayerData.IsHaveStaticFlag(v.m_ReceiveRewardFlag)
        
        --可以領獎
        if _CanReceive and not _Received then
            table.insert(_CanReceiveData, v)
        --還沒完成
        elseif not _CanReceive and not _Received then
            table.insert(_NotCompletedData, v)
        --已完成
        elseif _Received then
            table.insert(_CompletedData, v)
        end
    end

    for k, v in pairs(_CanReceiveData) do
        table.insert(_SortData, v)
    end
    for k, v in pairs(_NotCompletedData) do
        table.insert(_SortData, v)
    end
    for k, v in pairs(_CompletedData) do
        table.insert(_SortData, v)
    end
    return _SortData
end

--- 根據是否有前置永標來決定是否可以顯示這個圖鑑
---@param iData CollectData
---@return boolean 是否有對應前置永標
function Collect_Controller.GetAllowThisCollect(iData)
    local _IsHavePreFlag = false
    local _PreFlag = iData.m_PreFlag
    if _PreFlag ~= 0 then
        _IsHavePreFlag = PlayerData.IsHaveStaticFlag(_PreFlag)
    else
        _IsHavePreFlag = true
    end

    return _IsHavePreFlag
end

---判斷數值是否有達到完成標準
---@param iData CollectData 圖鑑的串表資料
---@return bool 是否已經成單一圖鑑的所有蒐藏
function Collect_Controller.CheckIsAchieved(iData)
    
    ---判斷玩家是否有這個圖鑑資料的蒐集狀態永標
    for key, value in pairs(iData.m_CollectDataItemSet) do
        if PlayerData.IsHaveStaticFlag(value.m_CollectedFlag) == false and value.m_CollectedFlag ~= 0 then
            return false
        end
    end

    return true
end

---設定圖鑑的背景UI
---@param iItem 圖鑑細節的UI元件table
---@param iCanReiceive boolean 是否可以領獎
---@param iReceived boolean 是否已經領獎
function Collect_Controller.SetCollectState(iItem,iCanReiceive, iReceived)
    if iCanReiceive == false then
        iItem.m_Image_BG:Trigger(ESelectionState.Normal)
        iItem.m_Image_Halo:Trigger(ESelectionState.Normal)
        iItem.m_Image_Line:Trigger(ESelectionState.Normal)
        iItem.m_Image_Score:Trigger(ESelectionState.Normal)
    else
        if iReceived then
            iItem.m_Image_BG:Trigger(ESelectionState.Selected)
            iItem.m_Image_Halo:Trigger(ESelectionState.Selected)
            iItem.m_Image_Line:Trigger(ESelectionState.Selected)
            iItem.m_Image_Score:Trigger(ESelectionState.Selected)
        else
            iItem.m_Image_BG:Trigger(ESelectionState.Normal)
            iItem.m_Image_Halo:Trigger(ESelectionState.Normal)
            iItem.m_Image_Line:Trigger(ESelectionState.Normal)
            iItem.m_Image_Score:Trigger(ESelectionState.Normal)
        end
    end
end

---點擊圖鑑種類頁籤
---@param iIdx number 點擊哪一個index的類別 
function Collect_Controller.OnClick_GroupType(iIdx)
    m_NowType = iIdx
    Collect_Controller.SetTypeBtnGroupIndex()
    Collect_Controller.UpdateScrollView()
end

---點擊領取單筆獎勵
---@param iIdx number 點擊目前data的哪一個index的資料
function Collect_Controller.OnClick_Receive(iIdx)
    m_isAllReceive = false
    m_NowReceiveIdx = iIdx

    local _DataTable = {}
    _DataTable.m_Kind = 2
    _DataTable.m_Idx = m_CurrentData[iIdx].m_Idx
    _DataTable.m_CollectionNumber = m_CurrentData[iIdx].m_CollectionNumber

    SendProtocol_021._018(_DataTable)
end

---點擊全部領取
function Collect_Controller.OnClick_AllReceive()
    m_isAllReceive = true
    m_CanReceiveDataCount = table.Count(m_CanReceiveData)
    for k, v in pairs(m_CanReceiveData) do
        local _DataTable = {}
        _DataTable.m_Kind = 2
        _DataTable.m_Idx = v.m_Idx
        _DataTable.m_CollectionNumber = v.m_CollectionNumber

        SendProtocol_021._018(_DataTable)
    end
end

---收到領取成功的協定設定獎勵和刷新ScrollView
---@param _Success bool server回傳的是否成功領獎bool
function Collect_Controller.GetReward(_Success)
    if m_isAllReceive then
        m_ReceivedCount = m_ReceivedCount + 1
        if m_ReceivedCount == m_CanReceiveDataCount or not _Success then
            Collect_Controller.SetGetReward(_Success)
            Collect_Controller.UpdateScrollView()
            m_isAllReceive = false
            m_ReceivedCount = 0
        end
    else
        if _Success then
            Collect_Controller.SetGetReward(_Success)
            Collect_Controller.UpdateScrollView()
        end
    end
end

---設定獲得的物品
---@param _Success bool server回傳的是否成功領獎bool
function Collect_Controller.SetGetReward(iSuccess)
    local _RewardData = {}
    _RewardData.m_RewardTitle = m_CollectReward_StringID
    _RewardData.m_RewardSubDatas = {}
    _RewardData.m_RewardSubDatas_Text = {}
    local _Point = 0
    local _MovingFlagTable = {}

    local _CanReceiveData = {}
    if iSuccess then
        if m_isAllReceive then
            _CanReceiveData = m_CanReceiveData
        else
            _CanReceiveData[1] = m_CanReceiveData[m_NowReceiveIdx]
        end
    else
        for k, v in pairs(m_CanReceiveData) do
            if k <= m_ReceivedCount then
                table.insert(_CanReceiveData, v)
            end
        end
    end
    
    _RewardData.m_RewardSubDatas[1] = {}
    for k, v in pairs(_CanReceiveData) do
        
        for _k, _v in pairs(v.m_CollectDataRewardSet) do
            --是物品才秀
            if _v.m_RewardType == 1 then
                local _Data = {}
                _Data.m_ItemID =  _v.m_RewardID
                _Data.m_ItemAmount = _v.m_RewardNum
                local _ItemData = ItemData.GetItemDataByIdx(_Data.m_ItemID)
                _Data.m_Rarity = _ItemData.m_Rarity
                table.insert(_RewardData.m_RewardSubDatas[1],_Data)
            end
        end
        _Point = _Point + v.m_ExplorationPoint

        ---如果有動標資料 要記錄 在最後領獎時可以顯示通知任務開啟文字
        if v.m_MovingFlag ~= 0 then
            table.insert(_MovingFlagTable,v.m_MovingFlag)
        end

    end
    
    local _subData = {}
    local _RewardSubData = CommonRewardMgr.GetRewardSubDataType(CommonRewardSubDataType.TextType)

    _RewardSubData.m_Title =  TextData.Get(m_Scroe_StringID)
    _RewardSubData.m_Number = ExploreDairy_Model.GetExplorePoints() - _Point
    _RewardSubData.m_AddtoNumber = ExploreDairy_Model.GetExplorePoints() 

    
    table.insert(_subData, _RewardSubData)
    
    for i = 1, table.Count(_MovingFlagTable) do
        local _RewardSubData_MovinFlag = CommonRewardMgr.GetRewardSubDataType(CommonRewardSubDataType.TextType)

        local _MissionData = MissionMgr.GetMissionByMovingFlag(_MovingFlagTable[i])
        if _MissionData ~= nil then
            ---解鎖特殊任務 字串ID 20605056
            _RewardSubData_MovinFlag.m_Title = EventStrAll:GetText(TextData.Get(20605056))
            _RewardSubData_MovinFlag.m_Content = EventStrAll:GetText(_MissionData.TitleStrID)

            table.insert(_subData, _RewardSubData_MovinFlag)
        end
    end

    table.insert(_RewardData.m_RewardSubDatas_Text,_subData)
    CommonRewardMgr.AddNewReward(CommonRewardType.ItemAndText, _RewardData )
end


---獎品區使用的OpenHint
function Collect_Controller.LongPressOpenHint_Reward(iIconData)
    Collect_Controller.OpenHint_Reward(iIconData.m_OnLongClickParam)
end

local _TitleAttributeMark = "<sprite name=Symbol_1>"
local _TitleDescriptionMark = "<sprite name=Symbol_3>"

---獎品區使用的OpenHint實做
function Collect_Controller.OpenHint_Reward(iParam)
    -- 從現在頁籤資料找Icon資料
    local _Idx = iParam[1]
    local _IconIdx = iParam[2]
    D.Log("點擊到ScrollIdx: ".._Idx.." , IconIdx: ".._IconIdx)

    local _data = m_CurrentData[_Idx]
    --物品
    if _data.m_CollectDataRewardSet[_IconIdx].m_RewardType == 1 then
        local _ItemData = ItemData.GetItemDataByIdx(_data.m_CollectDataRewardSet[_IconIdx].m_RewardID)
        HintMgr_Controller.OpenHint(EHintType.ItemHint , _ItemData)

    --稱號
    else
        local _CommonHintData = {}

        local _TitleData = TitleData.Get(_data.m_CollectDataRewardSet[_IconIdx].m_RewardID)

        local _HintBoxData = {}
        local _HintIdx = 1

        local function CreateHintBoxData(iHintBoxIdx, iPropertiesAy, iTitleStr)
            _HintBoxData[iHintBoxIdx] = CommonHint_Model.NewHintBox( iHintBoxIdx, EHintBoxType.KindAndValue, _TitleAttributeMark .. TextData.Get(iTitleStr) )
            local _AttributeList = {}
            for k, v in pairs(iPropertiesAy) do
                if v.m_Attr_ID ~= 0 then
                    -- 區塊內容
                    local _AttrString, _ValueString = GValue.AttributeToString(v.m_Attr_ID, v.m_Value, false)
                    CommonHint_Model.NewHintBoxContent_KindAndValue( _AttributeList, k, _AttrString, _ValueString )
                end
            end
            _HintBoxData[iHintBoxIdx].SetKindAndValueToHintBox(_AttributeList)

            return table.Count(_AttributeList) > 0
        end

        local function CreateTextHintBoxData(iHintBoxIdx, iTitleStr, iDescription)
            _HintBoxData[iHintBoxIdx] = CommonHint_Model.NewHintBox( iHintBoxIdx, EHintBoxType.NormalText, _TitleDescriptionMark .. TextData.Get(iTitleStr) )
            _HintBoxData[iHintBoxIdx].SetHintDescription(TextData.Get(iDescription))
        end

        local _GetPropertiesAy = CreateHintBoxData(_HintIdx, _TitleData.m_GetPropertiesAy, 510105)
        if _GetPropertiesAy then
            _HintIdx = _HintIdx + 1
        end
        local _EquipePropertiesAy = CreateHintBoxData(_HintIdx, _TitleData.m_EquipePropertiesAy, 510108)
        if _EquipePropertiesAy then
            _HintIdx = _HintIdx + 1
        end
        CreateTextHintBoxData(_HintIdx, 510104, _TitleData.m_TexIDt_Describe)

        -- CommonIcon 資料
        _CommonHintData[EHintBoxContentType.IconData] = _data.m_CommonIconData
        -- HintBox 內容
        _CommonHintData[EHintBoxContentType.HintContentData] = _HintBoxData
        HintMgr_Controller.OpenHint(EHintType.CommonHint, _CommonHintData)
    end
end


---提交物品/寵物區 使用的開啟提示
function Collect_Controller.LongPressOpenHint_SendObject(iIconData)
    Collect_Controller.OpenHint_SendObject(iIconData.m_OnLongClickParam)
end

---提交物品/寵物區 使用的開啟提示實作
function Collect_Controller.OpenHint_SendObject(iParam)
    -- 從現在頁籤資料找Icon資料
    local _Idx = iParam[1]
    local _IconIdx = iParam[2]
    D.Log("點擊到ScrollIdx: ".._Idx.." , IconIdx: ".._IconIdx)

    local _data = m_CurrentData[_Idx]
    --物品
    if m_UsePetIconType == false then
        local _ItemData = ItemData.GetItemDataByIdx(_data.m_CollectDataItemSet[_IconIdx].m_ItemID)
        HintMgr_Controller.OpenHint(EHintType.ItemHint , _ItemData)
    elseif m_UsePetIconType == true then
        local _PetData = PetData.GetPetDataByIdx(_data.m_CollectDataItemSet[_IconIdx].m_ItemID)
        print(" _data.m_CollectDataItemSet[_IconIdx].m_ItemID = " .. _data.m_CollectDataItemSet[_IconIdx].m_ItemID)
        local _PetHintData = {PetHint_Controller.DataType.PetID, _PetData.m_PetID, nil}
        HintMgr_Controller.OpenHint(EHintType.PetHint , _PetHintData)
    end
end
