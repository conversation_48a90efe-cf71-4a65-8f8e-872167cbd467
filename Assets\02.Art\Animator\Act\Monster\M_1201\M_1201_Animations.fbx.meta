fileFormatVersion: 2
guid: 26f2ef1a10ff2534b8a080389dd9ee5f
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Armature
  - first:
      1: 100002
    second: Head
  - first:
      1: 100004
    second: HeadTop_End
  - first:
      1: 100006
    second: Hips
  - first:
      1: 100008
    second: LeftArm
  - first:
      1: 100010
    second: LeftFoot
  - first:
      1: 100012
    second: LeftForeArm
  - first:
      1: 100014
    second: LeftHand
  - first:
      1: 100016
    second: LeftHandIndex1
  - first:
      1: 100018
    second: LeftHandIndex2
  - first:
      1: 100020
    second: LeftHandIndex3
  - first:
      1: 100022
    second: LeftHandIndex4
  - first:
      1: 100024
    second: LeftHandThumb1
  - first:
      1: 100026
    second: LeftHandThumb2
  - first:
      1: 100028
    second: LeftHandThumb3
  - first:
      1: 100030
    second: LeftHandThumb4
  - first:
      1: 100032
    second: LeftLeg
  - first:
      1: 100034
    second: Left<PERSON>houlder
  - first:
      1: 100036
    second: LeftT<PERSON>_End
  - first:
      1: 100038
    second: LeftToeBase
  - first:
      1: 100040
    second: LeftUpLeg
  - first:
      1: 100042
    second: //RootNode
  - first:
      1: 100044
    second: Mesh
  - first:
      1: 100046
    second: Neck
  - first:
      1: 100048
    second: RightArm
  - first:
      1: 100050
    second: RightFoot
  - first:
      1: 100052
    second: RightForeArm
  - first:
      1: 100054
    second: RightHand
  - first:
      1: 100056
    second: RightHandIndex1
  - first:
      1: 100058
    second: RightHandIndex2
  - first:
      1: 100060
    second: RightHandIndex3
  - first:
      1: 100062
    second: RightHandIndex4
  - first:
      1: 100064
    second: RightHandThumb1
  - first:
      1: 100066
    second: RightHandThumb2
  - first:
      1: 100068
    second: RightHandThumb3
  - first:
      1: 100070
    second: RightHandThumb4
  - first:
      1: 100072
    second: RightLeg
  - first:
      1: 100074
    second: RightShoulder
  - first:
      1: 100076
    second: RightToe_End
  - first:
      1: 100078
    second: RightToeBase
  - first:
      1: 100080
    second: RightUpLeg
  - first:
      1: 100082
    second: RL_BoneRoot
  - first:
      1: 100084
    second: RootNode
  - first:
      1: 100086
    second: Spine
  - first:
      1: 100088
    second: Spine1
  - first:
      1: 100090
    second: Spine2
  - first:
      1: 100092
    second: weapon_L
  - first:
      1: 100094
    second: weapon_R
  - first:
      4: 400000
    second: Armature
  - first:
      4: 400002
    second: Head
  - first:
      4: 400004
    second: HeadTop_End
  - first:
      4: 400006
    second: Hips
  - first:
      4: 400008
    second: LeftArm
  - first:
      4: 400010
    second: LeftFoot
  - first:
      4: 400012
    second: LeftForeArm
  - first:
      4: 400014
    second: LeftHand
  - first:
      4: 400016
    second: LeftHandIndex1
  - first:
      4: 400018
    second: LeftHandIndex2
  - first:
      4: 400020
    second: LeftHandIndex3
  - first:
      4: 400022
    second: LeftHandIndex4
  - first:
      4: 400024
    second: LeftHandThumb1
  - first:
      4: 400026
    second: LeftHandThumb2
  - first:
      4: 400028
    second: LeftHandThumb3
  - first:
      4: 400030
    second: LeftHandThumb4
  - first:
      4: 400032
    second: LeftLeg
  - first:
      4: 400034
    second: LeftShoulder
  - first:
      4: 400036
    second: LeftToe_End
  - first:
      4: 400038
    second: LeftToeBase
  - first:
      4: 400040
    second: LeftUpLeg
  - first:
      4: 400042
    second: //RootNode
  - first:
      4: 400044
    second: Mesh
  - first:
      4: 400046
    second: Neck
  - first:
      4: 400048
    second: RightArm
  - first:
      4: 400050
    second: RightFoot
  - first:
      4: 400052
    second: RightForeArm
  - first:
      4: 400054
    second: RightHand
  - first:
      4: 400056
    second: RightHandIndex1
  - first:
      4: 400058
    second: RightHandIndex2
  - first:
      4: 400060
    second: RightHandIndex3
  - first:
      4: 400062
    second: RightHandIndex4
  - first:
      4: 400064
    second: RightHandThumb1
  - first:
      4: 400066
    second: RightHandThumb2
  - first:
      4: 400068
    second: RightHandThumb3
  - first:
      4: 400070
    second: RightHandThumb4
  - first:
      4: 400072
    second: RightLeg
  - first:
      4: 400074
    second: RightShoulder
  - first:
      4: 400076
    second: RightToe_End
  - first:
      4: 400078
    second: RightToeBase
  - first:
      4: 400080
    second: RightUpLeg
  - first:
      4: 400082
    second: RL_BoneRoot
  - first:
      4: 400084
    second: RootNode
  - first:
      4: 400086
    second: Spine
  - first:
      4: 400088
    second: Spine1
  - first:
      4: 400090
    second: Spine2
  - first:
      4: 400092
    second: weapon_L
  - first:
      4: 400094
    second: weapon_R
  - first:
      21: 2100000
    second: No Name
  - first:
      43: 4300000
    second: Mesh
  - first:
      74: 7400000
    second: Scene
  - first:
      74: 7400002
    second: Idle_01
  - first:
      74: 7400004
    second: M_1201_Walk_01
  - first:
      74: 7400006
    second: M_1201_stun01
  - first:
      74: 7400008
    second: M_1201_Hit01
  - first:
      74: 7400010
    second: M_1201_Die01
  - first:
      74: 7400012
    second: M_1201_BIdle_01
  - first:
      74: 7400014
    second: M_1201_BRun_01
  - first:
      74: 7400016
    second: M_1201_Atk_01
  - first:
      74: 7400018
    second: M_1201_Atk_02
  - first:
      74: 7400020
    second: M_1201_Atk_03
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: Mesh
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_1201_Idle_01
      takeName: Idle_01
      internalID: 7400002
      firstFrame: 0
      lastFrame: 114
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Walk_01
      takeName: Walk_01
      internalID: 7400004
      firstFrame: 0
      lastFrame: 82
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_stun01
      takeName: stun01
      internalID: 7400006
      firstFrame: 0
      lastFrame: 162
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Hit01
      takeName: Hit01
      internalID: 7400008
      firstFrame: 0
      lastFrame: 48
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Die01
      takeName: Die01
      internalID: 7400010
      firstFrame: 0
      lastFrame: 123
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_BIdle_01
      takeName: BIdle_01
      internalID: 7400012
      firstFrame: 0
      lastFrame: 110
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_BRun_01
      takeName: BRun_01
      internalID: 7400014
      firstFrame: 0
      lastFrame: 44
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Atk_01
      takeName: Atk_01
      internalID: 7400016
      firstFrame: 0
      lastFrame: 83
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Atk_02
      takeName: Atk_02
      internalID: 7400018
      firstFrame: 0
      lastFrame: 112
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1201_Atk_03
      takeName: Atk_03
      internalID: 7400020
      firstFrame: 0
      lastFrame: 123
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
