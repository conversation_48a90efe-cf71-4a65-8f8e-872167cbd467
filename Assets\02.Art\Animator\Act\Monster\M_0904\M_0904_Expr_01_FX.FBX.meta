fileFormatVersion: 2
guid: e50f3004ba3ec644d99ab2ad7852c72d
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: DM_StoneA_FX_01
    100004: DM_StoneA_FX_02
    100006: DM_StoneA_FX_03
    100008: DM_StoneA_FX_04
    100010: DM_StoneA_FX_05
    100012: DM_StoneA_FX_06
    100014: DM_StoneA_FX_07
    100016: DM_StoneA_FX_08
    100018: DM_StoneB_FX_01
    100020: DM_StoneB_FX_02
    100022: DM_StoneB_FX_03
    100024: DM_StoneB_FX_04
    100026: DM_StoneB_FX_05
    100028: DM_StoneB_FX_06
    100030: DM_StoneB_FX_07
    100032: DM_StoneB_FX_08
    100034: DM_StoneB_FX_09
    100036: DM_StoneB_FX_10
    100038: DM_StoneB_FX_11
    100040: DM_StoneB_FX_12
    100042: DM_StoneB_FX_13
    100044: DM_StoneB_FX_14
    100046: DM_StoneB_FX_15
    100048: DM_StoneB_FX_16
    100050: DM_StoneB_FX_17
    100052: DM_StoneB_FX_18
    100054: DM_StoneB_FX_19
    100056: DM_StoneB_FX_20
    100058: //RootNode
    100060: Stone_FX
    100062: Stone_FX_Bone
    100064: StoneA_FX_Obj
    100066: StoneB_FX_Obj
    400000: Bip001
    400002: DM_StoneA_FX_01
    400004: DM_StoneA_FX_02
    400006: DM_StoneA_FX_03
    400008: DM_StoneA_FX_04
    400010: DM_StoneA_FX_05
    400012: DM_StoneA_FX_06
    400014: DM_StoneA_FX_07
    400016: DM_StoneA_FX_08
    400018: DM_StoneB_FX_01
    400020: DM_StoneB_FX_02
    400022: DM_StoneB_FX_03
    400024: DM_StoneB_FX_04
    400026: DM_StoneB_FX_05
    400028: DM_StoneB_FX_06
    400030: DM_StoneB_FX_07
    400032: DM_StoneB_FX_08
    400034: DM_StoneB_FX_09
    400036: DM_StoneB_FX_10
    400038: DM_StoneB_FX_11
    400040: DM_StoneB_FX_12
    400042: DM_StoneB_FX_13
    400044: DM_StoneB_FX_14
    400046: DM_StoneB_FX_15
    400048: DM_StoneB_FX_16
    400050: DM_StoneB_FX_17
    400052: DM_StoneB_FX_18
    400054: DM_StoneB_FX_19
    400056: DM_StoneB_FX_20
    400058: //RootNode
    400060: Stone_FX
    400062: Stone_FX_Bone
    400064: StoneA_FX_Obj
    400066: StoneB_FX_Obj
    2100000: 02 - Default
    4300000: StoneA_FX_Obj
    4300002: StoneB_FX_Obj
    7400000: M_0904_Expr_01_FX
    9500000: //RootNode
    13700000: StoneA_FX_Obj
    13700002: StoneB_FX_Obj
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: 02 - Default
    second: {fileID: 2100000, guid: e346269f78c22bc4fa28fb0687dd510a, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0904_Expr_01_FX
      takeName: M_0904_Expr_01_v3
      firstFrame: 0
      lastFrame: 130
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 1
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: efb3f663637474f448c78c496b6911e1,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
