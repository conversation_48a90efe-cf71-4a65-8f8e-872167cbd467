fileFormatVersion: 2
guid: defb45fe8c4f28f4a8a3dd4cd37554b7
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bone_F_accessories_001
    100002: Bone_F_Reins_001
    100004: Bone_F_Reins_002
    100006: Bone_L_accessories_002
    100008: Bone_L_accessories_005
    100010: Bone_L_Reins_003
    100012: Bone_L_Reins_004
    100014: Bone_L_Reins_005
    100016: Bone_L_Reins_006
    100018: Bone_L_Reins_007
    100020: Bone_R_accessories_002
    100022: Bone_R_accessories_003
    100024: Bone_R_accessories_003(mirrored)
    100026: Bone_R_accessories_004
    100028: Bone_R_accessories_004(mirrored)
    100030: Bone_R_accessories_005
    100032: Bone_R_Reins_003
    100034: Bone_R_Reins_004
    100036: Bone_R_Reins_005
    100038: Bone_R_Reins_006
    100040: Bone_R_Reins_007
    100042: Seat01
    100044: Seat02
    100046: V_0018_1Bone001
    100048: V_0018_1Bone001 1
    100050: V_0018_1Bone001Bone001
    100052: V_0018_1Bone001Bone001 1
    100054: V_0018_1Bone001Bone002
    100056: V_0018_1Bone001Bone002 1
    100058: V_0018_1Bone001Bone003
    100060: V_0018_1Bone001Bone003 1
    100062: V_0018_1Bone002
    100064: V_0018_1Bone002 1
    100066: V_0018_1Bone003
    100068: V_0018_1Bone003 1
    100070: V_0018_F_Tail_1
    100072: V_0018_F_Tail_2
    100074: V_0018_F_Tail_3
    100076: V_0018_Hub001
    100078: V_0018_Hub002
    100080: V_0018_Hub003
    100082: V_0018_Hub003Bone001
    100084: V_0018_L_Arm_1
    100086: V_0018_L_Arm_2
    100088: V_0018_L_Arm_Digit21
    100090: V_0018_L_Arm_Digit22
    100092: V_0018_L_Arm_Digit31
    100094: V_0018_L_Arm_Digit32
    100096: V_0018_L_Arm_Digit41
    100098: V_0018_L_Arm_Digit42
    100100: V_0018_L_Arm_Digit_11
    100102: V_0018_L_Arm_Digit_12
    100104: V_0018_L_Arm_L_Palm_
    100106: V_0018_L_Leg_1
    100108: V_0018_L_Leg_2
    100110: V_0018_L_Leg_3
    100112: V_0018_L_Leg_Digit_11
    100114: V_0018_L_Leg_Digit_12
    100116: V_0018_L_Leg_Digit_13
    100118: V_0018_L_Leg_Digit_21
    100120: V_0018_L_Leg_Digit_22
    100122: V_0018_L_Leg_Digit_23
    100124: V_0018_L_Leg_Digit_31
    100126: V_0018_L_Leg_Digit_32
    100128: V_0018_L_Leg_Digit_33
    100130: V_0018_L_Leg_Digit_41
    100132: V_0018_L_Leg_Digit_42
    100134: V_0018_L_Leg_L_Ankle_
    100136: V_0018_R_Arm_1
    100138: V_0018_R_Arm_2
    100140: V_0018_R_Arm_Digit21
    100142: V_0018_R_Arm_Digit22
    100144: V_0018_R_Arm_Digit31
    100146: V_0018_R_Arm_Digit32
    100148: V_0018_R_Arm_Digit41
    100150: V_0018_R_Arm_Digit42
    100152: V_0018_R_Arm_Digit_11
    100154: V_0018_R_Arm_Digit_12
    100156: V_0018_R_Arm_L_Palm_
    100158: V_0018_R_Leg_1
    100160: V_0018_R_Leg_2
    100162: V_0018_R_Leg_3
    100164: V_0018_R_Leg_Digit_11
    100166: V_0018_R_Leg_Digit_12
    100168: V_0018_R_Leg_Digit_13
    100170: V_0018_R_Leg_Digit_21
    100172: V_0018_R_Leg_Digit_22
    100174: V_0018_R_Leg_Digit_23
    100176: V_0018_R_Leg_Digit_31
    100178: V_0018_R_Leg_Digit_32
    100180: V_0018_R_Leg_Digit_33
    100182: V_0018_R_Leg_Digit_41
    100184: V_0018_R_Leg_Digit_42
    100186: V_0018_R_Leg_R_Ankle_
    100188: V_0018_R_Tail_1
    100190: V_0018_R_Tail_2
    100192: V_0018_R_Tail_3
    100194: //RootNode
    100196: V_0018_Spine1
    100198: V_0018_Spine1 1
    100200: V_0018_Spine2
    100202: V_0018_Spine2 1
    100204: V_0018_Spine3
    100206: V_0018_Spine4
    100208: V_0018_Spine5
    100210: V_0018_Tail1
    100212: V_0018_Tail2
    100214: V_0018_Tail3
    100216: V_0018_0Bone001
    100218: V_0018_0Bone001Bone001
    100220: V_0018_0Bone001Bone002
    100222: V_0018_0Bone001Bone003
    100224: V_0018_0Bone002
    100226: V_0018_0Bone003
    400000: Bone_F_accessories_001
    400002: Bone_F_Reins_001
    400004: Bone_F_Reins_002
    400006: Bone_L_accessories_002
    400008: Bone_L_accessories_005
    400010: Bone_L_Reins_003
    400012: Bone_L_Reins_004
    400014: Bone_L_Reins_005
    400016: Bone_L_Reins_006
    400018: Bone_L_Reins_007
    400020: Bone_R_accessories_002
    400022: Bone_R_accessories_003
    400024: Bone_R_accessories_003(mirrored)
    400026: Bone_R_accessories_004
    400028: Bone_R_accessories_004(mirrored)
    400030: Bone_R_accessories_005
    400032: Bone_R_Reins_003
    400034: Bone_R_Reins_004
    400036: Bone_R_Reins_005
    400038: Bone_R_Reins_006
    400040: Bone_R_Reins_007
    400042: Seat01
    400044: Seat02
    400046: V_0018_1Bone001
    400048: V_0018_1Bone001 1
    400050: V_0018_1Bone001Bone001
    400052: V_0018_1Bone001Bone001 1
    400054: V_0018_1Bone001Bone002
    400056: V_0018_1Bone001Bone002 1
    400058: V_0018_1Bone001Bone003
    400060: V_0018_1Bone001Bone003 1
    400062: V_0018_1Bone002
    400064: V_0018_1Bone002 1
    400066: V_0018_1Bone003
    400068: V_0018_1Bone003 1
    400070: V_0018_F_Tail_1
    400072: V_0018_F_Tail_2
    400074: V_0018_F_Tail_3
    400076: V_0018_Hub001
    400078: V_0018_Hub002
    400080: V_0018_Hub003
    400082: V_0018_Hub003Bone001
    400084: V_0018_L_Arm_1
    400086: V_0018_L_Arm_2
    400088: V_0018_L_Arm_Digit21
    400090: V_0018_L_Arm_Digit22
    400092: V_0018_L_Arm_Digit31
    400094: V_0018_L_Arm_Digit32
    400096: V_0018_L_Arm_Digit41
    400098: V_0018_L_Arm_Digit42
    400100: V_0018_L_Arm_Digit_11
    400102: V_0018_L_Arm_Digit_12
    400104: V_0018_L_Arm_L_Palm_
    400106: V_0018_L_Leg_1
    400108: V_0018_L_Leg_2
    400110: V_0018_L_Leg_3
    400112: V_0018_L_Leg_Digit_11
    400114: V_0018_L_Leg_Digit_12
    400116: V_0018_L_Leg_Digit_13
    400118: V_0018_L_Leg_Digit_21
    400120: V_0018_L_Leg_Digit_22
    400122: V_0018_L_Leg_Digit_23
    400124: V_0018_L_Leg_Digit_31
    400126: V_0018_L_Leg_Digit_32
    400128: V_0018_L_Leg_Digit_33
    400130: V_0018_L_Leg_Digit_41
    400132: V_0018_L_Leg_Digit_42
    400134: V_0018_L_Leg_L_Ankle_
    400136: V_0018_R_Arm_1
    400138: V_0018_R_Arm_2
    400140: V_0018_R_Arm_Digit21
    400142: V_0018_R_Arm_Digit22
    400144: V_0018_R_Arm_Digit31
    400146: V_0018_R_Arm_Digit32
    400148: V_0018_R_Arm_Digit41
    400150: V_0018_R_Arm_Digit42
    400152: V_0018_R_Arm_Digit_11
    400154: V_0018_R_Arm_Digit_12
    400156: V_0018_R_Arm_L_Palm_
    400158: V_0018_R_Leg_1
    400160: V_0018_R_Leg_2
    400162: V_0018_R_Leg_3
    400164: V_0018_R_Leg_Digit_11
    400166: V_0018_R_Leg_Digit_12
    400168: V_0018_R_Leg_Digit_13
    400170: V_0018_R_Leg_Digit_21
    400172: V_0018_R_Leg_Digit_22
    400174: V_0018_R_Leg_Digit_23
    400176: V_0018_R_Leg_Digit_31
    400178: V_0018_R_Leg_Digit_32
    400180: V_0018_R_Leg_Digit_33
    400182: V_0018_R_Leg_Digit_41
    400184: V_0018_R_Leg_Digit_42
    400186: V_0018_R_Leg_R_Ankle_
    400188: V_0018_R_Tail_1
    400190: V_0018_R_Tail_2
    400192: V_0018_R_Tail_3
    400194: //RootNode
    400196: V_0018_Spine1
    400198: V_0018_Spine1 1
    400200: V_0018_Spine2
    400202: V_0018_Spine2 1
    400204: V_0018_Spine3
    400206: V_0018_Spine4
    400208: V_0018_Spine5
    400210: V_0018_Tail1
    400212: V_0018_Tail2
    400214: V_0018_Tail3
    400216: V_0018_0Bone001
    400218: V_0018_0Bone001Bone001
    400220: V_0018_0Bone001Bone002
    400222: V_0018_0Bone001Bone003
    400224: V_0018_0Bone002
    400226: V_0018_0Bone003
    7400000: V_0018_Run
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: V_0018_Run
      takeName: V_0018_Run(_k_e_k__)
      firstFrame: 0
      lastFrame: 48
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: a214e870f2a11824bb1730adf68afb41,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
