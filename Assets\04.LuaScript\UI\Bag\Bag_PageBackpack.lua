---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---背包第二頁
---@class Bag_PageBackpack
---author 鼎翰
---version 1.0
---since [HEM 2.0]
---date 2022.12.01
Bag_PageBackpack = {}
local this = Bag_PageBackpack
-- Bag_Controller 的 ViewRef
this.m_ViewRef = {}
this.m_Controller = {}

--- 背包物品每行有5個
this.m_ItemCountPerRow = 5

--- 稀有度和種類刪除塞選每行有2個
this.m_SelectPerRow = 2

--- 當前排序規則 4 = 預設
---@type number
this.m_CurrentSortStyle = SortType.DEFAULT -- 排序種類 1.稀有度 2.等級 3.價格 4.預設

--- 當前背包頁簽資料
---@type table
this.CurrentBagTable = {}

--- 背包內Icons
---@type table
this.m_BagIcons = {}

--- 刪除選項Grp(種類)
---@type talbe
this.m_DeleteOptions = {}

--- 刪除副選項Grp(稀有度)
---@type table
this.m_DeleteSubOptions = {}

--- 輸入刪除數字中道具
--- <欲刪除道具資料, 欲刪除列表中index, IconIndex>
---@type table<table, number, number>
this.m_TmpDeleteData = {}

--- 是否要刷新背包Icon
this.IsRefreshingIcon = false

--- 要批次顯示的Icon
local m_VisibleBatchObj = {}
---@type UIVisibleBatch 批次顯示
local m_VisibleBatch

---初始化背包頁面
function Bag_PageBackpack.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --- 排序設定選單是否開啟
    this.m_IsSettingBtnActive = false

    --- 刪除模式是否開啟
    this.m_IsDeleteMenuActive = false

    --- 自動刪除選項是否開啟
    this.m_IsDeleteOptionsActive = false

    --- 是否要刷新背包Icon
    this.IsRefreshingIcon = false

    -- 刪除選項結果 預設全開
    Bag_Model.m_DeleteOptionSave = {true,true,true,true,true,true}
    
    -- 稀有度結果 預設只開白色稀有度
    Bag_Model.m_DeleteSubOptionSave = {true,false,false,false,false}

    this.m_PageIconName = {"MainIcon_067_H","MainIcon_069_H","MainIcon_068_H","MainIcon_0216_H"}
    -- region BagItem

    -- region BagItem上方TabButton
    -- 初始化GroupButtonBaseEvent
    this.m_GObjGroupButton = GroupButton.New(this.m_ViewRef.m_Dic_Trans:Get("&Group_ToggleBar_Bag"))

    -- 初始化GroupButton 各自的 Event
    for i = 1, GroupButton.GetCount(this.m_GObjGroupButton) do
        GroupButton.AddListenerByIndex(this.m_GObjGroupButton, i, EventTriggerType.PointerClick, function()
            Bag_PageBackpack.OnGroupTabButtonClick(i)
        end)
    end
    -- endregion

    this.m_BagItemIcon = this.m_ViewRef.m_Dic_Trans:Get("&ItemIconAnimate")

    this.m_GObjectScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_TopToBottom")
    this.m_ReuseItem = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row").gameObject
    this.m_ScrollView = ScrollView.Init(this.m_GObjectScrollView, false, this.m_ReuseItem,
        Bag_PageBackpack.GetMaxCountInBag, Bag_PageBackpack.AfterReuseItemInit,
        Bag_PageBackpack.AfterReuseItemIndexUpdate, true)


    this.m_Btn_TopBar_CloseBag = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_TopBar_CloseBag").gameObject)
    
    this.m_Btn_TopBar_CloseBag:AddListener(EventTriggerType.PointerClick, 
        function() 
            UIMgr.Close(Bag_Controller) 
        end)

    -- endregion

    -- Add By Chang
    --- 設定排序列表
    this.m_GObjSetting = {}
    this.m_GObjSetting = this.m_ViewRef.m_Dic_UIAnimation:Get("&GroupTab_Sort")
    
    -- 初始化排序列表GroupButton 各自的 Event
    for i = 1, GroupButton.GetCount(this.m_GObjSetting) do
        GroupButton.AddListenerByIndex(this.m_GObjSetting, 
            i, 
            EventTriggerType.PointerClick, 
            function()Bag_PageBackpack.OnGroupSortButtonClick(i)
        end)
    end

    --this.m_ViewRef.m_Dic_UIAnimation:Get("&Button_Tab").m_GObj.transform:Find("TMP_TabText")

    --- 原始功能按鈕組合包
    this.m_GroupBagDefault = this.m_ViewRef.m_Dic_Trans:Get("&GroupBagDefault")

    --- 設定排序列表按鈕(打開關閉)
    this.m_BagSortSettingBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_Setting"))
    this.m_BagSortSettingBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.OnClick_SortSetting)

    --- 刷新按鈕
    this.m_BagRefreshBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_Refresh"))
    this.m_BagRefreshBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.OnClick_Refresh)

    --- 背包空間
    this.m_DeleteGroup_BagCapBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_BagCapacity"))
    this.m_DeleteGroup_BagCapBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.OnClick_Refresh)
    this.m_Image_Icon_BagCapacity = this.m_ViewRef.m_Dic_Image:Get("&Image_Icon_BagCapacity")

    --- 背包空間文字
    this.m_BagCapacityTxt = {}
    this.m_BagCapacityTxt = this.m_ViewRef.m_Dic_TMPText:Get("&CapacityText")

    -- 設定刪除選單所需OBJ
    -- 刪除選單 開始 --
    --- 刪除選單
    this.m_DeleteGroup = {}
    this.m_DeleteGroup = this.m_ViewRef.m_Dic_Trans:Get("&Group_DeleteOptions")

    --- 回去按鈕
    this.m_DeleteGroup_BackBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_Back"))
    this.m_DeleteGroup_BackBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.DeleteMenu_OnOff)
    
    --- 取消選擇按鈕
    this.m_DeleteGroup_ResetBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_ResetMenu"))
    this.m_DeleteGroup_ResetBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.ClearSelect)

    --- 自動選擇按鈕
    this.m_DeleteGroup_AutoSelectBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_AutoSelect"))
    this.m_DeleteGroup_AutoSelectBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.DeleteOptions_OK)

    --- 自動選擇設定按鈕
    this.m_DeleteGroup_AutoSettingBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_AutoSetting"))
    this.m_DeleteGroup_AutoSettingBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.DeleteOptions_OnOff)

    --- 刪除按鈕按鈕
    this.m_DeleteGroup_DeleteBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_Delete"))
    this.m_DeleteGroup_DeleteBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.OnClick_SelectDeleteOK)

    -- 刪除選單 結束 --
    -- 分解/販賣/刪除 按鈕
    this.m_Btn_Alchemy = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_Alchemy"))
    this.m_Btn_Alchemy:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.DeleteMenu_OnOff)

    --- 開啟刪除選項
    this.m_DeleteMenuBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Icon_DeleteMenu"))
    this.m_DeleteMenuBtn:AddListener(EventTriggerType.PointerClick, Bag_PageBackpack.DeleteMenu_OnOff)

    --- 篩選刪除道具條件介面
    this.m_SelectDeleteOptions = {}
    this.m_SelectDeleteOptions = this.m_ViewRef.m_Dic_Trans:Get("&SelectDeleteOptions")

    --- 篩選刪除道具條件介面(種類條件)
    this.m_DeleteOptionGroup = {}
    this.m_DeleteOptionGroup = this.m_ViewRef.m_Dic_Trans:Get("&GroupTab_Delete_Kind")

    --- 刪除篩選選項(用於複製)
    this.m_ReuseBtn = this.m_ViewRef.m_Dic_Trans:Get("&ItemKind_Row").gameObject

    --- 設定刪除選項選單
    this.m_OptionScrollView = ScrollView.Init(this.m_DeleteOptionGroup, false, this.m_ReuseBtn,
        Bag_PageBackpack.DeleteOptions_Nums, Bag_PageBackpack.AfterInitDeleteOptions,
        Bag_PageBackpack.AfterUpdateDeleteOptions, true
    )

    --- 初始化刪除選項 各自的 Event
    for i = 1, Bag_PageBackpack.DeleteOptions_Nums() do
        this.m_DeleteOptions[i]:AddListener(EventTriggerType.PointerClick, function()
            Bag_PageBackpack.OnGroupDeleteOptionClick(i)
        end)
    end

    --- 篩選刪除道具條件副介面(稀有度條件)
    this.m_DeleteSubOptionGroup = {}
    this.m_DeleteSubOptionGroup = this.m_ViewRef.m_Dic_Trans:Get("&GroupTab_Delete_Rarity")

    --- 刪除篩選副選項(用於複製)
    this.m_ReuseSubBtn = this.m_ViewRef.m_Dic_Trans:Get("&ItemRarity_Row").gameObject

    --- 設定刪除副選項選單(稀有度)
    this.m_SubOptionScrollView = ScrollView.Init(this.m_DeleteSubOptionGroup, false, this.m_ReuseSubBtn,
        Bag_PageBackpack.DeleteSubOptions_Nums, Bag_PageBackpack.AfterInitDeleteSubOptions,
        Bag_PageBackpack.AfterUpdateDeleteSubOptions, true
    )

    --- 初始化刪除選項 各自的 Event
    for i = 1, Bag_PageBackpack.DeleteSubOptions_Nums() do
        this.m_DeleteSubOptions[i]:AddListener(EventTriggerType.PointerClick, function()
            Bag_PageBackpack.OnGroupDeleteSubOptionClick(i)
        end)
    end

    --- 刪除選項OK按鈕
    this.m_selectDeleteOK = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_OK"))
    this.m_selectDeleteOK:AddListener(EventTriggerType.PointerClick,
    function() 
        Bag_PageBackpack.DeleteOptions_OK()
        Bag_PageBackpack.DeleteOptions_OnOff(false)
    end)

    ---資源列
	this.m_Group_Resources = {}
	this.m_Group_Resources.transform = this.m_ViewRef.m_Dic_Trans:Get("&Group_Resources")
	local _CurrentResourceTable = 
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
	this.m_Group_Resources.m_Resources = ResourceBar:InitResourceBar(this.m_Group_Resources.transform, _CurrentResourceTable)
    
    -- 將背包排序整理
    Bag_Model.SortByType(this.m_CurrentSortStyle)

    -- 更新背包空間上限
    Bag_PageBackpack.BagCapacityUpdate()

    --初始化批次顯示資料
    m_VisibleBatch = UIVisibleEffect.AddUIVisibleBatch(m_VisibleBatchObj, 0.001)


    -- 讀取分頁Icon
    this.m_PageIcon = {}
    for key, value in pairs(this.m_PageIconName) do

        SpriteMgr.Load( value, function(_iSprite)
            this.m_PageIcon[key] = _iSprite
            end)
    end
end

--- 更新背包上限function
function Bag_PageBackpack.BagCapacityUpdate()

    if(this.m_DeleteGroup_BagCapBtn == nil) then
        return
    end

    -- 製作字串
    local _String = string.format("%d/%d", Bag_PageBackpack.GetBagItemCount(), Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex))

    -- 設定圓形bar條數量顯示
    if(this.m_DeleteGroup_BagCapBtn.transform:Find("Slider"):GetComponent("Slider")) then
        this.m_DeleteGroup_BagCapBtn.transform:Find("Slider"):GetComponent("Slider").value = Bag_PageBackpack.GetBagItemCount() / Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex)
    end

    -- 設定顯示字串
    this.m_BagCapacityTxt.text = _String

end

--====================排序列表類Start====================--
--- 排序列表開關
local function Setting_OnOff(iActive)

    -- 檢查有無資料
    if(this.m_GObjSetting) then

        -- 開啟/關閉按鈕 帶入引數
        this.m_IsSettingBtnActive = iActive

        -- 啟動按鈕
        this.m_GObjSetting:DoActive(this.m_IsSettingBtnActive)
    end

end

--- 排序列表開關判定
function Bag_PageBackpack.OnClick_SortSetting()

    -- 帶入相反的m_IsSettingBtn來做到開關
    Setting_OnOff(not this.m_IsSettingBtnActive)

end

--- 點下排序選項
function Bag_PageBackpack.OnGroupSortButtonClick(iIndex)

    -- 排序背包
    Bag_Model.SortByType(iIndex)

    -- 設定目前背包的排序方式
    this.m_CurrentSortStyle = iIndex

    -- 取得目前背包的目錄
    this.CurrentBagTable = Bag_Model.GetTableItemBag()

    -- 更新背包顯示
    ScrollView.UpdateToFirst(this.m_ScrollView)

    -- 開關排序列表
    Bag_PageBackpack.OnClick_SortSetting()

end
--====================排序列表類End====================--

--- 以目前排序方式來重整背包頁面
function Bag_PageBackpack.OnClick_Refresh()

    -- 以設定好的排序方式來整理背包 預設4
    Bag_Model.SortByType(this.m_CurrentSortStyle)

    -- 取得目前背包的目錄
    this.CurrentBagTable = Bag_Model.GetTableItemBag()

    -- 更新背包顯示
    ScrollView.UpdateToFirst(this.m_ScrollView)

end

--- 刪除選單開關
local function SettingDeleteMenu_OnOff(iActive)

    -- 刪除選單是否有資料
    if(this.m_DeleteGroup == nil)then
        return false
    end

    -- 設定刪除選單開關狀態
    this.m_IsDeleteMenuActive = iActive

    -- 如果 是 開啟，需要改為全選狀態，選中文字也需要改變
    -- 不是 則改回原設定
    if(iActive) then
        IconMgr.m_DoNotCancelAll = true

        -- 改變選擇種類
        IconMgr.SetSeletType(IconSetting.EMultSelectGroup.Bag)

        -- 設定Icon狀態
        for i = 1, table.Count(this.m_BagIcons) do
            this.m_BagIcons[i]:SetClickTwice(false, IconSetting.m_SelectTextId[IconSetting.ETwoStepTextId.Cancel], true)
        end
    else
        IconMgr.m_DoNotCancelAll = false

        -- 改變選擇種類
        IconMgr.SetSeletType()

        -- 設定Icon狀態
        for i = 1, table.Count(this.m_BagIcons) do
            this.m_BagIcons[i]:SetClickTwice(true)
        end
    end

    -- 開關刪除選單顯示
    if(this.m_DeleteGroup.gameObject) then
        this.m_DeleteGroup.gameObject:SetActive(this.m_IsDeleteMenuActive)
    end

    -- 同時要關閉或開啟 原始功能按鈕組合包
    if(this.m_GroupBagDefault.gameObject) then
        this.m_GroupBagDefault.gameObject:SetActive(not this.m_IsDeleteMenuActive)
    end

end

--- 刪除選單開關判定
function Bag_PageBackpack.DeleteMenu_OnOff()
    
    -- Icon選擇全部取消
    Bag_PageBackpack.ClearSelect()

    -- 改變按鈕點擊狀態
    for i = 1, table.Count(this.m_BagIcons) do
        this.m_BagIcons[i].m_ClickOnce = false
    end
    
    -- 要一起將篩選刪除道具條件介面關閉
    this.m_IsDeleteOptionsActive = false

    -- 開關選單
    if(this.m_SelectDeleteOptions.gameObject) then
        this.m_SelectDeleteOptions.gameObject:SetActive(this.m_IsDeleteOptionsActive)
    end
    SettingDeleteMenu_OnOff(not this.m_IsDeleteMenuActive)

end

--- 清除選擇Icon和列表
function Bag_PageBackpack.ClearSelect()

    -- Icon選擇全部強制取消
    IconMgr.CancelAllClick(true)

    -- 將欲清除列表清空
    Bag_Model.m_ItemDeleteList = {}

    -- 刷新介面
    ScrollView.Update(this.m_ScrollView)

end

--====================刪除篩選Start====================--
--- 篩選刪除道具條件介面開關
local function SettingDeleteOptions_OnOff(iActive)

    -- 設定bool
    this.m_IsDeleteOptionsActive = iActive

    -- 開啟或關閉
    if(this.m_SelectDeleteOptions.gameObject) then
        this.m_SelectDeleteOptions.gameObject:SetActive(this.m_IsDeleteOptionsActive)
    end

end

--- 篩選刪除道具條件介面開關判定
function Bag_PageBackpack.DeleteOptions_OnOff()

    SettingDeleteOptions_OnOff(not this.m_IsDeleteOptionsActive)
    -- 刪除選單 選擇初始化

    -- 目前做法 找到開啟的篩選選項圈起
    -- 刪除種類選項
    for i = 1, table.Count(Bag_Model.m_DeleteOptionSave) do

        -- 比對是不是開啟
        if(Bag_Model.m_DeleteOptionSave[i] == true) then

            -- 是的話 找到OBJ的選起圖片並選起
            if(this.m_DeleteOptions[i].transform:Find("Image_Icon_Selected").gameObject) then
                this.m_DeleteOptions[i].transform:Find("Image_Icon_Selected").gameObject:SetActive(Bag_Model.m_DeleteOptionSave[i] )
            end
        end
    end

    -- 刪除稀有度選項
    for i = 1, table.Count(Bag_Model.m_DeleteSubOptionSave) do

        -- 比對是不是開啟
        if(Bag_Model.m_DeleteSubOptionSave[i] == true) then

            -- 是的話 找到OBJ的選起圖片並選起
            if(this.m_DeleteSubOptions[i].transform:Find("Image_Icon_Selected").gameObject) then
                this.m_DeleteSubOptions[i].transform:Find("Image_Icon_Selected").gameObject:SetActive(Bag_Model.m_DeleteSubOptionSave[i] )
            end
        end
    end

end

--- 初始化刪除選擇(種類)
function Bag_PageBackpack.AfterInitDeleteOptions(iItem, iRowIdx)
    local _IntMaxOption = Bag_PageBackpack.DeleteOptions_Nums()

    if(iItem.m_GObj == nil) then
        return
    end

    -- 沒大於選項數
    if(iRowIdx <= _IntMaxOption / this.m_SelectPerRow) then

        -- 帶入名字
        -- [][][1] 1是名字字串 2 是表定數字
        --if(iItem.m_GObj.transform:Find("Text_Status").gameObject) then
        --    iItem.m_GObj.transform:Find("Text_Status").gameObject:GetComponent("TMPro.TextMeshProUGUI").text = TextData.Get(TabTextSetting.m_TabsText[TabTextSetting.m_Tabs.BAG][iRowIdx][1])
        --end

        -- 啟動顯示
        iItem.m_GObj:SetActive(true)

        local _Index = iRowIdx * 2

        if(_Index <= 0)then
            return;
        end

        -- 圖片色碼
        local _ImageString  = ""

        for i = 0, 1 do

            -- 初始化色碼
            _ImageString = ""

            -- 要確認有這個東西
            if(_Index - 1 + i <= table.Count(TabTextSetting.m_TabsText[TabTextSetting.m_Tabs.BAG])) then

                -- 取出色碼
                _ImageString = TabTextSetting.m_TabsText[TabTextSetting.m_Tabs.BAG][_Index - 1 + i].m_Image

            end

            -- 檢查有沒有色碼
            if(_ImageString ~= "") then

                -- 存下選項資料後面可以做改動
                this.m_DeleteOptions[_Index - 1 + i] = Button.New(iItem.m_GObj.transform:GetChild(i).gameObject)

                local _Image = this.m_DeleteOptions[_Index - 1 + i].transform:Find("Image_Icon").gameObject:GetComponent("Image")

                Button.SetImageGroup(this.m_DeleteOptions[_Index - 1 + i], _ImageString, 0)
                                
            else

                -- 沒有的話關閉那格顯示
                iItem.m_GObj.transform:GetChild(i).gameObject:SetActive(false)

            end
        end
    else
        iItem.m_GObj:SetActive(false)
    end
end

--- 更新刪除選擇(種類)
function Bag_PageBackpack.AfterUpdateDeleteOptions(iItem, iRowIdx)

end

--- 回傳刪除選項數量(種類)
function Bag_PageBackpack.DeleteOptions_Nums()
    return table.Count(TabTextSetting.m_TabsText[TabTextSetting.m_Tabs.BAG])
end

--- 初始化刪除選擇(稀有度)
function Bag_PageBackpack.AfterInitDeleteSubOptions(iItem, iRowIdx)
    local _IntMaxOption = Bag_PageBackpack.DeleteSubOptions_Nums()

    if(iItem.m_GObj == nil) then
        return
    end

    local _RowNumber = math.ceil(_IntMaxOption / this.m_SelectPerRow)

    -- 選項ID要等於或小於最大數量
    if(iRowIdx <= _RowNumber) then

        -- 帶入名字
        -- [][][1] 1是名字字串 2 是表定數字
        --if(iItem.m_GObj.transform:Find("Text_Status").gameObject) then
        --    iItem.m_GObj.transform:Find("Text_Status").gameObject:GetComponent("TMPro.TextMeshProUGUI").text = TextData.Get(TabTextSetting.m_RarityTabsOrder[iRowIdx][1])
        --end

        -- 啟動顯示
        iItem.m_GObj:SetActive(true)

        -- 
        local _Index = iRowIdx * 2

        if(_Index <= 0)then
            return;
        end

        -- 圖片色碼
        local _ColorCode  = 0

        for i = 0, 1 do

            -- 初始化色碼
            _ColorCode = 0

            -- 要確認有這個東西
            if(_Index - 1 + i <= table.Count(TabTextSetting.m_RarityTabsOrder)) then

                -- 取出色碼
                _ColorCode = TabTextSetting.m_RarityTabsOrder[_Index - 1 + i].m_Color

            end

            -- 檢查有沒有色碼
            if(_ColorCode ~= 0) then

                -- 存下選項資料後面可以做改動
                this.m_DeleteSubOptions[_Index - 1 + i] = Button.New(iItem.m_GObj.transform:GetChild(i).gameObject)

                -- 找到圖片並設下顏色
                this.m_DeleteSubOptions[_Index - 1 + i].transform:Find("Image_Icon").gameObject:GetComponent("Image").color = Extension.GetColor(_ColorCode)
            else

                -- 沒有的話關閉那格顯示
                iItem.m_GObj.transform:GetChild(i).gameObject:SetActive(false)

            end
        end
        
    else
        iItem.m_GObj:SetActive(false)
    end
end

--- 更新刪除選擇(稀有度)
function Bag_PageBackpack.AfterUpdateDeleteSubOptions(iItem, iRowIdx)

end

--- 回傳刪除選項數量(稀有度)
function Bag_PageBackpack.DeleteSubOptions_Nums()
    return table.Count(TabTextSetting.m_Rarity)
end
--====================刪除篩選End====================--

--- 設定背包Icon初始話
function Bag_PageBackpack.AfterReuseItemInit(iItem, iRowIdx)
    local _TItemIcons = {}
    for i = 1, this.m_ItemCountPerRow do
        local _ItemIconAnimate = this.m_BagItemIcon:Instantiate(iItem.m_GObj.transform)
        local _Parent = _ItemIconAnimate:Find("IconAnimFrame")
        local _Index = (iRowIdx - 1) * this.m_ItemCountPerRow + i
        _TItemIcons[i] = IconMgr.NewItemIcon(0, _Parent, this.m_BagItemIcon.sizeDelta.x, Bag_PageBackpack.PageBackIconClick, 
            _Index, IconSetting.EMultSelectGroup.Bag)
        _TItemIcons[i]:AddOnSelectAction(function(iIconSelf, iIsOnSelect)
            --D.Log("On Select _" .. iIconSelf.m_Idx)
            if iIsOnSelect and iIconSelf then
                HotKeyMgr.SetSelectIcon(iIconSelf)
            else
                HotKey_Controller.SetAllHotKeyBtnHighLightStatus(false)
            end
        end, false)
                :AddEvent(function()
            HotKeyMgr.SetSelectIcon(nil)
        end, EventTriggerType.PointerClick):SetScrollView(this.m_ScrollView)
        
        --[[ Icon 滑上去的時候框要會亮起來 改 Icon 底層控
        _TItemIcons[i].m_UIEvent:AddListener(EventTriggerType.PointerEnter, function(iEventData)
            -- 需要有道具 和 不是被選種的格子
            if this.m_BagIcons[_Index] and this.m_BagIcons[_Index].m_SaveData and not this.m_BagIcons[_Index].m_ClickOnce then
                _TItemIcons[i].m_Trans_Select.gameObject:SetActive(true)
            end
        end)

        -- Icon 滑離開的時候框要會關起來
        _TItemIcons[i].m_UIEvent:AddListener(EventTriggerType.PointerExit, function(iEventData)
            --- 需要有道具 和 不是被選種的格子
            if this.m_BagIcons[_Index] and this.m_BagIcons[_Index].m_SaveData and not this.m_BagIcons[_Index].m_ClickOnce then
                _TItemIcons[i].m_Trans_Select.gameObject:SetActive(false)
            end
        end)]]--

        _TItemIcons[i].m_Parent = _Parent

        -- 存下新創Icon資料
        this.m_BagIcons[_Index] = _TItemIcons[i]

        --- 這個 Icon 是不是可擴張的
        this.m_BagIcons[_Index].IsExpendIcon = false
        _ItemIconAnimate.gameObject:SetActive(true)
        if iRowIdx < 4 then
            _Parent.gameObject:SetActive(false)
            table.insert(m_VisibleBatchObj, _Parent.gameObject) 
        end

    end

    if iRowIdx >= 4 then
        iItem.m_GObj:SetActive(false)
        table.insert(m_VisibleBatchObj, iItem.m_GObj) 
    end

    iItem.m_Icons = _TItemIcons
end

--- 使用數字鍵盤後
local function UseNumberPanel(iAmount)

    -- 選擇0的話退出
    if(iAmount <= 0) then
        return false
    end

    -- 準備要塞入表內的資料
    local _InsertData = {this.m_TmpDeleteData[1], iAmount}

    -- 加入到表中
    table.insert(Bag_Model.m_ItemDeleteList, this.m_TmpDeleteData[2], _InsertData)

    -- 設定Icon已點擊一次
    this.m_BagIcons[this.m_TmpDeleteData[3]].m_ClickOnce = true

    -- 設定Icon是否需要點及兩次 和 選中文字
    this.m_BagIcons[this.m_TmpDeleteData[3]]:SetClickTwice(false, IconSetting.m_SelectTextId[IconSetting.ETwoStepTextId.Cancel], true)

    -- 設定Icon顯示數量
    this.m_BagIcons[this.m_TmpDeleteData[3]]:SetCount(iAmount)

    -- 選中Icon
    IconMgr.SelectIcon(this.m_BagIcons[this.m_TmpDeleteData[3]], true)
end

--- 刪除模式下點擊Icon的Event
local function DeleteItemSelect(iSaveData, iParam)
    local _InsertData = {}
    -- 找到欲刪除表內有無此按鍵物品
    local _Count = table.Count(Bag_Model.m_ItemDeleteList)

    -- 設最低要有1
    if(_Count < 1) then
        _Count = 1
    end

    for i = 1, _Count do
        -- 有資料並且有找到在欲刪除列表中
        -- [i][1]是存放資料[i][2]是數量
        if(Bag_Model.m_ItemDeleteList[i] ~= nil and Bag_Model.m_ItemDeleteList[i][1] == iSaveData) then

            -- 從列表中移除
            table.remove(Bag_Model.m_ItemDeleteList, i)

            -- 清除選擇
            this.m_BagIcons[iParam]:CancelSelect()

            -- 設定Icon顯示數量
            this.m_BagIcons[iParam]:SetCount(iSaveData.m_Count)

            break

        -- 如果 i == 數量 代表這東西是新的需要加入到列表中
        elseif(i == _Count) then

            -- 如果道具數量大於1 需要彈出數字鍵來輸入數字
            if(iSaveData.m_Count > 1) then

                -- 把點擊中道具資料存起來
                --this.m_TmpDeleteData = {}
                this.m_TmpDeleteData = {iSaveData, i, iParam}

                -- 打開數字鍵盤
                NumberPanel_Controller.OpenNumberPanel(0, iSaveData.m_Count, UseNumberPanel)

            else

                -- 物品只有1個
                _InsertData = {iSaveData, 1}

                -- 加入到表中
                table.insert(Bag_Model.m_ItemDeleteList, i, _InsertData)

                -- 設定Icon已點擊一次
                this.m_BagIcons[iParam].m_ClickOnce = true

                -- 設定Icon是否需要點及兩次 和 選中文字
                this.m_BagIcons[iParam]:SetClickTwice(false, IconSetting.m_SelectTextId[IconSetting.ETwoStepTextId.Cancel], true)

                -- 選中Icon
                IconMgr.SelectIcon(this.m_BagIcons[iParam], true)
            end
        end
    end
end

--- 設定背包Icon點擊
function Bag_PageBackpack.PageBackIconClick(iParam)
    
    if(this.m_BagIcons[iParam].m_SaveData == nil)then

        if(this.m_BagIcons[iParam].IsExpendIcon) then
            
            SellMgr.OpenUISell(BAG_COMMONQUERY_IDX[Bag_Model.m_CurrentSubPageIndex], {}, SellMgr.SendProtocol_012_001, {BAG_SELL_IDX})
            --return

        end

        return
    end

    -- 點擊Icon不在刪除模式時
    if (this.m_IsDeleteMenuActive ~= true) then

        -- 設定目前點選道具
        BagMgr.SetNowSelectItemSave(this.m_BagIcons[iParam].m_SaveData) 

        -- 取得目前點選道具
        local _Temp = BagMgr.GetNowSelectItemSave()
        local _iBagType = BagMgr.GetBagTypeByItemID(_Temp.m_ItemIdx)
        -- 使用道具
        BagMgr.UseItembySID(_Temp.m_SID, 1 ,_iBagType)

    end

    -- 在刪除模式中
    if (this.m_IsDeleteMenuActive == true) then
        DeleteItemSelect(this.m_BagIcons[iParam].m_SaveData, iParam)
    end

end

--- 背包Icon更新
function Bag_PageBackpack.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    local _Icons = iItem.m_Icons

    -- 共用詢問視窗資料
    local _BuyBagCommonqueryData = CommonQuery.GetCommonQueryByIdx(BAG_COMMONQUERY_IDX[Bag_Model.m_CurrentSubPageIndex])

    -- 買包空間介消資料
    local _BuyBagSellData = {}
    if(_BuyBagCommonqueryData ~= nil) then

        _BuyBagSellData = SellData.GetSellDataByIdx(_BuyBagCommonqueryData.m_CostIDX)

    end
    -- Update Row Element
    for i = 1, this.m_ItemCountPerRow do
        local _DataIdx = (iRowIdx - 1) * this.m_ItemCountPerRow + i
        local _HasData, _SaveData = Bag_PageBackpack.GetItemBagEatchIndex(_DataIdx)
        local _Icon = _Icons[i]
        _Icon.IsExpendIcon = false

        -- 打開顯示
        if(not _Icon.gameObject.activeInHierarchy) then
            _Icon.gameObject:SetActive(true)
        end

        -- 會需要將沒有資料的格子也顯示空格
        if (_HasData) then

            -- 更新Icon內容
            _Icon:RefreshIcon(_SaveData)

            -- 設定需要點擊兩下
            _Icon:SetClickTwice(true)
            
            -- 選中Icon
            IconMgr.CancelSelectIcon(_Icon)

            -- 如果是待刪除資料須選上
            for key, value in pairs (Bag_Model.m_ItemDeleteList) do
                if(value[1].m_SID == _SaveData.m_SID) then

                    -- 設定Icon已點擊一次
                    _Icon.m_ClickOnce = true

                    -- 設定Icon是否需要點及兩次 和 選中文字
                    _Icon:SetClickTwice(false, IconSetting.m_SelectTextId[IconSetting.ETwoStepTextId.Cancel], true)

                    -- 設定Icon顯示數量
                    _Icon:SetCount(value[2])

                    -- Icon選取
                    IconMgr.SelectIcon(_Icon, true)

                end
            end

        -- 最下行第一格會需要是"加"號
        elseif ((iRowIdx - 1) * this.m_ItemCountPerRow + i == Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex) + 1 and SellData.FlagCheck(_BuyBagSellData)) then

            -- 刷新Icon(空)
            _Icon:RefreshIcon(0)
            _Icon:SetSpecialTexture(IconSetting.ESpecialTextureType.Add) -- 圖換成加號

            -- 將選定圈圈關閉
            _Icon:CancelSelect()

            _Icon:SetClickTwice(false)
            _Icon.IsExpendIcon = true

        -- 最下行第三格以後的格子不需要顯示
        elseif ((iRowIdx - 1) * this.m_ItemCountPerRow + i >= Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex) + 1) then

            -- 關閉Icon顯示
            if(_Icon.gameObject) then
                _Icon.gameObject:SetActive(false)
            end

        -- 沒有資料的格子也需要更新並顯示ICON
        else
            
            -- 刷新Icon(空)
            _Icon:RefreshIcon(0)

            -- 將選定圈圈關閉
            _Icon:CancelSelect()

        end

        -- -- 如果 是 開啟，需要改為全選狀態，選中文字也需要改變
        -- if(this.m_IsDeleteMenuActive) then
        --     IconMgr.SetSeletType(iMultSelectGroup)
        -- end
    end

    -- 啟動Icon
    if(iItem.m_GObj and iItem.m_GObj.activeSelf == false) then
        --iItem.m_GObj:SetActive(true)
    end

end

--- 回傳的值用於背包格子直排數(改版需要顯示空的格子)(TODO:改玩家最大背包格數)
function Bag_PageBackpack.GetMaxCountInBag()
    -- 檢查購買次數是否已達最大值
    local function CheckBagBuyTimesIsMax(iBagExpansionFlag)
        local _TakemedicineData = TakemedicineData.GetTakemedicineDataByIdx(iBagExpansionFlag)
        if _TakemedicineData == nil then
            return true
        end
        return PlayerData_Attributes.GetYuDanUseCodition(iBagExpansionFlag) >= _TakemedicineData.m_UseLimit
    end

    local _MaxCountInScrollView = Mathf.Ceil(Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex) / this.m_ItemCountPerRow)

    local _ChangeRow = 0
    -- 擴充滿時不需要多一行顯示加號
    if not CheckBagBuyTimesIsMax(BAG_EXPANSION_FLAG[Bag_Model.m_CurrentSubPageIndex])
        and (Bag_Model.Bag_Max_Count(Bag_Model.m_CurrentSubPageIndex) % this.m_ItemCountPerRow == 0) then
        _ChangeRow  = 1
    end

    -- +1 原因需多出背包列表最下一行 + 1
    return _MaxCountInScrollView + _ChangeRow

end

--- 刪除刪選選項按鈕點擊OK
function Bag_PageBackpack.DeleteOptions_OK()

    -- 自動選擇刪除選項
    Bag_Model.AutoSelectDelete()

    -- 更新時先將點選全部取消
    IconMgr.CancelAllClick()

    -- 刷新介面
    ScrollView.Update(this.m_ScrollView)

end

function Bag_PageBackpack.OnClick_SelectDeleteOK()

    -- 打開物品確認清單
    ConfirmList_Model.OpenConfirmList("物品刪除清單", Bag_Model.m_ItemDeleteList, Bag_PageBackpack.DeleteItem, UIMgr.Close(ConfirmList_Controller))

end

function Bag_PageBackpack.DeleteItem()
    -- TODO 打開確定刪除畫面
    SendProtocol_007._002(table.Count(Bag_Model.m_ItemDeleteList), Bag_Model.m_ItemDeleteList)
    
    -- 將欲清除列表清空
    Bag_Model.m_ItemDeleteList = {}
    IconMgr.CancelAllClick(true)
end

---依照各類型，取得物品 Table 後，回傳指定 Index 的相對應物品
---@param iIndex integer DataTable 的 iIndex
---@return SaveItemData
function Bag_PageBackpack.GetItemBagEatchIndex(iDataIndex)
    return this.CurrentBagTable[iDataIndex] ~= nil, this.CurrentBagTable[iDataIndex]
end

---依照各類型，取得物品數量
---@param iIndex integer DataTable 的 iIndex
function Bag_PageBackpack.GetBagItemCount()
    return Bag_Model.GetBagItemCount(Bag_Model.m_CurrentSubPageIndex)
end

---背包物品上方Tab子頁面按鈕Event
---@param iGObjGroupTab table GameObject[] 要家 Event 的群組按鈕 GameObject 們
---@param iIndex integer 目前要加 Event 的按鈕 Index
function Bag_PageBackpack.OnGroupTabButtonClick(iIndex)

    if this.m_IsDeleteMenuActive then
        Bag_PageBackpack.DeleteMenu_OnOff()
    end
    -- 更新時先將點選全部取消
    IconMgr.CancelAllClick()

    -- 切頁前 刪除新物品清單
    if Bag_Model.m_CurrentSubPageIndex ~= iIndex then
        BagMgr.ClearPageNewItemList(Bag_Model.m_CurrentSubPageIndex)
    end
    -- 改背包分頁
    Bag_Model.m_CurrentSubPageIndex = iIndex
    
    -- 如果需要讓新獲得到具排再背包最後面 請註解此方法
    Bag_Model.SortByType(Bag_PageBackpack.m_CurrentSortStyle)
    -- 如果需要讓新獲得到具排再背包最後面 請註解此方法

    -- 切換分頁圖示
    this.m_Image_Icon_BagCapacity.sprite = this.m_PageIcon[Bag_Model.m_CurrentSubPageIndex]

    -- 取得改完分頁後的背包道具
    this.CurrentBagTable = Bag_Model.GetTableItemBag()

    -- 更新背包空間上限
    Bag_PageBackpack.BagCapacityUpdate()

    -- 更新背包顯示
    ScrollView.UpdateToFirst(this.m_ScrollView)

end

--- 背包物品有更新會呼叫
function Bag_PageBackpack:OnStateChanged(iState)

    if(this.m_ScrollView == nil) then
        return
    end

    if iState == EStateObserver.BagDataRefresh then

        -- 需要更新背包
        this.IsRefreshingIcon = true

    end

    if iState == EStateObserver.UpdateDiamond or
    iState == EStateObserver.UpdateCoin or
    iState == EStateObserver.UpdateHEMCoin then
        -- 更新錢包
        this.m_Group_Resources.m_Resources:OnUpdate()
    end

end

---刪除篩選選項點選event
---@param iIndex integer 目前要加 Event 的按鈕 Index
function Bag_PageBackpack.OnGroupDeleteOptionClick(iIndex)

    -- 更新選擇中結果
    Bag_Model.m_DeleteOptionSave[iIndex] = not Bag_Model.m_DeleteOptionSave[iIndex]

    -- 設定選項是否圈起
    if(this.m_DeleteOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject) then
        this.m_DeleteOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject:SetActive(Bag_Model.m_DeleteOptionSave[iIndex] )
    end

end

---刪除篩選選項點選event
---@param iIndex integer 目前要加 Event 的按鈕 Index
function Bag_PageBackpack.OnGroupDeleteSubOptionClick(iIndex)

    -- 更新選擇中結果
    Bag_Model.m_DeleteSubOptionSave[iIndex] = not Bag_Model.m_DeleteSubOptionSave[iIndex]

    -- 設定選項是否圈起
    if(this.m_DeleteSubOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject) then
        this.m_DeleteSubOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject:SetActive(Bag_Model.m_DeleteSubOptionSave[iIndex] )
    end
end

---初始化紅點們
function Bag_PageBackpack.InitRedPoint()
    this.GroupRedPoint = GroupButton.GetRedPoint(this.m_GObjGroupButton)

    this.m_Image_RedPoint_Weapon = this.GroupRedPoint[1]
    this.m_Image_RedPoint_Clothes = this.GroupRedPoint[2]
    this.m_Image_RedPoint_Necklace = this.GroupRedPoint[3]
    this.m_Image_RedPoint_Dia = this.GroupRedPoint[4]
    this.m_Image_RedPoint_Item = this.GroupRedPoint[5]
end

---註冊紅點
function Bag_PageBackpack.RegistRedPoint()
    -- region 註冊紅點Callback
    -- endregion
end

---更新紅點
function Bag_PageBackpack.UpdateRedPoint()

end

function Bag_PageBackpack.Open(iIndex)
    D.LogWarning(GString.Format("開啟子分頁：{0}", iIndex))
    -- 更新時先將點選全部取消
    GroupButton.OnPointerClickByIndex(this.m_GObjGroupButton, iIndex)
    m_VisibleBatch:Start(0)

end

function Bag_PageBackpack.Close()
    -- 背包頁籤回到第一頁
    Bag_Model.m_CurrentSubPageIndex = 1
    
    if(this.m_IsDeleteMenuActive) then
		this.DeleteMenu_OnOff()
	end

    -- 從最上面的開始排下去
    local _SortedTable = {}
    for _Key, _Value in pairs(this.m_ScrollView.m_ReuseItemsTable) do
    
        table.insert(_SortedTable, _Value)
    
    end
    
    -- 以物件位置高低排序位置 以最高的開始排
    table.sort(_SortedTable, function(iValue1, iValue2) 
        return iValue1.m_GObj.transform.localPosition.y > iValue2.m_GObj.transform.localPosition.y end)
    
    -- 清掉顯示 table
    table.Clear(m_VisibleBatchObj)
    
    -- 塞入一樣的排序規則
    for _K, _V in pairs(_SortedTable) do
            
        for i = 1, this.m_ItemCountPerRow do
            local _Parent = _V.m_Icons[i].m_Parent
            if _K < 4 then
                _Parent.gameObject:SetActive(false)
                table.insert(m_VisibleBatchObj, _Parent.gameObject) 
            end
        end
        
        if _K >= 4 then
            _V.m_GObj:SetActive(false)
            table.insert(m_VisibleBatchObj, _V.m_GObj) 
        end
    
    end
    
    m_VisibleBatch:Stop()
end

function Bag_PageBackpack.OnDestroy()
    UIVisibleEffect.RemoveUIVisibleBatch(m_VisibleBatch)
    return true
end

function Bag_PageBackpack.Update()
    if not UIMgr.IsVisible(Bag_Controller) then
        -- body
    end
    -- D.LogWarning(string.format("位置：%s", tostring(this.m_LoopList.ContainerTrans.localPosition))) 
    if (this.IsRefreshingIcon) then
        -- 更新時先將點選全部取消
        IconMgr.CancelAllClick()
    
        -- 取得加入新道具後的背包資料
        this.CurrentBagTable = Bag_Model.GetTableItemBag(this.IsRefreshingIcon)
        
        -- 更新背包空間上限
        Bag_PageBackpack.BagCapacityUpdate()
        
        -- 更新背包顯示
        ScrollView.Update(this.m_ScrollView)

        -- 更新後關掉
        this.IsRefreshingIcon = false
        
    end

end
