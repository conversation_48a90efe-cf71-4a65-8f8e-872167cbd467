---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Class Describes 屬性介面 頭像子分頁
---@class RoleAttribute_Page_Avatar
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.3.12
RoleAttribute_Page_Avatar = {}
local this = RoleAttribute_Page_Avatar

---頭像介面 資料清單類別
EAvatarListDataType = 
{
    ---頭像圖片
    HeadImage=1,
    ---外框圖片
    FrameImage =2,
}

---每一橫排可以多少張圖片
local _AmountOfRow = 4 

--- 頭像頭框列表
this.m_ListTable = {}

---目前使用/選擇的 頭像/--頭像外框
local m_CurrentHeadIcon = 0
local m_CurrentHeadFrame = 0
local m_SelectedHeadIcon = 0
local m_SelectedHeadFrame = 0

local m_HeadBtns = {}
local m_FrameBtns = {}

---初始化
function RoleAttribute_Page_Avatar.Init(iController)

    this.m_FocusIcon = {}
    this.m_FocusIcon.m_Type = EAvatarListDataType.HeadImage
    this.m_FocusIcon.m_Index = 0
    --this.m_FocusSpriteName = nil

    --設定RolAttribute_Controller
    this.m_Controller = iController.m_UIController
    --設定ViewRef
    this.m_ViewRef = iController.m_ViewRef
    --頭像介面本體 &DetailAttrPanel
    this.m_Page_GameObject = this.m_ViewRef.m_Dic_Trans:Get("&AvatarPage").gameObject

    RoleAttribute_Page_Avatar.BuiidTable()

     --頭像外框項目 物件
    this.m_IconFrameUnit_Head = this.m_ViewRef.m_Dic_Trans:Get("&IconFrameUnit").gameObject

     --頭像外框項目 物件
     this.m_IconFrameUnit_Frame = this.m_ViewRef.m_Dic_Trans:Get("&IconFrameUnit2").gameObject

     --ScrollView 項目掛載的 parent
    this.m_ContentAvatarList = this.m_ViewRef.m_Dic_Trans:Get("&ContentSubAttributeList_Avatar")

    ---根據串表 生成頭像物件
    for i = 1, table.Count(this.m_ListTable) do
        RoleAttribute_Page_Avatar.SetAvatarItem(i)
    end
    ---設定使用中/被選擇提示
    RoleAttribute_Page_Avatar.AssignHintOutLine()

    --說明面板
    this.m_AvatarInfoPanel = this.m_ViewRef.m_Dic_Trans:Get("&AvatarInfoPanel").gameObject
    --說明面板 內文
    this.m_TMP_HeadPanelContent = this.m_ViewRef.m_Dic_Trans:Get("&TMP_HeadPanelContent").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
    --說明面板 按鍵
    this.m_Btn_Select = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Select").gameObject)
    --說明面板 按鍵的說明文字
    this.m_TMP_CanUse = this.m_ViewRef.m_Dic_Trans:Get("&TMP_CanUse").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

    ---說明面板 按鍵的附加方法
    this.m_Btn_Select:AddListener(EventTriggerType.PointerClick,RoleAttribute_Page_Avatar.ConfirmChangeSprite)
end

---Update
function RoleAttribute_Page_Avatar.Update()
end

---開啟介面
function RoleAttribute_Page_Avatar.Open()
    ---物件本身開啟
    this.m_Page_GameObject:SetActive(true)
    ---默認關閉說明面板
    this.m_AvatarInfoPanel:SetActive(false)
    
    ---生成特效
    RoleAttribute_Page_Avatar.AssignAllEffectToHeadAndFrame()

    ---關閉裝備介面
    UIMgr.Close(Equipment_Controller)
end

---關閉介面
function RoleAttribute_Page_Avatar.Close()
    this.m_Page_GameObject:SetActive(false)
    this.m_AvatarInfoPanel:SetActive(false)

     ---回收特效
     RoleAttribute_Page_Avatar.RecycleAllEffectToHeadAndFrame()

    ---刷新左上角頭像顯示
    Main_SubCtrl_RoleAttribute.UpdatePlayerIcon(PlayerData.m_LoginPlayerData.m_HeadIconID_Server,PlayerData.m_LoginPlayerData.m_FrameIconID_Sever) 
end

---建立所有的頭像 外框 table 並填到同一張表
function RoleAttribute_Page_Avatar.BuiidTable()
    
    ---在table 的哪一個index
    local _CurentDataIndex = 1
  
    ---本次頭像填表 需要產生幾個index 
    ---目前規劃一橫排放4個 所以要除4  去計算要幾個橫排

    ---頭像的資料需要幾橫排
    local _LayerNeed = (HeadIconData.m_DataCount %_AmountOfRow == 0) and (HeadIconData.m_DataCount /_AmountOfRow) or (HeadIconData.m_DataCount /_AmountOfRow +1)

    for i = 1, _LayerNeed do
        this.m_ListTable[_CurentDataIndex] = {}
        --- 紀錄的種類 是頭像 還是頭框
        this.m_ListTable[_CurentDataIndex].m_DataType=  EAvatarListDataType.HeadImage
         --- 紀錄的種類 流水號index
        this.m_ListTable[_CurentDataIndex].m_ImageIndex = {}

        for j = 1, _AmountOfRow do
            local _DataIndex = (i-1) * _AmountOfRow + j

            ---如果不是整除 會填不滿 出現index大於總是量
            if _DataIndex > HeadIconData.m_DataCount then
                break
            end
            --- 填入頭像編號
            local _HeadIconData = HeadIconData.GetHeadIconDataByIdx(_DataIndex)
            if _HeadIconData ~= nil then
                this.m_ListTable[_CurentDataIndex].m_ImageIndex[j] = {}
                this.m_ListTable[_CurentDataIndex].m_ImageIndex[j].m_Idx = _HeadIconData.m_Idx
            end
        end
        ---填完目前這一層後  index +1
         _CurentDataIndex = _CurentDataIndex +1
    end

    ---頭框的資料需要幾橫排
    _LayerNeed = (HeadFrameData.m_DataCount %_AmountOfRow == 0) and (HeadFrameData.m_DataCount /_AmountOfRow) or (HeadFrameData.m_DataCount /_AmountOfRow +1)
    for i = 1, _LayerNeed do
        this.m_ListTable[_CurentDataIndex] = {}
        --- 紀錄的種類 是頭像 還是頭框
        this.m_ListTable[_CurentDataIndex].m_DataType=  EAvatarListDataType.FrameImage
        --- 紀錄的種類 流水號index
        this.m_ListTable[_CurentDataIndex].m_ImageIndex = {}

        for j = 1, _AmountOfRow do
            local _DataIndex = (i-1) * _AmountOfRow + j

            ---如果不是整除 會填不滿 出現index大於總是量
            if _DataIndex > HeadFrameData.m_DataCount then
                break
            end

            --- 填入頭框編號
            local _HeadFrameData = HeadFrameData.GetHeadFrameDataByIdx(_DataIndex)
            if _HeadFrameData ~= nil then
                this.m_ListTable[_CurentDataIndex].m_ImageIndex[j] = {}
                this.m_ListTable[_CurentDataIndex].m_ImageIndex[j].m_Idx  = _HeadFrameData.m_Idx
            end
        end
        ---填完目前這一層後  index +1
         _CurentDataIndex = _CurentDataIndex +1
    end
end

---頭像/頭框 放入圖片 特效
---@param iRowIdx number m_ListTable第幾筆數據要更新資料
function RoleAttribute_Page_Avatar.SetAvatarItem(iRowIdx)

    ---取得頭像/頭框 table的紀錄資訊
    local _HeadFrameObj ={}
    local _HeadFrameData= this.m_ListTable[iRowIdx]
    local _InsertIndex = 1
   
    ---取得prefab物件
    if _HeadFrameData.m_DataType == EAvatarListDataType.HeadImage then
        _HeadFrameObj.m_GameObject = GameObject.Instantiate(this.m_IconFrameUnit_Head)
    else
        _HeadFrameObj.m_GameObject = GameObject.Instantiate(this.m_IconFrameUnit_Frame)
    end

    ---每一橫排有 不只一個 所以要再生成一個table
    _HeadFrameObj.m_ImageSet = {} 
    local _PreNameWord = "Image_HeadFrame"

   ---設定圖片的欄位
    if _HeadFrameData.m_DataType == EAvatarListDataType.HeadImage then
        for i = 1, _AmountOfRow do
            local _Name = _PreNameWord..i
            _HeadFrameObj.m_ImageSet[i] ={}
            _HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame = _HeadFrameObj.m_GameObject.transform:Find(_Name).gameObject
            _HeadFrameObj.m_ImageSet[i].m_ImgBase = _HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame.transform:Find("Img_Base").gameObject
            _HeadFrameObj.m_ImageSet[i].m_Image = _HeadFrameObj.m_ImageSet[i].m_ImgBase.transform:Find("Img_HeadIcon").gameObject:GetComponent(typeof(Image))
            _HeadFrameObj.m_ImageSet[i].m_Btn = Button.New(_HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame)
        end
    else
        for i = 1, _AmountOfRow do
            local _Name = _PreNameWord..i
            _HeadFrameObj.m_ImageSet[i] ={}
            _HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame = _HeadFrameObj.m_GameObject.transform:Find(_Name).gameObject
            _HeadFrameObj.m_ImageSet[i].m_Image = _HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame.transform:Find("Img_Frame").gameObject:GetComponent(typeof(Image))
            _HeadFrameObj.m_ImageSet[i].m_Btn = Button.New(_HeadFrameObj.m_ImageSet[i].m_Image_HeadFrame)
        end
    end

    ---填入圖片/特效/生成排序位置
    if _HeadFrameData.m_DataType == EAvatarListDataType.HeadImage then
        RoleAttribute_Page_Avatar.LoadSpriteAndWriteEffectID(iRowIdx,_HeadFrameData.m_DataType,_HeadFrameObj)
        _InsertIndex = _InsertIndex + iRowIdx
    else
        RoleAttribute_Page_Avatar.LoadSpriteAndWriteEffectID(iRowIdx,_HeadFrameData.m_DataType,_HeadFrameObj)
        _InsertIndex = _InsertIndex + 1 + iRowIdx
    end
    
    ---開啟物件放到父物件下以及設定Scale
    _HeadFrameObj.m_GameObject:SetActive(true)
    _HeadFrameObj.m_GameObject.transform:SetParent(this.m_ContentAvatarList)
    _HeadFrameObj.m_GameObject.transform.localScale = Vector3(1,1,1)

    ---設定排序位置
    _HeadFrameObj.m_GameObject.transform:SetSiblingIndex(_InsertIndex);
end

---頭像/頭框的按鍵委託方法
---@param iType EAvatarListDataType 目前按鍵的類別
---@param iIndex number 目前按鍵的流水號
---@param iButtonSelf GameObject 按鍵的物件本身
function RoleAttribute_Page_Avatar.ButtonClick(iType,iIndex,iButtonSelf)

    ---目前按下去的按鍵 紀錄的是哪一種類別
    this.m_FocusIcon.m_Type = iType
    ---目前按下去的按鍵 紀錄的流水號
    this.m_FocusIcon.m_Index = iIndex

     --獲得方式說明內文 字串ID
     local _StringNo 

     --目前選的圖片是否正在使用中
     local _FocusSpriteNameIsUsing

     --目前選的圖片是否可以使用
     local _FocusSpriteQualified 
    
    if iType == EAvatarListDataType.HeadImage then

        ---目前選擇的圖片是否再被使用
        _FocusSpriteNameIsUsing = RoleAttribute_Page_Avatar.CheckHeadFrameIsUsing(iType ,iIndex)

        -- 判斷是否獲得這個頭像/頭框
        _FocusSpriteQualified =HeadIconData.CheckQualified(iIndex)

        --替換頭像區域的Sprite
        Main_SubCtrl_RoleAttribute.SetPlayerHeadIcon_TempReplace(iIndex)
        ---設定選擇中頭像提示數字
        m_SelectedHeadIcon = iIndex

        ---獲得方式說明字串編號
        _StringNo = HeadIconData.GetHeadIconDataByIdx(iIndex).m_GetStringID

    elseif iType == EAvatarListDataType.FrameImage then

        ---目前選擇的圖片是否再被使用
        _FocusSpriteNameIsUsing = RoleAttribute_Page_Avatar.CheckHeadFrameIsUsing(iType ,iIndex)
        -- 判斷是否獲得這個頭像/頭框
        _FocusSpriteQualified =HeadFrameData.CheckQualified(iIndex)

        --替換頭像區域的Sprite
        Main_SubCtrl_RoleAttribute.SetPlayerFrame_TempReplace(iIndex)
        ---設定選擇中頭框提示數字
         m_SelectedHeadFrame = iIndex
        
        ---獲得方式說明字串編號
        _StringNo = HeadFrameData.GetHeadFrameDataByIdx(iIndex).m_GetStringID

    end

    ---設定選擇框/目前使用框 提示
    RoleAttribute_Page_Avatar.AssignHintOutLine()

    --開啟說明面板
    this.m_AvatarInfoPanel:SetActive(true)
    
    this.m_TMP_HeadPanelContent.text = TextData.Get(_StringNo)
    
    ---按鍵設定
    RoleAttribute_Page_Avatar.SetInfoPanelButtonStatus(_FocusSpriteNameIsUsing,_FocusSpriteQualified)
    
end

---頭像/頭框 說明面版的按鍵 的委託方法
function RoleAttribute_Page_Avatar.ConfirmChangeSprite()
    RoleAttribute_Page_Avatar.SendChangeSpriteToServer()
end

---送頭像/頭框 協定給server
function RoleAttribute_Page_Avatar.SendChangeSpriteToServer()
    SendProtocol_003._013(this.m_FocusIcon.m_Type,this.m_FocusIcon.m_Index)
end

---按鍵狀態更新
---@param iFocusSpriteNameIsUsing bool  目前點選的圖片是否在使用中
---@param iFocusSpriteQualified bool 玩家是否可以使用目前被點選的圖片
function RoleAttribute_Page_Avatar.SetInfoPanelButtonStatus(iFocusSpriteNameIsUsing,iFocusSpriteQualified)
    
    --按鍵是否可以被點擊並執行delegate
    this.m_Btn_Select.interactable = (iFocusSpriteQualified == true and iFocusSpriteNameIsUsing == false)

    ---替換顯示文字
    if iFocusSpriteQualified == false then
        this.m_TMP_CanUse.text = TextData.Get(432604)
        this.m_Btn_Select:SetDisable(false)
    elseif iFocusSpriteNameIsUsing == true then
        this.m_TMP_CanUse.text = TextData.Get(432605)
        this.m_Btn_Select:SetEnable()
    else
        this.m_TMP_CanUse.text = TextData.Get(432603) 
        this.m_Btn_Select:SetEnable()
    end
end

---根據資料 將所有頭像分頁 需要的特效加上去
function RoleAttribute_Page_Avatar.AssignAllEffectToHeadAndFrame()
    for i = 1, table.Count(this.m_ListTable) do
        for j = 1, table.Count(this.m_ListTable[i].m_ImageIndex) do
         
            if this.m_ListTable[i].m_ImageIndex[j].m_EffectID ~=nil then

                    EffectMgr.PlayEffectWithPos(EEffectType.UI, this.m_ListTable[i].m_ImageIndex[j].m_EffectID, this.m_ListTable[i].m_ImageIndex[j].m_gameobject.transform
                             , Vector3(0,0,0),false, function(iHashCode)  
                                
                                local _TempObj =EffectMgr.GetEffectObjByHashCode(iHashCode)
                                local _IsAnimator = EffectMgr.IsEffectAnimator(iHashCode)
                                if _TempObj~= nil then
                                     _TempObj.transform:SetParent(this.m_ListTable[i].m_ImageIndex[j].m_gameobject.transform)
                                    this.m_ListTable[i].m_ImageIndex[j].m_HashCode = iHashCode
                                    _TempObj.transform.localPosition = Vector3(0,0,0)

                                    ---如果是animator需要調整大小
                                    if _IsAnimator then
                                        _TempObj.transform.localScale = Vector3(1,1,1)
                                    
                                    end
                                end
                            end)
            end 
        end
     end
    
end

---回收所有在頭像分頁用到的特效物件
function RoleAttribute_Page_Avatar.RecycleAllEffectToHeadAndFrame()
    for i = 1, table.Count(this.m_ListTable) do
        for j = 1, table.Count(this.m_ListTable[i].m_ImageIndex) do
         
            if this.m_ListTable[i].m_ImageIndex[j].m_EffectID ~=nil then
                EffectMgr.ReturnEffect(this.m_ListTable[i].m_ImageIndex[j].m_HashCode) 
            end 
        end
     end
end

--- 載入圖片並記錄特效ID
---@param iiRowIdx number 取資料使用的index
---@param iEAvatarListDataType EAvatarListDataType 檔案類型
---@param iHeadFrameObj GameObject 要被換圖片的物件
function RoleAttribute_Page_Avatar.LoadSpriteAndWriteEffectID(iRowIdx,iEAvatarListDataType,iHeadFrameObj)

    ---橫排中 所有物件各自填入圖片 紀錄特效訊息
    for i = 1, _AmountOfRow do
        if this.m_ListTable[iRowIdx].m_ImageIndex[i] ~=nil then

            ---把要填入圖片的ImageComponent加到記錄列表 方便後續替換圖片
            this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Image = iHeadFrameObj.m_ImageSet[i].m_Image
            this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Btn = iHeadFrameObj.m_ImageSet[i].m_Btn

            iHeadFrameObj.m_ImageSet[i].m_Image_HeadFrame:SetActive(true)

            iHeadFrameObj.m_ImageSet[i].m_FlowNumber =  this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx 
            ---取得頭像/頭框資訊
            local _HeadIconData

            if iEAvatarListDataType == EAvatarListDataType.HeadImage  then
                _HeadIconData = HeadIconData.GetHeadIconDataByIdx(this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx)
                table.insert(m_HeadBtns,iHeadFrameObj.m_ImageSet[i])
            elseif iEAvatarListDataType == EAvatarListDataType.FrameImage  then
                _HeadIconData = HeadFrameData.GetHeadFrameDataByIdx(this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx)
                table.insert(m_FrameBtns,iHeadFrameObj.m_ImageSet[i])
            end
            
            ---載入圖片
            SpriteMgr.Load( _HeadIconData.m_2DImageName, function(iSprite)
                iHeadFrameObj.m_ImageSet[i].m_Image.sprite = iSprite
                end)
                ---各圖片的按鍵功能(開啟說明面板)
                Button.AddListener(iHeadFrameObj.m_ImageSet[i].m_Image_HeadFrame, EventTriggerType.PointerClick,
                function()
                    RoleAttribute_Page_Avatar.ButtonClick(iEAvatarListDataType,this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx,iHeadFrameObj.m_ImageSet[i].m_Image.gameObject)
                end)

            --紀錄特效物件 如果有的話
            if _HeadIconData.m_Shader and _HeadIconData.m_Shader~="" then
                
                local _EffectID = tonumber(GString.GetNumberOnlyByString(_HeadIconData.m_Shader)[1])

                this.m_ListTable[iRowIdx].m_ImageIndex[i].m_EffectID = _EffectID
                this.m_ListTable[iRowIdx].m_ImageIndex[i].m_gameobject = iHeadFrameObj.m_ImageSet[i].m_Image.gameObject
                
            end

            ---檢查這個圖片是不是在被使用中 是的話紀錄使用框數字
            local _IsUsing = RoleAttribute_Page_Avatar.CheckHeadFrameIsUsing(iEAvatarListDataType ,this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx)
            
            if _IsUsing and iEAvatarListDataType == EAvatarListDataType.HeadImage  then
                m_CurrentHeadIcon = this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx
            elseif _IsUsing and iEAvatarListDataType == EAvatarListDataType.FrameImage  then
                m_CurrentHeadFrame = this.m_ListTable[iRowIdx].m_ImageIndex[i].m_Idx
            end
            
        else
            iHeadFrameObj.m_ImageSet[i].m_Image_HeadFrame:SetActive(false)
        end
   end

end

---檢查頭像/頭框 是否正在使用中(server紀錄的才是使用中的圖片)
---@param iEAvatarListDataType EAvatarListDataType 檢查的是頭像/頭框
---@param iIndex number 檢查的編號是哪一個
function RoleAttribute_Page_Avatar.CheckHeadFrameIsUsing(iEAvatarListDataType,iIndex)
    
    local _IsUsing = false
    if iEAvatarListDataType == EAvatarListDataType.HeadImage  then
        if PlayerData.m_LoginPlayerData.m_HeadIconID_Server == iIndex then
            _IsUsing = true
        end
    elseif iEAvatarListDataType == EAvatarListDataType.FrameImage  then
        if PlayerData.m_LoginPlayerData.m_FrameIconID_Sever == iIndex then
            _IsUsing = true
        end
    end

    return _IsUsing
end


---設定選擇框/目前使用框 提示
function RoleAttribute_Page_Avatar.AssignHintOutLine()

    local _CurrentHeadObj = RoleAttribute_Page_Avatar.GetSpecificAvatarImageGameObject(EAvatarListDataType.HeadImage,m_CurrentHeadIcon) 
    local _CurrentFrameObj = RoleAttribute_Page_Avatar.GetSpecificAvatarImageGameObject(EAvatarListDataType.FrameImage,m_CurrentHeadFrame) 
    local _SelectedHeadObj = RoleAttribute_Page_Avatar.GetSpecificAvatarImageGameObject(EAvatarListDataType.HeadImage,m_SelectedHeadIcon) 
    local _SelectedFrameObj = RoleAttribute_Page_Avatar.GetSpecificAvatarImageGameObject(EAvatarListDataType.FrameImage,m_SelectedHeadFrame) 

    ---按鍵組態對應
    --- 1 > 玩家目前使用的
    --- 2 > 玩家目前點擊看詳細資訊的
    --- 3 > 玩家目前沒有權限使用的
    --- 0 > 有權限 不在使用中也不在資訊檢視中的

    for key,value in pairs(m_HeadBtns) do
        local _IsQualified = HeadIconData.CheckQualified(value.m_FlowNumber)
        if value.m_Btn == _CurrentHeadObj then
            value.m_Btn:ChangeStateTransitionGroup(1)
        elseif value.m_Btn == _SelectedHeadObj then
            value.m_Btn:ChangeStateTransitionGroup(2)
        elseif _IsQualified == false then
            value.m_Btn:ChangeStateTransitionGroup(3)
        else
            value.m_Btn:ChangeStateTransitionGroup(0)
        end
        value.m_Btn:SetSelect(false)
    end

    for key,value in pairs(m_FrameBtns) do
        local _IsQualified = HeadFrameData.CheckQualified(value.m_FlowNumber)
        if value.m_Btn == _CurrentFrameObj then
            value.m_Btn:ChangeStateTransitionGroup(1)
        elseif value.m_Btn == _SelectedFrameObj then
            value.m_Btn:ChangeStateTransitionGroup(2)
        elseif _IsQualified == false then
            value.m_Btn:ChangeStateTransitionGroup(3)
        else
            value.m_Btn:ChangeStateTransitionGroup(0)
        end
        value.m_Btn:SetSelect(false)
    end
        
end

---取得特定的頭像/頭框的物件
---@param iEAvatarListDataType EAvatarListDataType 頭像介面資料清單類別
---@param iIndex number 串檔的流水號
---@return gameObject 用來放頭像/頭框圖片的物件
function RoleAttribute_Page_Avatar.GetSpecificAvatarImageGameObject(iEAvatarListDataType,iIndex)
    
    if iIndex == 0 or iIndex == nil then
        return nil
    end

    for k ,v in pairs(this.m_ListTable) do

        if v.m_DataType == iEAvatarListDataType then
            
            for k2 ,v2 in pairs(v.m_ImageIndex) do
                if v2.m_Idx == iIndex then
                    return v2.m_Btn
                end
            end
        end
    end
end

---取得特定的頭像/頭框的物件
---@param iParentObj GameObject 要被掛物件的父物件
---@param iChildObj GameObject 要拿來掛的子物件
function RoleAttribute_Page_Avatar.SetHint(iParentObj,iChildObj)
    
    if iParentObj ~= nil and iChildObj ~= nil then
        iChildObj.transform:SetParent(iParentObj.transform)
        iChildObj.transform.localPosition =Vector3(0,0,0)
        iChildObj.transform.localScale =Vector3(1,1,1)
        iChildObj:SetActive(true)
    elseif iParentObj == nil and iChildObj ~= nil then
        iChildObj:SetActive(false)
    end
end

---更新目前使用的  頭像/頭框
---@param iHeadNumber number 目前使用的頭像流水號
---@param iFrameNumber number 目前使用的頭框流水號
function RoleAttribute_Page_Avatar.SetCurrentHeadFrameNumber(iHeadNumber,iFrameNumber)

    if iHeadNumber ~= 0 and iHeadNumber~= nil then
        m_CurrentHeadIcon = iHeadNumber
    end

    if iFrameNumber ~= 0 and iFrameNumber~= nil then
        m_CurrentHeadFrame = iFrameNumber
    end

    RoleAttribute_Page_Avatar.AssignHintOutLine()
end