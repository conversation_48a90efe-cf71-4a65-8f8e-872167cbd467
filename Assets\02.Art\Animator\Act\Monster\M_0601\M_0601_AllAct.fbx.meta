fileFormatVersion: 2
guid: 3801710a29aaf3942bc20badb65db305
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Armature
  - first:
      1: 100002
    second: B_leg_L.1
  - first:
      1: 100004
    second: B_leg_L.2
  - first:
      1: 100006
    second: B_leg_L.3
  - first:
      1: 100008
    second: B_leg_L.4
  - first:
      1: 100010
    second: B_leg_L.4_end
  - first:
      1: 100012
    second: B_leg_R.1
  - first:
      1: 100014
    second: B_leg_R.2
  - first:
      1: 100016
    second: B_leg_R.3
  - first:
      1: 100018
    second: B_leg_R.4
  - first:
      1: 100020
    second: B_leg_R.4_end
  - first:
      1: 100022
    second: BM_leg_L.1
  - first:
      1: 100024
    second: BM_leg_L.2
  - first:
      1: 100026
    second: BM_leg_L.3
  - first:
      1: 100028
    second: BM_leg_L.4
  - first:
      1: 100030
    second: BM_leg_L.4_end
  - first:
      1: 100032
    second: BM_leg_R.1
  - first:
      1: 100034
    second: BM_leg_R.2
  - first:
      1: 100036
    second: BM_leg_R.3
  - first:
      1: 100038
    second: BM_leg_R.4
  - first:
      1: 100040
    second: BM_leg_R.4_end
  - first:
      1: 100042
    second: body.001
  - first:
      1: 100044
    second: body.002
  - first:
      1: 100046
    second: F_leg_L.1
  - first:
      1: 100048
    second: F_leg_L.2
  - first:
      1: 100050
    second: F_leg_L.3
  - first:
      1: 100052
    second: F_leg_L.4
  - first:
      1: 100054
    second: F_leg_L.4_end
  - first:
      1: 100056
    second: F_leg_R.1
  - first:
      1: 100058
    second: F_leg_R.2
  - first:
      1: 100060
    second: F_leg_R.3
  - first:
      1: 100062
    second: F_leg_R.4
  - first:
      1: 100064
    second: F_leg_R.4_end
  - first:
      1: 100066
    second: FM_leg_L.1
  - first:
      1: 100068
    second: FM_leg_L.2
  - first:
      1: 100070
    second: FM_leg_L.3
  - first:
      1: 100072
    second: FM_leg_L.4
  - first:
      1: 100074
    second: FM_leg_L.4_end
  - first:
      1: 100076
    second: FM_leg_R.1
  - first:
      1: 100078
    second: FM_leg_R.2
  - first:
      1: 100080
    second: FM_leg_R.3
  - first:
      1: 100082
    second: FM_leg_R.4
  - first:
      1: 100084
    second: FM_leg_R.4_end
  - first:
      1: 100086
    second: head
  - first:
      1: 100088
    second: Head_LP
  - first:
      1: 100090
    second: IK-B_leg_L
  - first:
      1: 100092
    second: IK-B_leg_L_end
  - first:
      1: 100094
    second: IK-B_leg_R
  - first:
      1: 100096
    second: IK-B_leg_R_end
  - first:
      1: 100098
    second: IK-BM_leg_L
  - first:
      1: 100100
    second: IK-BM_leg_L_end
  - first:
      1: 100102
    second: IK-BM_leg_R
  - first:
      1: 100104
    second: IK-BM_leg_R_end
  - first:
      1: 100106
    second: IK-F_leg_L
  - first:
      1: 100108
    second: IK-F_leg_L_end
  - first:
      1: 100110
    second: IK-F_leg_R
  - first:
      1: 100112
    second: IK-F_leg_R_end
  - first:
      1: 100114
    second: IK-FM_leg_L
  - first:
      1: 100116
    second: IK-FM_leg_L_end
  - first:
      1: 100118
    second: IK-FM_leg_R
  - first:
      1: 100120
    second: IK-FM_leg_R_end
  - first:
      1: 100122
    second: jaw_L.1
  - first:
      1: 100124
    second: jaw_L.2
  - first:
      1: 100126
    second: jaw_L.2_end
  - first:
      1: 100128
    second: jaw_R.1
  - first:
      1: 100130
    second: jaw_R.2
  - first:
      1: 100132
    second: jaw_R.2_end
  - first:
      1: 100134
    second: Leg_LP.001
  - first:
      1: 100136
    second: Leg_LP.002
  - first:
      1: 100138
    second: Leg_LP.003
  - first:
      1: 100140
    second: Leg_LP.004
  - first:
      1: 100142
    second: Leg_LP.005
  - first:
      1: 100144
    second: Leg_LP.006
  - first:
      1: 100146
    second: Leg_LP.007
  - first:
      1: 100148
    second: Leg_LP.008
  - first:
      1: 100150
    second: //RootNode
  - first:
      1: 100152
    second: mouth_L
  - first:
      1: 100154
    second: mouth_L_end
  - first:
      1: 100156
    second: mouth_R
  - first:
      1: 100158
    second: mouth_R_end
  - first:
      1: 100160
    second: paunch
  - first:
      1: 100162
    second: paunch_1_LP
  - first:
      1: 100164
    second: paunch_end
  - first:
      1: 100166
    second: paunch_LP
  - first:
      1: 100168
    second: Root
  - first:
      1: 100170
    second: Spine_LP
  - first:
      4: 400000
    second: Armature
  - first:
      4: 400002
    second: B_leg_L.1
  - first:
      4: 400004
    second: B_leg_L.2
  - first:
      4: 400006
    second: B_leg_L.3
  - first:
      4: 400008
    second: B_leg_L.4
  - first:
      4: 400010
    second: B_leg_L.4_end
  - first:
      4: 400012
    second: B_leg_R.1
  - first:
      4: 400014
    second: B_leg_R.2
  - first:
      4: 400016
    second: B_leg_R.3
  - first:
      4: 400018
    second: B_leg_R.4
  - first:
      4: 400020
    second: B_leg_R.4_end
  - first:
      4: 400022
    second: BM_leg_L.1
  - first:
      4: 400024
    second: BM_leg_L.2
  - first:
      4: 400026
    second: BM_leg_L.3
  - first:
      4: 400028
    second: BM_leg_L.4
  - first:
      4: 400030
    second: BM_leg_L.4_end
  - first:
      4: 400032
    second: BM_leg_R.1
  - first:
      4: 400034
    second: BM_leg_R.2
  - first:
      4: 400036
    second: BM_leg_R.3
  - first:
      4: 400038
    second: BM_leg_R.4
  - first:
      4: 400040
    second: BM_leg_R.4_end
  - first:
      4: 400042
    second: body.001
  - first:
      4: 400044
    second: body.002
  - first:
      4: 400046
    second: F_leg_L.1
  - first:
      4: 400048
    second: F_leg_L.2
  - first:
      4: 400050
    second: F_leg_L.3
  - first:
      4: 400052
    second: F_leg_L.4
  - first:
      4: 400054
    second: F_leg_L.4_end
  - first:
      4: 400056
    second: F_leg_R.1
  - first:
      4: 400058
    second: F_leg_R.2
  - first:
      4: 400060
    second: F_leg_R.3
  - first:
      4: 400062
    second: F_leg_R.4
  - first:
      4: 400064
    second: F_leg_R.4_end
  - first:
      4: 400066
    second: FM_leg_L.1
  - first:
      4: 400068
    second: FM_leg_L.2
  - first:
      4: 400070
    second: FM_leg_L.3
  - first:
      4: 400072
    second: FM_leg_L.4
  - first:
      4: 400074
    second: FM_leg_L.4_end
  - first:
      4: 400076
    second: FM_leg_R.1
  - first:
      4: 400078
    second: FM_leg_R.2
  - first:
      4: 400080
    second: FM_leg_R.3
  - first:
      4: 400082
    second: FM_leg_R.4
  - first:
      4: 400084
    second: FM_leg_R.4_end
  - first:
      4: 400086
    second: head
  - first:
      4: 400088
    second: Head_LP
  - first:
      4: 400090
    second: IK-B_leg_L
  - first:
      4: 400092
    second: IK-B_leg_L_end
  - first:
      4: 400094
    second: IK-B_leg_R
  - first:
      4: 400096
    second: IK-B_leg_R_end
  - first:
      4: 400098
    second: IK-BM_leg_L
  - first:
      4: 400100
    second: IK-BM_leg_L_end
  - first:
      4: 400102
    second: IK-BM_leg_R
  - first:
      4: 400104
    second: IK-BM_leg_R_end
  - first:
      4: 400106
    second: IK-F_leg_L
  - first:
      4: 400108
    second: IK-F_leg_L_end
  - first:
      4: 400110
    second: IK-F_leg_R
  - first:
      4: 400112
    second: IK-F_leg_R_end
  - first:
      4: 400114
    second: IK-FM_leg_L
  - first:
      4: 400116
    second: IK-FM_leg_L_end
  - first:
      4: 400118
    second: IK-FM_leg_R
  - first:
      4: 400120
    second: IK-FM_leg_R_end
  - first:
      4: 400122
    second: jaw_L.1
  - first:
      4: 400124
    second: jaw_L.2
  - first:
      4: 400126
    second: jaw_L.2_end
  - first:
      4: 400128
    second: jaw_R.1
  - first:
      4: 400130
    second: jaw_R.2
  - first:
      4: 400132
    second: jaw_R.2_end
  - first:
      4: 400134
    second: Leg_LP.001
  - first:
      4: 400136
    second: Leg_LP.002
  - first:
      4: 400138
    second: Leg_LP.003
  - first:
      4: 400140
    second: Leg_LP.004
  - first:
      4: 400142
    second: Leg_LP.005
  - first:
      4: 400144
    second: Leg_LP.006
  - first:
      4: 400146
    second: Leg_LP.007
  - first:
      4: 400148
    second: Leg_LP.008
  - first:
      4: 400150
    second: //RootNode
  - first:
      4: 400152
    second: mouth_L
  - first:
      4: 400154
    second: mouth_L_end
  - first:
      4: 400156
    second: mouth_R
  - first:
      4: 400158
    second: mouth_R_end
  - first:
      4: 400160
    second: paunch
  - first:
      4: 400162
    second: paunch_1_LP
  - first:
      4: 400164
    second: paunch_end
  - first:
      4: 400166
    second: paunch_LP
  - first:
      4: 400168
    second: Root
  - first:
      4: 400170
    second: Spine_LP
  - first:
      21: 2100000
    second: Material
  - first:
      43: 4300000
    second: Head_LP
  - first:
      43: 4300002
    second: Spine_LP
  - first:
      43: 4300004
    second: Leg_LP.005
  - first:
      43: 4300006
    second: Leg_LP.007
  - first:
      43: 4300008
    second: Leg_LP.004
  - first:
      43: 4300010
    second: Leg_LP.003
  - first:
      43: 4300012
    second: Leg_LP.002
  - first:
      43: 4300014
    second: Leg_LP.001
  - first:
      43: 4300016
    second: Leg_LP.008
  - first:
      43: 4300018
    second: Leg_LP.006
  - first:
      43: 4300020
    second: paunch_1_LP
  - first:
      43: 4300022
    second: paunch_LP
  - first:
      74: 7400000
    second: M_0601_Attack_01
  - first:
      74: 7400002
    second: M_0601_Attack_02
  - first:
      74: 7400004
    second: M_0601_Fly_00
  - first:
      74: 7400006
    second: M_0601_Die_00
  - first:
      74: 7400008
    second: Armature|defense
  - first:
      74: 7400010
    second: Armature|descent
  - first:
      74: 7400012
    second: Armature|end_defense
  - first:
      74: 7400014
    second: M_0601_Hit_00
  - first:
      74: 7400016
    second: M_0601_Stun_00
  - first:
      74: 7400018
    second: M_0601_Idle_00
  - first:
      74: 7400020
    second: M_0601_Bidle_00
  - first:
      74: 7400022
    second: M_0601_BRun_00
  - first:
      74: 7400024
    second: Armature|Spider
  - first:
      74: 7400026
    second: M_0601_Expr_01
  - first:
      74: 7400028
    second: Armature|touching the ground
  - first:
      74: 7400030
    second: M_0601_Walk_00
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: Head_LP
  - first:
      137: 13700002
    second: Leg_LP.001
  - first:
      137: 13700004
    second: Leg_LP.002
  - first:
      137: 13700006
    second: Leg_LP.003
  - first:
      137: 13700008
    second: Leg_LP.004
  - first:
      137: 13700010
    second: Leg_LP.005
  - first:
      137: 13700012
    second: Leg_LP.006
  - first:
      137: 13700014
    second: Leg_LP.007
  - first:
      137: 13700016
    second: Leg_LP.008
  - first:
      137: 13700018
    second: paunch_1_LP
  - first:
      137: 13700020
    second: paunch_LP
  - first:
      137: 13700022
    second: Spine_LP
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0601_Attack_01
      takeName: Armature|Attack_1
      internalID: 0
      firstFrame: 14
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Attack_02
      takeName: Armature|Attack_2
      internalID: 0
      firstFrame: 19
      lastFrame: 37
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Fly_00
      takeName: Armature|Death_1
      internalID: 0
      firstFrame: 25
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Die_00
      takeName: Armature|Death_2
      internalID: 0
      firstFrame: 0
      lastFrame: 34
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Hit_00
      takeName: Armature|hit_1
      internalID: 0
      firstFrame: 0
      lastFrame: 11
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Stun_00
      takeName: Armature|hit_2
      internalID: 0
      firstFrame: 0
      lastFrame: 9
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Idle_00
      takeName: Armature|idel
      internalID: 0
      firstFrame: 0
      lastFrame: 80
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Bidle_00
      takeName: Armature|idel-batlle
      internalID: 0
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_BRun_00
      takeName: Armature|run
      internalID: 0
      firstFrame: 0
      lastFrame: 12
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Expr_01
      takeName: Armature|start_defense
      internalID: 0
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_Walk_00
      takeName: Armature|walk
      internalID: 0
      firstFrame: 0
      lastFrame: 28
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_FlyStart
      takeName: Armature|hit_1
      internalID: -1089299330321864996
      firstFrame: 0
      lastFrame: 6
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0601_FlyEnd
      takeName: Armature|hit_1
      internalID: 7022840644888724750
      firstFrame: 6
      lastFrame: 15
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 0.5
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.5
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 83712163c55b75243be75d3573539058,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
