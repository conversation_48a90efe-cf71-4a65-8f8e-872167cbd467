---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---場景讀取與釋放
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.5.3

require("Controller/FloorBlockController")
require("Logic/GearMgr")
require("Data/ObstacleData")

SceneMgr = {}
local this = SceneMgr

--region 場景讀取變數

---@class ESceneStatus 場景讀取進度
local ESceneStatus = {
	Unload = 0,
	Load = 1,
	SetObstacle = 2,
	SetTimeline = 3,
	SetFloorBlock = 4,
	SetGear = 5,
	SetAudio = 6,
	Complete = 7,
}

local SceneStatusFunc = {
	Unload = function() SceneMgr.UnloadSceneCor() end,
	Load = function() SceneMgr.LoadSceneCor() end,
	SetObstacle = function() SceneMgr.SetObstacleCor() end,
	SetTimeline = function() SceneMgr.SetTimelineCor() end,
	SetFloorBlock = function() SceneMgr.SetFloorBlockObjCor() end,
	SetGear = function() SceneMgr.SetGearCor() end,
	SetAudio = function() SceneMgr.SetAudioCor() end,
	Complete = function() SceneMgr.LoadSceneCompleteCor() end,
}

---是否顯示障礙點
this.m_IS_OBSTACLE_DEBUG = false

---是否正在過場
this.m_IsLoadingScene = false

---@type ESceneStatus 現在讀取狀態
local m_Status = nil

---@type thread 現在運行中的coroutine
local m_Thread_Current
local m_Thread_Telelport

---讀取進度
local m_Percentage = 0

---lua function 進度條顯示
local m_Func_Progress = nil

---lua function 讀取完成後回呼
local m_LoadCompleteFunc = nil

--載入的是否為同一場景
local m_IsLoadSameScene = false

--endregion

--region 場景資訊

---@type string 要讀取的場景名(AssetName)
local m_Loading_SceneName

---@type string 現在場景名(AssetName)
local m_Current_SceneName

---@type ushort 場景屬性表ID
local m_SceneAttrID_Cur = 0

---@type ushort 場景ID
local m_SceneID_Cur = nil

---@type ushort C端場景ID
local m_SceneID_Cid = nil

---@type ushort 機關檔ID
local m_GearPrefab_id = nil

---@type number 場景高度
local m_SceneHeight = nil

---@type table 算出來的場景高度
local m_SceneMapHeight = {}

---障礙點資料
local m_ObstacleData = {}

---@type GameObject Scene
local m_GObj_Scene = nil

---@type GameObject Timeline
local m_GObj_Timeline = nil

--- Timeline Prefab上的Script
---@type SceneTimeline
local m_SceneTimeLineData = nil

local m_Audio_TimeLineAudio = {}

---@type table GearItems
local m_Item_Gear = {}

---@type PVE_Data 副本資訊
local m_PVE_Data = nil

---地城開始時間
local _DungeonStart = 0

---@type number 當前副本是否無剩餘時間
local m_DungeonIsTimeOut = false

---@type number 副本已經過時間
local m_PassedTime = 0

---@type number 副本是否已完成
local m_IsDungeonPassed = false

---揚州城場景編號4001
---@type int
local SCENEID_YANGZHOU = 4001

---揚州城時光機永標210
---@type int
local STATICFLAG_YANGZHOU = 210
--endregion

---動畫場景角色
---@type ModelController
local m_TimelineModel

---傳入用傳送特效
local m_LoadAsTeleport = false
---傳出用傳送特效
local m_LeaveAsTeleport = false
---傳入用訊息
local m_TeleportMsg = 0
function SceneMgr.Init()
	FloorBlockController.Init()
	CameraMgr.Init()
	if Extension.IsEditor then
		SceneMgr.SetOBDebugPlane()
	end
end

--region Set
function SceneMgr.SetPVE_Data(iData)
	m_PVE_Data = iData
	m_DungeonIsTimeOut = false
	_DungeonStart = HEMTimeMgr.SystemTime()
	SceneMgr.SetDungeonPassed(false)
end

---設定副本剩餘秒數
---@param iSec number 來自server的副本剩餘秒數資訊
function SceneMgr.SetDungeonTime(iSec)

	local _DungeonTotalTime = SceneMgr.GetDungeonTotalTime()

	if iSec == _DungeonTotalTime then
		_DungeonStart = HEMTimeMgr.SystemTime()
	else
		_DungeonStart:AddSeconds(iSec - _DungeonTotalTime)
	end
end

function SceneMgr.SetDungeonPassed(iPassed)
	m_IsDungeonPassed = iPassed
end

local function SetTimeLineModel(iPlayerOnly)

	if m_TimelineModel == nil or m_TimelineModel.m_IsReleased or m_TimelineModel.m_AppearanceInfo.m_Gender ~= PlayerData.GetGender() or m_SceneID_Cid == CreateRoleSetting.m_CreateRoleSceneID then
		--晚點再放
		local _Release
		if m_TimelineModel then
			_Release = m_TimelineModel
		end

		local _playerAppear = {}
		for k,v in pairs(RoleMgr.m_RC_Player.AppearData) do
			_playerAppear[k] = v
		end
		_playerAppear.m_IsSelf = false
		m_TimelineModel = AppearanceMgr.New(_playerAppear, tostring(PlayerData.GetCardID()), true, RoleMgr.m_RC_Player.m_Enhance, Layer.RTMModel)
		if _Release then
			AppearanceMgr.Release(tostring(PlayerData.GetCardID()), _Release)
		end
	end

	if not iPlayerOnly then

		if m_GObj_Timeline and m_SceneTimeLineData then
			local _ReplaceList = m_SceneTimeLineData:GetReplaceModelCount()
			for i = 0, _ReplaceList.Count -1 do
				local _obj = _ReplaceList[i]
				local _AppearData = NPCAppearData:NewModelOnly(_obj.name)
				AppearanceMgr.New(_AppearData, _obj.name, false, nil, Layer.RTMModel, function(iMC)
					if not iMC.m_IsReleased then
						iMC.transform.position = _obj.transform.position
						iMC.transform.eulerAngles = _obj.transform.eulerAngles
						iMC:SetModelSize(_obj.transform.localScale.x)
						local _BaseBipedObjs = iMC:GetBaseBiped()
						for _,v in pairs(_BaseBipedObjs) do
							v:SetParent(_obj.transform)
						end
						--設定NPC的LOD不然會醜醜
						local _LODGroup = _obj:GetComponent(typeof(LODGroup))

						if _LODGroup then
							local _Lod = iMC.m_ModelObject:GetComponent(typeof(LODGroup))
							if _Lod then
								--D.Log("設置timeline 的LOD".. _obj.name)
								local _LODs = _Lod:GetLODs()
								_LODGroup:SetLODs(_LODs)
							end
							_LODGroup:RecalculateBounds()
						end
					end
				end)
			end
		end

	end

end

---設定場景水體反射
function SceneMgr.SetWaterReflectionActive()
	local _SceneRefObj = m_GObj_Scene:GetComponent("SceneObjectReferenceInjector")
	if _SceneRefObj then
		_SceneRefObj:SetWaterReflectionActive(SettingMgr.GetWaterReflection())

	end

end

--endregion

--region Get

---若非副本區間 重設副本資料
function SceneMgr.ResetPVEData(iSceneAttrID)
	if m_PVE_Data ~= nil and not (8000 <= iSceneAttrID and iSceneAttrID <= 8999) then
		---清空4-5送的資料
		m_PVE_Data = nil
	end
end

function SceneMgr.GetPVE_Kind()
	return m_PVE_Data ~= nil and m_PVE_Data.m_PVE_Kind or 0
end

---@return boolean 檢查此SceneID是否為副本場景
function SceneMgr.GetIsPVE(iSceneAttrID)
	if m_PVE_Data == nil then
		return false
	end

	local _SceneAttr = SceneAttributeData.Get( m_PVE_Data.m_SceneAttrID )
	return ((8000 <= iSceneAttrID and iSceneAttrID <= 8999) and _SceneAttr.m_SceneType ~= ESceneType.PVPDungeon)
end

function SceneMgr.GetPVESceneAttrData()
	return SceneAttributeData.Get(m_PVE_Data.m_SceneAttrID)
end

---@return boolean 檢查是不是在 PVP 副本場景
function SceneMgr.GetIsInPVPDungeon()
	local _SceneAttr = SceneAttributeData.Get( m_SceneID_Cur )
	return _SceneAttr:GetIsPVPDungeon()
end

---@return number
function SceneMgr.GetScenePVPType()
	local _SceneAttr = SceneAttributeData.Get( m_SceneID_Cur )
	if _SceneAttr then
		return _SceneAttr:GetPVPType()
	end
end

---取得副本總秒數上限
function SceneMgr.GetDungeonTotalTime()
	local _DungeonTotalTime = 0
	if m_PVE_Data then
		_DungeonTotalTime = m_PVE_Data.m_DungeonTotalTime
	end
	return _DungeonTotalTime
end

---取得副本剩餘秒數
function SceneMgr.GetDungeonTime()
	local _DungeonTotalTime = 0
	if m_PVE_Data then
		_DungeonTotalTime = m_PVE_Data.m_DungeonTotalTime
	end
	return _DungeonTotalTime - m_PassedTime
end

function SceneMgr.GetSceneExitType()
	local _SceneAttr = SceneAttributeData.Get( m_SceneID_Cur )
	if _SceneAttr then
		return _SceneAttr.m_OutBtnEnable
	end

	return 0
end

function SceneMgr.GetSceneHeight()
	return m_SceneHeight
end

---@return number height
function SceneMgr.GetSceneHeightByRay(iVec3)
	local _LayerMask = 16777216 --減少一些運算
	local _Flag, _Hit = Physics.Raycast(iVec3, Vector3.down, nil, MaxFloorRayCastLength, _LayerMask)
	return (_Flag and {_Hit.point.y} or {0})[1]
end

function SceneMgr.GetSceneMapHeight()
	return m_SceneMapHeight
end

function SceneMgr.GetSceneID()
	return m_SceneID_Cur
end

---C端場景ID
function SceneMgr.GetSceneCID()
	return m_SceneID_Cid
end

---表的場景ID
function SceneMgr.GetAttrSceneID()
	return m_SceneAttrID_Cur
end


function SceneMgr.GetGearItems(iGearID)
	return m_Item_Gear[tostring(iGearID)]
end

function SceneMgr.GetObstacleSize()
	if m_Status and m_Status < ESceneStatus.SetAudio then
		return nil
	end

	return m_ObstacleData.m_SizeX
end

function SceneMgr.GetObstacleData()
	if m_Status and m_Status < ESceneStatus.SetAudio then
		return nil
	end

    return m_ObstacleData
end

---@return boolean
function SceneMgr.CheckCanWalk(iPosX, iPosZ)
	if m_Status and m_Status < ESceneStatus.SetAudio or m_ObstacleData.m_Nodes == nil then
		return false
	end

	local _GridX = bit.rshift(iPosX, 6)
	local _GridY = bit.rshift(iPosZ, 6)

	local _Index = _GridY * m_ObstacleData.m_ByteSizeX + math.floor(_GridX / 8)
	if _Index >= 0 and _Index < m_ObstacleData.m_Nodes.Length then
		return bit.band(m_ObstacleData.m_Nodes[_Index], bit.lshift(1, math.modf(_GridX, 8))) == 0
	end
	return false
end

---取得前往目標場景位置
function SceneMgr.GetMapLinkCoordinate(iTargetSceneID)
    local _mapLink = nil
    local _mapCoord = nil
    local _CurrentMapLinkID = SceneAttributeData.Get(m_SceneID_Cur).m_MapLinkID
	if _CurrentMapLinkID == nil or _CurrentMapLinkID == 0 then
		return
	end
	_mapLink = MapLinkData.Get(_CurrentMapLinkID)
	if _mapLink then
		local _targetMapLinkID = SceneAttributeData.Get(iTargetSceneID).m_MapLinkID
		if _targetMapLinkID == nil or _targetMapLinkID == 0 then
			return
		end
		_mapCoord = _mapLink.m_mapCoordinates[_targetMapLinkID]
		local _ReturnVector3 = {}
		_ReturnVector3.x = _mapCoord.x / 100
		_ReturnVector3.y = 0
		_ReturnVector3.z = _mapCoord.y / 100
		return _ReturnVector3 --在這裡轉數值
	end
end

--endregion

--region 讀取場景

---@param iSceneAttrData SceneAttributeData 場景屬性表
---@param iCaptionTheaterID UInt16 轉場效果ID
---@param iProgressFunc function 更新進度條delegate
---@param iLoadCompleteFunc function 讀完場景後Callback
function SceneMgr.StartLoadSceneCor(iSceneAttrData, iCaptionTheaterID, iProgressFunc, iLoadCompleteFunc)
	this.m_IsLoadingScene = true

	---Loading時跑的函數
	local _OnLoadDone = function()
		Loading_Controller.SetDebugInfo("Loading Scene")
		if UIMgr.IsVisible(Login_Controller) then
			UIMgr.CloseToPreviousPage(Login_Controller)
		end
		MapMgr.Release()

		D.Log("[SceneMgr]開始換場景 AttrID: " .. tostring(iSceneAttrData.m_SceneID) .. " ID: " .. tostring(iSceneAttrData.m_OirginalSceneID), "#CF98FB")

		if m_Thread_Current ~= nil then
			D.LogError("仍在處理場景中 請稍後")
			return
		end

		if m_SceneAttrID_Cur == iSceneAttrData.m_SceneID then
			m_Status = ESceneStatus.SetGear
			m_Func_Progress = iProgressFunc
			m_LoadCompleteFunc = iLoadCompleteFunc
			SceneMgr.SetNextStatus()
			return
		end

		m_Loading_SceneName = "M" .. iSceneAttrData.m_PrefabSName
		m_SceneAttrID_Cur = iSceneAttrData.m_SceneID
		m_SceneID_Cid = iSceneAttrData.m_PrefabSName
		m_SceneHeight = iSceneAttrData.m_SceneHeight

		-- if m_Loading_SceneName ~= "M1002" and Extension.IsEditor then
		-- 	D.LogError(
		-- 		"[SceneMgr]還沒搬揚州城以外的場景(" .. m_Loading_SceneName .. ") 將強制開啟揚州城!"
		-- 	)
		-- 	m_Loading_SceneName = "M1002"
		-- 	m_SceneAttrID_Cur = 4001
		-- end

		m_Status = ESceneStatus.Complete
		m_Func_Progress = iProgressFunc
		m_LoadCompleteFunc = iLoadCompleteFunc

		SceneMgr.SetNextStatus()
	end

	---開始Load的函數
	local _StartLoadFunc = function()
		m_SceneID_Cur = iSceneAttrData.m_OirginalSceneID
		m_IsLoadSameScene = m_SceneID_Cid == iSceneAttrData.m_PrefabSName
		if not m_IsLoadSameScene then
			UIMgr.Open(UIBlackBase_Controller)
		end
		---創角場景 或 有轉場效果 不用開loading
		if m_IsLoadSameScene then
			--20240522 Were 不放掉場景跟timeline
			if m_SceneID_Cid == CreateRoleSetting.m_CreateRoleSceneID or m_SceneID_Cid == CreateRoleSetting.m_LabSceneID then
				UIMgr.OpenLoading(ELoadingOpenType.None, Main_Controller, _OnLoadDone)
			else
				--20241113 侑薰 同場景轉換播放水波紋特效
				CameraMgr.m_WaterWaveEffect:StartWaterWaveEffect(EffectSetting.m_WaterWaveEffectParam.m_xPos,
				EffectSetting.m_WaterWaveEffectParam.m_yPos, EffectSetting.m_WaterWaveEffectParam.m_TimeOffset)
				UIMgr.OpenLoading(ELoadingOpenType.Effect, Main_Controller, _OnLoadDone)
			end
		elseif iCaptionTheaterID > 0 and iCaptionTheaterID ~= CreateRoleSetting.m_CreateRoleTheater then
			UIMgr.OpenLoading(ELoadingOpenType.CaptionTheater, Main_Controller, iCaptionTheaterID, _OnLoadDone)
		elseif iCaptionTheaterID == CreateRoleSetting.m_CreateRoleTheater then
			UIMgr.OpenLoading(ELoadingOpenType.UseCG, Main_Controller, CreateRoleSetting.m_CreateRoleCG, "",0,_OnLoadDone,iCaptionTheaterID)
		else
			UIMgr.OpenLoading(ELoadingOpenType.Defult, Main_Controller, "", "", true,_OnLoadDone)
		end
	end

	---因為傳送離開想加特效，這邊要多流程(目前場景為空則不做)
	if this.IsLeaveAsTeleport() and RoleMgr.GetPlayerRC() and m_SceneID_Cur then
		m_Thread_Telelport = coroutine.start(function()
			--CameraMgr.TriggerCameraSwitchSP(ECMPlayerEffect.Teleport, true)

			this.m_teleportStartTime = HEMTimeMgr.time
			--FIXME: 還是會沒全關就開始播動作
			while HEMTimeMgr.time - this.m_teleportStartTime < EffectSetting.m_TeleportDuration do
				coroutine.yield()
			end
			RoleMgr.TeleportLeave(PlayerData.GetCardID(), _StartLoadFunc)
		end)
	elseif SceneMgr.GetPVE_Kind() == 4 and RoleMgr.GetPlayerRC() and m_SceneID_Cur then
		--特殊表現:習武副本前需要播動作
		m_Thread_Telelport = coroutine.start(function()
			this.m_teleportStartTime = HEMTimeMgr.time
			CameraMgr.DoSwitchSP(ECMPlayerEffect.LearnWugongTeleport, function()
				if m_Thread_Telelport ~= nil then
					coroutine.stop(m_Thread_Telelport)
					m_Thread_Telelport = nil
				end
				CameraMgr.DoWhiteOut(true)
				_StartLoadFunc()

			end)

			while HEMTimeMgr.time - this.m_teleportStartTime < EffectSetting.m_LearnWugongWhiteOutDuration do
				coroutine.yield()
			end
			if m_Thread_Telelport then
				CameraMgr.DoWhiteOut(true)
			end

			while HEMTimeMgr.time - this.m_teleportStartTime < EffectSetting.m_LearnWugongDuration do
				coroutine.yield()
			end

			if m_Thread_Telelport then
				_StartLoadFunc()
			end

		end)
	else
		_StartLoadFunc()
	end

end

---登入重設
function SceneMgr.LoginSet()
	local _isLoadAsTeleport = true
	if RoleMgr.GetPlayerRC() then
		_isLoadAsTeleport = not RoleMgr.GetPlayerRC().m_StateController:IsDead()
	end
	---登入時想加傳送特效
	this.SetIsLoadAsTeleport(false, _isLoadAsTeleport)

end

--- 登出重設
function SceneMgr.LogOutReset()
	SceneMgr.SetIsLoadAsTeleport(false, false)
	m_Loading_SceneName = nil
	SceneMgr.UnloadSceneCor()
	-- 初始化參數
	m_Status = ESceneStatus.Unload
	m_SceneID_Cur = nil
	m_SceneAttrID_Cur = nil
	m_SceneHeight = nil
	m_Current_SceneName = nil
	m_SceneID_Cid = nil
	this.m_IsLoadingScene = false
end

--- 釋放場景,地塊 (該釋放的在這裡放一放)
function SceneMgr.UnloadSceneCor()
	if m_Current_SceneName ~= nil then
		-- 關閉環境音
		AudioMgr.ReturnSceneEnvironmentAudio()
		-- 釋放特效
		EffectMgr.ReleaseEffect()
		--先釋放機關
		GearMgr.ClearGear()

		if m_Loading_SceneName ~= m_Current_SceneName then
			D.Log("釋放場景 SceneName: " .. m_Current_SceneName)
			ResourceMgr.UnLoadScene(m_Current_SceneName)
			while ResourceMgr.UnLoadScene_Percentage ~= 1 do
				--釋放不跑條 先關閉
				--m_Percentage = ResourceMgr.UnLoadScene_Percentage * 1 / ESceneStatus.Complete
				coroutine.yield()
			end

			AppearanceMgr.ReleaseAll()

			--釋放Timeline
			if m_GObj_Timeline ~= nil then
				GameObject.Destroy(m_GObj_Timeline)
				ResourceMgr.Unload("T" .. m_SceneID_Cid)
				m_GObj_Timeline = nil
			end

			--釋放障礙點
			m_ObstacleData = {}
		else
			--20240522 Were 不放掉場景跟timeline
			m_Status = ESceneStatus.SetTimeline
			--創角會用到(當玩家選擇女角時)
			SetTimeLineModel(true)
		end

		m_SceneMapHeight = {}

		--釋放玩家
		RoleMgr.Release_All(false)
		--釋放NPC
		NPCMgr.ResetAll()
		--釋放寵物
		PetMgr.ResetAll(false)


		--消除該場景的buff資料
		BuffMgr.ClearSceneBuffData()

		--ModelController.ResetModelTabel()

		if RoleMgr.m_RC_Player then
			RoleMgr.m_RC_Player.m_MoveController:ResetArrivedFunc()
		end

		--釋放地塊
		FloorBlockController.ResetAll()

		--釋放Gear
		if not Extension.IsUnityObjectNull(m_Item_Gear.gameObject) then
			GameObject.Destroy(m_Item_Gear.gameObject)
			ResourceMgr.Unload("G" .. m_GearPrefab_id)
			table.Clear(m_Item_Gear)
		end
		m_GearPrefab_id = nil

	end

	if not m_IsLoadSameScene then
		-- 同場景轉換播水波紋時，做GC會卡頓
		-- GC
		D.Log("Garbege: " .. collectgarbage("count"))
		D.Log("Garbege: " .. collectgarbage("collect"))
		D.Log("Garbege: " .. collectgarbage("count"))
		ResourceMgr.GC(false)
	end
end

---讀取場景
function SceneMgr.LoadSceneCor()
	D.Log("讀取場景 SceneName: " .. m_Loading_SceneName)
	ResourceMgr.LoadScene(tostring(m_Loading_SceneName), LoadSceneMode.Additive)
	while ResourceMgr.LoadScene_Percentage ~= 1 do
		m_Percentage = ResourceMgr.LoadScene_Percentage * 1 / ESceneStatus.Complete
		coroutine.yield()
	end
	m_Current_SceneName = m_Loading_SceneName

	--m_GObj_Scene = GameObject.Find("S" .. m_SceneAttrID_Cur)
	m_GObj_Scene = GameObject.Find("S" .. m_SceneID_Cid)
	Extension.AddMissingComponent(m_GObj_Scene, typeof(Pathfinding.TileHandlerHelper))
	SceneMgr.SetWaterReflectionActive()
end

---設定場景音效
function SceneMgr.SetSceneAudio()
	AudioMgr.PlayBGM(AudioMgr.EMixerGroup.BGM, SceneAttributeData.Get(m_SceneAttrID_Cur).m_SceneMusic, true, true)
	local _SoundEnvironmentManager = m_GObj_Scene:GetComponent(typeof(SoundEnvironmentManager))

	-- 防呆 不是每個場景都會用到
	if _SoundEnvironmentManager ~= nil then
		for i = 0 , _SoundEnvironmentManager.m_EnvironmentSound.Count - 1 do
			AudioMgr.PlaySceneEnvironmentAudio(_SoundEnvironmentManager.m_EnvironmentSound[i])
		end
	end
end

---設定場景音效
function SceneMgr.SetAudioCor()
	SceneMgr.SetSceneAudio()
	SceneMgr.SetNextStatus()
end

---設定Timeline
function SceneMgr.SetTimelineCor()
	local _IsComplete = false
	local _SceneAttrData = SceneAttributeData.Get(m_SceneID_Cur)
	if _SceneAttrData.m_IsTimeLine then
		ResourceMgr.Load("T" .. _SceneAttrData.m_PrefabSName, function(iAsset)
			if iAsset ~= nil then
				m_GObj_Timeline = iAsset.gameObject
				m_GObj_Timeline.name = "T" .. _SceneAttrData.m_PrefabSName

				SceneMgr.SetSceneTimeLine()
			end
			_IsComplete = true
		end)
	else
		m_SceneTimeLineData = nil
		this.m_TimeLines = nil
		_IsComplete = true
	end

	while not _IsComplete do
		if m_Percentage < (ESceneStatus.SetTimeline + 1) / ESceneStatus.Complete - 0.1 then
			m_Percentage = m_Percentage + 0.1
		end
		coroutine.yield()
	end

	m_Percentage = (ESceneStatus.SetTimeline + 1) / ESceneStatus.Complete
end

---設定地塊
function SceneMgr.SetFloorBlockObjCor()
	local _BlockData = EventBlockData:GetEventBlockDataByIdx(m_SceneID_Cur)
	if _BlockData == nil then
		D.LogError("[Main] Missing EventBlockData SceneID: " .. m_SceneID_Cur)
		m_Percentage = (ESceneStatus.SetFloorBlock + 1) / ESceneStatus.Complete
		m_Func_Progress(m_Percentage)
		coroutine.stop(m_Thread_Current)
		return
	end

	for i = 1, _BlockData.m_BlockCount do
		FloorBlockController:New(_BlockData, i)
		m_Percentage = i / _BlockData.m_BlockCount * (ESceneStatus.SetFloorBlock + 1) / ESceneStatus.Complete
		if math.fmod(i, 10) == 0 then
			coroutine.yield()
		end
	end

	-- 測試中
	-- if m_IsLoadSameScene then
	-- 	SceneMgr.Send_004_001()
	-- end

	m_Percentage = (ESceneStatus.SetFloorBlock + 1) / ESceneStatus.Complete
end

---設定機關
function SceneMgr.SetGearCor()
	local _IsComplete = false
	local _SceneAttrData = SceneAttributeData.Get(m_SceneID_Cur)
	ResourceMgr.Load("G" .. _SceneAttrData.m_PrefabGName, function(iAsset)
		if iAsset ~= nil then
			m_Item_Gear.gameObject = iAsset.gameObject
			m_Item_Gear.gameObject.transform.localPosition = Vector3.zero
			m_Item_Gear.gameObject.transform.localScale = Vector3.one
			m_Item_Gear.gameObject.name = "G" .. _SceneAttrData.m_PrefabGName
			m_GearPrefab_id = m_Item_Gear.gameObject.name
			local _GearEnvironmentSound = m_Item_Gear.gameObject:GetComponent("SoundGearManager")
			if not Extension.IsUnityObjectNull(_GearEnvironmentSound) then
				for i = 0 , m_Item_Gear.gameObject.transform.childCount - 1 do
					local _GearObj = m_Item_Gear.gameObject.transform:GetChild(i)
					local _GearID = _GearObj.gameObject.name
					m_Item_Gear[_GearID] = {}
					m_Item_Gear[_GearID].gameObject = _GearObj.gameObject
					m_Item_Gear[_GearID].transform = _GearObj.gameObject.transform
					local _SoundGear = _GearEnvironmentSound:GetGearSound(_GearID)
					if _SoundGear then
						m_Item_Gear[_GearID].m_SoundGear = _SoundGear
					end
				end
			else
				D.Log("機關檔沒有音檔" .. m_Item_Gear.gameObject.name)

			end
		end

		_IsComplete = true
	end)


	while not _IsComplete do
		if m_Percentage < (ESceneStatus.SetGear + 1) / ESceneStatus.Complete - 0.1 then
			m_Percentage = m_Percentage + 0.1
		end
		coroutine.yield()
	end

	m_Percentage = (ESceneStatus.SetGear + 1) / ESceneStatus.Complete
end

function SceneMgr.SetObstacleCor()
	local _OBD = ObstacleData.New(m_SceneID_Cid)
	m_ObstacleData.m_SizeX = _OBD.m_NodeCountX
	m_ObstacleData.m_SizeZ = _OBD.m_NodeCountZ
	m_ObstacleData.m_Nodes = _OBD.m_ByteNodes
	if m_SceneID_Cur == CreateRoleSetting.m_CreateRoleSceneID and m_ObstacleData.m_Nodes == nil then
		m_ObstacleData.m_SizeX = 472
		m_ObstacleData.m_SizeZ = 472
		for i = 1, m_ObstacleData.m_SizeX * m_ObstacleData.m_SizeZ / 8 do
			m_ObstacleData.m_Nodes[i] = 0
		end
	end

	local _modInt, _modfloat  = math.modf(m_ObstacleData.m_SizeX / 8)

	m_ObstacleData.m_ByteSizeX = _modfloat == 0 and _modInt or (_modInt + 1)

	if Extension.IsEditor then
		SceneMgr.SetOBDebugPlanePixel()
	end

	for i = 1, m_ObstacleData.m_SizeX * m_ObstacleData.m_SizeZ do
		local _Index = math.floor(i/1000)
		if m_SceneMapHeight[_Index] == nil then
			m_SceneMapHeight[_Index] = {}
		end
		m_SceneMapHeight[_Index][i] = -1
	end

	m_Percentage = (ESceneStatus.SetObstacle + 1) / ESceneStatus.Complete
end

function SceneMgr.LoadSceneCompleteCor()
	D.Log("讀取場景" .. tostring(m_Current_SceneName) .. "完畢")

	-- 能量採集機不放在Gear流程，因為取高度那塊會出現取障礙點是nil的問題，導致礦機Y=0，一直被埋在地下
	-- 且本身不是機關，是寫死編號然後透過NPCData生，更偏向NPC一點
	GearMgr.NewEnergyMine(m_SceneID_Cur)

	--怕伺服器太快把NPC資料送過來，進入CreatNpc步驟，所以先 ResetCullingSphere 再送協定
	CullingGroupMgr.Inst:ResetCullingGroup()
	--if not m_IsLoadSameScene then
	--end
	--讀取濾鏡
	CameraMgr.LoadPostProcessProfile("PostProcessingProfile_"..string.sub(m_Current_SceneName,2),CameraMgr.SetDefaultPostProcess)
	--等待角色生完
	while not PlayerData.GetIsCreate() do
		coroutine.yield()
	end
	--Loading UI 已結束
	local _IsLoadingUIFinished = true --UIMgr.m_NowLoadingOpenType == ELoadingOpenType.Defult or UIMgr.m_NowLoadingOpenType == ELoadingOpenType.None

	--會停在 0.85 改 1
	if UIMgr.m_NowLoadingOpenType == ELoadingOpenType.Defult then
		Loading_Controller.SetSlider(1)
	elseif UIMgr.m_NowLoadingOpenType == ELoadingOpenType.CaptionTheater then
		_IsLoadingUIFinished = false
		while not _IsLoadingUIFinished do
			--Loading 類型是打字機時 等玩家看完
			_IsLoadingUIFinished = CaptionTheater_Controller.m_IsRunEndAndWaitLoadingClose
			coroutine.yield()
		end
	end

	if SceneAttributeData.Get(m_SceneAttrID_Cur).m_PerformanceUniformEnable == 1 then
		local m_MDC_Player = RoleMgr.Get(PlayerData.GetRoleID()):GetModelController()
		m_MDC_Player:UpdateUniform(m_MDC_Player.m_AppearanceInfo,true);
	end

	--如果這邊是6004的場景，而且玩家有動標2501
	--SceneMgr.CheckGuildScene()
	CameraMgr.DoWhiteOut(false)

	coroutine.stop(m_Thread_Current)
	m_Thread_Current = nil
	m_LoadCompleteFunc()
	m_Percentage = 0
end

function SceneMgr.SetNextStatus()
	local _Func = nil
	if m_Status and m_Status == ESceneStatus.Complete then
		m_Status = ESceneStatus.Unload
	else
		m_Status = m_Status + 1
	end
	_Func = SceneStatusFunc[table.GetKey(ESceneStatus, m_Status)]

	m_Thread_Current = coroutine.create(function()
		local _IsEnd, _ErrorMsg = pcall(_Func)
		if _ErrorMsg ~= nil or not _IsEnd then
			D.LogError("[SceneMgr] " .. _ErrorMsg, debug.traceback())
		end
	end)
end

--endregion

function SceneMgr.Update()
	if m_Thread_Current ~= nil then
		m_Func_Progress(m_Percentage)
		if coroutine.status(m_Thread_Current) == "suspended" then
			coroutine.resume(m_Thread_Current)
		elseif coroutine.status(m_Thread_Current) == "dead" then
			SceneMgr.SetNextStatus()
		end
	end
	if m_Thread_Telelport ~= nil then
		if coroutine.status(m_Thread_Telelport) == "suspended" then
			coroutine.resume(m_Thread_Telelport)
		elseif coroutine.status(m_Thread_Telelport) == "dead" then
			m_Thread_Telelport = nil
		end
	end

	--計算副本時間
	if m_PVE_Data and not m_IsDungeonPassed and this.GetSceneExitType() > 0 and this.GetSceneExitType() < 7 then
		local _timpSpan = HEMTimeMgr.SystemTime() - _DungeonStart
		m_PassedTime = _timpSpan.TotalSeconds

		if m_PassedTime > m_PVE_Data.m_DungeonTotalTime and not m_DungeonIsTimeOut then
			m_DungeonIsTimeOut = true
			local _SceneAttr = SceneAttributeData.Get( m_PVE_Data.m_SceneAttrID )

			if _SceneAttr.m_SceneType == ESceneType.LearnWugong then
				local _RewardData = CommonRewardMgr.GetRewardType(CommonRewardType.WinOrLose)
				_RewardData.m_UIType = 2
				CommonRewardMgr.AddNewReward(CommonRewardType.WinOrLose, _RewardData)
			end
		end
	end
end

---檢查此場景是否可以使用此物品
function SceneMgr.CanUseItem( iItemData )
	local _SceneAttrData = SceneAttributeData.Get( m_SceneID_Cur )
	--防呆
	if _SceneAttrData ~= nil then
		---限制物品種類1~5
		if table.Contains( _SceneAttrData.m_RestrictKindAy, iItemData.m_Type ) then
			return false
		end

		---限制物品1~10
		if table.Contains( _SceneAttrData.m_RestrictItemAy, iItemData.m_Idx ) then
			return false
		end
	end

	return true
end

---4-1通知遊戲伺服器已進入場景
function SceneMgr.Send_004_001()
	NetworkMgr.Send(4, 1, nil)
end

--region Debug
function SceneMgr.SetOBDebugPlane()
	this.m_GObj_ObstacleDebug = GameObject.New()
	this.m_GObj_ObstacleDebug.name = "Obstacle_Plane"
	this.m_GObj_ObstacleDebug.transform.localPosition = Vector3(0, 11.07, 0)
	this.m_Texture_ObstacleDebug = Texture2D.New(512, 512, UnityEngine.TextureFormat.RGBA32, false)
	this.m_Texture_ObstacleDebug.filterMode = UnityEngine.FilterMode.Point
	this.m_Mesh_ObstacleDebug = Extension.AddMissingComponent(this.m_GObj_ObstacleDebug, typeof(UnityEngine.MeshFilter))
	this.m_Mesh_ObstacleDebug.mesh = UnityEngine.Mesh.New()
	this.m_Mesh_ObstacleDebug.mesh.name = "Quad"
	this.m_Mesh_ObstacleDebug.mesh.vertices = {
		Vector3.zero,
		Vector3(0.64 * 512, 0, 0),
		Vector3(0.64 * 512, 0, 0.64 * 512),
		Vector3(0, 0, 0.64 * 512),
	}
	this.m_Mesh_ObstacleDebug.mesh.uv = { Vector2.zero, Vector2(1, 0), Vector2(1, 1), Vector2(0, 1) }
	local _Triangles = {}
	_Triangles[1] = 0
	_Triangles[2] = 1
	_Triangles[3] = 2
	_Triangles[4] = 0
	_Triangles[5] = 2
	_Triangles[6] = 3
	this.m_Mesh_ObstacleDebug.mesh.triangles = _Triangles
	this.m_Mesh_ObstacleDebug.mesh:RecalculateNormals()

	this.m_Renderer_ObstacleDebug = Extension.AddMissingComponent(
		this.m_GObj_ObstacleDebug,
		typeof(UnityEngine.MeshRenderer)
	)
	this.m_Renderer_ObstacleDebug.material = Material.New(Shader.Find("UI/Unlit/Transparent"))
	this.m_Renderer_ObstacleDebug.material.mainTexture = this.m_Texture_ObstacleDebug

	SceneMgr.SetOBDPlaneActive(this.m_IS_OBSTACLE_DEBUG)
end

function SceneMgr.SetOBDebugPlanePixel()
	for y = 0, m_ObstacleData.m_SizeZ - 1 do
		local _xIdx = 0
		for x = 0, m_ObstacleData.m_SizeX - 1 do
			if x == _xIdx then
				local _Index = math.floor(y * m_ObstacleData.m_ByteSizeX + x / 8)
				local _Pixel = _Index < m_ObstacleData.m_Nodes.Length and m_ObstacleData.m_Nodes[_Index] or 255
				local nx = x + 8 > m_ObstacleData.m_SizeX and math.fmod(m_ObstacleData.m_SizeX, 8) or 8
				for mx = 0, nx - 1 do
					local _IsShow = bit.band(_Pixel, bit.lshift(1, mx)) ~= 0
					-- D.Log("[" .. j + k .. "][" .. i .. "]" .. "IsShow: " .. tostring(_IsShow))
					this.m_Texture_ObstacleDebug:SetPixel(x + mx, y, _IsShow and Color(1, 0, 0, 1) or Color(0, 0, 0, 0))
				end
				_xIdx = _xIdx + 8
			end
		end
		this.m_Texture_ObstacleDebug:Apply()
	end
end

---設定障礙點顯示
function SceneMgr.SetOBDPlaneActive(iActive)
	if this.m_GObj_ObstacleDebug then
		this.m_IS_OBSTACLE_DEBUG = iActive
		this.m_GObj_ObstacleDebug:SetActive(iActive)
	end
end
--endregion

--region 場景TimeLine相關

---如果是實驗室場景並且動標2501
function SceneMgr.CheckGuildScene()
	if SceneMgr.GetSceneID() == CreateRoleSetting.m_LabSceneID then
		if PlayerData.IsHaveStaticFlag(CreateRoleSetting.m_CreateRoleFlag.Flag_ShowDoctor) then
			--寫死 實驗室 TimeLine 編號
			TimeLine_Model.m_TimeLine = 3
			SceneMgr.PlaySceneTimeLine(3)
			SceneMgr.SkipNowTimeLine()
		end
	end
end

---如果是揚州城場景並且永標210
function SceneMgr.CheckYangzhouCityScene()
	if SceneMgr.GetSceneID() == SCENEID_YANGZHOU then
		if PlayerData.IsHaveStaticFlag(STATICFLAG_YANGZHOU) then
			--寫死 揚州城 TimeLine 編號
			local _TimeLineNum = 0
			TimeLine_Model.m_TimeLine = _TimeLineNum
			SceneMgr.PlaySceneTimeLine(_TimeLineNum)
			SceneMgr.SkipNowTimeLine()
		end
	end
end

---綁定場景動作物件
function SceneMgr.SetSceneTimeLine()
	local _SceneRefObj = m_GObj_Scene:GetComponent("SceneObjectReferenceInjector")
	m_SceneTimeLineData = m_GObj_Timeline:GetComponent("SceneTimeline")
	if not m_SceneTimeLineData then
		return
	end
	this.m_TimeLines = m_SceneTimeLineData.TimelineList

	if m_SceneTimeLineData.mSubtitles then
		local _SubTitles = m_SceneTimeLineData.mSubtitles.texts

		for i = 0, _SubTitles.Length - 1 do
			if tonumber(_SubTitles[i]) then
				_SubTitles[i] = TextData.Get(tonumber(_SubTitles[i]))
			end
		end

	end

	local _anchorM, _anchorF = m_SceneTimeLineData:GetRoleDollies(nil,nil)
	local _anchor = {_anchorM, _anchorF}
	for i = 0, this.m_TimeLines.Count -1 do
		local _AnimationObj = _SceneRefObj:GetSceneObjectReference(i, true)
		local _ActivateObj = _SceneRefObj:GetSceneObjectReference(i, false)
		--setTimeLineData 把這些物件放到各自的TimeLineData裡面
		local _timeLineData = this.m_TimeLines[i]
		if _timeLineData then
			_timeLineData:BindTimeLineObject(_AnimationObj, _ActivateObj)
		end
	end

	--取timeLine音效
	table.Clear(m_Audio_TimeLineAudio)
	local _audio = m_GObj_Timeline.transform:Find("Audio")

	for i = 0, _audio.childCount - 1 do
		local _childAudio = _audio:GetChild(i).gameObject:GetComponent(typeof(AudioSource))
		if _childAudio then
			table.insert(m_Audio_TimeLineAudio,_childAudio)
		end
	end

	SetTimeLineModel()
end

---設定下次換場景參數
function SceneMgr.SetIsLoadAsTeleport(iTeleportLeave, iTeleportIn,iTeleportMsg)
	m_LeaveAsTeleport = iTeleportLeave
	m_LoadAsTeleport = iTeleportIn
	m_TeleportMsg =  iTeleportMsg~=nil and iTeleportMsg or 0
end

---是否本次離場需要特效
function SceneMgr.IsLeaveAsTeleport()
	return m_LeaveAsTeleport
end

---是否本次進場需要特效
function SceneMgr.IsLoadAsTeleport()
	return m_LoadAsTeleport
end
--- 是否本次進場要有訊息
function SceneMgr.GetTeleportMsg()
	return m_TeleportMsg
end
---timeline撥放完畢要把有的沒的關起來
local function DisableTimelineObject()
	if m_SceneTimeLineData then
		local _GObj = m_SceneTimeLineData.gameObject
		for i = 0, _GObj.transform.childCount - 1 do
			local _child = _GObj.transform:GetChild(i)
			_child.gameObject:SetActive(false)
		end
		---把確認跳過視窗關起來
		TimeLine_Controller.SetSkipWindowActive()
	end
end

---撥放中的timeline
local m_PlayingTimeLineIndex = -1
---是否timeline播放結束
local m_OnTimeLinePlayedDone = nil
---玩家人物置換錨點
local m_Anchor =
{
	[EPlayerGender.Man] = nil,
	[EPlayerGender.Woman] = nil
}


---撥放第幾個TimeLine
---@param iTimeLine number 第幾個timeLine
---@param iModelController ModelController 是否為創角模型
---@param iCompleteDelegate function 跑完timeLine後要做啥
function SceneMgr.PlaySceneTimeLine(iTimeLine, iModelController, iCompleteDelegate)
	m_PlayingTimeLineIndex = iTimeLine - 1
	if m_PlayingTimeLineIndex < 0 then
		m_PlayingTimeLineIndex = -1
		return
	end
	local _TimeLineCount = this.m_TimeLines.Count

	if m_PlayingTimeLineIndex + 1 > _TimeLineCount then
		m_PlayingTimeLineIndex = -1
		return
	end

	local _TimeLine = this.m_TimeLines[m_PlayingTimeLineIndex]
	local _MasterVocalSet = SettingMgr.m_AudioVolume.Master
	local _MC = iModelController and iModelController or m_TimelineModel

	---撥放完畢後執行
	m_OnTimeLinePlayedDone = function()
		---關閉timeline的條件: 接下來沒有能播的、這次的結束的就是自己
		local _IsCloseTimeline = m_PlayingTimeLineIndex == -1 or _TimeLine == this.m_TimeLines[m_PlayingTimeLineIndex]

		if (_TimeLine.type == TimelineType.PLAYERSELF or _TimeLine.type == TimelineType.CREATEROLE) and not _MC.m_IsReleased then
			_MC:ReturnBaseBiped(this.m_ModelFromModelController)
			_MC:SetBaseBipedActive(false)
		end
		if _TimeLine.type == TimelineType.SCENEGUIDE then
			HEMTimeMgr.DoFunctionDelay(0.3, function()
				--_TimeLine.gameObject:SetActive(false)
				DisableTimelineObject()
			end)
		else
			DisableTimelineObject()
		end
		if m_SceneID_Cur == CreateRoleSetting.m_CreateRoleSceneID
		   and _TimeLine.type == TimelineType.NOPLAYER then
			--創角不用開主相機
			if RoleCreate_Controller.m_CreateSys then
				RoleCreate_Controller.m_CreateSys.m_CamAnchor.gameObject:SetActive(true)
			end
		elseif _IsCloseTimeline then
			CameraMgr.SetMainCameraActive(true)
			CameraMgr.SetHUDCameraActive(true)
		end

		if _IsCloseTimeline then

			m_PlayingTimeLineIndex = -1

			--音效開回來
			AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, _MasterVocalSet)

			--選取開回來
			SelectMgr.SetActive(true)

			--怪異原因camera圖層被排在後面
			-- AppearanceMgr.m_Camera.gameObject:SetActive(false)
			-- AppearanceMgr.m_Camera.gameObject:SetActive(true)

			CinemachineMgr.Inst.m_CMBrain_Current = Camera.main

			if UIMgr.m_OpenedPlotAnimationUI == TimeLine_Controller then
				--這裡可能會讓PlaySceneTimeLine再執行一次
				UIMgr.CloseToPreviousPage(TimeLine_Controller)
			end
		end

		if iCompleteDelegate then
			iCompleteDelegate()
		end

	end

	local _anchorM, _anchorF = m_SceneTimeLineData:GetRoleDollies(nil,nil)
	m_Anchor[EPlayerGender.Man] = _anchorM
	m_Anchor[EPlayerGender.Woman] = _anchorF

	if _TimeLine then
		_TimeLine.gameObject:SetActive(true)
		--設定音效
		if SettingMgr.m_AudioSwitch.Master and SettingMgr.m_AudioSwitch.VOCAL then
			for k,v in pairs( m_Audio_TimeLineAudio) do
				v.volume = SettingMgr.m_AudioVolume.VOCAL * SettingMgr.m_AudioVolume.Master
			end
		else
			for k,v in pairs( m_Audio_TimeLineAudio) do
				v.volume = 0
			end
		end
		AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, 0)
		--選取關起來
		SelectMgr.SetActive(false)


		_TimeLine:Rewind()

		--相機都先關起來
		CameraMgr.SetMainCameraActive(false)
		--CameraMgr.SetUICameraActive(false)
		CameraMgr.SetHUDCameraActive(false)

		if _TimeLine.type ==  TimelineType.PLAYERSELF or _TimeLine.type ==  TimelineType.CREATEROLE then

			--m_TimelineModel:UpdateBodyModel()

			this.m_ModelFromModelController = _MC:GetBaseBiped()
			for _,v in pairs(this.m_ModelFromModelController) do
				v:SetParent(m_Anchor[_MC.m_AppearanceInfo.m_Gender])
				if string.gmatch(v.gameObject.name, "LOD") then
					v.localPosition = Vector3.zero
				end
			end

			_MC:SetBaseBipedActive(false)

			_TimeLine:PlaceActor(_MC.m_AppearanceInfo.m_Gender == EPlayerGender.Man)

			--設定玩家的LOD不然會醜醜
			local _LODGroup = m_Anchor[_MC.m_AppearanceInfo.m_Gender]:GetComponent(typeof(LODGroup))
			if _LODGroup then
				D.LogWarning("綁定LOD流程")

				local _Lod = _MC.m_ModelObject:GetComponent(typeof(LODGroup))
				if _Lod then
					D.LogWarning("取得並設置LOD")

					local _LODs = _Lod:GetLODs()
					_LODGroup:SetLODs(_LODs)
				else
					D.LogWarning("玩家模型沒有LOD")
				end
				_LODGroup:RecalculateBounds()
			else
				D.LogWarning("沒走到綁定LOD流程")

			end

		end

		local _director = _TimeLine.Director
		local _TimelineCamera = CMMgr.GetCameraBinding(_director)
		if _TimelineCamera then
			CinemachineMgr.Inst.m_CMBrain_Current = _TimelineCamera
		end

		_TimeLine:Play(m_OnTimeLinePlayedDone)
	end
end

---跳過目前的動畫(結束前0.1秒)
function SceneMgr.SkipNowTimeLine()
	--連續撥放 有可能會讓 m_PlayingTimeLineIndex = -1
	if m_PlayingTimeLineIndex < 0 and TimeLine_Model.m_TimeLine <= 0 then
		return
	end
	local _TimeLineID = m_PlayingTimeLineIndex >= 0 and  m_PlayingTimeLineIndex or TimeLine_Model.m_TimeLine - 1
	local _TimeLine = this.m_TimeLines[_TimeLineID]
	_TimeLine:Skip()
end
---取得當前場景名稱
function SceneMgr.GetCurrentSceneName()
	return TextData.Get(SceneAttributeData.Get(SceneMgr.GetSceneID()).m_SceneNameID)
end

---暫停當前動畫
function SceneMgr.PauseNowTimeLine()
	local _TimeLineID = m_PlayingTimeLineIndex >= 0 and  m_PlayingTimeLineIndex or TimeLine_Model.m_TimeLine - 1
	local _TimeLine = this.m_TimeLines[_TimeLineID]
	_TimeLine:Pause()
end

---繼續播放當前動畫
function SceneMgr.ResumeNowTimeLine()
	local _TimeLineID = m_PlayingTimeLineIndex >= 0 and  m_PlayingTimeLineIndex or TimeLine_Model.m_TimeLine - 1
	local _TimeLine = this.m_TimeLines[_TimeLineID]
	_TimeLine:Resume()
end
--endregion

function SceneMgr.OnUnrequire()
	if this.m_GObj_ObstacleDebug then
		GameObject.Destroy(this.m_GObj_ObstacleDebug)
		this.m_GObj_ObstacleDebug = nil
	end
	FloorBlockController.OnUnrequire()
	return true
end

return SceneMgr
