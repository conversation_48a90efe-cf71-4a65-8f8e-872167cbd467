---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---群組按鈕
---@class GroupButton
---author 鼎翰
---version 1.0
---since [HEM 2.0]
---date 2022.12.30
GroupButton = {}

---檢查個別的按鈕是否有初始化過
local function CheckIndexButton(iGroupButtonTable, iLuaIndex)
	if iGroupButtonTable[iLuaIndex] == nil then
		iGroupButtonTable[iLuaIndex] = Button.New(iGroupButtonTable.m_UIGroupBtnCtrl:GetButtonExByIndex(iLuaIndex).gameObject)
	end
	if iGroupButtonTable.m_RedPoints then
		iGroupButtonTable.m_RedPoints = {}
	end
	if iGroupButtonTable.m_RedPoints[iLuaIndex] == nil then
		iGroupButtonTable.m_RedPoints[iLuaIndex] = iGroupButtonTable[iLuaIndex].m_ButtonEx:GetRedPointImage()
	end
end

function GroupButton.New(iGObjGroupTab)
	local _Obj = type(iGObjGroupTab) == "userdata" and iGObjGroupTab or iGObjGroupTab.gameObject
	local _GroupButtons = {}
	_GroupButtons.gameObject = _Obj
	_GroupButtons.transform = _Obj.transform
		---取得群組按鈕控制的 Script
	---取 _Script_ButtonEx
	local _IsButtonExists, _UIGroupBtnCtrl = _GroupButtons.gameObject:TryGetComponent(typeof(UIGroupButtonCtrl), nil)
	--沒取到的話 加 Component
	if not _IsButtonExists then
		_UIGroupBtnCtrl = _GroupButtons.gameObject:AddComponent(typeof(UIGroupButtonCtrl))
	end
	_GroupButtons.m_UIGroupBtnCtrl = _UIGroupBtnCtrl
	_GroupButtons.m_RedPoints = {}
	for i = 1, _UIGroupBtnCtrl.ButtonCount do
        _GroupButtons[i] = Button.New(_UIGroupBtnCtrl:GetButtonExByIndex(i).gameObject)
		_GroupButtons.m_RedPoints[i] = _GroupButtons[i].m_ButtonEx:GetRedPointImage()
    end

	setmetatable(_GroupButtons, {__index = GroupButton})

	return _GroupButtons
end

---取得群組內所有按鈕的紅點
---@param iGObjGroupTab table GameObject 群組按鈕 GameObject 們
function GroupButton:GetRedPoint()
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end

    return self.m_RedPoints
end

---取得群組按鈕的數量
---@param iGObjGroupTab table GameObject 群組按鈕 GameObject 們
function GroupButton:GetCount()
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
    return self.m_UIGroupBtnCtrl.ButtonCount
end

---指定加群組內某按鈕 Event
---@param iLuaIndex int 要加的 Index
---@param iEventTriggerType EventTriggerType 要加的 TriggerType
---@param iEventDelegate LuaFunction 要加的 EventDelegate
function GroupButton:AddListenerByIndex(iLuaIndex, iEventTriggerType, iEventDelegate, iParam)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	CheckIndexButton(self, iLuaIndex)
	self[iLuaIndex]:AddListener(iEventTriggerType, iEventDelegate, iParam)
end

---重設所有按鈕選定
function GroupButton:ResetAllSelect()
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	self.m_UIGroupBtnCtrl:ResetAllButtonsState()
end

---群組按鈕 觸發某個按鈕 PointerClick
---@param iLuaIndex int 第幾個按鈕
function GroupButton:OnPointerClickByIndex(iLuaIndex)
	GroupButton.OnEventTriggerByIndex(self, iLuaIndex, EventTriggerType.PointerClick)
end

---群組按鈕 觸發某個按鈕 Event
---@param iLuaIndex int 第幾個按鈕
---@param iEventTriggerType EventTriggerType 觸發的 EventTriggerType
function GroupButton:OnEventTriggerByIndex(iLuaIndex, iEventTriggerType)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	if iEventTriggerType == EventTriggerType.PointerClick then
		CheckIndexButton(self, iLuaIndex)
		self[iLuaIndex]:OnEventTrigger(iEventTriggerType)
		self[iLuaIndex]:DoButtonStateTransition(iEventTriggerType)
	else
		EventTrigger.OnEventTrigger(iEventTriggerType)
	end
end

---群組按鈕 觸發某個按鈕 動態
---@param iLuaIndex int 第幾個按鈕
---@param iEventTriggerType EventTriggerType 觸發的 EventTriggerType
function GroupButton:DoButtonStateTransitionByIndex(iLuaIndex, iEventTriggerType)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	CheckIndexButton(self, iLuaIndex)
	self[iLuaIndex]:DoButtonStateTransition(iEventTriggerType)
end

---重新取得物件下所有的按鈕
function GroupButton:ReacquireAllButtonsUnderGroup()
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end

	self.m_UIGroupBtnCtrl:ReacquireAllButtonsUnderGroup()

	for i = 1, self.m_UIGroupBtnCtrl.ButtonCount do
        CheckIndexButton(self, i)
    end
end

---設定個別按鈕顯示
---@param iLuaIndex int 第幾個按鈕
---@param iIsActive bool 是否要顯示
function GroupButton:SetBtnActiveByIndex(iLuaIndex,iIsActive)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
    self.m_UIGroupBtnCtrl:GetButtonExByIndex(iLuaIndex).gameObject:SetActive(iIsActive)
end

---設定按鈕文字
---@param iLuaIndex int 第幾個按鈕
---@param iAudioID number 按鈕的按下的音效 ID
function GroupButton:SetAudioID(iLuaIndex,iAudioID)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	self.m_UIGroupBtnCtrl:GetButtonExByIndex(iLuaIndex).OnClickAudioID = iAudioID
end

---設定個別按鈕文字
---@param iLuaIndex int 第幾個按鈕
---@param iString string 按鈕的文字
function GroupButton:SetBtnTextByIndex(iLuaIndex,iString)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
    self.m_UIGroupBtnCtrl:GetButtonExByIndex(iLuaIndex):SetButtonText(iString)
end

---重設按鈕群組內所有按鈕狀態
---@param iGObjGroupTab GameObject 群組按鈕控制的 GameObject 們
function GroupButton:ResetAllButtonsState()
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	self.m_UIGroupBtnCtrl:ResetAllButtonsState()
end

---加按鈕進去 GroupBtnCtrl
function GroupButton:AddButtonToList(iGObj_NeedAddBtn)
	if type(self) == "userdata" or self.m_UIGroupBtnCtrl == nil then
		self = GroupButton.New(self)
	end
	self.m_UIGroupBtnCtrl:AddButtonToList(iGObj_NeedAddBtn)
end
