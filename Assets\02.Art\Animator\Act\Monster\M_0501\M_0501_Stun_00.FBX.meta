fileFormatVersion: 2
guid: 543aa71d0b889b145b442eff6c80605a
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: EagleBone001
    100002: EagleBone001 1
    100004: EagleBone002
    100006: EagleBone002 1
    100008: EagleHead
    100010: EagleHeadBone001
    100012: EagleLArm1
    100014: EagleLArm2
    100016: EagleLArmDigit01
    100018: EagleLArmDigit11
    100020: EagleLArmPalm
    100022: EagleLLeg1
    100024: EagleLLeg2
    100026: EagleLLegAnkle
    100028: EagleLLegDigit11
    100030: EagleLLegDigit12
    100032: EagleLLegDigit21
    100034: EagleLLegDigit22
    100036: EagleLLegDigit31
    100038: EagleLLegDigit32
    100040: EagleLLegDigit41
    100042: EagleLLegDigit42
    100044: EagleLLegPlatform
    100046: EaglePelvis
    100048: EagleRArm1
    100050: EagleRArm2
    100052: EagleRArmDigit01
    100054: EagleRArmDigit11
    100056: EagleRArmPalm
    100058: EagleRibcage
    100060: EagleRLeg1
    100062: EagleRLeg2
    100064: EagleRLegAnkle
    100066: EagleRLegDigit11
    100068: EagleRLegDigit12
    100070: EagleRLegDigit21
    100072: EagleRLegDigit22
    100074: EagleRLegDigit31
    100076: EagleRLegDigit32
    100078: EagleRLegDigit41
    100080: EagleRLegDigit42
    100082: EagleRLegPlatform
    100084: EagleSpine1
    100086: EagleSpine1 1
    100088: EagleSpine2
    100090: EagleSpine2 1
    100092: EagleTail1
    100094: EagleTail2
    100096: //RootNode
    100098: EagleLBone001
    100100: EagleLBone002
    100102: EagleRBone001
    100104: EagleRBone002
    400000: EagleBone001
    400002: EagleBone001 1
    400004: EagleBone002
    400006: EagleBone002 1
    400008: EagleHead
    400010: EagleHeadBone001
    400012: EagleLArm1
    400014: EagleLArm2
    400016: EagleLArmDigit01
    400018: EagleLArmDigit11
    400020: EagleLArmPalm
    400022: EagleLLeg1
    400024: EagleLLeg2
    400026: EagleLLegAnkle
    400028: EagleLLegDigit11
    400030: EagleLLegDigit12
    400032: EagleLLegDigit21
    400034: EagleLLegDigit22
    400036: EagleLLegDigit31
    400038: EagleLLegDigit32
    400040: EagleLLegDigit41
    400042: EagleLLegDigit42
    400044: EagleLLegPlatform
    400046: EaglePelvis
    400048: EagleRArm1
    400050: EagleRArm2
    400052: EagleRArmDigit01
    400054: EagleRArmDigit11
    400056: EagleRArmPalm
    400058: EagleRibcage
    400060: EagleRLeg1
    400062: EagleRLeg2
    400064: EagleRLegAnkle
    400066: EagleRLegDigit11
    400068: EagleRLegDigit12
    400070: EagleRLegDigit21
    400072: EagleRLegDigit22
    400074: EagleRLegDigit31
    400076: EagleRLegDigit32
    400078: EagleRLegDigit41
    400080: EagleRLegDigit42
    400082: EagleRLegPlatform
    400084: EagleSpine1
    400086: EagleSpine1 1
    400088: EagleSpine2
    400090: EagleSpine2 1
    400092: EagleTail1
    400094: EagleTail2
    400096: //RootNode
    400098: EagleLBone001
    400100: EagleLBone002
    400102: EagleRBone001
    400104: EagleRBone002
    7400000: M_0501_Stun_00
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0501_Stun_00
      takeName: M_0501_Stun_00
      firstFrame: 0
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 411fe58c08863d74598fdebd7e92668f,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
