---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---寵物控制器
---author WereHsu
---telephone #2896
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.11
PetMgr = {}
local this = PetMgr

---@type Transform
this.m_PetParent = nil

---@type Transform
this.m_Pet_Active = nil

---@type Transform
local m_Pet_Stored = nil

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 角色物件池
this.m_GObjPool_Pet = {}

---@type table<PlayerSID, RoleController>
this.m_Table_ActivePet = {}

---@type table<PetID,table<Index,RoleController>>
local m_Table_StorePet = {}

---@type table<number, RoleController>
this.m_ActiveCullingTable = {}

local PET_ROOT_NAME = "Pet_Root"
local PET_ACTIVE_NAME = "Pet_Active"
local PET_STORED_NAME = "Pet_Stored"
---寵物跟隨距離
local PET_FOLLOW_DISTANCE = 1.5
---跟太進會疊起來
local PET_DOWN_SPEED = 1

---金手指召喚大白狗
this.m_GoldFingerPetTest = false

--region 玩家資料區
---玩家登入時召喚中的寵物
local _LoginPetPlot = 0

---玩家召喚中的寵物
local _SummonedPlot = 0

---@type table 玩家擁有的寵物
this.m_MyPetsData = {}

---@type table 玩家寵物任務
this.m_MyPetMissionData = {}

--endregion

function PetMgr.Init()
    this.m_PetParent = GameObject.New(PET_ROOT_NAME).transform
    this.m_Pet_Active = GameObject.New(PET_ACTIVE_NAME).transform
    this.m_Pet_Active:SetParent(this.m_PetParent)
    m_Pet_Stored = GameObject.New(PET_STORED_NAME).transform
    m_Pet_Stored:SetParent(this.m_PetParent)

    this.m_GObjPool_Pet = Extension.GetGameObjPool(100, 0, PetMgr.RC_Reset, PetMgr.RC_Init)
    -- SelectMgr.SetOnTargetClicked(
    --     SelectMgr.ETargetType.NPC,
    --     function(iGObj)
    --         PetMgr.OnClick(iGObj)
    --     end
    -- )
    --PlayerData.Get(EPalyerData.Flags).SetNotify("MovingFlag_OnChange", this.RefreshCollectionHUD)
end

--把寵物存起來
local function StorePet(iSID)
    if this.m_Table_ActivePet[iSID] then
        --把要釋放的Pet存取到Store裡
        local _PetId = this.m_Table_ActivePet[iSID].m_NPCID
        local _RC = this.m_Table_ActivePet[iSID]
        if not m_Table_StorePet[_PetId] then
            m_Table_StorePet[_PetId] = {}
        end

        _RC.Time = System.DateTime.Now
        table.insert(m_Table_StorePet[_PetId], _RC)
        _RC.m_MoveController:StopMove(true)
        _RC:SetPosition(Vector3(0, 0, 0))
        if not Extension.IsUnityObjectNull(_RC.transform) then
            _RC.transform:SetParent(m_Pet_Stored)
            if ProjectMgr.IsEditor() then
                _RC.gameObject.name = "Stored Pet_" .. tostring(_PetId)
            end
        end

        this.m_Table_ActivePet[iSID] = nil
    end
end

--- 設定測試資訊
--- editor 會修改物件名稱，顯示使用/pool 中的物件數量
local function SetRootObjectName()
    if ProjectMgr.IsEditor() then
        this.m_Pet_Active.name =
            PET_ACTIVE_NAME .. "_" .. this.m_Pet_Active.childCount .. "_" .. table.Count(this.m_Table_ActivePet)
        local _TempCount = 0
        for key, value in pairs(m_Table_StorePet) do
            _TempCount = _TempCount + table.Count(value)
        end
        m_Pet_Stored.name = PET_STORED_NAME .. "_" .. m_Pet_Stored.childCount .. "_" .. table.Count(m_Table_StorePet).."_".._TempCount
    end
end

---用點跟面相取目前位置
local function CalculatePos(iV3, iEulerY)
    --翻過來
    iEulerY = iEulerY - 180
    --再往左右算60度以內
    iEulerY = iEulerY + Random.Range(-60, 60)

    local _NextRotation = Quaternion.New()
    _NextRotation:SetEuler(0, iEulerY, 0):Normalize()
    iV3 = iV3 + (_NextRotation * (Vector3.forward * PET_FOLLOW_DISTANCE))

    return iV3
end

--- 儲存某玩家的 Pet
---@param iPlayerSID Number
function PetMgr.RemoveActivePet(iPlayerSID)
    iPlayerSID = tostring(iPlayerSID)
    if this.m_Table_ActivePet[iPlayerSID] then
        StorePet(iPlayerSID)
    end

    SetRootObjectName()
end

--- 釋放寵物
function PetMgr.ReleasePet(iPetRC)
    if iPetRC.m_BoundingSphereIdx then
        CullingGroupMgr.Inst:ReleaseBounding(iPetRC.m_BoundingSphereIdx)
    end
    iPetRC:ReleaseHUD()
    --iRC:GetModelController():IsRelease()
    if not Extension.IsUnityObjectNull(iPetRC.gameObject) then
        this.m_GObjPool_Pet:Store(iPetRC.gameObject)
    end
    iPetRC = nil
    SetRootObjectName()
end

---切場景或登出，都把它放到store裡面在一起放掉
local function RemovePetModelController()
    for _petid,_RCTable in pairs(m_Table_StorePet) do
        for k,_rc in pairs(_RCTable) do
            this.ReleasePet(_rc)
            _RCTable[k] = nil
        end
        m_Table_StorePet[_petid] = nil
    end
end

---測測用的資料
function PetMgr.TestNewPet()
    if this.m_GoldFingerPetTest then
        local _PetsData = {}
        _PetsData.m_PetID = 1
        _PetsData.m_EnvolveLv = 1
        _PetsData.m_SID = tostring(PlayerData.GetCardID())

        PetMgr.NewPet(_PetsData)

    end
end

--- 取得 Pet RoleController
local function GetPetRC(iPetId, iCreateRoleData, iCreateDoneCallBack)
    ---@type RoleController
    local _Result
    if m_Table_StorePet[iPetId] and table.Count(m_Table_StorePet[iPetId]) > 0 then
        _Result = table.First(m_Table_StorePet[iPetId])
        table.remove(m_Table_StorePet[iPetId], 1)
        --為避免Dead影響回收SID須提前設置
        _Result.m_SID = iCreateRoleData.m_SID
        --死的要重設
        if _Result.m_StateController then
            _Result.m_StateController:ChangeState(EStateType.Idle)
        end
        _Result:SetRoleCreateData(iCreateRoleData)
        --抽出來的RC要做的更新
        _Result.m_MoveController:StopMove(true)
        _Result:SetPosition(iCreateRoleData.m_LocalPos)
        _Result:SetRotation(iCreateRoleData.m_LocalRot)
        _Result.m_HUDController.m_HUDData = iCreateRoleData.m_HUDData
        iCreateDoneCallBack(_Result)
        _Result:RefreshRelationShip()
        _Result:ModelFadeIn()
    else
        --從pool取出來
        local _GameObj = this.m_GObjPool_Pet:Get()

        --不該有東西在這裡面的(一起砍select應該沒問題)
        if _GameObj.transform.childCount > 0 then
            for i = 0 , _GameObj.transform.childCount -1 do
                local _child = _GameObj.transform:GetChild(i)
                if _child.name ~= "Select" then
                    local _childName = _child.name
                    _child.gameObject:Destroy()
                    ResourceMgr.Unload(_childName)
                end
            end
        end
        _Result = RoleController.New(_GameObj, iCreateRoleData, iCreateDoneCallBack)
    end

    _Result.m_Lv = iCreateRoleData.m_Lv
    _Result.gameObject.name = tostring(iCreateRoleData.m_SID) .. "_" .. iCreateRoleData.m_NPCAppearData.m_ModelName
    _Result.gameObject.layer = Layer.NPC
    _Result.gameObject.tag = "NPC"

    _Result:UpdateHUD(EHUD_UpdateChecker.Name)

    return _Result
end

---叫出新的寵物RC
function PetMgr.NewPet(iPetsData)
    local _OwnerSID = tostring(iPetsData.m_SID)
    if this.m_Table_ActivePet[_OwnerSID] and iPetsData.m_PetID ~= this.m_Table_ActivePet[_OwnerSID].m_NPCID then
        --收掉舊寵物
        PetMgr.RemoveActivePet(_OwnerSID)
    end
    local _inScenePetData = RoleCreateData:NewPet(iPetsData) -- InScenePetData:New(iPetsData)

    local _playerRC = RoleMgr.Get(_OwnerSID)
    if _playerRC then
        --取用場景玩家面相位置
        local _Pos = _playerRC.transform.position
        _Pos = CalculatePos(_Pos, _playerRC.m_ModelObject.transform.eulerAngles.y)
        _inScenePetData.m_LocalPos = _Pos
    else
        _inScenePetData.m_LocalPos = Vector3(0, -15, 0)

    end

    ---@param iRC RoleController
    local _doAfterCreateDone = function(iRC)
        if this.m_Table_ActivePet[_OwnerSID] then
            D.LogWarning("Pet還沒死透就收到重生: ".. this.m_Table_ActivePet[_OwnerSID].m_DebugIdentifyName)
            PetMgr.RemoveActivePet(_OwnerSID)
        end

        iRC.transform:SetParent(this.m_Pet_Active)
        this.m_Table_ActivePet[_OwnerSID] = iRC

        SetRootObjectName()

        -- 泡泡框
        iRC.m_TalkAction = {}
        -- 寵物專用 特殊發話類型
        iRC.m_TalkAction.m_TalkMode = EBubbleBoxMode.PetSpecial

        iRC.m_TalkAction.m_NormalTalkID =  _inScenePetData.m_TalkID_Idle
        iRC.m_TalkAction.m_FightTalkID =  _inScenePetData.m_TalkID_Fight

        local _DialogData = BubbleDialogData.New()
        _DialogData.m_Msg = TextData.Get(_inScenePetData.m_TalkID)
        _DialogData.m_NPCSID = iRC.m_SID

        --召喚要先喊
        BubbleMgr.ShowBubbleBox(_DialogData)

        --開始平常的
        BubbleMgr.StartBubble(iRC.m_SID)

        iRC:UpdatHUDHP(_inScenePetData.m_HUDData.m_MaxHp,_inScenePetData.m_HUDData.m_CurHp)

    end


    local _RC = GetPetRC(iPetsData.m_PetID, _inScenePetData, _doAfterCreateDone)-- RoleController.New(_GameObj, _inScenePetData, _doAfterCreateDone)
    _RC.m_CharacterController = Extension.AddMissingComponent(_RC.gameObject, typeof(CharacterController))
    _RC.m_CharacterController.radius = _inScenePetData:GetRadius()
    _RC.m_CharacterController.height = _inScenePetData:GetHeight()

end


--- 釋放所有寵物
function PetMgr.ResetAll(iNeedReleaseSelf)

    for key, value in pairs(this.m_Table_ActivePet) do
        --if iNeedReleaseSelf or tostring(key) ~= tostring(PlayerData.GetCardID()) then
            PetMgr.RemoveActivePet(key)
            this.m_Table_ActivePet[key] = nil
        --end
    end
    RemovePetModelController()

end


function PetMgr.Update()
    for k, v in pairs(this.m_Table_ActivePet) do
        v:Update()
    end
end

function PetMgr.LateUpdate()
    -- CullingGroupEvent 變動後更新
    for k, v in pairs(this.m_ActiveCullingTable) do
        if v:CullingUpdate() then
            this.m_ActiveCullingTable[k] = nil
        end
    end

    for k, v in pairs(this.m_Table_ActivePet) do
        if v.m_CurrentCulling and v.m_CurrentCulling < ECullingState.OUT_OF_RANGE then
            v:LateUpdate()
        end
    end
end

---登入時要做的事
function PetMgr.LoginSet()
    --送協定要求寵物資料
    SendProtocol_020._020_01()
end

function PetMgr.LoggingOutToDo()
    PetMgr.SetSummonedPetPlot(0)
    PetMgr.SetLoginPetPlot(0)
    PetMgr.ResetAll(true)
    this.m_MyPetsData = {}
    this.m_MyPetMissionData = {}
end

---直接用場景物件找到Pet的RoleController
function PetMgr.GetPetControllerGObj(iGObj)
    for key, value in pairs(this.m_Table_ActivePet) do
        if value.gameObject == iGObj then
            return value
        end
    end
end

---直接用場景物件找到Pet的RoleController
---@param iSID string
function PetMgr.GetPetController(iSID)
    iSID = tostring(iSID)
    --玩家ID十碼
    iSID = string.sub(iSID, 1, 10)
    return this.m_Table_ActivePet[iSID]
end

--雙擊
function PetMgr.OnClick(iGObj)
    local _RC = PetMgr.GetPetControllerGObj(iGObj)
    if _RC then
        _RC:OnClick()
    end
end

function PetMgr.RC_Init(iGObj)
    if iGObj ~= nil then
        iGObj.transform:SetParent(m_Pet_Stored)
        iGObj.transform.localPosition = Vector3.zero
        iGObj.transform.localScale = Vector3.one

        Extension.AddMissingComponent(iGObj, typeof(CharacterController))
    end
end

function PetMgr.RC_Reset(iGObj)
    if iGObj ~= nil then
        --iGObj:SetActive(false)
        iGObj.transform:SetParent(m_Pet_Stored)
    end
end

---透過起點、終點計算用向量計算終點位置
---@param iStartPos 原始起點
---@param iEndPos 原始終點
---@return Vector3
local function CalculateMoveEndPos(iStartPos, iEndPos)
    --玩家預期面向過來 Eular
    local _Vec3 = iEndPos - iStartPos
    local _FaceVal = 180/math.pi * math.atan2(_Vec3.x, _Vec3.z)

    iEndPos = CalculatePos(iEndPos, _FaceVal)

    return iEndPos
end

---寵物開始移動
---@param iStartPos Vector3 起始點Y為0的Vector3
---@param iEndPos Vector3 終點Y為0的Vector3
function PetMgr.SetPetMovement(iSID, iStartPos, iEndPos, iSpeed)
    iSID = tostring(iSID)
    if this.m_Table_ActivePet[iSID] ~= nil then
        --local _NowPos = this.m_Table_ActivePet[iSID].transform.position
        --if GFunction.DistanceWithoutHeight(_NowPos, iEndPos) > PET_FOLLOW_DISTANCE then
            -- local _StartPos = MoveMgr.GetFloorPosition(iStartPos)
            -- local _EndPos = MoveMgr.GetFloorPosition(iEndPos)
            -- _EndPos = CalculateMoveEndPos(_StartPos, _EndPos)

            -- this.m_Table_ActivePet[iSID]:OtherVectorMove(_EndPos, iSpeed)

            -- if this.m_Table_ActivePet[iSID].m_PlayerID then
            --     MoveMgr.DebugLog(("寵物開始移動 起點 x ="..iStartPos.x..", y ="..iStartPos.z))
            --     MoveMgr.DebugLog(("寵物開始移動 終點 x ="..iEndPos.x..", y ="..iEndPos.z))
            -- end
        this.SyncPetSpeed(iSID, iSpeed)
        --end

    end
end

-- ---直接設定寵物位置
-- function PetMgr.SetPetPosition(iSID, iEndPos)
--     iSID = tostring(iSID)
--     local _PetRC = this.m_Table_ActivePet[iSID]
--     if _PetRC ~= nil then
--         local _playerRC = RoleMgr.Get(iSID)
--         if _playerRC then
--             iEndPos = CalculatePos(iEndPos,  _playerRC.m_ModelObject.transform.eulerAngles.y)
--         end

--         --local _EndPos = MoveMgr.GetFloorPosition(iEndPos)
--         -- if not CullingGroupMgr.Inst:CheckBoundingSphereExist(_PetRC) then
--         --     _PetRC.m_BoundingSphereIdx = CullingGroupMgr.Inst:RegisterBoundingSphere(_PetRC, iEndPos, _PetRC.m_Radius)
--         --     --this.m_ActiveCullingTable[_PetRC.m_BoundingSphereIdx] = _PetRC
--         -- end

--         _PetRC:SetPosition(iEndPos)

--     end
-- end

---取消上次位移後，直接從目前位置到點
function PetMgr.SendPetMovement(iPlayerID, iEndPos)
    iPlayerID = tostring(iPlayerID)
    local _EndPos = iEndPos
    if iPlayerID ~= tostring(PlayerData.GetCardID()) then
        return
    end

    --戰鬥中的話要直接讓他跑過去
    if BattleMgr.m_IsAutoNormalBattle and BattleMgr.m_NowSkillData and BattleMgr.m_NowRangePos then
        --Vector3.GetTransformPosition(iEndPos, BattleMgr.m_Target.transform)
        _EndPos = {}
        Vector3.Copy(_EndPos, BattleMgr.m_NowRangePos)
    end

    local _PetRC = this.m_Table_ActivePet[iPlayerID]

    if _PetRC ~= nil then
        local _NowPos = _PetRC.transform.position
        --local _NowPath = _PetRC.m_MoveController.currentPath
        if GFunction.DistanceWithoutHeight(_NowPos, _EndPos) > PET_FOLLOW_DISTANCE then--and
           --(_NowPath == nil or #_NowPath <= 1) then
            Vector3.GetFloorPosition(_EndPos)
            _EndPos = CalculateMoveEndPos(_NowPos, _EndPos)
            _EndPos = GFunction.ScenePosToServerPos(_EndPos)
            SendProtocol_020._020_09(_EndPos.x, _EndPos.y)
        end

        setmetatable(_NowPos, nil)
    end
end

---取消上次位移後，直接從目前位置到點
function PetMgr.StopPetMovement(iPlayerID, iEndPos)
    iPlayerID = tostring(iPlayerID)
    local _PetRC = this.m_Table_ActivePet[iPlayerID]

    if _PetRC ~= nil then
        local _NowPos = _PetRC.transform.position
        --local _NowPath = _PetRC.m_MoveController.currentPath
        --if GFunction.DistanceWithoutHeight(_NowPos, iEndPos) > PET_FOLLOW_DISTANCE then--and
           --(_NowPath == nil or #_NowPath <= 1) then
        iEndPos:GetFloorPosition()
        --iEndPos = CalculateMoveEndPos(_NowPos, iEndPos)

        if _PetRC.m_CurrentCulling and _PetRC.m_CurrentCulling < ECullingState.OUT_OF_RANGE then
            _PetRC:MoveToTargetPos(nil, iEndPos, 42)
        else
            _PetRC:SetPosition(iEndPos)
        end
        if _PetRC.m_PlayerID then
            MoveMgr.DebugLog(("寵物結束移動 終點 x ="..iEndPos.x..", y ="..iEndPos.z))
        end
        --end
    end
end

---玩家速度變化時要一起變化寵物速度
function PetMgr.SyncPetSpeed(iPlayerID, iSpeed)
    iPlayerID = tostring(iPlayerID)
    local _PetRC = this.m_Table_ActivePet[iPlayerID]

    if _PetRC then
        _PetRC.m_MoveController:UpdateMoveSpeed(iSpeed - PET_DOWN_SPEED)
    end
end

---寵物同步玩家尋路
function PetMgr.SetPetPathFind(iPlayerID, iPath)
    iPlayerID = tostring(iPlayerID)
    local _PetRC = this.m_Table_ActivePet[iPlayerID]

    if _PetRC then
        local _PetPath = {}
        for i = 1, #iPath do
            if i > 1 then
                _PetPath[i] = CalculateMoveEndPos(iPath[i - 1], iPath[i])
            else
                _PetPath[i] = iPath[i]
            end
        end
        _PetRC.m_MoveController:StopMove(true)
        _PetRC.m_MoveController:OtherPathFind(_PetPath)
    end
end

---寵物停止攻擊(暫用)
function PetMgr.StopPetAttack(iPlayerID)
    iPlayerID = tostring(iPlayerID)
    local _PetRC = this.m_Table_ActivePet[iPlayerID]

    if _PetRC then
        _PetRC:GetModelController():SetBattleNormalState(false)
    end
end

---寵物演攻擊(暫用)
function PetMgr.SetPetAttack(iPetAction)

    iPetAction.PetOwnerId = tostring(iPetAction.PetOwnerId)
    local _PetRC = this.m_Table_ActivePet[iPetAction.PetOwnerId]

    if _PetRC then
        _PetRC:GetModelController():SetBattleNormalState(true)
    end

end

---寵物攻擊效果(暫用)
function PetMgr.SetPetAttackAffect(iPetAction)
    iPetAction.PetOwnerId = tostring(iPetAction.PetOwnerId)
    local _PetRC = this.m_Table_ActivePet[iPetAction.PetOwnerId]

    local _TargetRC
    if  iPetAction.TargetKind == 1 then
        _TargetRC = NPCMgr.GetNPCController(iPetAction.TargetID)
    elseif  iPetAction.TargetKind == 2 then
        _TargetRC = RoleMgr.Get(iPetAction.TargetID)
    end

    if _TargetRC and iPetAction.EffectValue > 0 then
        if _PetRC then
            _PetRC:GetModelController():SetAtk(1)
            _PetRC:SetLookAt(_TargetRC.transform.position)
        end

        _TargetRC:UpdatHUDHP(nil, iPetAction.TargetHp)
        --BattleText_Controller.SetHPBattleText(_TargetRC, iPetAction.EffectValue, iPetAction.TickValueKind)
        --暫用 未來應該會換成tick.anaylize
        BattleText_Controller.SetPetBattleText(_TargetRC, iPetAction.EffectValue, iPetAction.TickValueKind)
    end
    
end

--region 我的寵物資料區
--- 設定我的寵物資料 會以寵物資料 S 端寵物位置做 key
---@param table iPetsData 寵物資料表
function PetMgr.SetMyPetsData(iPetsData)

    for _key, _value in pairs(iPetsData) do

        this.m_MyPetsData[_value.m_PetSlot] = _value

    end

end

--- 設定新增得寵物資料 只會是單隻
---@param table iPetData
function PetMgr.SetMyPetData(iPetData)
    this.m_MyPetsData[iPetData.m_PetSlot] = iPetData
end

---玩家已召喚的寵物
function PetMgr.SetSummonedPetPlot(_PetPlot)
    if GoldFinger_Controller and GoldFinger_Controller.m_Toggle_SummonPlayerPet then
        GoldFinger_Controller.m_Toggle_SummonPlayerPet:SetIsOnWithoutNotify(_PetPlot > 0)
    end
    _SummonedPlot = _PetPlot
end

---取得目前召喚中的寵物
function PetMgr.GetSummonedPetPlot()
    return _SummonedPlot
end

---玩家已召喚的寵物
function PetMgr.SetLoginPetPlot(_PetPlot)
    _LoginPetPlot = _PetPlot
end

---跨場景再生寵物(含登入時的寵物)
function PetMgr.NewMyCrossScenePet()
    if _LoginPetPlot > 0 then
        SendProtocol_020._020_03(_LoginPetPlot, true)
        _LoginPetPlot = 0

    elseif _SummonedPlot > 0 then
        this.NewPet(this.m_MyPetsData[_SummonedPlot])
    end

end

--endregion

--- 取玩家的 PetDatam_PetID
function PetMgr.GetPlayerPetData()

    return this.m_MyPetsData

end

--- 用寵物在 Server 上的位置取寵物資料
function PetMgr.GetPlayerPetDataByPetPlot(iPetPlot)

    if(this.m_MyPetsData[iPetPlot] ~= nil) then
        return this.m_MyPetsData[iPetPlot]
    else
        return nil
    end

end

--- 設定寵物最愛資料
---@param number iSId 寵物 S 位置
---@param bool ibool 是不是最愛寵物
function PetMgr.SetPetLoveData(iSId, ibool)

    if(this.m_MyPetsData[iSId] == nil) then
        return
    end

    this.m_MyPetsData[iSId].m_IsPetLoved = ibool

end

--- 刪除寵物
---@param number iSId 寵物 S 位置
function PetMgr.DeletePetData(iSId)

    this.m_MyPetsData[iSId] = nil

end

--- 設定寵物派遣資料
---@param table iData 寵物資料
function PetMgr.SetPetMissionData(iData)

    local _MissionData = {}
    _MissionData.m_ServerData = iData
    _MissionData.m_BasicData = PetMissionData.GetPetMissionByIdx(iData.m_ID)
    table.insert(this.m_MyPetMissionData, _MissionData)

end

--- 清空MyPetMissionData
function PetMgr.ClearMyPetMission()
    this.m_MyPetMissionData = {}
end

--- 取得寵物派遣資料
---@return this.m_MyPetMissionData 寵物派遣資料
function PetMgr.GetPetMissionData()

    return this.m_MyPetMissionData

end

--- 寵物被招回或回收獎勵是寵物要改狀態
---@param number iMissionID 回收的任務 ID
function PetMgr.PetBackFromMission(iMissionID)

    for _k, _v in pairs(this.m_MyPetMissionData) do

        if(_v.m_ServerData.m_ID == iMissionID) then

            PetMission_Controller.ChangeState(_k, EMissionMissionState.Free)

            for _k1, _v1 in pairs(_v.m_ServerData.m_Pets) do

                if(_v1 ~= 0) then

                    -- 改寵物狀態
                    this.m_MyPetsData[_v1].m_PetState = EMissionMissionState.Free

                end

            end

            -- 改任務狀態
            _v.m_ServerData.m_State = EMissionMissionState.Free

        end

    end

end

--- 寵物換狀態\
---@param number iSID 寵物 Server 位置
---@param EMissionMissionState iState 寵物要換的狀態
function PetMgr.PetChangeState(iSID, iState)

    if(this.m_MyPetsData[iSID]) then
        this.m_MyPetsData[iSID].m_PetState = iState
    end

end

--- 用寵物ID檢查玩家取出玩家非出戰狀態寵物
---@param number iPetID 寵物ID
---@return table 如果有符合的 回傳資料
function PetMgr.CheckCollectAvaible(iPetID)

   local _PetCandidateTable = {}
   local _AllMyPet = PetMgr.GetPlayerPetData()
   local _SummonedPetPlot = PetMgr.GetSummonedPetPlot()

   for key,value in pairs(_AllMyPet) do
        if value.m_PetID == iPetID and value.m_PetSlot ~= _SummonedPetPlot then
            table.insert(_PetCandidateTable, value)
        end
   end

   return _PetCandidateTable
end

--- 從利用寵物串表ID 取得玩家所有的寵物編號為輸入編號的寵物
---@param number iPetID 寵物ID
function PetMgr.GetPetListByPetID(iPetID)
    local _PetTable ={}

    for key,value in pairs (this.m_MyPetsData) do
        if value.m_PetID == iPetID  then
            table.insert(_PetTable,value)
        end
    end

    return _PetTable
end

function PetMgr.OnUnrequire()
    if this.m_PetParent.gameObject~=nil then
        this.m_PetParent.gameObject:Destroy()
    end
    return true
end
return PetMgr

