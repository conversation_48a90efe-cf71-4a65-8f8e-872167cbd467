---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---主介面 子控制器 簡易聊天室窗
---@class Main_SubCtrl_SimpleChat
---author 鼎翰
---telephone #2917
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.5.17
Main_SubCtrl_SimpleChat = {}
local this = Main_SubCtrl_SimpleChat

this.MsgUnit = {}

---重刷框框大小
this.m_isRefersh = false

this.m_HideTime = 0

---簡易聊天室窗淡出秒數
local _UISimpleChatFadeOutTime = 5

---簡易聊天室窗移動距離
this.m_ChatFormDis = 100

---簡易聊天視窗訊息上限
this.m_ChatMaxMsg = 3

this.m_Refresh_Content = false

---簡易聊天室窗-表情符號尺寸
local SUBCHAT_EMOJI_SCALE = 0.3

---初始化
function Main_SubCtrl_SimpleChat.Init(iController)
	--設定controller
	this.m_Controller = iController.m_UIController
	--設定ViewRef
	this.m_ViewRef = iController.m_ViewRef

	-- this.m_Image_RedPoint_Chat = this.m_ViewRef.m_Dic_Image:Get("&Image_RedPoint_Chat")
	this.m_Trans_Chat = this.m_ViewRef.m_Dic_Trans:Get("&Scroll View_Chat")
	this.m_Trans_ChatContent = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Content")
	this.m_TMP_ChatTemplate = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_ChatTemplate")

	this.m_MsgItme = this.m_ViewRef.m_Dic_Trans:Get("&MsgUnit").gameObject
	this.m_MsgItme = Extension.CreatePrefabObjPool(this.m_MsgItme, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

	---整個聊天物件
	this.m_Group_Chat = this.m_ViewRef.m_Dic_Trans:Get("&Group_Chat"):GetComponent(typeof(UnityEngine.RectTransform))
	this.m_Group_Chat_PosY = this.m_Group_Chat.anchoredPosition.y

	---簡易聊天物件
	this.m_FadeObj = this.m_ViewRef.m_Dic_Trans:Get("&Scroll View_Chat"):GetComponent(typeof(UnityEngine.RectTransform))
	---簡易聊天物件 LeanTween ID 初始化先不顯示
	this.m_FadeObjID =
		LeanTween.alpha(this.m_FadeObj, 0, 0.1):setOnComplete(
		System.Action(
			function()
				this.m_FadeObjID = nil
			end
		)
	).id

	---簡易聊天背景物件
	this.m_FadeBGObj = this.m_ViewRef.m_Dic_Trans:Get("&Background_Chat"):GetComponent(typeof(UnityEngine.RectTransform))
	---簡易聊天背景物件 LeanTween ID 初始化先不顯示
	this.m_FadeBGObjID =
		LeanTween.alpha(this.m_FadeBGObj, 0, 0.1):setOnComplete(
		System.Action(
			function()
				this.m_Trans_Chat.gameObject:SetActive(false)
				this.m_FadeBGObjID = nil
			end
		)
	).id
end

function Main_SubCtrl_SimpleChat.Open()
	-- -- region 註冊紅點Callback
end

function Main_SubCtrl_SimpleChat.Close()
end

---Update
function Main_SubCtrl_SimpleChat.Update()
	if this.m_Refresh_Content then
		LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Trans_ChatContent)
		this.m_Refresh_Content = false
	end
	if this.m_HideTime > 0 then
		local _HideTime = System.DateTime.Now - this.m_ChatShowTime
		if _HideTime.TotalMilliseconds / 1000 > this.m_HideTime then
			this.m_HideTime = 0
			this.m_FadeObjID =
				LeanTween.alpha(this.m_FadeObj, 0, _UISimpleChatFadeOutTime):setOnComplete(
				System.Action(
					function()
						this.m_FadeObjID = nil
					end
				)
			).id
			this.m_FadeBGObjID =
				LeanTween.alpha(this.m_FadeBGObj, 0, _UISimpleChatFadeOutTime):setOnComplete(
				System.Action(
					function()
						this.m_Trans_Chat.gameObject:SetActive(false)
						this.m_FadeBGObjID = nil
					end
				)
			).id
		end
	end
end

--region 簡易聊天相關

function Main_SubCtrl_SimpleChat.ShowChat()
	if UIMgr.IsInitialized(Main_Controller) then
		this.m_ChatShowTime = System.DateTime.Now
		if ChatMgr.m_AutoHide then
			this.m_HideTime = 5
		else
			this.m_HideTime = 600000
		end
		if this.m_FadeObjID ~= nil then
			LeanTween.cancel(this.m_FadeObjID)
			this.m_FadeObjID = nil
		end
		this.m_FadeObjID = LeanTween.alpha(this.m_FadeObj, 1, 0.1).id

		if this.m_FadeBGObjID ~= nil then
			LeanTween.cancel(this.m_FadeBGObjID)
			this.m_FadeBGObjID = nil
		end
		if ChatMgr.m_ShowSimple then
			this.m_FadeBGObjID =
				LeanTween.alpha(this.m_FadeBGObj, 0.8, 0.1):setOnComplete(
				System.Action(
					function()
						this.m_Trans_Chat.gameObject:SetActive(true)
					end
				)
			).id
		end
	end
end

--快捷列拓展時簡易聊天視窗改變位置
---現階段企劃 原本中間下面快捷鍵區域 不會使用 因此不需要擴展/調整UI位置功能 方法註解掉並保留  2025.05.12  add by 彥凱
--[[
function Main_SubCtrl_SimpleChat.SetChatPos(iCount)

	--有可能取到 nil 先做檢查
	iCount = iCount ~= nil and iCount or 1
	---SimpleChat 還沒初始化
	if not Extension.IsUnityObjectNull(this.m_Group_Chat) then
		local _NowPos = this.m_Group_Chat.anchoredPosition
		_NowPos.y = this.m_Group_Chat_PosY + ((iCount - 1) * this.m_ChatFormDis)
		this.m_Group_Chat.anchoredPosition = _NowPos

		if UIMgr.IsInitialized(AVG_Controller) then
			AVG_Controller.AVGSetPos()
		end

		this.m_IsInitChatPosition = true
	end
end
]]
function Main_SubCtrl_SimpleChat.RefreshOnSecond()
	if this.m_HideTime > 0 and not this.m_Trans_Chat.gameObject.activeSelf then
	elseif this.m_HideTime > 0 then
		this.m_HideTime = this.m_HideTime - 1
	elseif this.m_HideTime == 0 then
		if this.m_Trans_Chat.gameObject.activeSelf then
			this.m_Trans_Chat.gameObject:SetActive(false)
		end
		return
	end
end

function Main_SubCtrl_SimpleChat.AddMsg(iMsg)
	if UIMgr.GetUIStage(this) < EUIStage.Initialized then
		return
	end
	--if this.m_TMP_ChatTemplate == nil then
	--	return
	--end
	--local _ChatTemp = this.m_TMP_ChatTemplate:Instantiate()
	--_ChatTemp.gameObject:SetActive(true)
	--
	--_ChatTemp.transform:SetParent(this.m_Trans_ChatContent)
	--_ChatTemp.transform.localScale = Vector3.one
	--
	--_ChatTemp.text = iMsg
	ChatMgr.Add_GS_Msg(iMsg)
	Main_SubCtrl_SimpleChat.SetChatMsg()
end

--- 重新設定所有任務物件 (過圖、追蹤變動、標記更新)
function Main_SubCtrl_SimpleChat.SetChatMsg()
	if UIMgr.GetUIStage(Main_Controller) < EUIStage.Initialized then
		return
	end

	Main_SubCtrl_SimpleChat.SetMessageListRowItem()

	---非初始化要重刷框框大小
	this.m_isRefersh = true

	--this.m_Trans_ChatContent.localPosition = Vector3(this.m_Trans_ChatContent.localPosition.x, this.m_Trans_ChatContent.rect.height, this.m_Trans_ChatContent.localPosition.z)
end

function Main_SubCtrl_SimpleChat.SetMessageListRowItem()
	--- 防呆 Lua UnRequire 可能沒登入過就要呼叫
	if this.m_Trans_ChatContent == nil then
		return
	end
	local _MsgData = {}
	local _MsgLength = table.Count(ChatMgr.m_ChatPageList)
	--如果超過三條訊息就取最後三條
	local _MsgStartCount = _MsgLength > this.m_ChatMaxMsg and _MsgLength - (this.m_ChatMaxMsg - 1) or 1
	for i = _MsgStartCount, _MsgLength do
		table.insert(_MsgData, ChatMgr.m_ChatPageList[i])
	end

	for i = 1, #this.MsgUnit do
		this.MsgUnit[i].Msg.text = ""
	end

	for i = 0, this.m_Trans_ChatContent.childCount - 1 do
		this.m_Trans_ChatContent:GetChild(i).gameObject:SetActive(false)
	end

	for k, _data in pairs(_MsgData) do
		if _data == nil then
			return
		end

		local _Unit
		local _MsgUnit = {}

		if this.MsgUnit[k] then
			_MsgUnit = this.MsgUnit[k]
		else
			_Unit = this.m_MsgItme:Get()
			_MsgUnit.gameObject = _Unit
			_MsgUnit.Msg = _Unit.transform:Find("img_listBG/&TMP_ChatText"):GetComponent(typeof(TMPro.TextMeshProUGUI))
			_MsgUnit.EmojiParent = _Unit.transform:Find("img_listBG/Emojiparent")
		end

		--設定物件名稱
		_MsgUnit.Name = "Unit_" .. k

		local _RC = RoleMgr.Get(_data.m_RoleID)
		local _Msg = ""
		local _TextID = ""

		if EChannelEmoji[_data.m_Classify] ~= nil then
			_TextID = GString.GetTMPEmoji(EChannelEmoji[_data.m_Classify])
		end

		if _TextID ~= "" then
			_Msg = _TextID
		end

		if _RC ~= nil then
			_Msg = _Msg .. " " .. _RC:GetName() .. " : "
		end

		---因物件重複使用 有可能先顯示 emoji 才顯示文字 所以必須判斷是否需要歸還
		if _MsgUnit.m_HashCode ~= nil then
			EmojiMgr.ReturnEmojiByNameAndHashCode(_MsgUnit.m_HashCode)
			_MsgUnit.m_HashCode = nil
		end

		---檢查是否為emoji 回傳是否 是的話還會回傳emoji name
		local _IsEmoji, _EmojiName = ChatMgr.CheckIsEmojiMessage(_data.m_Msg)

		if _IsEmoji then
			_MsgUnit.Msg.text = _Msg

			EmojiMgr.GetEmojiByName(
				_EmojiName,
				function(iHashCode, iObj)
					_MsgUnit.m_HashCode = iHashCode
					_MsgUnit.m_EmojiPrefab = iObj

					iObj.transform:SetParent(_MsgUnit.EmojiParent)
					iObj.transform.localPosition = Vector3(0, 0, 0)
					iObj.transform.localScale = Vector3(SUBCHAT_EMOJI_SCALE, SUBCHAT_EMOJI_SCALE, SUBCHAT_EMOJI_SCALE)
					iObj:SetActive(true)
				end
			)
		else
			_MsgUnit.Msg.text = _Msg .. _data.m_Msg
		end

		_MsgUnit.EmojiParent.gameObject:SetActive(_IsEmoji)
		_MsgUnit.gameObject.transform:SetParent(this.m_Trans_ChatContent)
		_MsgUnit.gameObject.transform.localScale = Vector3.one
		_MsgUnit.gameObject:SetActive(true)

		_MsgUnit.gameObject.transform:SetAsLastSibling()

		this.MsgUnit[k] = _MsgUnit
	end
	this.m_Refresh_Content = true
end

--endregion

return this
