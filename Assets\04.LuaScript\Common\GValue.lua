---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Class 數值
---@class GValue
---author GValue
---telephone #2950
---version 1.0
---since [黃易群俠傳M] 0.80
---date 2023.10.2
GValue = {}
local this = GValue

---@class EValueShowType 數字顯示類型
EValueShowType =
{
    --- 縮寫顯示
    Short = 0,

    --- 精準顯示
    Full = 1
}

--- 記下有大括弧起來的數字Param(自動變數)
EParamNumber =
{
    --紀錄格式如下
    [1] = {1},
}

--region 文字格式
---金錢格式 10000 -> 10,000
---@return string
function GValue.GetMoneyFormat(iValue)
	local formatted = tostring( tonumber(iValue) or 0)
	local k
	while true do
		formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", "%1,%2")
		if k == 0 then
			break
		end
	end
	return formatted
end

---依照指定位數補零 e.g. GFunction.Zero_stuffing(32, 5) -> 00032
---@param iValue number 數值
---@param iDight number 位數
function GValue.Zero_stuffing(iNum, iDight)
	local _Number = iNum
	local _NumberDight = 0
	--取得number為幾位數
	while _Number > 0 do
		_Number = math.floor(_Number * 0.1)
		_NumberDight = _NumberDight + 1
	end

	if _NumberDight < iDight then
		local _ZeroStuff = ""
		--不足位數補0
		for i = _NumberDight, iDight - 1 do
			_ZeroStuff = _ZeroStuff .. "0"
		end
		return _ZeroStuff .. tostring(iNum)
	else
		--位數足夠 直接回傳
		return tostring(iNum)
	end
end

--- 數字轉換字串 (因應未來可能會需要統一顯示 , 或是 K/W/M)
---TODO 尚未實做
---@param iValue number 數值
---@param iType EValueShowType 數字顯示類型
function GValue.ValueToString(iValue, iType)
    local _Result = iValue
    if iType == EValueShowType.Full then
        _Result = GValue.GetMoneyFormat(iValue)
    else
		--TODO 縮寫需新增
    end

    return _Result
end

--- 數量轉換字串 ( 統一顯示 3000 已上顯示 3000+ )
---@param iValue number 數值
---@param iType EValueShowType 數字顯示類型
function GValue.CountToString(iValue, iType)
    local _Result = iValue
    if iType == EValueShowType.Short then
		if iValue > IconSetting.m_ITEMMAXCOUNT then
			_Result = IconSetting.m_ITEMMAXCOUNT .. "+"
		end
    end

    return _Result
end

--- 屬性變字串 (包含正負及百分比)
---@param iAttributeType EAttributeType 屬性效果編號
---@param iValue number 數值
---@param iValueHandleFinish bool 數值是否已經處理完畢 true:數值處理完畢不再做運算(ex:從屬性介面來的轉換需求 已將數值處計算完畢 所以不需要再度計算)  false/nil:數值需要再做計算(:ex:裝備 讀串表資訊)
---@param iShowINCOrDEC bool 是否顯示正負符號
---@return string 屬性名稱
---@return string 數值字串
function GValue.AttributeToString(iAttributeType, iValue,iValueHandleFinish, iShowINCOrDEC)
	--- 取得屬性資訊
	local _StatusNameData = StatusNameData.GetStatusNameDataByIdx(iAttributeType)
	if _StatusNameData == nil then
		return " " , " " 
	end
	local _Result_Name = StatusNameData.GetStatusNameStringByIndex(iAttributeType)
	local _Result_Value = iValue

	--- 處理數值
	if iValueHandleFinish == false or iValueHandleFinish == nil then	
		if _StatusNameData.m_ValueType == 1 then -- 百分比
			_Result_Value = iValue * 0.01
		elseif _StatusNameData.m_ValueType == 2 then -- 千分比
			_Result_Value = iValue * 0.001
		end
	end

	--- 百分位要自己乘回去
	if _StatusNameData.m_ShowPercent == 1 then
		_Result_Value = _Result_Value * 100
	end

	--- 小數點位數
	if _StatusNameData.m_FormateType == 1 then -- 小數點後 1位
		_Result_Value = string.format("%.1f",_Result_Value)
	elseif _StatusNameData.m_FormateType == 2 then -- 小數點後 2位
		_Result_Value = string.format("%.2f",_Result_Value)
	end

	--- 正負號
	if iShowINCOrDEC == false or iShowINCOrDEC == nil then
		if _StatusNameData.m_INCOrDEC == 0 then -- 增加
			_Result_Value = GString.Format(TextData.Get(797),_Result_Value)
		elseif _StatusNameData.m_INCOrDEC == 1 then -- 減少
			_Result_Value = string.Concat("-" , _Result_Value)
		end
	end

	--- 加百分號
	if _StatusNameData.m_ShowPercent == 1 then
		_Result_Value = string.Concat(_Result_Value,"%")
	end

    return _Result_Name , _Result_Value
end


local _TimeGape =
{-- 全部換算成毫秒
	["Hour"] = 60*60*1000,
	["Minute"] = 60*1000,
	["Second"] = 1000
}

--- 時間轉換-串表的時間轉換
--- 1100 -> 1.1秒
function GValue.TableTimeToString(iValue, iIsNotUseStyle)
	local _Result = ""
	if iValue == 0 then
		return _Result
	end
	local _Time = iValue
	
	-- 時
	if _Time >= _TimeGape["Hour"] then
		local _HoruStr = math.floor(_Time/_TimeGape["Hour"])
		_Result = string.Concat(_Result, _HoruStr , TextData.Get(8006))
		_Time = _Time % _TimeGape["Hour"]
	end
	-- 分
	if _Time >= _TimeGape["Minute"] then
		local _MinuteStr = math.floor(_Time/_TimeGape["Minute"])
		_Result = string.Concat(_Result, _MinuteStr , TextData.Get(8005))
		_Time = _Time % _TimeGape["Minute"]
	end
	-- 秒
	if _Time >= 0 then
		local _SecondStr = math.floor(_Time/_TimeGape["Second"])
		_Result = string.Concat(_Result, _SecondStr , TextData.Get(8004))
	end

	if iIsNotUseStyle == nil or iIsNotUseStyle == false then
		return GString.StringWithStyle(_Result,"W")
	else
		return _Result
	end
end
