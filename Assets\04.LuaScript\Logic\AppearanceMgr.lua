---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================


---管理顯示在UI/假玩家的外觀
---@class AppearanceMgr
---author WereHsu
---version 1.0
---since [ProjectBase] 0.1
---date 2023.12.13
AppearanceMgr = {}
local this = AppearanceMgr

---根物件
this.m_AppearanceRoot = nil

---Pool存起來的物件
local m_ModelPoolRoot

---Appearance存起來的模型
local m_ModelStoreRoot

---現存複製的外觀
---@type table<string,table<number,ModelController>>
local m_Active_Appearance = {}

---暫存複製的外觀
---@type table<string,table<number,ModelController>>
local m_Stored_Appearance = {}

---正在使用中的裝備模型
---@type table<string, table<number, ModelController>>
local m_ActiveEquipment = {}

---存模型的池子
local m_GObjPool_Model

---全清時勿砍
local m_PlayerModelController

---基本的MC，如果撈不到MC資料就會用這個
local m_DefaultModelController

local m_ShowDebugMsg = false

---時間增幅
local m_BaseTimeIncrease = 2.5
---相機本體時間增幅
local m_PositionSpeedIncrease = 0.5

-- 相機本體移動設定
--- 相機本體移動目標點
local m_DesiredCamPosition = Vector3(0, 0, 0)
--- 相機本體移動起始點
local m_StartSmoothMovePosition = nil
--- 相機本體移動進度
local m_SmoothMoveProgress = 0

-- 相機錨點移動設定
--- 相機錨點移動目標點
local m_DesiredCamAnchorPosition = Vector3(0, 0, 0)
--- 相機錨點移動起始點
local m_StartAnchorSmoothMovePosition = nil
--- 相機錨點移動進度
local m_SmoothAnchorMoveProgress = 0

--- sensorSize
local m_SpecialSensorSize = Vector2(90, 45)

local function CamUpdate()
    if this.m_CameraAnchor == nil or this.m_Camera == nil then
        return
    end

    -- 相機位置移動
    if m_StartSmoothMovePosition then
        m_SmoothMoveProgress = m_SmoothMoveProgress + m_BaseTimeIncrease * Time.deltaTime * m_PositionSpeedIncrease
        this.m_Camera.transform.localPosition = Vector3.Lerp(
                m_StartSmoothMovePosition,
                m_DesiredCamPosition,
                m_SmoothMoveProgress  )

        -- 到達目的地
        if m_SmoothMoveProgress >= 1 then
            m_StartSmoothMovePosition = nil
            m_SmoothMoveProgress = 0
        end
    end

    -- 相機錨點移動
    if m_StartAnchorSmoothMovePosition then
        m_SmoothAnchorMoveProgress = m_SmoothAnchorMoveProgress + m_BaseTimeIncrease * Time.deltaTime
        this.m_CameraAnchor.transform.localPosition = Vector3.Lerp(
                m_StartAnchorSmoothMovePosition,
                m_DesiredCamAnchorPosition,
                m_SmoothAnchorMoveProgress )

        -- 到達目的地
        if m_SmoothAnchorMoveProgress >= 1 then
            m_StartAnchorSmoothMovePosition = nil
            m_SmoothAnchorMoveProgress = 0
        end
    end

    if not m_StartSmoothMovePosition and not m_StartAnchorSmoothMovePosition then
        this.m_IsSmoothMoving = false
    end
end

---設置存起來的ModelController
local function SetStoreMC(iMC)
    iMC:SetRelease()
    iMC.transform.gameObject:SetActive(false)
    iMC.transform:SetParent(m_ModelStoreRoot.transform)
    iMC:ResetDissolveEffect(true)
    iMC:SetAniBool(AnimationHash.Bool_IsLoopSituation, false)

end


function AppearanceMgr.DebugLog(iMessageStr, iColorStr)
    if not m_ShowDebugMsg then
        do
            return
        end
    end

    if iColorStr == nil then
        D.Log(GString.Format("[AppearanceLog] {0}", iMessageStr))
    else
        D.Log(GString.Format("<color={0}>[AppearanceLog] {1}</color>", iColorStr, iMessageStr))
    end
end

---初始化設定根物件
function AppearanceMgr.Init()
    this.m_AppearanceRoot = GameObject.New("Appearance_Root")
    this.m_AppearanceRoot.transform.localPosition = Vector3(900,0,0)
    this.m_AppearanceRoot.transform.localScale = Vector3.one

    m_ModelPoolRoot = GameObject.New("Model_StorePool")
    m_ModelPoolRoot.transform:SetParent(this.m_AppearanceRoot.transform)
    m_ModelPoolRoot.transform.localPosition = Vector3.zero
    m_ModelPoolRoot.transform.localScale = Vector3.one

    m_ModelStoreRoot = GameObject.New("Model_StoreRoot")
    m_ModelStoreRoot.transform:SetParent(this.m_AppearanceRoot.transform)
    m_ModelStoreRoot.transform.localPosition = Vector3.zero
    m_ModelStoreRoot.transform.localScale = Vector3.one

    m_GObjPool_Model = Extension.GetGameObjPool(
		90,
		0,
		AppearanceMgr.ModelObj_Reset,
		AppearanceMgr.ModelObj_Init
	)

    AppearanceMgr.CreateAppearanceCamera()

    AppearanceMgr.CreateAppearanceLight()
end

function AppearanceMgr.CreateDefault()
    if not m_DefaultModelController then
        local _DefaultModelRoot = GameObject.New("Model_Default")
        _DefaultModelRoot.transform:SetParent(this.m_AppearanceRoot.transform)
        _DefaultModelRoot.transform.localPosition = Vector3.zero
        _DefaultModelRoot.transform.localScale = Vector3.one

        local _TmpAppear = {}

        for k,v in pairs(RoleMgr.m_RC_Player.AppearData) do
            _TmpAppear[k] = v
        end
        _TmpAppear.m_IsSelf = false
        m_DefaultModelController = ModelController:New(
            _DefaultModelRoot,"DefaultModel" , _TmpAppear,PlayerData.m_LoginPlayerData.m_Enhance, function()
                --m_DefaultModelController.m_ModelObject:SetActive(false)
                for i = 0 , m_DefaultModelController.m_SkinnedMeshRenderer.Length - 1 do
                    if not Extension.IsUnityObjectNull(m_DefaultModelController.m_SkinnedMeshRenderer[i]) then
                        m_DefaultModelController.m_SkinnedMeshRenderer[i].gameObject:SetActive(false)
                    end
                end
            end)

        --_DefaultModelRoot:SetActive(false)
    end
end

function AppearanceMgr.Update()
    for _id, _table in pairs(m_Stored_Appearance) do
        ---@param _MC ModelController
        for _idx, _MC in pairs(_table) do
            if _MC.transform.parent ~= m_ModelStoreRoot.transform then
                AppearanceMgr.DebugLog("怪東西沒存到Root: ".._idx)
            end
            if _MC.m_RecordRelease then
                local _SystemTime = HEMTimeMgr.SystemTime()
                local _time = _SystemTime - _MC.m_RecordRelease
                if _time.TotalSeconds > NPCSetting.m_ModelReturnTime then
                    _MC.m_RecordRelease = nil
                    AppearanceMgr.DebugLog("正流程釋放模型: ".._MC.transform.gameObject:GetInstanceID())
                    AppearanceMgr.Release(_id, _MC)
                end
            end
        end
    end

    for _id, _table in pairs(m_Active_Appearance) do
        ---@param _MC ModelController
        for _idx, _MC in pairs(_table) do
            _MC:Update()
        end
    end

    if this.m_IsSmoothMoving then
        CamUpdate()
    end
end

local m_StoreCount = 0

---Debug用的檢查物件數量
local function OnChangeItemCount()
    if not ProjectMgr.IsDebug() then
        return
    end

    local _ActiveCount = 0
    for k,v in pairs(m_Active_Appearance) do
        _ActiveCount = _ActiveCount + table.Count(v)
    end

    local _StoreCount = 0
    for k,v in pairs(m_Stored_Appearance) do
        _StoreCount = _StoreCount + table.Count(v)
    end

    this.m_AppearanceRoot.name = "Appearance_Root_".._ActiveCount.."_".._StoreCount.."_"..m_ModelStoreRoot.transform.childCount

    AppearanceMgr.DebugLog("預計要回收的數量 : "..m_StoreCount)
end

local function GetPoolObject()
    local _Counter = 0
    local _ReturnItem
    while _ReturnItem == nil do
        _ReturnItem = m_GObjPool_Model:Get()
        if Extension.IsUnityObjectNull(_ReturnItem) then
            AppearanceMgr.DebugLog("[AppearanceMgr] m_GObjPool_Model 取不到物件")
            --m_GObjPool_Model:Store(_ReturnItem)
            _ReturnItem = nil
            _Counter = _Counter + 1
        end

        if _Counter > 10 then
            D.LogError("AppearanceMgr物件池取不到物件")
            break
        end
    end

    return _ReturnItem
end

---取預設的模型
function AppearanceMgr.GetDefaultModel()
    return m_DefaultModelController
end

---玩家登入要設定玩家模型，這個模型不會換場景後清除
function AppearanceMgr.SetPlayerModel(iRC)
    PlayerData.SetIsModelCreate(false)
    iRC.m_ModelController = AppearanceMgr.New(iRC.AppearData, iRC.m_PlayerID, true, iRC.m_Enhance, Layer.PlayerSelf, function(iMC) AppearanceMgr.PlaceNewModel(iRC, iMC) end)
    iRC.m_ModelController:SetRoleController(iRC)
    m_PlayerModelController = iRC.m_ModelController
end

---預計用在UI上的模型相機
function AppearanceMgr.CreateAppearanceCamera()
    local _CameraAnchor = GameObject.New("CameraParent")
    _CameraAnchor.transform:SetParent(this.m_AppearanceRoot.transform)
    _CameraAnchor.transform.localPosition = Vector3.New(0, 0, 3)
    _CameraAnchor.transform.localScale = Vector3.one
    _CameraAnchor.transform.localEulerAngles = Vector3.New(45, 135, 0)
    this.m_CameraAnchor = _CameraAnchor

    local _AppearanceCamera = GameObject.New("AppearanceCamera")
    _AppearanceCamera.transform:SetParent(_CameraAnchor.transform)
    _AppearanceCamera.transform.localPosition = Vector3.New(0, 1, 0)
    _AppearanceCamera.transform.localScale = Vector3.one
    _AppearanceCamera.transform.localEulerAngles = Vector3.New(0, 180, 0)

    ---攝影機
    this.m_Camera = Extension.AddMissingComponent(_AppearanceCamera, typeof(Camera))
    this.m_Camera.orthographic = false
    this.m_Camera.nearClipPlane = 0.01
    this.m_Camera.farClipPlane = 50
    this.m_Camera.cullingMask = 2 ^ Layer.UIModel + 2 ^ Layer.UIModelLight + 2 ^ Layer.FX --+ 2 ^ Layer.Player + 2 ^ Layer.NPC
    this.m_Camera.clearFlags = UnityEngine.CameraClearFlags.Depth
    this.m_Camera.depthTextureMode = UnityEngine.DepthTextureMode.Depth
    this.m_Camera.orthographicSize = 3.0
    this.m_Camera.fieldOfView = 50
    --- 聲音
    this.AudioListener = Extension.AddMissingComponent(_AppearanceCamera, typeof(UnityEngine.AudioListener))
    this.AudioListener.enabled = false
end

---預計用在UI上的打光
function AppearanceMgr.CreateAppearanceLight()
    local _AppearanceLight = GameObject.New("AppearanceLight")
    _AppearanceLight.transform:SetParent(this.m_AppearanceRoot.transform)
    _AppearanceLight.transform.localPosition = Vector3.New(0, 1, 0)
    _AppearanceLight.transform.localScale = Vector3.one
    _AppearanceLight.transform.localEulerAngles = Vector3.New(120, 0, 0)

    this.m_Light = Extension.AddMissingComponent(_AppearanceLight, typeof(UnityEngine.Light))
    this.m_Light.type = UnityEngine.LightType.Directional
    this.m_Light.cullingMask = 2 ^ Layer.UIModel + 2 ^ Layer.UIModelLight
end

---新增一個Model
---@param iGObj GameObject
---@param iAppearanceInfo PlayerAppearData|NPCAppearData
---@param iID number
---@param IsPlayer boolean
---@param iEnhance sEnhance
function AppearanceMgr.New(iAppearanceInfo, iID, IsPlayer, iEnhance, iLayer, iLoadCompleteDelegate)

    --這裡從pool要
    local _GObj
    ---@type ModelController
    local _MC
    iID = tostring(iID)

    if m_Stored_Appearance[iID] and table.Count(m_Stored_Appearance[iID]) > 0 then
        _MC = table.FirstAndRemove(m_Stored_Appearance[iID])
    end

    if _MC and not Extension.IsUnityObjectNull(_MC.m_ModelObject) then
        if table.Count(m_Stored_Appearance[iID]) == 0 then
            m_Stored_Appearance[iID] = nil
        end

        AppearanceMgr.DebugLog("取消回收模型 :".._MC.transform.gameObject:GetInstanceID())

        _MC:CancelRelease()

        _MC.transform.gameObject.layer = iLayer
        if IsPlayer then
            _MC.m_AppearanceInfo = iAppearanceInfo
            _MC:UpdateWeapon(_MC.m_AppearanceInfo.m_WeaponType)
            _MC:UpdateModel(false)

            _MC.m_LoadCompleteDelegate = iLoadCompleteDelegate
        else
            iLoadCompleteDelegate(_MC)
        end
        m_StoreCount = m_StoreCount - 1
    else

        _GObj = GetPoolObject()
        if Extension.IsUnityObjectNull(_GObj) then
            --D.LogError("[AppearanceMgr] m_GObjPool_Model 取不到模型. IsPlayer: " .. tostring(IsPlayer) .. ". ID: " .. iID .. ". " .. debug.traceback())
            return
        end
        _GObj.layer = iLayer

        if _MC then
            AppearanceMgr.StoreModel(iID, _MC)
        end

        if IsPlayer then
            _MC = ModelController:New(_GObj,iID, iAppearanceInfo,iEnhance, iLoadCompleteDelegate)
            _GObj.name = "PlayerModel_"..iID.."_".._MC.transform.gameObject:GetInstanceID()
        else
            _MC = ModelController:NewNPC(_GObj, iAppearanceInfo,iLoadCompleteDelegate)
            _GObj.name = "NPCModel_"..iID.."_".._MC.transform.gameObject:GetInstanceID()
        end
        AppearanceMgr.DebugLog("產出模型 :".._GObj:GetInstanceID())
    end

    --_MC.transform.gameObject:SetActive(false)

    if m_Active_Appearance[iID] == nil then
        m_Active_Appearance[iID] = {}
    end
    m_Active_Appearance[iID][_MC.transform.gameObject:GetInstanceID()] = _MC


    OnChangeItemCount()
    return _MC
end

---@param iRC RoleController
---@param iMC ModelController
function AppearanceMgr.PlaceNewModel(iRC, iMC)

    iMC.transform:SetParent(iRC.m_ModelObject.transform)
    iMC.transform.localPosition = Vector3.zero
    iMC.transform.localRotation = Vector3.zero
    iRC.m_FadeType = RoleController.FadeType.FadeIn
    iMC:StartFade(iRC.m_FadeType)
    iMC.transform.gameObject:SetActive(true)

    -- 模型縮放
    iMC:SetModelSize(iRC.m_OriginScale)

    if iRC.m_IsSelf then
        PlayerData.SetIsModelCreate(true)
    end

    if iRC.m_StateController and iRC.m_StateController:IsDead() then
        iMC:SetDie(true)
    else
        iMC:SetDie(false)
    end

    if iRC.m_IsFirstShowType == ETeleportInType.ShowUpFromSneak then
        iMC:StartDissolve(true)
        ---播過了
        iRC.m_IsFirstShowType = nil

    elseif iRC.m_IsFirstShowType == ETeleportInType.MapTeleport then
        if not iRC.m_StateController:IsDead() then
            local _SPosVec2 = GFunction.ScenePosToServerPos(iRC.transform.localPosition)
            RoleMgr.Teleport(iRC.m_PlayerID, _SPosVec2.x, _SPosVec2.y, nil, nil, iMC)
        end
        iRC.m_IsFirstShowType = nil
    end

    ---iRC.m_PlayerID and iRC.m_NPCID 代表他是假人
    local _IsFakeFriendlyPlayer = iRC.m_PlayerID and iRC.m_NPCID and iRC.m_Relation ~= nil and not iRC.m_Relation[ERelatType.Enemy]
    local _GlowType = _IsFakeFriendlyPlayer and EGlowType.FAKEPLAYER or EGlowType.NONE

    iMC:SetGlowEffect(_GlowType)

    ---如果NPC 需要長駐特效 做出該特效
    if iRC.AppearData.m_DebutEffectID and iRC.AppearData.m_DebutEffectID ~=0 then
        local _EffectData = EffectData.New()
        _EffectData.m_ID = iRC.AppearData.m_DebutEffectID
        _EffectData.m_BonePos = EBoneIdx.Foot
        _EffectData.m_isFollow = true
        --播特效
        iMC:SetEffect(_EffectData)
    end
end

---@param iMC ModelController
---@param iModelBaseName table 要換的裝備
function AppearanceMgr.SetLoadedCosmetic(iMC, iModelBaseTable)
    local _CharacterBaseModel = iMC.m_OldCBM
    for _ModelBodyPart, _ItemPos in pairs(ModelBodyPart) do
        if _CharacterBaseModel and
           _CharacterBaseModel[_ItemPos] and
           not string.IsNullOrEmpty(_CharacterBaseModel[_ItemPos]) and
           _CharacterBaseModel[_ItemPos] ~= iModelBaseTable[_ItemPos] then

            AppearanceMgr.RemoveLoadedCosmetic(iMC, _CharacterBaseModel[_ItemPos])
        end

        if not string.IsNullOrEmpty(iModelBaseTable[_ItemPos]) then
            AppearanceMgr.AddLoadedCosmetic(iMC, iModelBaseTable[_ItemPos])
        end
    end
end

function AppearanceMgr.AddLoadedCosmetic(iMC, iName)

    if m_ActiveEquipment[iName] == nil then
        m_ActiveEquipment[iName] = {}
    end
    m_ActiveEquipment[iName][iMC] = iMC

end

function AppearanceMgr.RemoveLoadedCosmetic(iMC, iName)
    if m_ActiveEquipment[iName] then
        m_ActiveEquipment[iName][iMC] = nil
    end
    if table.Count(m_ActiveEquipment[iName]) <= 0 then
        m_ActiveEquipment[iName] = nil
        ResourceMgr.Unload(iName)
    end
end

---回收Model
---@param iIdx number
---@param iMC ModelController
function AppearanceMgr.StoreModel(iIdx, iMC)
    local _idx = tostring(iIdx)

    if m_Stored_Appearance[_idx] == nil then
        m_Stored_Appearance[_idx] = {}
    end

    if m_Active_Appearance[_idx] then

        ---@param k number
        ---@param v ModelController
        for k,v in pairs(m_Active_Appearance[_idx]) do
            if (iMC == nil or v == iMC) and v ~= m_PlayerModelController then
                m_Active_Appearance[_idx][k] = nil
                SetStoreMC(v)
                m_Stored_Appearance[_idx][k] = v
                m_StoreCount = m_StoreCount + 1

                AppearanceMgr.DebugLog("回收模型 :"..k)
            end
        end

        if table.Count(m_Active_Appearance[_idx]) == 0 then
            m_Active_Appearance[_idx] = nil
        end
    end

    --漏網之魚
    if iMC and iMC ~= m_PlayerModelController and m_Stored_Appearance[_idx][iMC.transform.gameObject:GetInstanceID()] == nil then
        SetStoreMC(iMC)
        m_Stored_Appearance[_idx][iMC.transform.gameObject:GetInstanceID()] = iMC
        m_StoreCount = m_StoreCount + 1

    end
    OnChangeItemCount()
end

---@param iIdx number
---@param iMC ModelController
---@param iUnload boolean 是否需要unload資源
---釋放物件 TODO: 玩家不過場，並且場景上有人不斷換寵物可能會卡
function AppearanceMgr.Release(iIdx, iMC, iUnload)
    local _isRelease = ""
    local _idx = tostring(iIdx)
    if m_Active_Appearance[_idx] then

        for k,v in pairs(m_Active_Appearance[_idx]) do
            if (iMC == nil or v == iMC) and v ~= m_PlayerModelController then
                _isRelease = "Release Appearance Active"
                if not Extension.IsUnityObjectNull(v.transform) and not Extension.IsUnityObjectNull(v.transform.gameObject) then
                    local _obj = v.transform.gameObject
                    AppearanceMgr.DebugLog("正常釋放模型 :".._obj:GetInstanceID())

                    v:IsRelease(iUnload)

                    m_GObjPool_Model:Store(_obj)
                end
                m_Active_Appearance[_idx][k] = nil
            end
        end
        if table.Count(m_Active_Appearance[_idx]) == 0 then
            m_Active_Appearance[_idx] = nil
        end
    end
    if m_Stored_Appearance[_idx] then
        for k,v in pairs(m_Stored_Appearance[_idx]) do
            if (iMC == nil or v == iMC) and v ~= m_PlayerModelController then
                _isRelease = "Release Appearance Store"
                if not Extension.IsUnityObjectNull(v.transform) and not Extension.IsUnityObjectNull(v.transform.gameObject) then
                    local _obj = v.transform.gameObject

                    v:IsRelease(iUnload)
                    AppearanceMgr.DebugLog("正常釋放模型 :".._obj:GetInstanceID())

                    m_GObjPool_Model:Store(_obj)
                end
                m_Stored_Appearance[_idx][k] = nil
                m_StoreCount = m_StoreCount - 1
            end
        end
        if table.Count(m_Stored_Appearance[_idx]) == 0 then
            m_Stored_Appearance[_idx] = nil
        end
    end
    if string.IsNullOrEmpty(_isRelease) then
        AppearanceMgr.DebugLog("沒有釋放到模型")
        -- if iMC and iMC.m_RecordRelease then
        --     iMC:IsRelease()
        -- end
    else
        AppearanceMgr.DebugLog(_isRelease)
    end

    OnChangeItemCount()
end

---通通清掉
function AppearanceMgr.ReleaseAll(iReleasePlayer)

    for k,v in pairs(m_Active_Appearance) do
        --玩家穿的東西不用unload
        AppearanceMgr.Release(k, nil , k ~= tostring(tostring(PlayerData.GetCardID())))
    end
    for k,v in pairs(m_Stored_Appearance) do
        --玩家穿的東西不用unload
        AppearanceMgr.Release(k, nil , k ~= tostring(tostring(PlayerData.GetCardID())))
    end

    if iReleasePlayer and m_PlayerModelController then
        local _GObj = m_PlayerModelController.transform.gameObject
        m_PlayerModelController:IsRelease(true)
        m_GObjPool_Model:Store(_GObj)
        m_Active_Appearance[tostring(PlayerData.GetCardID())] = nil
        m_PlayerModelController = nil
    end
end

---刷新外觀
function AppearanceMgr.UpdateModelAppearance(iIdx, iMC, iEwaponType)
    local _idx = tostring(iIdx)
    if m_Active_Appearance[_idx] then

        ---@param k number
        ---@param v ModelController
        for k,v in pairs(m_Active_Appearance[_idx]) do
            if iMC == nil or v == iMC then
                v:UpdateModel(false)
                if iEwaponType then
                    --v.m_AppearanceInfo.m_WeaponType = iEwaponType
                    v:UpdateWeapon(iEwaponType)
                    v:SwitchWeaponAni()
                end
            end
        end
    end
end

function AppearanceMgr.ModelObj_Reset(iGObj)
    if iGObj then
        iGObj.transform:SetParent(m_ModelPoolRoot.transform)
        iGObj.name = "Stroed Model"
        iGObj.transform.localPosition = Vector3.zero
        iGObj.transform.localEulerAngles = Vector3.zero
        iGObj:SetActive(false)

        if iGObj.transform.childCount > 0 then
            for i = 0 ,iGObj.transform.childCount - 1 do
                local _ChildObj = iGObj.transform:GetChild(i).gameObject
                _ChildObj:Destroy()
            end
        end
    end
end

function AppearanceMgr.ModelObj_Init(iGObj)
    iGObj.transform:SetParent(m_ModelPoolRoot.transform)
    iGObj.transform.localPosition = Vector3.zero
end

--region UIModel用的相機設定

function AppearanceMgr.EnableCamera(iActive)
    if this.m_Camera ~= nil and this.m_Camera.gameObject.activeSelf ~= iActive then
        this.m_Camera.gameObject:SetActive(iActive)
    end
end

function AppearanceMgr.SetCameraPosition(iV3, iSmooth)
    if this.m_Camera ~= nil then
        if iSmooth then
            m_DesiredCamPosition = iV3
            m_StartSmoothMovePosition = this.m_Camera.transform.localPosition
            m_SmoothMoveProgress = 0
        else
            m_DesiredCamPosition = iV3
            this.m_Camera.transform.localPosition = iV3
        end

        this.m_IsSmoothMoving = iSmooth
    end
end

function AppearanceMgr.SetCameraRotation(iV3)

    if(this.m_Camera ~= nil) then

        this.m_Camera.transform.localEulerAngles = iV3

    end

end

function AppearanceMgr.SetCameraFOV(iFOV)
    if(this.m_Camera ~= nil) then

        this.m_Camera.fieldOfView = iFOV

    end
end

function AppearanceMgr.SetCameraAnchorPosition(iV3, iSmooth)

    if this.m_CameraAnchor ~= nil then
        if iSmooth then
            m_DesiredCamAnchorPosition = iV3
            m_StartAnchorSmoothMovePosition = this.m_CameraAnchor.transform.localPosition
            m_SmoothAnchorMoveProgress = 0
        else
            m_DesiredCamAnchorPosition = iV3
            this.m_CameraAnchor.transform.localPosition = iV3
        end

        this.m_IsSmoothMoving = iSmooth
    end

end

function AppearanceMgr.SetCameraAnchorRotation(iV3)

    if(this.m_CameraAnchor ~= nil) then

        this.m_CameraAnchor.transform.localEulerAngles = iV3

    end

end

function AppearanceMgr.SetTargetTexture(iRT)

    if(this.m_Camera ~= nil) then

        this.m_Camera.targetTexture = iRT

    end

end

function AppearanceMgr.SetClearFlagToSolidColor()

    if(this.m_Camera ~= nil) then

        this.m_Camera.clearFlags = UnityEngine.CameraClearFlags.SolidColor

    end

end

--endregion



function AppearanceMgr.GetAppearanceRoot()

    if(this.m_AppearanceRoot ~= nil) then

        return this.m_AppearanceRoot

    end

end

function AppearanceMgr.GetCamera()

    return  this.m_Camera

end

function AppearanceMgr.GetAudioListener()

    return this.AudioListener

end

--- 打開 PhysicalCamera 並設定到固定的值
function AppearanceMgr.TurnOnPhysicalCameraAndSetToFixValue()

    this.m_Camera.usePhysicalProperties = true
    this.m_Camera.sensorSize = m_SpecialSensorSize
    this.m_Camera.gateFit = UnityEngine.Camera.GateFitMode.None

end

--- 打開 PhysicalCamera 並設定到固定的值
function AppearanceMgr.TurnOffPhysicalCameraAndSetToFixValue()

    this.m_Camera.usePhysicalProperties = false

end

function AppearanceMgr.OnUnrequire()
    if this.m_AppearanceRoot then
        this.m_AppearanceRoot:Destroy()
        this.m_AppearanceRoot = nil
    end
    return true
end
return AppearanceMgr
