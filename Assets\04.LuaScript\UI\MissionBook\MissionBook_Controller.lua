---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("UI/MissionBook/MissionMgr")

---任務書介面
---@class MissionBook_Controller
---author WereHsu
---version 1.0
---since [黃易群俠傳M] 0.90
---date 2023.03.17
MissionBook_Controller = {}
local this = MissionBook_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("MissionBook_View", "MissionBook_Controller", EUIOrderLayers.FullPage, false, "Temporary_use_004")
this.m_NowShowPage = EMissionType.Main

local ARROW_CLOSE = Vector3(0, 0, -180)
local ARROW_OPEN = Vector3(180, 0, -90)
local CHAPTER_STATUSCHECK_ALPHA = 0.3

---章節物件高度
local CHAPTER_OBJECT_HEIGHT = 0
---任務物件高度
local MISSION_OBJECT_HEIGHT = 0

this.m_MissionListTable = {}

--- 任務 Table 重複利用物件
this.m_ScrollView_MissionList = {}

--- 那個任務被選中
local m_SelectedMissionData = {}

--- 現在點選中章節
local m_NowOnClickChapter = 0

--- 上一次點選的章節
local m_LastClickChapter = 0

--- 上一次點遠的任務
local m_LastClickMission = 0

--- 要不要顯示收集獎勵按鈕
local m_ShowCollectRewardBtn = true

---是否要執行直建顯示的dotween
local m_ShowDisplayEffectInChapterArea = true

---右側任務面板 左右移動的相關設定
---初始位置
local _End_RightMissionPanel = nil
---結束位置
local _Start_RightMissionPanel = nil
---初始位置 結束位置 x 距離
local _Length_Delta = 20


---任務介面開啟後 默認點去哪個介面
EMissionUIDefaultPage = {
    ---未領取章節
    UnClaim = 1, 
    ---前往某個章節
    Chapter = 2, 
    ---前往某個任務
    Mission =3, 

}

---任務介面的頁籤
EMissionPage = {
    ---目前章節
    NowChapter = 1,
    ---完成章節
    CompleteChapter = 2,
    ---特殊任務
    SpecialMission = 3
}

--- 開啟頁面類型
local m_NowClickPageType = EMissionPage.NowChapter

---此次開啟後 要跳到未領取章節獎勵
local _MissionUIDefaultPage = EMissionUIDefaultPage.UnClaim

--- 取狀態字串
local function GetStatusString(iStatus)

    -- 可接
    if(iStatus == EMissionStatus.CanAccept) then
        return GString.StringWithStyle(TextData.Get(20600100), "BB")
    -- 進行中
    elseif(iStatus == EMissionStatus.Undone) then
        return GString.StringWithStyle(TextData.Get(20600101), "B")
    -- 可完成
    elseif(iStatus == EMissionStatus.CanComplete) then
        return GString.StringWithStyle(TextData.Get(20600102), "O")
    -- 等級不足
    elseif(iStatus == EMissionStatus.NoLevel) then
        return GString.StringWithStyle(TextData.Get(20600103), "W")
    -- 條件不足
    elseif(iStatus == EMissionStatus.NoCondition) then
        return GString.StringWithStyle(TextData.Get(20600104), "W")
    -- 需完成[/0]
    elseif(iStatus == EMissionStatus.NeedToFinish) then
        return 20600105
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return GString.StringWithStyle(TextData.Get(20600106), "W")
    end

end

local function GetNameStringStyle(iStrId, iStatus)

    -- 可接
    if(iStatus == EMissionStatus.CanAccept) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "W")
    -- 進行中
    elseif(iStatus == EMissionStatus.Undone) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "W")
    -- 可完成
    elseif(iStatus == EMissionStatus.CanComplete) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "O")
    -- 等級不足
    elseif(iStatus == EMissionStatus.NoLevel) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "W")
    -- 條件不足
    elseif(iStatus == EMissionStatus.NoCondition) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "Gray")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return GString.StringWithStyle(EventStrAll:GetText(iStrId), "O")
    end

end

--- 取主支線框框顏色
local function GetMissionTypeBorderColor(iStatus, iType)

    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition or iStatus == EMissionStatus.NoLevel) then
        return Extension.GetColor("#757575")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return Extension.GetColor("#d79050")
    -- 所有其他的 主線
    elseif(iType == 1) then
        return Extension.GetColor("#277ab8")
    -- 所有其他的 支線
    elseif(iType == 2) then
        return Extension.GetColor("#25b996")
    else
        return Extension.GetColor("#25b996")
    end

end

--- 取主支線框框顏色 用於改按鈕的狀態 Button.ChangeStateTransitionGroup 使用
local function GetMissionTypeColor(iStatus, iType)

    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition or iStatus == EMissionStatus.NoLevel) then
        return 2
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return 1
    -- 所有其他的 主線
    elseif(iType == 1) then
        return 0
    -- 所有其他的 支線
    elseif(iType == 2) then
        return 3
    else
        return 0
    end

end

--- 取章節狀態背景顏色
local function GetChapterStatusBGColor(iStatus)
    local _Color
    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition) then
        _Color = Extension.GetColor("#4e4e4e")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        _Color = Extension.GetColor("#8a4f1b")
    else
        _Color = Extension.GetColor("#8a4f1b")
    end

    _Color.a = CHAPTER_STATUSCHECK_ALPHA

    return _Color
end

--- 取章節狀態上層顏色
local function GetChapterStatusUpperColor(iStatus)
    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition) then
        return Extension.GetColor("#4e4e4e")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return Extension.GetColor("#553524")
    else
        return Extension.GetColor("#553524")
    end
end

--- 取章節框顏色
local function GetChapterFrameColor(iStatus)
    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition) then
        return Extension.GetColor("#cbcbcb")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return Extension.GetColor("#f1ce70")
    else    
        -- 其它狀態
        return Extension.GetColor("#4498d5")
    end
end

--- 取章節框背景顏色
local function GetChapterBGColor(iStatus)
    -- 條件不足
    if(iStatus == EMissionStatus.NoCondition) then
        return Extension.GetColor("#4e4e4e")
    -- 已完成
    elseif(iStatus == EMissionStatus.Done) then
        return Extension.GetColor("#d38d4f")
    else    
        -- 其它狀態
        return Extension.GetColor("#2578b5")
    end
end

---完成程度取得對應圖片
local function MissionCompleteSprite(iEventTipType) 

    if iEventTipType == EMissionStatus.CanAccept then
        return "Icon102000"
    elseif iEventTipType == EMissionStatus.Undone then
        return "Icon102001"
    elseif iEventTipType == EMissionStatus.CanComplete then
        return "Icon102002"
    end

end

--- 取主支線字串
local function GetMissionTypeString(iType)

    if(iType == EMissionType.Main) then
        return TextData.Get(20600107)
    elseif(iType == EMissionType.Branch) then
        return TextData.Get(20600108)
    elseif(iType == EMissionType.Special) then
        return TextData.Get(20600109)
    end

end

---前端抓到的UI放這裡
local function InitialUI()

    -- 右邊內容部分會用到的原件
    this.m_MissionTitle = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_MissionTitle")
    this.m_MissionStatus = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_MissionStatus")
    this.m_MissionType = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_MissionType") 
    this.m_MissionDetails = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_MissionDetails")
    this.m_MissionTitleChapter = this.m_ViewRef.m_Dic_Trans:Get("&Image_Chapter")
    this.m_ItemGetText = this.m_ViewRef.m_Dic_TMPText:Get("&Text_ItemGet")
    this.m_MissionTypeImg = this.m_ViewRef.m_Dic_Trans:Get("&Image_MissionType"):GetComponent(typeof(Image))
    this.m_ChapterRawImage = this.m_MissionTitleChapter:Find( "RawImage" ):GetComponent("RawImage")
    this.m_MissionInfo_Canvas = this.m_ViewRef.m_Dic_Image:Get("&Panel_Mission_Info").gameObject:GetComponent("CanvasGroup")
    --this.m_MissionTab = this.m_ViewRef.m_Dic_Trans:Get("&Button_Tab_Missions")
    --this.m_MissionTab.gameObject:SetActive(false)

    this.m_GroupItemToGet = this.m_ViewRef.m_Dic_Trans:Get("&Penel_ItemsToGet")
    this.m_MissionListUnit = this.m_ViewRef.m_Dic_Trans:Get("&Unit_MissionList")
    this.m_MissionChapter = this.m_ViewRef.m_Dic_Trans:Get("&MissionChapter")
    this.m_Content = this.m_ViewRef.m_Dic_Trans:Get("&Content_MissionList"):GetComponent(typeof(ContentFitterImmediate))

    -- 這定徒步前往按鈕
    this.m_WalkBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Walk"))
    this.m_WalkBtn:ClearListener()
    this.m_WalkBtn:AddListener(EventTriggerType.PointerClick, function()
        SearchMgr.OnClickMissionData(m_SelectedMissionData)
        UIMgr.Close(ExploreDairy_Controller)
    end)

    -- 設定傳送按鈕
    this.m_TeleportBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_TP"))
    this.m_TeleportBtn:ClearListener()
    this.m_TeleportBtn:AddListener(EventTriggerType.PointerClick, function()
        --SearchMgr.OnClickMissionData(m_SelectedMissionData)
        SearchMgr.OnClickTeleport(m_SelectedMissionData)
        UIMgr.Close(ExploreDairy_Controller)
    end)

    -- 設定領取獎勵按鈕
    this.m_RewardBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Reward"))
    Button.ClearListener(this.m_RewardBtn)
    Button.AddListener(this.m_RewardBtn, EventTriggerType.PointerClick, function()
        local _AllChapters = MissionMgr.GetAllChapter()
    
        if(_AllChapters[_ShowChapterIdx] ~= nil) then
            SendProtocol_003._015(m_NowOnClickChapter)
        end
    end)

    this.m_RewardPlusPoints = this.m_ViewRef.m_Dic_Trans:Get("&PlusPoints")
    this.m_RewardPlusPointsText = this.m_RewardPlusPoints:Find("Text_Points"):GetComponent(typeof(TMPro.TMP_Text))

    this.m_GObj_GroupButton = GroupButton.New(this.m_ViewRef.m_Dic_Trans:Get("&GroupBtn_ChapterStatus"))
    this.m_GObj_GroupButton:ReacquireAllButtonsUnderGroup()
    for i = 1, GroupButton.GetCount(this.m_GObj_GroupButton) do
        this.m_GObj_GroupButton:AddListenerByIndex(
            i,
            EventTriggerType.PointerClick,
            function()
                MissionBook_Controller.SetShowChapters(i)
                if i == EMissionPage.SpecialMission and not Button.IsDisable(this.m_GObj_GroupButton[i]) then
                    MissionBook_Controller.ChapterClick(1)
                    this.m_ScrollView_ChapterList[1].m_GObj.gameObject:SetActive(false)                    
                    MissionBook_Controller.MissionClick(this.m_ScrollView_MissionList[1])
                end
            end
        )
    end

end

local _isInit = true
---初始化右邊任務列表
local function InitialScrollBar()
    this.m_GObj_MissionList = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_MissionList")
    this.m_ScrollView_ChapterList = {}
	--ScrollView.Init( this.m_GObj_MissionList.gameObject, false, this.m_MissionChapter.gameObject, this.GetMaxCountInScrollView, this.AfterReuseItemInit, this.AfterReuseItemIndexUpdate, true)

    -- 將所有章節產出
    for i = 1, MissionMgr.GetChapterCount() do

        -- 章節列表空間開出來
        this.m_ScrollView_ChapterList[i] = {}

        -- 產生的物件
        local _ItemObject = nil
        local _CopyFrom = this.m_MissionChapter.gameObject:GetComponent("RectTransform")

        -- 第一個 用本人
        if i == 1 then
            
            _ItemObject = _CopyFrom

        -- 其他 產生物件
        else

            _ItemObject = GameObject.Instantiate(_CopyFrom)

        end

        -- 設定物件基本資料
        _ItemObject.name = "Chapter_"..i
        _ItemObject:SetParent(this.m_ViewRef.m_Dic_Trans:Get("&Content_MissionList"))
        _ItemObject.localScale = Vector3.one
        _ItemObject.localPosition = Vector3.zero
        CHAPTER_OBJECT_HEIGHT = _ItemObject.rect.height

        this.m_ScrollView_ChapterList[i].m_GObj = _ItemObject
        this.m_ScrollView_ChapterList[i].m_Index = i

        MissionBook_Controller.InitChapterItem(this.m_ScrollView_ChapterList[i])

    end

    local _ChapterMissionData = MissionMgr.GetMissionTable()
    local _MostMissionChapter = MissionMgr.WhoGotTheMostMission()
    local _MostMissionChapterCounter = table.Count(_ChapterMissionData[_MostMissionChapter])
    -- 先產出最多數量的任務
    for i = 1, _MostMissionChapterCounter do

        -- 任務列表空間開出來 這家夥會被重複利用
        -- 不像章節的一樣有多少開多少 任務會比較多所以會重複使用這幾個
        this.m_ScrollView_MissionList[i] = {}

        -- 產生的物件
        local _ItemObject = nil
        local _CopyFrom = this.m_MissionListUnit.gameObject:GetComponent("RectTransform")

        -- 第一個 用本人
        if i == 1 then
            
            _ItemObject = _CopyFrom

        -- 其他 產生物件
        else

            _ItemObject = GameObject.Instantiate(_CopyFrom)

        end

        -- 設定物件基本資料
        _ItemObject.name = "Mission_"..i
        _ItemObject:SetParent(this.m_ViewRef.m_Dic_Trans:Get("&Content_MissionList"))
        _ItemObject.localScale = Vector3.one
        _ItemObject.localPosition = Vector3.zero
        MISSION_OBJECT_HEIGHT = _ItemObject.rect.height

        this.m_ScrollView_MissionList[i].m_GObj = _ItemObject
        this.m_ScrollView_MissionList[i].m_Index = i

        MissionBook_Controller.InitMissionItem(this.m_ScrollView_MissionList[i])
        
    end

    for _Key, _Value in pairs(this.m_ScrollView_MissionList) do

        -- 改一下名字 教學要用
        _Value.m_Unit.m_MainButtonObj.name = "MissionButton_".._Key
        _Value.m_Unit.m_GObj_TraceToggle:Find( "Button_Check" ).name = "Button_Check_".._Key

    end

    MissionBook_Controller.UpdateMissionItem()

    -- 開啟時要在第一頁
    this.m_GObj_GroupButton:OnPointerClickByIndex(EMissionPage.NowChapter)

    -- 右邊的要是目前章節可以顯示的第一個
    local _UnDoneChapter = MissionMgr.GetUnDoneChapter()
    local _DoneChapter = MissionMgr.GetDoneChapter()

    ---後續開啟頁面時 就會再選擇一次章節 此階段可不必選取章節 先關閉測試
    --MissionBook_Controller.ChapterClick(_UnDoneChapter[1].m_MissionChapter)

    if(table.IsNullOrEmpty(_DoneChapter)) then

        this.m_GObj_GroupButton[EMissionPage.CompleteChapter]:SetDisable()

    end

end

--- 用任務Id打開任務介面
---@param iData 任務資料結構
---@param iEMissionUIDefaultPage EMissionUIDefaultPage 任務介面開啟後要點去哪個介面
function MissionBook_Controller.OpenMissionByData(iData,iEMissionUIDefaultPage)

    MissionBook_Controller.SetMissionUIDefaultPage(iEMissionUIDefaultPage)
    MissionMgr.SetAllMissionStatus(true)
    this.UpdateAllChapterInfo()
    local _GetDataSuccess = false
    if iEMissionUIDefaultPage == EMissionUIDefaultPage.Chapter then
         -- 檢查一下上次點的是不是同一個
        if(iData.Kind ~= m_LastClickChapter) then
            MissionBook_Controller.ChapterClick(iData.Kind)
            _GetDataSuccess = true
        end
    elseif iEMissionUIDefaultPage == EMissionUIDefaultPage.Mission then

        ---必須先點開對應的章節
        MissionBook_Controller.ChapterClick(iData.Kind)
        for i = 1, table.Count(this.m_ScrollView_MissionList) do
            if(this.m_ScrollView_MissionList[i].m_Data) then
                
                if(this.m_ScrollView_MissionList[i].m_Data.FlagID == iData.FlagID) then
                    MissionBook_Controller.MissionClick(this.m_ScrollView_MissionList[i])
                    MissionBook_Controller.Redirect(iData.Kind,i-1) 
                    _GetDataSuccess = true
                end
            end
        end
    end

    if _GetDataSuccess == false then
        ---甚麼都找不到時 使用默認的方式開啟介面
        MissionBook_Controller.SetMissionUIDefaultPage(EMissionUIDefaultPage.UnClaim)
    end
   
end

---重新導向章節任務位置
function MissionBook_Controller.Redirect(iChapter,iMissionDataIndex)
    local _ChapterY = iChapter * CHAPTER_OBJECT_HEIGHT
    local _MissionY = iMissionDataIndex * MISSION_OBJECT_HEIGHT
    
    if _ChapterY == 0 and _MissionY == 0 then
        this.m_Content.transform.localPosition = Vector3(this.m_Content.transform.localPosition.x,0,0)
    else
        this.m_Content.transform.localPosition = Vector3(this.m_Content.transform.localPosition.x,_ChapterY + _MissionY,0)
    end
end
--- 拿完章節獎勵後需要執行
---@param number iChapter 哪個章節
function MissionBook_Controller.DoAfterGettingChapterRewards(iChapter)

    -- 把字串改一改
    local _GetPrizeStr = TextData.Get(20600119)
    this.m_ScrollView_ChapterList[iChapter].m_Unit.m_Condition:GetComponent( typeof( TMPro.TextMeshProUGUI ) ).text = _GetPrizeStr

    -- 開起到完成章節
    this.m_GObj_GroupButton:OnPointerClickByIndex(EMissionPage.CompleteChapter)
end

function MissionBook_Controller.Init(iController)

    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --設定完UI要準備塞資料
    --MissionMgr.SetAllMissionStatus()

    InitialUI()
    --InitMissionTab()

    InitialScrollBar()

    _isInit = false
end

---開啟頁籤
function MissionBook_Controller.OpenTab(iEMissionType)

    MissionMgr.SwitchMissionTab(iEMissionType) 
    if this.m_ScrollView_ChapterList then

        local _MissionData = MissionMgr.GetSortedMission(1)
        this.SetMissionInfo(_MissionData)
        
        --ScrollView.UpdateToFirst(this.m_ScrollView_ChapterList)
    end
end

local function UpdateButtonState(iChapterType, iGetChapterFunc)
    if table.IsNullOrEmpty(iGetChapterFunc()) then
        this.m_GObj_GroupButton[iChapterType]:SetDisable()
    else
        this.m_GObj_GroupButton[iChapterType]:SetEnable()
    end
end

---開啟介面
function MissionBook_Controller.Open(iParam)
    if not _isInit then
        --設定完UI要準備塞資料
        MissionMgr.SetAllMissionStatus(true)
        --InitMissionTab()
        GroupButton.OnPointerClickByIndex(this.m_GObj_GroupButton,
        EMissionPage.NowChapter)
        this.UpdateAllChapterInfo()

        -- 檢查一下需不需要把完成章節變灰色的
        UpdateButtonState(EMissionPage.CompleteChapter, MissionMgr.GetDoneChapter)
        UpdateButtonState(EMissionPage.SpecialMission, MissionMgr.GetSpecialMission)
        
        if _MissionUIDefaultPage == EMissionUIDefaultPage.UnClaim then
            local _UnDoneChapter = MissionMgr.GetUnDoneChapter()
            this.ChapterClick(_UnDoneChapter[1].m_MissionChapter)
            this.Redirect(0,0)
        end
    end
    
    return true
end

---完成任務可獲得物品
this.m_ItemToGet_List = {}
---設置任務資訊
function MissionBook_Controller.SetMissionInfo(iMission)

    -- 是數字的話轉一下 不是的話直接填
    if(type(iMission.TitleStrID) == "number") then

        this.m_MissionTitle.text = EventStrAll:GetText(iMission.TitleStrID)
        -- 如果是進行中 內容要給步驟提示
        if(iMission.Finish == EMissionStatus.Undone) then

            this.m_MissionDetails.text = EventStrAll:GetText(iMission.TipStrID)

        else

            this.m_MissionDetails.text = EventStrAll:GetText(iMission.OutlineStrID)

        end

        this.m_ItemGetText.text = TextData.Get(20600111)

        this.m_MissionTitleChapter.gameObject:SetActive(false)

    else

        this.m_MissionTitle.text = iMission.ChapterNumber.."  "..iMission.TitleStrID
        this.m_MissionDetails.text = iMission.OutlineStrID

        local _Chapters = MissionMgr.GetAllChapter()
        local _String = "Loading".._Chapters[m_NowOnClickChapter].m_ChampterImg

        -- 載入圖片
        if(_Chapters[m_NowOnClickChapter].m_ChampterImg < 10) then

            _String = "Loading0" .._Chapters[m_NowOnClickChapter].m_ChampterImg

        end

        -- 載入章節圖片
        this.m_ChapterRawImage.texture = this.m_ScrollView_ChapterList[m_NowOnClickChapter].m_Unit.m_MainImg.texture
        --TextureMgr.Load(_String, false, function(_iSprite)
        --    this.m_ChapterRawImage.texture = _iSprite
        --end)

        this.m_ItemGetText.text = TextData.Get(20600112)

        this.m_MissionTitleChapter.gameObject:SetActive(true)

    end

    if(iMission.StatusStr ~= nil) then

        local _BorderLineColor = {}
        local _StatusString = {}
        --this.m_MissionStatus.text = iMission.StatusStr

        if(iMission.Finish ~= nil) then
            _BorderLineColor = GetMissionTypeBorderColor(iMission.Finish, iMission.Classity)
            _StatusString = iMission.StatusStr --GetStatusString(iMission.Finish)
            _StatusString = GString.GetTextWithSize(_StatusString, 28)
             
        else
            _BorderLineColor = GetMissionTypeBorderColor(EMissionStatus.CanAccept, 1)
            _StatusString = GString.StringWithStyle(iMission.StatusStr, "BO_01") 
            _StatusString = GString.GetTextWithSize(_StatusString, 32)
            
        end
        this.m_MissionStatus.text = _StatusString
        this.m_MissionTypeImg.color = _BorderLineColor
    end

    if(iMission.Type ~= nil) then

        this.m_MissionType.text = iMission.Type

    end

    -- 任務物品
    --- 物品排序
    local _SortItem = ExploreDairy_Model.SortShowReward(iMission.Item)
    -- 多少是真正要的
    local _ActualItemNumber = 0
    for _k, _v in pairs(_SortItem) do

        if(_v.m_Value > 0) then
            _ActualItemNumber = _ActualItemNumber + 1
        end

    end
    
    -- 把沒用的隱形掉
    for k,v in pairs(this.m_ItemToGet_List) do 

        if(k > _ActualItemNumber) then
            this.m_ItemToGet_List[k].gameObject:SetActive(false)
        end
        
    end

    -- 任務物品排序
    for k,v in pairs(_SortItem) do 
        if v.m_Value > 0 then
            if v.m_Kind ~= EMissionPrizeKind.Item and v.m_Kind ~= EMissionPrizeKind.ExplorePoint and v.m_Kind ~= EMissionPrizeKind.SelfPickItem then
                if(this.m_ItemToGet_List[k] ~= nil) then

                    this.m_ItemToGet_List[k].gameObject:SetActive(true)
                    this.m_ItemToGet_List[k]:RefreshIcon(MissionMgr.m_KindIDToItemID[v.m_Kind])

                else

                    this.m_ItemToGet_List[k] = IconMgr.NewItemIcon( MissionMgr.m_KindIDToItemID[v.m_Kind], this.m_GroupItemToGet, 110, function() end, false )

                end
                -- 圖片
				SpriteMgr.Load( ExploreDairy_Model.RewardIconString(v.m_Kind), this.m_ItemToGet_List[k].m_Image_Icon)
            elseif v.m_Kind == EMissionPrizeKind.ExplorePoint then
                -- 顯示點數
                this.m_RewardPlusPoints.gameObject:SetActive(true)

                -- 領獎分數
                this.m_RewardPlusPointsText.text =  "+" .. v.m_Value
            else
                if(this.m_ItemToGet_List[k] ~= nil) then

                    this.m_ItemToGet_List[k].gameObject:SetActive(true)
                    this.m_ItemToGet_List[k]:RefreshIcon(v.m_Idx)

                else
                    this.m_ItemToGet_List[k] = IconMgr.NewItemIcon( v.m_Idx, this.m_GroupItemToGet, 110, function() end, false )
                end
            end

            if(v.m_Kind ~= EMissionPrizeKind.ExplorePoint) then
                this.m_ItemToGet_List[k]:SetCount( v.m_Value )
                this.m_ItemToGet_List[k]:SetClickTwice(false)
            end

        end    
    end

    ---演出右側任務資訊 滑入+淡入效果
    ---在初始化時 UI的最終布局尚未確定 所以改成再第一次需要這個演出時 設定對應演出起點終點
    ---如果此時尚未設定滑動的起點終點 表示是第一次演出 取得滑入的 起點/終點位置
    if _End_RightMissionPanel == nil then
        ---設定初始位置 終點位置
        _End_RightMissionPanel = this.m_MissionInfo_Canvas.gameObject.transform.localPosition
        _Start_RightMissionPanel = _End_RightMissionPanel + Vector3(_Length_Delta,0,0)
    end

    this.m_MissionInfo_Canvas.gameObject.transform.localPosition = _Start_RightMissionPanel
    LeanTween.value( this.m_MissionInfo_Canvas.gameObject, System.Action_float(
        function(iValue)
            this.m_MissionInfo_Canvas.gameObject.transform.localPosition = _Start_RightMissionPanel+ (_End_RightMissionPanel-_Start_RightMissionPanel)*iValue
            this.m_MissionInfo_Canvas.alpha = iValue
            end), 0, 1, 0.5)

end

local m_InitDataCount = 0
--region scrollView相關參數
function MissionBook_Controller.GetMaxCountInScrollView()
    if _isInit then
        if m_InitDataCount <= 0 then
            m_InitDataCount = 10
        end
        return m_InitDataCount
    else
        return MissionMgr.GetChapterCount()
    end

end
--- 查詢目前點擊的章節
function MissionBook_Controller.GetNowClickChapter()
    return m_NowOnClickChapter
end

function MissionBook_Controller.AfterReuseItemInit(iItem, iRowIdx)
    if iItem == nil then
        return
    end
    --this.SetMissionListRowItem(iItem)
    MissionBook_Controller.SetChapterItem(iItem)
end

function MissionBook_Controller.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    if iItem == nil then
        return
    end
    --this.SetMissionListRowItem(iItem)
    
    local _UnDoneChapter = MissionMgr.GetUnDoneChapter()
    local _String = "Loading".._UnDoneChapter[iItem.m_Index].m_ChampterImg

    if(_UnDoneChapter[iItem.m_Index].m_ChampterImg < 10) then

        _String = "Loading0" .._UnDoneChapter[iItem.m_Index].m_ChampterImg

    end

    TextureMgr.Load(_String, false, function(_iSprite)
        iItem.m_Unit.m_MainImg.texture = _iSprite
        end)

    this.m_Content:ForceUpdateContentSize()

    iItem.m_GObj:SetActive(iRowIdx <= this.GetMaxCountInScrollView())
end

--- 設定章節
---@param table iItem 要設定的按鈕
function MissionBook_Controller.InitChapterItem(iItem)

    local _Chapters = MissionMgr.GetAllChapter()

    iItem.m_Unit = {}
    local _MissionChapter = {}
    local _ImageBG = iItem.m_GObj.transform:Find( "Image_BG" )
    _MissionChapter.m_MainBtn = Button.New(iItem.m_GObj)
    _MissionChapter.m_Condition = iItem.m_GObj.transform:Find( "Text_Condition" )
    _MissionChapter.m_MainImg = _ImageBG:Find( "RawImage" ):GetComponent("RawImage")
    _MissionChapter.m_Name = iItem.m_GObj.transform:Find( "Text_Name" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _MissionChapter.m_Number = iItem.m_GObj.transform:Find( "Text_Number" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _MissionChapter.m_Arrow = iItem.m_GObj.transform:Find( "Image_Arrow" ).gameObject
    _MissionChapter.m_SelectedBG = iItem.m_GObj.transform:Find( "Image_BG_Light" ).gameObject
    _MissionChapter.m_StatusCheck = iItem.m_GObj.transform:Find( "Image_Status_Check" ).gameObject
    _MissionChapter.m_StatusBG = _MissionChapter.m_StatusCheck:GetComponent(typeof(Image))
    _MissionChapter.m_StatusUpper = _MissionChapter.m_StatusCheck.transform:Find("Image_HighLight"):GetComponent(typeof(Image))
    _MissionChapter.m_ChapterNumberImage = iItem.m_GObj.transform:Find("Image_ChapterNumber"):GetComponent(typeof(Image))
    _MissionChapter.m_MainFrame = iItem.m_GObj.transform:Find("Image_Frame"):GetComponent(typeof(Image))
    _MissionChapter.m_ChapterNumber = iItem.m_GObj.transform:Find( "Text_ChapterNumber"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _MissionChapter.m_IsDone = false
    _MissionChapter.m_IsOpen = false
    _MissionChapter.m_ChapterColor = GetChapterFrameColor(EMissionStatus.CanAccept)

    --- 確定章節編號，若無則使用特殊章節
    local _ChapterGetIndex = _Chapters[iItem.m_Index] and iItem.m_Index or MissionMgr.SPECIAL_CHAPTER_IDX
    local _ChapterDataIndex = _Chapters[iItem.m_Index] and iItem.m_Index or MissionMgr.SPECIAL_DATA_CHAPTER_IDX

    -- 點擊 event
    _MissionChapter.m_MainBtn:AddListener(EventTriggerType.PointerClick, function()
        MissionBook_Controller.ChapterClick(iItem.m_Index)
    end)

    -- 啟動章節條件顯示 查動標
    if(PlayerData_Flags[EFlags.Static].IsHaveFlag(_Chapters[_ChapterGetIndex].m_OpenFlag) or _Chapters[_ChapterGetIndex].m_OpenFlag == 0) then

        _MissionChapter.m_Condition.gameObject:SetActive(false)

    else

        _MissionChapter.m_Condition.gameObject:SetActive(true)

    end
    

    -- 載入圖片需要的字串準備
    local _String = "Loading".._Chapters[_ChapterGetIndex].m_ChampterImg

    -- 載入圖片
    if(_Chapters[_ChapterGetIndex].m_ChampterImg < 10) then

        _String = "Loading0" .._Chapters[_ChapterGetIndex].m_ChampterImg

    end

    -- 載入章節圖片
    TextureMgr.Load(_String, false, function(_iSprite)
        _MissionChapter.m_MainImg.texture = _iSprite
    end)

    -- 章節名
    local _NameStr = TextData.Get(_Chapters[_ChapterGetIndex].m_ChapterName)
    _MissionChapter.m_Name.text = _NameStr

    -- 第幾章節
    local _ChapterNumberStr = TextData.Get(_Chapters[_ChapterGetIndex].m_ChapterNumberID)
    _MissionChapter.m_ChapterNumber.text = _ChapterNumberStr

    -- 章節數量
    local _ChapterMissionData = MissionMgr.GetMissionTable()
    local _TotalNumber = table.Count(_ChapterMissionData[_ChapterGetIndex])
    local _DoneNumber = 0

    if _ChapterMissionData[_ChapterDataIndex] ~= nil then
        -- 找完成數量
        for key, value in pairs(_ChapterMissionData[_ChapterDataIndex]) do
            if(value.Finish == EMissionStatus.Done) then
                _DoneNumber = _DoneNumber + 1
            end
        end
    else
        D.LogError("第 ".._ChapterDataIndex.." 章節沒有填任務！！！")
    end

    -- 串字串
    local _NumberStr = GString.Format("[{0}/{1}]" , _DoneNumber, _TotalNumber)

    -- 放入字串
    _MissionChapter.m_Number.text = _NumberStr

    -- 做好的東西存起來
    iItem.m_Unit = _MissionChapter

end

--- 章節被點擊
---@param number iChapter 點擊的章節按鈕
function MissionBook_Controller.ChapterClick(iChapter)

    ---淡入演出的canvas table
    local _EffectObjectTable = {}

    -- 取得所有章節任務資料
    local _ChapterMissionData = MissionMgr.GetMissionTable()

    -- 整理一下此章節
    MissionMgr.ChapterMissionSorting(iChapter)

    ---更新現在點的章節
    m_NowOnClickChapter = iChapter

    ---任務章節編號
    local _ShowChapterIdx = iChapter
    ---串表對應章節編號
    local _ShowChapterDataIdx = iChapter
    if m_NowClickPageType == EMissionPage.SpecialMission then
        _ShowChapterIdx = MissionMgr.SPECIAL_CHAPTER_IDX
        _ShowChapterDataIdx = MissionMgr.SPECIAL_DATA_CHAPTER_IDX
    end

    -- 確認有資料
    if(_ChapterMissionData[_ShowChapterDataIdx]) then

        this.m_ScrollView_ChapterList[iChapter].m_Unit.m_IsOpen = not this.m_ScrollView_ChapterList[iChapter].m_Unit.m_IsOpen

        ---如果需要演出淡入效果 蒐集物件canvas
        if m_ShowDisplayEffectInChapterArea then
            local _Canvas = this.m_ScrollView_ChapterList[iChapter].m_GObj.gameObject:GetComponent("CanvasGroup")
            table.insert(_EffectObjectTable,_Canvas)
        end

        local _MissionCount = table.Count(_ChapterMissionData[_ShowChapterDataIdx])

        local _MostMissionChapter = MissionMgr.WhoGotTheMostMission()
        local _MostMissionChapterCounter = table.Count(_ChapterMissionData[_MostMissionChapter])

        -- 全部先丟到最後面
        for i = 1, _MostMissionChapterCounter do
            this.m_ScrollView_MissionList[i].m_GObj.transform:SetSiblingIndex( this.m_Content.transform.childCount - 1)
            this.m_ScrollView_MissionList[i].m_GObj.gameObject:SetActive(false)
        end

        -- 上個按的按鈕縮小
        if(this.m_ScrollView_MissionList[m_LastClickMission]) then

            --this.m_ScrollView_MissionList[m_LastClickMission].m_GObj.transform.sizeDelta = MISSION_UNCLICK_SIZE
            this.m_ScrollView_MissionList[m_LastClickMission].m_Unit.m_SelectImg:SetActive(false)

        end

        this.m_ScrollView_ChapterList[iChapter].m_Unit.m_Arrow.transform.localRotation = Quaternion.Euler(ARROW_CLOSE)

        -- 被選中都要亮
        this.m_ScrollView_ChapterList[iChapter].m_Unit.m_SelectedBG:SetActive(true)

        -- 要展開在排
        if(this.m_ScrollView_ChapterList[iChapter].m_Unit.m_IsCanNotOpen) then
            if(this.m_ScrollView_ChapterList[iChapter].m_Unit.m_IsOpen) then

                -- 存下 ChapterNumberImage 原本的顏色
                this.m_ScrollView_ChapterList[iChapter].m_Unit.m_ChapterColor = this.m_ScrollView_ChapterList[iChapter].m_Unit.m_ChapterNumberImage.color
                this.m_ScrollView_ChapterList[iChapter].m_Unit.m_ChapterNumberImage.color = GetChapterBGColor(EMissionStatus.Done)

                -- 排到要出現的位置
                for i = 1, _MissionCount do
                    this.m_ScrollView_MissionList[i].m_GObj.transform:SetSiblingIndex( iChapter - 1 + i )
                    this.m_ScrollView_MissionList[i].m_GObj.gameObject:SetActive(true)
                    local _Mission = MissionMgr.GetPickMission(_ShowChapterDataIdx, i)
                    local _Str = GetNameStringStyle(_Mission.TitleStrID, _Mission.Finish)
                    
                    if(_Str~="") then
                        this.m_ScrollView_MissionList[i].m_Unit.m_Name.text = _Str
                    end
                    this.m_ScrollView_MissionList[i].m_Data = _Mission

                    ---如果需要演出淡入效果 蒐集物件canvas
                    if m_ShowDisplayEffectInChapterArea then
                        local _Canvas = this.m_ScrollView_MissionList[i].m_GObj.gameObject:GetComponent("CanvasGroup")                        
                        table.insert(_EffectObjectTable,_Canvas)
                    end
                end

                this.m_ScrollView_ChapterList[iChapter].m_Unit.m_Arrow.transform.localRotation = Quaternion.Euler(ARROW_OPEN)

            end

        end

        local _Chapters = MissionMgr.GetAllChapter()

        -- 如果這次點擊的和上次點擊的不是同一個
        if(m_NowOnClickChapter ~= m_LastClickChapter and m_LastClickChapter ~= 0)then

            if(not this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_IsDone) then
                this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_SelectedBG:SetActive(false)
            end
            this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_ChapterNumberImage.color = this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_ChapterColor
            this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_Arrow.transform.localRotation = Quaternion.Euler(ARROW_CLOSE)
            this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_IsOpen = false
            
        end

        local _SetShowMissionInfo = {}

        _SetShowMissionInfo.TitleStrID = TextData.Get(_Chapters[_ShowChapterIdx].m_ChapterName or "")
        _SetShowMissionInfo.TitleStrID = TextData.Get(_Chapters[_ShowChapterIdx].m_ChapterName or "")
        _SetShowMissionInfo.OutlineStrID = TextData.Get(_Chapters[_ShowChapterIdx].m_ChapterInfo or "")
        _SetShowMissionInfo.ChapterNumber = TextData.Get(_Chapters[_ShowChapterIdx].m_ChapterNumberID or "")
        _SetShowMissionInfo.StatusStr = this.m_ScrollView_ChapterList[iChapter].m_Unit.m_Number.text
        _SetShowMissionInfo.Item = {}

        for key, value in pairs(_Chapters[_ShowChapterIdx].m_UpgradeEffect) do

            _SetShowMissionInfo.Item[key] = {}
            _SetShowMissionInfo.Item[key].m_Value = value.m_Value
            _SetShowMissionInfo.Item[key].m_Idx = value.m_Idx
            _SetShowMissionInfo.Item[key].m_Kind = value.m_Kind

        end

        -- 移動到目的地
        this.m_WalkBtn.gameObject:SetActive(false)

        -- 關掉傳送
        this.m_TeleportBtn.gameObject:SetActive(false)

        -- 開啟領獎
        this.m_RewardBtn.gameObject:SetActive(true)

        -- 顯示點數
        this.m_RewardPlusPoints.gameObject:SetActive(true)

        -- 領獎分數
        this.m_RewardPlusPointsText.text = "+" .. _Chapters[_ShowChapterIdx].m_ReceivePoints

        if(m_ShowCollectRewardBtn) then

            this.m_RewardBtn.gameObject:SetActive(true)
            if(this.m_ScrollView_ChapterList[m_NowOnClickChapter].m_Unit.m_IsDone)then
                this.m_RewardBtn:SetEnable()
            else
                this.m_RewardBtn:SetDisable()
            end
            
        else

            this.m_RewardBtn.gameObject:SetActive(false)

        end

        this.SetMissionInfo(_SetShowMissionInfo)
        
    end

    -- 更新上一次點的章節是哪個
    m_LastClickChapter = m_NowOnClickChapter

    MissionBook_Controller.UpdateMissionItem()
    
    ---如果需要演出淡入效果 執行演出
    ---企劃希望0.5秒左右演完淡入效果
    ---相鄰物件的alpha值差異
    local _DeltaAlpha = 0.2
    if m_ShowDisplayEffectInChapterArea == true then
        
        LeanTween.value( this.m_ScrollView_MissionList[iChapter].m_GObj.gameObject, System.Action_float(
            function(iValue)

                for i = 1, table.Count(_EffectObjectTable) do
                    local _alpha = (i-1)*(-_DeltaAlpha) + iValue*( (table.Count(_EffectObjectTable)-1)*_DeltaAlpha +1)
                    _EffectObjectTable[i].alpha = _alpha
                end
            
            end), 0, 1, 0.5)
    end
end

--- 更新章節資料
function MissionBook_Controller.UpdateAllChapterInfo()

    -- 更新章節數量
    local _ChapterMissionData = MissionMgr.GetMissionTable()
    local _AllChapters = MissionMgr.GetAllChapter()
    local _MissionByStaticFlag = MissionMgr.GetMissionByStaticFlag()
    local _FirstCotent = 0

    for key, value in pairs(this.m_ScrollView_ChapterList) do

        if(not value.m_Unit.m_IsDone) then

            --- 確定章節編號，若無則使用特殊章節
            local _ChapterIndex = _AllChapters[value.m_Index] and value.m_Index or MissionMgr.SPECIAL_CHAPTER_IDX
            local _ChapterDataIndex = _ChapterMissionData[value.m_Index] and value.m_Index or MissionMgr.SPECIAL_DATA_CHAPTER_IDX

            local _TotalNumber = table.Count(_ChapterMissionData[_ChapterDataIndex])
            local _DoneNumber = 0

            -- 找完成數量
            for _key, _value in pairs(_ChapterMissionData[_ChapterDataIndex]) do

                if(_value.Finish == EMissionStatus.Done) then

                    _DoneNumber = _DoneNumber + 1

                end

            end

            -- 串字串
            local _NumberStr = GString.Format("[{0}/{1}]" , _DoneNumber, _TotalNumber)

            -- 放入字串
            value.m_Unit.m_Number.text = _NumberStr

            -- 章節所有任務做完了
            if(_DoneNumber == _TotalNumber) then
                value.m_Unit.m_StatusCheck:SetActive(true)
                value.m_Unit.m_StatusBG.color = GetChapterStatusBGColor(EMissionStatus.Done)
                value.m_Unit.m_StatusUpper.color = GetChapterStatusUpperColor(EMissionStatus.Done)
                value.m_Unit.m_SelectedBG:SetActive(true)
                value.m_Unit.m_IsDone = true
            end

            -- 啟動章節條件顯示 查動標
            if(PlayerData_Flags[EFlags.Static].IsHaveFlag(_AllChapters[_ChapterIndex].m_OpenFlag) or _AllChapters[_ChapterIndex].m_OpenFlag == 0) then

                value.m_Unit.m_Condition.gameObject:SetActive(false)

                value.m_Unit.m_ChapterNumberImage.color = GetChapterBGColor(EMissionStatus.CanAccept)
                value.m_Unit.m_ChapterColor = GetChapterBGColor(EMissionStatus.CanAccept)

                value.m_Unit.m_MainFrame.color = GetChapterFrameColor(EMissionStatus.CanAccept)

                value.m_Unit.m_StatusCheck:SetActive(false)
                -- 完成了
                if(value.m_Unit.m_IsDone) then
                    value.m_Unit.m_StatusCheck:SetActive(true)
                    value.m_Unit.m_ChapterNumberImage.color = GetChapterBGColor(EMissionStatus.Done)
                    value.m_Unit.m_ChapterColor = GetChapterBGColor(EMissionStatus.Done)

                    -- 顯示領獎
                    value.m_Unit.m_Condition.gameObject:SetActive(true)
                    if(PlayerData_Flags[EFlags.Static].IsHaveFlag(_AllChapters[_ChapterIndex].m_PrizeFlag)) then
                        
                        local _GetPrizeStr = TextData.Get(20600119)
                        value.m_Unit.m_Condition:GetComponent( typeof( TMPro.TextMeshProUGUI ) ).text = _GetPrizeStr
                        
                    else

                        local _GetPrizeStr = TextData.Get(20600121)
                        value.m_Unit.m_Condition:GetComponent( typeof( TMPro.TextMeshProUGUI ) ).text = _GetPrizeStr

                    end
                -- 進行中
                elseif(_FirstCotent == 0) then
                    value.m_Unit.m_StatusCheck:SetActive(false)
                    value.m_Unit.m_Condition.gameObject:SetActive(true)
                    local _GetPrizeStr = TextData.Get(20600120)
                    value.m_Unit.m_Condition:GetComponent( typeof( TMPro.TextMeshProUGUI ) ).text = _GetPrizeStr
                    _FirstCotent = 1

                end

                value.m_Unit.m_IsCanNotOpen = true
            else

                value.m_Unit.m_Condition.gameObject:SetActive(true)

                value.m_Unit.m_ChapterNumberImage.color = GetChapterBGColor(EMissionStatus.NoCondition)
                value.m_Unit.m_ChapterColor = GetChapterBGColor(EMissionStatus.NoCondition)
                local _NeedMissionData = _MissionByStaticFlag[_AllChapters[_ChapterIndex].m_OpenFlag]
                local _NameStr = EventStrAll:GetText(_NeedMissionData.TitleStrID)
                local _ChapterNum = 20601000 + _NeedMissionData.Kind
                local _ChapterStr = TextData.Get(_ChapterNum)
                local _CombineStr = GString.Format(TextData.Get(20600115), _ChapterStr, _NameStr)
                value.m_Unit.m_Condition:GetComponent( typeof( TMPro.TextMeshProUGUI ) ).text = _CombineStr

                value.m_Unit.m_MainFrame.color = GetChapterFrameColor(EMissionStatus.NoCondition)
                value.m_Unit.m_StatusCheck:SetActive(true)
                value.m_Unit.m_StatusBG.color = GetChapterStatusBGColor(EMissionStatus.NoCondition)
                value.m_Unit.m_StatusUpper.color = GetChapterStatusUpperColor(EMissionStatus.NoCondition)
                value.m_Unit.m_IsCanNotOpen = false
            end

        end

    end

end

local function ColorChange(iType)
    
    if (iType == EMissionPage.NowChapter) then
        return MissionMgr.GetUnDoneChapter(), true
    elseif (iType == EMissionPage.CompleteChapter) then
        return MissionMgr.GetDoneChapter(), false
    elseif (iType == EMissionPage.SpecialMission) then
        return MissionMgr.GetSpecialMission(), false
    end

end

--- 設定哪些章節現在要展示
---@param iNumber iType 1.NowChpater 2.UndoneChapter
function MissionBook_Controller.SetShowChapters(iType)

    local _Data

    _Data, m_ShowCollectRewardBtn = ColorChange(iType)

    -- 沒資料
    if(_Data == nil or not next(_Data)) then
        if iType == EMissionPage.CompleteChapter or iType == EMissionPage.SpecialMission then
            this.m_GObj_GroupButton[iType]:SetDisable()
        end
        return
    end

    m_NowClickPageType = iType

    if iType == EMissionPage.CompleteChapter or iType == EMissionPage.SpecialMission then
        this.m_GObj_GroupButton[iType]:SetEnable()
    end


    local _AllChapters = MissionMgr.GetAllChapter()
    local _PlayerLV = PlayerData.GetLv()
    
    for _key, _value in pairs(this.m_ScrollView_ChapterList) do

        _value.m_GObj.gameObject:SetActive(false)

    end

    if(_Data ~= nil) then

        for _key, _value in pairs(_Data) do

            if _value.m_MissionChapter then
                if(_PlayerLV >= _AllChapters[_value.m_MissionChapter].m_OpenLevel) then
                    this.m_ScrollView_ChapterList[_value.m_MissionChapter].m_GObj.gameObject:SetActive(true)
                end
            else                
                if _value.Classity == EMissionPage.SpecialMission then
                    this.m_ScrollView_ChapterList[1].m_GObj.gameObject:SetActive(true)
                end
            end
    
        end

    end

    local _ChapterMissionData = MissionMgr.GetMissionTable()
    local _MostMissionChapter = MissionMgr.WhoGotTheMostMission()
    local _MostMissionChapterCounter = table.Count(_ChapterMissionData[_MostMissionChapter])

    -- 全部先丟到最後面
    for i = 1, _MostMissionChapterCounter do
        this.m_ScrollView_MissionList[i].m_GObj.transform:SetSiblingIndex( this.m_Content.transform.childCount - 1)
        this.m_ScrollView_MissionList[i].m_GObj.gameObject:SetActive(false)
    end

    -- 關閉 mission
    if(m_LastClickChapter ~= 0)then

        this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_Arrow.transform.localRotation = Quaternion.Euler(ARROW_CLOSE)
        this.m_ScrollView_ChapterList[m_LastClickChapter].m_Unit.m_IsOpen = false

    end

end

--- 設定任務條內容
---@param table iItem 要設定的按鈕
function MissionBook_Controller.InitMissionItem(iItem)

    iItem.m_Unit = {}
    local _Mission = {}
    local _ImageBG = iItem.m_GObj.transform:Find( "img_listBG" )
    _Mission.m_Name = _ImageBG:Find( "TMP_MissionName" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _Mission.m_Tag = _ImageBG:Find( "Image_Tag" )
    _Mission.m_TagIcon = _Mission.m_Tag:Find("Image_TagIcon"):GetComponent(typeof(Image))
    _Mission.m_Status = _ImageBG:Find( "TMP_MissionStatus" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _Mission.m_MainButton = Button.New(iItem.m_GObj.transform:Find( "img_listBG" ))
    _Mission.m_BorderLine = _ImageBG:Find( "Image_Slot" ):GetComponent(typeof(Image))
    _Mission.m_Type = _ImageBG:Find( "Image_Slot" ):Find( "Text" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    _Mission.m_GObj_TraceToggle = _ImageBG:Find( "Toggle_BG" )
    _Mission.m_Button_TraceToggle = _Mission.m_GObj_TraceToggle:Find( "Button_Check" ):GetComponent("Toggle")
    _Mission.m_SelectImg = _ImageBG:Find("Image_BoarderSelect").gameObject
    _Mission.m_MainImage = _ImageBG:GetComponent(typeof(Image))
    _Mission.m_MainButtonObj = _ImageBG

    -- 做好的東西存起來
    iItem.m_Unit = _Mission
    iItem.m_Data = {}

    -- 點擊
    _Mission.m_MainButton:AddListener(EventTriggerType.PointerClick, MissionBook_Controller.MissionClick, iItem)
end

--- 任務按下
---@param number iItem 被點擊的按鈕號碼
function MissionBook_Controller.MissionClick(iItem)

    local _ShowChapterDataIdx = m_NowOnClickChapter
    if m_NowClickPageType == EMissionPage.SpecialMission then
        _ShowChapterDataIdx = MissionMgr.SPECIAL_DATA_CHAPTER_IDX
    end

    local _MissionData = MissionMgr.GetPickMission(_ShowChapterDataIdx, iItem.m_Index)

    -- 上個按的按鈕縮小
    if(this.m_ScrollView_MissionList[m_LastClickMission]) then

        this.m_ScrollView_MissionList[m_LastClickMission].m_Unit.m_SelectImg:SetActive(false)

    end

    -- 這次點擊的按鈕放大
    --iItem.m_GObj.transform.sizeDelta = MISSION_CLICK_SIZE
    iItem.m_Unit.m_SelectImg:SetActive(true)

    this.m_Content:ForceUpdateContentSize()

    -- 更新上次點擊的按鈕
    m_LastClickMission = iItem.m_Index

    -- 找有資料的話
    if(_MissionData ~= nil) then

        local _AllChapters = MissionMgr.GetAllChapter()
        local _String = this.m_ScrollView_MissionList[iItem.m_Index].m_Unit.m_Status.text
        local _MissionByStaticFlag = MissionMgr.GetMissionByStaticFlag()
        local _ChapterMissionData = MissionMgr.GetMissionTable()

        if(_MissionData.Finish == EMissionStatus.NoCondition) then
            local _NameStr = EventStrAll:GetText(_MissionByStaticFlag[_MissionData.ExFlag].TitleStrID)
            
            _String = GString.Format(TextData.Get(GetStatusString(EMissionStatus.NeedToFinish)), _NameStr)
        end

        _MissionData.StatusStr = _String
        _MissionData.Type = this.m_ScrollView_MissionList[iItem.m_Index].m_Unit.m_Type.text

        -- 關閉顯示獎勵點數
        this.m_RewardPlusPoints.gameObject:SetActive(false)

        -- 更新右邊任務內容
        this.SetMissionInfo(_MissionData)
        
        if(_MissionData.Finish ~= EMissionStatus.CanAccept) then
            _MissionData.EventID = _MissionData.m_Step.EventID
        end
        m_SelectedMissionData = _MissionData

        -- 移動到目的地
        this.m_WalkBtn.gameObject:SetActive(true)

        -- 開啟傳送
        this.m_TeleportBtn.gameObject:SetActive(true)

        if(_MissionData.Finish == EMissionStatus.Done or _MissionData.Finish == EMissionStatus.NoCondition) then

            this.m_WalkBtn:SetDisable()
            this.m_TeleportBtn:SetDisable()
            
        else

            this.m_WalkBtn:SetEnable()
            this.m_TeleportBtn:SetEnable()

        end

        -- 關閉領獎
        this.m_RewardBtn.gameObject:SetActive(false)

    end
    

end

--- 更新
function MissionBook_Controller.UpdateMissionItem()

    local _AllChapters = MissionMgr.GetAllChapter()
    local _MissionByStaticFlag = MissionMgr.GetMissionByStaticFlag()

    ---任務章節編號
    local _ShowChapterIdx = m_NowOnClickChapter
    ---串表對應章節編號
    local _ShowChapterDataIdx = m_NowOnClickChapter
    if m_NowClickPageType == EMissionPage.SpecialMission then
        _ShowChapterIdx = MissionMgr.SPECIAL_CHAPTER_IDX
        _ShowChapterDataIdx = MissionMgr.SPECIAL_DATA_CHAPTER_IDX
    end

    if(_AllChapters[_ShowChapterIdx] ~= nil) then

        local _ChapterMissionData = MissionMgr.GetMissionTable()

        for i = 1, table.Count(_ChapterMissionData[_ShowChapterDataIdx]) do
            local _Status = _ChapterMissionData[_ShowChapterDataIdx][i].Finish
            local _Type = _ChapterMissionData[_ShowChapterDataIdx][i].Classity
            local _GetStatusString = GetStatusString(_Status)

            if(_Status == EMissionStatus.NoCondition) then

                local _NameStr = EventStrAll:GetText(_MissionByStaticFlag[_AllChapters[_ShowChapterIdx].m_OpenFlag].TitleStrID)
                _GetStatusString = GString.Format(_GetStatusString, _NameStr)

            end

            --region 設定追蹤圖示

            --追蹤任務顯示勾勾
            this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.onValueChanged:RemoveAllListeners()

            if _ChapterMissionData[_ShowChapterDataIdx][i].m_bTrack then
                this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.isOn = true
            else
                this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.isOn = false
            end
            
            this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.onValueChanged:AddListener(function(isOn) 
                if _Type ~= EMissionType.Main then
                    MissionMgr.UpdateAndSaveTrackMission(_ChapterMissionData[_ShowChapterDataIdx][i].FlagID)
                    --this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.isOn = true
                else
                    MissionMgr.UpdateAndSaveTrackMission(_ChapterMissionData[_ShowChapterDataIdx][i].FlagID)
                    --this.m_ScrollView_MissionList[i].m_Unit.m_Button_TraceToggle.isOn = true
                end
                Tracing_Controller.RefreshMissionList()

            end)

            local _ShowTag = false
            -- 條件不夠 等級不足 已完成都要進入
            if(_Status == EMissionStatus.NoCondition or _Status == EMissionStatus.Done or _Status == EMissionStatus.NoLevel) then

                _ShowTag = true

            end

            this.m_ScrollView_MissionList[i].m_Unit.m_Tag.gameObject:SetActive(_ShowTag)

            --可接取任務不顯示追蹤 主任務也不顯示
            this.m_ScrollView_MissionList[i].m_Unit.m_GObj_TraceToggle.gameObject:SetActive((_Status == EMissionStatus.Undone or _Status == EMissionStatus.CanComplete) and _Type ~= EMissionType.Main)
            --endregion

            this.m_ScrollView_MissionList[i].m_Unit.m_Status.text = _GetStatusString
            this.m_ScrollView_MissionList[i].m_Unit.m_Type.text = GetMissionTypeString(_Type)

            local _BorderLineColor = GetMissionTypeColor(_Status, _Type)
            Button.ChangeStateTransitionGroup(this.m_ScrollView_MissionList[i].m_Unit.m_MainButton.m_ButtonEx, _BorderLineColor)
        end

    end

end

---設定列表資訊
function MissionBook_Controller.SetMissionListRowItem(iUnit)

    local _MissionData = MissionMgr.GetSortedMission(iUnit.m_Index)

    iUnit.m_Unit = {}
    local _MissionUnit = {}
    _MissionUnit.MainButton = Button.New(iUnit.m_GObj.transform:Find( "img_listBG" ))
    _MissionUnit.CompleteIcon = _MissionUnit.MainButton.transform:Find( "Icon_Compeletion" ):GetComponent(typeof(Image))
    _MissionUnit.TeleportBtn = Button.New(_MissionUnit.MainButton.transform:Find( "Btn_Teleport" ))
    _MissionUnit.GObj_TraceToggle = iUnit.m_GObj.transform:Find( "Toggle_BG" )
    _MissionUnit.Button_TraceToggle = _MissionUnit.GObj_TraceToggle:Find( "Button_Check" ):GetComponent("Toggle")

    if _MissionData == nil then
        return
    end

    --region 設定追蹤圖示

    --追蹤任務顯示勾勾
    _MissionUnit.Button_TraceToggle.onValueChanged:RemoveAllListeners()

    if _MissionData.m_bTrack then
        _MissionUnit.Button_TraceToggle.isOn = true
    else
        _MissionUnit.Button_TraceToggle.isOn = false
    end
    
    _MissionUnit.Button_TraceToggle.onValueChanged:AddListener(function(isOn) 
        if _MissionData.Classity ~= EMissionType.Main then
            MissionMgr.UpdateAndSaveTrackMission(_MissionData.FlagID)
        else
            _MissionUnit.Button_TraceToggle.isOn = true
        end
        Tracing_Controller.RefreshMissionList()

    end)

    --可接取任務不顯示追蹤
    _MissionUnit.GObj_TraceToggle.gameObject:SetActive(not(_MissionData.Finish == EMissionStatus.CanAccept))

    --endregion

    --設定清單任務名稱
    _MissionUnit.MainButton:SetText(EventStrAll:GetText(_MissionData.TitleStrID))
    _MissionUnit.MainButton:ClearListener()
    _MissionUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
        this.SetMissionInfo(_MissionData)
    end)

    --設定完成度圖示
    local _SpriteName = MissionCompleteSprite(_MissionData.Finish)
    SpriteMgr.Load( _SpriteName, _MissionUnit.CompleteIcon)

    --設定傳送按鈕
    _MissionUnit.TeleportBtn:ClearListener()
    _MissionUnit.TeleportBtn:AddListener(EventTriggerType.PointerClick, function()
        SearchMgr.OnClickMissionData(_MissionData)
        UIMgr.Close(MissionBook_Controller)
    end)
    iUnit.m_Unit = _MissionUnit
end

function MissionBook_Controller.SetMissionUIDefaultPage(iMissionUIDefaultPage)
    _MissionUIDefaultPage = iMissionUIDefaultPage
end

function MissionBook_Controller.Close()
    for key, value in pairs(this.m_ScrollView_ChapterList) do

        value.m_Unit.m_IsDone = false

    end
end

--endregion