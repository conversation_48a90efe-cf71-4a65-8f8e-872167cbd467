Shader "HEM/UIIconPulse (SoftMaskable)"
{
    Properties
    {
        [PerRendererData] _MainTex("Sprite Texture", 2D) = "white" { }
        _Intensity("亮度(HDR級別)", float) = 1 // 用於控制最終顏色的亮度增強值，影響整體顏色的強度

        [Header(Base Image Setting)]
        _PulseTimeScale("條紋速度", float) = 0.8 // UV條紋的呼吸速度，越大條紋呼吸越慢
        _PulsePosScale("條紋密度", Range(0, 5)) = 0.0012 // 條紋的密度，控制條紋效果的範圍和密集度

        [Header(HighLight Setting)]
        _LightIntensity("光線強度", float) = 0.5 // 光線強度，控制高光部分的亮度
        _LightWidth("光線寬度", float) = 0.2 // 光線的寬度，控制高光範圍
        _Angle("光線角度", Range(0, 360)) = 45 // 光線角度，用來定義光線掃描的角度範圍
        _ColorStart("起始光線顏色", Color) = (1, 1, 1, 1) // 起始光線顏色
        _ColorEnd("結束光線顏色", Color) = (1, 1, 1, 1) // 結束光線顏色
        _EdgeSoftness("邊緣柔和度", Range(0.01, 1.0)) = 0.2 // 控制高光邊緣柔和程度
    }

    SubShader
    {
        Tags { "RenderType" = "Transparent" "Queue" = "Transparent" }
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
            #include "Packages/com.coffee.softmask-for-ugui/Shaders/SoftMask.cginc"
            #pragma shader_feature _ SOFTMASK_EDITOR
            #pragma shader_feature_local _ SOFTMASKABLE

            struct appdata
            {
                float4 vertex : POSITION;
                fixed4 color : COLOR;
                float2 Mainuv : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float2 Mainuv : TEXCOORD0;
                float4 vertexObjPos : TEXCOORD1;
            };

            sampler2D _MainTex; // 主貼圖
            float4 _MainTex_ST; // 主要貼圖的變換
            half4 _Color; // 顏色
            half _Intensity; // 亮度增強值

            half _PulseTimeScale; // 條紋速度
            half _PulsePosScale; // 條紋密度
            half _LightIntensity; // 光線強度
            half _LightWidth; // 光線寬度
            half _Angle; // 光線角度
            half4 _ColorStart; // 起始光線顏色
            half4 _ColorEnd; // 結束光線顏色
            half _EdgeSoftness; // 邊緣柔和度

            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex); // 物體空間到剪輯空間的轉換
                o.Mainuv = TRANSFORM_TEX(v.Mainuv, _MainTex); // UV變換
                o.vertexObjPos = v.vertex; // 保存物體空間的位置
                o.color = v.color; // 顏色
                return o;
            }

            // 曲線公式
            float EvaluatePulsePhase(float iTime)
            {
                float x = fmod(iTime, 1.62);
                float x2 = x * x;
                float x3 = x2 * x;
                float x4 = x3 * x;
                float x5 = x4 * x;
                float x6 = x5 * x;
                float x7 = x6 * x;

                return -5.1827 * x7 + 29.2194 * x6 - 63.22 * x5 + 65.8918 * x4 - 35.5134 * x3 + 11.2191 * x2 - 1.1386 * x + 0.9429;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // 取得貼圖顏色
                fixed4 pulseTex = tex2D(_MainTex, i.Mainuv);

                // 旋轉角度計算
                float angleRad = _Angle * 0.017453278; // 角度轉換為弧度 ( * 3.1419 / 180.0 = * 0.017453278)
                // 計算旋轉矩陣
                float cosAngle = cos(angleRad);
                float sinAngle = sin(angleRad);

                // 根據角度旋轉處理物體位置
                float2 uvCenter = i.Mainuv - 0.5; // 把 UV 移到中心為 (0,0)
                float rotatedDist = (uvCenter.x * cosAngle + uvCenter.y * sinAngle) * 100.0; // <-- 這裡加了 100 倍的擴大

                // 把 _Time.y 丟到曲線公式計算
                float pulsePhase = EvaluatePulsePhase(_Time.y);

                // 計算呼吸燈的亮度（條紋效果）
                float highLightVal = abs(sin(pulsePhase * _PulseTimeScale - rotatedDist * _PulsePosScale));
                highLightVal = smoothstep(0.0, _EdgeSoftness, (1 - saturate(highLightVal / _LightWidth))) * _LightIntensity;

                i.color.a *= SoftMask(i.vertex, i.vertexObjPos, i.color.a);

                // 右上 UV = (1,1) 左下 UV = (0,0) 
                // 漸層參數 (1 - u) * (1 - v)
                float lerpVal = (1.0 - i.Mainuv.x + 1.0 - i.Mainuv.y) * 0.5;
                // 漸層顏色由右上到左下
                float3 gradColor = lerp(_ColorStart.rgb, _ColorEnd.rgb, lerpVal);
                float gradAlpha = lerp(_ColorStart.a, _ColorEnd.a, lerpVal);

                float3 texColor = pulseTex.rgb * i.color.rgb * pulseTex.a * i.color.a ;
                float3 highLighCol = pulseTex.a * i.color.a * highLightVal * gradColor * gradAlpha;
                
                // 計算最終顏色
                fixed4 finalColor = fixed4((texColor + highLighCol) * 0.5, pulseTex.a * i.color.a);

                // 亮度增強
                finalColor.rgb = finalColor.rgb * pow(2, _Intensity);

                return finalColor;
            }
            ENDCG
        }
    }
    FallBack "UI/Default" // 預設後備著色器
}