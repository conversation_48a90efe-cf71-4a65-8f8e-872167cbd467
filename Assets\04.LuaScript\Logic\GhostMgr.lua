---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/SystemSettingData/EffectSetting")

---殘影控制器
---@class GhostMgr
---author WereHsu
---version 1.0
---since [ProjectBase] 0.1
---date 2023.10.23
GhostMgr = {}
--GhostMgr.__index = GhostMgr

local this = GhostMgr


this.MAX_POOL_COUNT = EffectSetting.m_MaxGhostEffectCount * 4
this.m_Pool = {}
---流水號
this.m_Idx = 0
this.m_IsInit = false

function GhostMgr.Init()
    this.m_GhostMgr = GameObject.New("GhostMgr").transform

    this.m_ObjEmptyGhost = GameObject.New( "EmptyGhost" )
    this.m_ObjEmptyGhost.transform:SetParent(this.m_GhostMgr)

    this.m_ObjEmptyGhost:AddComponent(typeof(UnityEngine.MeshRenderer))
    this.m_ObjEmptyGhost:AddComponent(typeof(UnityEngine.MeshFilter))

    this.m_ObjEmptyGhost:SetActive( false )

    this.m_GObjPool_Ghost = Extension.CreatePrefabObjPool(this.m_ObjEmptyGhost,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

    this.m_IsInit = true
end

function GhostMgr.GetGhost(iGhostController)
    if not this.m_IsInit then
        return
    end

    local _Ghost = {}
    _Ghost.gameObject = this.m_GObjPool_Ghost:Get()
    _Ghost.m_MeshRenderer = _Ghost.gameObject:GetComponent(typeof(UnityEngine.MeshRenderer))
    _Ghost.m_MeshFilter = _Ghost.gameObject:GetComponent(typeof(UnityEngine.MeshFilter))

    if not Extension.IsUnityObjectNull(_Ghost.m_MeshFilter) then
        if Extension.IsUnityObjectNull(_Ghost.m_MeshFilter.mesh) then
            _Ghost.m_MeshFilter.mesh = UnityEngine.Mesh.New()
        end
        _Ghost.m_Mesh = _Ghost.m_MeshFilter.mesh
    end
    _Ghost.gameObject.transform:SetParent(this.m_GhostMgr)
    _Ghost.gameObject.layer = iGhostController.m_Layer
    _Ghost.m_Duration = iGhostController.m_Duration
    _Ghost.m_DeleteTime = Time.time + iGhostController.m_Duration
    if iGhostController.m_BeShadow then
        _Ghost.m_MeshRenderer.shadowCastingMode = EShadowCastingMode.On
    else
        _Ghost.m_MeshRenderer.shadowCastingMode = EShadowCastingMode.Off
    end

    _Ghost.m_FadeOutOnTime = iGhostController.m_FadeOutOnTime


    this.m_Idx = this.m_Idx + 1
    if this.m_Idx > this.MAX_POOL_COUNT then
        this.m_Idx = 0
    end
    _Ghost.m_Idx = this.m_Idx
    _Ghost.gameObject.name = GString.Format("Ghost_{0}",_Ghost.m_Idx)

    if this.m_Pool[_Ghost.m_Idx] then
        this.RecycleGhost(this.m_Pool[_Ghost.m_Idx])
    end

    this.m_Pool[this.m_Idx] = _Ghost

    return _Ghost
end

function GhostMgr.RecycleGhost(iGhost)
    if this.m_Pool[iGhost.m_Idx] then
        local _Gobj = iGhost.gameObject
        _Gobj.transform.localScale = Vector3(0,0,0)
        _Gobj.transform.position = Vector3(0,0,0)
        _Gobj.transform.eulerAngles = Vector3(0,0,0)

        iGhost.m_Duration = 0
        iGhost.m_DeleteTime = 0

        if not Extension.IsUnityObjectNull(iGhost.m_Mesh) then
            iGhost.m_Mesh:Clear()
        end

        this.m_GObjPool_Ghost:Store(iGhost.gameObject)
        this.m_Pool[iGhost.m_Idx] = nil
    end
end

function GhostMgr.Update()
    if not this.m_IsInit then
        GhostMgr.Init()
        return
    end

    for k,v in pairs(this.m_Pool) do
        if not Extension.IsUnityObjectNull(v.gameObject) then
            local _Timer = v.m_DeleteTime - Time.time
            if _Timer <= 0 then
                v.gameObject:SetActive(false)
                GhostMgr.RecycleGhost(v)
            elseif not Extension.IsUnityObjectNull(v.m_MeshRenderer.material) and v.m_FadeOutOnTime then
                local _Rate = _Timer / v.m_Duration
                -- local _Color = v.m_MeshRenderer.material:GetColor( "_Color" )
                -- _Color.a = _Color.a * _Rate
                v.m_MeshRenderer.material:SetFloat( "_Transparency", _Rate )
            end
        end

    end

end

function GhostMgr.OnUnrequire()
    this.m_GhostMgr.gameObject:Destroy()
end
return this
