---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("UI/Login/Login_Model")
require("UI/Login/Login_Page_ServerList")
require("UI/Login/Login_Page_UserContract")
require("UI/Login/Login_Page_CGLogin/Login_Page_CGLogin")

---Login
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.03.24
Login_Controller = {}
local this = Login_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Login_View", "Login_Controller", EUIOrderLayers.FullPage, false, "bg_002", false, EUIOpenCloseType.Defult, false)

--帳號字數限制
this.m_AccountWordLimit = {Min = 4 , Max = 12}
--密碼字數限制
this.m_PassWordLimit = {Min = 4, Max = 18}

---登入UI中 需要互相切換的頁面
this.m_PageType = {
	---快速登入
	QuickLogin = 1,
	---切換帳號
	ChangeAccount = 2,
	---切換伺服器
	ChangeServer = 3,
	---網龍登入
	CGLogin = 4,
	---更換密碼
	ChangePassword = 5,
	---註冊網龍帳號
	Register = 6,
	---登入詢問視窗
	--InquiryWindow = 7,
	---公告
	Bulletin = 8,
	---使用者條款
	UserContract = 9,
}

---主要按鈕們
this.m_BtnType = {
	ChangeAccount = 1,
	Setting = 2,
	DataCheck = 3,
	Bulletin = 4,
	UserContract = 5,
}

this.m_LoginErrorType = {
	AccountError = 1,
	PassWordError = 2,
}
---選取的公告Tab DataIdx從1開始!
this.m_BulletinTabIdx = 1

this.m_SubPage = {}

---伺服器選單 頁面
this.m_SubPage[this.m_PageType.ChangeServer] = Login_Page_ServerList
---網龍登入 頁面
this.m_SubPage[this.m_PageType.CGLogin] = Login_Page_CGLogin
---使用者合約 頁面
this.m_SubPage[this.m_PageType.UserContract] = Login_Page_UserContract

---設定版本資訊
local function SetGameVersion()
    this.m_TMP_Version.text = GString.Format(GameTools.ResTextGetter.GetResText("Version"), Application.version, ResourceMgr.RUNTIME_RESOURCE_VERSION)
end

local function LoginViewInit()
	---與燈號機連線
	NetworkMgr.ConnectToSeverLight()

	---Logo 圖
	local _RawImage_Logo = this.m_ViewRef.m_Dic_RawImage:Get("&RawImage_Logo")
	_RawImage_Logo.gameObject:SetActive(true)

	---介面上的按鈕們
	this.m_TUIRenderCtrl = {}
	---切換帳號按鈕
	this.m_Btn_ChangeAccount = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Common_Gray_ChangeAccount"))
	this.m_Btn_ChangeAccount:AddListener( EventTriggerType.PointerClick, Login_Controller.OnClick_ChangeAccount)
	this.m_TUIRenderCtrl[this.m_BtnType.ChangeAccount] =this.m_Btn_ChangeAccount.gameObject:GetComponent("UIRenderCtrl")
	---設定按鈕
	this.m_Btn_Setting = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Common_Gray_Setting"))
	this.m_Btn_Setting:AddListener( EventTriggerType.PointerClick, Login_Controller.OnClick_Setting)
	this.m_TUIRenderCtrl[this.m_BtnType.Setting] = this.m_Btn_Setting.gameObject:GetComponent("UIRenderCtrl")
	---資料檢查按鈕
	this.m_Btn_DataCheck = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Common_Gray_DataCheck"))
	this.m_Btn_DataCheck:AddListener(EventTriggerType.PointerClick, Login_Controller.OnClick_DataCheck)
	this.m_TUIRenderCtrl[this.m_BtnType.DataCheck] = this.m_Btn_DataCheck.gameObject:GetComponent("UIRenderCtrl")
	---公告按鈕
	this.m_Btn_Bulletin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Common_Gray_Bulletin"))
	this.m_Btn_Bulletin:AddListener(EventTriggerType.PointerClick, Login_Controller.OnClick_Bulletin)
	this.m_TUIRenderCtrl[this.m_BtnType.Bulletin] = this.m_Btn_Bulletin.gameObject:GetComponent("UIRenderCtrl")
	---使用者合約按鈕
	this.m_Btn_UserContract = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Common_Gray_UserContract"))
	this.m_Btn_UserContract:AddListener(EventTriggerType.PointerClick, Login_Controller.OnClick_UserContract)
	this.m_TUIRenderCtrl[this.m_BtnType.UserContract] = this.m_Btn_UserContract.gameObject:GetComponent("UIRenderCtrl")

	---版本資訊
    this.m_TMP_Version = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_Version")
	---注意資訊
    this.m_TMP_Action = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_Action")
	SetGameVersion()
end

---初始化
function Login_Controller.Init()
	LoginViewInit()

	--region 快速登入
	this.m_Evt_QuickLogin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_QuickLogin"))
	this.m_Evt_QuickLogin:AddListener(EventTriggerType.PointerClick, Login_Controller.OnClick_QuickLogin)
	this.m_Group_QuickLogin = this.m_Evt_QuickLogin.gameObject
	LeanTween.alphaCanvas(this.m_Group_QuickLogin:GetComponent("CanvasGroup"), 0, 1):setRepeat(-1):setLoopPingPong()
	--endregion

	--region 切換伺服器按鈕
	this.m_Group_ServerListBtn = this.m_ViewRef.m_Dic_Trans:Get("&Group_ServerListBtn").gameObject
	this.m_Group_ServerListBtn:SetActive(true)
	this.m_Evt_ServerListBtn = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_ChangeServer"))
	this.m_Evt_ServerListBtn:AddListener(EventTriggerType.PointerClick, Login_Controller.OnClick_ServerList)
	this.m_Text_ServerName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_ServerName")
	--endregion

	--region 登入種類按鈕
	this.m_Group_AccountTypeBtn = this.m_ViewRef.m_Dic_Trans:Get("&Group_AccountTypeBtn").gameObject
	this.m_Evt_AppleLogin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_AppleLogin"))
	this.m_Evt_AppleLogin:AddListener(EventTriggerType.PointerClick, Login_Model.AppleLogin)
	this.m_Evt_GoogleLogin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_GoogleLogin"))
	this.m_Evt_GoogleLogin:AddListener(EventTriggerType.PointerClick, Login_Model.GoogleLogin)
	this.m_Evt_FBLogin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_FBLogin"))
	this.m_Evt_FBLogin:AddListener(EventTriggerType.PointerClick, Login_Model.FBLogin)
	this.m_Evt_CGLogin = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Evt_CGLogin"))
	this.m_Evt_CGLogin:AddListener(EventTriggerType.PointerClick, Login_Model.CGLogin)
	--endregion

	--初始化 相關登入所需資料
	Login_Model.Init()
	--ServerList 頁面
	Login_Page_ServerList.Init(this)
	--使用者政策
	Login_Page_UserContract.Init(this)
	--網龍登入頁面
	Login_Page_CGLogin.Init(this)
end

function Login_Controller.Open()
	AudioMgr.PlaySpecialBGM(AudioMgr.EMixerGroup.BGM_NRM, AudioSetting.m_PlayingBGMKind.LoginBGM, true)
	this.OpenCheck()
	return true
end

---登入介面開啟要檢查甚麼
function Login_Controller.OpenCheck()
	-- 判斷是否開啟公告
	if Login_Model.m_IsShowBulletin then
		UIMgr.Open(Bulletin_Controller)
	end

	if not Login_Model.m_IsAgreementPermitted then
		Login_Controller.SetPage(this.m_PageType.UserContract)
	else
		-- 判斷是否快速登入
		if Login_Model.m_IsQuickLogin then
			Login_Controller.SetPage(this.m_PageType.QuickLogin)
		else
			Login_Controller.SetPage(this.m_PageType.ChangeAccount)
		end
	end

	--更新ServerName
	this.m_Text_ServerName.text = Login_Model.GetServerData().m_ServerName
end

---控制UI Page
function Login_Controller.SetPage(iPage)
	if iPage ~= this.m_PageType.Bulletin then
		Login_Controller.CloseAllSubPage()
	end

	if iPage == this.m_PageType.ChangeServer or iPage == this.m_PageType.UserContract then
		this.m_SubPage[iPage].Open()
	elseif iPage >= this.m_PageType.CGLogin and iPage <= this.m_PageType.Register then
		this.m_SubPage[4].Open(iPage)
	elseif iPage == this.m_PageType.Bulletin then
		UIMgr.Open(Bulletin_Controller)
	else
		this.OpenPage(iPage)
	end

end

function Login_Controller.CloseAllSubPage()
	for key, value in pairs(this.m_SubPage) do
		if value ~= nil then
			value.Close()
		end
	end
	this.m_Group_QuickLogin:SetActive(false)
	this.m_Group_AccountTypeBtn:SetActive(false)
end

function Login_Controller.OpenPage(iPage)
	if iPage == this.m_PageType.QuickLogin then
		--開啟快速登入頁面
		this.m_Group_QuickLogin:SetActive(true)
	elseif iPage == this.m_PageType.ChangeAccount then
		--變更帳戶頁面 (可選登入種類)
		Login_Model.m_EAccountType = -1
		Login_Model.m_Str_Account = ""
		Login_Model.m_Str_Password = ""
		Login_Model.SaveLoginData(false)
		this.m_Group_AccountTypeBtn:SetActive(true)
	end
end

function Login_Controller.ShowLoginError(  )

	if this.m_LoginErrorType.AccountError then

	elseif this.m_LoginErrorType.PassWordError then

	end
end
--region 按鈕click事件

---點擊更換帳號按鈕
function Login_Controller.OnClick_ChangeAccount()
	Login_Controller.SetPage(this.m_PageType.ChangeAccount)
end

---點擊設定按鈕
function Login_Controller.OnClick_Setting()
	UIMgr.Open(Setting_Controller)
end

---點擊資料修復按鈕
function Login_Controller.OnClick_DataCheck()
	CommonQueryMgr.AddNewInform(10,{},{}, Login_Controller.OnClick_DataCheck_Confirm)
end
function Login_Controller.OnClick_DataCheck_Confirm()
	Extension.SetStateFileCheck()--設定EStateOfGame 為檔案檢查
	Game.QuitLua()
end
---點擊公告
function Login_Controller.OnClick_Bulletin()
	Login_Controller.SetPage(this.m_PageType.Bulletin)
end

---點擊使用者合約
function Login_Controller.OnClick_UserContract()
	Login_Controller.SetPage(this.m_PageType.UserContract)
end

---點擊更換Server
function Login_Controller.OnClick_ServerList()
	Login_Controller.SetPage(this.m_PageType.ChangeServer)
end

---點擊快速登入
function Login_Controller.OnClick_QuickLogin()
	Login_Model.SendLoginRequest()
end
--endregion
