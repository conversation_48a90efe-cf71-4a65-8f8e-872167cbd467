---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---PetIcon 繼承自 BasicIcon
---@class PetIcon
---author hui
---version 1.0
---since [HEM 2.0]
---date 2025.2.12
PetIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function PetIcon.Init( iParent )
    PetIcon.LoadResources( "PetIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

function PetIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam )
    local _PetId = 0
    if type(iData) == "number" then
        _PetId = iData
    elseif type(iData) == "table" then
        _PetId = iData.m_PetID
    end

    local function OnPetClick()
        if iOnClick then
            iOnClick(iOnClickParam)
        end
    end

    local _Icon = BasicIcon:New( m_Tmp, EIconType.Pet, 0, iParent, iWidth, OnPetClick )
    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    setmetatable( _Icon, { __index = self } )

    _Icon:SetNeedOpenHint(true)
         :SetNeedLongPress(false)
         :SetClickTwice(false)

    -- 選擇框
    if _Icon.m_Trans_Select == nil then
        _Icon.m_Trans_Select = _Icon.transform:Find( "Image_Select" ):GetComponent( typeof( Image ) )

        _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
    end

    if iParent then
        _Icon.transform:SetParent( iParent )
    end

    ---設定數量
    if _Icon.m_TMP_Count == nil then
        _Icon.m_TMP_Count = _Icon.transform:Find( "TMP_Count" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end

    ---不被IconMgr IconMgr.CancelAllClick() 清除選擇，體現在開Hint時不會被統一清除
    _Icon.m_NeedClearSelect = true

    _Icon:RefreshIcon(_PetId)

    return _Icon
end

--- 設定稀有度外框
function PetIcon:SetRank( iRank )
    --- 舊物品表顏色到 8 號、物品表調整完成前防呆處理 2024.01.23 Modify by 蛋糕
    if iRank > 6 then
        iRank = 6
    end

    self:SetFrameType(iRank)

    return self
end

--- 設定外框圖
function PetIcon:SetFrameImage(iRank)
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            -- 存下所有可變色區塊
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end

    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].sprite = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].sprite
    end

    return self
end

---設定物品數量
---@param iCount number 數量 or 要顯示的字串
---@param iStyle string 請給 style (優先度高於原始設定)
---@param iWithoutSizeController boolean 不受尺寸控制
function PetIcon:SetCount( iCount, iStyle, iWithoutSizeController )
    if iCount ~= nil then
        if type(iCount) == "number" then
            self.m_TMP_Count.text = tostring( iCount > 0 and GValue.CountToString(iCount, EValueShowType.Short) or "" )

            if iCount > IconSetting.m_ITEMMAXCOUNT then
                self.m_TMP_Count.text = GString.GetTextWithSize(self.m_TMP_Count.text, IconSetting.IconTextSizeSmall)
            end

            -- 改用是否有idx決定要不要顯示數量
            self:SetObjectActive(self.m_TMP_Count, self.m_Idx > 0)

            self.m_Count = iCount

            self.m_TMP_Count.alignment = TMPro.TextAlignmentOptions.BottomRight;
        end
    else
        self:SetObjectActive(self.m_TMP_Count, false)
    end

    if iStyle then
        self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, iStyle)
    end

    if iWithoutSizeController then
        ---計算 Scale, 採用縮放的方式才不會影響子物件定位點導致顯示異常
        local _Scale = 1 / self.transform.localScale.x
        self.m_TMP_Count.transform.localScale = Vector3( _Scale, _Scale, _Scale )
    else
        self.m_TMP_Count.transform.localScale = Vector3.one
    end

    return self
end

--- 設定選擇
function PetIcon:ShowSelect( iIsSelect )
    self:SetObjectActive(self.m_Trans_Select, iIsSelect)

    if iIsSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSelectImage
        self.m_Trans_Select.color = Color.White
    end
end

--- 取消選擇
---@param iDoSelectAction boolean 是否執行選擇函式
function PetIcon:CancelSelect(iDoSelectAction)
    UIMgr.OpenIconName(false)
    self:ShowSelect( false )

    -- 給 icon 底層因為判斷不選取，不在次觸發 action 用
    if iDoSelectAction ~= false then
        self:DoSelectAction(false)
    end
end

---重設物品 Icon
---@param iData number 物品 Idx
---@param iData SavePetData 物品 Save
function PetIcon:RefreshIcon( iData )
    local _PetId = 0
    if type(iData) == "number" then
        _PetId = iData
    elseif type(iData) == "table" then
        _PetId = iData.m_PetID
    else
        do return end
    end

    local function ResetIcon()
        self.m_PetData = nil
        self.m_SaveData = nil
        self:RefreshBasicIcon(0)
        self:SetName()
            :SetRank(ERank.None)
            :SetCount(0)
    end

    if _PetId == 0 then
        ResetIcon()
        do return end
    end

    local _PetData = PetData.GetPetDataByIdx( _PetId )
    if _PetData == nil then
        ResetIcon()
        do return end
    end

    self.m_PetData =_PetData

    self:RefreshBasicIcon(_PetId, self.m_PetData:GetPetTextureName())

    if type(iData) == "table" then
        self.m_SaveData = iData
        self:SetName(iData:GetPetSaveName())
            :SetRank(self.m_PetData.m_PetRank)
            :SetCount( iData.m_Count )
    else
        self.m_SaveData = nil
        self:SetName(self.m_PetData:PetName())
            :SetRank(self.m_PetData.m_PetRank)
            :SetCount( 0 )
    end

    self:ShowSelect( false )
end

--region 開關 Hint
function PetIcon:OpenHint()
    if not self.m_NeedOpenHint or self.m_Idx == 0 then
        return
    end

    IconMgr.CancelAllClick()

    local _PetHintData ={}
    --必須根據 petIcon 被帶入了甚麼樣的資料決定開啟方法
    if self.m_SaveData == nil then
        _PetHintData = {PetHint_Controller.DataType.PetID, self.m_Idx, nil}
        HintMgr_Controller.OpenHint(EHintType.PetHint, _PetHintData)
    else
        _PetHintData = {PetHint_Controller.DataType.PetSaveData, nil, self.m_SaveData}
        HintMgr_Controller.OpenHint(EHintType.PetHint, _PetHintData)
    end
end

function PetIcon:CloseHint()
    -- UIMgr.Close( ItemHint_Controller)
end
--endregion 開關 Hint
