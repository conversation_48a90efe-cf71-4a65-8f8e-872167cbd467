fileFormatVersion: 2
guid: cb1fc63a2a648e544b2423947fa19994
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: <PERSON><PERSON><PERSON>_Abdomen_01_01SHJnt
    100004: <PERSON><PERSON><PERSON>_Abdomen_01_02SHJnt
    100006: <PERSON><PERSON><PERSON>_Abdomen_01_03SHJnt
    100008: <PERSON><PERSON><PERSON>_Abdomen_01_04SHJnt
    100010: <PERSON><PERSON><PERSON>_Abdomen_01_05SHJnt
    100012: Scorpion_l_BackLeg_AnkleSHJnt
    100014: Scorpion_l_BackLeg_BallSHJnt
    100016: Scorpion_l_BackLeg_HipSHJnt
    100018: Scorpion_l_BackLeg_Knee1SHJnt
    100020: Scorpion_l_BackLeg_Knee2SHJnt
    100022: Scorpion_l_BackLeg_ToeSHJnt
    100024: Scorpion_l_Claw_01_01SHJnt
    100026: Scorpion_l_Claw_01_02SHJnt
    100028: Scor<PERSON>_l_Claw_02_01SHJnt
    100030: Scor<PERSON>_l_Claw_02_02SHJnt
    100032: Scorpion_l_FrontLeg_AnkleSHJnt
    100034: <PERSON>or<PERSON>_l_FrontLeg_BallSHJnt
    100036: Scorpion_l_FrontLeg_HipSHJnt
    100038: Scorpion_l_FrontLeg_Knee1SHJnt
    100040: Scorpion_l_FrontLeg_Knee2SHJnt
    100042: Scorpion_l_FrontLeg_ToeSHJnt
    100044: Scorpion_l_MidBackLeg_AnkleSHJnt
    100046: Scorpion_l_MidBackLeg_BallSHJnt
    100048: Scorpion_l_MidBackLeg_HipSHJnt
    100050: Scorpion_l_MidBackLeg_Knee1SHJnt
    100052: Scorpion_l_MidBackLeg_Knee2SHJnt
    100054: Scorpion_l_MidBackLeg_ToeSHJnt
    100056: Scorpion_l_MidFrontLeg_AnkleSHJnt
    100058: Scorpion_l_MidFrontLeg_BallSHJnt
    100060: Scorpion_l_MidFrontLeg_HipSHJnt
    100062: Scorpion_l_MidFrontLeg_Knee1SHJnt
    100064: Scorpion_l_MidFrontLeg_Knee2SHJnt
    100066: Scorpion_l_MidFrontLeg_ToeSHJnt
    100068: Scorpion_l_Pedipalp_01_01SHJnt
    100070: Scorpion_l_Pedipalp_01_02SHJnt
    100072: Scorpion_l_Pedipalp_01_03SHJnt
    100074: Scorpion_l_Pedipalp_01_04SHJnt
    100076: Scorpion_MAINSHJnt
    100078: Scorpion_r_BackLeg_AnkleSHJnt
    100080: Scorpion_r_BackLeg_BallSHJnt
    100082: Scorpion_r_BackLeg_HipSHJnt
    100084: Scorpion_r_BackLeg_Knee1SHJnt
    100086: Scorpion_r_BackLeg_Knee2SHJnt
    100088: Scorpion_r_BackLeg_ToeSHJnt
    100090: Scorpion_r_Claw_01_01SHJnt
    100092: Scorpion_r_Claw_01_02SHJnt
    100094: Scorpion_r_Claw_02_01SHJnt
    100096: Scorpion_r_Claw_02_02SHJnt
    100098: Scorpion_r_FrontLeg_AnkleSHJnt
    100100: Scorpion_r_FrontLeg_BallSHJnt
    100102: Scorpion_r_FrontLeg_HipSHJnt
    100104: Scorpion_r_FrontLeg_Knee1SHJnt
    100106: Scorpion_r_FrontLeg_Knee2SHJnt
    100108: Scorpion_r_FrontLeg_ToeSHJnt
    100110: Scorpion_r_MidBackLeg_AnkleSHJnt
    100112: Scorpion_r_MidBackLeg_BallSHJnt
    100114: Scorpion_r_MidBackLeg_HipSHJnt
    100116: Scorpion_r_MidBackLeg_Knee1SHJnt
    100118: Scorpion_r_MidBackLeg_Knee2SHJnt
    100120: Scorpion_r_MidBackLeg_ToeSHJnt
    100122: Scorpion_r_MidFrontLeg_AnkleSHJnt
    100124: Scorpion_r_MidFrontLeg_BallSHJnt
    100126: Scorpion_r_MidFrontLeg_HipSHJnt
    100128: Scorpion_r_MidFrontLeg_Knee1SHJnt
    100130: Scorpion_r_MidFrontLeg_Knee2SHJnt
    100132: Scorpion_r_MidFrontLeg_ToeSHJnt
    100134: Scorpion_r_Pedipalp_01_01SHJnt
    100136: Scorpion_r_Pedipalp_01_02SHJnt
    100138: Scorpion_r_Pedipalp_01_03SHJnt
    100140: Scorpion_r_Pedipalp_01_04SHJnt
    100142: Scorpion_ROOTSHJnt
    100144: Scorpion_Sting_01_01SHJnt
    100146: Scorpion_Sting_01_02SHJnt
    100148: Scorpion_Sting_01_03SHJnt
    100150: Scorpion_Sting_01_04SHJnt
    100152: Scorpion_Sting_01_05SHJnt
    100154: Scorpion_Sting_01_06SHJnt
    100156: Scorpion_Sting_01_07SHJnt
    400000: //RootNode
    400002: Scorpion_Abdomen_01_01SHJnt
    400004: Scorpion_Abdomen_01_02SHJnt
    400006: Scorpion_Abdomen_01_03SHJnt
    400008: Scorpion_Abdomen_01_04SHJnt
    400010: Scorpion_Abdomen_01_05SHJnt
    400012: Scorpion_l_BackLeg_AnkleSHJnt
    400014: Scorpion_l_BackLeg_BallSHJnt
    400016: Scorpion_l_BackLeg_HipSHJnt
    400018: Scorpion_l_BackLeg_Knee1SHJnt
    400020: Scorpion_l_BackLeg_Knee2SHJnt
    400022: Scorpion_l_BackLeg_ToeSHJnt
    400024: Scorpion_l_Claw_01_01SHJnt
    400026: Scorpion_l_Claw_01_02SHJnt
    400028: Scorpion_l_Claw_02_01SHJnt
    400030: Scorpion_l_Claw_02_02SHJnt
    400032: Scorpion_l_FrontLeg_AnkleSHJnt
    400034: Scorpion_l_FrontLeg_BallSHJnt
    400036: Scorpion_l_FrontLeg_HipSHJnt
    400038: Scorpion_l_FrontLeg_Knee1SHJnt
    400040: Scorpion_l_FrontLeg_Knee2SHJnt
    400042: Scorpion_l_FrontLeg_ToeSHJnt
    400044: Scorpion_l_MidBackLeg_AnkleSHJnt
    400046: Scorpion_l_MidBackLeg_BallSHJnt
    400048: Scorpion_l_MidBackLeg_HipSHJnt
    400050: Scorpion_l_MidBackLeg_Knee1SHJnt
    400052: Scorpion_l_MidBackLeg_Knee2SHJnt
    400054: Scorpion_l_MidBackLeg_ToeSHJnt
    400056: Scorpion_l_MidFrontLeg_AnkleSHJnt
    400058: Scorpion_l_MidFrontLeg_BallSHJnt
    400060: Scorpion_l_MidFrontLeg_HipSHJnt
    400062: Scorpion_l_MidFrontLeg_Knee1SHJnt
    400064: Scorpion_l_MidFrontLeg_Knee2SHJnt
    400066: Scorpion_l_MidFrontLeg_ToeSHJnt
    400068: Scorpion_l_Pedipalp_01_01SHJnt
    400070: Scorpion_l_Pedipalp_01_02SHJnt
    400072: Scorpion_l_Pedipalp_01_03SHJnt
    400074: Scorpion_l_Pedipalp_01_04SHJnt
    400076: Scorpion_MAINSHJnt
    400078: Scorpion_r_BackLeg_AnkleSHJnt
    400080: Scorpion_r_BackLeg_BallSHJnt
    400082: Scorpion_r_BackLeg_HipSHJnt
    400084: Scorpion_r_BackLeg_Knee1SHJnt
    400086: Scorpion_r_BackLeg_Knee2SHJnt
    400088: Scorpion_r_BackLeg_ToeSHJnt
    400090: Scorpion_r_Claw_01_01SHJnt
    400092: Scorpion_r_Claw_01_02SHJnt
    400094: Scorpion_r_Claw_02_01SHJnt
    400096: Scorpion_r_Claw_02_02SHJnt
    400098: Scorpion_r_FrontLeg_AnkleSHJnt
    400100: Scorpion_r_FrontLeg_BallSHJnt
    400102: Scorpion_r_FrontLeg_HipSHJnt
    400104: Scorpion_r_FrontLeg_Knee1SHJnt
    400106: Scorpion_r_FrontLeg_Knee2SHJnt
    400108: Scorpion_r_FrontLeg_ToeSHJnt
    400110: Scorpion_r_MidBackLeg_AnkleSHJnt
    400112: Scorpion_r_MidBackLeg_BallSHJnt
    400114: Scorpion_r_MidBackLeg_HipSHJnt
    400116: Scorpion_r_MidBackLeg_Knee1SHJnt
    400118: Scorpion_r_MidBackLeg_Knee2SHJnt
    400120: Scorpion_r_MidBackLeg_ToeSHJnt
    400122: Scorpion_r_MidFrontLeg_AnkleSHJnt
    400124: Scorpion_r_MidFrontLeg_BallSHJnt
    400126: Scorpion_r_MidFrontLeg_HipSHJnt
    400128: Scorpion_r_MidFrontLeg_Knee1SHJnt
    400130: Scorpion_r_MidFrontLeg_Knee2SHJnt
    400132: Scorpion_r_MidFrontLeg_ToeSHJnt
    400134: Scorpion_r_Pedipalp_01_01SHJnt
    400136: Scorpion_r_Pedipalp_01_02SHJnt
    400138: Scorpion_r_Pedipalp_01_03SHJnt
    400140: Scorpion_r_Pedipalp_01_04SHJnt
    400142: Scorpion_ROOTSHJnt
    400144: Scorpion_Sting_01_01SHJnt
    400146: Scorpion_Sting_01_02SHJnt
    400148: Scorpion_Sting_01_03SHJnt
    400150: Scorpion_Sting_01_04SHJnt
    400152: Scorpion_Sting_01_05SHJnt
    400154: Scorpion_Sting_01_06SHJnt
    400156: Scorpion_Sting_01_07SHJnt
    7400000: P_0020_Run
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: P_0020_Run
      takeName: P_0020_Run
      firstFrame: 1
      lastFrame: 19
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 0075a892b1dc35d42aa86fed89c3429d,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
