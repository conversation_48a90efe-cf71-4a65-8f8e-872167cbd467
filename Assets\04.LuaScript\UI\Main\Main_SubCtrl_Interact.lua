---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---主介面 子控制器 人物右邊互動按鈕群
---@class Main_SubCtrl_Interact
---author WereHsu
---telephone #2896
---version 1.0
---since [黃易群俠傳M] 2.0
---date 2024.01.11
Main_SubCtrl_Interact = {}

local this = Main_SubCtrl_Interact

local m_GObjPool_Interact
local m_Active_InteractObject = {}

local m_UnitHeight = 0
local m_DefaultViewportHeight = 0

---互動按鈕種類
local EInteractType= 
{
    Collection = 1,
    Talk = 2,
    Gear = 3,
}
--- 互動按鈕 Icon
local EInteractIconName = {}
    EInteractIconName[EInteractType.Collection] = "MainIcon_043"
    EInteractIconName[EInteractType.Talk] = "MainIcon_007"
    EInteractIconName[EInteractType.Gear] = "MainIcon_043"

---現存按鈕統計
local m_CountInteractType = {}

---初始化
function Main_SubCtrl_Interact.Init(iController)
    --設定controller
    this.m_Controller = iController.m_UIController
    --設定ViewRef
    this.m_ViewRef = iController.m_ViewRef

    for k,v in pairs(EInteractType) do
        m_CountInteractType[v] = 0
    end

    this.m_Unit_InteractBtn = this.m_ViewRef.m_Dic_Trans:Get("&GObj_Interact_1")
    this.m_ScrollView_InteractBtn = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_InteractBtn")
    this.m_Viewport_InteractBtn = this.m_ViewRef.m_Dic_Trans:Get("&Viewport_InteractBtn")
    this.m_Root_Interact = this.m_ViewRef.m_Dic_Trans:Get("&Content_InteractBtn")

    m_GObjPool_Interact = Extension.CreatePrefabObjPool(
		this.m_Unit_InteractBtn.gameObject,
		Vector3.zero,
		Vector4.zero
	)
    m_UnitHeight = this.m_Unit_InteractBtn.sizeDelta.y
    m_DefaultViewportHeight = this.m_Viewport_InteractBtn.sizeDelta.y
    SearchMgr.SetListRefreshListener(ESearchKind.FriendlyNPC, this.RefreshInteractable)
end


local function TweenFrame(iCount)
    ---目前物件的數量
    --local _nowCount = table.Count(m_Active_InteractObject)
    if iCount >= 0 then
        LeanTween.size(this.m_Root_Interact, Vector2(0, iCount * m_UnitHeight), 0.5):setEase(LeanTweenType.easeOutQuint)
    end

    if iCount >= 1 then
        local _value = m_DefaultViewportHeight + m_UnitHeight * (iCount)
        _value = _value > 0 and 0 or _value
        LeanTween.size(this.m_Viewport_InteractBtn, Vector2(0, _value), 0.3):setEase(LeanTweenType.easeOutQuint)
    else
        LeanTween.size(this.m_Viewport_InteractBtn, Vector2(0, m_DefaultViewportHeight), 0.3):setEase(LeanTweenType.easeOutQuint)
    end
end

--每次周邊友善NPC刷新
function Main_SubCtrl_Interact.RefreshInteractable()

    ---紀錄原始數量
    local _tmpCount = table.Count(m_Active_InteractObject)
    ---原始數量增減後
    local _CulCount = _tmpCount
    local _list = SearchMgr.GetSearchList(ESearchKind.FriendlyNPC)
    local _listSID = {}

    ---要載入哪個圖圖
    local _LoadSprite = function(iImage, iImageName)
        local _ImageChange = iImage.transform:GetComponent(typeof(UIImageChange))
        SpriteMgr.Load( iImageName.."_D", function(iSprite)
            iImage.sprite = iSprite
            _ImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, iSprite)
            _ImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Disabled, iSprite)
            _ImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Highlighted, iSprite)
        end)
        SpriteMgr.Load( iImageName.."_L", function(iSprite)
            _ImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Selected, iSprite)
        end)
    end


    for _,_RC in pairs(_list) do
        --local _Obj
        --沒物件則生物件並撥放插入動畫
        if m_Active_InteractObject[_RC.m_SID] == nil then
            ---主物件
            local _Obj = m_GObjPool_Interact:Get()

            ---物件資料
            local _InteractItem = {}
            ---主物件的動態
            local _Anim_InteractGobj = _Obj:GetComponent("UIAnimation")

            ---主物件下第一個子物件為按鈕
            local _Btn_Interact = _Obj.transform:GetChild(0)
            ---按鈕的動態
            local _Anim_Interact = _Btn_Interact:GetComponent("UIAnimation")

            local _ImageName = "MainIcon_043"
            ---按鈕物件圖示
            _InteractItem.m_ImageIcon = _Obj.transform:GetChild(1):GetChild(0):GetComponent(typeof(Image))
            --local _ImageChange = _InteractItem.m_ImageIcon.transform:GetComponent(typeof(UIImageChange))
            _InteractItem.m_MainBtn = Button.New(_Obj)
            _InteractItem.transform = _Obj.transform
            _InteractItem.m_AnimationRoot = _Anim_InteractGobj
            _InteractItem.m_Animation = _Anim_Interact

            if _RC.m_DebugIdentifyName and _RC.m_HUDController then
                _InteractItem.m_MainBtn:SetText(_RC.m_HUDController.m_HUDData.m_Name)
                _Obj.name = "InteractItem_".._RC.m_DebugIdentifyName
                if _RC.m_NPCMode == EventNPCMode.Collection then
                    --採集物
                    _InteractItem.m_InteractType = EInteractType.Collection
                    _ImageName = EInteractIconName[EInteractType.Collection]
                else
                    --其他都對話
                    _InteractItem.m_InteractType = EInteractType.Talk
                    _ImageName = EInteractIconName[EInteractType.Talk]
                end
            else
                --機關
                local _name = _RC.m_Name
                if _name == nil then
                    _name = TextData.Get(1004)
                else
                    _name = _RC.m_Name
                end
                _InteractItem.m_MainBtn:SetText(_name)
                _Obj.name = "InteractItem_".._name

                _InteractItem.m_InteractType = EInteractType.Gear

                _ImageName = EInteractIconName[EInteractType.Gear]
            end
            _LoadSprite(_InteractItem.m_ImageIcon, _ImageName)
            --LeanTween.size(_InteractItem.transform, Vector2(this.m_Unit_InteractBtn.sizeDelta.x, this.m_Unit_InteractBtn.sizeDelta.y), 0.3)--:setEase(LeanTweenType.easeOutQuint)

            Button.ClearListener(_InteractItem.m_MainBtn)
            Button.AddListener(_InteractItem.m_MainBtn, EventTriggerType.PointerClick, SelectMgr.ChangeAndInterAct, _RC)
    

            m_Active_InteractObject[_RC.m_SID] = _InteractItem
            --依照數量變化做
            _CulCount = _CulCount + 1
            m_CountInteractType[_InteractItem.m_InteractType] = m_CountInteractType[_InteractItem.m_InteractType] + 1

            UIVisibleEffect.DoVisibleEffect(_Anim_InteractGobj, _InteractItem.m_AnimationRoot.m_AnimationType, true)
            UIVisibleEffect.DoVisibleEffect(_Anim_Interact, _InteractItem.m_Animation.m_AnimationType, true)

            --設定插入位置
            local _idx = 0
            for _type,_Count in pairs(m_CountInteractType) do
                if _InteractItem.m_InteractType >= _type then
                    _idx = _idx + _Count
                end
            end

            m_Active_InteractObject[_RC.m_SID].transform:SetParent(this.m_Root_Interact)
            m_Active_InteractObject[_RC.m_SID].transform.localScale = Vector3.one
            m_Active_InteractObject[_RC.m_SID].transform.localPosition = Vector3.zero
            m_Active_InteractObject[_RC.m_SID].transform.sizeDelta = Vector2.zero
            m_Active_InteractObject[_RC.m_SID].transform.gameObject:SetActive(true)
            m_Active_InteractObject[_RC.m_SID].transform:SetSiblingIndex(_idx - 1)
        end


        _listSID[_RC.m_SID] = true
    end

    for _SID,_Item in pairs(m_Active_InteractObject) do
        if not _listSID[_SID] then
            --_Item.m_ImageIcon.transform.gameObject:SetActive(false)
            UIVisibleEffect.DoVisibleEffect(_Item.m_AnimationRoot, _Item.m_AnimationRoot.m_AnimationType, false)
            UIVisibleEffect.DoVisibleEffect(_Item.m_Animation, _Item.m_Animation.m_AnimationType, false)

            --LeanTween.size(m_Active_InteractObject[_SID].transform, Vector2(this.m_Unit_InteractBtn.sizeDelta.x, 0), 0.3)--:setEase(LeanTweenType.easeOutQuint)
            m_Active_InteractObject[_SID].m_DestroyFunction = function()
                Button.ClearListener(_Item.m_MainBtn)
                _Item.transform.gameObject:SetActive(false)
                _Item.m_ImageIcon.transform.gameObject:SetActive(true)
                _Item.m_MainBtn.m_ButtonEx:DoButtonStateTransition(ESelectionState.Normal)

                if m_CountInteractType[_Item.m_InteractType] > 0 then
                    m_CountInteractType[_Item.m_InteractType] = m_CountInteractType[_Item.m_InteractType] - 1
                end
                _Item.transform:SetSiblingIndex(_Item.transform.parent.childCount - 1)

                m_GObjPool_Interact:Store(_Item.transform.gameObject)
                m_Active_InteractObject[_SID] = nil
            end
            HEMTimeMgr.DoFunctionDelay(0.5, _Item.m_DestroyFunction)
            _CulCount = _CulCount - 1
        elseif _Item.m_DestroyFunction then
            HEMTimeMgr.CancelDoFunctionDelay(_Item.m_DestroyFunction)
            -- UIVisibleEffect.DoVisibleEffect( _Item.m_AnimationRoot.transform.gameObject, _Item.m_AnimationRoot.m_AnimationType, true)
            -- UIVisibleEffect.DoVisibleEffect( _Item.m_Animation.transform.gameObject, _Item.m_Animation.m_AnimationType, true)

        end
    end

    if _CulCount ~= _tmpCount then
        TweenFrame(_CulCount)
    end
end

function Main_SubCtrl_Interact.Update()
    LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Root_Interact )
end

return this
