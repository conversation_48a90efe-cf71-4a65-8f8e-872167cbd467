fileFormatVersion: 2
guid: 4466c927c39de924b91c4e346b64f336
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 L Calf
    100008: Bip001 L Clavicle
    100010: Bip001 L Finger0
    100012: Bip001 L Finger01
    100014: Bip001 L Finger1
    100016: Bip001 L Finger11
    100018: Bip001 L Finger2
    100020: Bip001 L Finger21
    100022: Bip001 L Finger3
    100024: Bip001 L Finger31
    100026: Bip001 L Finger4
    100028: Bip001 L Finger41
    100030: Bip001 L Foot
    100032: Bip001 L Forearm
    100034: Bip001 L Hand
    100036: Bip001 L Thigh
    100038: Bip001 L UpperArm
    100040: Bip001 Neck
    100042: Bip001 Pelvis
    100044: Bip001 R Calf
    100046: Bip001 R Clavicle
    100048: Bip001 R Finger0
    100050: Bip001 R Finger01
    100052: Bip001 R Finger1
    100054: Bip001 R Finger11
    100056: Bip001 R Finger2
    100058: Bip001 R Finger21
    100060: Bip001 R Finger3
    100062: Bip001 R Finger31
    100064: Bip001 R Finger4
    100066: Bip001 R Finger41
    100068: Bip001 R Foot
    100070: Bip001 R Forearm
    100072: Bip001 R Hand
    100074: Bip001 R Thigh
    100076: Bip001 R UpperArm
    100078: Bip001 Spine
    100080: Bip001 Spine1
    100082: bones_F_F_Skirt_001
    100084: bones_F_F_Skirt_002
    100086: bones_F_R_Skirt_001
    100088: bones_F_R_Skirt_002
    100090: bones_F_Skirt_001
    100092: bones_F_Skirt_002
    100094: bones_F_Skirt_003
    100096: bones_F_Skirt_004
    100098: bones_Head_001
    100100: bones_L_Ear_001
    100102: bones_L_Ear_002
    100104: bones_L_Hair_001
    100106: bones_L_Skirt_001
    100108: bones_L_Skirt_002
    100110: bones_L_Sleeve_001
    100112: bones_L_Sleeve_002
    100114: bones_R_Ear_001
    100116: bones_R_Ear_002
    100118: bones_R_Hair_001
    100120: bones_R_Skirt_001
    100122: bones_R_Skirt_002
    100124: bones_R_Sleeve_001
    100126: bones_R_Sleeve_002
    100128: Dummy_ L_Foot_001
    100130: Dummy_ R_Foot_001
    100132: Dummy_F_Hair_001
    100134: Dummy_L_Calf_001
    100136: Dummy_L_F_Skirt_001
    100138: Dummy_L_F_Skirt_002
    100140: Dummy_L_F_Skirt_003
    100142: Dummy_L_F_Skirt_004
    100144: Dummy_L_Hair_001
    100146: Dummy_L_UpperArm_001
    100148: Dummy_L_UpperArm_002
    100150: Dummy_R_Calf_001
    100152: Dummy_R_F_Skirt_001
    100154: Dummy_R_F_Skirt_002
    100156: Dummy_R_F_Skirt_003
    100158: Dummy_R_F_Skirt_004
    100160: Dummy_R_Hair_001
    100162: Dummy_R_UpperArm_001
    100164: Dummy_R_UpperArm_002
    100166: M_1101
    100168: //RootNode
    100170: Particle View 001
    100172: bones_Eyebrow
    100174: bones_L_Eye_001
    100176: bones_L_Eye_Down_001
    100178: bones_L_Eye_Up_001
    100180: bones_L_Mouth_001
    100182: bones_Mouth_Down_001
    100184: bones_Mouth_UP_001
    100186: bones_R_Eye_001
    100188: bones_R_Eye_Down_001
    100190: bones_R_Eye_Up_001
    100192: bones_R_Mouth_001
    100194: bones_Tongue_001
    100196: bones_Tongue_002
    100198: Dummy_Tongue_000
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 L Calf
    400008: Bip001 L Clavicle
    400010: Bip001 L Finger0
    400012: Bip001 L Finger01
    400014: Bip001 L Finger1
    400016: Bip001 L Finger11
    400018: Bip001 L Finger2
    400020: Bip001 L Finger21
    400022: Bip001 L Finger3
    400024: Bip001 L Finger31
    400026: Bip001 L Finger4
    400028: Bip001 L Finger41
    400030: Bip001 L Foot
    400032: Bip001 L Forearm
    400034: Bip001 L Hand
    400036: Bip001 L Thigh
    400038: Bip001 L UpperArm
    400040: Bip001 Neck
    400042: Bip001 Pelvis
    400044: Bip001 R Calf
    400046: Bip001 R Clavicle
    400048: Bip001 R Finger0
    400050: Bip001 R Finger01
    400052: Bip001 R Finger1
    400054: Bip001 R Finger11
    400056: Bip001 R Finger2
    400058: Bip001 R Finger21
    400060: Bip001 R Finger3
    400062: Bip001 R Finger31
    400064: Bip001 R Finger4
    400066: Bip001 R Finger41
    400068: Bip001 R Foot
    400070: Bip001 R Forearm
    400072: Bip001 R Hand
    400074: Bip001 R Thigh
    400076: Bip001 R UpperArm
    400078: Bip001 Spine
    400080: Bip001 Spine1
    400082: bones_F_F_Skirt_001
    400084: bones_F_F_Skirt_002
    400086: bones_F_R_Skirt_001
    400088: bones_F_R_Skirt_002
    400090: bones_F_Skirt_001
    400092: bones_F_Skirt_002
    400094: bones_F_Skirt_003
    400096: bones_F_Skirt_004
    400098: bones_Head_001
    400100: bones_L_Ear_001
    400102: bones_L_Ear_002
    400104: bones_L_Hair_001
    400106: bones_L_Skirt_001
    400108: bones_L_Skirt_002
    400110: bones_L_Sleeve_001
    400112: bones_L_Sleeve_002
    400114: bones_R_Ear_001
    400116: bones_R_Ear_002
    400118: bones_R_Hair_001
    400120: bones_R_Skirt_001
    400122: bones_R_Skirt_002
    400124: bones_R_Sleeve_001
    400126: bones_R_Sleeve_002
    400128: Dummy_ L_Foot_001
    400130: Dummy_ R_Foot_001
    400132: Dummy_F_Hair_001
    400134: Dummy_L_Calf_001
    400136: Dummy_L_F_Skirt_001
    400138: Dummy_L_F_Skirt_002
    400140: Dummy_L_F_Skirt_003
    400142: Dummy_L_F_Skirt_004
    400144: Dummy_L_Hair_001
    400146: Dummy_L_UpperArm_001
    400148: Dummy_L_UpperArm_002
    400150: Dummy_R_Calf_001
    400152: Dummy_R_F_Skirt_001
    400154: Dummy_R_F_Skirt_002
    400156: Dummy_R_F_Skirt_003
    400158: Dummy_R_F_Skirt_004
    400160: Dummy_R_Hair_001
    400162: Dummy_R_UpperArm_001
    400164: Dummy_R_UpperArm_002
    400166: M_1101
    400168: //RootNode
    400170: Particle View 001
    400172: bones_Eyebrow
    400174: bones_L_Eye_001
    400176: bones_L_Eye_Down_001
    400178: bones_L_Eye_Up_001
    400180: bones_L_Mouth_001
    400182: bones_Mouth_Down_001
    400184: bones_Mouth_UP_001
    400186: bones_R_Eye_001
    400188: bones_R_Eye_Down_001
    400190: bones_R_Eye_Up_001
    400192: bones_R_Mouth_001
    400194: bones_Tongue_001
    400196: bones_Tongue_002
    400198: Dummy_Tongue_000
    2100000: M_1101
    4300000: M_1101
    7400000: M_Walk
    9500000: //RootNode
    13700000: M_1101
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_Walk
      takeName: M_Walk
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: dad121a675cc76343b5f3e45a7dd72ce,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
