﻿//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2024 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------    
//
//=====================================================================

using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using GameTools.Log;
using LuaInterface;
using UnityEngine.SceneManagement;

/// <summary>
/// 相機提供的 Gizmos Debug 功能
/// <AUTHOR>
/// @telephone #2911
/// @version 1.0
/// @since [黃易群俠傳M] 2.0
/// @date 2024.3.18
/// </summary>
public class GizmosDebug : MonoBehaviour
{
	/// <summary>
	/// 畫場景距離格線
	/// </summary>
	[NoToLua, NonSerialized] public bool m_isDrawSceneLine = false;

	[NoToLua, NonSerialized] public Terrain m_CurrentTerrain = null;
	[NoToLua, NonSerialized] public float m_LineHeight = 0.5f;
	private float m_RealHeight = 0.0f;
	private Vector3 m_MaxSceneSize;

	[NoToLua] public bool m_isDrawSingleLine = false;
	[SerializeField, Header("線段長(不可調整)")] private float m_PointDistance = 0.0f;
	private Vector3 m_SingleLine_Start = Vector3.zero;
	private Vector3 m_SingleLine_End = Vector3.zero;

	[SerializeField, Header("線段寬度")] public float m_width = 1.0f;
	private Camera m_SelfCamera;
	
	[SerializeField, Header("線段持續時間")] public float m_Duration = 10.0f;
	
	[SerializeField, Header("圓環平滑度")] public int m_RigTheta = 360;

	private void OnEnable()
	{
		//SetDrawSceneLine(true);
		m_SelfCamera = GetComponent<Camera>();
	}

	/// <summary>
	/// 開關畫場景格線
	/// </summary>
	/// <param name="iIsOn"></param>
	[NoToLua]
	public void SetDrawSceneLine(bool iIsOn)
	{
		m_isDrawSceneLine = iIsOn;
		if (iIsOn)
		{
			Scene _ActScene = SceneManager.GetActiveScene();
			D.Log("活動中的場景: " + _ActScene.name);

			GameObject[] _RootObjs = _ActScene.GetRootGameObjects();
			
			// 根搜尋測試
			GameObject _TerrainObj = null;

			// 嘗試取得Scene Component
			for (int i = 0; i < _RootObjs.Length; i++)
			{
				string _RootObj = _RootObjs[i].name.Replace("S", "M");
				if (_ActScene.name == _RootObj)
				{
					_TerrainObj = GameObject.Find(_RootObjs[i].name + "/Terrain");
					// 沒找到，嘗試找地形+場景名稱
					if (_TerrainObj == null)
					{
						_TerrainObj = GameObject.Find(_RootObjs[i].name + "/Terrain" + _RootObjs[i].name);
						if (_TerrainObj != null)
						{
							GetTerrainByObj(_TerrainObj);
						}
					}
					else
					{
						GetTerrainByObj(_TerrainObj);
					}
					
					break;
				}
			}
		}
	}

	public void SetDrawLine(bool iIsOn)
	{
		m_isDrawSingleLine = iIsOn;
	}

	private void GetTerrainByObj(GameObject iTerrainObj)
	{
		Terrain _Terrain = null;
		
		// 有找到，抓看看有沒有Terrain
		_Terrain = iTerrainObj.GetComponentInChildren<Terrain>();
		if (_Terrain == null)
		{
			// 沒找到，放棄
			D.LogError("[GizmosDebug] Can't Get Scene Terrain. Please put the terrain into Component manually");
		}
		else
		{
			// 有找到，進去取長寬階段
			SetTerrain(_Terrain);
			D.Log("Terrain Name: " + _Terrain.name);
		}
	}

	private void SetTerrain(Terrain iTerrain)
	{
		m_CurrentTerrain = iTerrain;
		m_MaxSceneSize = m_CurrentTerrain.terrainData.size;
		m_RealHeight = iTerrain.transform.position.y;
	}

	public void SetDrawSetting(Vector3 iVecStart, Vector3 iVecEnd)
	{
		m_SingleLine_Start = iVecStart;
		m_SingleLine_End = iVecEnd;
		m_PointDistance = Vector3.Distance(m_SingleLine_Start, m_SingleLine_End);
	}
	
	public class WireData
	{
		public Vector3 m_Center; // 中心點
		public float m_Radius; // 半徑 - 圓形資訊
		public Vector3 m_Size; // 比例 - 方形資訊
		public Matrix4x4 m_Matrix; // 旋轉矩陣 - 方形資訊
		public float m_AddTime; // 添加時間
		public Vector3 m_Forward;
		public Quaternion m_Qua;
		public float m_FanAngle; // 扇形角度(全部)
	}

	private List<WireData> m_WireList = new List<WireData>();

	public void SetDrawArea(Vector3 iCenter, float iRadius, Vector3 iSize,float iAngle = 360.0f, Transform iTrans = null)
	{
		WireData _NewWire = new WireData();
		_NewWire.m_Center = iCenter;
		_NewWire.m_Radius = iRadius;
		_NewWire.m_Size = iSize;
		_NewWire.m_FanAngle = iAngle;
		
		if (iTrans == null)
		{
			_NewWire.m_Matrix = Matrix4x4.zero;
		}
		else
		{
			_NewWire.m_Forward = iTrans.forward;
			_NewWire.m_Qua = iTrans.rotation;
		}
		
		_NewWire.m_AddTime = Time.timeSinceLevelLoad;
		m_WireList.Add(_NewWire);
	}

	private void OnDrawGizmos()
	{
		// 畫全場景格線(目前沒用，但保留功能)
		if (m_isDrawSceneLine)
		{
			Gizmos.color = Color.green;
			
			Vector3 _VecStart = Vector3.zero;
			_VecStart.y = m_LineHeight + m_RealHeight;
			
			Vector3 _VecEnd = _VecStart;
			_VecEnd.z = m_MaxSceneSize.z;

			while (_VecStart.x <= m_MaxSceneSize.x)
			{
				Gizmos.DrawLine(_VecStart, _VecEnd);
				_VecStart.x++;
				_VecEnd.x++;
			}

			_VecStart.x = 0;
			_VecEnd = _VecStart;
			_VecEnd.x = m_MaxSceneSize.x;
			
			while (_VecStart.z <= m_MaxSceneSize.z)
			{
				Gizmos.DrawLine(_VecStart, _VecEnd);
				_VecStart.z++;
				_VecEnd.z++;
			}
		}

		if (m_isDrawSingleLine)
		{
			Gizmos.color = Color.yellow;
			GizmosDrawLine(m_SingleLine_Start, m_SingleLine_End, m_width);

			Gizmos.color = Color.blue;
			Matrix4x4 _DefaultMatrix = Gizmos.matrix;
			for (int i = 0; i < m_WireList.Count; i++)
			{
				if(m_WireList[i].m_Radius > 0 && m_WireList[i].m_FanAngle  < 360.0)
				{
					GizmoDrawFanRig(m_WireList[i], m_width);
				}
				else if (m_WireList[i].m_Radius > 0)
				{
					GizmosDrawWireRig(m_WireList[i], m_width);
				}
				else
				{
					GizmosDrawWireRect(m_WireList[i], m_width);
				}

				// 達顯示秒數後清除
				if (Time.timeSinceLevelLoad - m_WireList[i].m_AddTime > m_Duration)
				{
					m_WireList.RemoveAt(i);
					i--;
				}
			}

			Gizmos.matrix = _DefaultMatrix;
		}
	}

	#region  線段加粗畫法

	public void GizmosDrawLine(Vector3 iStart, Vector3 iEnd, float iWidth)
	{
		int _count = 1 + Mathf.CeilToInt(iWidth); // how many lines are needed.
		if (_count == 1)
		{
			Gizmos.DrawLine(iStart, iEnd);
		}
		else
		{
			if (m_SelfCamera == null)
			{
				//Debug.LogError("Camera.current is null");
				return;
			}
			
			// 把目標點轉換成螢幕空間
			var _scp1 = m_SelfCamera.WorldToScreenPoint(iStart);
			var _scp2 = m_SelfCamera.WorldToScreenPoint(iEnd);

			Vector3 _v1 = (_scp2 - _scp1).normalized; // line direction
			Vector3 _n = Vector3.Cross(_v1, Vector3.forward); // normal vector

			for (int i = 0; i < _count; i++)
			{
				Vector3 _o = 0.99f * _n * iWidth * ((float)i / (_count - 1) - 0.5f);
				Vector3 _origin = m_SelfCamera.ScreenToWorldPoint(_scp1 + _o);
				Vector3 _destiny = m_SelfCamera.ScreenToWorldPoint(_scp2 + _o);
				Gizmos.DrawLine(_origin, _destiny);
			}
		}
	}
	
	/// <summary>
	/// 畫球體
	/// </summary>
	/// <param name="iCenter"> 球中心點 </param>
	/// <param name="iRadius"> 半徑 </param>
	/// <param name="iWidth"> 線段寬度 </param>
	public void GizmosDrawWireSphere(Vector3 iCenter, float iRadius, float iWidth)
	{
		int _count = 1 + Mathf.CeilToInt(iWidth); // how many lines are needed.
		if (_count == 1)
		{
			Gizmos.DrawWireSphere(iCenter, iRadius);
		}
		else
		{
			if (m_SelfCamera == null)
			{
				Debug.LogError("Camera.current is null");
				return;
			}
			
			// 把目標點轉換成螢幕空間
			var _scp1 = m_SelfCamera.WorldToScreenPoint(iCenter);

			Vector3 _v1 = _scp1.normalized; // line direction
			Vector3 _n = Vector3.Cross(_v1, Vector3.forward); // normal vector

			for (int i = 0; i < _count; i++)
			{
				Vector3 _o = 0.99f * _n * iWidth * ((float)i / (_count - 1) - 0.5f);
				Vector3 _Center = m_SelfCamera.ScreenToWorldPoint(_scp1 + _o);
				Gizmos.DrawWireSphere(_Center, iRadius);
			}
		}
	}

	// 畫圓形線圈
	public void GizmosDrawWireRig(WireData iData, float iWidth)
	{
		float angle = 360f / m_RigTheta;
		List<Vector3> points = new List<Vector3>();
		Vector3 v = iData.m_Center + iData.m_Forward * iData.m_Radius; //求出直線的距離點
		
		Quaternion r = iData.m_Qua;
		for (int i = 1; i < m_RigTheta; i++)
		{
			Quaternion q = Quaternion.Euler(r.eulerAngles.x, r.eulerAngles.y - (angle * i), r.eulerAngles.z);//求出第i個點的旋轉角度
			v = iData.m_Center + (q * Vector3.forward) * iData.m_Radius; //i點的座標
			points.Add(v);
		}
		for (int i = 0; i < points.Count; i++)
		{
			if (i == points.Count - 1)
			{
				GizmosDrawLine(points[i], points[0], iWidth);
			}
			else
			{
				GizmosDrawLine(points[i], points[i + 1], iWidth);
			}
		}
	}
	
	// 畫長方體
	public void GizmosDrawWireCube(Vector3 iCenter, Vector3 iSize, float iWidth)
	{
		int _count = 1 + Mathf.CeilToInt(iWidth); // how many lines are needed.
		if (_count == 1)
		{
			Gizmos.DrawWireCube(iCenter, iSize);
		}
		else
		{
			if (m_SelfCamera == null)
			{
				Debug.LogError("Camera.current is null");
				return;
			}
			
			// 把目標點轉換成螢幕空間
			var _scp1 = m_SelfCamera.WorldToScreenPoint(iCenter);

			Vector3 _v1 = _scp1.normalized; // line direction
			Vector3 _n = Vector3.Cross(_v1, Vector3.forward); // normal vector

			for (int i = 0; i < _count; i++)
			{
				// 矩形偏移特別大，調低係數降低明顯的重影
				Vector3 _o = 0.15f * _n * iWidth * ((float)i / (_count - 1) - 0.5f);
				Vector3 _origin = m_SelfCamera.ScreenToWorldPoint(_scp1 + _o);
				Gizmos.DrawWireCube(_origin, iSize);
			}
		}
	}

	// 畫長方形
	public void GizmosDrawWireRect(WireData iData, float iWidth)
	{
		// 長寬寬計算
		Vector3 _Width = iData.m_Qua * Vector3.right * iData.m_Size.x;
		Vector3 _Height = iData.m_Forward * iData.m_Size.z;
		Vector3 _NewCenter = iData.m_Center + new Vector3(0, iData.m_Size.y, 0);
		
		// 算四點位
		Vector3 _LeftBottom = _NewCenter - (_Width/2);
		Vector3 _RightBottom = _NewCenter + (_Width/2);
		Vector3 _LeftTop = _LeftBottom + _Height;
		Vector3 _RightTop = _RightBottom + _Height;
		
		// 畫長方形
		GizmosDrawLine(_LeftBottom, _RightBottom, iWidth);
		GizmosDrawLine(_RightBottom, _RightTop, iWidth);
		GizmosDrawLine(_RightTop, _LeftTop, iWidth);
		GizmosDrawLine(_LeftTop, _LeftBottom, iWidth);
	}

	//畫扇型
	public void GizmoDrawFanRig(WireData iData, float iWidth)
	{
		float _angle = iData.m_FanAngle / m_RigTheta;
		float _startAngle = iData.m_FanAngle/2; // 一半的角度
		List<Vector3> points = new List<Vector3>();
		Vector3 v = iData.m_Center + iData.m_Forward * iData.m_Radius; //求出直線的距離點
		
		Quaternion r = iData.m_Qua;

		points.Add(iData.m_Center); //加入中心點
		for (int i = 1; i < m_RigTheta; i++)
		{
			Quaternion q = Quaternion.Euler(r.eulerAngles.x, r.eulerAngles.y - (_startAngle - _angle * i), r.eulerAngles.z);//求出第i個點的旋轉角度

			v = iData.m_Center + (q * Vector3.forward) * iData.m_Radius; //i點的座標
			points.Add(v);
		}
		
		for (int i = 0; i < points.Count; i++)
		{
			if (i == points.Count - 1)
			{
				GizmosDrawLine(points[i], points[0], iWidth);
			}
			else
			{
				GizmosDrawLine(points[i], points[i + 1], iWidth);
			}
		}
	}

	#endregion
	
}
