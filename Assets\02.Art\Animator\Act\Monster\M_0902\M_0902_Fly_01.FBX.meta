fileFormatVersion: 2
guid: e572694dbf6f2464998f2bfc50dd46c6
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Head
    100004: Bip001 L Clavicle
    100006: Bip001 L UpperArm
    100008: Bip001 Neck
    100010: Bip001 Pelvis
    100012: Bip001 R Clavicle
    100014: Bip001 R UpperArm
    100016: Bip001 Spine
    100018: Bip001 Spine1
    100020: Bone_ L_Finger_0
    100022: Bone_ L_Finger_01
    100024: Bone_ R_Finger_0
    100026: Bone_ R_Finger_01
    100028: Bone_F_skirt_001
    100030: Bone_F_skirt_002
    100032: Bone_F_skirt_003
    100034: Bone_L_ Forearm
    100036: Bone_L_Finger_1
    100038: Bone_L_Finger_11
    100040: Bone_L_Finger_2
    100042: Bone_L_Finger_21
    100044: Bone_L_Finger_3
    100046: <PERSON>_L_Finger_31
    100048: <PERSON>_L_Finger_4
    100050: <PERSON>_<PERSON>_Finger_41
    100052: Bone_L_Hand
    100054: <PERSON>_<PERSON>_shoulder_01
    100056: <PERSON>_L_wing_01
    100058: Bone_R_ Forearm
    100060: Bone_R_Finger_1
    100062: Bone_R_Finger_11
    100064: Bone_R_Finger_2
    100066: Bone_R_Finger_21
    100068: Bone_R_Finger_3
    100070: Bone_R_Finger_31
    100072: Bone_R_Finger_4
    100074: Bone_R_Finger_41
    100076: Bone_R_Hand
    100078: Bone_R_shoulder_01
    100080: Bone_R_wing_01
    100082: Bone_skirt_01
    100084: Dummy_L_ Forearm_002
    100086: Dummy_L_ UpperArm_001
    100088: Dummy_L_shoulder_002
    100090: Dummy_L_wing_001
    100092: Dummy_R_ Forearm_001
    100094: Dummy_R_ UpperArm_002
    100096: Dummy_R_shoulder_001
    100098: Dummy_R_wing_001
    100100: Dummy_wing_001
    100102: //RootNode
    400000: Bip001
    400002: Bip001 Head
    400004: Bip001 L Clavicle
    400006: Bip001 L UpperArm
    400008: Bip001 Neck
    400010: Bip001 Pelvis
    400012: Bip001 R Clavicle
    400014: Bip001 R UpperArm
    400016: Bip001 Spine
    400018: Bip001 Spine1
    400020: Bone_ L_Finger_0
    400022: Bone_ L_Finger_01
    400024: Bone_ R_Finger_0
    400026: Bone_ R_Finger_01
    400028: Bone_F_skirt_001
    400030: Bone_F_skirt_002
    400032: Bone_F_skirt_003
    400034: Bone_L_ Forearm
    400036: Bone_L_Finger_1
    400038: Bone_L_Finger_11
    400040: Bone_L_Finger_2
    400042: Bone_L_Finger_21
    400044: Bone_L_Finger_3
    400046: Bone_L_Finger_31
    400048: Bone_L_Finger_4
    400050: Bone_L_Finger_41
    400052: Bone_L_Hand
    400054: Bone_L_shoulder_01
    400056: Bone_L_wing_01
    400058: Bone_R_ Forearm
    400060: Bone_R_Finger_1
    400062: Bone_R_Finger_11
    400064: Bone_R_Finger_2
    400066: Bone_R_Finger_21
    400068: Bone_R_Finger_3
    400070: Bone_R_Finger_31
    400072: Bone_R_Finger_4
    400074: Bone_R_Finger_41
    400076: Bone_R_Hand
    400078: Bone_R_shoulder_01
    400080: Bone_R_wing_01
    400082: Bone_skirt_01
    400084: Dummy_L_ Forearm_002
    400086: Dummy_L_ UpperArm_001
    400088: Dummy_L_shoulder_002
    400090: Dummy_L_wing_001
    400092: Dummy_R_ Forearm_001
    400094: Dummy_R_ UpperArm_002
    400096: Dummy_R_shoulder_001
    400098: Dummy_R_wing_001
    400100: Dummy_wing_001
    400102: //RootNode
    7400000: M_0902_Fly_01
    7400002: M_0902_GetUp_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0902_Fly_01
      takeName: M_0902_Fly_01
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_0902_GetUp_01
      takeName: M_0902_Fly_01
      firstFrame: 20
      lastFrame: 65
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 8f371ef3a78978e47a0fda238d618546,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
