%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &320870945867298308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2692156290917923973}
  - component: {fileID: 2064243471097067206}
  - component: {fileID: 5628762125892036294}
  m_Layer: 5
  m_Name: Text_Title_External
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2692156290917923973
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 320870945867298308}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2713458228478884432}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -19}
  m_SizeDelta: {x: 400, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2064243471097067206
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 320870945867298308}
  m_CullTransparentMesh: 1
--- !u!114 &5628762125892036294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 320870945867298308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '@21000001@'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 22db25345aed61a49af66fd6c5af6952, type: 2}
  m_sharedMaterial: {fileID: 21847876457680786, guid: 22db25345aed61a49af66fd6c5af6952,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: 87
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 40
  m_fontSizeBase: 40
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &3683372315018468070
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4343156681185612239}
  - component: {fileID: 5589887771759058481}
  - component: {fileID: 415593881464520169}
  m_Layer: 5
  m_Name: '&MainPanel'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4343156681185612239
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3683372315018468070}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1944881320782928469}
  - {fileID: 7086152813241683455}
  m_Father: {fileID: 5186040462583654090}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5589887771759058481
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3683372315018468070}
  m_CullTransparentMesh: 1
--- !u!114 &415593881464520169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3683372315018468070}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47f9a81376f4b194c9f63e5503937dbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &4704780897611868684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8076084315965991305}
  - component: {fileID: 7635250293757984264}
  - component: {fileID: 756346983425435829}
  m_Layer: 5
  m_Name: Text_Caption_Inner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8076084315965991305
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4704780897611868684}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4869595004569651119}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -150}
  m_SizeDelta: {x: 400, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7635250293757984264
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4704780897611868684}
  m_CullTransparentMesh: 1
--- !u!114 &756346983425435829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4704780897611868684}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '@21000004@'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 22db25345aed61a49af66fd6c5af6952, type: 2}
  m_sharedMaterial: {fileID: 21847876457680786, guid: 22db25345aed61a49af66fd6c5af6952,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: 87
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 40
  m_fontSizeBase: 40
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &5203983426576658605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2713458228478884432}
  - component: {fileID: 6336723221883476025}
  - component: {fileID: 902630846887950467}
  - component: {fileID: 1803068223273189956}
  m_Layer: 5
  m_Name: '&Btn_External'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2713458228478884432
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5203983426576658605}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2585948465769330629}
  - {fileID: 2692156290917923973}
  - {fileID: 2692318204121649184}
  m_Father: {fileID: 7086152813241683455}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 500, y: 600}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &6336723221883476025
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5203983426576658605}
  m_CullTransparentMesh: 1
--- !u!114 &902630846887950467
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5203983426576658605}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.371529, g: 0.39589232, b: 0.6509434, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1803068223273189956
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5203983426576658605}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 902630846887950467}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 5628762125892036294}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 0}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!1 &5314170009756019330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2585948465769330629}
  - component: {fileID: 677181547299057683}
  - component: {fileID: 4431290050519800103}
  m_Layer: 5
  m_Name: Image_External
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2585948465769330629
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5314170009756019330}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2713458228478884432}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 148}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &677181547299057683
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5314170009756019330}
  m_CullTransparentMesh: 1
--- !u!114 &4431290050519800103
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5314170009756019330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: d16f88b6eaab3014a8bd0f8ccdc8a947, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &5513745322802404078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2692318204121649184}
  - component: {fileID: 613065509359058049}
  - component: {fileID: 7510532073628410903}
  m_Layer: 5
  m_Name: Text_Caption_External
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2692318204121649184
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5513745322802404078}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2713458228478884432}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -150}
  m_SizeDelta: {x: 400, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &613065509359058049
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5513745322802404078}
  m_CullTransparentMesh: 1
--- !u!114 &7510532073628410903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5513745322802404078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '@21000002@'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 22db25345aed61a49af66fd6c5af6952, type: 2}
  m_sharedMaterial: {fileID: 21847876457680786, guid: 22db25345aed61a49af66fd6c5af6952,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: 87
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 40
  m_fontSizeBase: 40
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &6184518546520483455
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7086152813241683455}
  - component: {fileID: 4785500185877306876}
  - component: {fileID: 2438141089071836567}
  - component: {fileID: 4560435751056525519}
  m_Layer: 5
  m_Name: Group_Skill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7086152813241683455
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6184518546520483455}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2713458228478884432}
  - {fileID: 4869595004569651119}
  m_Father: {fileID: 4343156681185612239}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4785500185877306876
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6184518546520483455}
  m_CullTransparentMesh: 1
--- !u!114 &2438141089071836567
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6184518546520483455}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19668922, g: 0.22468202, b: 0.6415094, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &4560435751056525519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6184518546520483455}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 4
  m_Spacing: -200
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 1
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &6428336166109463950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5186040462583654090}
  - component: {fileID: 3874794813053100487}
  - component: {fileID: 4097944853164304954}
  - component: {fileID: 6445497696144152050}
  m_Layer: 5
  m_Name: QuantumEchoEntry_View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5186040462583654090
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6428336166109463950}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7707689263990484355}
  - {fileID: 4343156681185612239}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &3874794813053100487
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6428336166109463950}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &4097944853164304954
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6428336166109463950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &6445497696144152050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6428336166109463950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c3a974cff652d7f4991d1011f8c675a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsAutoRef: 1
  m_Dic_Trans:
    m_List:
    - Key: '&MainPanel'
      Value: {fileID: 4343156681185612239}
    - Key: '&Button_Back'
      Value: {fileID: 8050791935695149262}
    - Key: '&Button_Close_Blue'
      Value: {fileID: 4697861930224463008}
    - Key: '&Button_Hint'
      Value: {fileID: 6720985746821006962}
    - Key: '&Group_Resources'
      Value: {fileID: 5076660649145903244}
    - Key: '&Button_Add'
      Value: {fileID: 2114310955289416560}
    - Key: '&Btn_External'
      Value: {fileID: 2713458228478884432}
    - Key: '&Btn_Inner'
      Value: {fileID: 4869595004569651119}
  m_Dic_Canvas:
    m_List: []
  m_Dic_Image:
    m_List:
    - Key: '&Button_Back'
      Value: {fileID: 5023809859093768339}
    - Key: '&Button_Close_Blue'
      Value: {fileID: 2004415629180383959}
    - Key: '&Button_Hint'
      Value: {fileID: 5076494974724398758}
    - Key: '&Button_Add'
      Value: {fileID: 2879961374219038798}
    - Key: '&Btn_External'
      Value: {fileID: 902630846887950467}
    - Key: '&Btn_Inner'
      Value: {fileID: 516339265780339174}
  m_Dic_RawImage:
    m_List: []
  m_Dic_Toggle:
    m_List: []
  m_Dic_Slider:
    m_List: []
  m_Dic_TMPText:
    m_List: []
  m_Dic_TMPInputField:
    m_List: []
  m_Dic_GroupButtonCtrl:
    m_List: []
  m_Dic_ButtonEx:
    m_List:
    - Key: '&Button_Back'
      Value: {fileID: 3142980300337897722}
    - Key: '&Button_Close_Blue'
      Value: {fileID: 6651110286590534098}
    - Key: '&Button_Hint'
      Value: {fileID: 1875329340354321407}
    - Key: '&Button_Add'
      Value: {fileID: 403666493268205533}
    - Key: '&Btn_External'
      Value: {fileID: 1803068223273189956}
    - Key: '&Btn_Inner'
      Value: {fileID: 1643022255257742980}
  m_Dic_Dropdown:
    m_List: []
  m_Dic_AutoReplace_TMPText:
    m_List:
    - Key: {fileID: 5628762125892036294}
      Value: 21000001
    - Key: {fileID: 7510532073628410903}
      Value: 21000002
    - Key: {fileID: 2056191613859556498}
      Value: 21000003
    - Key: {fileID: 756346983425435829}
      Value: 21000004
  m_Dic_UIAnimation:
    m_List: []
  m_Dic_Image_Language:
    m_List: []
  m_Dic_UITeachComponent:
    m_List:
    - Key: '&Button_Close_Blue'
      Value: {fileID: 1193380198227833135}
--- !u!1 &7958426781804633527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7771429872289890170}
  - component: {fileID: 4082830410640753703}
  - component: {fileID: 6254078843184799582}
  m_Layer: 5
  m_Name: Image_Inner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7771429872289890170
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7958426781804633527}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4869595004569651119}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 148}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4082830410640753703
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7958426781804633527}
  m_CullTransparentMesh: 1
--- !u!114 &6254078843184799582
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7958426781804633527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 6fa01104a6ed7b7418ae330cda3ca894, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &8085865042869248641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7707689263990484355}
  - component: {fileID: 7380884811290355098}
  m_Layer: 5
  m_Name: FixedPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7707689263990484355
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8085865042869248641}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5186040462583654090}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &7380884811290355098
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8085865042869248641}
  m_CullTransparentMesh: 1
--- !u!1 &8398443048707008382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4165976576392501204}
  - component: {fileID: 2626008754839657131}
  - component: {fileID: 2056191613859556498}
  m_Layer: 5
  m_Name: Text_Title_Inner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4165976576392501204
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8398443048707008382}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4869595004569651119}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -19}
  m_SizeDelta: {x: 400, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2626008754839657131
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8398443048707008382}
  m_CullTransparentMesh: 1
--- !u!114 &2056191613859556498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8398443048707008382}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: '@21000003@'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 22db25345aed61a49af66fd6c5af6952, type: 2}
  m_sharedMaterial: {fileID: 21847876457680786, guid: 22db25345aed61a49af66fd6c5af6952,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: 87
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 40
  m_fontSizeBase: 40
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &8655671899984784215
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4869595004569651119}
  - component: {fileID: 4577387027655903051}
  - component: {fileID: 516339265780339174}
  - component: {fileID: 1643022255257742980}
  m_Layer: 5
  m_Name: '&Btn_Inner'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4869595004569651119
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8655671899984784215}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7771429872289890170}
  - {fileID: 4165976576392501204}
  - {fileID: 8076084315965991305}
  m_Father: {fileID: 7086152813241683455}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 500, y: 600}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4577387027655903051
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8655671899984784215}
  m_CullTransparentMesh: 1
--- !u!114 &516339265780339174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8655671899984784215}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.371529, g: 0.39589232, b: 0.6509434, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1643022255257742980
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8655671899984784215}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 516339265780339174}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 2056191613859556498}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 0}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!1001 &4937287581147775693
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 4343156681185612239}
    m_Modifications:
    - target: {fileID: 213691269762345537, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 266.34
      objectReference: {fileID: 0}
    - target: {fileID: 1461131174947038948, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 56
      objectReference: {fileID: 0}
    - target: {fileID: 1855896497342121151, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1855896497342121151, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1855896497342121151, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 207.44
      objectReference: {fileID: 0}
    - target: {fileID: 1855896497342121151, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -8.419994
      objectReference: {fileID: 0}
    - target: {fileID: 2966577986756852993, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 249.44
      objectReference: {fileID: 0}
    - target: {fileID: 3116187997225261571, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 72.32205
      objectReference: {fileID: 0}
    - target: {fileID: 4935445167428122593, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4935445167428122593, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4935445167428122593, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 133.17
      objectReference: {fileID: 0}
    - target: {fileID: 4935445167428122593, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -40.6607
      objectReference: {fileID: 0}
    - target: {fileID: 5943676599705373462, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_fontSize
      value: 23.3
      objectReference: {fileID: 0}
    - target: {fileID: 6472603375203307965, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 40
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_Pivot.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 76.355
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7239776489963647006, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8299762757379329914, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_Name
      value: Group_TitleBar
      objectReference: {fileID: 0}
    - target: {fileID: 8299762757379329914, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9066683931933106301, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9066683931933106301, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9066683931933106301, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 175.44
      objectReference: {fileID: 0}
    - target: {fileID: 9066683931933106301, guid: d06cac9922412764d81b8d61a3d3ef71,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -29.42
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 608321391948246190, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
    - {fileID: 8405687186994114036, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
    - {fileID: 4719766994947423443, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
    - {fileID: 5016643467362268492, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
    - {fileID: 7309724604973649813, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: d06cac9922412764d81b8d61a3d3ef71, type: 3}
--- !u!114 &621472542925028115 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5484026557774608862, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416732235676688467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1416732235676688467 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6281952565543854750, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1875329340354321407
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416732235676688467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 5076494974724398758}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 0}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 621472542925028115}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!114 &1895084021332041738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416732235676688467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1717168324934650037 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6003403423532878456, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1963525380990000588
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717168324934650037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &3142980300337897722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717168324934650037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 5023809859093768339}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 0}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 5746616290654916804}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!224 &1944881320782928469 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6807577473279973528, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2004415629180383959 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6869635952479531034, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2759309196183662166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &2114310955289416560 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6472603375203307965, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2200218030461924372 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6488695584241055449, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2759309196183662166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2376808485372506835 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7239776489963647006, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &403666493268205533
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2376808485372506835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2879961374219038798}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 0}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 7627713905040541116}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!114 &7627713905040541116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2376808485372506835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2759309196183662166 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7120136023542869147, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1193380198227833135
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2759309196183662166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a36062235f6ba584e82164543dfd2645, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ETeachComponentType: Button
  m_Image_TeachComponent: {fileID: 2004415629180383959}
  m_IsUseImageWidthAndHeight: 0
  m_IsTeachComponentInChildren: 0
  m_UIAnimation: {fileID: 0}
--- !u!114 &3807175031257520110
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2759309196183662166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &6651110286590534098
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2759309196183662166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2004415629180383959}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 0}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 2200218030461924372}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!114 &2879961374219038798 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7166187400914875011, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2376808485372506835}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &3532349487608585022 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 8467235423565093363, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4720542399235169161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &4697861930224463008 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 411776366407511661, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4720542399235169161 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 362109484507060548, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2241575156414684387
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4720542399235169161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f780cc96ee9c5e4993beb0e3b04d8ae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 6248763847425274451}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
  m_IsSwitchBtn: 0
  m_IndexInGroup: -1
  m_TMP_BtnText: {fileID: 0}
  m_ButtonRedPoint: {fileID: 0}
  m_UIRenderCtrl: {fileID: 3532349487608585022}
  m_GroupButtonCtrl: {fileID: 0}
  m_ClickAudioIdx: 0
  m_IsUseParentRender: 0
  m_IsNotDoStateTransitionWithButton: 0
  m_CurState: 0
  m_LastState: 0
  m_SetStateWhenEnable: 0
  m_IsCanUseCallBack: 1
--- !u!114 &6433478234526324395
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4720542399235169161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &5023809859093768339 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 89206223678827102, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717168324934650037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &5076494974724398758 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 213799679887023211, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416732235676688467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &5076660649145903244 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 213691269762345537, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5746616290654916804 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 812004681499234825, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717168324934650037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5705b012b5ac3c5458b095d5a2fb7ca2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &6248763847425274451 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1314159910431551646, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4720542399235169161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &6720985746821006962 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 1855896497342121151, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &8050791935695149262 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 3116187997225261571, guid: d06cac9922412764d81b8d61a3d3ef71,
    type: 3}
  m_PrefabInstance: {fileID: 4937287581147775693}
  m_PrefabAsset: {fileID: 0}
