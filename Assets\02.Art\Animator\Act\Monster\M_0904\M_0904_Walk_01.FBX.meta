fileFormatVersion: 2
guid: 2fd96dc1f32640a438a04de806b87eac
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Head
    100004: Bip001 L Clavicle
    100006: Bip001 L UpperArm
    100008: Bip001 Neck
    100010: Bip001 Pelvis
    100012: Bip001 R Clavicle
    100014: Bip001 R UpperArm
    100016: Bip001 Spine
    100018: Bip001 Spine1
    100020: Bone_ L_Finger_0
    100022: Bone_ L_Finger_01
    100024: Bone_ R_Finger_0
    100026: Bone_ R_Finger_01
    100028: Bone_L_ Forearm
    100030: Bone_L_Finger_1
    100032: <PERSON>_L_Finger_11
    100034: Bone_L_Finger_2
    100036: Bone_L_Finger_21
    100038: Bone_L_Finger_3
    100040: Bone_L_Finger_31
    100042: Bone_L_Finger_4
    100044: Bone_L_Finger_41
    100046: Bone_L_Hand
    100048: Bone_L_ribbon_01
    100050: <PERSON>_L_ribbon_02
    100052: Bone_L_ribbon_03
    100054: <PERSON>_<PERSON>_shoulder_01
    100056: <PERSON>_L_skirt_01
    100058: <PERSON>_L_skirt_02
    100060: Bone_L_wing_01
    100062: Bone_L_wing_02
    100064: Bone_R_ Forearm
    100066: Bone_R_Finger_1
    100068: Bone_R_Finger_11
    100070: Bone_R_Finger_2
    100072: Bone_R_Finger_21
    100074: Bone_R_Finger_3
    100076: Bone_R_Finger_31
    100078: Bone_R_Finger_4
    100080: Bone_R_Finger_41
    100082: Bone_R_Hand
    100084: Bone_R_ribbon_01
    100086: Bone_R_ribbon_02
    100088: Bone_R_ribbon_03
    100090: Bone_R_shoulder_01
    100092: Bone_R_skirt_01
    100094: Bone_R_skirt_02
    100096: Bone_R_wing_01
    100098: Bone_R_wing_02
    100100: Dummy_L_ Forearm_002
    100102: Dummy_L_shoulder_002
    100104: Dummy_L_skirt_002
    100106: Dummy_L_wing_001
    100108: Dummy_L_wing_002
    100110: Dummy_R_ Forearm_001
    100112: Dummy_R_shoulder_001
    100114: Dummy_R_skirt_001
    100116: Dummy_R_wing_001
    100118: Dummy_R_wing_002
    100120: Dummy_wing_001
    100122: //RootNode
    400000: Bip001
    400002: Bip001 Head
    400004: Bip001 L Clavicle
    400006: Bip001 L UpperArm
    400008: Bip001 Neck
    400010: Bip001 Pelvis
    400012: Bip001 R Clavicle
    400014: Bip001 R UpperArm
    400016: Bip001 Spine
    400018: Bip001 Spine1
    400020: Bone_ L_Finger_0
    400022: Bone_ L_Finger_01
    400024: Bone_ R_Finger_0
    400026: Bone_ R_Finger_01
    400028: Bone_L_ Forearm
    400030: Bone_L_Finger_1
    400032: Bone_L_Finger_11
    400034: Bone_L_Finger_2
    400036: Bone_L_Finger_21
    400038: Bone_L_Finger_3
    400040: Bone_L_Finger_31
    400042: Bone_L_Finger_4
    400044: Bone_L_Finger_41
    400046: Bone_L_Hand
    400048: Bone_L_ribbon_01
    400050: Bone_L_ribbon_02
    400052: Bone_L_ribbon_03
    400054: Bone_L_shoulder_01
    400056: Bone_L_skirt_01
    400058: Bone_L_skirt_02
    400060: Bone_L_wing_01
    400062: Bone_L_wing_02
    400064: Bone_R_ Forearm
    400066: Bone_R_Finger_1
    400068: Bone_R_Finger_11
    400070: Bone_R_Finger_2
    400072: Bone_R_Finger_21
    400074: Bone_R_Finger_3
    400076: Bone_R_Finger_31
    400078: Bone_R_Finger_4
    400080: Bone_R_Finger_41
    400082: Bone_R_Hand
    400084: Bone_R_ribbon_01
    400086: Bone_R_ribbon_02
    400088: Bone_R_ribbon_03
    400090: Bone_R_shoulder_01
    400092: Bone_R_skirt_01
    400094: Bone_R_skirt_02
    400096: Bone_R_wing_01
    400098: Bone_R_wing_02
    400100: Dummy_L_ Forearm_002
    400102: Dummy_L_shoulder_002
    400104: Dummy_L_skirt_002
    400106: Dummy_L_wing_001
    400108: Dummy_L_wing_002
    400110: Dummy_R_ Forearm_001
    400112: Dummy_R_shoulder_001
    400114: Dummy_R_skirt_001
    400116: Dummy_R_wing_001
    400118: Dummy_R_wing_002
    400120: Dummy_wing_001
    400122: //RootNode
    7400000: M_0904_Walk_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0904_Walk_01
      takeName: M_0904_Walk_01
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: efb3f663637474f448c78c496b6911e1,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
