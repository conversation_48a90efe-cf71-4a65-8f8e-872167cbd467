fileFormatVersion: 2
guid: 628daacf7e0336941962fc044c15c64e
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Chicken_Chest_01_01SHJnt
    100002: Chicken_Chest_01_02SHJnt
    100004: Chicken_Chest_01_03SHJnt
    100006: Chicken_Comb_01_01SHJnt
    100008: Chicken_Comb_01_02SHJnt
    100010: Chicken_Head_JawEndSHJnt
    100012: Chicken_Head_JawSHJnt
    100014: Chicken_Head_TopSHJnt
    100016: Chicken_l_Leg_AnkleSHJnt
    100018: Chicken_l_Leg_BallSHJnt
    100020: Chicken_l_Leg_HipSHJnt
    100022: Chicken_l_Leg_KneeSHJnt
    100024: Chicken_l_Leg_ToeSHJnt
    100026: Chicken_l_Toe_01_01SHJnt
    100028: Chicken_l_Toe_01_02SHJnt
    100030: Chicken_l_Toe_01_03SHJnt
    100032: Chicken_l_Toe_02_01SHJnt
    100034: Chicken_l_Toe_02_02SHJnt
    100036: Chicken_l_Toe_02_03SHJnt
    100038: Chicken_l_Toe_03_01SHJnt
    100040: Chicken_l_Toe_03_02SHJnt
    100042: Chicken_l_Toe_03_03SHJnt
    100044: Chicken_l_Wattle_01_01SHJnt
    100046: Chicken_l_Wattle_01_02SHJnt
    100048: Chicken_MAINSHJnt
    100050: Chicken_Neck_01_01SHJnt
    100052: Chicken_Neck_01_02SHJnt
    100054: Chicken_Neck_01_03SHJnt
    100056: Chicken_r_Leg_AnkleSHJnt
    100058: Chicken_r_Leg_BallSHJnt
    100060: Chicken_r_Leg_HipSHJnt
    100062: Chicken_r_Leg_KneeSHJnt
    100064: Chicken_r_Leg_ToeSHJnt
    100066: Chicken_r_Toe_01_01SHJnt
    100068: Chicken_r_Toe_01_02SHJnt
    100070: Chicken_r_Toe_01_03SHJnt
    100072: Chicken_r_Toe_02_01SHJnt
    100074: Chicken_r_Toe_02_02SHJnt
    100076: Chicken_r_Toe_02_03SHJnt
    100078: Chicken_r_Toe_03_01SHJnt
    100080: Chicken_r_Toe_03_02SHJnt
    100082: Chicken_r_Toe_03_03SHJnt
    100084: Chicken_r_Wattle_01_01SHJnt
    100086: Chicken_r_Wattle_01_02SHJnt
    100088: Chicken_ROOTSHJnt
    100090: Chicken_Tail_01_01SHJnt
    100092: Chicken_Tail_01_02SHJnt
    100094: Chicken_Tail_01_03SHJnt
    100096: //RootNode
    400000: Chicken_Chest_01_01SHJnt
    400002: Chicken_Chest_01_02SHJnt
    400004: Chicken_Chest_01_03SHJnt
    400006: Chicken_Comb_01_01SHJnt
    400008: Chicken_Comb_01_02SHJnt
    400010: Chicken_Head_JawEndSHJnt
    400012: Chicken_Head_JawSHJnt
    400014: Chicken_Head_TopSHJnt
    400016: Chicken_l_Leg_AnkleSHJnt
    400018: Chicken_l_Leg_BallSHJnt
    400020: Chicken_l_Leg_HipSHJnt
    400022: Chicken_l_Leg_KneeSHJnt
    400024: Chicken_l_Leg_ToeSHJnt
    400026: Chicken_l_Toe_01_01SHJnt
    400028: Chicken_l_Toe_01_02SHJnt
    400030: Chicken_l_Toe_01_03SHJnt
    400032: Chicken_l_Toe_02_01SHJnt
    400034: Chicken_l_Toe_02_02SHJnt
    400036: Chicken_l_Toe_02_03SHJnt
    400038: Chicken_l_Toe_03_01SHJnt
    400040: Chicken_l_Toe_03_02SHJnt
    400042: Chicken_l_Toe_03_03SHJnt
    400044: Chicken_l_Wattle_01_01SHJnt
    400046: Chicken_l_Wattle_01_02SHJnt
    400048: Chicken_MAINSHJnt
    400050: Chicken_Neck_01_01SHJnt
    400052: Chicken_Neck_01_02SHJnt
    400054: Chicken_Neck_01_03SHJnt
    400056: Chicken_r_Leg_AnkleSHJnt
    400058: Chicken_r_Leg_BallSHJnt
    400060: Chicken_r_Leg_HipSHJnt
    400062: Chicken_r_Leg_KneeSHJnt
    400064: Chicken_r_Leg_ToeSHJnt
    400066: Chicken_r_Toe_01_01SHJnt
    400068: Chicken_r_Toe_01_02SHJnt
    400070: Chicken_r_Toe_01_03SHJnt
    400072: Chicken_r_Toe_02_01SHJnt
    400074: Chicken_r_Toe_02_02SHJnt
    400076: Chicken_r_Toe_02_03SHJnt
    400078: Chicken_r_Toe_03_01SHJnt
    400080: Chicken_r_Toe_03_02SHJnt
    400082: Chicken_r_Toe_03_03SHJnt
    400084: Chicken_r_Wattle_01_01SHJnt
    400086: Chicken_r_Wattle_01_02SHJnt
    400088: Chicken_ROOTSHJnt
    400090: Chicken_Tail_01_01SHJnt
    400092: Chicken_Tail_01_02SHJnt
    400094: Chicken_Tail_01_03SHJnt
    400096: //RootNode
    7400000: Walk
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Walk
      takeName: Take 001
      firstFrame: 1
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: bd02583cf8bd2d145ab8217d0969ea9b,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
