﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功系統 Controller
---@class InnerBook_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.20
InnerBook_Controller = {}
local this = InnerBook_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("InnerBook_View", "InnerBook_Controller", EUIOrderLayers.FullPage, false, "bg_013")

require("UI/InnerBook/InnerBook_Model")
require("UI/InnerBook/MethodRun_Controller")
require("UI/InnerBook/SpiritVeins_Controller")
require("UI/InnerBook/SixSpirit_Controller")
require("UI/InnerBook/SpiritLiberation_Controller")

InnerBook_Controller[1] = MethodRun_Controller
InnerBook_Controller[2] = SpiritVeins_Controller
InnerBook_Controller[3] = SixSpirit_Controller
InnerBook_Controller[4] = SpiritLiberation_Controller

---各分頁物件
this.m_PageGameObject = {}

---靈脈屬性圖顏色
local _Image_Attr_NormalImg = {}
local _Image_Attr_SelectImg = {}

---穴位圖顏色
local _Image_Point_Color = Extension.GetColor("#86B5E3")

---穴位圖大小
local _Image_Point_NormalSize = Vector2(80, 80)
local _Image_Point_DisableSize = Vector2(40, 40)

---初始化
function InnerBook_Controller.Init()
	this.m_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&MainPanel")
	--資源列表
	local _CurrentResourceTable =
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
	this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_MainPanel, 0, "", _CurrentResourceTable)

    --心法運行
	this.m_PageGameObject[EInnerBookTab.MethodRun] = this.m_ViewRef.m_Dic_Trans:Get("&MethodRun_View").gameObject
    --靈脈貫通
    this.m_PageGameObject[EInnerBookTab.SpiritVeins] = this.m_ViewRef.m_Dic_Trans:Get("&SpiritVeins_View").gameObject
    --六脈靈功
    this.m_PageGameObject[EInnerBookTab.SixSpirit] = this.m_ViewRef.m_Dic_Trans:Get("&SixSpirit_View").gameObject
    --靈氣解放
    this.m_PageGameObject[EInnerBookTab.SpiritLiberation] = this.m_ViewRef.m_Dic_Trans:Get("&SpiritLiberation_View").gameObject

    --遮罩
    this.m_Panel_Mask = this.m_ViewRef.m_Dic_Trans:Get("&Panel_Mask").gameObject
    this.m_Image_SoftMask = this.m_ViewRef.m_Dic_Trans:Get("&Image_SoftMask").gameObject:GetComponent(typeof(SoftMask))

    for i = 1, table.Count(this.m_PageGameObject) do
		if InnerBook_Controller[i] ~= nil then
			InnerBook_Controller[i].Init(this)
		end
	end

    --region 靈脈貫通&六脈靈功共用元件
    this.m_Group_CommonMeridian = this.m_ViewRef.m_Dic_Trans:Get("&Group_CommonMeridian")    

    --靈脈元件
    this.m_GObj_Meridian = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Meridian")
    this.m_MeridianData = {}

    --奇經
    this.m_Text_QiMeridian = this.m_ViewRef.m_Dic_TMPText:Get("&Text_QiMeridian")
    this.m_Text_QiMeridian.text = GString.Format(TextData.Get(10101010), TextData.Get(20112037), TextData.Get(20112029))
    this.m_Group_Qi = this.m_ViewRef.m_Dic_Trans:Get("&Group_Qi")
    --正經
    this.m_Text_ZhangMeridian = this.m_ViewRef.m_Dic_TMPText:Get("&Text_ZhangMeridian")
    this.m_Text_ZhangMeridian.text = GString.Format(TextData.Get(10101010), TextData.Get(20112037), TextData.Get(20112030))
    this.m_Group_Zhang = this.m_ViewRef.m_Dic_Trans:Get("&Group_Zhang")
    for i = 1, InnerBook_Model.m_MeridianCount do
        local _Object = nil
        if i == 1 then
            _Object =  this.m_GObj_Meridian
        else
            _Object =  this.m_GObj_Meridian:Instantiate( this.m_GObj_Meridian )
            local _Parent = i <= 2 and this.m_Group_Qi or this.m_Group_Zhang
            _Object:SetParent(_Parent)
        end
        _Object:SetSiblingIndex(i - 1)
       
        local _Info = {}
        _Info.m_GameObject = _Object.gameObject
        --進度Icon
        _Info.m_Image_Circle = _Object:Find("Image_Circle_BG/Image_Circle").gameObject:GetComponent(typeof( Image ))
        --完成度%數
        local _Text_Complete = _Object:Find("Image_Circle_BG/Text_Complete").gameObject
        _Info.m_Text_Complete = _Text_Complete:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        --靈脈名稱
        local _Text_Name = _Object:Find("Text_Name").gameObject
        _Info.m_Text_Name = _Text_Name:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        --靈脈階段
        local _Text_Step = _Object:Find("Text_Step").gameObject
        _Info.m_Text_Step = _Text_Step:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        --靈脈屬性
        _Info.m_Image_Att = {}
        _Image_Attr_NormalImg[i] = {}
        _Image_Attr_SelectImg[i] = {}
        for j = 1, 2 do
            _Info.m_Image_Att[j] = {}
            _Info.m_Image_Att[j].Image = _Object:Find("Image_Att" .. j).gameObject:GetComponent(typeof( Image ))
            _Info.m_Image_Att[j].Render = _Object:Find("Image_Att" .. j).gameObject:GetComponent(typeof( UIImageChange ))
            SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Normal][j][InnerBook_Model.m_MeridianMark[j][i]], function(iSprite)
                _Info.m_Image_Att[j].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, iSprite)
                table.insert(_Image_Attr_NormalImg[i], iSprite)
            end)
            SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Highlighted][j][InnerBook_Model.m_MeridianMark[j][i]], function(iSprite)
                _Info.m_Image_Att[j].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Highlighted, iSprite)
            end)
            SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Selected][j][InnerBook_Model.m_MeridianMark[j][i]], function(iSprite)
                _Info.m_Image_Att[j].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Selected, iSprite)
                table.insert(_Image_Attr_SelectImg[i], iSprite)
            end)
            _Info.m_Image_Att[j].Image.sprite = _Info.m_Image_Att[j].Render.m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal)
        end

        --按鈕
        _Info.m_Btn = Button.New(_Info.m_GameObject)
        _Info.m_Btn:AddListener(EventTriggerType.PointerClick, function() InnerBook_Controller.OnClick_Meridian(i) end)
        
        --設定靈脈初始資訊
        _Info.m_Text_Complete.text = math.floor((InnerBook_Model.m_MeridianData[i].m_Point / 9) * 100) .. "%"
        _Info.m_Image_Circle.fillAmount = InnerBook_Model.m_MeridianData[i].m_Point / 9
        _Info.m_Text_Name.text = TextData.Get(20112030 + i)
        _Info.m_Text_Step.text = TextData.Get(21002050 + InnerBook_Model.m_MeridianData[i].m_Step)

        table.insert(this.m_MeridianData, _Info)
    end

    --穴位
    this.m_Panel_AcuPoint = this.m_ViewRef.m_Dic_Trans:Get("&Panel_AcuPoint")
    this.m_Group_AcuPoint = this.m_ViewRef.m_Dic_Trans:Get("&Group_AcuPoint")
    this.m_Image_Point = this.m_ViewRef.m_Dic_Trans:Get("&Image_Point")
    this.m_Image_Line = this.m_ViewRef.m_Dic_Trans:Get("&Image_Line")
    this.m_Group_Acu = this.m_ViewRef.m_Dic_Trans:Get("&Group_Acu")
    this.m_PointData = {}
    this.m_LineData = {}
    for k = 1, InnerBook_Model.m_MeridianCount do
        local _GroupAcu = this.m_Group_Acu:Instantiate( this.m_Group_Acu )
        _GroupAcu:SetParent(this.m_Group_AcuPoint)
        _GroupAcu:SetSiblingIndex(k)
        _GroupAcu.gameObject.name = "Group_" .. k
        
        this.m_PointData[k] = {}
        this.m_LineData[k] = {}
        for i = 1, 9 do
            local _PointObject = this.m_Image_Point:Instantiate( this.m_Image_Point )
            _PointObject:SetParent(_GroupAcu.transform)
            _PointObject:SetSiblingIndex(i - 1)
           
            local _Point = {}
            _Point.m_GameObject = _PointObject.gameObject
            _Point.m_GameObject.name = "Point_" .. k .. "_" .. i
            _Point.m_Trnas = _PointObject.transform
            _Point.m_Image = _Point.m_GameObject:GetComponent(typeof( Image ))
            _Point.m_Render = _Point.m_GameObject:GetComponent(typeof( UIImageChange ))
    
            table.insert(this.m_PointData[k], _Point)
    
            if i <= 8 then
                local _LineObject = this.m_Image_Line:Instantiate(this.m_Image_Line)
                _LineObject:SetParent(_GroupAcu.transform)
                _LineObject:SetSiblingIndex(i - 1)
        
                local _Line = {}
                _Line.m_GameObject = _LineObject.gameObject
                _Line.m_GameObject.name = "Line_" .. k .. "_" .. i
                _Line.m_Trnas = _LineObject.transform
                _Line.m_Render = _Line.m_GameObject:GetComponent(typeof( UIRenderChangeColor ))
                table.insert(this.m_LineData[k], _Line)
            end
        end
    end
    this.m_Image_Point.gameObject:SetActive(false)
    this.m_Image_Line.gameObject:SetActive(false)
    this.m_Group_Acu.gameObject:SetActive(false)

    InnerBook_Controller.SetAcuCoordinate()

    --靈脈資訊
    this.m_Text_SpiritName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritName")
    this.m_Text_SpiritPercent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritPercent")    
    --endregion

    --選擇推演武功
    this.m_Panel_ChooseWugong = this.m_ViewRef.m_Dic_Trans:Get("&Panel_ChooseWugong")

    this.m_ChooseWugongData = {}
    --1.原始武功 2.新的武功
    for i = 1, 2 do
        this.m_ChooseWugongData[i] = {}
        this.m_ChooseWugongData[i].m_Text_Wugong = this.m_ViewRef.m_Dic_TMPText:Get("&Text_ChooseWugong_" .. i)
        local _Trans_ChooseWugong = this.m_ViewRef.m_Dic_Trans:Get("&Trans_ChooseWugong_" .. i)
        this.m_ChooseWugongData[i].m_Btn_Wugong = Button.New(_Trans_ChooseWugong)
        this.m_ChooseWugongData[i].m_Btn_Wugong:AddListener(EventTriggerType.PointerClick, function() InnerBook_Controller.OnClick_Wugong(i) end)
        local _Image_Skill = _Trans_ChooseWugong.transform:Find("Image_Icon").transform
        this.m_ChooseWugongData[i].m_SkillIcon = IconMgr.NewInnerSkillIcon(0, _Image_Skill,100)
        this.m_ChooseWugongData[i].m_Text_Caption = _Trans_ChooseWugong.transform:Find("Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_ChooseWugongData[i].m_WugongID = 0
    end

    this.m_Btn_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm"))
    this.m_Btn_Confirm:AddListener(EventTriggerType.PointerClick, function() InnerBook_Controller.OnClick_ChooseConfirm() end)

    local _IconTable = {"Common_pet_but001D", "Common_pet_but002D", "Common_pet_but003D", "Common_pet_but004D"}
    this.m_GObjPageGroupButton = LeftGroupTab.New(this.m_MainPanel, 2, _IconTable, function(iIdx)
		InnerBook_Controller.OnClick_Tab(iIdx)
	end)
end

function InnerBook_Controller.Update()
    if this.m_PageGameObject[EInnerBookTab.MethodRun].activeSelf then
        MethodRun_Controller.Update()
    elseif this.m_PageGameObject[EInnerBookTab.SixSpirit].activeSelf then
        SixSpirit_Controller.Update()
    end
end

function InnerBook_Controller.Open()
    if this.m_Group_Resources then
        this.m_Group_Resources.m_Resources:OnUpdate()
        this.m_FullPageTitleBar.m_Button_Hint.gameObject:SetActive(false)
    end

    InnerBook_Controller.OnClick_Tab(EInnerBookTab.MethodRun)
    return true
end

---點擊頁面按鈕
function InnerBook_Controller.OnClick_Tab(iIdx)
    this.m_GObjPageGroupButton:DoButtonStateTransitionByIndex(iIdx, EventTriggerType.PointerClick)
    this.m_Panel_ChooseWugong.gameObject:SetActive(false)

    for i = 1, table.Count(this.m_PageGameObject) do
        if this.m_PageGameObject[i] ~= nil then
            this.m_PageGameObject[i]:SetActive(i == iIdx)
        end
    end
    InnerBook_Controller.SetCommonMeridianActive()

    local _Name = GString.Format(TextData.Get(10101010), TextData.Get(InnerBook_Model.m_InnerString), TextData.Get(InnerBook_Model.m_EPageName[iIdx]))
    this.m_FullPageTitleBar:SetTitleText(_Name)
    if InnerBook_Controller[iIdx] ~= nil then
        InnerBook_Controller[iIdx].Open()
    end
end

---設定遮罩開關
function InnerBook_Controller.SetMaskActive(iActive, iMaskEnable)
    this.m_Panel_Mask:SetActive(iActive)
    local _MaskEnable = iMaskEnable == nil and true or iMaskEnable
    this.m_Image_SoftMask.enabled = _MaskEnable
end

---設定共用靈脈介面開關
function InnerBook_Controller.SetCommonMeridianActive()
    local _Active = this.m_PageGameObject[EInnerBookTab.SpiritVeins].activeSelf or this.m_PageGameObject[EInnerBookTab.SixSpirit].activeSelf
    this.m_Group_CommonMeridian.gameObject:SetActive(_Active)
end

---點擊靈脈
function InnerBook_Controller.OnClick_Meridian(iIdx)
    InnerBook_Model.m_MeridianIdx = iIdx
    InnerBook_Model.m_CurrentMeridian = InnerBook_Model.m_MeridianData[iIdx]

    if this.m_PageGameObject[EInnerBookTab.SpiritVeins].activeSelf then
        SpiritVeins_Controller.SetEffectInfo(iIdx)
    elseif this.m_PageGameObject[EInnerBookTab.SixSpirit].activeSelf then
        SixSpirit_Controller.SetWugongInfo(iIdx, 1)
    end
end

---設定共用靈脈資訊
function InnerBook_Controller.SetCommonMeridianInfo(iIdx, iStep, iPoint)
    --靈脈資訊-左邊
    this.m_MeridianData[iIdx].m_Text_Complete.text = math.floor((iPoint / 9) * 100) .. "%"
    this.m_MeridianData[iIdx].m_Image_Circle.fillAmount = iPoint / 9
    this.m_MeridianData[iIdx].m_Text_Step.text = TextData.Get(21002050 + iStep)

    --穴位
    for k, v in pairs(this.m_PointData) do
        for _k, _v in pairs(v) do
            if k == iIdx then
                local _State = iPoint >= _k and ESelectionState.Selected or ESelectionState.Normal
                _v.m_Render:Trigger(_State)
                _v.m_Image.color = Color.White
                _v.m_GameObject.transform.sizeDelta = _Image_Point_NormalSize
            else
                _v.m_Render:Trigger(ESelectionState.Disabled)
                _Image_Point_Color.a = 0.8
                _v.m_Image.color = _Image_Point_Color
                _v.m_GameObject.transform.sizeDelta = _Image_Point_DisableSize
            end
        end
    end

    for k, v in pairs(this.m_LineData) do
        for _k, _v in pairs(v) do
            if k == iIdx then
                local _State = iPoint > _k and ESelectionState.Selected or ESelectionState.Normal
                _v.m_Render:Trigger(_State)
            else
                _v.m_Render:Trigger(ESelectionState.Disabled)
            end
        end
    end

    --靈脈資訊-中間
    this.m_Text_SpiritName.text = GString.Format(TextData.Get(10101010), TextData.Get(20112030 + iIdx), TextData.Get(21002050 + iStep))
    this.m_Text_SpiritPercent.text = GString.Format(TextData.Get(20102013), math.floor((iPoint / 9) * 100)) .. "%"

    InnerBook_Controller.SetMeridianObjState(iIdx)
end

---設定穴位座標
function InnerBook_Controller.SetAcuCoordinate()
    local _Coordinate = InnerMeridianData.m_AcuCoordinateData
    for k, v in pairs(this.m_PointData) do
        for _k, _v in pairs(v) do
            _v.m_GameObject.transform.anchoredPosition = _Coordinate[k].m_Point[_k]
            if _k <= 8 then
                this.m_LineData[k][_k].m_GameObject.transform.anchoredPosition = _Coordinate[k].m_Line[_k]
                this.m_LineData[k][_k].m_GameObject.transform.localEulerAngles = _Coordinate[k].m_Rota[_k]
            end
        end
    end
end

---設定按鈕效果
function InnerBook_Controller.SetMeridianObjState(iIdx)
    for k, v in pairs(this.m_MeridianData) do
        local _AttState = k == iIdx and ESelectionState.Selected or ESelectionState.Normal
        local _State = k == iIdx and 1 or 0
        v.m_Btn:ChangeStateTransitionGroup(_State)
        for i = 1, 2 do
            local _Image_Attr_Img = k == iIdx and _Image_Attr_SelectImg[k][i] or _Image_Attr_NormalImg[k][i]
            v.m_Image_Att[i].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _Image_Attr_Img)
            v.m_Image_Att[i].Render:Trigger(_AttState)
        end
    end
end

--region 選擇推演武功相關功能

local _TempWugongData = {}

---設定推演選擇武功
function InnerBook_Controller.SetChooseWugong(iData)
    _TempWugongData = iData

    this.m_Panel_ChooseWugong.gameObject:SetActive(true)

    local _ChooseWugongData = {}
    
    --原本的武功
    local _OriWugong = InnerBook_Model.m_CurrentMeridian.m_WugongData[iData.m_Step]
    local _WugongData = WugongData.GetWugongDataByIdx(_OriWugong.m_ID)
    table.insert(_ChooseWugongData, _WugongData)
    --新的武功
    _WugongData = WugongData.GetWugongDataByIdx(iData.m_WugongID)
    table.insert(_ChooseWugongData, _WugongData)

    for k, v in pairs(this.m_ChooseWugongData) do
        v.m_Text_Wugong.text = GString.Format(TextData.Get(21002114 + k), _ChooseWugongData[k].m_Name)
        v.m_SkillIcon:RefreshIcon(_ChooseWugongData[k].m_Idx)
        v.m_Text_Caption.text = TextData.Get(_ChooseWugongData[k].m_TextIdx)
        v.m_WugongID = _ChooseWugongData[k].m_Idx
    end
end

---點擊新舊武功
---iChoose = true = 舊的 / false = 新的
function InnerBook_Controller.OnClick_Wugong(iIdx)
    _TempWugongData.m_WugongID = this.m_ChooseWugongData[iIdx].m_WugongID
    if iIdx == 1 then
        this.m_ChooseWugongData[1].m_Btn_Wugong:ChangeStateTransitionGroup(1)
        this.m_ChooseWugongData[2].m_Btn_Wugong:ChangeStateTransitionGroup(0)
    else
        this.m_ChooseWugongData[1].m_Btn_Wugong:ChangeStateTransitionGroup(0)
        this.m_ChooseWugongData[2].m_Btn_Wugong:ChangeStateTransitionGroup(1)
    end
end

---選擇推演靈功確認按鈕
function InnerBook_Controller.OnClick_ChooseConfirm()
    this.m_Panel_ChooseWugong.gameObject:SetActive(false)
    if _TempWugongData.m_WugongID == this.m_ChooseWugongData[2].m_WugongID then
        SendProtocol_003._017(7, _TempWugongData)
    end
    this.m_ChooseWugongData[1].m_WugongID = 0
    this.m_ChooseWugongData[2].m_WugongID = 0
end

--endregion