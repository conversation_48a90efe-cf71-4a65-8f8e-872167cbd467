﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---量子回音入口 Controller
---@class QuantumEchoEntry_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.20
QuantumEchoEntry_Controller = {}
local this = QuantumEchoEntry_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("QuantumEchoEntry_View", "QuantumEchoEntry_Controller", EUIOrderLayers.FullPage, false, "bg_000")

---初始化
function QuantumEchoEntry_Controller.Init()
    this.m_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&MainPanel")
	--資源列表
	local _CurrentResourceTable =
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
	this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_MainPanel, 0, TextData.Get(20112006), _CurrentResourceTable)

    this.m_Btn_Extenal = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_External"))
    this.m_Btn_Extenal:AddListener(EventTriggerType.PointerClick, function() UIMgr.Open(SkillBook_Controller) end)

    this.m_Btn_Inner = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Inner"))
    this.m_Btn_Inner:AddListener(EventTriggerType.PointerClick, function() UIMgr.Open(InnerBook_Controller) end)
end

function QuantumEchoEntry_Controller.Open()
    if this.m_Group_Resources then
        this.m_Group_Resources.m_Resources:OnUpdate()
    end
    return true
end