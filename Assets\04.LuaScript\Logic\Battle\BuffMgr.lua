---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---增減益管理器
---@class BuffMgr
---author WereHsu
---version 1.0
---since HEM 2.0
---date 2023.10.19
BuffMgr = {}
local this = BuffMgr

---@type table<number, table<number,sBuffData>>
---場景全物件的buff列表
this.m_BuffList = {}

local m_IsInitialized = false

local m_CorestateGroup =
{
    [EWeaponType.Knife] = 101,
    [EWeaponType.Sword] = 102,
    [EWeaponType.Fist] = 103,
    [EWeaponType.Pike] = 104,
    [EWeaponType.Stick] = 105
}

---S端傳來的時間標準化值
local m_BuffTimeFormat = 1000

---是否限制外功施放
this.m_LimitAtk = {}
---玩家本人是否限制內功施放
this.m_PlayerLimitSkill = {}
---是否被限制移動
this.m_LimitMove = {}

---手動刪除buff
this.m_BuffToRemove = nil

-- ---隱身buff(是否顯示遮罩) --改在表裡
-- this.m_SneakBuff =
-- {
--     [1404] = false,
--     [3061] = false,
--     [3070] = false,
--     [1440] = true,
-- }

---嘲諷效果ID
this.m_RidiculeDebuff =
{
    [5206] = "Ridicule_I",
    [5207] = "Ridicule_II",
    [5208] = "Ridicule_III"
}

---誰嘲諷我
this.m_RidiculeRoleSID = nil

---@class EBuffEffectKind
EBuffEffectKind =
{
    Non = 0,
    AddHp = 1,      --增加
    AddMp = 2,
    AddSp = 3,
    LessHp =  11 ,  --減少
    LessMp = 12,
    LessSp = 13,
    AbsorbHp = 21,  --吸收
    AbsorbMp = 22,
    BombHp = 31,    --引爆
    BomeMp = 32 ,
    BounceHp = 33,  --反彈HP
    TickLessHP = 41, --Tick減少HP
}

---依附在tick上的Buff跳字
this.m_BuffAffectOnTick =
{
    -- [tickid] =
    -- {
    --     [AffectSID] = 差值,
    -- }
}

--this.m_BuffInfo = {}

---初始化
function BuffMgr.Init()
    D.Log( "BuffMgr Init" )

    --HEMTimeMgr.AddListener( BuffMgr.RefreshBuffListOnSecond, 1, true )

    m_IsInitialized = true
end

local function CloseOldBattleText(iSID, iBuffId)
    if this.m_BuffList[iSID] and this.m_BuffList[iSID][iBuffId] and this.m_BuffList[iSID][iBuffId].m_BattleText then
        --舊的關起來
        this.m_BuffList[iSID][iBuffId].m_BattleText:SetActive(false)
    end
end

---新增buff
---@param iSID number
---@param iBuffId number
---@param iBuffData BuffServerData
function BuffMgr.AddBuff(iSID, iBuffId, iBuffData)
    iSID = tostring(iSID)
    if this.m_BuffList[iSID] == nil then
        this.m_BuffList[iSID] = {}
    end
    ---紀錄buff的承受者 BuffIcon轉圈圈的倒數 需要承受者的SID 才能抓到資料
    iBuffData.m_AcceptSID = iSID
    local _BuffData = BuffData.GetBuffDataByIdx(iBuffId)

    if not _BuffData then
        D.Log("BuffData為空，BuffId = ".. iBuffId)
        return
    end

    if this.m_BuffList[iSID][iBuffId] then
        if this.m_BuffList[iSID][iBuffId].m_EffectHash then
            iBuffData.m_EffectHash = this.m_BuffList[iSID][iBuffId].m_EffectHash
        end
        if this.m_BuffList[iSID][iBuffId].m_IsLoadEffect then
            iBuffData.m_IsLoadEffect = this.m_BuffList[iSID][iBuffId].m_IsLoadEffect
        end

        CloseOldBattleText(iSID, iBuffId)

        this.m_BuffList[iSID][iBuffId] = iBuffData
        -- 彈藥效果或擊飛效果要重複撥放
        if _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.Ammunition or _BuffData.m_ActNum == BuffData.EBuffAct.Fly then
            BuffMgr.SetBuffEffect(iSID, iBuffId, true)
        end
    else
        this.m_BuffList[iSID][iBuffId] = iBuffData
        BuffMgr.SetBuffEffect(iSID, iBuffId, true)
    end

    BuffMgr.SetDebuffBattleText(iSID, iBuffId, true, iBuffData.m_LauncherSId, iBuffData.m_Time)

    if _BuffData.m_BuffType == BuffData.EBuffType.F_CoreSkill then
        --判斷核心技
        this.m_BuffList[iSID][iBuffId].m_IsCoreBuff = true
    end

    if _BuffData.m_CostBuffId > 0 then
        this.m_BuffList[iSID][iBuffId].m_CostBuffId = _BuffData.m_CostBuffId
        this.m_BuffList[iSID][iBuffId].m_CostBuffAmount = _BuffData.m_CostBuffAmount
    end

    --埋個Log
    if this.m_BuffList[iSID] == nil then
        D.Log("m_BuffList[iSID] 空")
    elseif this.m_BuffList[iSID][iBuffId] == nil then
        D.Log("m_BuffList[iSID][iBuffId] 空")
    elseif _BuffData.m_PassiveType == nil then
        D.Log("_BuffData.m_PassiveType 空")
    end

    this.m_BuffList[iSID][iBuffId].m_PassiveType = _BuffData.m_PassiveType

    --嘲諷記錄
    local _IsPlayerSelf = iSID == tostring(PlayerData.GetRoleID())
    if _IsPlayerSelf and this.m_RidiculeDebuff[iBuffId] then
        this.m_RidiculeRoleSID = iBuffData.m_LauncherSId
    end

    BuffMgr.Notify(iSID, iBuffId, true)
end

---每秒刷新buff剩餘時間(主要給顯示用)
local function RefreshBuffListOnUpdate()
    --刷全部的buff
    for _Sid,_BuffDatas in pairs(this.m_BuffList) do
        if table.Count(_BuffDatas) > 0 then
            --local _IsPlayerSelf = _Sid == tostring(PlayerData.GetRoleID())
            --if _IsPlayerSelf then
            for _BuffId, value in pairs(_BuffDatas) do
                if value.m_CostBuffId then
                    local _CostBuffInfo = BuffMgr.GetRoleBuff(_Sid, value.m_CostBuffId)
                    if _CostBuffInfo then
                        value.m_CountDown = _CostBuffInfo.m_StackCount / value.m_CostBuffAmount
                    else
                        value.m_CountDown = 0
                    end
                end

                if value.m_CountDown then
                    value.m_CountDown = value.m_CountDown - HEMTimeMgr.m_DeltaTime
                    -- if _IsPlayerSelf and value.m_CountDown <= 0 and value.m_PassiveType == 0 then
                    -- 全權由S端處理
                    --     BuffMgr.RemoveRoleBuff(_Sid, _BuffId)
                    -- elseif value.m_PassiveType ~= 0 then
                    --     value.m_CountDown = 0
                    -- end
                    if value.m_CountDown <= 0 then
                        value.m_CountDown = 0
                    end

                    if value.m_IsCoreBuff then
                        if value.m_CountDown <= EffectSetting.m_BattleSuitEffectEndDuration then
                            RoleMgr.GetPlayerRC():GetModelController():EndingBattleSuitEffect(value.m_CountDown)
                        elseif value.m_CountDown > EffectSetting.m_BattleSuitEffectEndDuration then
                            RoleMgr.GetPlayerRC():GetModelController():StartBattleSuitEffect()
                        end
                    end

                    local _BuffType = BuffData.GetBuffDataByIdx(_BuffId)

                    if _BuffType ~= nil and _BuffType.m_BuffType >= BuffData.EBuffType.N_Normal and _BuffType.m_BuffType <= BuffData.EBuffType.N_WorldBoss_NotCancel then
                        if value.m_CountDown <= EffectSetting.m_DeBuffEffectFadeOutTime then
                            EffectMgr.StartFadeOut(value.m_EffectHash)
                        end
                    end
                else
                    D.Log("BuffID : ".._BuffId .."沒有填寫時間")
                end
            end
            --end
        end

        if table.Count(_BuffDatas) <= 0 then
            --BuffMgr.ClearRoleBuffData(k)
            this.m_BuffList[_Sid] = nil
        end

    end
end

function BuffMgr.Update()
    RefreshBuffListOnUpdate()
end

---移除特定物件的特定buff
function BuffMgr.RemoveRoleBuff(iSID, iBuffId)
    iSID = tostring(iSID)

    BuffMgr.SetBuffEffect(iSID, iBuffId, false)

    BuffMgr.SetDebuffBattleText(iSID, iBuffId, false)

    if this.m_BuffList[iSID] then
        if this.m_BuffList[iSID][iBuffId] then
            this.m_BuffList[iSID][iBuffId] = nil
        end
    end

    --嘲諷記錄
    local _IsPlayerSelf = iSID == tostring(PlayerData.GetRoleID())
    if _IsPlayerSelf and this.m_RidiculeDebuff[iBuffId] then
        this.m_RidiculeRoleSID = nil
    end

    this.Notify(iSID, iBuffId, false)
end

---取出特定SID的全部Buff、也可以查是否有某Buff
---@return sBuffData
function BuffMgr.GetRoleBuff(iSID, iBuffId)
    local _Return = this.m_BuffList[tostring(iSID)]
    if _Return and iBuffId then
        _Return = _Return[iBuffId]
    end

    return _Return
end

---取出特定SID的可顯示的Buff數量
function BuffMgr.GetRoleBuffCount(iSID)
    local _BuffList = this.m_BuffList[tostring(iSID)]
    local _Return = 0
    if _BuffList then
        for _buffId, _BuffData in pairs(_BuffList) do
            if BuffData.GetBuffDataByIdx(_buffId) and
               BuffData.GetBuffDataByIdx(_buffId).m_Priority > 0 then
                _Return = _Return + 1
            end
        end
    end
    return _Return
end

-- ---設定使用技能Buff資訊
-- function BuffMgr.SetBuffInfo(iBuffID, iBuffData)
--     if iBuffData then
--         if BuffMgr.CheckIsWeaponEffect(iBuffData.m_EffectPos) then
--             this.m_BuffInfo[iBuffID] = iBuffData
--         end
--     else
--         this.m_BuffInfo[iBuffID] = nil
--     end
-- end

-- function BuffMgr.GetBuffInfo()
--     return this.m_BuffInfo
-- end

---每次刷新粒子特效
---@param iSID number 物件的Sid
---@param iBuffId number 被變動的buffId
---@param IsAdd boolean 被加入還是被消除
local function SetContinueEffect(iSID, iBuffId, IsAdd)
    iSID = tostring(iSID)

    -- 身上特效做刷新
    local _RC = RoleMgr.Get(iSID)
    if not _RC then
        _RC = NPCMgr.GetNPCController(iSID)
        -- if _RC == nil then
        --     --沒對應的RC
        --     return
        -- end
    end
    ---@type BuffData
    local _BuffData = BuffData.GetBuffDataByIdx(iBuffId)

    if not _BuffData then
        return
    end

    local _AddEffectHash = function(iHashCode)
        if this.m_BuffList[iSID] and this.m_BuffList[iSID][iBuffId] then
            this.m_BuffList[iSID][iBuffId].m_EffectHash = iHashCode
            this.m_BuffList[iSID][iBuffId].m_IsLoadEffect = nil
        else
            EffectMgr.ReturnEffect(iHashCode)
        end
    end

    if IsAdd then
        if this.m_BuffList[iSID] == nil or this.m_BuffList[iSID][iBuffId] == nil then
            return
        end

        if _BuffData.m_EffectIdx > 0 then
            if this.m_BuffList[iSID][iBuffId].m_EffectHash then
                EffectMgr.ReturnEffect(this.m_BuffList[iSID][iBuffId].m_EffectHash)
                this.m_BuffList[iSID][iBuffId].m_IsLoadEffect = nil
            end

            local function GetEffectData()
                local _EffectData = EffectData.New()
                _EffectData.m_ID = _BuffData.m_EffectIdx
                _EffectData.m_BonePos = _BuffData.m_EffectPos
                _EffectData.m_isFollow = true
                _EffectData.m_isFollowRotation = _BuffData.m_EffectFollowRotation == 0
                _EffectData.m_LoadCompleteDelegate = _AddEffectHash

                this.m_BuffList[iSID][iBuffId].m_IsLoadEffect = _EffectData

                return _EffectData
            end

            if _RC then
                --- 特殊表現種類 4 需要特殊處理
                if _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.Ammunition then
                    _RC:SetSpecialEffect(GetEffectData(), _BuffData.m_SpecialTick, this.m_BuffList[iSID][iBuffId].m_StackCount)
                else
                    if this.m_BuffList[iSID][iBuffId].m_IsLoadEffect then
                        return
                    end
                    _RC:SetEffect(GetEffectData())
                end
            end

        end
    else
        if this.m_BuffList[iSID] and this.m_BuffList[iSID][iBuffId] then
            if this.m_BuffList[iSID][iBuffId].m_EffectHash then
                EffectMgr.ReturnEffect(this.m_BuffList[iSID][iBuffId].m_EffectHash)
                this.m_BuffList[iSID][iBuffId].m_IsLoadEffect = nil
                --BuffMgr.SetBuffInfo(iBuffId, nil)
            end

            --- 顯示輪轉特效的 buff 需特殊處理
            if _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.Ammunition and _RC then
                local _EffectData = EffectData.New()
                _EffectData.m_ID = _BuffData.m_EffectIdx
                _EffectData.m_BonePos = _BuffData.m_EffectPos
                _EffectData.m_isFollow = true
                _EffectData.m_LoadCompleteDelegate = _AddEffectHash
                _RC:SetSpecialEffect(_EffectData, _BuffData.m_SpecialTick)
            end
        end
    end

end

---確認特效是否是武器部位
function BuffMgr.CheckIsWeaponEffect(iBone)
    for k, v in ipairs(EWeaponBone) do
        if v == iBone then
            return true
        end
    end
    return false
end

---刷新物件身上Buff特效
---@param iSID number 物件的Sid
---@param iBuffId number 被變動的buffId
---@param IsAdd boolean 被加入還是被消除
function BuffMgr.SetBuffEffect(iSID, iBuffId, IsAdd)
    -- 身上特效做刷新
    local _RC = RoleMgr.Get(iSID)
    if not _RC then
        _RC = NPCMgr.GetNPCController(iSID)

    end
    ---@type BuffData
    local _BuffData = BuffData.GetBuffDataByIdx(iBuffId)

    if _RC then
        local _EffectData = EffectData.New()
        ---身上粒子特效刷新
        if IsAdd then
            if _BuffData.m_BuffJoinEffect > 0 then
                _EffectData.m_ID = _BuffData.m_BuffJoinEffect
                _EffectData.m_BonePos = _BuffData.m_BuffJoinPos
                    _RC:SetEffect(_EffectData)
            end
        else
            if _BuffData.m_BuffDeathEffect > 0 then
                _EffectData.m_ID = _BuffData.m_BuffDeathEffect
                _EffectData.m_BonePos = _BuffData.m_BuffDeathPos
                _EffectData.m_isFollow = true
                _EffectData.m_isFollowRotation = _BuffData.m_EffectFollowRotation == 0
                _RC:SetEffect(_EffectData)
            end
        end
    end

    --這個可以不用RC
    SetContinueEffect(iSID, iBuffId, IsAdd)

    if _RC == nil then
        --沒對應的RC
        return
    end

    --核心技專用
    if _BuffData.m_BuffType == BuffData.EBuffType.F_CoreSkill then

        if IsAdd then
            _RC:SetBattleSuitEffect(true)
        else
            _RC:SetBattleSuitEffect(false)
        end
    end

    -- 被封印外功
    if _BuffData.m_LimitAtk > 0 then
        if IsAdd then
            if this.m_LimitAtk[iSID] == nil then
                this.m_LimitAtk[iSID] = {}
            end
            this.m_LimitAtk[iSID][iBuffId] = 1

        elseif this.m_LimitAtk[iSID] ~= nil then
            this.m_LimitAtk[iSID][iBuffId] = nil
            if table.Count(this.m_LimitAtk[iSID]) == 0 then
                this.m_LimitAtk[iSID] = nil
            end
        end
    end

    -- 限制位移
    if _BuffData.m_LimitMove > 0 then
        if IsAdd then
            if this.m_LimitMove[iSID] == nil then
                this.m_LimitMove[iSID] = {}
            end

            this.m_LimitMove[iSID][iBuffId] = 1

            if _RC == RoleMgr.GetPlayerRC() then
                --MoveMgr.PlayerSelfStop(ESendMoveReason.Forcibly, RoleMgr.GetSelfPlayerPos())
                MoveMgr.ForceStopAllMove(RoleMgr.GetSelfPlayerPos(), false, ESendMoveReason.Forcibly)

            else
                _RC.m_MoveController:StopMove(true)
            end
        else
            if this.m_LimitMove[iSID] then
                this.m_LimitMove[iSID][iBuffId] = nil
            end

            if table.Count(this.m_LimitMove[iSID]) == 0 then
                this.m_LimitMove[iSID] = nil
            end
        end
    end

    -- 身上動作刷新
    if _BuffData.m_ActNum > BuffData.EBuffAct.Non then
        local _AnimBoolHash
        local _AnimTriggerHash
        if _BuffData.m_ActNum == BuffData.EBuffAct.Stun then -- 暈眩
            _AnimBoolHash = AnimationHash.Bool_IsStun
            _AnimTriggerHash = AnimationHash.Trigger_Stun
        elseif _BuffData.m_ActNum == BuffData.EBuffAct.Down then -- 擊倒
            _AnimBoolHash = AnimationHash.Bool_IsDown
            _AnimTriggerHash = AnimationHash.Trigger_Down
        elseif _BuffData.m_ActNum == BuffData.EBuffAct.Fly then -- 擊飛
            _AnimBoolHash = AnimationHash.Bool_IsFly
            _AnimTriggerHash = AnimationHash.Trigger_Fly
        end
        --有特殊控場動作的一定不能位移不能打攻擊
        if IsAdd then
            if not _RC.m_StateController:IsDead() then
                _RC.m_StateController:ChangeState(EStateType.Dominated)

                _RC:GetModelController():SetAniBool(_AnimBoolHash, true)
                _RC:GetModelController():SetAniTrigger(_AnimTriggerHash)

                -- 擊飛Y軸效果
                if _RC.m_ModelController and _BuffData.m_ActNum == BuffData.EBuffAct.Fly then
                    _RC.m_ModelController:SetMoveY(_BuffData.m_LimitFlyHeight / 100, _BuffData.m_FlyTime / 1000, _BuffData.m_FlyToTop / 1000, _BuffData.m_FlyToGround / 1000)
                end
            end
        else
            --_RC.m_StateController:ChangeState(EStateType.Idle)
            _RC:GetModelController():SetAniBool(_AnimBoolHash, false)
            if this.m_LimitMove[iSID] == nil or this.m_LimitAtk[iSID] == nil or
               table.Count(this.m_LimitMove[iSID]) <= 0 or table.Count(this.m_LimitAtk[iSID]) <= 0  then
                --沒被控場了 而且還沒掛的話就回到原本的狀態
                if not _RC.m_StateController:IsDead() then
                    _RC.m_StateController:ChangeState(EStateType.Idle)
                end
            end
        end
    end

    -- 隱身效果
    if _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.Normal then
        --隱身效果
        _RC:SetSneakEffect(IsAdd)
    elseif _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.MoveAfterimage then
        --劍法移動殘影
        _RC.m_GhostController:SetType(EffectSetting.m_GhostEffectType.Basic)
        _RC.m_GhostController:Play(IsAdd)
    elseif _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.CloneAfterimage then
        --背後靈分身
        _RC.m_GhostController:SetType(EffectSetting.m_GhostEffectType.Buff_1225)
        _RC.m_GhostController:Play(IsAdd)
    elseif _BuffData.m_SpecialPerform == BuffData.ESpecialPerform.OutLineGlowEffect then
        ---根據是否為加狀態調整發光效果參數
        local _GlowType = IsAdd and EGlowType.PLAYER_BUFF_EFFECT or EGlowType.NONE
        --九尾外衣效果
        _RC:SetGlowEffect(_GlowType)
    end

    -- 模型縮放效果
    if _BuffData.m_ModelScale > 0 then
        if IsAdd then
            _RC:ChangeScale(_RC.m_OriginScale * _BuffData.m_ModelScale / 100)
        else
            _RC:ChangeScale(_RC.m_OriginScale)
        end
    end

    -- 只需處理自己的
    if _RC == RoleMgr.GetPlayerRC() then
        -- 自己才需要處理遮罩
        if _BuffData.m_FullScreenCover > 0 then
            if PlayerData_Flags[EFlags.Static].IsHaveFlag(Death_Model.m_PassNewComerAreaStaticID) == false and iBuffId == Death_Model.m_FakeDeadBuffID then

            else
                Main_SubCtrl_FullScreenEffect.ShowSneakEffect(IsAdd)
            end

        end

        -- buff 改變欄位技能
        if _BuffData.m_ChangeSkillType ~= 0 and _BuffData.m_ChangeSkillId ~= 0 then
            if IsAdd then
                HotKeyMgr.ChangeSkillByBuff( PlayerData.GetWeaponType(), _BuffData.m_ChangeSkillType, _BuffData.m_ChangeSkillId )
            else
                HotKeyMgr.RecoverSkillByBuff( _BuffData.m_ChangeSkillId )
            end
        end

        -- buff 是可取消攻擊的，且正在自動普攻中，則取消自動普攻
        if _BuffData.m_CancelAtk and BattleMgr.m_IsAutoNormalBattle then
            BattleMgr.SetAutoNormalBattle(false)
        end

        -- 關閉磁能HUD
        if _BuffData.m_PassiveType == EPassiveType.Magnet and not IsAdd then
            _RC:UpdateCoreSkillHUD(0, 0)
        end

        --跳buff的大字
        if _BuffData.m_ShowBuffWordId > 0 and IsAdd then
            local _Text = TextData.Get(_BuffData.m_ShowBuffWordId)
            if not string.IsNullOrEmpty(_Text) then
                local _BattleTextType = _BuffData.m_BuffKind == 0 and EBattleTextType.BuffBigWord or EBattleTextType.DebuffWord
                BattleText_Controller.CreateBattleText(_RC, _Text, _BattleTextType)
            end
        end
    end

    -- 護盾效果
    if _BuffData.m_PassiveType == EPassiveType.Shield and not IsAdd then
        _RC:UpdatHUDShield()
    end
end

---控場跳字
function BuffMgr.SetDebuffBattleText(iSID, iBuffId, IsAdd, iLauncherSID, iTime)
    local _BuffData = BuffData.GetBuffDataByIdx(iBuffId)
    iSID = tostring(iSID)
    iLauncherSID = tostring(iLauncherSID)

    local _IsRelated = iSID == tostring(PlayerData.GetCardID()) or iLauncherSID == tostring(PlayerData.GetCardID())
    --沒寫時間固定2秒
    iTime = iTime ~= nil and iTime or 2

    if _BuffData == nil then
        D.Log("沒buff: "..iBuffId)
        return
    end

    if _BuffData.m_ShowDebuffIconId == 0 then
        return
    end

    -- if IsAdd and _IsRelated and iTime > 0 then
    --     this.m_BuffList[iSID][iBuffId].m_BattleText = BattleText_Controller.SetDebuffBattleText(iSID, string.format("%06d",_BuffData.m_ShowDebuffIconId) , iTime)
    -- else
    --     CloseOldBattleText(iSID, iBuffId)
    -- end
end

---影響數值
---@param iSID number 玩家 ID
---@param iAtkerSID number 施法者 ID
---@param iKind number 效果種類
---@param iValue number 效果數值
---@param iCurrentValue number 目前數值
---@param iTickID number Tick ID
local function AffectValue(iSID, iAtkerSID, iKind, iValue, iCurrentValue, iTickID)
    iSID = tostring(iSID)
    iAtkerSID = tostring(iAtkerSID)
    if iCurrentValue then
        --變動數值要跳文字、改變HUD數值
        if iSID == tostring(PlayerData.GetCardID()) then
            ---受擊種類
            local _Kind = 0
            ---如果效果種類是受傷
            if iKind == table.GetKey(ETickValueKind, EBattleTextType.Hurt) then
                _Kind = EValueKind.LessHp
            ---如果效果種類是補血
            elseif iKind == table.GetKey(ETickValueKind, EBattleTextType.MakeHP) then
                _Kind = EValueKind.AddHp
            end

            if _Kind > 0 then
                PlayerData.UpdatePlayerHp(iCurrentValue, PlayerData.GetBaseValues(EBaseValues.HP).Max())

                BattleText_Controller.SetHPBattleText(RoleMgr.GetPlayerRC(), iValue, _Kind)
            end
        else
            local _RC = RoleMgr.Get(iSID)
            if not _RC then
                _RC = NPCMgr.GetNPCController(iSID)
                if _RC == nil then
                    --沒對應的RC
                    return
                end
            end
            if iAtkerSID == tostring(PlayerData.GetCardID()) then
                ---目前的值-接進來的值
                local _Value = _RC.m_HUDController.m_HUDData.m_CurHp - iCurrentValue
                ---受擊種類
                local _Kind = 0
                if _Value > 0 then
                    _Kind = EBattleTextType.Dot
                elseif _Value < 0 then
                    _Kind = EBattleTextType.MakeHP
                end
                if _Kind > 0 and iTickID == nil then
                    BattleText_Controller.CreateBattleText(_RC, _Value, _Kind)
                else
                    --Tick用的Buff數值
                    if this.m_BuffAffectOnTick[iTickID] == nil then
                        this.m_BuffAffectOnTick[iTickID] = {}
                    end
                    --if this.m_BuffAffectOnTick[iTickID][iSID] == nil then
                    this.m_BuffAffectOnTick[iTickID][iSID] = _Value
                    --Buff計算的只到這，剩下的交給Tick
                    return
                    --end

                    --this.m_BuffAffectOnTick[iTickID][iSID][iKind] = _Value
                end
            end

            _RC:UpdatHUDHP(nil , iCurrentValue)
        end
    end

end

---接協定10-9 10-10
function BuffMgr.BuffAffect(isPlayer, iPacket)
    local _AffectData = {}
    if isPlayer then
        _AffectData.m_SID = iPacket:ReadUInt64()
    else
        _AffectData.m_SID = iPacket:ReadUInt32()
    end
    _AffectData.m_BuffId = iPacket:ReadUInt16()
    _AffectData.m_Kind = iPacket:ReadByte()
    _AffectData.m_Value = iPacket:ReadUInt32()
    ---施法者 1:玩家 2:NPC
    _AffectData.m_AtkerKind = iPacket:ReadByte()
    if _AffectData.m_AtkerKind == 1 then
        _AffectData.m_AtkerSID = iPacket:ReadUInt64()
    elseif _AffectData.m_AtkerKind == 2 then
        _AffectData.m_AtkerSID = iPacket:ReadUInt32()
    end

    if _AffectData.m_Kind == EBuffEffectKind.AddHp or
       _AffectData.m_Kind == EBuffEffectKind.LessHp or
       _AffectData.m_Kind == EBuffEffectKind.AbsorbHp or
       _AffectData.m_Kind == EBuffEffectKind.BombHp or
       _AffectData.m_Kind == EBuffEffectKind.BounceHp or
       _AffectData.m_Kind == EBuffEffectKind.TickLessHP then
        ---目前血量
        _AffectData.m_CurrentValue = iPacket:ReadUInt32()
    end

    --Tick類型的
    if _AffectData.m_Kind == EBuffEffectKind.TickLessHP then
        _AffectData.m_CurrentTick = iPacket:ReadUInt16()

    end

    ---Buff施放者的RoleID
    local _AttackRoleID = tostring(_AffectData.m_AtkerSID)
    ---Buff施放者是否在監控名單
    local _IsInTraceList_Atk = BattleMgr.IsPlayerShouldBeRecord(_AttackRoleID)

    ---Buff接受者是否在監控名單
    local _IsInTraceList_Def = BattleMgr.IsPlayerShouldBeRecord(tostring( _AffectData.m_SID) )

    if BattleMgr.m_UseBattleCalculate then
        ---buff的施法者是在監名單
        if _IsInTraceList_Atk then
            BattleMgr.RecordNewDataAndAssinType(_AffectData,EDamageOriginType.Buff,EBattleCalculateMode.AttackMode)
        end
        ---buff的接受者是在監看名單
        ---承受者是玩家 但施放者不是玩家 才算是敵人的輸出
        if _IsInTraceList_Def and _IsInTraceList_Atk == false then
            BattleMgr.RecordNewDataAndAssinType(_AffectData,EDamageOriginType.Buff,EBattleCalculateMode.DefenceMode,tostring( _AffectData.m_SID))
        end
    end

    --影響的資料要生特效、改變玩家數值

    AffectValue(_AffectData.m_SID,_AffectData.m_AtkerSID, _AffectData.m_Kind, _AffectData.m_Value, _AffectData.m_CurrentValue, _AffectData.m_CurrentTick)
    SetContinueEffect(_AffectData.m_SID, _AffectData.m_BuffId, true)
end

---接協定10-13 10-14 護盾發動跳吸收
function BuffMgr.BuffShieldAffect(isPlayer, iPacket)
    local _AffectData = {}
    if isPlayer then
        _AffectData.m_SID = iPacket:ReadUInt64()
    else
        _AffectData.m_SID = iPacket:ReadUInt32()
    end
    _AffectData.m_BuffId = iPacket:ReadUInt16()
    _AffectData.m_ShieldKind = iPacket:ReadByte() --狀態種類(1)[種類1.護盾 2.霸體 3.血盾 4.罡盾]
    local _AttackerKind = iPacket:ReadByte()
    if _AttackerKind == 1 then
        _AffectData.m_AttackerSID = iPacket:ReadUInt64()
    else
        _AffectData.m_AttackerSID = iPacket:ReadUInt32()
    end

    ---跟玩家本人有關再跳
    local _SID = tostring(_AffectData.m_SID)
    local _AttackerSID = tostring(_AffectData.m_AttackerSID)

    if _SID == tostring(PlayerData.GetRoleID()) or _AttackerSID == tostring(PlayerData.GetRoleID()) then
        --基本上這個協定只有跳字w
        local _ValueKind
        local _RC
        if isPlayer then
            _RC = RoleMgr.Get(_AffectData.m_SID)
        else
            _RC = NPCMgr.GetNPCController(_AffectData.m_SID)
        end

        if _AffectData.m_ShieldKind == 1 then
            _ValueKind = EValueKind.Assimilate
        elseif _AffectData.m_ShieldKind == 2 then
            _ValueKind = EValueKind.ResistNegative
        --elseif _AffectData.m_ShieldKind == 3 and not _RC.m_IsSelf then
        --    _ValueKind = EValueKind.BloodShield
        elseif _AffectData.m_ShieldKind == 4 then
            _ValueKind = EValueKind.EnegyShield
        end

        if _ValueKind then
            BattleText_Controller.SetHPBattleText(_RC, 0, _ValueKind)
        end

    end

end

---接協定10-18. 血盾獨立出來算
function BuffMgr.BloodShieldAffect(iPacket)
    local _RoleID = iPacket:ReadUInt64()
    local _BuffID = iPacket:ReadUInt16()
    local _Shield = iPacket:ReadUInt32()
    local _LauncherKind = iPacket:ReadByte()
    local m_LauncherSId

    if _LauncherKind == 1 then
        m_LauncherSId = iPacket:ReadUInt64()
    elseif _LauncherKind == 2 then
        m_LauncherSId = iPacket:ReadUInt32()
    end

    local _RC = RoleMgr.Get(_RoleID)
    if m_LauncherSId and _Shield > 0 and (_RC.m_IsSelf or tostring(m_LauncherSId) ==  tostring(PlayerData.GetRoleID()) ) then
        ---護盾跳字
        if _RC.m_HUDController.m_HUDData.m_CurShield and _RC.m_HUDController.m_HUDData.m_CurShield > _Shield then
            BattleText_Controller.SetHPBattleText(_RC, _RC.m_HUDController.m_HUDData.m_CurShield -_Shield, EValueKind.BloodShield)
        end
    end

    if _RC then
        _RC:UpdatHUDShield(_Shield)
    end
    D.Log("10-18 RoleID: " .. tostring(_RoleID) .. " BuffID: " .. _BuffID .. "Shield: " .. _Shield)

end

---取出想要的資料
function BuffMgr.GetTickBuffAffect(iTickID, iAffectSID)
    local _Return = 0
    iAffectSID = tostring(iAffectSID)
    if this.m_BuffAffectOnTick[iTickID] then

        _Return = this.m_BuffAffectOnTick[iTickID][iAffectSID] or 0

        this.m_BuffAffectOnTick[iTickID][iAffectSID] = nil
        if table.Count(this.m_BuffAffectOnTick[iTickID]) <= 0 then
            this.m_BuffAffectOnTick[iTickID] = nil
        end
    end

    return _Return
end

---玩家本人是否中了不可位移的debuff
function BuffMgr.PlayerCanMove()
    return this.m_LimitMove[tostring(PlayerData.GetRoleID())] == nil
end

---提醒任何東西要做啥
---@param iSID string 物件的Sid
---@param iBuffId number 被變動的buffId
---@param IsAdd boolean 被加入還是被消除
function BuffMgr.Notify(iSID, iBuffId, IsAdd)
    -- 預想: 若SId是玩家/隊友/目標，則通知前端更新
    GStateObserverManager.Notify(EStateObserver.BuffRefresh, iSID, iBuffId, IsAdd)
end

--- 玩家離場時清除所有場景其他物件的buff資料
function BuffMgr.ClearSceneBuffData()
    for _Sid,_BuffDatas in pairs(this.m_BuffList) do
        if _Sid ~= tostring(PlayerData.GetRoleID()) then
            for _BuffId, _BuddData in pairs(_BuffDatas) do
                BuffMgr.RemoveRoleBuff(_Sid, _BuffId)
            end
            this.m_BuffList[_Sid] = nil
        end
    end
end

---某角色離開場景/死亡時清除
function BuffMgr.ClearRoleBuffData(iSID)
    iSID = tostring(iSID)

    local _BuffDatas = this.m_BuffList[iSID]
    if _BuffDatas then
        for _BuffId, _BuddData in pairs(_BuffDatas) do
            BuffMgr.RemoveRoleBuff(iSID, _BuffId)
        end

        this.m_BuffList[iSID] = nil
        this.Notify(iSID)
    end
end

---登出時清除所有buff資料
function BuffMgr.ClearAllData()
    --- 防呆 避免UnRequired時，沒有玩家角色SID
    if RoleMgr.GetPlayerRC() and RoleMgr.GetPlayerRC().m_SIDthen then
        this.ClearRoleBuffData(RoleMgr.GetPlayerRC().m_SID)
    end

    table.Clear(this.m_BuffList)
    this.m_LimitAtk = {}
    this.m_PlayerLimitSkill = {}
    this.m_LimitMove = {}
end

---計算Buff顯示優先度
---@param iSID number 角色編號
---@param iBuffIcon table Buff圖示
---@param iBuffLimitCount number Buff顯示數量限制
function BuffMgr.CalculateBuffPriority(iSID)
    local _AllBuff = this.GetRoleBuff(iSID)
    local _ShowBuff = {}

    if _AllBuff then
        for key, value in pairs(_AllBuff) do
            local _Buff = {}
            local BuffData = BuffData.GetBuffDataByIdx(key)
            _Buff.cBuffData = BuffData
            _Buff.sBuffData = value
            -- _Buff.m_BuffId = key
            -- _Buff.m_StackCount = value.m_StackCount
            if BuffData and BuffData.m_Priority > 0 then
                table.insert(_ShowBuff, _Buff)
            end
        end

        ---依照優先度排序
        table.sort(_ShowBuff, function (a, b)
            return a.cBuffData.m_Priority < b.cBuffData.m_Priority
        end)
    end

    return _ShowBuff

end

--- 協定 10-15、10-16、10-17、10-19 效果影響人數 ( 接資料 )
---@class PreData 在協定先解出來，需要再使用的資料
---@param iPreData PreData
---@param iPreData.m_SID number 發招者 Server ID
---@param iPreData.m_RoleType ERoleType 發招者角色類型
---@param iPreData.m_BuffID number buffID
---@param iIsProjectObj bool 是否為飛行物類型(協定10-19)
function BuffMgr.ProtocolAffect(iPreData, iPacket , iIsProjectObj)
    local _SenderRC = nil
    if iPreData.m_RoleType == ERoleType.Self or iPreData.m_RoleType == ERoleType.OtherPlayer then
        _SenderRC = RoleMgr.Get(iPreData.m_SID)
    else
        _SenderRC = NPCMgr.GetNPCController(iPreData.m_SID)
    end
    -- 找不到發招者
    if not _SenderRC then
        return
    end

    local _Data = {}
    ---範圍影響到的人數
    _Data.m_AffectCount = iPacket:ReadByte()
    _Data.m_AffectTable = {}
    for i = 1, _Data.m_AffectCount do
        ---範圍影響的種類 1:NPC 2:Player
        local _Affectkind = iPacket:ReadByte()
        if _Affectkind > 0 then
            local _AffectData = {}
            if _Affectkind == 1 then
                _AffectData.m_AffectKind = EAffectKind.Npc
                _AffectData.m_AffectSID = iPacket:ReadUInt32()
            elseif _Affectkind == 2 then
                _AffectData.m_AffectSID = iPacket:ReadUInt64()
                _AffectData.m_AffectKind = EAffectKind.Player
            end

            _AffectData.m_ValueKind = iPacket:ReadByte()
            _AffectData.m_AffectValue = 0
            _AffectData.m_CurrentValue = 0
            if _AffectData.m_ValueKind ~= 0 then
                _AffectData.m_AffectValue = iPacket:ReadUInt32()
                _AffectData.m_CurrentValue = iPacket:ReadUInt32()
            end

            BattleMgr.ShowLog(tostring(_SenderRC.m_SID) .. " hit " .. tostring(_AffectData.m_AffectSID) .. " / HP: " .. _AffectData.m_CurrentValue)
            -- 特殊 Tick 演出

            _Data.m_AffectTable[_AffectData.m_AffectSID] = _AffectData
        end
    end

    _Data.m_BuffID = iPreData.m_BuffID


    if BattleMgr.m_UseBattleCalculate then
        local _AttackRoleID = tostring(iPreData.m_SID)
        local _IsInTraceList_Atk  = BattleMgr.IsPlayerShouldBeRecord(_AttackRoleID )
        local _IsInTraceList_def

        if table.Count(_Data.m_AffectTable) == 0 then
            return
        end

        ---檢查加害者
        if _IsInTraceList_Atk  then
            ---buff的施法者是要紀錄的玩家
            for k , v in pairs(_Data.m_AffectTable) do
                if iIsProjectObj then
                    v.m_ProjectSkillId = iPreData.m_SkillId
                    v.m_ProjectBuffId = _Data.m_BuffId
                    v.m_AttackRoleID = _AttackRoleID
                    BattleMgr.RecordNewDataAndAssinType(v,EDamageOriginType.ProjectObject,EBattleCalculateMode.AttackMode)
                else
                    v.m_BuffId = _Data.m_BuffID
                    v.m_AttackRoleID = _AttackRoleID
                    BattleMgr.RecordNewDataAndAssinType(v,EDamageOriginType.ProtocolBuff,EBattleCalculateMode.AttackMode)
                end


            end
        end

        ---檢查被害者
        for k,v in pairs(_Data.m_AffectTable) do
            ---承受者是玩家 但施放者不是玩家 才算是敵人的輸出
            _IsInTraceList_def = BattleMgr.IsPlayerShouldBeRecord(tostring(v.m_AffectSID))

            if _IsInTraceList_def and _IsInTraceList_Atk == false then

                if iIsProjectObj then
                    v.m_ProjectSkillId = iPreData.m_SkillId
                    v.m_ProjectBuffId = _Data.m_BuffId
                    v.m_AttackRoleID = _AttackRoleID
                    BattleMgr.RecordNewDataAndAssinType(v,EDamageOriginType.ProjectObject,EBattleCalculateMode.DefenceMode,tostring(v.m_AffectSID))
                else
                    v.m_BuffId = _Data.m_BuffID
                    v.m_AttackRoleID = _AttackRoleID
                    BattleMgr.RecordNewDataAndAssinType(v,EDamageOriginType.ProtocolBuff,EBattleCalculateMode.DefenceMode,tostring(v.m_AffectSID))
                end
            end
        end
    end

    BattleMgr.SetBuffAffect(_Data, _SenderRC)
end

--- 播放地板範圍效果
function BuffMgr.PlayFloorRangeEffect(iBuffId, iPos)
    if iBuffId == 0 then
        return
    else
        local _BuffData = BuffData.GetBuffDataByIdx(iBuffId)

        if _BuffData then
            EffectMgr.PlayEffectWithPos(EEffectType.Model, _BuffData.m_MagicEffectIdx, EffectMgr.m_EffectActive, iPos)
        end
    end
end

---判斷此buff是否為回覆系buff 只看影響HP 單純buff(ex 攻擊力+5%)
---@param iEBuffEffectKind EBuffEffectKind 判斷的buff類型
function BuffMgr.IsCuringTypeBuff(iEBuffEffectKind)

    local _IsImpactHP
    local _IsCuring
    local _IsAlsoDamage

    ---單純給buff的技能也要被記錄 因此也被歸類到isImpactHP = true 那一類
    if iEBuffEffectKind == EBuffEffectKind.AddHp or
    iEBuffEffectKind == EBuffEffectKind.AbsorbHp or
    iEBuffEffectKind == EBuffEffectKind.LessHp or
    iEBuffEffectKind == EBuffEffectKind.BombHp or
    iEBuffEffectKind == EBuffEffectKind.BounceHp or
    iEBuffEffectKind == EBuffEffectKind.Non
     then
        _IsImpactHP = true
     else
        _IsImpactHP = false
    end

    if iEBuffEffectKind == EBuffEffectKind.AddHp then
        _IsCuring = true
        _IsAlsoDamage = false
    elseif iEBuffEffectKind == EBuffEffectKind.AbsorbHp  then
        _IsCuring = true
        _IsAlsoDamage = true
    else
        _IsCuring = false
        _IsAlsoDamage = true
    end

    return _IsImpactHP,_IsCuring,_IsAlsoDamage
end

---判斷此protocolbuff是否為回覆系buff 只看HP
---@param iEProtocolBuffEffectType EProtocolBuffEffectType 判斷的buff類型
---@return boolean _IsImpactHP 是否為影響HP或狀態類
---@return boolean _IsCuring 回傳是否為治療類型 protocolbuff
function BuffMgr.IsCuringType_ProtocolBuff(iEProtocolBuffEffectType)

    local _IsImpactHP
    local _IsCuring
    ---目前沒有吸血類protocol buff預先開對應bool

    if iEProtocolBuffEffectType == EProtocolBuffEffectType.AddHp or
    iEProtocolBuffEffectType == EProtocolBuffEffectType.ReduceHP or
    iEProtocolBuffEffectType == EProtocolBuffEffectType.State
    then
        _IsImpactHP = true
    else
        _IsImpactHP = false
    end

    if iEProtocolBuffEffectType == EProtocolBuffEffectType.AddHP then
        _IsCuring = true
    else
        _IsCuring = false
    end

    return _IsImpactHP,_IsCuring
end

function BuffMgr.RemoveBuffResult(iResult)
    if iResult == 0 then
        if this.m_BuffToRemove then
            local _BuffData = BuffData.GetBuffDataByIdx(this.m_BuffToRemove)
            MessageMgr.AddCenterMsg(false,GString.Format(TextData.Get(480), _BuffData.m_Name))
            this.m_BuffToRemove = nil
        end
    elseif iResult == 1 then
        MessageMgr.AddCenterMsg(false,TextData.Get(483))
    end
end

---檢查玩家身上是否有某可以堆疊層數的buff 並回傳層數以及當前層數是否超過某個特定數值
---@param iBuffID number 狀態流水ID
---@param iThreshold number 檢查用的門檻層數
function BuffMgr.CheckBuffLayer(iBuffID,iThreshold)
    local _BuffLayer = 0
    local _EnoughLayer = false

    if iBuffID ~= 0 and iThreshold ~= 0 then
        local _BuffData = BuffMgr.GetRoleBuff(RoleMgr.m_RC_Player.m_PlayerID, iBuffID)
        if _BuffData then
            _EnoughLayer = _BuffData and _BuffData.m_StackCount >= iThreshold
            _BuffLayer = _BuffData.m_StackCount
        end
    end

    return _BuffLayer, _EnoughLayer
end

return BuffMgr
