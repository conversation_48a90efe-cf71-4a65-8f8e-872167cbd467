---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

-- require("UI/Setting/Setting_Model")

---設定
---author Hui
---version 1.0
---since [ProjectBase] 0.1
---date 2023.02.04

Setting_Controller = {}
local this = Setting_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Setting_View", "Setting_Controller", EUIOrderLayers.FullPage, false, "bg_000")

---玩家經由系統介面主動登出
this.m_IsLogingOut = false
---玩家經由系統介面主動登出
this.m_IsLogingOutChangeLanguage = false
---玩家想切換成甚麼語言
this.m_LanguageDropDownValue = 0

---換語言詢問面板 CommonQueryID
local m_ChangeLanguageAsk_CommonQueryNo = 9
---各語言字串ID
-- ******** 繁體中文  ********	簡體中文 ********	英文 ********	泰文   ******** 日文
local m_Language_TW = ********
local m_Language_CN = ********
local m_Language_EN = ********
local m_Language_TH = ********
local m_Language_JP = ********

---設定頁面
---@class ESettingPage
local ESettingPage = {
    ---影像
    Picture = 1,
    ---音效
    Audio = 2,
    ---戰鬥輔助
    CombatAid = 3,
    ---對話
    Dialogue = 4,
    ---帳號
    Account = 5,
    ---語言設定
    Language = 6,
    ---按鈕配置設定
    KeyboardInput = 7
}

local m_InputUIOpinion = {}

---初始化TitleBar
local function InitTitleBar()
    --region TitleBar 按鈕們
    this.m_Btn_Back = this.m_ViewRef.m_Dic_Trans:Get("&Button_Back")
    Button.AddListener(this.m_Btn_Back, EventTriggerType.PointerClick, function()
            UIMgr.CloseToPreviousPage(this)
        end)

    this.m_Btn_Close = this.m_ViewRef.m_Dic_Trans:Get("&Button_Close_Blue")
    Button.AddListener(this.m_Btn_Close, EventTriggerType.PointerClick, function()
            UIMgr.Close(this)
        end)
    this.m_Group_TMP_Title = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_Title")
    this.m_Group_TMP_Title.text = "設定"
end

local function InitAudio()
    local _TempPanelObj = {}
    _TempPanelObj.AudioSwitch = {}
    _TempPanelObj.AudioSlider = {}
    for key, value in pairs(SettingMgr.m_AudioSwitch) do
        _TempPanelObj.AudioSwitch[key] = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_"..key)
        _TempPanelObj.AudioSwitch[key].isOn = value
        _TempPanelObj.AudioSwitch[key].transform:Find("Text (TMP)"):GetComponent(typeof(TMPro.TMP_Text)).text = key..": "..SettingMgr.m_AudioVolume[key] * 100
        _TempPanelObj.AudioSwitch[key].onValueChanged:AddListener(function(isOn)
                SettingMgr.SetAudioSwich(AudioMgr.EMixerGroup[key], isOn)
            end)

        _TempPanelObj.AudioSlider[key] = this.m_ViewRef.m_Dic_Slider:Get("&Slider_"..key)
        _TempPanelObj.AudioSlider[key].value = SettingMgr.m_AudioVolume[key]
        _TempPanelObj.AudioSlider[key].onValueChanged:AddListener(function()
                SettingMgr.SetAudioVolume(AudioMgr.EMixerGroup[key], Mathf.Ceil(_TempPanelObj.AudioSlider[key].value * 100 ) / 100)
                _TempPanelObj.AudioSwitch[key].transform:Find("Text (TMP)"):GetComponent(typeof(TMPro.TMP_Text)).text = key..": "..SettingMgr.m_AudioVolume[key] * 100
            end)
    end
end

---初始化戰鬥相關的設定物件
local function InitBattle()
    local _TempPanelObj = {}
    _TempPanelObj.AutoSearchSwitch = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_AutoSearch")
    _TempPanelObj.AutoSearchSwitch.isOn = EffectMgr.m_IS_SHOWEFFECT
    _TempPanelObj.AutoSearchSwitch.onValueChanged:AddListener(function(isOn)
            SearchMgr.m_AutoSearch = isOn
        end)

    _TempPanelObj.m_DropDown_UseSkillType = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_UseSkillType")
    _TempPanelObj.m_DropDown_UseSkillType.value = SettingMgr.m_PC_UseSkillType
    if ProjectMgr.IsPC() or ProjectMgr.IsEditor() then
        _TempPanelObj.m_DropDown_UseSkillType.onValueChanged:AddListener(function()
            SettingMgr.SetUseSkillType(_TempPanelObj.m_DropDown_UseSkillType.value)
        end)
    else
        _TempPanelObj.m_DropDown_UseSkillType.gameObject:SetActive(false)
    end
end

local function InitMessage()
    local _TempPanelObj = {}
    _TempPanelObj.m_TalkSpeedSlider = this.m_ViewRef.m_Dic_Slider:Get("&Slider_TalkSpeed")
    _TempPanelObj.m_TalkSpeedSlider.value = MessageSettings.m_DefaultSliderValue
    _TempPanelObj.m_TalkSpeedSlider.onValueChanged:AddListener(function(iValue)
        EventTalk_Model.SetEventTalkSpeed(iValue)
    end)

end


---初始化 邊界內縮
local function InitUIIndentation()
    --寬
    this.m_SliderHorizontal = this.m_ViewRef.m_Dic_Slider:Get("&Slider_Horizontal")
    this.m_Text_Horizontal = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Horizontal")
    this.m_SliderHorizontal.value = UIMgr.m_UIIndentationDistance.Horizontal
    this.m_Text_Horizontal.text = "Horizontal : ".. this.m_SliderHorizontal.value
    this.m_SliderHorizontal.maxValue = MAX_INDENTATION_DISTANCE
    this.m_SliderHorizontal.onValueChanged:AddListener(function()
        local _Value = 0
        if UIMgr.m_IsUseBoundaryCtrl then
            _Value = UIMgr.GetIndentationDistanceInRange(this.m_SliderHorizontal.value)
            UIMgr.SetUIIndentationDistance(this.m_SliderHorizontal.value, UIMgr.m_UIIndentationDistance.Vertical)
        end
        this.m_SliderHorizontal.value = _Value
        this.m_Text_Horizontal.text = "Horizontal : ".. tostring(math.floor(this.m_SliderHorizontal.value))
        UIMgr.SetUIStorePlace()
        end)

    --高
    this.m_SliderVertical = this.m_ViewRef.m_Dic_Slider:Get("&Slider_Vertical")
    this.m_Text_Vertical = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Vertical")
    this.m_SliderVertical.value = UIMgr.m_UIIndentationDistance.Vertical
    this.m_Text_Vertical.text = "Vertical : ".. this.m_SliderVertical.value
    this.m_SliderVertical.maxValue = MAX_INDENTATION_DISTANCE
    this.m_SliderVertical.onValueChanged:AddListener(function()
        local _Value = 0
        if UIMgr.m_IsUseBoundaryCtrl then
            _Value = UIMgr.GetIndentationDistanceInRange(this.m_SliderVertical.value)
            UIMgr.SetUIIndentationDistance(UIMgr.m_UIIndentationDistance.Horizontal, _Value)
            UIMgr.UpdateAllUIAnimation()
        end
        this.m_SliderVertical.value = _Value
        this.m_Text_Vertical.text = "Vertical : ".. tostring(math.floor(_Value))
        end)
end

--- 設定內縮數值
local function SetUIIndentationValue()
    this.m_SliderHorizontal:SetValueWithoutNotify(UIMgr.m_UIIndentationDistance.Horizontal)
    this.m_SliderVertical:SetValueWithoutNotify(UIMgr.m_UIIndentationDistance.Vertical)
end

---初始化 UI 元件縮放比
local function InitUIMatchWidthOrHeight()
    ---MatchValue
    local _FomatString = "%.2f"
    ---設定 UI 元件縮放比
    this.m_Text_MatchValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_MatchValue")
    this.m_Slider_MatchValue = this.m_ViewRef.m_Dic_Slider:Get("&Slider_MatchValue")
    this.m_Slider_MatchValue:SetValueWithoutNotify(UIMgr.m_MatchWidthOrHeight.Value)
    this.m_Text_MatchValue.text = string.format(_FomatString, this.m_Slider_MatchValue.value)
    this.m_Slider_MatchValue.onValueChanged:AddListener(function()
        this.m_Text_MatchValue.text = string.format(_FomatString, this.m_Slider_MatchValue.value)
        UIMgr.SetMatchWidthOrHeight(this.m_Slider_MatchValue.value)
        end)
end

---設定 UI 元件縮放比數值
local function SetUIMatchWidthOrHeight()
    this.m_Slider_MatchValue:SetValueWithoutNotify(UIMgr.m_MatchWidthOrHeight.Value)
end

---返回登入
local function  BackToLogin()
    SendProtocol_001._007(25)
    CameraMgr.SetMainCameraActive(true)
    CameraMgr.SetHUDCameraActive(true)
end

local function OnClick_BackLogin()
	-- 開啟共用詢問視窗
    CommonQueryMgr.AddNewInform(1,{},{}, BackToLogin )
end

local function OnClick_CopyCardID()
    GUIUtility.systemCopyBuffer = this.m_Text_PlayerCardID.text
    MessageMgr.AddCenterMsg(false , "已複製至剪貼簿")
end

---初始化 帳號設定
local function InitAccountSettings()
    ---返回登入按鈕
    this.m_Button_BackToLogin = this.m_ViewRef.m_Dic_Trans:Get("&Button_BackToLogin")
    Button.AddListener(this.m_Button_BackToLogin, EventTriggerType.PointerClick, OnClick_BackLogin)
    ---複製 CardID 按鈕
    this.m_Button_CopyCardID = this.m_ViewRef.m_Dic_Trans:Get("&Button_CopyCardID")
    Button.AddListener(this.m_Button_CopyCardID, EventTriggerType.PointerClick, OnClick_CopyCardID)
    ---玩家的 CardID TMP
    this.m_Text_PlayerCardID = this.m_ViewRef.m_Dic_TMPText:Get("&Text_CardID")
    ---玩家的 帳號 TMP
    this.m_Text_Account = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Account")
end

--- 打開 帳號設定
local function SetAccountSettings()
    this.m_Text_PlayerCardID.text = Login_Model.m_IsLogin == true and tostring(PlayerData.GetCardID()) or ""
    this.m_Text_Account.text = Login_Model.m_IsLogin == true and tostring(Login_Model.m_Str_Account) or ""
    this.m_Button_BackToLogin.gameObject:SetActive(Login_Model.m_IsLogin == true and not string.IsNullOrEmpty(this.m_Text_PlayerCardID.text))
end

--- 初始化 圖像設定
local function InitGraphics()
    local _TempPanelObj = {}
    _TempPanelObj.EffectSwitch = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_ShowEffect")
    _TempPanelObj.EffectSwitch.isOn = EffectMgr.m_IS_SHOWEFFECT
    _TempPanelObj.EffectSwitch.onValueChanged:AddListener(function(isOn)
            EffectMgr.m_IS_SHOWEFFECT = isOn
        end)

    _TempPanelObj.CameraEffectSwitch = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_ShowCameraEffect")
    _TempPanelObj.CameraEffectSwitch.isOn = CameraMgr.m_IS_CAMERASHAKE
    _TempPanelObj.CameraEffectSwitch.onValueChanged:AddListener(function(isOn)
        CameraMgr.m_IS_CAMERASHAKE = isOn
        CameraMgr.SaveData()
    end)

    _TempPanelObj.CameraPlayerEffectSwitch = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_CameraPlayerEffect")
    _TempPanelObj.CameraPlayerEffectSwitch.isOn = CameraMgr.m_IS_PLAYEREFFECT
    _TempPanelObj.CameraPlayerEffectSwitch.onValueChanged:AddListener(function(isOn)
        CameraMgr.m_IS_PLAYEREFFECT = isOn
        CameraMgr.SaveData()
    end)

    _TempPanelObj.CameraMove = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_CameraActiveType")
    _TempPanelObj.CameraMove.isOn = ECameraType.IntToEnum(CameraMgr.GetCMMgr():GetNowState()) == ECameraType.Free
    _TempPanelObj.CameraMove.onValueChanged:AddListener(function(isOn)
        if isOn then
            CameraMgr.SetCameraStats(ECameraType.Free)
        else
            CameraMgr.SetCameraStats(ECameraType.Fix)
        end

        CameraMgr.SaveData()
    end)

    _TempPanelObj.CameraFollow = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_CameraFollowType")
    _TempPanelObj.CameraFollow.isOn = ECameraType.IntToEnum(CameraMgr.GetCMMgr():GetNowState()) == ECameraType.Free
    _TempPanelObj.CameraFollow.onValueChanged:AddListener(function(isOn)
        CameraMgr.SetCameraMove(isOn)
        CameraMgr.SaveData()
    end)

    _TempPanelObj.CameraPostProcess = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_PostProcessActive")
    _TempPanelObj.CameraPostProcess.isOn = CameraMgr.GetPostProcessActive()
    _TempPanelObj.CameraPostProcess.onValueChanged:AddListener(function(isOn)
        CameraMgr.SetPostProcessActive(isOn)
    end)

    local _FarClipPlaneDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_FarClipPlane")
    _TempPanelObj.ShadowQuality = Dropdown:New(_FarClipPlaneDropDown, EShadowDistanceName,
        function(iType)
            CameraMgr.SetFarClipPlane(iType)
        end)
    _TempPanelObj.ShadowQuality:SetValueWithoutNotify(CameraMgr.GetFarClipPlane())

    --[[_TempPanelObj.CameraMove = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_CameraType")
    _TempPanelObj.CameraMove.value = CameraMgr.GetCMMgr():GetNowState()
    _TempPanelObj.CameraMove.onValueChanged:AddListener(function(iType)
        CameraMgr.SetCameraStats(ECameraType.IntToEnum(iType))
        CameraMgr.SaveData()
    end)]]--

    ---設定選項文字
    local _QualityName = {}
    for k,v in pairs(EQualityName) do
        --非PC不開極致選項
        if ProjectMgr.IsPC() or ( k ~= EQuality.Ultra ) then
            _QualityName[k] = v
        end
    end

    local _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_TextureQuality")
    _TempPanelObj.TextureQuality = Dropdown:New(_TextureQualityDropDown, _QualityName,
        function(iType)
            SettingMgr.SaveQualityData(iType)
        end)
    _TempPanelObj.TextureQuality:SetValueWithoutNotify(SettingMgr.GetEQuality())

    _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_ShadowQuality")
    _TempPanelObj.ShadowQuality = Dropdown:New(_TextureQualityDropDown, _QualityName,
        function(iType)
            SettingMgr.SaveShadowQuality(iType)
        end)
    _TempPanelObj.ShadowQuality:SetValueWithoutNotify(SettingMgr.GetShadowQuality())

    _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_ShadowDistance")
    _TempPanelObj.ShadowDistance = Dropdown:New(_TextureQualityDropDown, EShadowDistanceName,
        function(iType)
            SettingMgr.SaveShadowDistance(iType)
        end)
    _TempPanelObj.ShadowDistance:SetValueWithoutNotify(SettingMgr.GetShadowDistance())

    _TempPanelObj.AntiAliasing = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_AntiAliasing")
    _TempPanelObj.AntiAliasing.isOn = SettingMgr.GetAntiAliasing()
    _TempPanelObj.AntiAliasing.onValueChanged:AddListener(function(isOn)
            SettingMgr.SaveAntiAliasing(isOn)
        end)

    _TempPanelObj.vSync = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_vSync")
    _TempPanelObj.vSync.isOn = SettingMgr.GetvSync()
    _TempPanelObj.vSync.onValueChanged:AddListener(function(isOn)
            SettingMgr.SavevSync(isOn)
        end)

    --Toggle水體反射
    _TempPanelObj.WaterReflection = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_WaterReflection")
    _TempPanelObj.WaterReflection.isOn = SettingMgr.GetWaterReflection()
    _TempPanelObj.WaterReflection.onValueChanged:AddListener(function(isOn)
            SettingMgr.SaveWaterReflection(isOn)
        end)

    --裝置太爛，幀數要鎖
    _QualityName[EQuality.Ultra] = nil
    if SettingMgr.m_DeviceLevel < EDeviceLevel.High then
        _QualityName[EQuality.VeryHigh] = nil
    end

    _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_FrameRate")
    _TempPanelObj.FrameRate = Dropdown:New(_TextureQualityDropDown, _QualityName,
        function(iType)
            SettingMgr.SaveFrameRate(iType)
        end)
    _TempPanelObj.FrameRate:SetValueWithoutNotify(SettingMgr.GetFrameRate())

    _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_WindowResolution")
    _TempPanelObj.FrameRate = Dropdown:New(_TextureQualityDropDown, EResolutionName,
        function(iType)
            SettingMgr.SaveScreenResolution(iType)
        end)
    _TempPanelObj.FrameRate:SetValueWithoutNotify(SettingMgr.GetResolution())

    _TextureQualityDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_WindowResolutionType")
    _TempPanelObj.FrameRate = Dropdown:New(_TextureQualityDropDown, EResolutionStyleName,
        function(iType)
            SettingMgr.SaveScreenResolutionStyle(iType)
        end)
    _TempPanelObj.FrameRate:SetValueWithoutNotify(SettingMgr.GetResolutionStyle())

    ---PC才開放螢幕比選項
    if not ProjectMgr.IsPC() then
        _TextureQualityDropDown.gameObject:SetActive(false)
        _TempPanelObj.vSync.gameObject:SetActive(false)
    end

    _TempPanelObj.FullScreen = this.m_ViewRef.m_Dic_Toggle:Get("&Button_Toggle_FullScreen")
    _TempPanelObj.FullScreen.isOn = SettingMgr.GetFullScreen()
    _TempPanelObj.FullScreen.onValueChanged:AddListener(function(isOn)
            SettingMgr.SaveFullScreen(isOn)
        end)

    ---PC才開放全螢幕選項
    if not ProjectMgr.IsPC() then
        _TempPanelObj.FullScreen.gameObject:SetActive(false)
    end
end


---換語言要登出的按鍵方法
local function OnClickConfirm_CommonQueryOfChangeLanguageAndBackLogin()
    ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_LastLanguage", (this.m_LanguageDropDownValue))
    this.m_IsLogingOutChangeLanguage = true
    ---回到登入介面
    BackToLogin()
end


---找到語言對應字串 找不到對應enum時默認回傳繁中
local function GetLanguageTextByLanguageIndex()

    local _StringID = nil
    if this.m_LanguageDropDownValue == ELanguage.TW then
        _StringID = m_Language_TW
    elseif this.m_LanguageDropDownValue == ELanguage.CN then
        _StringID = m_Language_CN
    elseif this.m_LanguageDropDownValue == ELanguage.TH then
        _StringID = m_Language_TH
    else
        _StringID = m_Language_TW
    end

    return _StringID
end

---跳出提示通知換語言需要登出
local function OnClick_ChangeLanguageAndBackLogin()

    local _SelectedLanguage =  TextData.Get(GetLanguageTextByLanguageIndex())
    -- 開啟共用詢問視窗
    CommonQueryMgr.AddNewInform(m_ChangeLanguageAsk_CommonQueryNo,{},{_SelectedLanguage}, OnClickConfirm_CommonQueryOfChangeLanguageAndBackLogin )
end

---初始化語言選擇
local function InitLanguage()
    local _TempPanelObj = {}
    _TempPanelObj.m_DropDown_SelectedLanguage = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_SelectedLanguage")
    _TempPanelObj.m_DropDown_SelectedLanguage.value = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_LastLanguage")

    _TempPanelObj.m_DropDown_SelectedLanguage.onValueChanged:AddListener(function()
        this.m_LanguageDropDownValue = _TempPanelObj.m_DropDown_SelectedLanguage.value
        OnClick_ChangeLanguageAndBackLogin()

    end)
end

---使用中的按鈕物件
local m_ButtonSettingItem = {}
---存起來的按鈕物件
local m_StoredButtonSettingItem = {}
---input的存檔
local m_SaveInput

---按鍵設定的下拉選單
local m_InputOpinionDropDown = nil


--- 初始化 按鍵設定
--- @param iUI string UI名稱
local function Onclick_ButtonSettingsUI(iUI)
    ---查KeyBoardInputSetting.m_InputTable 是否有對應到的資料
    local _inputTable
    for k,v in pairs(KeyBoardInputSetting.m_InputTable) do
        if k == iUI then
            _inputTable = v
            break
        end
    end

    --先把啟用中的東西收好來
    for _UI,_Table in pairs(m_ButtonSettingItem) do
        for _key, _item in pairs(_Table) do
            table.insert(m_StoredButtonSettingItem, _item)
            _Table[_key] = nil
        end

        m_ButtonSettingItem[_UI] = nil

    end

    --依選項複製
    for _key, _ShowName in pairs(_inputTable) do
        if not string.IsNullOrEmpty(_ShowName) then
            ---要調整用的物件
            local _item
            --有舊東西拿出來
            if table.Count(m_StoredButtonSettingItem) > 0 then
                _item = table.FirstAndRemove(m_StoredButtonSettingItem)
                _item.m_Key = _key
            else
                --沒的話生新的
                _item = {}
                _item.m_GObj = GameObject.Instantiate(this.m_Item_ButtonSetting.gameObject)
                _item.m_Name = _item.m_GObj.transform:Find("Text_SettingName")
                _item.m_Name = _item.m_Name:GetComponent(typeof(TMPro.TextMeshProUGUI))
                _item.m_Button = _item.m_GObj.transform:Find("Button_Setting")
                _item.m_Button = Button.New(_item.m_Button)
                _item.m_Key = _key
                --按下去的功能
                _item.m_Button:AddListener(EventTriggerType.PointerClick, function()

                    --開始設定囉
                    InputMgr.ModifyButton(m_InputUIOpinion[m_InputOpinionDropDown.value + 1], _item.m_Key)
                    this.SetButtonSettingUIName(m_InputUIOpinion[m_InputOpinionDropDown.value + 1], _item.m_Key, "設定中")
                end)

                _item.m_GObj.transform:SetParent(this.m_Item_ButtonSetting.parent)
                _item.m_GObj.transform.localScale = Vector3.one
            end

            _item.m_GObj:SetActive(true)

            --放進啟用中物件的流程
            if m_ButtonSettingItem[iUI] == nil then
                m_ButtonSettingItem[iUI] = {}
            end
            m_ButtonSettingItem[iUI][_item.m_Key] = _item

            this.SetButtonSettingUIName(iUI, _item.m_Key)

        end

    end

    ---多出來的要關掉
    if table.Count(m_StoredButtonSettingItem) > 0 then
        for k,v in pairs(m_StoredButtonSettingItem) do
            v.m_GObj:SetActive(false)
        end
    end

end

---設定按鍵名字
function Setting_Controller.SetButtonSettingUIName(iUI, iKey, iKeyCode)
    local _SetString = iKeyCode == nil and EAvailableKeyCode[m_SaveInput[iUI][iKey]] or iKeyCode

    m_ButtonSettingItem[iUI][iKey].m_Name.text = KeyBoardInputSetting.m_InputTable[iUI][iKey] .. ": " .. _SetString
end

--- 初始化 按鍵設定
local function InitButtonSettings()
    --取存檔
    m_SaveInput = ClientSaveMgr.GetDataTable(EClientSaveDataType.KeyBoardInput)

    this.m_Item_ButtonSetting = this.m_ViewRef.m_Dic_Trans:Get("&Item_ButtonSetting")
    this.m_Item_ButtonSetting.gameObject:SetActive(false)

    --取選項
    for k,v in pairs(KeyBoardInputSetting.m_InputTable) do
        table.insert(m_InputUIOpinion, k)
    end

    m_InputOpinionDropDown = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_SelectButtonSettingUI")
    m_InputOpinionDropDown = Dropdown:New(m_InputOpinionDropDown, m_InputUIOpinion,
            function(ivalue)
                Onclick_ButtonSettingsUI(m_InputUIOpinion[ivalue + 1])
            end)
    Onclick_ButtonSettingsUI(m_InputUIOpinion[1])

end

local function OpenPage(iPage)
    if this.m_NowShowPage then
        this.m_Panel_TabAy[this.m_NowShowPage].gameObject:SetActive(false)
    end
    this.m_NowShowPage = iPage
    this.m_Panel_TabAy[this.m_NowShowPage].gameObject:SetActive(true)

    --各頁面開啟設定
    if this.m_NowShowPage == ESettingPage.Picture then
        SetUIIndentationValue()
        SetUIMatchWidthOrHeight()
    elseif this.m_NowShowPage == ESettingPage.Account then
        SetAccountSettings()
    end
end

function Setting_Controller.Open(iParams)
    if iParams and iParams.OpenPage then
        OpenPage(OpenPage)
    end
    GroupButton.OnPointerClickByIndex(this.m_GroupButton_Tab, this.m_Button_TabAy[this.m_NowShowPage].transform:GetSiblingIndex() + 1)
    return true
end

---初始化分頁
local function InitTabGroup()
    this.m_TabContent = this.m_ViewRef.m_Dic_Trans:Get("&TabContent")
    this.m_Button_TabAy = {}
    this.m_Panel_TabAy = {}
    for i = 1, this.m_TabContent.transform.childCount do
        local _Key = i
        this.m_Button_TabAy[_Key] = this.m_ViewRef.m_Dic_Trans:Get(GString.Format("&Button_Tab_{0}", i))

        this.m_Panel_TabAy[_Key] = this.m_ViewRef.m_Dic_Trans:Get(GString.Format("&Panel_{0}", i))

	    --按鈕設定不開放給非PC玩家
        if _Key == ESettingPage.KeyboardInput and not (ProjectMgr.IsEditor() or ProjectMgr.IsPC()) then
            this.m_Button_TabAy[_Key].gameObject:SetActive(false)

        else
            this.m_Panel_TabAy[_Key].gameObject:SetActive(true)

        end

        -- 強制布局排版
        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Panel_TabAy[_Key]);
        this.m_Panel_TabAy[_Key].gameObject:SetActive(false)

        local _Temp = Button.New(this.m_Button_TabAy[_Key])
        _Temp:AddListener(EventTriggerType.PointerClick, function()
                OpenPage(i)
            end)
    end

    this.m_GroupButton_Tab = this.m_ViewRef.m_Dic_Trans:Get("&GroupTab_Text_Vertical")
    GroupButton.ReacquireAllButtonsUnderGroup(this.m_GroupButton_Tab.gameObject)
end

function Setting_Controller.Close()
    GroupButton.ResetAllButtonsState(this.m_GroupButton_Tab.gameObject)
    SettingMgr.OnCloseSetSettings()
    return true
end

---初始化
function Setting_Controller.Init()
    ---初始化 FullPageTitleBar
    this.m_Transform_FullPanel = this.m_ViewRef.m_Dic_Trans:Get("&FullPanel_A")
    this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_Transform_FullPanel, 0, TextData.Get(78))
    InitTabGroup()

    InitAudio()
    InitGraphics()
    InitBattle()
    InitMessage()

    InitUIIndentation()
    InitLanguage()
    InitUIMatchWidthOrHeight()
    InitAccountSettings()
    InitButtonSettings()

    OpenPage(ESettingPage.Picture)
end

function Setting_Controller.OnDestroy()
    table.Clear(m_ButtonSettingItem)
    table.Clear(m_StoredButtonSettingItem)
    return true
end
