---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Data/TextData")
require("Data/IllegalNameData")
require("Data/RandomNameData")
require("Data/ItemData")
require("Data/MenuData")
require("Data/PetData")
require("Data/PetAttributeData")
require("Data/TitleData")
require("Data/AppearanceData")

require("Data/TickData")
require("Data/SkillData")
require("Data/WugongData")
require("Data/WugongGapeData")

require("Data/SceneAttributeData")
require("Data/NPCData")
require("Data/TabData")
require("Data/GuildDomainData")
require("Data/GuildGiftData")
require("Data/GuildLvData")
require("Data/CaptionTheaterData")

require("Data/PartyData")
require("Data/LoveData")
require("Data/LoveActionData")
require("Data/LiveSkillData")
require("Data/PetTalentData")
require("Data/PetStoreData")
require("Data/PetSkillData")
require("Data/PetPowerData")
require("Data/ActivityTag")
require("Data/ActivityContent")
require("Data/MallData")
require("Data/MailData")
require("Data/MallMainTagData")
require("Data/MallSubTagData")
require("Data/GamblingData")
require("Data/GemData")
require("Data/FuDaiData")
require("Data/FormulaData")
require("Data/CurrencyData")
require("Data/CoverData")
require("Data/BuffData")
require("Data/BuffGroup")
require("Data/BannerData")
require("Data/AutoDBSwitchData")
require("Data/StatusNameData")
require("Data/CashRewardData")
require("Data/CashFlowData")
require("Data/CashBonusData")
require("Data/SpecialSkillBtnData")
require("Data/BossGPSData")
require("Data/NotificationData")
require("Data/SiegeMapData")
require("Data/ActBoxData")
require("Data/ActBoxContentData")
require("Data/SellData")
require("Data/SiegeTurretData")
require("Data/DyeingColorData")
require("Data/ChangeweaponData")
require("Data/ActivitySkillData")
require("Data/ComposeData")
require("Data/ComposeTabsData")
require("Data/DisconnectData")
require("Data/CourseRewardsData")
require("Data/DecomposeData")
require("Data/SkillActData")
require("Data/CommonQuery")
require("Data/EnhanceColorData")
require("Data/EnhanceData")
require("Data/EnhanceFXData")
require("Data/EnhanceItemData")
require("Data/EnhanceProbData")
require("Data/ExchangeConditionData")
require("Data/ExchangeItemData")
require("Data/ExchangeShopData")
require("Data/ExchangeCardData")
require("Data/StageData")
require("Data/WardrobeData")
require("Data/SuitData")
require("Data/FeatureData")
require("Data/MapLinkData")
require("Data/BattleTextData")
require("Data/QuestListData")
require("Data/TeachingStepsData")
require("Data/UITeachData")
require("Data/FacialFeatureData")
require("Data/WugongPreviewData")
require("Data/TakemedicineData")
require("Data/LivingSkillinfoData")

require("Data/HeadIconData")
require("Data/HeadFrameData")

require("Data/WorldMapData")
require("Data/MapTabsData")
require("Data/MapNapTabsData")
require("Data/EventCollisionClassData")
require("Data/MapNpcClassData")

require("Data/TimeMachineData")
require("Data/TMRoomEffectData")

require("Data/LevelData")
require("Data/EnergyMineData")
require("Data/CampaignData")
require("Data/CampaignPassData")

require("Data/NPCTreasureData")
require("Data/ExplorePoint_Prize")
require("Data/QuestPrize")

require("Data/FriendRank")

require("Data/CombineItem")
require("Data/PetFuseData")
require("Data/RandomSkill")

require("Data/QuickMessageData")
require("Data/EmojiData")
require("Data/PetMissionData")

--region 黑市商人串檔資料 Added by 凌傑RM#120024 0121
require("Data/MysticShopData")
require("Data/MysticItemData")
--endregion

require("Data/ModifyArmorData")
require("Data/ModifyBrainData")
require("Data/ModifyCustomizeData")


require("Data/AchievementID")
require("Data/AchievementTitle")


require("Data/CollectTitle")
require("Data/CollectData")

--region 內功系統
require("Data/InnerBookData")
require("Data/InnerMeridianData")
require("Data/InnerMeridianSkillData")
--endregion

---author 鼎翰
---telephone #2917
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.18

---一般串檔的 Model
---@class DataModel
DataModel = {}
local this = DataModel

---儲存所有一般串檔 Cotoutine 的 table
---@type table
this.m_DataCoroutineTable = {}

---儲存所有一般串檔的 table
---@type table
this.m_DataTable = {
    TextData,
    IllegalNameData,
    RandomNameData,
    MenuData,
    ItemData,
    PetData,
    PetAttributeData,
    TitleData,
    AppearanceData,
    --region 戰鬥相關
    SkillActData,
    TickData,
    SkillData,
    WugongData,
    WugongGapeData,
    --endregion 戰鬥相關
    SceneAttributeData,
    NPCData,
    TabData,
    GuildDomainData,
    GuildGiftData,
    GuildLvData,
    CaptionTheaterData,
    PartyData,
    LoveData,
    LoveActionData,
    LiveSkillData,
    PetTalentData,
    PetStoreData,
    PetSkillData,
    PetPowerData,
    ActivityTag,
    ActivityContent,
    MallData,
    MailData,
    MallMainTagData,
    MallSubTagData,
    GamblingData,
    GemData,
    FuDaiData,
    FormulaData,
    CurrencyData,
    CoverData,
    BuffData,
    BuffGroup,
    BannerData,
    AutoDBSwitchData,
    StatusNameData,
    CashRewardData,
    CashFlowData,
    CashBonusData,
    SpecialSkillBtnData,
    BossGPSData,
    NotificationData,
    SiegeMapData,
    ActBoxData,
    ActBoxContentData,
    SellData,
    SiegeTurretData,
    DyeingColorData,
    ChangeweaponData,
    ActivitySkillData,
    ComposeData,
    ComposeTabsData,
    DisconnectData,
    CourseRewardsData,
    DecomposeData,
    CommonQuery,
    EnhanceColorData,
    EnhanceData,
    EnhanceFXData,
    EnhanceProbData,
    ExchangeConditionData,
    ExchangeItemData,
    ExchangeShopData,
    ExchangeCardData,
    StageData,
    WardrobeData,
    SuitData,
    FeatureData,
    EventMapNPC,
    TeachingStepsData,
    UITeachData,
    MapLinkData,
    BattleTextData,
    DPlotData,
    QuestListData,
    FacialFeatureData,
    WugongPreviewData,
    TakemedicineData,
    LivingSkillinfoData,

    HeadIconData,
    HeadFrameData,

    WorldMapData,
    MapTabsData,
    MapNapTabsData,
    EventCollisionClassData,
    MapNpcClassData,

    --時光機
    TimeMachineData,
    TMRoomEffectData,
    --武裝升級
    LevelData,
    --能量礦脈
    EnergyMineData,

    CampaignData,
    CampaignPassData,

    NPCTreasureData,
    ExplorePoint_Prize,
    QuestPrize,
    
    -- 好友親密度階級
    FriendRank,
    -- 組合物品資訊表
    CombineItem,

    ---聊天 快速句子
    QuickMessageData,
    ---聊天 表情符號
    EmojiData,
	--寵物合成資料
	PetFuseData,
    RandomSkill,
    --region 黑市商人串檔資料 Added by 凌傑RM#120024 0121
    MysticShopData,
    MysticItemData,
    --endregion
    
    ---再造增幅
    ModifyArmorData,
    ModifyBrainData,
    ModifyCustomizeData,

    --寵物派遣資料
    PetMissionData,
    --成就
    AchievementID,
    AchievementTitle,

    ---圖鑑
    CollectTitle,
    CollectData,
    --內功
    InnerBookData,
    InnerMeridianData,
    InnerMeridianSkillData,
}

--取得一般串檔筆數
function DataModel.GetAllData()
    return this.m_DataTable
end

---取得一般串檔總數
function DataModel.GetDataCount()
    return table.Count(this.m_DataTable)
end

---是否包含再依般一般串檔內
function DataModel.Contains(iData)
    return table.Contains(this.m_DataTable, iData)
end

---目前改好 Coroutine 的一般串檔(暫時用)
DataModel.m_CheangeNewCoroutineData = {
	TextData,
	ItemData,
	PetData,
	PetAttributeData,
	ActBoxContentData,
	ActBoxData,
	ActivityContent,
	ActivitySkillData,
	ActivityTag,
	AppearanceData,
	AutoDBSwitchData,
	BannerData,
	BossGPSData,
	CaptionTheaterData,
	CashBonusData,
	CashFlowData,
	CashRewardData,
	ChangeweaponData,
	CommonQuery,
	ComposeData,
	ComposeTabsData,
	CourseRewardsData,
	CoverData,
	CurrencyData,
	DecomposeData,
	DisconnectData,
	DyeingColorData,
	EnhanceColorData,
	EnhanceData,
	EnhanceFXData,
    MenuData,
	TakemedicineData,
	LivingSkillinfoData,
	--region 戰鬥相關
	TickData,
	SkillData,
	WugongData,
	WugongGapeData,
	--endregion 戰鬥相關
	BuffData,
    BuffGroup,
	SuitData,
	TabData,
	StatusNameData,
	SellData,

    --頭像
    HeadIconData,
    HeadFrameData,

    --大地圖
    WorldMapData,
    MapTabsData,
    MapNapTabsData,
    EventCollisionClassData,
    MapNpcClassData,

    --時光機
    TimeMachineData,
    TMRoomEffectData,
    --武裝升級
    LevelData,
    --能量礦脈
    EnergyMineData,
    TeachingStepsData,

    CampaignData,
    CampaignPassData,

    NPCTreasureData,
	ExplorePoint_Prize,
    QuestPrize,
    
    -- 好友親密度階級
    FriendRank,
    -- 組合物品資訊表
    CombineItem,

     ---聊天 快速句子
     QuickMessageData,
     ---聊天 表情符號
     EmojiData,
	--寵物合成資料
	PetFuseData,
    RandomSkill,
    --region 黑市商人串檔資料 Added by 凌傑RM#120024 0121
    MysticShopData,
    MysticItemData,
    --endregion
    
    ---再造增幅
    ModifyArmorData,
    ModifyBrainData,
    ModifyCustomizeData,
    
    ---兌換商店
    ExchangeShopData,
    ExchangeItemData,
    ExchangeConditionData,

	--寵物派遣資料
    PetMissionData,

    --成就
    AchievementID,
    AchievementTitle,

    ---圖鑑
    CollectTitle,
    CollectData,

    --內功
    InnerBookData,
    InnerMeridianData,
    InnerMeridianSkillData,
    
	EnhanceProbData,
    ExchangeCardData,
    FeatureData,
    FormulaData,
    FuDaiData,
    GamblingData,
    GemData,
    GuildDomainData,
    GuildGiftData,
    GuildLvData,
    IllegalNameData,
    LiveSkillData,
    LoveActionData,
    LoveData,
    MailData,
    MallData,
    MapLinkData,
    NotificationData,
    NPCData,
    PartyData,
    PetPowerData,
    PetSkillData,
    PetStoreData,
    PetTalentData,
    QuestListData,
    RandomNameData,
    SceneAttributeData,
    SiegeMapData,
    SiegeTurretData,
    SpecialSkillBtnData,
    StageData,
    TitleData,
    WardrobeData,
    WugongPreviewData,
}

---一般串檔 因為切換語言需要更新的table
DataModel.m_ChangeLanguageRnewTable = {
	TextData,

}
