---小地圖控制器
---@class MiniMap_Controller
---<AUTHOR>
---@date 2022.09.07
MiniMap_Controller = {}
local this = MiniMap_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("MiniMap_View", "MiniMap_Controller", EUIOrderLayers.HalfPage_Right, true)

---攝影機照射範圍寬度
---@type number
local m_CameraOrthographicSize

---攝影機照射最小範圍限制
---@type number
local m_CameraSize_MinLimit

---攝影機照射最大範圍限制
---@type number
local m_CameraSize_MaxLimit

---攝影機照射範圍逐次增減量
---@type number
local m_CameraSize_PerChange

---被複製出來的Mask材質
local m_ClonedMaterial = nil

---攝影機縮放循環
---@class ECameraSize_Loop
ECameraSize_Loop = 
{
    ZoomIn = 1,
    Normal = 2,
    ZoomOut = 3
}

---前次縮放循環
---@type ECameraSize_Loop
local m_LastLoopState = ECameraSize_Loop.Normal
--- 預設小地圖相機正交大小
local m_DefaultCameraSize

---展開按鈕開關狀態 Add by 凌傑RM#131759 2025.0610
---@type boolean
local m_IsFoldSelected = false

---初始化
function MiniMap_Controller.Init()
    this.m_Text_PlayerPosition = this.m_ViewRef.m_Dic_TMPText:Get("&Text_PlayerPosition")
    this.m_Image_MapRender = this.m_ViewRef.m_Dic_Trans:Get("&Image_MapRender")
    Button.AddListener(this.m_Image_MapRender, EventTriggerType.PointerClick, function()
        UIMgr.Open(Map_Controller)
    end)
    this.m_UIAnimation_MapRender = this.m_ViewRef.m_Dic_UIAnimation:Get("&MapRender_Panel")

    -- 縮放視角
    this.m_Btn_ZoomIn = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ZoomIn")
    Button.AddListener(this.m_Btn_ZoomIn, EventTriggerType.PointerClick, this.OnClick_MiniMapZoomIn)
    this.m_Btn_ZoomOut = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ZoomOut")
    Button.AddListener(this.m_Btn_ZoomOut, EventTriggerType.PointerClick, this.OnClick_MiniMapZoomOut)

    -- 傳送選單
    this.m_Btn_Teleport = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Teleport")
    Button.AddListener(this.m_Btn_Teleport, EventTriggerType.PointerClick, this.OnClick_TeleportList)
    
    -- 小地圖開關
    this.m_Btn_Fold = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Fold"))
    this.m_Btn_Fold:AddListener(EventTriggerType.PointerClick, this.OnClick_MiniMapActive)
    
    -- 場景名稱
    this.m_Text_SceneName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SceneName")
    
    -- PVP Icon
    this.m_Img_PvPIcon = this.m_ViewRef.m_Dic_Image:Get("&Img_PvPIcon")

    ---攝影機照射最小範圍限制
    m_CameraSize_MinLimit = MapMgr.MAP_SIZE/24
    ---攝影機照射最大範圍限制
    m_CameraSize_MaxLimit = MapMgr.MAP_SIZE/8
    ---小地圖攝影機預設照射範圍
    m_DefaultCameraSize =  MapMgr.MAP_SIZE/16
    
    ---攝影機照射範圍逐次增減量
    --m_CameraSize_PerChange = (m_CameraSize_MaxLimit - m_CameraSize_MinLimit)/2

    if ProjectMgr.IsEditor() then
        this.SetPosAndRotateActive(false)
    end

    local _RawImage_MapRender = this.m_Image_MapRender:GetComponent("RawImage")
    if _RawImage_MapRender and _RawImage_MapRender.material then
        m_ClonedMaterial = Material.Instantiate(_RawImage_MapRender.material)
        _RawImage_MapRender.material = m_ClonedMaterial
    end
    
    ---該小地圖介面
    this.m_Obj_MiniMapPanel = this.m_ViewRef.m_Dic_Trans:Get("&Main_Panel").gameObject

    ---離開副本物件 DungeonCountDownSet
    this.m_Obj_DungeonCountDownSet = this.m_ViewRef.m_Dic_Trans:Get("&DungeonCountDownSet").gameObject

    ---副本時間倒數條 圓圈
    this.m_Img_CountDownCircle = this.m_ViewRef.m_Dic_Image:Get("&RoundSlider_Fill")

    ---離開副本時間 單位部分的TMP
    this.m_TMP_TimeUnit_Table = {}
    local _PreNameUnit = "&TMP_ExitTimeUnit"
    for i = 1, 2 do
        local _TMPUnitName = _PreNameUnit..i
        this.m_TMP_TimeUnit_Table[i] =   this.m_ViewRef.m_Dic_TMPText:Get(_TMPUnitName)
    end

     ---離開副本時間 數值部分的TMP
     this.m_TMP_TimeValue_Table = {}
     local _PreNameValue = "&TMP_ExitTimeValue"
     for i = 1, 2 do
         local _TMPUnitName = _PreNameValue..i
         this.m_TMP_TimeValue_Table[i] =   this.m_ViewRef.m_Dic_TMPText:Get(_TMPUnitName)
     end

    --- 離開副本時間UI 流光特效 
    ---流光效果的旋轉中心
    this.m_FlashEffectCenter = this.m_ViewRef.m_Dic_Trans:Get("&FlashEffectCenter").gameObject
    ---是否正在旋轉中
    this.m_IsPlayingFlashEffect = false

    -- 離開副本
    this.m_Button_ExitScene_Specific = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_ExitScene_Specific"))
    Button.AddListener(this.m_Button_ExitScene_Specific, EventTriggerType.PointerClick, this.OnClick_ExitScene)
    
    UIMgr.Preload(Map_Controller, false)
end

function MiniMap_Controller.Update()

    if MapMgr.GetCurrentUI() ~= this.m_UIView then
        return
    end

    if RoleMgr.m_RC_Player ~= nil then
        --更新攝影機
        this.UpdateMapCamera()
        --更新玩家座標
        this.UpdateCoordinates()
    end

    MiniMap_Controller.SetExitTimeText()
end

function MiniMap_Controller.IsShowRightPage(iIsSwitch)
    if this.m_Obj_MiniMapPanel ~= nil then
        this.m_Obj_MiniMapPanel:SetActive(iIsSwitch)
    end
end

function MiniMap_Controller.Open()
    --MapMgr.SetCurrentToggleList(EMapForm.MiniMap)
    MapMgr.SetCurrentUI(this.m_UIView)

    -- 初始化小地圖&展開按鈕的顯示 Modify by 凌傑RM#131759 2025.0610
    this.m_Btn_Fold:SetSelect(false)
    m_IsFoldSelected = false
    this.m_UIAnimation_MapRender:DoActive(not m_IsFoldSelected)

    this.SetCurrentZoomState(CameraMgr.GetCameraData_MinimapState())
    MapMgr.RefreshMap()
    this.RefreshMap()
    
    return true
end

function MiniMap_Controller.ToDoAfterOpenUISucceed()

end

---刷新地圖
function MiniMap_Controller.RefreshMap()
    --更新攝影機照射寬度
    m_CameraOrthographicSize = MapMgr.m_Camera.orthographicSize
    --更新地圖名稱
    this.m_Text_SceneName.text = TextData.Get(SceneAttributeData.Get(SceneMgr.GetSceneID()).m_SceneNameID)
    --刷新PvPIcon
    this.UpdatePvPIcon()
    --刷新離開場景按鈕(副本類型是0或7就不顯示)
    this.SetExitSceneActive(SceneMgr.GetSceneExitType() > 0 and SceneMgr.GetSceneExitType() < 7)
end

---更新攝影機位置
function MiniMap_Controller.UpdateMapCamera()

    if m_CameraOrthographicSize then
        local _PlayerPos = Vector3.GetTransformPosition(nil, RoleMgr.m_RC_Player.transform)
        -- 將玩家位置換算為地圖上位置
        local _XAxis = MapMgr.m_MapScale * _PlayerPos.x
        local _YAxis = MapMgr.m_MapScale * _PlayerPos.z
        
        local _Rect = MapMgr.m_Camera.rect
        local _width = MapMgr.m_MiniMapRT.width
        local _height = MapMgr.m_MiniMapRT.height
        -- 沒理解為什麼調整Camera Viewport Rect的各項數值都只有寬度會變動，但這樣計算可以得到正確的寬度
        local _Limit_X = m_CameraOrthographicSize * (_width/_height)
        local _Limit_Y = m_CameraOrthographicSize 
        -- 限制攝影機移動範圍
        _XAxis = Mathf.Clamp(_XAxis, _Limit_X, MapMgr.MAP_SIZE - _Limit_X)
        _YAxis = Mathf.Clamp(_YAxis, _Limit_Y, MapMgr.MAP_SIZE - _Limit_Y)
        -- 更新攝影機位置
        MapMgr.m_Camera.transform.localPosition = Vector3.New(_XAxis, _YAxis, 0)
    end

end

---更新座標
function MiniMap_Controller.UpdateCoordinates()
    if not RoleMgr.m_RC_Player then
        return
    end
    local _PlayerPosition = Vector3.GetTransformPosition(nil, RoleMgr.m_RC_Player.transform)
    local _Direct = GFunction.GetTransformLocalEulerAngles(RoleMgr.m_RC_Player.m_ModelObject.transform)

    -- 座標
    local _str_Coordinate = (math.floor(_PlayerPosition.x * 100)).." , " ..(math.floor(_PlayerPosition.z * 100))
    
    -- 旋轉角度
    local _Angle = math.floor(_Direct.y * 10) / 10
    _str_Coordinate = string.Concat(_str_Coordinate, "\n旋轉角度: " .. _Angle )
    
    this.m_Text_PlayerPosition.text = _str_Coordinate
end

---小地圖縮放
function MiniMap_Controller.OnClick_MiniMapZoomIn()
    -- 更新按鈕與相機正交大小
    if m_LastLoopState == ECameraSize_Loop.ZoomOut then
        MapMgr.m_Camera.orthographicSize = m_DefaultCameraSize
        m_LastLoopState = ECameraSize_Loop.Normal
    else
        MapMgr.m_Camera.orthographicSize = m_CameraSize_MinLimit
        this.m_Btn_ZoomIn.gameObject:SetActive(false)
        this.m_Btn_ZoomOut.gameObject:SetActive(true)
        m_LastLoopState = ECameraSize_Loop.ZoomIn
    end
    
    -- 更新攝影機照射範圍
    this.RefreshMap()
    -- 更新存檔
    CameraMgr.SetCameraData_MinimapState(m_LastLoopState)
end

function MiniMap_Controller.OnClick_MiniMapZoomOut()
    -- 更新按鈕與相機正交大小
    if m_LastLoopState == ECameraSize_Loop.ZoomIn then
        MapMgr.m_Camera.orthographicSize = m_DefaultCameraSize
        m_LastLoopState = ECameraSize_Loop.Normal
    else
        MapMgr.m_Camera.orthographicSize = m_CameraSize_MaxLimit
        this.m_Btn_ZoomOut.gameObject:SetActive(false)
        this.m_Btn_ZoomIn.gameObject:SetActive(true)
        m_LastLoopState = ECameraSize_Loop.ZoomOut
    end
    
    -- 更新攝影機照射範圍
    this.RefreshMap()
    -- 更新存檔
    CameraMgr.SetCameraData_MinimapState(m_LastLoopState)
end

---@param iZoomState ECameraSize_Loop
function MiniMap_Controller.SetCurrentZoomState(iZoomState)
    if not iZoomState then
        iZoomState = ECameraSize_Loop.Normal
    end
    
    m_LastLoopState = iZoomState
    if iZoomState == ECameraSize_Loop.ZoomIn then
        -- 正在 ZoomIn，下一步會是回到Normal狀態
        -- 把 ZoomIn 按鈕關掉
        this.m_Btn_ZoomIn.gameObject:SetActive(false)
        -- 把 ZoomOut 按鈕打開
        this.m_Btn_ZoomOut.gameObject:SetActive(true)

        MapMgr.m_Camera.orthographicSize = m_CameraSize_MinLimit
    else
        -- Normal 跟 ZoomOut 狀態，下一步都是 ZoomIn
        -- 把 ZoomOut 按鈕關掉
        this.m_Btn_ZoomOut.gameObject:SetActive(false)
        -- 把 ZoomIn 按鈕打開
        this.m_Btn_ZoomIn.gameObject:SetActive(true)

        if iZoomState == ECameraSize_Loop.ZoomOut then
            MapMgr.m_Camera.orthographicSize = m_CameraSize_MaxLimit
        else
            MapMgr.m_Camera.orthographicSize = m_DefaultCameraSize
        end
    end
end

---小地圖開關
function MiniMap_Controller.OnClick_MiniMapActive()

    if this.m_Btn_Fold then

        -- 展開按鈕觸發時, 設定小地圖&按鈕狀態 Modify by 凌傑RM#131759 2025.0610
        this.m_UIAnimation_MapRender:DoActive(m_IsFoldSelected)
        m_IsFoldSelected = not m_IsFoldSelected
    end
end

function MiniMap_Controller.EnableGlitch()
    m_ClonedMaterial:SetFloat(Shader.PropertyToID("_Alpha"), EffectSetting.m_MiniMapNoSignalAlpha)
end

function MiniMap_Controller.DisableGlitch()
    m_ClonedMaterial:SetFloat(Shader.PropertyToID("_Alpha"), 1)
end

function MiniMap_Controller.UpdatePvPIcon()
    --this.m_Img_PvPIcon.gameObject:SetActive(SceneMgr.GetIsInPVPDungeon())
    -- 取得當前地圖的PVP類型
    local _ScenePVPType = SceneMgr.GetScenePVPType()
    if _ScenePVPType == EScenePVPType.Peace then
        SpriteMgr.Load("MainIcon_046", function(iAsset)
            this.m_Img_PvPIcon.sprite = iAsset
        end)
    elseif _ScenePVPType == EScenePVPType.Neutrality then
        SpriteMgr.Load("MainIcon_047", function(iAsset)
            this.m_Img_PvPIcon.sprite = iAsset
        end)
    elseif _ScenePVPType == EScenePVPType.Hostility then
        SpriteMgr.Load("MainIcon_045", function(iAsset)
            this.m_Img_PvPIcon.sprite = iAsset
        end)
    end
end

---傳送選單
function MiniMap_Controller.OnClick_TeleportList()
    D.Log("[MapMgr] 開啟傳送選單")
end

---離開副本
function MiniMap_Controller.OnClick_ExitScene()
    D.Log("[MapMgr] 離開副本")
    local _SendLeaveDungeon =function()
        SendProtocol_011._002(5)
    end
    ---詢問視窗串表 編號559 詢問是否離開副本的對應相關設定
    CommonQueryMgr.AddNewInform(559,{},{},_SendLeaveDungeon)
end 

--region 各功能開關
--- 開關離開場景按鈕
function MiniMap_Controller.SetExitSceneActive(iIsOn)
    if not this.m_Obj_DungeonCountDownSet then
        return
    end
    
    this.m_Obj_DungeonCountDownSet:SetActive(iIsOn)

end

function MiniMap_Controller.SetPosAndRotateActive(iIsOn)
    if not this.m_Text_PlayerPosition then
        return
    end

    this.m_IsShowPlayerPosAndRotate = iIsOn
    this.m_Text_PlayerPosition.gameObject:SetActive(iIsOn)
end
--endregion

--region 副本時間倒數
---轉一圈所需時間
local m_TweenPeriodTime = 1
---完成此次旋轉 跟 下一次開始旋轉 中間的間隔時間
local m_IldeTime = 1

---啟動流光效果
---@param iTime number 轉一圈需要多少時間
function MiniMap_Controller.FlashEffectTween(iTime)

        this.m_FlashEffectCenter.gameObject:SetActive(true)
        this.m_FlahTween = LeanTween.value(this.m_FlashEffectCenter,0,-360,iTime) 
        :setOnUpdate(
            System.Action_float(
                function(value)
                    this.m_FlashEffectCenter:GetComponent("RectTransform").localEulerAngles =  Vector3(0, 0, value)
                end))
        :setOnComplete(
            System.Action(
                    function()
                        this.m_IsPlayingFlashEffect = false
                        this.m_FlashEffectCenter.gameObject:SetActive(false)
                    end
                )).id

end

--- 設定離開副本時間 Text
function MiniMap_Controller.SetExitTimeText()
    if not this.m_Obj_DungeonCountDownSet.activeSelf then
        return
    end
    --- 時間變化
    local _ExitTime = math.floor(SceneMgr.GetDungeonTime())
    _ExitTime = _ExitTime > 0 and _ExitTime or 0
    local _CurrentTimeSpan = System.TimeSpan.FromSeconds(_ExitTime)

    ---時間顯示
    local _TimeValue = {} 
    local _TimeUnit = {}

    ---大於1小時 用 X時Y分 小於一小時 用 X分Y秒
    if _CurrentTimeSpan.Hours == 0 then
        local _MinuteStr = GFunction.Zero_stuffing(_CurrentTimeSpan.Minutes,2) == "000" and "00" or GFunction.Zero_stuffing(_CurrentTimeSpan.Minutes,2)
        local _SecondStr = GFunction.Zero_stuffing(_CurrentTimeSpan.Seconds,2) == "000" and "00" or GFunction.Zero_stuffing(_CurrentTimeSpan.Seconds,2)

        _TimeValue[1] = _MinuteStr 
        _TimeValue[2] = _SecondStr 

        --- 8005 字串ID 分
        --- 8004 字串ID 秒 
        _TimeUnit[1] = TextData.Get(8005)
        _TimeUnit[2] = TextData.Get(8004)

    else
        local _HoutStr = GFunction.Zero_stuffing(_CurrentTimeSpan.Hours,2) == "000" and "00" or GFunction.Zero_stuffing(_CurrentTimeSpan.Minutes,2)
        local _MinuteStr = GFunction.Zero_stuffing(_CurrentTimeSpan.Minutes,2) == "000" and "00" or GFunction.Zero_stuffing(_CurrentTimeSpan.Minutes,2)

        _TimeValue[1] = _HoutStr
        _TimeValue[2] = _MinuteStr

        --- 8006 字串ID 時
        --- 8005 字串ID 分 
        _TimeUnit[1] = TextData.Get(8006) 
        _TimeUnit[2] = TextData.Get(8005) 
    end

    for i = 1, 2 do
        this.m_TMP_TimeUnit_Table[i].text = _TimeUnit[i]
        this.m_TMP_TimeValue_Table[i].text = _TimeValue[i]
    end

    this.m_Img_CountDownCircle.fillAmount =  _ExitTime/SceneMgr.GetDungeonTotalTime()

    if this.m_IsPlayingFlashEffect == false then
        
        this.m_IsPlayingFlashEffect = true
        
        HEMTimeMgr.DoFunctionDelay(m_IldeTime,MiniMap_Controller.FlashEffectTween,m_TweenPeriodTime)
    end
end
