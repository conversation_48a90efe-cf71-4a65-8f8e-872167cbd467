fileFormatVersion: 2
guid: f975dc9fce9b8f2468021133dedca448
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Foot
    100014: Bip001 L Forearm
    100016: Bip001 L Hand
    100018: Bip001 L HorseLink
    100020: Bip001 L Thigh
    100022: Bip001 L Toe0
    100024: Bip001 L Toe0Nub
    100026: Bip001 L UpperArm
    100028: Bip001 Neck
    100030: Bip001 Pelvis
    100032: Bip001 R Calf
    100034: Bip001 R Clavicle
    100036: Bip001 R Foot
    100038: Bip001 R Forearm
    100040: Bip001 R Hand
    100042: Bip001 R HorseLink
    100044: Bip001 R Thigh
    100046: Bip001 R Toe0
    100048: Bip001 R Toe0Nub
    100050: Bip001 R UpperArm
    100052: Bip001 Spine
    100054: Bip001 Spine1
    100056: Bip001 Xtra01
    100058: Bip001 Xtra0102
    100060: Bip001 Xtra01Nub
    100062: Bip001 Xtra02
    100064: Bip001 Xtra02Nub
    100066: Bip001 Xtra02Opp
    100068: Bip001 Xtra02OppNub
    100070: Bip001 Xtra03
    100072: Bip001 Xtra03Nub
    100074: Bip001 Xtra04
    100076: Bip001 Xtra04Nub
    100078: Bip001 Xtra04Opp
    100080: Bip001 Xtra04OppNub
    100082: Bip001 Xtra05
    100084: Bip001 Xtra05Nub
    100086: Bip001 Xtra05Opp
    100088: Bip001 Xtra05OppNub
    100090: //RootNode
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Foot
    400014: Bip001 L Forearm
    400016: Bip001 L Hand
    400018: Bip001 L HorseLink
    400020: Bip001 L Thigh
    400022: Bip001 L Toe0
    400024: Bip001 L Toe0Nub
    400026: Bip001 L UpperArm
    400028: Bip001 Neck
    400030: Bip001 Pelvis
    400032: Bip001 R Calf
    400034: Bip001 R Clavicle
    400036: Bip001 R Foot
    400038: Bip001 R Forearm
    400040: Bip001 R Hand
    400042: Bip001 R HorseLink
    400044: Bip001 R Thigh
    400046: Bip001 R Toe0
    400048: Bip001 R Toe0Nub
    400050: Bip001 R UpperArm
    400052: Bip001 Spine
    400054: Bip001 Spine1
    400056: Bip001 Xtra01
    400058: Bip001 Xtra0102
    400060: Bip001 Xtra01Nub
    400062: Bip001 Xtra02
    400064: Bip001 Xtra02Nub
    400066: Bip001 Xtra02Opp
    400068: Bip001 Xtra02OppNub
    400070: Bip001 Xtra03
    400072: Bip001 Xtra03Nub
    400074: Bip001 Xtra04
    400076: Bip001 Xtra04Nub
    400078: Bip001 Xtra04Opp
    400080: Bip001 Xtra04OppNub
    400082: Bip001 Xtra05
    400084: Bip001 Xtra05Nub
    400086: Bip001 Xtra05Opp
    400088: Bip001 Xtra05OppNub
    400090: //RootNode
    7400000: M_0202_BRun_00
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0202_BRun_00
      takeName: M_0202_BRun_00
      firstFrame: 0
      lastFrame: 16
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 0e9629b5b402b2c4793454de3454b7a4,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
