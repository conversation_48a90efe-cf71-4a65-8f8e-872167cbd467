//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2021 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------
//
//=====================================================================

using LuaInterface;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UIFrame.ViewRef;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using GameTools.UIExtension;

namespace UIFrame.ViewRef
{
    /// <summary>
    /// Editor 無法顯示 Dictionary, 模擬 Key Value
    /// </summary>
    [Serializable]
    public class RefKeyValue<T> where T : UnityEngine.Object
    {
        public string Key;
        public T Value;
    }

    /// <summary>
    /// Editor 無法顯示 Dictionary, 模擬 Key Value
    /// 給予UI TMP & UGUI Text 使用
    /// </summary>
    [Serializable]
    public class RefUITextKeyValue<T> where T : UnityEngine.Object
    {
        public T Key;
        public uint Value;
    }

    /// <summary>
    /// Editor 模擬 Dictionary 顯示
    /// </summary>
    /// <typeparam name="T"> Key </typeparam>
    /// <typeparam name="T1"> Value </typeparam>
    [Serializable]
    public class RefDic<T> where T : UnityEngine.Component
    {
        /// <summary>
        /// 自動刷新Ref連結的辨識符號
        /// </summary>
        private const string SYMBOL_AUTO_REF = "&";
        /// <summary>
        /// 自動刷新Ref連結的辨識符號 目前設定只用在根據語言切換的圖片
        /// </summary>
        private const string SYMBOL_AUTO_REF_LANGUAGE = "&*";
        /// <summary>
        /// 搜尋用, 也可以用迴圈處理
        /// </summary>
        private Dictionary<string, T> m_Dic = new Dictionary<string, T>();
        /// <summary>
        /// 物件陣列( 顯示用 )
        /// </summary>
        [SerializeField] List<RefKeyValue<T>> m_List;
        public int Count()
        {
            return m_Dic.Count;
        }

        public void Add( T iValue )
        {
            if ( m_List == null )
                m_List = new List<RefKeyValue<T>>();

            RefKeyValue<T> _Ref = new RefKeyValue<T>();
            _Ref.Key = iValue.name;
            _Ref.Value = iValue;
            m_List.Add( _Ref );
        }

        public T Get( string iKey )
        {
            if ( m_Dic == null || !m_Dic.ContainsKey( iKey ) )
                return default( T );   //就是return null

            return m_Dic[ iKey ];
        }

        public string[] GetKeys()
        {
            if (m_Dic == null)
                return null;   //就是return null
            string[] _return = new string[m_Dic.Count];
            int i = 0;
            foreach (KeyValuePair<string, T> item in m_Dic)
            {
                _return[i] = item.Key;
                i++;
            }
            return _return;
        }

        public Dictionary<string, T> GetDic()
        {
            return m_Dic;
        }

        /// <summary>
        /// 清除存取的REF資料
        /// </summary>
        public void Clear()
        {
            if ( m_Dic != null )
                m_Dic.Clear();
            else
                m_Dic = new Dictionary<string, T>();

            if ( m_List != null )
                m_List.Clear();
            else
                m_List = new List<RefKeyValue<T>>();
        }

        /// <summary>
        /// 刷新Dictionary資料( Dictionary資料不會被存在Prefab )
        /// </summary>
        public void Refresh()
        {
            if (m_Dic == null)
                m_Dic = new Dictionary<string, T>();
            else
                m_Dic.Clear();

            for (int i = 0; i < m_List.Count; i++)
                {
                    if (!m_Dic.ContainsKey(m_List[i].Key))
                    {
                        m_Dic.Add(m_List[i].Key, m_List[i].Value);
                    }
                    else
                    {
                        string _Type = m_List[i].Value.ToString();
                        Debug.LogError($"{_Type} has same key, please check object Key: {m_List[i].Key}");
                    }
                }
        }

        public void Search( Transform iTrans )
        {
#if UNITY_EDITOR
            Clear();

            List<T> _List = new List<T>();
            iTrans.GetComponentsInChildren( true, _List );
            for ( int i = 0; i < _List.Count; i++ )
            {
                if ( _List[ i ].name.StartsWith( SYMBOL_AUTO_REF ) )
                {//開頭是自動Ref辨識符號
                    Add( _List[ i ] );
                }
            }
#endif
        }
         /// <summary>
        /// 搜尋Ref連結的辨識符號 (特別用在搜尋會根據語言切換的圖片 的符號)
        /// </summary>
        public void LanguageSearch( Transform iTrans )
        {
#if UNITY_EDITOR
            Clear();

            List<T> _List = new List<T>();
            iTrans.GetComponentsInChildren( true, _List );
            for ( int i = 0; i < _List.Count; i++ )
            {
                if ( _List[ i ].name.StartsWith( SYMBOL_AUTO_REF_LANGUAGE ) )
                {//開頭是自動Ref辨識符號
                    Add( _List[ i ] );
                }
            }
#endif
        }
    }

    /// <summary>
    /// Editor 模擬 Dictionary 顯示
    /// </summary>
    /// <typeparam name="T"> Key </typeparam>
    /// <typeparam name="T1"> Value </typeparam>
    [Serializable]
    public class RefUITextDic<T> where T : UnityEngine.Component
    {
        /// <summary>
        /// 抓字串符號
        /// </summary>
        private const string TEXT_AUTO_REF = "@";
        /// <summary>
        /// 搜尋用, 也可以用迴圈處理
        /// </summary>
        private Dictionary<T, uint> m_Dic = new Dictionary<T, uint>();
        /// <summary>
        /// 物件陣列( 顯示用 )
        /// </summary>
        [SerializeField] List<RefUITextKeyValue<T>> m_List;

        public int Count()
        {
            return m_Dic.Count;
        }

        public void Add( T iValue, uint iTextID )
        {
            if ( m_List == null )
                m_List = new List<RefUITextKeyValue<T>>();

            RefUITextKeyValue<T> _Ref = new RefUITextKeyValue<T>();
            _Ref.Key = iValue;
            _Ref.Value = iTextID;
            m_List.Add( _Ref );
        }

        public uint Get( T iKey )
        {
            if ( m_Dic == null || !m_Dic.ContainsKey( iKey ) )
                return 0;   //就是return null

            return m_Dic[ iKey ];
        }

        public Dictionary<T, uint> GetDic()
        {
            return m_Dic;
        }

        /// <summary>
        /// 清除存取的REF資料
        /// </summary>
        public void Clear()
        {
            if ( m_Dic != null )
                m_Dic.Clear();
            else
                m_Dic = new Dictionary<T, uint>();

            if ( m_List != null )
                m_List.Clear();
            else
                m_List = new List<RefUITextKeyValue<T>>();
        }

        /// <summary>
        /// 刷新Dictionary資料( Dictionary資料不會被存在Prefab )
        /// </summary>
        public void Refresh()
        {
            if ( m_Dic == null )
                m_Dic = new Dictionary<T, uint>();
            else
                m_Dic.Clear();

            for (int i = 0; i < m_List.Count; i++)
            {
                if (!m_Dic.ContainsKey(m_List[i].Key))
                {
                    m_Dic.Add(m_List[i].Key, m_List[i].Value);
                }
            }
        }

        public void Search( Transform iTrans )
        {
#if UNITY_EDITOR
            Clear();

            List<T> _List = new List<T>();
            iTrans.GetComponentsInChildren( true, _List );
            for ( int i = 0; i < _List.Count; i++ )
            {
                string _Text = "";
                //要用特殊符號包字串
                if ( _List[ i ] is TMP_Text )
                {
                    TMP_Text _Tmp = _List[ i ] as TMP_Text;
                    _Text = _Tmp.text;
                }

                //至少要有3個Char才能表達需替換的字串
                if (_Text.Length >= 3)
                {
                    if ( _Text.StartsWith( TEXT_AUTO_REF ) && _Text.EndsWith( TEXT_AUTO_REF ) )
                    {
                        //取出字串編號
                        if ( uint.TryParse( _Text.Substring( 1, _Text.Length - 2 ), out uint _Idx ) )
                            Add( _List[ i ], _Idx );
                    }
                }
            }
#endif
        }
    }
}

/// <summary>
/// MVC View 層 Ref連結
/// 負責Unity物件取得 提供toLua使用
/// 自動獲取 UIPrefab Reference
/// 同Type Ref的數量並不會太多, 可用for or list find
/// <AUTHOR>
/// @version 1.0
/// @since [ProjectBase] 0.1
/// @date 2021.9.29
/// </summary>
[ExecuteInEditMode]
public class ViewRef : MonoBehaviour
{
    #region AutoRef
#if UNITY_EDITOR
    [Header( "沒自動刷新的話開關腳本以自動刷新Ref連結" )]
    /// <summary>
    /// 自動取得Ref連結
    /// </summary>
    [SerializeField] bool m_IsAutoRef = true;

    /// <summary>
    /// When script is loaded or iValue change
    /// </summary>
    private void OnValidate()
    {
        if ( !m_IsAutoRef )
        {
            return;
        }
    }

    [NoToLua]
    /// <summary>
    /// 刷新腳本上的物件Ref連結
    /// </summary>
    public void RefreshRefObject()
    {
        // D.Log( $"Auto ref: { this.name }", Color.green );
        //取得所有的物件
        m_Dic_Trans.Search( this.transform );
        m_Dic_Canvas.Search( this.transform );
        m_Dic_Image.Search( this.transform );
        m_Dic_RawImage.Search( this.transform );
        m_Dic_Toggle.Search( this.transform );
        m_Dic_Slider.Search( this.transform );
        m_Dic_TMPText.Search( this.transform );
        m_Dic_TMPInputField.Search ( this.transform );
        m_Dic_GroupButtonCtrl.Search(this.transform);
        m_Dic_ButtonEx.Search(this.transform);
        m_Dic_Dropdown.Search( this.transform );
        m_Dic_AutoReplace_TMPText.Search( this.transform );
        m_Dic_UIAnimation.Search(this.transform);
        m_Dic_Image_Language.LanguageSearch(this.transform);
        m_Dic_UITeachComponent.Search( this.transform );
        // D.Log( $"Auto ref finish", Color.green );
    }
#endif
    #endregion

    #region UI
    Canvas Canvas;
    public Canvas m_Canvas
    {
        get
        {
            if ( Canvas == null )
            {
                Canvas = GetComponent<Canvas>();
            }

            return Canvas;
        }
    }
    public GameObject BaseGameObjct
    {
        get
        {
            return base.gameObject;
        }
    }
    public Transform BaseTransform
    {
        get
        {
            return base.transform;
        }
    }

    /// <summary>
    /// 模擬Editor Diction顯示
    /// </summary>
    public RefDic<Transform> m_Dic_Trans;
    public RefDic<Canvas> m_Dic_Canvas;
    public RefDic<Image> m_Dic_Image;
    public RefDic<RawImage> m_Dic_RawImage;
    public RefDic<Toggle> m_Dic_Toggle;
    public RefDic<Slider> m_Dic_Slider;
    public RefDic<TMP_Text> m_Dic_TMPText;
    public RefDic<TMP_InputField> m_Dic_TMPInputField;
    public RefDic<GameTools.UIExtension.UIGroupButtonCtrl> m_Dic_GroupButtonCtrl;
    public RefDic<GameTools.UIExtension.ButtonEx> m_Dic_ButtonEx;
    public RefDic<TMP_Dropdown> m_Dic_Dropdown;
    public RefUITextDic<TMP_Text> m_Dic_AutoReplace_TMPText;
    public RefDic<UIAnimation> m_Dic_UIAnimation;
    public RefDic<Image> m_Dic_Image_Language;
    public RefDic<UITeachComponent> m_Dic_UITeachComponent;


    public void SelectToggle( string iKey )
    {
        if ( m_Dic_Toggle != null )
        {
            if ( m_Dic_Toggle.Get( iKey ).group )
            {
                m_Dic_Toggle.Get( iKey ).group.SetAllTogglesOff();
                m_Dic_Toggle.Get( iKey ).Select();
            }
        }
    }
    #endregion

    void Update()
    {
#if UNITY_EDITOR
        if ( m_IsAutoRef && !Application.isPlaying )
        {//運用Unity Editor 某些行為( 點擊Camera || 修改Prefab || 開關腳本 ) 才會觸發Update的特性刷新
            RefreshRefObject();
        }
#endif
    }

    public void Init()
    {
        // Dictionary 不支援儲存資料到Prefab, 初始化時刷一次
        m_Dic_Trans.Refresh();
        m_Dic_Canvas.Refresh();
        m_Dic_Image.Refresh();
        m_Dic_RawImage.Refresh();
        m_Dic_Toggle.Refresh();
        m_Dic_Slider.Refresh();
        m_Dic_TMPText.Refresh();
        m_Dic_TMPInputField.Refresh();
        m_Dic_GroupButtonCtrl.Refresh();
        m_Dic_ButtonEx.Refresh();
        m_Dic_Dropdown.Refresh();
        m_Dic_AutoReplace_TMPText.Refresh();
        m_Dic_UIAnimation.Refresh();
        m_Dic_Image_Language.Refresh();
    }
}


