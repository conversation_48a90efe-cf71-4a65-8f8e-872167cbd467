---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---請輸入結構描述, 用途
---寵物合成介面 Controller 控制寵物合成的，寵物介面下的一個頁面。
---@class PetAttribute_Controller
---author <PERSON> Wei
---telephone #2892
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.19
PetAttribute_Controller = {}
local this = PetAttribute_Controller

--- 普通心
local NORMAL_HEART = "MainBtn_036_A"

--- 紅心
local RED_HEART = "MainBtn_036"

--- 第一技能
local PET_FIRSTSKILL = 1

--- 第二技能
local PET_SECONDSKILL = 2

--- 第三技能
local PET_THIRDSKILL = 3

--- 第一技能解鎖所需星數
local PET_FIRSTSKILL_UNLOCK_STAR = 2

--- 第二技能解鎖所需星數
local PET_SECONDSKILL_UNLOCK_STAR = 3

--- 第三技能解鎖所需星數
local PET_THIRDSKILL_UNLOCK_STAR = 4

--- 寵物道具給的功能數量
local PET_EQUIPMENT_GIVES_NUMBER = 2

--- 最少要給4行
local PET_EQUIPMENT_LOWEST_REQUIREMENT = 4

--- 一行要有幾格
local ROW_ITEM_NUMBER = 5

--- 寵物升星 CommonQuery
local PET_UPSTAR_COMMONQUERY_NUMBER = 514

--- 寵物改名 CommonQuery
local PET_CHANGE_NAME_COMMONQUERY_NUMBER = 510

--- 寵物改名會跳出第二個 CommonQuery 是給介消的
local PET_CHANGE_NAME_SECOND_COMMONQUERY_NUMBER = 509

--- 寵物裝備種類編號
local PET_EQUIPMENT_TYPE_ID = 74

--- 裝備道具 Icon 大小
local PET_EQUIPMENT_ICON = 100

--- 寵物最大星等
local PET_MAX_STAR = 5

--- 寵物道具排序一行格數
local ITEM_PER_ROW = 2

--- 寵物詞有數量字大小
local PET_CARRY_STRING_SIZE = 32

--- 寵物技能 Icon 大小
local PET_SKILL_ICON = 80

--- 名字字串上限數量
local PET_NAME_STRING_SIZE = 7

---==========字串==========---

--- 目前沒裝備中道具 字串
local NO_EQUIPMENT = 20111017

--- 升星條件 字串
local PET_UPSTAR_NEED = 20323128

--- 攻擊倍率 字串
local ATTACK_PERCENTAGE = 20114301

--- 攻速 字串
local ATTSPEED = 20114302

--- 爆率 字串
local CRITICAL_CHANCE = 20114303

--- 寵物更隨中 字串
local PET_FOLLOW = 20323103

--- 寵物派遣中 字串
local PET_MISSION = 20323104

--- 呼叫自律者 字串
local PET_CALL = 20323105

--- 收回自律者 字串
local PET_BACK = 20323106

--- 自律者數量： 字串
local PET_NUMBER = 20323101

--- 最愛設定中 : 字串
local PET_LOVE = 20102021

--- 自律者升星 : 字串
local PET_LEVELUP = 20323107

--- 已達最大星等 : 字串內容
local PET_MAXLEVEL = 20323108

--- 派遣進行中 : 字串內容
local Pet_MISSION_GOING = 20323409

--- 卸下 : 字串內容
local PET_EQUIPMENT_UNLOAD = 20103019

--- 確認 : 字串內容
local PET_EQUIPMENT_CONFIRM = 477

--- 輸入名稱過長，請重新輸入 : 字串內容
local PET_NAME_TOO_LONG = 10300410

--- 解鎖需求 : 字串內容
local PET_UNLOCK_REQUIREMENT = 20111039

--- 狀態派遣中 : 字型
local STATE_MISSION_FONT = "BB"

--- 狀態招喚中 : 字型
local STATE_SUMMONING_FONT = "PO"

--- 基礎數值 : 字型
local BASE_VALUE_FONT = "PO"

--- 強化數值 : 字型
local STRENGTHEN_VALUE_FONT = "G"

--- 寵物升星玩家需要的等級
local PET_UPSTAR_PLAYER_LV_NEED = {
    20,
    30,
    50,
    80
}

--- 列表排序
---@type number
this.m_PetListArrange = 0

--- 點選中寵物 SID
---@type number
this.m_NowOnSelectPetSID = 0

--- 點選中寵物 Id
---@type table
this.m_NowOnSelectPetData = {}

--- 列表顯示用資料
---@type table
this.m_ShowPetListData = {}

--- 背包內Icons
---@type table
this.m_EquipmentIcons = {}

--- 頁面 Init 了沒
---@type bool
this.m_IsInit = false

--- 抓那些 UI 的東西
local function InitialUI()

    -- 初始排序
    this.m_PetListArrange = EPetArrange[1].Power

    -- 資訊介面 寵物種類
    this.m_AttributePetType = this.m_ViewRef.m_Dic_Trans:Get("&Image_PetTypeIcon"):GetComponent(typeof(Image))

    -- 資訊介面 寵物名字
    this.m_AttributeName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_PetAttributeName")

    -- 資訊介面 寵物戰力
    this.m_AttributePower = this.m_ViewRef.m_Dic_TMPText:Get("&Text_AttributePetPower")

    -- 資訊介面 寵物目前狀態 (跟隨中 派遣中)
    this.m_AttributeState = this.m_ViewRef.m_Dic_TMPText:Get("&Text_PetState")

    -- 資訊介面 寵物星數
    this.m_AttributeStarGroup = this.m_ViewRef.m_Dic_Trans:Get("&Panel_AttributeStars")
    this.m_AttributeStars = {}
    for i = 1, PetMaxStar do
        local _Str = "Star" .. i
        this.m_AttributeStars[i] = this.m_AttributeStarGroup.transform:Find(_Str):GetComponent(typeof(Image))
    end

    -- 加加一起設定
    local _AttributeTable = {}

    -- 攻擊相關數值
    this.m_AttackAttribute = {}
    this.m_AttackAttribute.m_GObject = this.m_ViewRef.m_Dic_Trans:Get("&Image_AttackAttribute")
    table.insert(_AttributeTable, this.m_AttackAttribute)

    -- 攻速相關數值
    this.m_AttSpeedAttribute = {}
    this.m_AttSpeedAttribute.m_GObject = this.m_ViewRef.m_Dic_Trans:Get("&Image_AttSpeedAttribute")
    table.insert(_AttributeTable, this.m_AttSpeedAttribute)

    -- 爆擊相關數值
    this.m_CritAttribute = {}
    this.m_CritAttribute.m_GObject = this.m_ViewRef.m_Dic_Trans:Get("&Image_CriticalAttribute")
    table.insert(_AttributeTable, this.m_CritAttribute)

    for _k, _v in pairs(_AttributeTable)do
        _v.m_BasicValue = _v.m_GObject:Find("Text_BasicValue"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    end

    -- 技能相關
    this.m_RightPanel = this.m_ViewRef.m_Dic_Trans:Get("&Panel_RightPart")
    this.m_AttributeSkill = {}
    for i = 1, Pet_Model.GetSkillMaxCount() do
        this.m_AttributeSkill[i] = {}
        this.m_AttributeSkill[i].GObject = this.m_RightPanel:Find("Panel_Skill_"..i)

        if(i ~= PET_THIRDSKILL) then
            this.m_AttributeSkill[i].SkillIcon = IconMgr.NewSkillIcon(0, this.m_AttributeSkill[i].GObject, PET_SKILL_ICON, nil)
        else
            this.m_AttributeSkill[i].SkillIcon = IconMgr.NewBuffIcon(0, this.m_AttributeSkill[i].GObject, PET_SKILL_ICON, function()
                HintMgr_Controller.OpenHint(EHintType.BuffHint, tonumber(this.m_AttributeSkill[i].SkillIcon.m_Idx))
            end)
            
        end

        this.m_AttributeSkill[i].Name = this.m_AttributeSkill[i].GObject:Find("Panel/Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_AttributeSkill[i].Unlock = this.m_AttributeSkill[i].GObject:Find("Panel/Text_UnLock")
        -- 星星字串
        local _StarStr = ""
        for k = 1, i + 1 do
            _StarStr = _StarStr .. "★"
        end
        this.m_AttributeSkill[i].Unlock:GetComponent(typeof(TMPro.TextMeshProUGUI)).text = TextData.Get(PET_UNLOCK_REQUIREMENT).._StarStr
    end

    -- 裝備相關
    this.m_AttributeEquipmentGroup = this.m_ViewRef.m_Dic_Trans:Get("&Image_EquipmentPart")
    this.m_ItemIconParent = this.m_AttributeEquipmentGroup.transform:Find("Image_ItemIcon")
    this.m_ItemIcon = IconMgr.NewItemIcon(0, this.m_ItemIconParent, PET_EQUIPMENT_ICON, PetAttribute_Controller.OpenWindow_Equipment, false)
    this.m_ItemIcon:SetClickTwice(false)
    this.m_ItemName = this.m_ItemIconParent:Find("Text_Name"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    this.m_ItemBonus = {}
    for i = 1, Pet_Model.GetItemBonus() do
        this.m_ItemBonus[i] = {}
        this.m_ItemBonus[i].name = this.m_AttributeEquipmentGroup.transform:Find("Text_Bonus_" .. i):GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_ItemBonus[i].value = this.m_AttributeEquipmentGroup.transform:Find("Text_Bonus_" .. i):Find("Text_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    end

    -- 寵物還不能裝裝備的時候使用的遮罩
    this.m_EquipmentMask = this.m_AttributeEquipmentGroup.transform:Find("ImageWrapper")
    this.m_EquipmentMask.transform:Find("Image_Lock").transform:Find("Text_Unlock"):GetComponent(typeof(TMPro.TextMeshProUGUI)).text = TextData.Get(PET_UNLOCK_REQUIREMENT).."★★★★★"

    -- 沒有寵物裝備的時候顯示 蓋在上面用的
    this.m_ShowWhenNoPet = this.m_ViewRef.m_Dic_Trans:Get("&Image_ScrollViewCover")

    -- 去製作寵物裝備按鈕
    this.m_GoMakePetEquipmentBtn = this.m_ShowWhenNoPet.transform:Find("Button_GoMake")

    -- 寵物升星按鈕
    this.m_PetUpStarBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_UpgradePet")
    Button.AddListener(this.m_PetUpStarBtn, EventTriggerType.PointerClick, PetAttribute_Controller.PetUpStar)

    -- 寵物升星按鈕上的字
    this.m_PetUpStarText = this.m_PetUpStarBtn.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 寵物最愛按鈕
    this.m_PetLoveBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_PetLove")
    Button.AddListener(this.m_PetLoveBtn, EventTriggerType.PointerClick, PetAttribute_Controller.SetPetLove)

    -- 寵物改名
    this.m_PetChangeNameBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_ChangeName")
    Button.AddListener(this.m_PetChangeNameBtn, EventTriggerType.PointerClick, PetAttribute_Controller.PetChangeName)

    -- 寵物招喚/收起
    this.m_PetCallBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_CallPet")
    Button.AddListener(this.m_PetCallBtn, EventTriggerType.PointerClick, PetAttribute_Controller.CallPet)

    -- 招喚/收起按鈕上文字
    this.m_PetCallText = this.m_PetCallBtn.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))

end

--- 初始化寵物裝備介面(分開出來寫 怕難找)
local function InitEquipmentUI()

    -- 稀有度結果 預設是全選
    this.m_SortOptionsSave = {true,true,true,true,true}

    -- 裝備顯示視窗
    this.m_WindowBlackBG = this.m_ViewRef.m_Dic_Trans:Get("&Img_BlackBackGround")
    this.m_WindowEquimpent = this.m_ViewRef.m_Dic_Trans:Get("&Window_Equipment")

    local function CloseEquipWindow()

        -- 黑背景關閉
        this.m_WindowBlackBG.gameObject:SetActive(false)

        -- 裝備選擇視窗 Set False
        this.m_WindowEquimpent.gameObject:SetActive(false)
    end

    -- 關閉裝備顯示視窗
    this.m_CloseWindowEquipment = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_CloseEquipment")
    Button.AddListener(this.m_CloseWindowEquipment, EventTriggerType.PointerClick, CloseEquipWindow)

    -- 第二關閉按鈕
    this.m_CancelEquip = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_CancelAttribute")
    Button.AddListener(this.m_CancelEquip, EventTriggerType.PointerClick, CloseEquipWindow)

    -- 確定裝備按鈕
    this.m_ConfirmEquip = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_ConfirmAttribute")
    Button.AddListener(this.m_ConfirmEquip, EventTriggerType.PointerClick, function()
        
        -- 準備一下道具 ID
        local _ItemID = 0
        if(this.m_PickEquipItem.m_SaveData) then
            _ItemID = this.m_PickEquipItem.m_SaveData.m_ItemIdx
        end

        -- 準備一下道具 SID
        local _ItemSID = 0
        if(this.m_PickEquipItem.m_SaveData) then
            _ItemSID = this.m_PickEquipItem.m_SaveData.m_SID
        end

        -- 送協定 這邊填 1 是因為目前就只有一個裝備 不知道會不會生出第 2 個 應該是不會
        SendProtocol_020._020_08(this.m_NowOnSelectPetSID, 1, _ItemID, _ItemSID)

        -- 關掉裝備頁面
        CloseEquipWindow()

    end)

    -- 確定換裝備按鈕上的文字
    this.m_ConfirmEquipText = this.m_ConfirmEquip.transform:Find("Text_Info"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 寵物現在穿的裝備
    this.m_NowPetEquipItem = {}
    this.m_NowPetEquipItem.GObject = this.m_WindowEquimpent.transform:Find("Image_EquipNow")

    -- 裝備中道具 Icon
    this.m_NowPetEquipItemParent = this.m_NowPetEquipItem.GObject.transform:Find("Image_IconParent")
    this.m_NowPetEquipItem.ItemIcon = IconMgr.NewItemIcon(0, this.m_NowPetEquipItemParent, PET_EQUIPMENT_ICON)
    this.m_NowPetEquipItem.ItemIcon:SetClickTwice(false)

    -- 裝備中道具名稱
    this.m_NowPetEquipItem.ItemName = this.m_NowPetEquipItem.GObject.transform:Find("Text_ItemName"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 裝備中道具數值
    this.m_NowPetEquipItem.ItemValue = {}
    this.m_NowPetEquipItem.ItemValue.Panel = this.m_NowPetEquipItem.GObject.transform:Find("Panel_Value")

    -- 寵物目前沒有裝備 沒有裝備的時候要開啟
    this.m_NowPetNoEquipment = this.m_NowPetEquipItem.GObject.transform:Find("Text_NoEquipNow")

    -- 寵物選擇裝備
    this.m_PickEquipItem = {}
    this.m_PickEquipItem.GObject = this.m_WindowEquimpent.transform:Find("Image_EquipPick")

    -- 裝備中道具 Icon
    this.m_PickEquipItemParent = this.m_PickEquipItem.GObject.transform:Find("Image_IconParent")
    this.m_PickEquipItem.ItemIcon = IconMgr.NewItemIcon(0, this.m_PickEquipItemParent, PET_EQUIPMENT_ICON)
    this.m_PickEquipItem.ItemIcon:SetClickTwice(false)

    -- 寵物選擇名稱
    this.m_PickEquipItem.ItemName = this.m_PickEquipItem.GObject.transform:Find("Text_ItemName"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 寵物選擇裝備數值
    this.m_PickEquipItem.ItemValue = {}
    this.m_PickEquipItem.ItemValue.Panel = this.m_PickEquipItem.GObject.transform:Find("Panel_Value")

    -- 沒有選擇寵物裝備時要顯示
    this.m_NoSelectedPetItem = this.m_PickEquipItem.GObject.transform:Find("Text_NoEquipPick")

    -- 目前只有兩個
    for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
        -- 先開空間
        this.m_NowPetEquipItem.ItemValue[i] = {}
        this.m_PickEquipItem.ItemValue[i] = {}

        -- 裝備中
        this.m_NowPetEquipItem.ItemValue[i].Name = this.m_NowPetEquipItem.ItemValue.Panel.transform:Find("Text_Name_"..i):GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_NowPetEquipItem.ItemValue[i].Value = this.m_NowPetEquipItem.ItemValue[i].Name.transform:Find("Text_Value_"..i):GetComponent(typeof(TMPro.TextMeshProUGUI))

        -- 選擇中
        this.m_PickEquipItem.ItemValue[i].Name = this.m_PickEquipItem.ItemValue.Panel.transform:Find("Text_Name_"..i):GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_PickEquipItem.ItemValue[i].Value = this.m_PickEquipItem.ItemValue[i].Name.transform:Find("Text_Value_"..i):GetComponent(typeof(TMPro.TextMeshProUGUI))
    end

    -- 寵物道具
    this.m_PetItems =  {}

    -- 道具選單 ScrollView
    this.m_ItemScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Item")
    this.m_ReuseScrollItem = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row").gameObject
    this.m_ScrollView = ScrollView.Init(this.m_ItemScrollView, false, this.m_ReuseScrollItem,
    PetAttribute_Controller.GetRowNumber, PetAttribute_Controller.AfterReuseItemInit,
    PetAttribute_Controller.AfterReuseItemIndexUpdate, true)

    -- 寵物排序選項
    this.m_SortOptions = {}

    -- 開啟道具排序
    this.m_OpenSortBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_EquipItemSort")
    Button.AddListener(this.m_OpenSortBtn, EventTriggerType.PointerClick, function()
        this.m_ItemSortMenu.gameObject:SetActive(not this.m_ItemSortMenu.gameObject.activeSelf)
        -- 稀有度選項
        for i = 1, table.Count(this.m_SortOptionsSave) do

            -- 比對是不是開啟
            if(this.m_SortOptionsSave[i] == true) then

                -- 是的話 找到OBJ的選起圖片並選起
                if(this.m_SortOptions[i].transform:Find("Image_Icon_Selected").gameObject) then
                    this.m_SortOptions[i].transform:Find("Image_Icon_Selected").gameObject:SetActive(this.m_SortOptionsSave[i] )
                end
            end
        end 
    end)

    -- 道具排序選單會用到的重複物件
    this.ReUseGObject = this.m_ViewRef.m_Dic_Trans:Get("&ItemRarity_Row").gameObject

    -- 道具排序選單
    this.m_ItemSortMenu = this.m_ViewRef.m_Dic_Trans:Get("&ItemSortOrder")

    -- 道具 ScrollView parent
    this.m_OptionScrollViewParent = this.m_ViewRef.m_Dic_Trans:Get("&OptionScrollViewParent")

    -- 道具排序 m_ScrollView_TitleOverview
    this.m_OptionScrollView = ScrollView.Init(this.m_OptionScrollViewParent, false, this.ReUseGObject, function ()
    return table.Count(TabTextSetting.m_Rarity) end, PetAttribute_Controller.AfterInitSortOptions,
    PetAttribute_Controller.AfterUpdateSortOptions, true
    )

    -- ok 排序確認按鈕
    this.m_SortOkBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_OK")
    Button.AddListener(this.m_SortOkBtn, EventTriggerType.PointerClick, function() ScrollView.Update(this.m_ScrollView) 
        this.m_ItemSortMenu.gameObject:SetActive(not this.m_ItemSortMenu.gameObject.activeSelf) end)

end

--- 寵物狀態轉成文字
---@param number iNum 寵物狀態種類
---@return string _Result
local function PetAttributeTypeToString(iNum)

    local _Result = ""

    if(iNum == EAttributeType.IncreasePetAttack or iNum == EAttributeType.DecreasePetAttack) then
        _Result = TextData.Get(ATTACK_PERCENTAGE)
    elseif(iNum == EAttributeType.IncreasePetAttSpeed or iNum == EAttributeType.DecreasePetAttSpeed) then
        _Result = TextData.Get(ATTSPEED)
    elseif(iNum == EAttributeType.IncreasePetCritChance or iNum == EAttributeType.DecreasePetCritChance) then
        _Result = TextData.Get(CRITICAL_CHANCE)
    elseif(iNum == EAttributeType.IncreasePetCritDmg or iNum == EAttributeType.DecreasePetCritDmg) then
        _Result = TextData.Get(CRITICAL_CHANCE)
    end

    return _Result

end

--- 初始化刪除選擇(稀有度)
function PetAttribute_Controller.AfterInitSortOptions(iItem, iRowIdx)
    local _IntMaxOption = table.Count(TabTextSetting.m_Rarity)

    if(iItem.m_GObj == nil) then
        return
    end

    local _RowNumber = math.ceil(_IntMaxOption / ITEM_PER_ROW)

    -- 選項ID要等於或小於最大數量
    if(iRowIdx <= _RowNumber) then

        -- 啟動顯示
        iItem.m_GObj:SetActive(true)

        -- Index
        local _Index = iRowIdx * ITEM_PER_ROW

        if(_Index <= 0)then
            return;
        end

        -- 圖片色碼
        local _ColorCode  = 0

        for i = 0, 1 do

            -- 初始化色碼
            _ColorCode = 0

            -- 要確認有這個東西
            if(_Index - 1 + i <= table.Count(TabTextSetting.m_RarityTabsOrder)) then

                -- 取出色碼
                _ColorCode = TabTextSetting.m_RarityTabsOrder[_Index - 1 + i].m_Color

            end

            -- 檢查有沒有色碼
            if(_ColorCode ~= 0) then

                -- 存下選項資料後面可以做改動
                this.m_SortOptions[_Index - 1 + i] = Button.New(iItem.m_GObj.transform:GetChild(i).gameObject)

                -- 找到圖片並設下顏色
                this.m_SortOptions[_Index - 1 + i].transform:Find("Image_Icon").gameObject:GetComponent("Image").color = Extension.GetColor(_ColorCode)

                this.m_SortOptions[_Index - 1 + i]:AddListener(EventTriggerType.PointerClick, function()
                    PetAttribute_Controller.OnGroupOptionClick(_Index - 1 + i)
                end)
            else

                -- 沒有的話關閉那格顯示
                iItem.m_GObj.transform:GetChild(i).gameObject:SetActive(false)

            end
        end
        
    else
        iItem.m_GObj:SetActive(false)
    end

end

--- 選項點選event
---@param iIndex integer 目前要加 Event 的按鈕 Index
function PetAttribute_Controller.OnGroupOptionClick(iIndex)

        -- 更新選擇中結果
        this.m_SortOptionsSave[iIndex] = not this.m_SortOptionsSave[iIndex]

        -- 設定選項是否圈起
        if(this.m_SortOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject) then
            this.m_SortOptions[iIndex].transform:Find("Image_Icon_Selected").gameObject:SetActive(this.m_SortOptionsSave[iIndex] )
        end

end

function PetAttribute_Controller.AfterUpdateSortOptions()


end

--- 寵物升星按鈕點擊
function PetAttribute_Controller.PetUpStar()

    -- 寵物S端上存的數值
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(this.m_NowOnSelectPetSID)

    -- 目前幾星
    local _UpStarLv = _PetServerData.m_StarLv
    
    -- 如果寵物已經抵達最大星等 後面就不用走了
    if(_UpStarLv > PET_MAX_STAR) then
        return
    end

    -- 寵物串檔上會有的基本資料
    local _PetBasicData = _PetServerData.m_BasicData

    -- 寵物升星玩家需求等級
    local _NeedPlayerLv = PET_UPSTAR_PLAYER_LV_NEED[_UpStarLv]

    -- 需要的道具 table
    local _NeedItemTable = {}

    -- 需要的道具數量 table
    local _NeedItemAmountTable = {}

    -- table 塞資料
    _NeedItemTable[1] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemID1
    _NeedItemAmountTable[1] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemCount1
    _NeedItemTable[2] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemID2
    _NeedItemAmountTable[2] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemCount2
    -- 看有沒有第三個道具
    if(_PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemID3 ~= 0) then
        _NeedItemTable[3] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemID3
        _NeedItemAmountTable[3] = _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostItemCount3
    end

    -- 玩家等級夠不夠
    local _IsPlayerLvOK = PlayerData.GetLv() >= _NeedPlayerLv

    local _Type2Data_V8 = CommonQueryMgr.GetNewCommonQueryData()

    local _IsConfirmBtnOK = true

    -- 道具數量夠不夠
    for _k, _v in pairs(_NeedItemTable) do

        local _ItemNumber = BagMgr.GetItemInBagAmount(_v)

        if(_ItemNumber < _NeedItemAmountTable[_k]) then
            _IsConfirmBtnOK = false
        end

    end

    -- 玩家等級夠不夠
    if(not _IsPlayerLvOK) then

        _IsConfirmBtnOK = false

    end

    -- 錢錢檢查
    local _MoneyKind = table.GetKey(EItemIDResourceTable, _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostTokenKind)
    local _MyMoney = PlayerData.GetCurrency(EItemIDResourceTable[_MoneyKind])

    if(_MyMoney < _PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostTokenCount) then
        _IsConfirmBtnOK = false
    end

	---給icon模式 使用的icnoBox 填資料範例
    local _MakeText = GString.Format(TextData.Get(20323114), _NeedPlayerLv)
	_Type2Data_V8:BuildIconBoxTable(EIconBoxDataType.Icon,_NeedItemTable,_NeedItemAmountTable,{true,true,true},false)
	_Type2Data_V8:BuildRequireTable(TextData.Get(PET_UPSTAR_NEED),{_MakeText},{_IsPlayerLvOK})
    _Type2Data_V8:BuildConsumeTable({_MoneyKind}, {_PetBasicData.m_PetUpgradeCosts[_UpStarLv].m_CostTokenCount})
    _Type2Data_V8:BuildConfirmBtnStatus(_IsConfirmBtnOK)

	CommonQueryMgr.AddNewInform(PET_UPSTAR_COMMONQUERY_NUMBER,{},{_PetServerData.m_PetName}, function() SendProtocol_020._020_05(_PetServerData.m_PetSlot, _PetServerData.m_PetID) end,nil,
	nil,nil,nil,nil,_Type2Data_V8)

end

--- 寵物升星按鈕點擊
function PetAttribute_Controller.PetChangeName()

    -- 寵物S端上存的數值
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(this.m_NowOnSelectPetSID)

    local _Type3Data = CustomCommonQueryData_Type3.NewData(ECommonQueryType3_InputType.Standard, "", "", true, false, PET_NAME_STRING_SIZE, TextData.Get(PET_NAME_TOO_LONG))

	CommonQueryMgr.AddNewInform(PET_CHANGE_NAME_COMMONQUERY_NUMBER,{},{},
     function()
        SellMgr.OpenUISell(PET_CHANGE_NAME_SECOND_COMMONQUERY_NUMBER, {}, function(_ItemData)
            -- 這邊要改成寵物改名的協定
            SendProtocol_020._008(_PetServerData.m_PetSlot, CustomCommonQueryData_Type3.m_InfoString, _ItemData)
        end
        , {}, nil, nil)
    end,
    nil,
	nil,nil,nil,nil,_Type3Data)

end

--- 寵物招喚/收回
function PetAttribute_Controller.CallPet()

    -- 招喚中寵物 SID
    local _SummonedPet = PetMgr.GetSummonedPetPlot()

    -- 看是要招喚還是收起
    local _IsGonnaSummon = _SummonedPet == 0

    -- 沒有招喚中寵物 把現在選中的寵物 SID 放上去
    if(_SummonedPet == 0) then
        _SummonedPet = this.m_NowOnSelectPetSID
    end
    
    SendProtocol_020._020_03(_SummonedPet, _IsGonnaSummon)

end

--- 取道具名稱和數值
---@param number iKind 道具種類
---@param number iValue 道具數值
local function GetNameAndValue(iKind, iValue)
    local _Name = PetAttributeTypeToString(iKind)
    local _Value = iValue
    local _IsSpeed = false

    -- 要看是不是速度
    if(iKind == EAttributeType.IncreasePetAttSpeed or iKind == EAttributeType.DecreasePetAttSpeed)then
        _IsSpeed = true
    else
        _IsSpeed = false
    end

    -- 不是速度的 value 要做百分比顯示
    if(not _IsSpeed) then
        _Value = _Value / 10 .. "%"
    end

    return _Name, _Value
end

--- 打開裝備選擇視窗
function PetAttribute_Controller.OpenWindow_Equipment()

    -- 點擊的時候把數值清掉
    this.m_PickEquipItem.m_SaveData = nil

    -- 黑背景關閉
    this.m_WindowBlackBG.gameObject:SetActive(true)
        
    -- 裝備選擇視窗 Set True
    this.m_WindowEquimpent.gameObject:SetActive(true)

    -- 寵物數值
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(this.m_NowOnSelectPetSID)

    -- 清空目前選擇中的道具 (一進來一定是空的)
    -- 清空 Icon
    this.m_PickEquipItem.ItemIcon:RefreshIcon(0)

    -- 清空名字
    this.m_PickEquipItem.ItemName.text = ""

    -- 清空數值和名稱
    for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
        this.m_PickEquipItem.ItemValue[i].Name.text = ""
        this.m_PickEquipItem.ItemValue[i].Value.text = ""
    end

    -- 開啟沒有選擇中道具提示
    this.m_NoSelectedPetItem.gameObject:SetActive(true)

    -- 看看寵物有沒有裝備
    if(_PetServerData.m_PetEquipment[1] == 0) then

        -- 清空 Icon
        this.m_NowPetEquipItem.ItemIcon:RefreshIcon(0)

        -- 清空名字
        this.m_NowPetEquipItem.ItemName.text = ""

        -- 清空數值和名稱
        for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
            this.m_NowPetEquipItem.ItemValue[i].Name.text = ""
            this.m_NowPetEquipItem.ItemValue[i].Value.text = ""
        end

        -- 開啟沒有裝備提示
        this.m_NowPetNoEquipment.gameObject:SetActive(true)

        --- 沒有裝備的話保持確認字樣
        this.m_ConfirmEquipText.text = TextData.Get(PET_EQUIPMENT_CONFIRM) -- 確認裝備

    else

        -- 取道具資料
        local _Item = ItemData.GetItemDataByIdx(_PetServerData.m_PetEquipment[1])

        -- 刷新 Icon
        this.m_NowPetEquipItem.ItemIcon:RefreshIcon(_PetServerData.m_PetEquipment[1])

        -- 加上名字
        this.m_NowPetEquipItem.ItemName.text = TextData.Get(_Item.m_Idx_ItemNameText)

        -- 清空數值和名稱
        for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
            if(_Item.m_Attributes[i].m_Kind == 0) then
                this.m_NowPetEquipItem.ItemValue[i].Name.text = ""
                this.m_NowPetEquipItem.ItemValue[i].Value.text = ""
            else

                local _Name, _Value = GetNameAndValue(_Item.m_Attributes[i].m_Kind, _Item.m_Attributes[i].m_Value)

                -- 這邊要改成寵物道具的數值
                this.m_NowPetEquipItem.ItemValue[i].Name.text = _Name
                this.m_NowPetEquipItem.ItemValue[i].Value.text = _Value
            end
        end

        --- 開啟沒有裝備提示
        this.m_NowPetNoEquipment.gameObject:SetActive(false)

        --- 有裝備的話確認的字樣要變
        this.m_ConfirmEquipText.text = TextData.Get(PET_EQUIPMENT_UNLOAD) -- 

    end

    -- 刷一下列表
    ScrollView.UpdateToFirst(this.m_ScrollView)
    
end

--- 取需要幾排
function PetAttribute_Controller.GetRowNumber()

    -- 回傳數字
    local _Result = 0

    -- 寵物道具數量 / 一行要有幾個
    if (next(this.m_PetItems)) then
        _Result = table.Count(this.m_PetItems) / ROW_ITEM_NUMBER
    end

    -- 要數少於 4 就上最少的數量
    if(_Result < PET_EQUIPMENT_LOWEST_REQUIREMENT) then
        _Result = PET_EQUIPMENT_LOWEST_REQUIREMENT
    end

    return PET_EQUIPMENT_LOWEST_REQUIREMENT

end

--- 重複使用道具初始化
function PetAttribute_Controller.AfterReuseItemInit(iItem, iRowIdx)
    local _ItemIcons = {}

    for i = 1, ROW_ITEM_NUMBER do

        local _Parent = iItem.m_GObj.transform
        local _Index = (iRowIdx - 1) * ROW_ITEM_NUMBER + i

        -- 創 Icon
        _ItemIcons[i] = IconMgr.NewItemIcon(0, _Parent, PET_EQUIPMENT_ICON, PetAttribute_Controller.PetEquipmentItemClick, 
        _Index)

        --_ItemIcons[i]:SetClickTwice(false)

        -- 記一下 Parent
        _ItemIcons[i].m_Parent = _Parent

        -- 存下新創Icon資料
        this.m_EquipmentIcons[_Index] = _ItemIcons[i]

    end

    iItem.m_Icons = _ItemIcons

end

--- 重複使用道具更新
function PetAttribute_Controller.AfterReuseItemIndexUpdate(iItem, iRowIdx)

    -- 有東西就關掉遮罩
    if (next(this.m_PetItems)) then

        this.m_ShowWhenNoPet.gameObject:SetActive(false)

    else

        this.m_ShowWhenNoPet.gameObject:SetActive(true)

    end

    local _FilterRarityParam = {} -- 轉換稀有度篩選值
    -- 篩選Icon稀有度選取結果
    for i = 1, table.Count(this.m_SortOptionsSave) do

        -- 找到稀有度項目
        if(this.m_SortOptionsSave[i] == true) then

            -- 取得稀有度設定表
            _FilterRarityParam[i] = TabTextSetting.m_RarityTabsOrder[i].m_Id
        else
            -- 沒有設0
            _FilterRarityParam[i] = 0
        end

    end

    local _Result = {}
    local _Index = 1

    for _key, _value in pairs(this.m_PetItems) do
        
        for _Rkey, _Rvalue in pairs(_FilterRarityParam) do

            if(_value.m_ItemData.m_Rarity == _Rvalue) then

                _Result[_Index] = _value

                _Index = _Index + 1

                break

            end

        end

    end

    -- Update Row Element
    for i = 1, ROW_ITEM_NUMBER do

        -- 算出道具 Index
        local _DataIdx = (iRowIdx - 1) * ROW_ITEM_NUMBER + i

        -- 取資料
        local _ThisItem = _Result[_DataIdx]

        local _Icon = iItem.m_Icons[i]

        -- 有資料
        if(_ThisItem ~= nil) then

            -- 更新Icon內容
            _Icon:RefreshIcon(_ThisItem)

            -- 設定需要點擊兩下
            --_Icon:SetClickTwice(false)

            -- 選中Icon
            IconMgr.CancelSelectIcon(_Icon)

            -- 如果是選上的可能要再選 TODO


        else

            -- 刷新Icon(空)
            _Icon:RefreshIcon(0)

            -- 將選定圈圈關閉
            _Icon:CancelSelect()

        end

    end

end

--- 寵物裝備道具被選中點擊
function PetAttribute_Controller.PetEquipmentItemClick(iIndex)

    local _IconData = this.m_EquipmentIcons[iIndex]

    -- 要是沒有東西就 return
    if(_IconData == nil or _IconData.m_Idx == 0) then
        return
    end

    -- 清空 Icon
    this.m_PickEquipItem.ItemIcon:RefreshIcon(_IconData.m_SaveData)

    -- 清空名字
    this.m_PickEquipItem.ItemName.text = _IconData.m_Name

    -- 把 SaveData 也開個 table 存起來
    this.m_PickEquipItem.m_SaveData = _IconData.m_SaveData

    -- 清空數值和名稱
    for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
        local _SkillKind = _IconData.m_SaveData.m_ItemData.m_Attributes[i].m_Kind
        if(_SkillKind ~= 0) then

            local _Name, _Value = GetNameAndValue(_SkillKind, _IconData.m_SaveData.m_ItemData.m_Attributes[i].m_Value)

            this.m_PickEquipItem.ItemValue[i].Name.text = _Name
            this.m_PickEquipItem.ItemValue[i].Value.text = "".._Value
        else
            this.m_PickEquipItem.ItemValue[i].Name.text = ""
            this.m_PickEquipItem.ItemValue[i].Value.text = ""           
        end
    end

    -- 關閉沒有選擇中道具提示
    this.m_NoSelectedPetItem.gameObject:SetActive(false)

    --- 點選裝備後就改確認
    this.m_ConfirmEquipText.text = TextData.Get(PET_EQUIPMENT_CONFIRM) -- 確認裝備

end

-- 除錯用
local function InitDebug()

    -- 寵物 3D 顯示調整大小
    this.m_3DDebugSizeeGObject = this.m_ViewRef.m_Dic_Trans:Get("&Debug3DSize")

    -- 寵物 3D InputField
    this.m_3DDebugSizeInputField = this.m_ViewRef.m_Dic_TMPInputField:Get("&DebugInputField_Size")

    -- 寵物 3D 顯示調整高度
    this.m_3DDebugHightGObject = this.m_ViewRef.m_Dic_Trans:Get("&Debug3DHight")

    -- 寵物 3D InputField
    this.m_3DDebugHightInputField = this.m_ViewRef.m_Dic_TMPInputField:Get("&DebugInputField_Hight")

    -- 寵物 3D 左右位置調整
    this.m_3DDebugRightGObject = this.m_ViewRef.m_Dic_Trans:Get("&Debug3DRight")

    -- 寵物 3D InputField
    this.m_3DDebugRightInputField = this.m_ViewRef.m_Dic_TMPInputField:Get("&DebugInputField_Right")

end

---初始化
function PetAttribute_Controller.Init(iController)

    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    InitialUI()
    InitEquipmentUI()
    if (ProjectMgr.IsDebug() and ProjectMgr.IsEditor()) then
        InitDebug()
    end

    -- Init 了
    this.m_IsInit = true
end

---Update
function PetAttribute_Controller.Update()

    -- Debug 3D
    if (ProjectMgr.IsDebug() and ProjectMgr.IsEditor()) then
        if(Pet_Model.m_IsPet3DDebug) then

            Pet_Model.m_NowPetCameraHight = tonumber(this.m_3DDebugHightInputField.text)
            Pet_Model.m_NowPetCameraX = tonumber(this.m_3DDebugRightInputField.text)
            Pet_Model.m_NowPetScale = tonumber(this.m_3DDebugSizeInputField.text)

            if(Pet_Model.m_NowPetCameraHight ~= nil and Pet_Model.m_NowPetScale ~= nil) then
                Pet_Model.SetPetCamera(Pet_Model.m_NowPetCameraHight, Pet_Model.m_NowPetCameraX, Pet_Model.m_NowPetScale)
                Pet_Model.SetPetScale(Pet_Model.m_NowPetScale)
            end

            if(not this.m_3DDebugSizeeGObject.gameObject.activeSelf) then
                this.m_3DDebugSizeeGObject.gameObject:SetActive(true)
            end

            if(not this.m_3DDebugHightGObject.gameObject.activeSelf) then
                this.m_3DDebugHightGObject.gameObject:SetActive(true)
            end

            if(not this.m_3DDebugRightGObject.gameObject.activeSelf) then
                this.m_3DDebugRightGObject.gameObject:SetActive(true)
            end

        else

            if(this.m_3DDebugSizeeGObject.gameObject.activeSelf) then
                this.m_3DDebugSizeeGObject.gameObject:SetActive(false)
            end

            if(this.m_3DDebugHightGObject.gameObject.activeSelf) then
                this.m_3DDebugHightGObject.gameObject:SetActive(false)
            end

            if(this.m_3DDebugRightGObject.gameObject.activeSelf) then
                this.m_3DDebugRightGObject.gameObject:SetActive(false)
            end
            
        end
    end

end

--- 刷新寵物道具
local function PetItemRefresh()
    -- 背包道具
    local _BagItems = Bag_Model.BagSortAndReturn(EBagType.Component)

    -- 寵物道具
    this.m_PetItems =  {}

    if(_BagItems ~= nil) then
        for _k, _v in pairs(_BagItems) do

            local _ItemData = ItemData.GetItemDataByIdx(_v.m_ItemIdx)

            if(_ItemData.m_Type == PET_EQUIPMENT_TYPE_ID) then
                table.insert(this.m_PetItems, _v)
            end

        end
    end
end


--- 開啟介面
function PetAttribute_Controller.Open(iParam)

    -- 設定寵物顯示
    if (Extension.IsUnityObjectNull(this.m_PetShowRT)) then
        --- 把 RenderTexture 讀進來
        ResourceMgr.Load("PetRenderTexture", function(iAsset)
            this.m_PetShowRT = iAsset
            AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
            AppearanceMgr.SetClearFlagToSolidColor()
            AppearanceMgr.EnableCamera(true)
        end)
    else
        AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
        AppearanceMgr.SetClearFlagToSolidColor()
        AppearanceMgr.EnableCamera(true)
    end

    PetItemRefresh()
    
    -- 選擇使用第一個選項
    PetList_Controller.SetPanelOption1On(true)

    -- 寵物塞選
    PetAttribute_Controller.Option1Click(this.m_PetListArrange)

    -- 寵物資料 打開就是顯是第一個
    if (next(this.m_ShowPetListData)) then
        PetAttribute_Controller.PetListClick(this.m_ShowPetListData[1])
    end

    -- 寵物數量顯數
    local _String = GString.StringWithStyle(TextData.Get(PET_NUMBER), "BWB")
    local _LargeStr = GString.GetTextWithSize(_String, PET_CARRY_STRING_SIZE)
    PetList_Controller.SetExtraInfo(true, _LargeStr)

    local _NumberString = table.Count(this.m_ShowPetListData).." / "..Pet_Model.GetCarryPetLimit()
    PetList_Controller.SetExtraValue(true, _NumberString)

    return true
end

--- 頁面關閉
function PetAttribute_Controller.Close()

    AppearanceMgr.SetTargetTexture(nil)
    AppearanceMgr.EnableCamera(false)
    Pet_Model.ClearPetModel()

end

--- Destroy 時執行
function PetAttribute_Controller.OnDestroy()

    --- 列表排序
    ---@type number
    this.m_PetListArrange = 0

    --- 點選中寵物 SID
    ---@type number
    this.m_NowOnSelectPetSID = 0

    --- 點選中寵物 Id
    ---@type table
    this.m_NowOnSelectPetData = {}

    --- 列表顯示用資料
    ---@type table
    this.m_ShowPetListData = {}

    --- 背包內Icons
    ---@type table
    this.m_EquipmentIcons = {}

    --- 頁面 Init 了沒
    ---@type bool
    this.m_IsInit = false

end

--- 選擇方案一有更動 這邊是寵物排序要重新給寵物列表
function PetAttribute_Controller.Option1Click(iNumber)

    -- 整理完基本排序的
    local _MyPetData = Pet_Model.ArrangePetListOrder(iNumber)

    -- 沒東西就 return 
    if(table.IsNullOrEmpty(_MyPetData)) then
        PetList_Controller.SetShowData({})
        return nil
    end

    -- 換頁紀錄一下
    this.m_PetListArrange = iNumber

    -- 第一個固定是跟隨中寵物
    -- 招喚中寵物 SID
    local _SummonedPet = PetMgr.GetSummonedPetPlot()

    -- 如果有招喚中寵物要把他排第一
    if(_SummonedPet ~= 0) then

        local _SummonedPetData = {}
        local _OtherPets = {}

        for _, _pet in pairs(_MyPetData) do

            if(_pet.m_PetSlot == _SummonedPet) then
    
                _SummonedPetData = _pet
    
            else
    
                table.insert(_OtherPets, _pet)
    
            end
    
        end

        _MyPetData = {}  -- 清空原表
        table.insert(_MyPetData, _SummonedPetData)

        for _, _pet in pairs(_OtherPets) do
            table.insert(_MyPetData, _pet)  -- 把其它寵物都加進來
        end

    end

    -- 設定寵物列表要用的資料
    local _ShowData = {}
    for _k, _v in pairs(_MyPetData) do

        local _State = false
        if(_v.m_PetState == EMissionMissionState.Working or _v.m_PetState == EMissionMissionState.CollectAble) then
            _State = true
        end

        -- 創建 table
        local _Table = PetList_Controller.CreateEmptyListDataTable()
        _Table.m_ID = _v.m_PetID
        _Table.m_Name = _v.m_PetName
        _Table.m_Type = _v.m_BasicData.m_PetType
        _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_v.m_BasicData.m_Pet2DIcon, 6)
        _Table.m_Star = _v.m_StarLv
        _Table.m_State = _State
        _Table.m_IsShowLikePet = true
        _Table.m_IsPetLoved = _v.m_IsPetLoved
        _Table.m_IsShowNumber = false
        _Table.m_Number = _v.m_Power
        _Table.m_DataType = EPetDataType.PET
        _Table.m_SID = _v.m_PetSlot

        table.insert(_ShowData, _Table)
    end

    -- 設定列表中要出現的資料
    PetList_Controller.SetShowData(_ShowData)

    -- 下拉視窗的文字
    PetList_Controller.SetDropDownListText(iNumber)

    -- 儲存列表資料
    this.m_ShowPetListData = _ShowData
end

--- 寵物點選 有成功設訂的話 return true
function PetAttribute_Controller.PetListClick(iData)

    -- 如果沒有資料或是空表就 return
    if not iData or next(iData) == nil then
        return
    end

    -- 設定寵物種類
    local _TypeStr = Pet_Model.GetStatusImageString(iData.m_Type)
    SpriteMgr.Load(_TypeStr, this.m_AttributePetType)

    -- 設定寵物名字
    this.m_AttributeName.text = iData.m_Name

    -- 設定寵物戰力
    this.m_AttributePower.text = iData.m_Number

    -- 設定寵物星
    for i = 1, PetMaxStar do
        if (i <= iData.m_Star) then
            this.m_AttributeStars[i].color = Extension.GetColor(PetStarColor.STAR_LIGHT)
        else
            this.m_AttributeStars[i].color = Extension.GetColor(PetStarColor.STAR_DARK)
        end
    end

    -- 設定寵物 3D
    Pet_Model.SetPetModel(iData.m_ID)

    -- 如果 Debug 工具有開
    if (ProjectMgr.IsDebug() and ProjectMgr.IsEditor()) then
        if (Pet_Model.m_IsPet3DDebug) then
            this.m_3DDebugSizeInputField.text = Pet_Model.m_NowPetScale
            this.m_3DDebugHightInputField.text = Pet_Model.m_NowPetCameraHight
            this.m_3DDebugRightInputField.text = Pet_Model.m_NowPetCameraX
        end
    end

    -- 清掉上一個點選的框
    if (this.m_NowOnSelectPetSID ~= 0) then
        PetList_Controller.DeleteSelectedItem(this.m_NowOnSelectPetSID)
    end

    -- 紀錄一下SID
    this.m_NowOnSelectPetSID = iData.m_SID

    -- 點中寵物資料
    this.m_NowOnSelectPetData = iData

    -- 設定選擇到表中
    PetList_Controller.SetSelectedItem(iData.m_SID, iData.m_ID)

    -- 刷一下
    PetList_Controller.Refresh()

    -- 寵物資料
    local _PetServerData = PetMgr.GetPlayerPetDataByPetPlot(iData.m_SID)

    -- 企劃給的計算式 數值編號 - 1 + 寵物星等 這會是白字的部分
    local _PetAttribute = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv)

    -- 數值怪怪的就離開
    if (not next(_PetAttribute)) then
        return
    end

    -- 寵物要是沒有 5 星 不給穿裝的
    if (_PetServerData.m_StarLv < PET_MAX_STAR) then
        this.m_EquipmentMask.gameObject:SetActive(true)
    else
        this.m_EquipmentMask.gameObject:SetActive(false)
    end

    local _EquipStates = {}
    -- 裝備狀態
    _EquipStates[EPetStrengthen.ATTACK] = 0
    _EquipStates[EPetStrengthen.ATKSPEED] = 0
    _EquipStates[EPetStrengthen.CRITICAL] = 0

    -- 檢查一下寵物有沒有裝備
    if(_PetServerData.m_PetEquipment[1]) then
        -- 取道具資料
        local _Item = ItemData.GetItemDataByIdx(_PetServerData.m_PetEquipment[1])
        if(_Item ~= nil) then
            for i = 1, PET_EQUIPMENT_GIVES_NUMBER do
                local _Kind = _Item.m_Attributes[i].m_Kind
                local _Value = _Item.m_Attributes[i].m_Value
                if(_Kind == EAttributeType.IncreasePetAttack or _Kind == EAttributeType.DecreasePetAttack) then
                    _EquipStates[EPetStrengthen.ATTACK] = _Value + _EquipStates[EPetStrengthen.ATTACK]
                elseif(_Kind == EAttributeType.IncreasePetAttSpeed or _Kind == EAttributeType.DecreasePetAttSpeed) then
                    _EquipStates[EPetStrengthen.ATKSPEED] = _Value + _EquipStates[EPetStrengthen.ATKSPEED]
                elseif(_Kind == EAttributeType.IncreasePetCritChance or _Kind == EAttributeType.DecreasePetCritChance) then
                    _EquipStates[EPetStrengthen.CRITICAL] = _Value + _EquipStates[EPetStrengthen.CRITICAL]
                end
            end
        end
    end

    -- 綠字的部分 數值編號 - 1 + 寵物星等 + 強化次數 這個要一個一個算 (後面還要加裝備數值)
    local _PetBonusAtk = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATTACK]).m_Atk - _PetAttribute.m_Atk + _EquipStates[EPetStrengthen.ATTACK]
    local _PetBonusAtkSpd = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.ATKSPEED]).m_AtkSpeed - _PetAttribute.m_AtkSpeed + _EquipStates[EPetStrengthen.ATKSPEED]
    local _PetBonusCrit = PetAttributeData.GetPetAttributeDataByIdx(_PetServerData.m_BasicData.m_BasicAttribute - 1 + _PetServerData.m_StarLv + _PetServerData.m_PetStrengthen[EPetStrengthen.CRITICAL]).m_Criticle - _PetAttribute.m_Criticle + _EquipStates[EPetStrengthen.CRITICAL]

    -- 白字 數值是以千分位 要做額外的 / 10 做進位
    local _AttributeStr = {string.format("%.1f", _PetAttribute.m_Atk / 10) .. "%", 
    _PetAttribute.m_AtkSpeed, 
    string.format("%.1f", _PetAttribute.m_Criticle / 10) .. "%"}

    -- 綠字
    if (_PetBonusAtk > 0) then
        _AttributeStr[PET_FIRSTSKILL] = _AttributeStr[PET_FIRSTSKILL] .. GString.StringWithStyle(string.format("+%.1f", _PetBonusAtk / 10) .. "%", STRENGTHEN_VALUE_FONT)
    end

    if (_PetBonusAtkSpd > 0) then
        _AttributeStr[PET_SECONDSKILL] = _AttributeStr[PET_SECONDSKILL] .. GString.StringWithStyle("+" .. _PetBonusAtkSpd, STRENGTHEN_VALUE_FONT)
    end

    if (_PetBonusCrit > 0) then
        _AttributeStr[PET_THIRDSKILL] = _AttributeStr[PET_THIRDSKILL] .. GString.StringWithStyle(string.format("+%.1f", _PetBonusCrit / 10) .. "%", STRENGTHEN_VALUE_FONT)
    end

    this.m_AttackAttribute.m_BasicValue.text = _AttributeStr[PET_FIRSTSKILL]
    this.m_AttSpeedAttribute.m_BasicValue.text = _AttributeStr[PET_SECONDSKILL]
    this.m_CritAttribute.m_BasicValue.text = _AttributeStr[PET_THIRDSKILL]

    --技能
    for i = 1, Pet_Model.GetSkillMaxCount() do

        -- 需要加 3 迴圈才能對上
        local _StrengthenSlot = i + 3

        -- 前面兩個技能是 S 端隨機給的 最後一個寵物技能會和串表上的固定 所以讀表
        if (i ~= Pet_Model.GetSkillMaxCount()) then

            -- 寵物技能資料 看有沒有撈到
            local _PetSkillData = SkillData.GetSkillDataByIdx(_PetServerData.m_PetSkill[i])

            -- 防呆 防止沒撈到資料 或資料沒填
            if (_PetSkillData) then

                -- 找目前的技能是啥 要看看有沒有等級
                if (_PetServerData.m_PetStrengthen[_StrengthenSlot] ~= 0) then
                    for k = 1, _PetServerData.m_PetStrengthen[_StrengthenSlot] do

                        _PetSkillData = SkillData.GetSkillDataByIdx(_PetSkillData.m_NextSkillId)

                    end
                end

                -- 再防呆 防止沒撈到資料 或 企劃資料沒填
                if (_PetSkillData) then
                    this.m_AttributeSkill[i].SkillIcon:RefreshIcon(_PetSkillData.m_Idx)
                    this.m_AttributeSkill[i].Name.text = _PetSkillData.m_Name
                end

            end

            -- 第一個條件開啟
            if (i == PET_FIRSTSKILL) then
                if (_PetServerData.m_StarLv >= PET_FIRSTSKILL_UNLOCK_STAR) then
                    this.m_AttributeSkill[i].Unlock.gameObject:SetActive(false)
                else
                    this.m_AttributeSkill[i].Unlock.gameObject:SetActive(true)
                end
            end

            -- 第二個條件開啟
            if (i == PET_SECONDSKILL) then
                if (_PetServerData.m_StarLv >= PET_SECONDSKILL_UNLOCK_STAR) then
                    this.m_AttributeSkill[i].Unlock.gameObject:SetActive(false)
                else
                    this.m_AttributeSkill[i].Unlock.gameObject:SetActive(true)
                end
            end

        else
            -- 第三組技能
            local _BuffData = BuffData.GetBuffDataByIdx(_PetServerData.m_BasicData.m_PassiveBuff)
            if (_BuffData) then
                this.m_AttributeSkill[i].SkillIcon:RefreshIcon(_BuffData.m_Idx)
                this.m_AttributeSkill[i].Name.text = TextData.Get(_BuffData.m_NameTextIdx)
            end
            if (_PetServerData.m_StarLv >= PET_THIRDSKILL_UNLOCK_STAR) then
                this.m_AttributeSkill[i].Unlock.gameObject:SetActive(false)
            else
                this.m_AttributeSkill[i].Unlock.gameObject:SetActive(true)
            end
        end

    end

    -- 裝備
    if (_PetServerData.m_PetEquipment[1] == 0) then

        -- 沒東西就清空 Icon
        this.m_ItemIcon:RefreshIcon(0)
        this.m_ItemIcon:SetSpecialTexture(IconSetting.ESpecialTextureType.Add) -- 圖換成加號

        -- 沒有裝備的顯示
        this.m_ItemName.text = TextData.Get(NO_EQUIPMENT)

        -- 數值清空
        for i = 1, Pet_Model.GetItemBonus() do
            this.m_ItemBonus[i].name.text = ""
            this.m_ItemBonus[i].value.text = ""
        end

    else

        -- 抓一下道具資料
        local _Item = ItemData.GetItemDataByIdx(_PetServerData.m_PetEquipment[1])
        if (_Item == nil) then
            return
        end

        -- 有東西就刷新 Icon
        this.m_ItemIcon:RefreshIcon(_PetServerData.m_PetEquipment[1])

        -- 有裝備的顯示
        this.m_ItemName.text = TextData.Get(_Item.m_Idx_ItemNameText)

        for i = 1, Pet_Model.GetItemBonus() do
            if(_Item.m_Attributes[i].m_Kind == 0) then
                this.m_ItemBonus[i].name.text = ""
                this.m_ItemBonus[i].value.text = ""
            else

                -- 這邊要改成寵物道具的數值
                local _Name, _Value = GetNameAndValue(_Item.m_Attributes[i].m_Kind, _Item.m_Attributes[i].m_Value)
                _Value = "+ ".._Value

                this.m_ItemBonus[i].name.text = _Name
                this.m_ItemBonus[i].value.text = _Value
            end
        end

    end

    -- 寵物最愛
    if (_PetServerData.m_IsPetLoved) then
        SpriteMgr.Load(RED_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))
    else
        SpriteMgr.Load(NORMAL_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))
    end

    -- 寵物招喚
    if (PetMgr.GetSummonedPetPlot() > 0 and not iData.m_State) then

        -- 寵物狀態文字
        if (PetMgr.GetSummonedPetPlot() == iData.m_SID) then
            this.m_AttributeState.text = GString.StringWithStyle(TextData.Get(PET_FOLLOW), STATE_SUMMONING_FONT)
        else
            this.m_AttributeState.text = ""
        end

        -- 寵物按鈕文字
        this.m_PetCallText.text = TextData.Get(PET_BACK)

        -- 寵物按鈕如果沒有開啟
        Button.SetEnable(this.m_PetCallBtn)

    elseif(iData.m_State) then

        this.m_AttributeState.text =  GString.StringWithStyle(TextData.Get(PET_MISSION), STATE_MISSION_FONT)

        -- 寵物按鈕文字
        this.m_PetCallText.text = TextData.Get(Pet_MISSION_GOING)

        -- 寵物按鈕如果沒有開啟
        Button.SetDisable(this.m_PetCallBtn, false)

    else

        -- 寵物狀態文字
        this.m_AttributeState.text = ""

        -- 寵物按鈕文字
        this.m_PetCallText.text = TextData.Get(PET_CALL)
        
        -- 寵物按鈕如果沒有開啟
        Button.SetEnable(this.m_PetCallBtn)

    end

    -- 寵物升星按鈕狀態
    if (iData.m_Star >= PetMaxStar) then

        Button.SetDisable(this.m_PetUpStarBtn, false)
        this.m_PetUpStarText.text = TextData.Get(PET_MAXLEVEL)

    else

        Button.SetEnable(this.m_PetUpStarBtn)
        this.m_PetUpStarText.text = TextData.Get(PET_LEVELUP)

    end

end

--- 設定寵物最愛
function PetAttribute_Controller.SetPetLove()

    -- 讀取最愛寵物
    local _LovePet = ClientSaveMgr.GetDataValue(EClientSaveDataType.LovePet, LOVE_PET)
    if(_LovePet == nil)then
		_LovePet = {}
	end

    local _SIdStr = tostring(this.m_NowOnSelectPetSID)

    -- 查一下寵物有沒有被最愛
    if(_LovePet[_SIdStr] ~= nil) then

        PetMgr.SetPetLoveData(this.m_NowOnSelectPetSID, false)
        _LovePet[_SIdStr] = nil

        SpriteMgr.Load(NORMAL_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))

    else

        PetMgr.SetPetLoveData(this.m_NowOnSelectPetSID, true)
        _LovePet[_SIdStr] = this.m_NowOnSelectPetSID

        SpriteMgr.Load(RED_HEART, this.m_PetLoveBtn.transform:GetComponent(typeof(Image)))

        -- 中央訊息
        MessageMgr.AddCenterMsg(false, TextData.Get(PET_LOVE))

    end

    -- 改ClientSave
	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.LovePet,LOVE_PET, _LovePet)

    -- 刷新寵物列表
    PetAttribute_Controller.Option1Click(this.m_PetListArrange)

end

--- 協定後刷新
function PetAttribute_Controller.RefreshAfterProtocol()
    
    -- 刷新寵物列表
    PetAttribute_Controller.Option1Click(this.m_PetListArrange)

    -- 用 SID 找列表中資料來做選中寵物的資料刷新
    local _RefreshOnThisPet = {}
    for _k, _v in pairs(this.m_ShowPetListData) do
        if(_v.m_SID == this.m_NowOnSelectPetSID) then
            _RefreshOnThisPet = _v
            break
        end
    end

    -- 重新刷新一下被選中寵物的顯示資料
    PetAttribute_Controller.PetListClick(_RefreshOnThisPet)

    -- 刷新一下寵物裝備
    PetItemRefresh()

end
