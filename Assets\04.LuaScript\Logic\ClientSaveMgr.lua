---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---@class ClientSaveMgr Client 端存檔管理
---author Gabriel
---version 1.0
---since [HEM2.0] 0.1
---date 2022.7.25

---載入存檔相關
---[新增存檔類型需要新增至此](自己做好檔案再加)
require("Common/ClientSaveData/ClientSaveDataBase")
require("Common/ClientSaveData/DeviceData")
require("Common/ClientSaveData/AccountData")
require("Common/ClientSaveData/KeyBoardInputData")
require("Common/ClientSaveData/MissionTrackData")
require("Common/ClientSaveData/AutoDrinkData")
require("Common/ClientSaveData/LoveItemData")
require("Common/ClientSaveData/LoveAreaData")
require("Common/ClientSaveData/ChatChannelData")
require("Common/ClientSaveData/PinPrinterProductData")
require("Common/ClientSaveData/SavedSentenceData")
require("Common/ClientSaveData/LastEmojiData")
require("Common/ClientSaveData/LovePetData")
require("Common/ClientSaveData/MallRedData")
require("Common/ClientSaveData/MailSaveData")

---Client 端存檔管理
ClientSaveMgr = {}
local this = ClientSaveMgr

---@class EClientSaveDataType  Client 端存檔 Data 類型
---[新增存檔類型需要新增至此]
---存檔類型(沒有分類的話自己再加一個)
EClientSaveDataType =
{
    Device = 1,
    Account = 2,
    KeyBoardInput = 3,
    Mail = 8, --UI008
    AutoDrink = 40,    --UI040
    MissionTrack = 61, --UI061
    LoveItem = 102, --UI102
    LoveArea = 51, --UI051
    ChatChannel = 83,   --UI083
    PinPrinterProduct = 112 ,-- UISetting編號 122 合成系統 (3D列印)
    LastEmoji = 8, --聊天介面 表情
    SavedSentence = 10008, --聊天介面 快速發句子 因為不能重複 原聊天介面ID +10000 作為編號
    LovePet = 52, --UI052
    Mall = 127, --UI127

}

---[新增存檔類型需要新增至此]
---ClientSaveMgr 存檔的地方，有新增 EClientSaveDataType 再加
local _ClientSaveData = {}
---ClientSaveMgr 存檔的地方
_ClientSaveData[EClientSaveDataType.Device] = {}
_ClientSaveData[EClientSaveDataType.Account] = {}
_ClientSaveData[EClientSaveDataType.KeyBoardInput] = {}
_ClientSaveData[EClientSaveDataType.AutoDrink] = {}
_ClientSaveData[EClientSaveDataType.MissionTrack] = {}
_ClientSaveData[EClientSaveDataType.LoveItem] = {}
_ClientSaveData[EClientSaveDataType.LoveArea] = {}
_ClientSaveData[EClientSaveDataType.ChatChannel] = {}
_ClientSaveData[EClientSaveDataType.PinPrinterProduct] = {}
_ClientSaveData[EClientSaveDataType.LastEmoji] = {}
_ClientSaveData[EClientSaveDataType.SavedSentence] = {}
_ClientSaveData[EClientSaveDataType.LovePet] = {}
_ClientSaveData[EClientSaveDataType.Mall] = {}
_ClientSaveData[EClientSaveDataType.Mail] = {}

---[新增存檔類型需要新增至此]
---@class EClientSaveDataVersion 存檔版本，有新增 EClientSaveDataType 再加
EClientSaveDataVersion = {}
---裝置存檔版本
EClientSaveDataVersion[EClientSaveDataType.Device] = 0.8
EClientSaveDataVersion[EClientSaveDataType.Account] = 0.1
EClientSaveDataVersion[EClientSaveDataType.KeyBoardInput] = 0.2
EClientSaveDataVersion[EClientSaveDataType.AutoDrink] = 0.6
EClientSaveDataVersion[EClientSaveDataType.MissionTrack] = 0.2
EClientSaveDataVersion[EClientSaveDataType.LoveItem] = 0.1
EClientSaveDataVersion[EClientSaveDataType.LoveArea] = 0.1
EClientSaveDataVersion[EClientSaveDataType.ChatChannel] = 0.1
EClientSaveDataVersion[EClientSaveDataType.PinPrinterProduct] = 0.1
EClientSaveDataVersion[EClientSaveDataType.LastEmoji] = 0.1
EClientSaveDataVersion[EClientSaveDataType.SavedSentence] = 0.1
EClientSaveDataVersion[EClientSaveDataType.LovePet] = 0.1
EClientSaveDataVersion[EClientSaveDataType.Mall] = 0.2
EClientSaveDataVersion[EClientSaveDataType.Mail] = 0.1

---[新增存檔類型需要新增至此]
---@class EClientSaveDataName 檔案名稱，有新增 EClientSaveDataType 再加
EClientSaveDataName = {}
---裝置存檔名稱
EClientSaveDataName[EClientSaveDataType.Device] = "DevicesData"
EClientSaveDataName[EClientSaveDataType.Account] = "AccountData"
EClientSaveDataName[EClientSaveDataType.KeyBoardInput] = "KeyBoardInputData"
EClientSaveDataName[EClientSaveDataType.AutoDrink] = "AutoDrinkData"
EClientSaveDataName[EClientSaveDataType.MissionTrack] = "MissionTrackData"
EClientSaveDataName[EClientSaveDataType.LoveItem] = "LoveItemData"
EClientSaveDataName[EClientSaveDataType.LoveArea] = "LoveAreaData"
EClientSaveDataName[EClientSaveDataType.ChatChannel] = "ChatChannelData"
EClientSaveDataName[EClientSaveDataType.PinPrinterProduct] = "PinPrinterProductData"
EClientSaveDataName[EClientSaveDataType.LastEmoji] = "LastEmojiData"
EClientSaveDataName[EClientSaveDataType.SavedSentence] = "SavedSentenceData"
EClientSaveDataName[EClientSaveDataType.LovePet] = "LovePetData"
EClientSaveDataName[EClientSaveDataType.Mall] = "MallRedData"
EClientSaveDataName[EClientSaveDataType.Mail] = "MailSaveData"

---[新增存檔類型需要新增至此]
---@class EIsRoleIDClientFileDataName Client 端存檔 Data 名稱
---(要分 RoleID 存檔再加) 值必須要跟 EClientSaveDataName 裡面的一樣
local EIsRoleIDClientFileDataName =
{
    AutoDrink = EClientSaveDataName[EClientSaveDataType.AutoDrink],
    MissionTrack = EClientSaveDataName[EClientSaveDataType.MissionTrack],
    LoveItem = EClientSaveDataName[EClientSaveDataType.LoveItem],
    LoveArea = EClientSaveDataName[EClientSaveDataType.LoveArea],
    ChatChannel = EClientSaveDataName[EClientSaveDataType.ChatChannel],
    PinPrinterProduct = EClientSaveDataName[EClientSaveDataType.PinPrinterProduct],
    LastEmoji = EClientSaveDataName[EClientSaveDataType.LastEmoji],
    SavedSentence = EClientSaveDataName[EClientSaveDataType.SavedSentence],
    LovePet = EClientSaveDataName[EClientSaveDataType.LovePet],
    MallRed = EClientSaveDataName[EClientSaveDataType.Mall],
    Mail = EClientSaveDataName[EClientSaveDataType.Mail],
}

---[新增存檔類型需要新增至此]
---@class EIsEncryptClientFileDataName Client 端存檔 Data 名稱
---(要加密再加) 值必須要跟 EClientSaveDataName 裡面的一樣
local EIsEncryptClientFileDataName =
{
    Account = EClientSaveDataName[EClientSaveDataType.Account],
}

--region localFunction

---清除暫存在 ClientSaveMgr 內的指定類型存檔
---@param iEClientSaveDataType EClientSaveDataType 要清除的類型
local function ClearCurrentSaveTable(iEClientSaveDataType)
    _ClientSaveData[iEClientSaveDataType].m_DataTable = {}
    _ClientSaveData[iEClientSaveDataType].m_Version = -1
end

---取得檔案是否分 RoleID 存檔
---@param iFileName string 要改變值的檔案名稱
local function IsFileRoleIDSave(iFileName)
    local _Count = 0
    for key ,value in pairs (EIsRoleIDClientFileDataName) do
        if value == iFileName then
            _Count = 1
        end
    end
    return _Count ~= 0
end

---取得檔案是否加密
---@param iFileName string 要改變值的檔案名稱
local function IsFileEncrypt(iFileName)
    local _Count = 0
    for key ,value in pairs (EIsEncryptClientFileDataName) do
        if value == iFileName then
            _Count = 1
        end
    end
    return _Count ~= 0
end

---取得檔案路徑
---@param iFileName string 要取得路徑的檔案名稱
local function GetPath(iFileName)
    ---存檔名稱
    local _FileName = iFileName
    ---角色 ID
    local _RoleID = 0
    ---路徑
    local _Path = ""

    ---有加密的檔案 名稱也加密
    if IsFileEncrypt(_FileName) then
        _FileName = AES.Encrypt(_FileName)
    end

    if IsFileRoleIDSave(iFileName) then
        _RoleID = PlayerData.GetRoleID() or 0
    end

    _Path = FilePath.GetPath(_FileName, _RoleID)

    return _Path
end

---取得新的 Data 版本
---@param iClientSaveDataType EClientSaveDataType 要取得的檔案類型
local function GetDataVersion(iClientSaveDataType)
    return EClientSaveDataVersion[iClientSaveDataType]
end

---取得 Data 名稱
---@param iClientSaveDataType EClientSaveDataType 要取得的檔案類型
local function GetDataName(iClientSaveDataType)
    return EClientSaveDataName[iClientSaveDataType]
end



---[新增存檔類型需要新增至此]
---取得資料，有新增 EClientSaveDataType 再加
local function GetData(iClientSaveDataType, iData)
    if iClientSaveDataType == EClientSaveDataType.Device then
        --裝置
        return DeviceData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.Account then
        --帳號
        return AccountData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.KeyBoardInput then
        --鍵盤輸入
        return KeyBoardInputData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.AutoDrink then
        --自動喝水
        return AutoDrinkData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.MissionTrack then
        --任務追蹤
        return MissionTrackData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.LoveItem then
        --我的最愛
        return LoveItemData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.LoveArea then
        --我的最愛-大地圖
        return LoveAreaData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.ChatChannel then
        --聊天設定頁面
        return ChatChannelData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.PinPrinterProduct then
        --3D列印介面
        return PinPrinterProductData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.LastEmoji then
        --3D列印介面
        return LastEmojiData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.SavedSentence then
        --3D列印介面
        return SavedSentenceData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.LovePet then
        --我的最愛寵物
        return LovePetData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.Mall then
        --商城紅點
        return MallRedData:New(iData)
    elseif iClientSaveDataType == EClientSaveDataType.Mail then
        --信箱信件
        return MailSaveData:New(iData)
    end
end

---取得存檔資料 並設定在 ClientSaveMgr
---單純取檔只需要輸入名稱就好
---@param iClientSaveDataType EClientSaveDataType 要取得的檔案類型
---@param iData Table 第一次讀存檔時用(要取檔不需要這個)
local function GetDataTable(iClientSaveDataType, iData)
    if table.IsNullOrEmpty(_ClientSaveData[iClientSaveDataType].m_DataTable) then
        local _Data  = GetData(iClientSaveDataType,iData)
        _ClientSaveData[iClientSaveDataType] = _Data
    end
    --return
    return _ClientSaveData[iClientSaveDataType]
end

---讀檔function
---@param iClientSaveDataType EClientSaveDataType 要讀的檔案類型
local function ReadFile(iClientSaveDataType)
    --檔案名稱
    local _DataName = GetDataName(iClientSaveDataType)
    --字串轉 Table 的 Data
    local _Data = nil
    --取得路徑
    local _Path = GetPath(_DataName)
    --取得存檔字串
    local _JsonText = JsonMgr.GetJsonTextByPath(_Path, false)
    --取 Jeson 字串錯誤 (空的 或是 沒有版本 或是沒有 m_DataTable 資料 )
    local _IsJesonTextError = string.IsNullOrEmpty(_JsonText) or string.find(_JsonText, "m_Version") == nil or string.find(_JsonText, "m_DataTable") == nil

    ---Jeson 錯誤格式
    local _ErrorText = "\"m_Version\":"..GetDataVersion(iClientSaveDataType)..","

    ---取檔時如果是取出的字串裡面是數字? Mac 使用 json.decode 會壞掉?
    local _JesonTextError = string.find(_JsonText, _ErrorText)

    if _JesonTextError then
        --取檔時如果是取出的字串裡面成員是數字? Mac 使用 json.decode 會壞掉?
        _JsonText = _JsonText:gsub( _ErrorText, "\"m_Version\":\""..GetDataVersion(iClientSaveDataType).."\",")
    end

    if _IsJesonTextError then
        --重製新資料
        GetDataTable(iClientSaveDataType)
        this.Save(iClientSaveDataType)
    else
        --先解一次
        _Data = JsonMgr.JsonTextToTable(_JsonText)

        --如果取得的資料格式錯誤
        if table.Count(_Data) > ClientSaveDataBase.m_ClientSaveBaseTableCount and (not table.ContainsKey(_Data, "m_Version") or not table.ContainsKey(_Data, "m_DataTable")) then
            --重製新資料
            GetDataTable(iClientSaveDataType)
            this.Save(iClientSaveDataType)
            return
        end

        ---取檔時版本轉成數字
        _Data.m_Version = tonumber(_Data.m_Version)

        --有加密的話 m_DataTable 要解密
        if IsFileEncrypt(_DataName) then
            _Data.m_DataTable = AES.Decrypt(_Data.m_DataTable)
        end

        --存檔的 m_DataTable 是字串 所以要轉成 Table
        if type(_Data.m_DataTable) ~= "table" then
            _Data.m_DataTable = JsonMgr.JsonTextToTable(_Data.m_DataTable)
        end

        --把讀到的給到 ClientSaveMgr 內的暫存
        GetDataTable(iClientSaveDataType, _Data)
    end
end

---存檔function
---@param iJsonText string 要存的字串
---@param iPath string 要存的路徑
---@param iIsEncrypt bool 是否要加密
local function SaveText(iJsonText, iPath, iIsEncrypt)
    AES.SaveText(iJsonText, iPath, iIsEncrypt)
end

--endregion

---遊戲開始時要取的 Data 並設定在ClientSaveMgr
---[新增存檔類型需要新增至此]
function ClientSaveMgr.LoadClientFileWhenGameStart()
    -- 寫法：ReadFile(EClientSaveDataType)
    --DeviceData
    ReadFile(EClientSaveDataType.Device)
    ReadFile(EClientSaveDataType.Account)
    ReadFile(EClientSaveDataType.KeyBoardInput)

end

---登入後要取的 Data 並設定在ClientSaveMgr
---[新增存檔類型需要新增至此]
function ClientSaveMgr.LoadClientFileAfterLogin()
    -- 寫法：ReadFile(EClientSaveDataType)
    --AutoDrinkData
    ReadFile(EClientSaveDataType.AutoDrink)
    --MissionTrackData
    ReadFile(EClientSaveDataType.MissionTrack)
    --LoveItemData
    ReadFile(EClientSaveDataType.LoveItem)
    --LoveAreaData
    ReadFile(EClientSaveDataType.LoveArea)
    --SkillSetPage
    ReadFile(EClientSaveDataType.ChatChannel)
    ---PinPrinterProductData
    ReadFile(EClientSaveDataType.PinPrinterProduct)
    ---LastEmojiData
    ReadFile(EClientSaveDataType.LastEmoji)
    ---SavedSentenceData
    ReadFile(EClientSaveDataType.SavedSentence)
    ---LovePetData
    ReadFile(EClientSaveDataType.LovePet)
    ---MallRedData
    ReadFile(EClientSaveDataType.Mall)
    ---MailSaveData
    ReadFile(EClientSaveDataType.Mail)
end

---登出時要做甚麼
---[新增存檔類型需要新增至此]
function ClientSaveMgr.LoggingOutToDo()
    -- 把跟帳號的紀錄清掉
    ClearCurrentSaveTable(EClientSaveDataType.LoveItem)
    ClearCurrentSaveTable(EClientSaveDataType.LoveArea)
    ClearCurrentSaveTable(EClientSaveDataType.AutoDrink)
    ClearCurrentSaveTable(EClientSaveDataType.MissionTrack)
    ClearCurrentSaveTable(EClientSaveDataType.ChatChannel)
    ClearCurrentSaveTable(EClientSaveDataType.PinPrinterProduct)
    ClearCurrentSaveTable(EClientSaveDataType.LastEmoji)
    ClearCurrentSaveTable(EClientSaveDataType.SavedSentence)
    ClearCurrentSaveTable(EClientSaveDataType.LovePet)
    ClearCurrentSaveTable(EClientSaveDataType.Mall)
    ClearCurrentSaveTable(EClientSaveDataType.Mail)
    Mail_Model.SaveMailData()
end

---初始化
function ClientSaveMgr.Init()
    ClientSaveMgr.LoadClientFileWhenGameStart()
end

---改變檔案內指定成員的值 改變值後自動存檔
---@param iClientSaveDataType EClientSaveDataType 要改變值的檔案名稱
---@param iKey string 要改變值的名稱
---@param iValue T 要改變值的值
function ClientSaveMgr.ChangeDataValue(iClientSaveDataType, iKey, iValue)
    --改變資料的值
    rawset(GetDataTable(iClientSaveDataType).m_DataTable, iKey, iValue)

    --存檔
    ClientSaveMgr.Save(iClientSaveDataType)
end

---重設資料 並自動存預設檔
---@param iClientSaveDataType EClientSaveDataType 要重設的檔案名稱
function ClientSaveMgr.ResetData(iClientSaveDataType)
    --檔案名稱
    local _DataName = GetDataName(iClientSaveDataType)
    --共用區域變數
    local _Path = ""
    --取得路徑
    _Path = GetPath(_DataName)
    --刪除檔案 並 建立預設檔
    FilePath.RemoveFileByPath(_DataName, _Path, ReadFile(iClientSaveDataType))
end

---刪除檔案
---@param iClientSaveDataType EClientSaveDataType 要刪除的檔案名稱
function ClientSaveMgr.DeleteData(iClientSaveDataType)
    --檔案名稱
    local _DataName = GetDataName(iClientSaveDataType)
    --共用區域變數
    local _Path = ""
    --取得路徑
    _Path = GetPath(_DataName)
    --刪除檔案
    FilePath.RemoveFileByPath(_DataName, _Path, nil)
end

--region Get相關

---取得存檔 m_DataTable 資料 並設定在 ClientSaveMgr
---單純取檔只需要輸入名稱就好
---@param iClientSaveDataType EClientSaveDataType 要取得的檔案類型
function ClientSaveMgr.GetDataTable(iClientSaveDataType)
    local _DataTable = GetDataTable(iClientSaveDataType).m_DataTable
    return _DataTable
end

---取得檔案內指定成員的值
---@param iClientSaveDataType EClientSaveDataType 要取得值的檔案名稱
---@param iKey string 要取得值的名稱
function ClientSaveMgr.GetDataValue(iClientSaveDataType, iKey)
    --取得檔案資料
    local _DataTable = GetDataTable(iClientSaveDataType).m_DataTable
    return rawget(_DataTable, iKey)
end

--endregion

--region 存檔

---存檔
---@param iClientSaveDataType EClientSaveDataType 要存的檔案類型
function ClientSaveMgr.Save(iClientSaveDataType)
    --檔案名稱
    local _DataName = GetDataName(iClientSaveDataType)
    ---最後存檔的 JsonText
    local _JsonText = ""
    ---轉字串結果
    local _Result = false
    ---存檔路徑
    local _Path = GetPath(_DataName)
    --檢查是否有資料夾路徑
    FilePath.CreateDataFolder(_Path)

    --取得資料
    local _DataTable = GetDataTable(iClientSaveDataType)

    --取得檔案是否加密
    local _IsEncrypt = IsFileEncrypt(_DataName)

    ---實際 Data 資料
    local _DataJsonText = ""

    --存檔時版本轉成字串
    _DataTable.m_Version = tostring(_DataTable.m_Version)

    _Result, _DataJsonText = pcall(json.encode, _DataTable.m_DataTable)

    if(not _Result) then
        D.LogError(_DataJsonText)
        return
    end

    --有要加密就加密
    if _IsEncrypt then
        _DataJsonText = AES.Encrypt(_DataJsonText)
    end

    ---最後要存的結構
    local _SaveData = {}

    _SaveData.m_Version = _DataTable.m_Version
    _SaveData.m_DataTable = _DataJsonText

    --取得所需存檔字串
    _Result, _JsonText = pcall(json.encode, _SaveData)
    if(not _Result) then
        D.LogError(_JsonText)
        return
    end

    --存檔 外層 不需要加密
    SaveText(_JsonText, _Path, false)
end
--endregion

return ClientSaveMgr
