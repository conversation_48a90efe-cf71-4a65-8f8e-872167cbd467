fileFormatVersion: 2
guid: d6125011cae73624db1b902570504e9f
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001 Head
    100002: Bip001 Spine1
    100004: Bone001
    100006: Bone002
    100008: Bone003
    100010: Bone004
    100012: Bone005
    100014: Bone006
    100016: Bone007
    100018: Bone008
    100020: Bone009
    100022: //RootNode
    100024: L anal fin01
    100026: L anal fin02
    100028: L anal fin03
    100030: L anal fin04
    100032: L Dorsal Fin01
    100034: L Dorsal Fin02
    100036: L Dorsal Fin03
    100038: L Dorsal Fin04
    100040: L Dorsal Fin05
    100042: L Dorsal Fin06
    100044: L Dorsal Fin07
    100046: L Dorsal Fin08
    100048: L Dorsal Fin09
    100050: L Dorsal Fin10
    100052: L Dorsal Fin11
    100054: L Dorsal Fin12
    100056: L Dorsal Fin13
    100058: L Dorsal Fin14
    100060: L Dorsal Fin15
    100062: L Dorsal Fin16
    100064: L Dorsal Fin17
    100066: Lhand01
    100068: Lhand02
    100070: Lhand03
    100072: Lhand04
    100074: Lhand05
    100076: mouth
    100078: R anal fin01
    100080: R anal fin02
    100082: R anal fin03
    100084: R anal fin04
    100086: R Dorsal Fin01
    100088: R Dorsal Fin02
    100090: R Dorsal Fin03
    100092: R Dorsal Fin04
    100094: R Dorsal Fin05
    100096: R Dorsal Fin06
    100098: R Dorsal Fin07
    100100: R Dorsal Fin08
    100102: R Dorsal Fin09
    100104: R Dorsal Fin10
    100106: R Dorsal Fin11
    100108: R Dorsal Fin12
    100110: R Dorsal Fin13
    100112: R Dorsal Fin14
    100114: R Dorsal Fin15
    100116: R Dorsal Fin16
    100118: R Dorsal Fin17
    100120: Rhand01
    100122: Rhand02
    100124: Rhand03
    100126: Rhand04
    100128: Rhand05
    100130: Spine02
    100132: Spine03
    100134: Spine04
    100136: Bip001
    100138: Skin
    400000: Bip001 Head
    400002: Bip001 Spine1
    400004: Bone001
    400006: Bone002
    400008: Bone003
    400010: Bone004
    400012: Bone005
    400014: Bone006
    400016: Bone007
    400018: Bone008
    400020: Bone009
    400022: //RootNode
    400024: L anal fin01
    400026: L anal fin02
    400028: L anal fin03
    400030: L anal fin04
    400032: L Dorsal Fin01
    400034: L Dorsal Fin02
    400036: L Dorsal Fin03
    400038: L Dorsal Fin04
    400040: L Dorsal Fin05
    400042: L Dorsal Fin06
    400044: L Dorsal Fin07
    400046: L Dorsal Fin08
    400048: L Dorsal Fin09
    400050: L Dorsal Fin10
    400052: L Dorsal Fin11
    400054: L Dorsal Fin12
    400056: L Dorsal Fin13
    400058: L Dorsal Fin14
    400060: L Dorsal Fin15
    400062: L Dorsal Fin16
    400064: L Dorsal Fin17
    400066: Lhand01
    400068: Lhand02
    400070: Lhand03
    400072: Lhand04
    400074: Lhand05
    400076: mouth
    400078: R anal fin01
    400080: R anal fin02
    400082: R anal fin03
    400084: R anal fin04
    400086: R Dorsal Fin01
    400088: R Dorsal Fin02
    400090: R Dorsal Fin03
    400092: R Dorsal Fin04
    400094: R Dorsal Fin05
    400096: R Dorsal Fin06
    400098: R Dorsal Fin07
    400100: R Dorsal Fin08
    400102: R Dorsal Fin09
    400104: R Dorsal Fin10
    400106: R Dorsal Fin11
    400108: R Dorsal Fin12
    400110: R Dorsal Fin13
    400112: R Dorsal Fin14
    400114: R Dorsal Fin15
    400116: R Dorsal Fin16
    400118: R Dorsal Fin17
    400120: Rhand01
    400122: Rhand02
    400124: Rhand03
    400126: Rhand04
    400128: Rhand05
    400130: Spine02
    400132: Spine03
    400134: Spine04
    400136: Bip001
    400138: Skin
    4300000: Skin
    7400000: Boss_0004_BRun_01
    9500000: //RootNode
    13700000: Skin
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Boss_0004_BRun_01
      takeName: Boss_0004_BRun_01
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: d1d6bbc3ddcf1644891b41543bee060c,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
