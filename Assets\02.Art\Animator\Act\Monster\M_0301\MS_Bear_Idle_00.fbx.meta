fileFormatVersion: 2
guid: 868b8ba67634f4f44a4c2060ba8e814a
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bear_Head_JawEndSHJnt
    100002: Bear_Head_JawSHJnt
    100004: Bear_Head_TopSHJnt
    100006: Bear_l_Clavicle_01_01SHJnt
    100008: Bear_l_Ear_01_01SHJnt
    100010: Bear_l_Ear_01_02SHJnt
    100012: Bear_l_FrontLeg_AnkleSHJnt
    100014: Bear_l_FrontLeg_BallSHJnt
    100016: Bear_l_FrontLeg_HipSHJnt
    100018: Bear_l_FrontLeg_KneeSHJnt
    100020: Bear_l_FrontLeg_ToeSHJnt
    100022: Bear_l_HindLeg_AnkleSHJnt
    100024: Bear_l_HindLeg_BallSHJnt
    100026: Bear_l_HindLeg_HipSHJnt
    100028: Bear_l_HindLeg_KneeSHJnt
    100030: Bear_l_HindLeg_ToeSHJnt
    100032: Bear_<PERSON>INSHJnt
    100034: Bear_Neck_01SHJnt
    100036: Bear_Neck_02SHJnt
    100038: Bear_Neck_TopSHJnt
    100040: Bear_r_Clavicle_01_01SHJnt
    100042: Bear_r_Ear_01_01SHJnt
    100044: Bear_r_Ear_01_02SHJnt
    100046: Bear_r_FrontLeg_AnkleSHJnt
    100048: Bear_r_FrontLeg_BallSHJnt
    100050: Bear_r_FrontLeg_HipSHJnt
    100052: Bear_r_FrontLeg_KneeSHJnt
    100054: Bear_r_FrontLeg_ToeSHJnt
    100056: Bear_r_HindLeg_AnkleSHJnt
    100058: Bear_r_HindLeg_BallSHJnt
    100060: Bear_r_HindLeg_HipSHJnt
    100062: Bear_r_HindLeg_KneeSHJnt
    100064: Bear_r_HindLeg_ToeSHJnt
    100066: Bear_ROOTSHJnt
    100068: Bear_Spine_01SHJnt
    100070: Bear_Spine_02SHJnt
    100072: Bear_Spine_03SHJnt
    100074: Bear_Spine_04SHJnt
    100076: Bear_Spine_TopSHJnt
    100078: Bear_Tail_01_01SHJnt
    100080: Bear_Tail_01_02SHJnt
    100082: //RootNode
    400000: Bear_Head_JawEndSHJnt
    400002: Bear_Head_JawSHJnt
    400004: Bear_Head_TopSHJnt
    400006: Bear_l_Clavicle_01_01SHJnt
    400008: Bear_l_Ear_01_01SHJnt
    400010: Bear_l_Ear_01_02SHJnt
    400012: Bear_l_FrontLeg_AnkleSHJnt
    400014: Bear_l_FrontLeg_BallSHJnt
    400016: Bear_l_FrontLeg_HipSHJnt
    400018: Bear_l_FrontLeg_KneeSHJnt
    400020: Bear_l_FrontLeg_ToeSHJnt
    400022: Bear_l_HindLeg_AnkleSHJnt
    400024: Bear_l_HindLeg_BallSHJnt
    400026: Bear_l_HindLeg_HipSHJnt
    400028: Bear_l_HindLeg_KneeSHJnt
    400030: Bear_l_HindLeg_ToeSHJnt
    400032: Bear_MAINSHJnt
    400034: Bear_Neck_01SHJnt
    400036: Bear_Neck_02SHJnt
    400038: Bear_Neck_TopSHJnt
    400040: Bear_r_Clavicle_01_01SHJnt
    400042: Bear_r_Ear_01_01SHJnt
    400044: Bear_r_Ear_01_02SHJnt
    400046: Bear_r_FrontLeg_AnkleSHJnt
    400048: Bear_r_FrontLeg_BallSHJnt
    400050: Bear_r_FrontLeg_HipSHJnt
    400052: Bear_r_FrontLeg_KneeSHJnt
    400054: Bear_r_FrontLeg_ToeSHJnt
    400056: Bear_r_HindLeg_AnkleSHJnt
    400058: Bear_r_HindLeg_BallSHJnt
    400060: Bear_r_HindLeg_HipSHJnt
    400062: Bear_r_HindLeg_KneeSHJnt
    400064: Bear_r_HindLeg_ToeSHJnt
    400066: Bear_ROOTSHJnt
    400068: Bear_Spine_01SHJnt
    400070: Bear_Spine_02SHJnt
    400072: Bear_Spine_03SHJnt
    400074: Bear_Spine_04SHJnt
    400076: Bear_Spine_TopSHJnt
    400078: Bear_Tail_01_01SHJnt
    400080: Bear_Tail_01_02SHJnt
    400082: //RootNode
    7400000: MS_Bear_Idle_00
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: MS_Bear_Idle_00
      takeName: Take 001
      firstFrame: 1
      lastFrame: 180
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 15d53aab7e632324194bedfcce1c8dab,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
