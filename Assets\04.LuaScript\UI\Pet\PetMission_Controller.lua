---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---請輸入結構描述, 用途
---寵物派遣介面 Controller 控制寵物派遣的，寵物介面下的一個頁面。 有些東西是從活動介面 copy 過來的
---@class PetMission_Controller
---author Chang Wei
---telephone #2892
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2025.02.10
PetMission_Controller = {}
local this = PetMission_Controller

local IMAGE_STRING = "PetDispatchBG_"

--- 派遣條件最大寵物種類數
local MAX_KIND = 3

--- 派遣條件最大星數
local MAX_STAR = 5

--- 一種 Icon 最多數量
local MAX_ICON_NUM = 3

--- 派遣頁面 Icon 會用的 size
local MISSION_ICON_SIZE = 115

--- 全部派遣表單上的 Icon size
local LIST_MISSION_ICON_SIZE = 75

--- 第一個道具
local FIRST_ITEM = 1

--- 左右按鈕移動距離
local MAIN_MOVE_DIS = 447

--- 寵物派遣刷新(免費) CommonQuery
local PET_MISSION_REFRESH_FREE = 511

--- 寵物派遣刷新 CommonQuery
local PET_MISSION_REFRESH = 512

--- 戰力足的數字樣式
local POWER_OK_TYPE = "PO"

--- 戰力不足的數字樣式
local POWER_NOT_OK_TYPE = "R_01"

--- 正常字串樣式
local NORNAL_STRING_TYPE = "W"

--- 派起次數的字串樣式
local MISSION_SEND_STRING_TYPE = "B_01"

--- 刷新次數字串樣式
local MISSION_REFRESH_STRING_TYPE = "W"

--- 戰力足的圖片顏色
local POWER_OK_IMAGE_COLOR = Extension.GetColor("#E9D99D")

--- 戰力不足的圖片顏色
local POWER_NOT_OK_IMAGE_COLOR = Extension.GetColor("#F5898F")

--- 派遣任務上下方遮罩顏色 預設
local DEFAULT_COVER_COLOR = Extension.GetColor("#033758")

--- 派遣任務上下方遮罩顏色 可領取
local GET_REWARD_COVER_COLOR = Extension.GetColor("#8a4f1b")

--- 派遣任務邊框顏色 預設
local DEFAULT_BORDER_COLOR = Extension.GetColor("#6acef2")

--- 派遣任務邊框顏色 可領取
local GET_REWARD_BORDER_COLOR = Extension.GetColor("#f1ce70")

--- 灰色牌
local GREY_COLOR = Extension.GetColor("#5B5B5B")

--- 選擇欲派遣的自律者 字串
local PICK_YOUR_PET = 20323417

--- 沒有符合種類或星等的自律者 字串
local YOU_GOT_NO_PET = 20323418

--- 已達派遣次數上限 字串
local REACH_THE_MAX_DELIVER = 20323425

--- 沒有可領取的獎勵 字串
local NO_REWARD_TO_CLAIM = 20323426

--- 派遣進行中,無法選擇其它自律者 字串
local MISSION_WORKING_CANT_CHOOSE = 20323427

--- 派遣完成 字串
local MISSION_COMPLETE = 20323411

--- 可領取獎勵 字串
local MISSION_CAN_GET_REWARD = 20323412

--- 今日派遣次數 : 字串
local MISSION_SEND_TODAY = 20323401

--- 免費刷新次數： 字串
local MISSION_REFRESH_TODAY = 20323402

--- 寵物種類限制串表轉換結果
local KindLimitedResult = {
    {EPetType.MONSTER},
    {EPetType.HUMAN},
    {EPetType.MYSTIC},
    {EPetType.MONSTER, EPetType.HUMAN},
    {EPetType.MONSTER, EPetType.MYSTIC},
    {EPetType.HUMAN, EPetType.MYSTIC},
    {EPetType.MONSTER, EPetType.HUMAN, EPetType.MYSTIC},
}

--- 派遣卡
local m_Table_MainUnit = {}

--- 圖片載入完成的計數器
local m_LoadCount = 0

--- 活動顯示批次處理
local m_MainVisibleBatch

--- 全部派遣重複道具 pool
local m_Table_MissionUnit = {}

--- 列表排序 每個頁面分開存 好像是企劃要得? 給忘了
---@type number
this.m_PetListArrange = 0

--- 目前點選任務可用寵物種類
---@type table
this.m_NowCanSetPetType = {}

--- 目前點選派遣任務的資料
---@type table
this.m_NowMissionMission = {}

--- 全部派遣內容
this.SendAllMissionPetSet = {}

--- 第一個進入的畫面 &Group_Main 介面的初始化
local function InitialUI()

    -- 選派遣任務
    this.m_Group_Main = this.m_ViewRef.m_Dic_Trans:Get("&Group_Main")
    this.m_ScrollView_Main = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Main"):GetComponent("UIScrollView")
    this.m_ScrollView_Main:SetReverseScroll(true)

    this.m_Button_Main = this.m_ViewRef.m_Dic_Trans:Get("&Button_Main").gameObject
    this.m_GObj_Main = Extension.CreatePrefabObjPool(this.m_Button_Main, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    this.m_Content_Main = this.m_ViewRef.m_Dic_Trans:Get("&Content_Main")
    this.m_Content_Main_Rect = this.m_Content_Main.gameObject:GetComponent("RectTransform")

    this.m_Btn_LeftArrow = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_LeftArrow"))
    this.m_Btn_LeftArrow:AddListener(EventTriggerType.PointerClick, PetMission_Controller.OnClick_LeftArrow)
    this.m_Btn_RightArrow = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_RightArrow"))
    this.m_Btn_RightArrow:AddListener(EventTriggerType.PointerClick, PetMission_Controller.OnClick_RightArrow)

    -- 今日派遣次數
    this.m_SendNumber = this.m_Group_Main.gameObject.transform:Find("Text_TodayDeliver"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 刷新任務 function
    local function _RefreshMission()

        local _iType = 0
        local _CommonQuery = PET_MISSION_REFRESH_FREE
        local _FillString = Pet_Model.GetPetFreeRefreshNumber()
        local _IconString = TextData.Get(20102035)

        -- 看一下寵物還有沒有免費刷新
        if(Pet_Model.GetPetFreeRefreshNumber() <= 0) then

            _iType = 1
            _CommonQuery = PET_MISSION_REFRESH
            _FillString = ""
            _IconString = ""

        end

        local _Type2Data_V8 = CommonQueryMgr.GetNewCommonQueryData()

        ---給icon模式 使用的icnoBox 填資料範例
        _Type2Data_V8:BuildIconBoxTable(EIconBoxDataType.Icon,{95011},{_IconString},{false},false)

        CommonQueryMgr.AddNewInform(_CommonQuery,{},{_FillString}, function() SendProtocol_020._021_04(_iType) end,nil,
        nil,nil,nil,nil,_Type2Data_V8)

    end

    -- 今天刷新次數按鈕
    this.m_RefreshButton = this.m_Group_Main.gameObject.transform:Find("Image_TodayDeliverLeft")
    Button.AddListener(this.m_RefreshButton, EventTriggerType.PointerClick,_RefreshMission)

    -- 裡面的按鈕
    this.m_RefreshButtonBtn = this.m_Group_Main.gameObject.transform:Find("Image_TodayDeliverLeft/Button")
    Button.AddListener(this.m_RefreshButtonBtn, EventTriggerType.PointerClick,_RefreshMission)

    -- 免費要顯示的刷新
    this.m_RefreshFree = this.m_Group_Main.gameObject.transform:Find("Image_TodayDeliverLeft/Text_TodayDeliverLeft")

    -- 付費的刷新
    this.m_RefreshPay = this.m_Group_Main.gameObject.transform:Find("Image_TodayDeliverLeft/Text_NoMoreFree")

    -- 刷新次數文字
    this.m_RefreshNumber = this.m_Group_Main.gameObject.transform:Find("Image_TodayDeliverLeft/Text_TodayDeliverLeft"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 全部派遣
    this.m_DeliverSendAll = this.m_ViewRef.m_Dic_Trans:Get("&Button_SendAll")
    Button.AddListener(this.m_DeliverSendAll, EventTriggerType.PointerClick, PetMission_Controller.DeliverSendAllPet)

    --全部領取
    this.m_DeliverGetAll =  this.m_ViewRef.m_Dic_Trans:Get("&Button_CollectAll")
    Button.AddListener(this.m_DeliverGetAll, EventTriggerType.PointerClick, PetMission_Controller.DeliverCollectAllReward)

end

--- 稍微分開 這是點選下去後的頁面上需要的東西 &Panel_MissionInfo 的初始化
local function InitialUI2()

    -- 派遣內容頁面
    this.m_GroupMissionInfo = this.m_ViewRef.m_Dic_Trans:Get("&Panel_MissionInfo")

    -- 派遣內容頁面的任務牌
    this.m_MissionUnit = this.m_GroupMissionInfo.gameObject.transform:Find("MissionInfo_Main")

    -- 派遣任務寵物種類限制
    this.m_PetKindLimit = {}
    this.m_PetKindLimit.Panel = this.m_MissionUnit.gameObject.transform:Find("Panel_PetKind")
    this.m_PetKindLimit.Image = {}
    for i = 1, MAX_KIND do

        this.m_PetKindLimit.Image[i] = this.m_PetKindLimit.Panel.gameObject.transform:Find("Image_Kind_".. i)

    end

    -- 派遣任務名稱
    this.m_MissionTitle = this.m_MissionUnit.gameObject.transform:Find("Text_Title"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 派遣任務背景圖片
    this.m_MissionBG_TypePic = this.m_MissionUnit.gameObject.transform:Find("Image_BG/Image_TypePic"):GetComponent("RawImage")

    -- 派遣任務星星
    this.m_PetNeedStar = {}
    for i = 1, MAX_STAR do

        this.m_PetNeedStar[i] = this.m_MissionUnit.gameObject.transform:Find("Panel_MissionStar/Star_".. i):GetComponent(typeof(Image))

    end

    -- 任務所需時間
    this.m_Panel_MissionEstimateTime = this.m_MissionUnit.gameObject.transform:Find("Panel_EstimateTime")
    this.m_Text_MissionEstimateTime = this.m_Panel_MissionEstimateTime.gameObject.transform:Find("Text_EstimateTime_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 任務倒數時間
    this.m_Panel_MissionWorking = this.m_MissionUnit.gameObject.transform:Find("Panel_WorkingShow")
    this.m_Text_MissionWorkingTime = this.m_Panel_MissionWorking.gameObject.transform:Find("Panel_Working_Text/Text_TimeLeft_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 返回列表功能
    local function _FBackToList()
        -- 開啟派遣任務選擇
        this.m_Group_Main.gameObject:SetActive(true)

        -- 關閉任務詳細說明
        this.m_GroupMissionInfo.gameObject:SetActive(false)

        -- 關閉寵物列表
        PetPage_Controller.OpenSelectList(false)
    end

    -- 招回派遣按鈕
    this.m_Btn_CallBack = this.m_MissionUnit.gameObject.transform:Find("Button_CallBack")
    Button.AddListener(this.m_Btn_CallBack, EventTriggerType.PointerClick, function()

        -- 存下可能會被收回的寵物列表
        Pet_Model.AddBackMission(this.m_NowMissionMission.m_ServerData.m_ID)

        SendProtocol_020._021_03(this.m_NowMissionMission.m_ServerData.m_ID)

        _FBackToList()

    end)

    -- 右邊的內容物平台
    this.m_RightPanel = this.m_GroupMissionInfo.gameObject.transform:Find("Image_Back")

    -- 派遣任務說明
    this.m_MissionMissionDetail = this.m_RightPanel.gameObject.transform:Find("Text_Detail"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 派遣獎勵
    this.m_Reward = {}
    for i = 1, MAX_ICON_NUM do

        -- 獎勵道具
        this.m_Reward[i] = {}
        this.m_Reward[i].m_IconParent = this.m_RightPanel.gameObject.transform:Find("Panel_RewardIcons/Image_RewardIconFrame_"..i)
        this.m_Reward[i].m_ItemIcon = IconMgr.NewItemIcon(0, this.m_Reward[i].m_IconParent.gameObject.transform, MISSION_ICON_SIZE)
        this.m_Reward[i].m_ItemIcon:SetClickTwice(false)

        -- 獎勵格所需戰力值
        this.m_Reward[i].m_PowerNeedImage = this.m_RightPanel.gameObject.transform:Find("Image_Item"..i.."_RequirePower"):GetComponent(typeof(Image))
        this.m_Reward[i].m_PowerNeedText = this.m_Reward[i].m_PowerNeedImage.gameObject.transform:Find("Text_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    end

    -- 派遣派出寵物
    this.m_PetSelect = {}
    for i = 1, MAX_ICON_NUM do

        -- 選擇寵物 Icon
        this.m_PetSelect[i] = {}
        this.m_PetSelect[i].m_IconParent = this.m_RightPanel.gameObject.transform:Find("Panel_PetIcons/Image_PetIconFrame_"..i)
        this.m_PetSelect[i].m_ButtonX = this.m_PetSelect[i].m_IconParent.gameObject.transform:Find("ButtonPet_X")
        Button.AddListener(this.m_PetSelect[i].m_ButtonX, EventTriggerType.PointerClick, function()
            -- 關閉寵物 Icon 上的 X 按鈕
            this.m_PetSelect[i].m_ButtonX.gameObject:SetActive(false)

            -- 刷新清空選中的寵物 Icon
            this.m_PetSelect[i].m_PetIcon:RefreshIcon(0)

            -- 刷新寵物列表
            PetList_Controller.DeleteSelectedItem(this.m_PetSelect[i].m_PetData.m_SID)
            PetList_Controller.Refresh()

            -- 刷新總和戰鬥力
            PetMission_Controller.RefreshTotalPower()

            -- 更新獎勵 Icon
            PetMission_Controller.UpdateReward()
        end)

        this.m_PetSelect[i].m_PetIcon = IconMgr.NewPetIcon(0, this.m_PetSelect[i].m_IconParent.gameObject.transform, MISSION_ICON_SIZE)
        this.m_PetSelect[i].m_PetData = {}

    end

    -- 寵物加總戰力值
    this.m_PetTotalPowerText = this.m_RightPanel.gameObject.transform:Find("Image_TotalPowerPoint/Text_Number"):GetComponent(typeof(TMPro.TextMeshProUGUI))

    -- 返回列表按鈕
    this.m_GoBackToList = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_BackList")
    Button.AddListener(this.m_GoBackToList, EventTriggerType.PointerClick, _FBackToList)

    -- 送出派遣按鈕
    this.m_SendPetsMission = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_SendPet")
    Button.AddListener(this.m_SendPetsMission, EventTriggerType.PointerClick, function()

        -- 做要送資料的 table
        local _SendTable = {}
        _SendTable.m_ID = this.m_NowMissionMission.m_ServerData.m_ID
        _SendTable.m_Pets = {}
        for _k, _v in pairs(this.m_PetSelect) do

            if(next(_v.m_PetData)) then
                _SendTable.m_Pets[_k] = {}
                _SendTable.m_Pets[_k].m_Slot = _v.m_PetData.m_SID
                _SendTable.m_Pets[_k].m_ID = _v.m_PetData.m_ID

                -- 加到準備送出列表中
                Pet_Model.AddSendPetList(_v.m_PetData.m_SID)
            end

        end

        if(Pet_Model.GetPetNowDeliverNumber() - Pet_Model.GetPetMissionOnGoingCount() == 0) then

            MessageMgr.AddCenterMsg(true, TextData.Get(REACH_THE_MAX_DELIVER))

        else

            SendProtocol_020._021_02({_SendTable})

        end

        _FBackToList()

    end)
end

--- 這是一鍵全部派遣的 UI 會用到的初始化 &Img_SendAllPetBG 的初始化
local function InitialUI3()

    -- 彈出全部派遣小視窗
    this.m_MissionWindow = {}

    -- 全部派遣的底
    this.m_MissionWindow.m_SendAllPetBG = this.m_ViewRef.m_Dic_Trans:Get("&Img_SendAllPetBG")

    -- 全部派遣的 Winodw
    this.m_MissionWindow.m_SendAllPetWindow = this.m_MissionWindow.m_SendAllPetBG.gameObject.transform:Find("Window_SendAllPet")

    -- 即將派遣列表
    this.m_MissionWindow.m_ScrollView_Mission = this.m_MissionWindow.m_SendAllPetWindow.gameObject.transform:Find("ScrollView_Item"):GetComponent("UIScrollView")
    this.m_MissionWindow.m_ScrollView_Mission:SetReverseScroll(true)

    -- 放列表內容物
    this.m_MissionWindow.m_Content_Mission = this.m_MissionWindow.m_SendAllPetWindow.gameObject.transform:Find("ScrollView_Item/ContentArea")

    -- ScrollView 重複使用原件
    this.m_MissionWindow.m_RepeatItem = this.m_ViewRef.m_Dic_Trans:Get("&Image_MissionName")

    -- 道具池子
    this.m_MissionWindow.m_ItemPool = Extension.CreatePrefabObjPool(this.m_MissionWindow.m_RepeatItem.gameObject, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

    local function _CloseSendAllPetWindow()

        this.m_MissionWindow.m_SendAllPetBG.gameObject:SetActive(false)

    end

    -- 取消按鈕
    this.m_MissionWindow.m_CancelBtn = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_CancelSendAll")
    Button.AddListener(this.m_MissionWindow.m_CancelBtn, EventTriggerType.PointerClick, _CloseSendAllPetWindow)

    -- 確認按鈕
    this.m_MissionWindow.m_ConfirmBtn = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_ConfirmSendAll")
    Button.AddListener(this.m_MissionWindow.m_ConfirmBtn, EventTriggerType.PointerClick, function()

        if(Pet_Model.GetPetNowDeliverNumber() - Pet_Model.GetPetMissionOnGoingCount() == 0) then
            MessageMgr.AddCenterMsg(true, TextData.Get(REACH_THE_MAX_DELIVER))
        end

        for k, v in pairs(this.SendAllMissionPetSet) do

            -- 做要送資料的 table
            local _SendTable = {}
            _SendTable.m_ID = v.Mission.m_ServerData.m_ID
            _SendTable.m_Pets = {}
            for _k, _v in pairs(v.m_Pets) do

                _SendTable.m_Pets[_k] = {}
                _SendTable.m_Pets[_k].m_Slot = _v.m_PetSlot
                _SendTable.m_Pets[_k].m_ID = _v.m_PetID

                -- 加到準備送出列表中
                Pet_Model.AddSendPetList(_v.m_PetSlot)

            end

            SendProtocol_020._021_02({_SendTable})

        end

        _CloseSendAllPetWindow()

    end)

    -- X 按鈕
    this.m_MissionWindow.m_XBtn = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_CloseSendAllMission")
    Button.AddListener(this.m_MissionWindow.m_XBtn, EventTriggerType.PointerClick, _CloseSendAllPetWindow)

end

--- 設定任務道具資料
---@param table iMissionData 要派遣的任務資料 包括要派遣的寵物
function PetMission_Controller.SetMissionItemData(iMissionData)

    -- 先全部設 false 後面再看要開幾個
    for _k, _v in pairs(m_Table_MissionUnit) do
        _v.gameObject:SetActive(false)
    end

    for _k, _data in pairs(iMissionData) do
        local _Unit
        local _MainUnit = {}

        -- 看看有沒有東西 有就直接拉來用
        if(m_Table_MissionUnit[_k]) then
            _MainUnit = m_Table_MissionUnit[_k]
        else
            -- 從池生個新的
            _Unit = this.m_MissionWindow.m_ItemPool:Get()
            _MainUnit.gameObject = _Unit

            -- 找任務名
            _MainUnit.Text_MissionTitle = _Unit.gameObject.transform:Find("Text_MissionName"):GetComponent(typeof(TMPro.TextMeshProUGUI))

            -- 寵物 Icon
            _MainUnit.m_PetIconGroup = {}

            -- 找出要生寵物 Icon 的地方 生出寵物 Icon
            for i = 1, MAX_ICON_NUM do
                local _Parent = _Unit.gameObject.transform:Find("Text_MissionName/PetMissionItem_Row/Parent_"..i)
                _MainUnit.m_PetIconGroup[i] = {}
                _MainUnit.m_PetIconGroup[i].Parent = _Parent
                _MainUnit.m_PetIconGroup[i].Icon = IconMgr.NewPetIcon(0, _Parent.gameObject.transform, LIST_MISSION_ICON_SIZE)
                _MainUnit.m_PetIconGroup[i].Icon.gameObject.transform:SetSiblingIndex(0)
                _MainUnit.m_PetIconGroup[i].Type = _Parent.gameObject.transform:Find("Image_PetTypeFrame/Image_PetTypeIcon"):GetComponent(typeof(Image))
            end

            -- 設定這傢伙出生點
            _MainUnit.gameObject.transform:SetParent(this.m_MissionWindow.m_Content_Mission.transform)
            _MainUnit.gameObject.transform.localScale = Vector3.one

        end

        -- 東西準備好了 可以塞資料了
        -- 任務名稱
        _MainUnit.Text_MissionTitle.text = TextData.Get(_data.Mission.m_BasicData.m_TitleStringID)

        -- 要派遣的寵物 和寵物種類
        for i = 1, MAX_ICON_NUM do

            -- 看有沒有寵物資料
            if(_data.m_Pets[i]) then

                _MainUnit.m_PetIconGroup[i].Icon:RefreshIcon(_data.m_Pets[i])
                local _TypeStr = Pet_Model.GetStatusImageString(_data.m_Pets[i].m_BasicData.m_PetType)
                SpriteMgr.Load( _TypeStr, _MainUnit.m_PetIconGroup[i].Type)

                _MainUnit.m_PetIconGroup[i].Parent.gameObject:SetActive(true)

            else

                -- 沒有要把整個格子關掉
                _MainUnit.m_PetIconGroup[i].Parent.gameObject:SetActive(false)

            end
        end

        m_Table_MissionUnit[_k] = _MainUnit

        m_Table_MissionUnit[_k].gameObject:SetActive(true)
    end

end

---初始化
function PetMission_Controller.Init(iController)

    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    -- 進去派遣任務第一頁會用到的 Init
    InitialUI()

    -- 第二頁的 Init
    InitialUI2()

    -- 全部派遣跳出的小視窗
    InitialUI3()

end

---Update
function PetMission_Controller.Update()

    -- 這邊估計要做更新派遣任務的時間
    local _MissionData = PetMgr.GetPetMissionData()
    for _k, _v in pairs(m_Table_MainUnit) do

        if(_v.Text_Time) then
            _v.Text_Time.text = GString.GetTimeFormat(GString.ETimeSpanType.HMS_HourNoPad, _MissionData[_k].m_ServerData.m_TimeLeft)
        end

    end

    if(next(this.m_NowMissionMission) and this.m_NowMissionMission.m_ServerData.m_State == EMissionMissionState.Working)then
        this.m_Text_MissionWorkingTime.text = GString.GetTimeFormat(GString.ETimeSpanType.HMS_HourNoPad, this.m_NowMissionMission.m_ServerData.m_TimeLeft)
    end

end

--- 開啟介面
function PetMission_Controller.Open(iParam)

    -- 開啟派遣任務選擇
    this.m_Group_Main.gameObject:SetActive(true)

    -- 關閉任務詳細說明
    this.m_GroupMissionInfo.gameObject:SetActive(false)

    -- 設定寵物派遣次數顯示
    PetMission_Controller.RefreshMissionSendNumber()

    -- 設定刷新派遣顯示
    PetMission_Controller.RefreshMissionRefreshNumber()

    -- 註冊更新
	PlayerData_Flags[EFlags.StaticNum].SetNotify(Pet_Model.GetPetEveryDayMissionFlag() ,PetMission_Controller.RefreshSendNumber)
    PlayerData_Flags[EFlags.StaticNum].SetNotify(Pet_Model.GetPetRefreshMissionFlag() ,PetMission_Controller.RefreshMissionRefreshNumber)

    PetMission_Controller.SetScrollView_Main()

    -- 寵物數量關閉
    PetList_Controller.SetExtraValue(false)

    return true
end

function PetMission_Controller.OnDestroy()

    m_Table_MainUnit = {}

    m_Table_MissionUnit = {}
    return true
end

function PetMission_Controller.Close()

    PlayerData_Flags[EFlags.StaticNum].RemoveNotify(Pet_Model.GetPetEveryDayMissionFlag())
    PlayerData_Flags[EFlags.StaticNum].RemoveNotify(Pet_Model.GetPetRefreshMissionFlag())

end

--- 刷新任務派錢次數
function PetMission_Controller.RefreshMissionSendNumber()

    local _LeftNumber = Pet_Model.GetPetNowDeliverNumber()

    -- 沒有剩餘次數了
    if(_LeftNumber == 0) then

        -- 設定寵物派遣次數顯示
        this.m_SendNumber.text = TextData.Get(MISSION_SEND_TODAY) .. GString.StringWithStyle(TextData.Get(REACH_THE_MAX_DELIVER), MISSION_SEND_STRING_TYPE)

    else

        -- 設定寵物派遣次數顯示
        this.m_SendNumber.text = TextData.Get(MISSION_SEND_TODAY) .. GString.StringWithStyle(Pet_Model.GetPetNowDeliverNumber().."/"..Pet_Model.GetPetDeliverMaxNumber(), MISSION_SEND_STRING_TYPE)

    end

end

--- 刷新任務刷新次數
function PetMission_Controller.RefreshMissionRefreshNumber()

    local _LeftNumber = Pet_Model.GetPetFreeRefreshNumber()

    -- 沒有剩餘次數了
    if(_LeftNumber == 0) then

        -- 免費的要關
        this.m_RefreshFree.gameObject:SetActive(false)

        -- 付費的要打開
        this.m_RefreshPay.gameObject:SetActive(true)

    else

        -- 免費的要打開
        this.m_RefreshFree.gameObject:SetActive(true)

        -- 付費的要關
        this.m_RefreshPay.gameObject:SetActive(false)

        -- 設定刷新派遣顯示
        this.m_RefreshNumber.text = TextData.Get(MISSION_REFRESH_TODAY) .. Pet_Model.GetPetFreeRefreshNumber().."/"..Pet_Model.GetPetFreeRefreshMaxNumber()

    end

end

--- 點選左移動
function PetMission_Controller.OnClick_LeftArrow()
    local _pos = this.m_Content_Main_Rect.anchoredPosition
    local _moveDic = _pos.x > 0 and MAIN_MOVE_DIS or -MAIN_MOVE_DIS
    local _count = math.abs(math.ceil(_pos.x / _moveDic))
    if _pos.x < 0 then
        _pos.x = (_count - 1) * -MAIN_MOVE_DIS
        this.m_Content_Main_Rect.anchoredPosition = _pos
    end
end

--- 點選右移動
function PetMission_Controller.OnClick_RightArrow()
    local _pos = this.m_Content_Main_Rect.anchoredPosition
    local _moveDic = _pos.x > 0 and MAIN_MOVE_DIS or -MAIN_MOVE_DIS
    local _count = math.abs(math.floor(_pos.x / _moveDic))
    _pos.x = -((_count + 1) * MAIN_MOVE_DIS)
    if _pos.x >= -((table.Count(PetMgr.GetPetMissionData()) - 4) * MAIN_MOVE_DIS) then
        this.m_Content_Main_Rect.anchoredPosition = _pos
    end
end

--- 刷新總和戰鬥力
function PetMission_Controller.RefreshTotalPower()
    local _TotalPower = 0
    this.m_PetTotalPowerText.text = 0
    for i = 1, MAX_ICON_NUM do

        if(this.m_PetSelect[i].m_PetIcon.m_Idx ~= 0 and PetList_Controller.CheckIsThisInTheList(this.m_PetSelect[i].m_PetData.m_SID)) then
            _TotalPower = _TotalPower + this.m_PetSelect[i].m_PetData.m_Number
        end

    end
    this.m_PetTotalPowerText.text = _TotalPower
end

--- 寵物點選
---@param table iData 寵物資料
function PetMission_Controller.PetListClick(iData)

    -- 找一下空 Icon
    for i = 1, MAX_ICON_NUM do

        if (this.m_PetSelect[i].m_PetIcon.m_Idx == 0 and not PetList_Controller.CheckIsThisInTheList(iData.m_SID) and this.m_NowMissionMission.m_BasicData.m_PetNumberLimit >= i) then
            -- 選擇寵物 Icon
            this.m_PetSelect[i].m_ButtonX.gameObject:SetActive(true)
            this.m_PetSelect[i].m_PetIcon:RefreshIcon(iData.m_ID)
            this.m_PetSelect[i].m_PetData = iData
            -- 設定選擇到表中
            PetList_Controller.SetSelectedItem(iData.m_SID, iData.m_ID)
            break
        end

    end

    -- 更新總戰力
    PetMission_Controller.RefreshTotalPower()

    -- 更新獎勵 Icon
    PetMission_Controller.UpdateReward()

end

--- 設定派遣任務内容
---@param bool iMoveEffect 要不要移動特效
function PetMission_Controller.SetScrollView_Main(iMoveEffect)

    -- 要不要跑移動的特效 一般狀態下是要跑的
    local _IsMoveEffect = true
    if(iMoveEffect ~= nil) then
        _IsMoveEffect = iMoveEffect
    end

    local _MissionData = PetMgr.GetPetMissionData()

    -- 要有動作才關他 不然不理
    if(_IsMoveEffect) then
        for k, v in pairs(m_Table_MainUnit) do
            v.gameObject:SetActive(false)
        end
    end

    for k, _data in pairs(_MissionData) do
        local _Unit
        local _MainUnit = {}

        if m_Table_MainUnit[k] then
            _MainUnit = m_Table_MainUnit[k]
        else
            _Unit = this.m_GObj_Main:Get()
            _MainUnit.gameObject = _Unit
            _MainUnit.Image_BG = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_BG")
            _MainUnit.Image_TypePic = _MainUnit.Image_BG.transform:Find("Image_TypePic"):GetComponent("RawImage")

            _MainUnit.Text_Title = _Unit.gameObject.transform:Find("EffectFrame_Main/Text_Title"):GetComponent(typeof(TMPro.TextMeshProUGUI))
            _MainUnit.Text_Factor = _Unit.gameObject.transform:Find("EffectFrame_Main/Text_Factor"):GetComponent(typeof(TMPro.TextMeshProUGUI))

            _MainUnit.MainButton = Button.New(_Unit.gameObject)
            _MainUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
                this.m_ClickIdx = k
                local _GetMissionData = PetMgr.GetPetMissionData()
                PetMission_Controller.OnClick_Main(_GetMissionData[k], _MainUnit.Image_TypePic.texture)
            end)

            _MainUnit.gameObject.transform:SetParent(this.m_Content_Main.transform)
            _MainUnit.gameObject.transform.localScale = Vector3.one

            local _ChangeColorComponent = {}
            _ChangeColorComponent[1] = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_Frame"):GetComponent(typeof(Image))
            _ChangeColorComponent[2] = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_TextBG_TOP"):GetComponent(typeof(Image))
            _ChangeColorComponent[3] = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_TextBG_BOT"):GetComponent(typeof(Image))

            _MainUnit.ChangeColorComponent = _ChangeColorComponent

            -- 派遣寵物種類條件 種類限制 view 上的擺放是照 1:獸型 2:人形 3:神秘 來排序的 初始化
            _MainUnit.Kind = {}
            local _KindParent = _MainUnit.gameObject.transform:Find("EffectFrame_Main/Panel_PetKind")
            for i = 1, MAX_KIND do

                _MainUnit.Kind[i] = _KindParent.gameObject.transform:Find("Image_Kind_".. i):GetComponent(typeof(Image))
                _MainUnit.Kind[i].gameObject:SetActive(false)

            end

            -- 派遣條件星數初始化
            _MainUnit.Stars = {}
            local _StarParent = _MainUnit.gameObject.transform:Find("EffectFrame_Main/Panel_MissionStar")
            for i = 1, MAX_STAR do

                _MainUnit.Stars[i] = _StarParent.gameObject.transform:Find("Star_".. i):GetComponent(typeof(Image))

            end

            -- 寵物派遣中才需要顯示的面板
            _MainUnit.Panel_Working = _MainUnit.gameObject.transform:Find("EffectFrame_Main/Panel_WorkingShow")

            -- 寵物派遣剩餘時間
            _MainUnit.Text_Time = _MainUnit.Panel_Working.gameObject.transform:Find("Panel_Working_Text/Text_TimeLeftNumber"):GetComponent(typeof(TMPro.TextMeshProUGUI))

            -- 寵物招回按鈕
            _MainUnit.Btn_CallBack = _Unit.gameObject.transform:Find("Button_CallBack")
            Button.AddListener(_MainUnit.Btn_CallBack, EventTriggerType.PointerClick, function()

                local _GetMissionData = PetMgr.GetPetMissionData()

                -- 存下可能會被收回的寵物列表
                Pet_Model.AddBackMission(_GetMissionData[k].m_ServerData.m_ID)

                -- 送出收回協定
                SendProtocol_020._021_03(_GetMissionData[k].m_ServerData.m_ID)

            end)

            -- 派遣結束的顯示面板
            _MainUnit.Panel_CollectReward = _MainUnit.gameObject.transform:Find("EffectFrame_Main/Panel_CollectRewardShow")

            -- 把內容的字覆蓋上去
            _MainUnit.Panel_CollectReward.gameObject.transform:Find("Text_CollectReward"):GetComponent(typeof(TMPro.TextMeshProUGUI)).text = TextData.Get(MISSION_COMPLETE).."\n"..TextData.Get(MISSION_CAN_GET_REWARD)

            -- 派起任務獎勵回收
            _MainUnit.Btn_CollectReward = _Unit.gameObject.transform:Find("Button_GetReward")
            Button.AddListener(_MainUnit.Btn_CollectReward, EventTriggerType.PointerClick, function()

                local _GetMissionData = PetMgr.GetPetMissionData()

                -- 存下可能會被收回的寵物列表
                for _k, _v in pairs(_GetMissionData[k].m_ServerData.m_Pets) do

                    if(_v ~= 0) then
                        Pet_Model.AddSendPetList(_v)
                    end

                end

                -- 送出收回協定
                SendProtocol_020._021_05({_GetMissionData[k].m_ServerData.m_ID})

            end)

            -- 主要獎勵道具
            local _IconParent = _MainUnit.gameObject.transform:Find("EffectFrame_Main/Icon_Parent")
            _MainUnit.MainItemIcon = IconMgr.NewItemIcon(0, _IconParent.gameObject.transform, MISSION_ICON_SIZE)
            _MainUnit.MainItemIcon:SetClickTwice(false)

        end

        local _ImageString = IMAGE_STRING .. GFunction.Zero_stuffing(_data.m_BasicData.m_FileName, 3)
        TextureMgr.Load(_ImageString, true, function(iTex)
            if iTex ~= nil then
                _MainUnit.Image_TypePic.texture = iTex
                _MainUnit.Image_BG.gameObject:SetActive(true)
                ---圖都 Load 完才跑動態效果 要不要跑
                if(_IsMoveEffect) then
                    PetMission_Controller.MainContentEffect()
                end
            else
                _MainUnit.Image_BG.gameObject:SetActive(false)
            end
        end)

        for i = 1, MAX_KIND do

            _MainUnit.Kind[i].gameObject:SetActive(false)

        end

        -- 派遣寵物種類條件顯示
        local _WhatKindIsOk = KindLimitedResult[_data.m_BasicData.m_PetKindRequire]
        for _k, _v in pairs(_WhatKindIsOk) do

            _MainUnit.Kind[_v].gameObject:SetActive(true)

        end

        -- 派遣條件星數設定
        for i = 1, MAX_STAR do

            if(i > _data.m_BasicData.m_PetLevelRequire) then
                _MainUnit.Stars[i].color = Extension.GetColor(PetStarColor.STAR_DARK)
            else
                _MainUnit.Stars[i].color = Extension.GetColor(PetStarColor.STAR_LIGHT)
            end

        end

        -- 派遣任務名稱
        _MainUnit.Text_Title.text = TextData.Get(_data.m_BasicData.m_TitleStringID)

        -- 需不需要顯示派遣中面板
        if(_data.m_ServerData.m_State == EMissionMissionState.Working) then
            _MainUnit.Panel_Working.gameObject:SetActive(true)
            _MainUnit.Btn_CallBack.gameObject:SetActive(true)
        else
            _MainUnit.Panel_Working.gameObject:SetActive(false)
            _MainUnit.Btn_CallBack.gameObject:SetActive(false)
        end

        -- 需不需要顯示可收集面板
        if(_data.m_ServerData.m_State == EMissionMissionState.CollectAble) then

            -- 按鈕和文字顯示
            _MainUnit.Panel_CollectReward.gameObject:SetActive(true)
            _MainUnit.Btn_CollectReward.gameObject:SetActive(true)

            -- 把該換色的換一換
            for _k, _v in pairs(_MainUnit.ChangeColorComponent) do

                -- 第一個顏色要下不同
                if(_k == 1) then
                    _v.color = GET_REWARD_BORDER_COLOR
                else
                    _v.color = GET_REWARD_COVER_COLOR
                end

            end


        else

            -- 按鈕和文字關閉
            _MainUnit.Panel_CollectReward.gameObject:SetActive(false)
            _MainUnit.Btn_CollectReward.gameObject:SetActive(false)

            -- 把該換色的換一換
            for _k, _v in pairs(_MainUnit.ChangeColorComponent) do

                -- 第一個顏色要下不同
                if(_k == 1) then
                    _v.color = DEFAULT_BORDER_COLOR
                else
                    _v.color = DEFAULT_COVER_COLOR
                end

            end

        end

        -- 道具出現率
        local _TreasureData = NPCTreasureData.GetTreasureDataByIdx(_data.m_BasicData.m_BasicItemID)
        local _SettingData = Pet_Model.CalculateRewardCount(_TreasureData)

        -- 刷新道具 基本道具取第一個就好
        _MainUnit.MainItemIcon:RefreshIcon(_TreasureData.m_TreasureData[1].m_ItemID)

        if type(_SettingData.m_Count) == "string" and string.match(_SettingData.m_Count, "%d") then
            _MainUnit.MainItemIcon:SetCount(_SettingData.m_Count, "WO_01")
        else
            _MainUnit.MainItemIcon:SetCount(_SettingData.m_Count)
        end

        m_Table_MainUnit[k] = _MainUnit
    end
end

function PetMission_Controller.MainContentEffect()
    m_LoadCount = m_LoadCount + 1
    ---如果圖都 Load 完才跑動態效果
    if m_LoadCount == #PetMgr.GetPetMissionData() then
        m_MainVisibleBatch = UIVisibleEffect.AddUIVisibleBatch(m_Table_MainUnit, 0.1)
        m_MainVisibleBatch:Start(0)
        m_LoadCount = 0
        local _Frequency = 0.8 + 0.1 * #PetMgr.GetPetMissionData()
        HEMTimeMgr.DoFunctionDelay(_Frequency, function()
            this.m_ScrollView_Main.enabled = true
        end)
    end
end

--- 設定點擊任務後資料到詳細說明面板上
---@param table iData 寵物派遣資料
---@param texture iTex 圖片直接帶入就好
function PetMission_Controller.SetMissionClickToPanel(iData, iTex)

    -- 設寵物限制種類
    local _WhatKindIsOk = KindLimitedResult[iData.m_BasicData.m_PetKindRequire]
    this.m_NowCanSetPetType = _WhatKindIsOk
    for i = 1, MAX_KIND do

        this.m_PetKindLimit.Image[i].gameObject:SetActive(false)

    end
    for _k, _v in pairs(_WhatKindIsOk) do

        this.m_PetKindLimit.Image[_v].gameObject:SetActive(true)

    end

    -- 設定派遣任務名稱
    this.m_MissionTitle.text = TextData.Get(iData.m_BasicData.m_TitleStringID)

    -- 設定任務圖片
    this.m_MissionBG_TypePic.texture = iTex

    -- 設定任務寵物星數需求
    for i = 1, MAX_STAR do

        if(i > iData.m_BasicData.m_PetLevelRequire) then
            this.m_PetNeedStar[i].color = Extension.GetColor(PetStarColor.STAR_DARK)
        else
            this.m_PetNeedStar[i].color = Extension.GetColor(PetStarColor.STAR_LIGHT)
        end

    end

    -- 派遣任務所需時間
    -- 做一下分鐘轉秒
    local _MinutesToSecond = iData.m_BasicData.m_NeedTime * 60
    this.m_Text_MissionEstimateTime.text = GString.GetTimeFormat(GString.ETimeSpanType.HMS_HourNoPad, _MinutesToSecond)

    -- 派遣任務說明
    this.m_MissionMissionDetail.text = TextData.Get(iData.m_BasicData.m_DetailStringID)

    -- 設定派遣獎勵
    for i = 1, MAX_ICON_NUM do

        -- 第一個道具
        if(i == FIRST_ITEM) then

            -- 獎勵道具 Icon
            local _TreasureData = NPCTreasureData.GetTreasureDataByIdx(iData.m_BasicData.m_BasicItemID)
            local _SettingData = Pet_Model.CalculateRewardCount(_TreasureData)
            -- 刷新道具 基本道具取第一個就好
            this.m_Reward[i].m_ItemIcon:RefreshIcon(_TreasureData.m_TreasureData[1].m_ItemID)

            if type(_SettingData.m_Count) == "string" and string.match(_SettingData.m_Count, "%d") then
                this.m_Reward[i].m_ItemIcon:SetCount(_SettingData.m_Count, "WO_01")
            else
                this.m_Reward[i].m_ItemIcon:SetCount(_SettingData.m_Count)
            end

        else

            -- 獎勵道具 Icon
            local _TreasureData = NPCTreasureData.GetTreasureDataByIdx(iData.m_BasicData.m_ExtraDrops[i - 1].m_ItemID)
            local _SettingData = Pet_Model.CalculateRewardCount(_TreasureData)
            this.m_Reward[i].m_ItemIcon:RefreshIcon(_TreasureData.m_TreasureData[1].m_ItemID)

            if type(_SettingData.m_Count) == "string" and string.match(_SettingData.m_Count, "%d") then
                this.m_Reward[i].m_ItemIcon:SetCount(_SettingData.m_Count, "WO_01")
            else
                this.m_Reward[i].m_ItemIcon:SetCount(_SettingData.m_Count)
            end

            -- 獎勵格所需戰力值
            this.m_Reward[i].m_PowerNeedImage.color = POWER_NOT_OK_IMAGE_COLOR
            this.m_Reward[i].m_PowerNeedText.text = GString.StringWithStyle(iData.m_BasicData.m_ExtraDrops[i - 1].m_PowerRequire, POWER_NOT_OK_TYPE)

        end

        -- 寵物派遣 X 按鈕全都關閉
        this.m_PetSelect[i].m_ButtonX.gameObject:SetActive(false)

        -- 設定派遣寵物
        this.m_PetSelect[i].m_PetIcon:RefreshIcon(0)
        if(iData.m_BasicData.m_PetNumberLimit >= i) then
            this.m_PetSelect[i].m_IconParent.gameObject:SetActive(true)
        else
            this.m_PetSelect[i].m_IconParent.gameObject:SetActive(false)
        end

        -- 清除寵物選項
        PetList_Controller.DeleteSelectedItem(this.m_PetSelect[i].m_PetData.m_SID)

        -- 清空寵物資料
        this.m_PetSelect[i].m_PetData = {}

    end

    -- 總戰力清 0
    this.m_PetTotalPowerText.text = 0

    -- 存下現在派遣任務的資料
    this.m_NowMissionMission = {}
    this.m_NowMissionMission = iData

    -- MissionMissionState 的狀態
    if(iData.m_ServerData.m_State == EMissionMissionState.Working) then

        -- 我的寵物資料
        local _MyPetData = PetMgr.GetPlayerPetData()

        -- 任務預估時間 關閉
        this.m_Panel_MissionEstimateTime.gameObject:SetActive(false)

        -- 任務倒數時間 開啟
        this.m_Panel_MissionWorking.gameObject:SetActive(true)

        -- 寵物招回按鈕
        this.m_Btn_CallBack.gameObject:SetActive(true)

        -- 寵物派遣按鈕要關掉
        this.m_SendPetsMission.gameObject:SetActive(false)

        -- 找一下派遣出去的寵物 把他們加到格内做顯示
        for i = 1, MAX_ICON_NUM do

            -- 選擇寵物 Icon
            if(_MyPetData[iData.m_ServerData.m_Pets[i]]) then
                this.m_PetSelect[i].m_ButtonX.gameObject:SetActive(false)
                this.m_PetSelect[i].m_PetIcon:RefreshIcon(_MyPetData[iData.m_ServerData.m_Pets[i]].m_PetID)
                -- 創建 table
                local _Table = PetList_Controller.CreateEmptyListDataTable()
                _Table.m_ID = _MyPetData[iData.m_ServerData.m_Pets[i]].m_PetID
                _Table.m_Name = _MyPetData[iData.m_ServerData.m_Pets[i]].m_PetName
                _Table.m_Type = _MyPetData[iData.m_ServerData.m_Pets[i]].m_BasicData.m_PetType
                _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_MyPetData[iData.m_ServerData.m_Pets[i]].m_BasicData.m_Pet2DIcon, 6)
                _Table.m_Star = _MyPetData[iData.m_ServerData.m_Pets[i]].m_StarLv
                _Table.m_IsShowLikePet = false
                _Table.m_IsPetLoved = _MyPetData[iData.m_ServerData.m_Pets[i]].m_IsPetLoved
                _Table.m_IsShowNumber = false
                _Table.m_Number = _MyPetData[iData.m_ServerData.m_Pets[i]].m_Power
                _Table.m_DataType = EPetDataType.PET
                _Table.m_SID = _MyPetData[iData.m_ServerData.m_Pets[i]].m_PetSlot
                this.m_PetSelect[i].m_PetData = _Table
                -- 設定選擇到表中
                PetList_Controller.SetSelectedItem(iData.m_ServerData.m_Pets[i], _MyPetData[iData.m_ServerData.m_Pets[i]].m_PetID)
            end

        end

        -- 開遮罩
        PetList_Controller.SetPetListCover(true)

        -- 更新總戰力
        PetMission_Controller.RefreshTotalPower()

        -- 更新獎勵 Icon
        PetMission_Controller.UpdateReward()

    else

        -- 任務預估時間關閉
        this.m_Panel_MissionEstimateTime.gameObject:SetActive(true)

        -- 任務倒數時間
        this.m_Panel_MissionWorking.gameObject:SetActive(false)

        -- 寵物招回按鈕
        this.m_Btn_CallBack.gameObject:SetActive(false)

        -- 寵物派遣按鈕
        this.m_SendPetsMission.gameObject:SetActive(true)

        -- 關遮罩
        PetList_Controller.SetPetListCover(false)

    end

end

--- 更新派遣獎勵圖和字
function PetMission_Controller.UpdateReward()

    -- 設定派遣獎勵
    for i = 2, MAX_ICON_NUM do

        -- 這邊驗證夠不夠戰力 因為只有分 夠或不夠所以不直接 if 分開兩個判定就好
        if(tonumber(this.m_PetTotalPowerText.text) >= this.m_NowMissionMission.m_BasicData.m_ExtraDrops[i - 1].m_PowerRequire) then

            -- 獎勵格所需戰力值
            this.m_Reward[i].m_PowerNeedImage.color = POWER_OK_IMAGE_COLOR
            this.m_Reward[i].m_PowerNeedText.text = GString.StringWithStyle(this.m_NowMissionMission.m_BasicData.m_ExtraDrops[i - 1].m_PowerRequire, POWER_OK_TYPE)

        else

            -- 獎勵格所需戰力值
            this.m_Reward[i].m_PowerNeedImage.color = POWER_NOT_OK_IMAGE_COLOR
            this.m_Reward[i].m_PowerNeedText.text = GString.StringWithStyle(this.m_NowMissionMission.m_BasicData.m_ExtraDrops[i - 1].m_PowerRequire, POWER_NOT_OK_TYPE)

        end
    end

end

--- 寵物派遣按鈕點下
---@param table iData 寵物派遣資料
---@param texture iTex 順便給圖 這樣就不用再 load 了
function PetMission_Controller.OnClick_Main(iData, iTex)

    -- 如果是可領取 就不讓點進去了
    if(iData.m_ServerData.m_State == EMissionMissionState.CollectAble) then
        local _GetMissionData = PetMgr.GetPetMissionData()

        -- 存下可能會被收回的寵物列表
        for _k, _v in pairs(_GetMissionData[this.m_ClickIdx].m_ServerData.m_Pets) do

            if(_v ~= 0) then
                Pet_Model.AddSendPetList(_v)
            end

        end

        -- 送出收回協定
        SendProtocol_020._021_05({_GetMissionData[this.m_ClickIdx].m_ServerData.m_ID})
        return
    end

    -- 關閉任務選擇顯示
    this.m_Group_Main.gameObject:SetActive(false)

    -- 填入資料
    PetMission_Controller.SetMissionClickToPanel(iData, iTex)

    -- 開啟任務詳細說明顯示
    this.m_GroupMissionInfo.gameObject:SetActive(true)

    -- 打開寵物列表
    PetPage_Controller.OpenSelectList(true)

    -- 選擇使用第一個選項
    PetList_Controller.SetPanelOption1On(true)

    -- 刷新寵物列表
    PetMission_Controller.Option1Click(this.m_PetListArrange)

    -- 下拉視窗的文字
    PetList_Controller.SetDropDownListText(this.m_PetListArrange)

end

--- 派遣列表被點選
function PetMission_Controller.Option1Click(iNumber)

    -- 整理完基本排序的
    local _MyPetData = Pet_Model.ArrangePetListOrder(iNumber)

    -- 沒東西就 return
    if(table.IsNullOrEmpty(_MyPetData)) then
        PetList_Controller.SetShowData({})
        return nil
    end

    -- 招喚中寵物 SID
    local _SummonedPet = PetMgr.GetSummonedPetPlot()
    local _OtherPets = {}
    for _key, _value in pairs(_MyPetData) do

        -- 跳過招喚的還要跳過派遣
        if(_value.m_PetSlot ~= _SummonedPet and _value.m_PetState == EMissionMissionState.Free) then

            table.insert(_OtherPets, _value)

        end

    end

    local function _CheckType(iType)

        for _k, _v in pairs(this.m_NowCanSetPetType) do

            if(_v == iType) then
                return true
            end

        end

        return false

    end

    -- 清空 _MyPetData
    _MyPetData = {}
    local _YesKindPet = {}
    _MyPetData = _OtherPets

    -- 要對一下寵物類型對不對
    for _key, _value in pairs(_MyPetData) do

        if(_CheckType(_value.m_BasicData.m_PetType) and _value.m_StarLv >= this.m_NowMissionMission.m_BasicData.m_PetLevelRequire) then
            table.insert(_YesKindPet, _value)
        end

    end

    -- 再換
    _MyPetData = {}
    _MyPetData = _YesKindPet

    -- 設定寵物列表要用的資料
    local _ShowData = {}
    for _k, _v in pairs(_MyPetData) do

        -- 創建 table
        local _Table = PetList_Controller.CreateEmptyListDataTable()
        _Table.m_ID = _v.m_PetID
        _Table.m_Name = _v.m_PetName
        _Table.m_Type = _v.m_BasicData.m_PetType
        _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_v.m_BasicData.m_Pet2DIcon, 6)
        _Table.m_Star = _v.m_StarLv
        _Table.m_IsShowLikePet = false
        _Table.m_IsPetLoved = _v.m_IsPetLoved
        _Table.m_IsShowNumber = false
        _Table.m_Number = _v.m_Power
        _Table.m_DataType = EPetDataType.PET
        _Table.m_SID = _v.m_PetSlot

        table.insert(_ShowData, _Table)
    end

    -- 設定列表中要出現的資料
    PetList_Controller.SetShowData(_ShowData)

    -- 看有沒有寵物在列表中決定選擇顯示的字串
    if (next(_ShowData)) then
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(PICK_YOUR_PET), NORNAL_STRING_TYPE))
    else
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(YOU_GOT_NO_PET), NORNAL_STRING_TYPE))
    end

    -- 要再檢查一個 如果是已經派遣出去的任務的字串
    if (this.m_NowMissionMission.m_ServerData.m_State == EMissionMissionState.Working) then
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(MISSION_WORKING_CANT_CHOOSE), NORNAL_STRING_TYPE))
    end

end

--- 把所有寵物送出去可以派遣的任務
function PetMission_Controller.DeliverSendAllPet()

    -- 以戰力來排序後面會優先以戰力來派遣
    local _PetsListData = Pet_Model.ArrangePetListOrder(EPetArrange[1].Power, false)

    -- 招喚中寵物 SID
    local _SummonedPet = PetMgr.GetSummonedPetPlot()
    local _OtherPets = {}
    for _key, _value in pairs(_PetsListData) do

        -- 跳過招喚的還要跳過派遣
        if(_value.m_PetSlot ~= _SummonedPet and _value.m_PetState == EMissionMissionState.Free) then

            table.insert(_OtherPets, _value)

        end

    end

    -- 裝一裝寵物派遣組合
    local _PetMissionSet = {}

    --- 給一組派遣寵物
    ---@param number iNeedPower 需要的戰力
    ---@param table iKind 能派遣的種類
    ---@param number iPetNumber 可以送的寵物最大數
    ---@param number iPetStar 寵物星力需求
    ---@return table _SendPets 派遣的寵物
    local function _OneDeliverMission(iNeedPower, iKind, iPetNumber, iPetStar)

        local _SendPets = {}    -- 這任務要派遣出去的寵物
        local _NowPetsPower = 0 -- 目前組合戰力
        local _OnlyPetsAllow = iPetNumber
        local _Count = 0    -- 算一算數量
        local _RemoveKey = {}    -- 要移除 key 的

        for _k, _v in pairs(_OtherPets) do

            -- 檢查看看能不能 break 了
            if(_NowPetsPower >= iNeedPower or _Count >= _OnlyPetsAllow) then

                break

            end

            for _key, _value in pairs(iKind) do

                -- 要找到符合的寵物種類
                if(_v.m_BasicData.m_PetType == _value and _v.m_PetState == EMissionMissionState.Free and _v.m_StarLv >= iPetStar) then

                    table.insert(_SendPets, _v)
                    table.insert(_RemoveKey, _k)
                    _NowPetsPower = _NowPetsPower + _v.m_Power
                    _Count = _Count + 1

                end

            end

        end

        -- 反向移除已選擇的寵物
        for i = #_RemoveKey, 1, -1 do
            local _k = _RemoveKey[i]
            table.remove(_OtherPets, _k)  -- 使用 table.remove 移除指定索引
        end

        return _SendPets

    end

    -- 取所有派遣資料
    local _MissionCanDoCount = 0
    local _MissionData = PetMgr.GetPetMissionData()
    for _k, _v in pairs(_MissionData) do
        if(_v.m_ServerData.m_State == EMissionMissionState.Free and _MissionCanDoCount + Pet_Model.GetPetMissionOnGoingCount() < Pet_Model.GetPetNowDeliverNumber()) then
            local _GetSet = {}
            _GetSet = _OneDeliverMission(_v.m_BasicData.m_ExtraDrops[2].m_PowerRequire, KindLimitedResult[_v.m_BasicData.m_PetKindRequire], _v.m_BasicData.m_PetNumberLimit, _v.m_BasicData.m_PetLevelRequire)

            if(next(_GetSet)) then
                local _MissionSet = {}
                _MissionSet.Mission = _v
                _MissionSet.m_Pets = _GetSet
                table.insert(_PetMissionSet, _MissionSet)
                _MissionCanDoCount = _MissionCanDoCount + 1
            end
        end
    end

    -- 可能沒有符合條件的寵物 嗎?

    this.SendAllMissionPetSet = _PetMissionSet

    if(Pet_Model.GetPetNowDeliverNumber() - Pet_Model.GetPetMissionOnGoingCount() <= 0) then

        MessageMgr.AddCenterMsg(true, TextData.Get(REACH_THE_MAX_DELIVER))

    else

        PetMission_Controller.SetMissionItemData(_PetMissionSet)

        -- 檢查 _PetMissionSet 是不是空的
        if(next(_PetMissionSet)) then
            this.m_MissionWindow.m_SendAllPetBG.gameObject:SetActive(true)
        else
            MessageMgr.AddCenterMsg(true, TextData.Get(YOU_GOT_NO_PET))
        end

    end

end

--- 回收全部獎勵
function PetMission_Controller.DeliverCollectAllReward()

    -- 取所有派遣資料
    local _MissionData = PetMgr.GetPetMissionData()
    local _CollectMissionList = {}

    for _k, _v in pairs(_MissionData) do

        -- 找那些狀態是可回收的
        if(_v.m_ServerData.m_State == EMissionMissionState.CollectAble) then

            -- 存下要回收的獎勵編號
            table.insert(_CollectMissionList, _v.m_ServerData.m_ID)

            -- 存下可能會被收回的寵物列表
            for _k, _v in pairs(_v.m_ServerData.m_Pets) do

                if(_v ~= 0) then

                    Pet_Model.AddSendPetList(_v)

                end

            end

        end

    end

    if(next(_CollectMissionList)) then

        -- 送出收回協定
        SendProtocol_020._021_05(_CollectMissionList)

    else

        MessageMgr.AddCenterMsg(true, TextData.Get(NO_REWARD_TO_CLAIM))

    end

end

--- 收到協定後要刷新的
function PetMission_Controller.RefreshAfterProtocol(iParam)

    -- 1.寵物派遣任務資料 收到寵物資料不想要刷了
    if(iParam.Kind == 1) then

        PetMission_Controller.SetScrollView_Main(false)

    -- 2.派遣寵物結果(1)[0.成功 其它失敗]
    elseif(iParam.Kind == 2) then

        -- 取要送出去派遣的寵物
        local _SendPetList = Pet_Model.GetSendPetList()

        -- 查一下有沒有東西 只要有東西就一定要執行這邊
        if(next(_SendPetList)) then

            -- 有沒有成功
            if(iParam.Result == 0) then
                -- 查 table 下面的寵物 SID
                for _k, _v in pairs(_SendPetList) do

                    -- 設那些寵物到工作狀態
                    PetMgr.PetChangeState(_v, EMissionMissionState.Working)

                end
            end

            -- 從要派送寵物列表中移除
            for _k in pairs(_SendPetList) do
                _SendPetList[_k] = nil  -- 將每個 key 設為 nil，真正清空原表
            end

        end

    -- 3.召回某寵物結果(1)[0.成功 1.此任務編號未派遣]
    elseif(iParam.Kind == 3) then

        -- 主要改寵物狀態 和派遣任務狀態
        local _BackTable = Pet_Model.GetBackMissionList()
        if(next(_BackTable)) then

            -- 看看有沒有成功
            if(iParam.Result == 0) then

                for _k, _v in pairs(m_Table_MainUnit) do

                    -- 按鈕和文字關閉
                    --_v.Panel_CollectReward.gameObject:SetActive(false)

                end

                for _k, _v in pairs(_BackTable) do
                    PetMgr.PetBackFromMission(_v)
                end

            end

            -- 從要派送寵物列表中移除
            table.remove(_BackTable, 1)

        end

    else

        PetMission_Controller.Open(0)

    end

end

--- 換收集狀態
---@param number iKey 那個版要換
---@param EMissionMissionState iState 哪種狀態
function PetMission_Controller.ChangeState(iKey, iEMissionMissionState)

    -- 有東西才搞(可能還沒有跑過)
    if(m_Table_MainUnit[iKey]) then

        local _MainUnit = m_Table_MainUnit[iKey]

        -- 需不需要顯示派遣中面板
        if(iEMissionMissionState == EMissionMissionState.Working) then
            _MainUnit.Panel_Working.gameObject:SetActive(true)
            _MainUnit.Btn_CallBack.gameObject:SetActive(true)
        else
            _MainUnit.Panel_Working.gameObject:SetActive(false)
            _MainUnit.Btn_CallBack.gameObject:SetActive(false)
        end

        -- 需不需要顯示可收集面板
        if(iEMissionMissionState == EMissionMissionState.CollectAble) then

            _MainUnit.Panel_CollectReward.gameObject:SetActive(true)
            _MainUnit.Btn_CollectReward.gameObject:SetActive(true)

            -- 把該換色的換一換
            for _k, _v in pairs(_MainUnit.ChangeColorComponent) do

                -- 第一個顏色要下不同
                if(_k == 1) then
                    _v.color = GET_REWARD_BORDER_COLOR
                else
                    _v.color = GET_REWARD_COVER_COLOR
                end

            end

        else

            _MainUnit.Panel_CollectReward.gameObject:SetActive(false)
            _MainUnit.Btn_CollectReward.gameObject:SetActive(false)

            -- 把該換色的換一換
            for _k, _v in pairs(_MainUnit.ChangeColorComponent) do

                -- 第一個顏色要下不同
                if(_k == 1) then
                    _v.color = DEFAULT_BORDER_COLOR
                else
                    _v.color = DEFAULT_COVER_COLOR
                end

            end

        end

    end

end
