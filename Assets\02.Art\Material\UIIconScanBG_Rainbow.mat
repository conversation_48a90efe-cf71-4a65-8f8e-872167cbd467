%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: UIIconScanBG_Rainbow
  m_Shader: {fileID: 4800000, guid: 359b966fb16aa0f48a7b606f30132a78, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - Use Shake Glitch
  - _PulseGlitch_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Angle: 225
    - _BlockGlitch: 0
    - _Block_Frequency: 3
    - _Block_Intensity: 2
    - _Block_Size: 7
    - _BumpScale: 1
    - _CRT_Intensity: 2
    - _CRT_RandomValue: 0
    - _Cutoff: 0.5
    - _Density: 0.05
    - _DetailNormalMapScale: 1
    - _DissoveGlitch: 1
    - _DstBlend: 0
    - _EdgeSoftness: 1
    - _FlashIntensity: 0
    - _FlashTimeScale: 0
    - _Frequency: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Intensity: 1
    - _JitterGlitch: 0
    - _Jitter_Intensity: 0.01
    - _Jitter_RandomValue: 0
    - _Jitter_Threshold: 0.02
    - _LightAngle: 0
    - _LightIntensity: 0.45
    - _LightWidth: 0.5
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _PulseGlitch: 1
    - _PulsePhase: 2.21608
    - _PulsePosScale: 0.015
    - _PulseTexOffsetScale: -0.02
    - _PulseTimeScale: 2
    - _RGBGlitch: 0
    - _RandomValue: 0
    - _ScanSoftness: 1
    - _ShakeGlitch: 0
    - _Shake_Intensity: 0
    - _Shake_XRandomValue: 0.5
    - _Shake_YRandomValue: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _UVSec: 0
    - _UseShakeGlitch: 1
    - _ZWrite: 1
    - _intensity: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorEnd: {r: 0.12549019, g: 0.858878, b: 1, a: 0.8627451}
    - _ColorStart: {r: 1, g: 0.25, b: 0.75735307, a: 0.8627451}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _LightColor: {r: 1, g: 1, b: 1, a: 1}
    - _ScanColor: {r: 1, g: 0.9669811, b: 0.9009434, a: 0.78431374}
  m_BuildTextureStacks: []
