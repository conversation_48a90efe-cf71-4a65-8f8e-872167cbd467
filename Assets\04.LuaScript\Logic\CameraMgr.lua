---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---主攝影機控制 (含後處理)
---@class CameraMgr
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2022.5.18

CameraMgr = {}
CameraMgr.__index = CameraMgr

--region 變數
local m_MainCamera_Transform

---@type UnityEngine.Rendering.PostProcessing.PostProcessLayer
local m_PostProcessLayer

---@type UnityEngine.Rendering.PostProcessing.PostProcessVolume
local m_PostProcessVolume

---@type UnityEngine.Rendering.PostProcessing.PostProcessProfile
local m_PostProcessProfile_Default

---需 Unload 的濾鏡名稱
---@type String
local m_Str_PPPName

---@type UnityEngine.Camera 主攝影機
local m_MainCamera

---@type Transform 
---給玩家用的攝影機補燈光
local m_CameraLight

--	若之後要調燈光位置可以用
-- ---@type Transform 
-- ---給玩家用的攝影機補燈光Parent
-- local m_CameraLightRoot

---@type CinemachineFreeLook 活動中的Cinemachine
local m_ActiveCinemachine

---@type number 攝影機縮放比率，越大就要滾越久
local ZOOM_RATIO = 20
---縮放比例 0~1
---@type float 攝影機距離
local m_SetDistance = 1
---目前縮放比例
---@type float
local m_NowDistance = 1
---正在縮放時又跑縮放，其值會比上次縮放更多
local ZOOM_ADD_ON_ZOOMING = 2
---正在縮放時又跑縮放，縮放速度將遞增
local m_ZoomingSpeedCounter = 1
---正在縮放時又跑縮放，縮放速度遞增速度
local ZOOM_SPEED_ON_ZOOMING = 0.01

---每五次再打射線算是否隱藏樹木
local LATEUPDATE_FREQUENCY = 5
---每次LateUpdate就加1
local m_LateUpdateCount = 0
---受隱藏的物件
local m_BeSealObject = {}
---抓材質內容的東西
local m_MaterialPropertyBlock

---現在相機是否全白
local m_IsWhite = false

local m_WhiteStartTime


--region 攝影機存檔資料
--- 攝影機存檔資料
---@type table
local m_CameraData
--endregion

---@type Transform
local m_HUDCamera_Transform

---@type Camera
local m_HUDCamera

---@type ScanEffect
local m_ScanEffect

local m_CloneMaterial_MapScan = nil

local m_GizmosDebug
--endregion

---取得對應的視野距離
local function GetCameraFarPlaneValue()
    if ProjectMgr.IsPC() then
		return SystemSetting.m_PCFarPlane[m_CameraData.m_FarClipPlane]
	else
		return SystemSetting.m_DeviceFarPlane[m_CameraData.m_FarClipPlane]
	end
end

---初始化
function CameraMgr.Init()
	
	CameraMgr.LoadData()

	m_MainCamera_Transform = GameObject.Find("MainCamera").transform
	m_MainCamera_Transform.gameObject.layer = Layer.PlayerSelf
	m_ScanEffect = m_MainCamera_Transform.gameObject:GetComponent(typeof(ScanEffect))
	-- 複製一個 Material 出來 才不會改到本體
	if m_ScanEffect and m_ScanEffect.m_Material ~= nil then
		m_CloneMaterial_MapScan = Material.Instantiate(m_ScanEffect.m_Material)
		m_ScanEffect.m_Material = m_CloneMaterial_MapScan
	end

	m_GizmosDebug = m_MainCamera_Transform.gameObject:GetComponent(typeof(GizmosDebug))
	m_HUDCamera_Transform = GameObject.Find("HUDCamera").transform

	if ProjectMgr.IsDebug then
    	CameraMgr.SetGizmosLine(BattleMgr.m_IsShowSkillArea.Client or BattleMgr.m_IsShowSkillArea.Server)
	end

	--region PostProcess
	m_PostProcessLayer = Extension.AddMissingComponent(
			m_MainCamera_Transform.gameObject,
		typeof(UnityEngine.Rendering.PostProcessing.PostProcessLayer)
	)
	--m_PostProcessLayer.enabled = true
	m_PostProcessLayer.volumeLayer = 2 ^ Layer.PlayerSelf

	CameraMgr.SetPostProcessActive()

	m_PostProcessVolume = Extension.AddMissingComponent(
			m_MainCamera_Transform.gameObject,
		typeof(UnityEngine.Rendering.PostProcessing.PostProcessVolume)
	)
	m_PostProcessVolume.isGlobal = true

	CameraMgr.LoadPostProcessProfile("PostProcessingProfile_Default", CameraMgr.SetDefaultPostProcess)

	--同場景轉場使用的水波紋特效 add by 侑薰 20241113
	CameraMgr.m_WaterWaveEffect = m_MainCamera_Transform.gameObject:GetComponent(typeof(WaterWaveEffect))
	--endregion

	--region Camera setting
	m_MainCamera = Extension.AddMissingComponent(m_MainCamera_Transform.gameObject, typeof(UnityEngine.Camera))

	m_CameraLight = GameObject.Find("CameraLight").transform
	if SettingMgr.m_DeviceLevel < EDeviceLevel.High and m_CameraLight then
		GameObject.Destroy(m_CameraLight.gameObject)
		m_CameraLight = nil
	end

	-- 這個只會排除渲染，但物件如果是 Active 還是會繼續刷新
	local _CullDistance = {}
	for i = 1, 32 do
		_CullDistance[i] = 0
	end
	_CullDistance[Layer.Boss +1] = 80
	_CullDistance[Layer.SceneSmallObjects +1] = 40
	--_CullDistance[Layer.NPC +1] = 30
	--_CullDistance[Layer.Player +1] = 30
	--_CullDistance[Layer.RTMInvisible +1] = 30
	Extension.SetCullDistance(_CullDistance)
	m_MainCamera.layerCullSpherical = true
	m_MainCamera.depthTextureMode = UnityEngine.DepthTextureMode.Depth
	--endregion

	m_HUDCamera = Extension.AddMissingComponent(m_HUDCamera_Transform.gameObject, typeof(UnityEngine.Camera))

	m_MaterialPropertyBlock = MaterialPropertyBlock.New()

	--region 障礙物淡出
	---障礙物 Layer
	CameraMgr.m_ObstacleLayerMask = (2 ^ Layer.SceneObstacle + 2 ^ Layer.SceneObstacle_IgnoreCameraCollider) % (2 ^ 32) -- 限制到 32 位整數範圍
	if CameraMgr.m_ObstacleLayerMask >= 2 ^ 31 then -- 大於 31 位整數要轉為負數
		CameraMgr.m_ObstacleLayerMask = CameraMgr.m_ObstacleLayerMask - 2 ^ 32 -- 轉換到有符號整數範圍
	end
	--endregion
end

---Tween期間設定樹木的淡出
local function CullTweenAction(iValue, iRenderer)
	for i = 0, iRenderer.Length - 1 do
		local _Renderer = iRenderer[i]
		if not Extension.IsUnityObjectNull(_Renderer) then
			if iValue < 1 then
				--2550 讓消失的物件透一點
				_Renderer.material.renderQueue = 2550

			else
				--2002 讓物件回到原本的值
				_Renderer.material.renderQueue = 2002
			end

			local _MaterialPropertyBlock = m_MaterialPropertyBlock
			_Renderer:GetPropertyBlock(_MaterialPropertyBlock)
			_MaterialPropertyBlock:SetFloat(FadeEffectThreshold, iValue)
			_Renderer:SetPropertyBlock(_MaterialPropertyBlock)
	
			if iValue < 0.1 then
				_Renderer.shadowCastingMode = EShadowCastingMode.ShadowsOnly
			else
				_Renderer.shadowCastingMode = EShadowCastingMode.On
			end	
		end
	end

end

function CameraMgr.LateUpdate()

	--用來計算目前相機距離的地方
	if m_NowDistance ~= m_SetDistance then
		local _ZoomSpeed = ZOOM_SPEED_ON_ZOOMING * m_ZoomingSpeedCounter
		if m_NowDistance > m_SetDistance then
			m_NowDistance = m_NowDistance - _ZoomSpeed
		else
			m_NowDistance = m_NowDistance + _ZoomSpeed
		end

		if math.abs(m_NowDistance - m_SetDistance) < _ZoomSpeed then
			m_NowDistance = m_SetDistance
		end
		CMMgr:SetRigOrbit(m_NowDistance)
	else
		m_ZoomingSpeedCounter = 1

	end

	--用來計算鏡頭遮蔽的地方
	m_LateUpdateCount = m_LateUpdateCount + 1

	if m_LateUpdateCount > LATEUPDATE_FREQUENCY then
		--CameraMgr.DrawLine(m_MainCamera_Transform.position, m_MainCamera_Transform.position + m_MainCamera_Transform.forward)
		local _Vec = Vector3.New(0, 0, 0)
		Vector3.Copy(_Vec, m_MainCamera_Transform.forward)
		local _Back = _Vec * -1
		local _RayCastStart = m_MainCamera_Transform.position + (_Vec * EffectSetting.m_CullTreeDistance)
		local _AllHits = Physics.RaycastAll(_RayCastStart, _Back, EffectSetting.m_CullTreeDistance, CameraMgr.m_ObstacleLayerMask)
		local _Ay_Hits = {}

		for i = 0, _AllHits.Length - 1 do
			_Ay_Hits[i + 1] = _AllHits[i]
		end

		local _NewSeal = {}
		if #_Ay_Hits > 0 then
			for i = 1, #_Ay_Hits do
				if _Ay_Hits[i].collider.gameObject.tag == "ObstacleMask" then
					local _Parent = _Ay_Hits[i].collider.gameObject.transform.parent.gameObject
					local _InstanceID = _Parent:GetInstanceID()
					if _NewSeal[_InstanceID] == nil then
						_NewSeal[_InstanceID] = _Parent
						if m_BeSealObject[_InstanceID] then
							m_BeSealObject[_InstanceID] = nil
						else
							local _MeshRender = _Parent:GetComponentsInChildren(typeof(UnityEngine.MeshRenderer))
							LeanTween.value(_Parent, System.Action_float(function(iValue) CullTweenAction(iValue, _MeshRender) end), 1, 0.06, 0.5)

						end
					end
				end
			end
		end
		for k,v in pairs(m_BeSealObject) do
			if not Extension.IsUnityObjectNull(v) then
				local _MeshRender = v:GetComponentsInChildren(typeof(UnityEngine.MeshRenderer))
				LeanTween.value(v,System.Action_float(function(iValue) CullTweenAction(iValue, _MeshRender) end), 0.06, 1, 0.5)
			end
		end
		m_BeSealObject = _NewSeal

		m_LateUpdateCount = 0
	end

	---相機補光計算
	if RoleMgr.GetPlayerRC() and m_CameraLight then
		local _PlayerPos = Vector3.GetTransformPosition(nil, RoleMgr.GetPlayerRC().transform)
		local _CamEulerAngle = GFunction.GetTransformLocalEulerAngles(m_MainCamera_Transform)
		--反過來
		--_CamEulerAngle.x = 0
		_CamEulerAngle.y = (_CamEulerAngle.y + 180) % 360
		local _CulRotation = Quaternion.Euler(0, _CamEulerAngle.y, 0 )

		m_CameraLight.transform.position = _PlayerPos + (_CulRotation * EffectSetting.m_PlayerSpotLightPos)
		--m_CameraLightRoot.transform.localEulerAngles = _CamEulerAngle
		--用完清掉
		setmetatable(_CulRotation, nil)
	end

	---變白區間
	if m_WhiteStartTime then
		local _Duration = HEMTimeMgr.time - m_WhiteStartTime

		if m_IsWhite then
			if _Duration < EffectSetting.m_CameraWhiteOutDuration then
				CMMgr:SetWhitePlaneAlpha(Mathf.Lerp(0, 1, _Duration / EffectSetting.m_CameraWhiteOutDuration))
			else
				CMMgr:SetWhitePlaneAlpha(1)
				if _Duration > EffectSetting.m_CameraAllWhiteDuration + EffectSetting.m_CameraWhiteOutDuration then
					CameraMgr.DoWhiteOut(false)
				end
			end
		else
			if _Duration < EffectSetting.m_CameraWhiteOutDuration then
				CMMgr:SetWhitePlaneAlpha(Mathf.Lerp(1, 0, _Duration / EffectSetting.m_CameraWhiteOutDuration))
			else
				CMMgr:SetWhitePlaneAlpha(0)
				m_WhiteStartTime = nil
			end
		end
	end
end

function CameraMgr.GetMainCamera()
	return m_MainCamera
end

function CameraMgr.GetMainCameraState()
	return CMMgr:GetMainCameraState()
end

function CameraMgr.GetHUDCamera()
	return m_HUDCamera
end

--region 攝影機後處理相關
local _SwitchCase_PostProcess =
{
	["Bloom"] = function(iSet) iSet.active = true end,
	["ColorGrading"] = function(iSet) iSet.active = true end,
	["Vignette"] = function(iSet) iSet.active = true end
}

function CameraMgr.SetDefaultPostProcess(iAsset)
	m_PostProcessProfile_Default = iAsset
end

---讀取後處理檔案 檔名:"PostProcessProfile_{0}"
---@param iName string
function CameraMgr.LoadPostProcessProfile(iName, iCallback)
	if m_Str_PPPName and m_Str_PPPName ~= iName then
		ResourceMgr.Unload(m_Str_PPPName)
	end
	ResourceMgr.Load(iName, function(iAsset)
		m_PostProcessVolume.profile = iAsset

		if m_PostProcessVolume.profile ~= nil then
			for i = 0, (m_PostProcessVolume.profile.settings.Count-1) do
				local _SwitchCase_Setting = _SwitchCase_PostProcess[m_PostProcessVolume.profile.settings[i].name]
				if _SwitchCase_Setting then
					_SwitchCase_Setting(m_PostProcessVolume.profile.settings[i])
				else
					m_PostProcessVolume.profile.settings[i].active = not( ProjectMgr.IsiOS() or ProjectMgr.IsAndroid() )
				end
			end
		end

		iCallback(iAsset)
		m_Str_PPPName = iName
	end)
end

function CameraMgr.TriggerScanEffect(iIsTrigger, iVect)
	m_ScanEffect:TriggerScan(iIsTrigger, iVect)
end

---開關繪製線段功能
function CameraMgr.SetGizmosLine(iIsOn)
	m_GizmosDebug:SetDrawLine(iIsOn)
end

---取得目前的相機後製設定
function CameraMgr.GetPostProcessActive()

	if m_CameraData == nil then
		CameraMgr.LoadData()
	end

	return m_CameraData.m_PostProcess
end

---設置目前的相機後製設定
function CameraMgr.SetPostProcessActive(iActive)
	if iActive ~= nil and m_CameraData.m_FarClipPlane ~= iActive then
		m_CameraData.m_PostProcess = iActive
		ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device, "m_CameraData", m_CameraData)
	end

	m_PostProcessLayer.enabled = m_CameraData.m_PostProcess
end

---取得目前的視野設定
function CameraMgr.GetFarClipPlane()

	if m_CameraData == nil then
		CameraMgr.LoadData()
	end

	return m_CameraData.m_FarClipPlane
end

---設置目前的視野設定(沒值的話只走設定)
function CameraMgr.SetFarClipPlane(iEQuality)
	if iEQuality and m_CameraData.m_FarClipPlane ~= iEQuality then
		m_CameraData.m_FarClipPlane = iEQuality
		ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device, "m_CameraData", m_CameraData)
	end

	CMMgr:SetFarClipPlane(GetCameraFarPlaneValue())
end

---繪製線段
---@param iStart Vector3
---@param iEnd Vector3
function CameraMgr.DrawLine(iStart, iEnd)
	m_GizmosDebug:SetDrawSetting(iStart, iEnd)
end

---繪製線圈，帶入半徑 = 畫圓形
---@param iType BattleMgr.m_IsShowSkillArea 區域類型
---@param iCenter Vector3 中心點
------@param iRadius float 半徑(圓形)
---@param iSize Vector3 Scale(矩形)
function CameraMgr.DrawWire(iType, iCenter, iRadius, iSize,iAngle, iTrans)
	if not iSize then
		iSize = Vector3.zero
	end

	if not iRadius then
		iRadius = 0
	end

	if not iAngle then
		iAngle = 360
	end

	if iType == true then
		m_GizmosDebug:SetDrawArea(iCenter, iRadius, iSize,iAngle, iTrans)
	end
end

---設定旋轉矩陣
function CameraMgr.SetGizmosMatrix(iMatrix)
	m_GizmosDebug:SetMatrix(iMatrix)
end
--endregion

--region 攝影機點擊位置轉換相關
function CameraMgr.GetScreenPointToRay(iVec3)
	return m_MainCamera:ScreenPointToRay(iVec3)
end

function CameraMgr.GetWorldToScreenPoint(iVec3)
	return m_MainCamera:WorldToScreenPoint(iVec3)
end
--endregion

--region Cinemachine相關
---初始化
function CameraMgr.InitCinemachine()
	--獲取活動中的Cinemachine
	---@class CinemachineMgr.Inst
	CMMgr = CinemachineMgr.Inst
	m_ActiveCinemachine = CMMgr.m_CMActiveFreeLook
	--Cinemachine預設設定套用
	CMMgr:ResetCinemachineSetting()

	--region 玩家Cinemachine設定初始化

	--依相機模式套用設定，使用預設設定
	CameraMgr.SetCameraStats(m_CameraData.m_PlayerCameraMode)
	CameraMgr.SetCameraMove(m_CameraData.m_IsFollow)
	CameraMgr.SetFarClipPlane()
	CMMgr.m_X_Speed = m_CameraData.m_XAxisSpeed
	CMMgr.m_Y_Speed = m_CameraData.m_YAxisSpeed
	--region 只有固定模式才會套用的相機設定
	if m_CameraData.m_PlayerCameraMode == tostring(ECameraType.Fix) then
		--Position
		CMMgr:SetCMLook(Vector2(0, m_CameraData.m_YAxis), true)
		--Zoom Value
		m_SetDistance = m_CameraData.m_ZoomValue
		m_NowDistance = m_SetDistance
		CMMgr:SetRigOrbit(m_CameraData.m_ZoomValue)
	end
	--endregion
	--endregion

	-- 沒存檔的防呆
	if m_CameraData.m_Is_Shake == nil then
		-- 預設打開
		CameraMgr.m_IS_CAMERASHAKE = true
		CameraMgr.SaveData()
	else
		CameraMgr.m_IS_CAMERASHAKE = m_CameraData.m_Is_Shake
	end

	if m_CameraData.m_Is_PlayerEffect == nil then
		-- 預設關閉
		CameraMgr.m_IS_PLAYEREFFECT = false
		CameraMgr.SaveData()
	else
		CameraMgr.m_IS_PLAYEREFFECT = m_CameraData.m_Is_PlayerEffect
	end
end

---取得 CinemachineMgr.Inst，供其他腳本使用
---@return CinemachineMgr.Inst
function CameraMgr.GetCMMgr()
	if CMMgr == nil then
		CMMgr = CinemachineMgr.Inst
	end

	return CMMgr
end

---
function CameraMgr.SetMainTarget(iTargetTransform)
	CameraMgr.SetLookAt(0, iTargetTransform)
	CameraMgr.SetFollow(0, iTargetTransform)
	CameraMgr.SetLookAt(1, iTargetTransform)
	CameraMgr.SetLookAt(2, iTargetTransform)
	-- 若之後要調燈光位置可以用
	-- m_CameraLightRoot = GameObject.New("LightRoot")
	-- m_CameraLightRoot.transform:SetParent(iTargetTransform)
	-- m_CameraLightRoot.transform.localPosition = Vector3.zero
	-- if m_CameraLight then
	-- 	local _CamLight = GameObject.Instantiate(m_CameraLight.gameObject)
	-- 	_CamLight.transform:SetParent(m_CameraLightRoot.transform)
	-- 	_CamLight.transform.localEulerAngles = Vector3.zero
	-- 	_CamLight.transform.localPosition = EffectSetting.m_PlayerSpotLightPos
	-- 	_CamLight.transform.localScale = Vector3.one
	-- 	_CamLight:SetActive(true)
	-- end
	
end
---設定目標
function CameraMgr.SetLookAt(iIndex, iTrans)
	CMMgr:SetLookAt(iIndex, iTrans)
end
---設定追隨對象
function CameraMgr.SetFollow(iIndex, iTrans)
	CMMgr:SetFollow(iIndex, iTrans)
end

---設定在某物件下
function CameraMgr.SetParent(iIndex, iTrans)
	CMMgr:SetParent(iIndex, iTrans)
end


---相機震動
---@param iID number CameraNoise ID
---@param iAmplitude number float 震幅(建議從1開始調整)
---@param iFrequency number float 頻率(建議從1開始調整)
---@param iDuration number float 持續時間
---@param iEaseType number 遞增效果，0 = 不變， 1 = 漸強, 2 = 漸弱
function CameraMgr.OnShake(iID, iAmplitude, iFrequency, iDuration, iEaseType)
	if not CameraMgr.m_IS_CAMERASHAKE then
		return
	end

	CMMgr:ShakeCamera(iID, iAmplitude, iFrequency, iDuration, iEaseType)
end
--endregion

--region 相機操作相關

---縮放
---@param iEventData UILuaEvent
function CameraMgr.OnZoom(iEventData)
	if m_ActiveCinemachine == nil then
		return
	end

	if iEventData == nil then
		return
	end

	local _Divide = ZOOM_RATIO
	if m_NowDistance ~= m_SetDistance then
		m_ZoomingSpeedCounter = m_ZoomingSpeedCounter + ZOOM_ADD_ON_ZOOMING
		_Divide = _Divide - m_ZoomingSpeedCounter
		if _Divide < 0 then
			_Divide = 1
		end
	end

	local _ZoomValue = iEventData.scrollDelta.y/_Divide
	m_SetDistance = m_SetDistance - _ZoomValue
	m_SetDistance = Mathf.Clamp(m_SetDistance, 0, 1)

	--CMMgr:SetRigOrbit(m_SetDistance)
end

function CameraMgr.OnLook(iEventData)
	CMMgr:SetCMLook(iEventData.delta)
	--D.Log("Draging")
end

--region 相機切換
function CameraMgr.OnClick_NextCamera(iEventData)
	-- 取當前狀態(int)
	local _State = CMMgr:GetNowState()
	-- 取列舉總數
	local _Length = CinemachineMgr.GetEnumLength()

	if _State + 1 >= _Length then
		-- 超過列舉上限，回到 ECameraType.Fix
		_State = 1
	else
		_State = _State + 1
	end

	CameraMgr.SwitchCameraThenSave(ECameraType.IntToEnum(_State))
end

---切換相機並存檔(只要有切換就存)
---@param iType ECameraType
function CameraMgr.SwitchCameraThenSave(iType)
	CMMgr:SwitchLiveCamera(iType)
	Main_Controller.RefreshButtonTextByCurrentCameraState()

	m_CameraData.m_PlayerCameraMode = tostring(iType)
	CameraMgr.SaveData()
end

---切換相機模式
function CameraMgr.SetCameraStats(iType)
	--如果是用字串設定就需要轉成C#Enum
	if type(iType) == "string" then
		CMMgr:SwitchLiveCamera(ECameraTypeChange[iType])
		m_CameraData.m_PlayerCameraMode = iType
		return
	else
		m_CameraData.m_PlayerCameraMode = tostring(iType)
	end

	CMMgr:SwitchLiveCamera(iType)
end

---開關鏡頭功能
function CameraMgr.SetCameraMove(iIsOn)
	CMMgr:SwitchCameraMove(iIsOn)
	m_CameraData.m_IsFollow = iIsOn
end
--endregion
--endregion

--region 相機存讀檔相關
function CameraMgr.LoadData()
	-- 用__newindex直接改變m_CameraData的值
	local _Data = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device, "m_CameraData")
	if _Data ~= nil then
		m_CameraData = _Data
	end
end

function CameraMgr.SaveData()
	m_CameraData.m_ZoomValue = m_SetDistance
	m_CameraData.m_YAxis = m_ActiveCinemachine.m_YAxis.Value
	m_CameraData.m_Is_Shake = CameraMgr.m_IS_CAMERASHAKE
	m_CameraData.m_Is_PlayerEffect = CameraMgr.m_IS_PLAYEREFFECT
	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device, "m_CameraData", m_CameraData)
end

function CameraMgr.GetCameraData_MinimapState()
	if not m_CameraData then
		return nil
	end

	return m_CameraData.m_MinimapState
end

function CameraMgr.SetCameraData_MinimapState(iState)
	if not iState then
		return
	end

	m_CameraData.m_MinimapState = iState
	ClientSaveMgr.Save(EClientSaveDataType.Device)
end
--endregion

--region 相機在跑場景TimeLine
function CameraMgr.SetMainCameraActive(iActive)
	m_MainCamera.gameObject:SetActive(iActive)
end

function CameraMgr.SetUICameraActive(iActive)
	UIMgr.m_UICamera.gameObject:SetActive(iActive)
end

function CameraMgr.SetHUDCameraActive(iActive)
	m_HUDCamera.gameObject:SetActive(iActive)
end
--endregion

--region CullingGroup
local m_CullingGroupSetting = {
	RangeSetting = {
		20.0,
		35.0,
		45.0,
		60.0
	},

	MaxBoundNum = 1500
}

function CameraMgr.GetCullingGroupSetting()
	return m_CullingGroupSetting
end
--endregion

--region 特殊演出相機特效(使用時會掛在玩家身上)

---特殊演出鏡位
local m_CameraPerform =
{
	[ECMPlayerEffect.Teleport] =
	{
		m_Pos = Vector3.New(0, 1.6, 6),
		m_AimTrackOffeset = Vector3.New(0, 1.6, 0),
		m_Duration = 1.5,
		m_isOpenCameraSwitch = false,
		m_Particle = 0,

	},
	[ECMPlayerEffect.LearnWugongBack] =
	{
		m_Pos = Vector3.New(6.8, 2.5, 2),
		m_AimTrackOffeset = Vector3.New(-1.5, 1.6, 1.5),
		m_Duration = 3,
		m_isOpenCameraSwitch = true,
		m_Particle = 10702,
		m_IsCoroutine = true,

	},
	[ECMPlayerEffect.LearnWugongTeleport] =
	{
		m_Pos = Vector3.New(6.8, 2.5, 2),
		m_AimTrackOffeset = Vector3.New(-1.5, 1.6, 1.5),
		m_Duration = 10,
		m_isOpenCameraSwitch = true,
		m_Particle = 10701,
		m_IsCoroutine = false,

	},
}

---目前切換相機模式
local m_NowSwitchType

---觸發特殊鏡位
function CameraMgr.TriggerCameraSwitchSP(iECAMERA_EFFECT, iIsTrigger)
	if not CameraMgr.m_IS_PLAYEREFFECT then
		return
	end

	if iIsTrigger then
		if m_CameraPerform[iECAMERA_EFFECT] then

			if m_CameraPerform[iECAMERA_EFFECT].m_Pos then
				CMMgr:SetSwitchCameraPos(2, m_CameraPerform[iECAMERA_EFFECT].m_Pos)
			end

			if m_CameraPerform[iECAMERA_EFFECT].m_AimTrackOffeset then
				CMMgr:SetSwitchCameraAimTrackOffset(2, m_CameraPerform[iECAMERA_EFFECT].m_AimTrackOffeset)
			end

		end
	end

	m_NowSwitchType = iIsTrigger and iECAMERA_EFFECT or nil

	m_ScanEffect:TriggerPlayerEffect(iIsTrigger)
end

---結束目前的切換相機
local function EndNowSwitch()
	if UIMgr.IsVisible(CameraSwitch_Controller) and CameraSwitch_Controller.m_UIStage ~= EUIStage.Closing then
		UIMgr.CloseToPreviousPage(CameraSwitch_Controller)
	end
	CameraMgr.TriggerCameraSwitchSP(m_NowSwitchType, false)
end

---切換有設定玩家特效的表現
local function SetCamSPEffect()
	if m_NowSwitchType and m_CameraPerform[m_NowSwitchType].m_Particle > 0 then
		local _PlayerRC = RoleMgr.GetPlayerRC()
		if _PlayerRC then
			local _EffectData = EffectData.New()
			_EffectData.m_ID = m_CameraPerform[m_NowSwitchType].m_Particle
			_EffectData.m_BonePos = EBoneIdx.Foot
			_EffectData.m_LookAtTrans = _PlayerRC.transform

			_PlayerRC:SetEffect(_EffectData)
			if m_NowSwitchType == ECMPlayerEffect.LearnWugongBack then
				RoleMgr.m_RC_Player:GetModelController():SetAniInteger(AnimationHash.Int_SpecialActID, 2)
				RoleMgr.m_RC_Player:GetModelController():SetAniTrigger(AnimationHash.Trigger_SpecialAct)
			elseif m_NowSwitchType == ECMPlayerEffect.LearnWugongTeleport then
				RoleMgr.m_RC_Player:GetModelController():SetAniInteger(AnimationHash.Int_SpecialActID, 1)
				RoleMgr.m_RC_Player:GetModelController():SetAniTrigger(AnimationHash.Trigger_SpecialAct)
			
			end

		end
	end

end

---一次性的切換運鏡演出
---會自動幫你改回原相機
---還會取用類似Timeline的演出UI，按Skip可跳過
function CameraMgr.DoSwitchSP(iECAMERA_EFFECT, iSkipDelegate)
	if m_CameraPerform[iECAMERA_EFFECT] then
		m_NowSwitchType = iECAMERA_EFFECT
		CameraMgr.TriggerCameraSwitchSP(iECAMERA_EFFECT, true)
		if m_CameraPerform[iECAMERA_EFFECT].m_isOpenCameraSwitch then
			UIMgr.Open(CameraSwitch_Controller, function() 
				EndNowSwitch()
				if iSkipDelegate then
					iSkipDelegate()
				end
			end)
		end

		if m_CameraPerform[iECAMERA_EFFECT].m_IsCoroutine and coroutine.running() then
			if Loading_Controller.m_UIStage ~= EUIStage.Closed then
				coroutine.wait(Loading_Close_Wait_Time)
			end
			SetCamSPEffect()
			coroutine.wait(m_CameraPerform[iECAMERA_EFFECT].m_Duration)
			EndNowSwitch()
		else
			SetCamSPEffect()
			HEMTimeMgr.DoFunctionDelay(m_CameraPerform[iECAMERA_EFFECT].m_Duration, EndNowSwitch)
		end

	end
end

function CameraMgr.DoWhiteOut(isFadeIn)
	if m_IsWhite ~= isFadeIn then
		m_IsWhite = isFadeIn
		m_WhiteStartTime = HEMTimeMgr.time
	
	end
end


--endregion

---登出要做甚麼
function CameraMgr.LoggingOutToDo()
	--把物件還給CM_List 2 = ECMIndex.PlayerEffectCamera
	if CMMgr then
		CameraMgr.SetParent(2, CMMgr.transform.parent)
	end
end

return CameraMgr
