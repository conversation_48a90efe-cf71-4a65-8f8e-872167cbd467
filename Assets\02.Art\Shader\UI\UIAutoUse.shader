Shader "HEM/UIAutoUse (SoftMaskable)"
{
    Properties
    {
        [PerRendererData] _MainTex("Sprite Texture", 2D) = "white" {}
        _Intensity("亮度(HDR級別)", float) = 0.1

        [Header(Scan Light Settings)]
        _ScanColor("顏色", Color) = (1,1,1,1)
        _LightCount("掃光數量（最多 3 條）", float) = 3
        _LightIntensity("強度", float) = 0.8
        _LightWidth("寬度", float) = 0.8
        _EdgeSoftness("邊緣柔和度", Range(0.01, 1)) = 1
        _Speed("旋轉速度", float) = 0.2
    }

    SubShader
    {
        Tags{ "Queue" = "Transparent" "RenderType" = "Transparent" }
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"
            #include "Packages/com.coffee.softmask-for-ugui/Shaders/SoftMask.cginc"
            #pragma shader_feature _ SOFTMASK_EDITOR
            #pragma shader_feature_local _ SOFTMASKABLE

            sampler2D _MainTex;
            float4 _MainTex_ST;

            half _Intensity;
            half4 _ScanColor;
            half _LightCount;
            half _LightIntensity;
            half _LightWidth;
            half _EdgeSoftness;
            half _Speed;

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                fixed4 color : COLOR;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4 vertexObjPos : TEXCOORD1;
                fixed4 color : COLOR;
            };

            v2f vert(appdata v)
            {
                v2f o;
                // 轉換物體空間到剪輯空間
                o.vertex = UnityObjectToClipPos(v.vertex);
                // 計算貼圖 UV
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                // 保存物體空間的位置
                o.vertexObjPos = v.vertex;
                // 傳遞頂點顏色
                o.color = v.color;
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // 取得貼圖顏色
                fixed4 mainCol = tex2D(_MainTex, i.uv);
                // 結合頂點透明度
                float alpha = mainCol.a * i.color.a;
                // 以中心為旋轉點
                float2 centerUV = i.uv - 0.5;
                // 計算掃光角度（順時針） atan2 出來會是 -π ~ π
                float angle = atan2(centerUV.y, centerUV.x);
                // 轉換成 0 ~ 2π
                angle = angle < 0 ? angle + 6.2831853 : angle; 

                // 起始一個很大的值
                float minAngleDiff = 10.0; 
                // 動態旋轉掃光（時間推進） frac 是取小數點部分
                float t = frac(_Time.y * _Speed);
                // 掃光角度
                float baseAngle = 0.f;
                // 目標角度
                float targetAngle = 0.f;
                // 角度差
                float angleDiff = 0.f;

                if (_LightCount > 0)
                {
                    // 分配整個圓給掃光
                    baseAngle = (6.2831853 / _LightCount) * 0;
                    // 順時針旋轉 角度維持在 0~2π
                    targetAngle = fmod(baseAngle - t * 6.2831853 + 6.2831853, 6.2831853);
                    // 計算當前角度和目標角度的差值
                    angleDiff = angle - targetAngle;
                    // 負的就補上 2π
                    if (angleDiff < 0) 
                        angleDiff += 6.2831853;
                    // 取最小的角度差
                    minAngleDiff = min(minAngleDiff, angleDiff);
                }

                if (_LightCount > 1)
                {
                    // 分配整個圓給掃光
                    baseAngle = (6.2831853 / _LightCount) * 1;
                    // 順時針旋轉 角度維持在 0~2π
                    targetAngle = fmod(baseAngle - t * 6.2831853 + 6.2831853, 6.2831853);
                    // 計算當前角度和目標角度的差值
                    angleDiff = angle - targetAngle;
                    // 負的就補上 2π
                    if (angleDiff < 0) 
                        angleDiff += 6.2831853;
                    // 取最小的角度差
                    minAngleDiff = min(minAngleDiff, angleDiff);
                }

                if (_LightCount > 2)
                {
                    // 分配整個圓給掃光
                    baseAngle = (6.2831853 / _LightCount) * 2;
                    // 順時針旋轉 角度維持在 0~2π
                    targetAngle = fmod(baseAngle - t * 6.2831853 + 6.2831853, 6.2831853);
                    // 計算當前角度和目標角度的差值
                    angleDiff = angle - targetAngle;
                    // 負的就補上 2π
                    if (angleDiff < 0) 
                        angleDiff += 6.2831853;
                    // 取最小的角度差
                    minAngleDiff = min(minAngleDiff, angleDiff);
                }

                // 掃光強度
                float highlight = saturate(1 - minAngleDiff / _LightWidth);
                highlight = pow(highlight, _EdgeSoftness) * _LightIntensity;
                // 僅在不透明的區域顯示
                highlight *= mainCol.a;

                // SoftMask
                i.color.a *= SoftMask(i.vertex, i.vertexObjPos, i.color.a);

                float3 baseColor = mainCol.rgb * i.color.rgb * alpha;
                float3 scanColor = _ScanColor.rgb * _ScanColor.a * highlight;
                // 混合底色和掃光顏色
                float3 finalRGB = lerp(baseColor, scanColor, highlight);
                fixed4 finalColor = fixed4(finalRGB * pow(2, _Intensity), alpha);
                finalColor.rgb = finalColor.rgb * pow(2, _Intensity);
                return finalColor;
            }
            ENDCG
        }
    }
    FallBack "UI/Default"
}