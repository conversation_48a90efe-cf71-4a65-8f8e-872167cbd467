fileFormatVersion: 2
guid: 6311286931c4d1f44a1b67bcaec67285
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Bone001
  - first:
      1: 100004
    second: Bone002
  - first:
      1: 100006
    second: Bone003
  - first:
      1: 100008
    second: Bone005
  - first:
      1: 100010
    second: Bone007
  - first:
      1: 100012
    second: Bone008
  - first:
      1: 100014
    second: Bone010
  - first:
      1: 100016
    second: Bone011
  - first:
      1: 100018
    second: Bone013
  - first:
      1: 100020
    second: Bone014
  - first:
      1: 100022
    second: Bone016
  - first:
      1: 100024
    second: Bone017
  - first:
      1: 100026
    second: Bone018
  - first:
      1: 100028
    second: CP_011
  - first:
      1: 100030
    second: root
  - first:
      1: 100032
    second: Bip001
  - first:
      1: 100034
    second: Bip001 Head
  - first:
      1: 100036
    second: Bip001 L Hand
  - first:
      1: 100038
    second: Bip001 R Hand
  - first:
      1: 100040
    second: MID_GIRL
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Bone001
  - first:
      4: 400004
    second: Bone002
  - first:
      4: 400006
    second: Bone003
  - first:
      4: 400008
    second: Bone005
  - first:
      4: 400010
    second: Bone007
  - first:
      4: 400012
    second: Bone008
  - first:
      4: 400014
    second: Bone010
  - first:
      4: 400016
    second: Bone011
  - first:
      4: 400018
    second: Bone013
  - first:
      4: 400020
    second: Bone014
  - first:
      4: 400022
    second: Bone016
  - first:
      4: 400024
    second: Bone017
  - first:
      4: 400026
    second: Bone018
  - first:
      4: 400028
    second: CP_011
  - first:
      4: 400030
    second: root
  - first:
      4: 400032
    second: Bip001
  - first:
      4: 400034
    second: Bip001 Head
  - first:
      4: 400036
    second: Bip001 L Hand
  - first:
      4: 400038
    second: Bip001 R Hand
  - first:
      4: 400040
    second: MID_GIRL
  - first:
      23: 2300000
    second: MID_GIRL
  - first:
      33: 3300000
    second: MID_GIRL
  - first:
      43: 4300000
    second: CP_011
  - first:
      43: 4300002
    second: MID_GIRL
  - first:
      74: 7400000
    second: idle
  - first:
      74: 7400002
    second: run
  - first:
      74: 7400004
    second: ready
  - first:
      74: 7400006
    second: hit
  - first:
      74: 7400008
    second: weak
  - first:
      74: 7400010
    second: atk1
  - first:
      74: 7400012
    second: win
  - first:
      74: 7400014
    second: die
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: CP_011
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: run
      takeName: Take 001
      internalID: 0
      firstFrame: 35
      lastFrame: 55
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ready
      takeName: Take 001
      internalID: 0
      firstFrame: 60
      lastFrame: 80
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: hit
      takeName: Take 001
      internalID: 0
      firstFrame: 85
      lastFrame: 95
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      - path: CP_011
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: weak
      takeName: Take 001
      internalID: 0
      firstFrame: 100
      lastFrame: 140
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      - path: CP_011
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk1
      takeName: Take 001
      internalID: 0
      firstFrame: 145
      lastFrame: 175
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: win
      takeName: Take 001
      internalID: 0
      firstFrame: 180
      lastFrame: 205
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      - path: CP_011
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: die
      takeName: Take 001
      internalID: 0
      firstFrame: 210
      lastFrame: 220
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001
        weight: 1
      - path: Bip001/Bone001
        weight: 1
      - path: Bip001/Bone001/Bone002
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head
        weight: 1
      - path: Bip001/Bone001/Bone002/Bip001 Head/Bone005
        weight: 1
      - path: Bip001/Bone001/Bone007
        weight: 1
      - path: Bip001/Bone001/Bone007/Bip001 L Hand
        weight: 1
      - path: Bip001/Bone001/Bone010
        weight: 1
      - path: Bip001/Bone001/Bone010/Bone011
        weight: 1
      - path: Bip001/Bone001/Bone013
        weight: 1
      - path: Bip001/Bone001/Bone013/Bip001 R Hand
        weight: 1
      - path: Bip001/Bone001/Bone016
        weight: 1
      - path: Bip001/Bone001/Bone016/Bone017
        weight: 1
      - path: Bip001/Bone001/Bone018
        weight: 1
      - path: CP_011
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: cc380b8fdec4a42408350290f2bb9555,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
