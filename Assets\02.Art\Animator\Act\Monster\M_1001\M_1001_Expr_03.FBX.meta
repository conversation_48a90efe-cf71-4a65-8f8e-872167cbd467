fileFormatVersion: 2
guid: 742f75063a654d34291f7e82ada8bfdf
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 D_LEye
    100004: Bip001 D_REye
    100006: Bip001 Head
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger02
    100018: Bip001 L Finger1
    100020: Bip001 L Finger11
    100022: Bip001 L Finger12
    100024: Bip001 L Finger2
    100026: Bip001 L Finger21
    100028: Bip001 L Finger22
    100030: Bip001 L Finger3
    100032: Bip001 L Finger31
    100034: Bip001 L Finger32
    100036: Bip001 L Finger4
    100038: Bip001 L Finger41
    100040: Bip001 L Finger42
    100042: Bip001 L Foot
    100044: Bip001 L Forearm
    100046: Bip001 L Hand
    100048: Bip001 L Thigh
    100050: Bip001 L Toe0
    100052: Bip001 L Toe01
    100054: Bip001 L Toe02
    100056: Bip001 L Toe1
    100058: Bip001 L Toe11
    100060: Bip001 L Toe12
    100062: Bip001 L Toe2
    100064: Bip001 L Toe21
    100066: Bip001 L Toe22
    100068: Bip001 L Toe3
    100070: Bip001 L Toe31
    100072: Bip001 L Toe32
    100074: Bip001 L Toe4
    100076: Bip001 L Toe41
    100078: Bip001 L Toe42
    100080: Bip001 L UpperArm
    100082: Bip001 Mouth
    100084: Bip001 Neck
    100086: Bip001 Pelvis
    100088: Bip001 R Calf
    100090: Bip001 R Clavicle
    100092: Bip001 R Finger0
    100094: Bip001 R Finger01
    100096: Bip001 R Finger02
    100098: Bip001 R Finger1
    100100: Bip001 R Finger11
    100102: Bip001 R Finger12
    100104: Bip001 R Finger2
    100106: Bip001 R Finger21
    100108: Bip001 R Finger22
    100110: Bip001 R Finger3
    100112: Bip001 R Finger31
    100114: Bip001 R Finger32
    100116: Bip001 R Finger4
    100118: Bip001 R Finger41
    100120: Bip001 R Finger42
    100122: Bip001 R Foot
    100124: Bip001 R Forearm
    100126: Bip001 R Hand
    100128: Bip001 R Thigh
    100130: Bip001 R Toe0
    100132: Bip001 R Toe01
    100134: Bip001 R Toe02
    100136: Bip001 R Toe1
    100138: Bip001 R Toe11
    100140: Bip001 R Toe12
    100142: Bip001 R Toe2
    100144: Bip001 R Toe21
    100146: Bip001 R Toe22
    100148: Bip001 R Toe3
    100150: Bip001 R Toe31
    100152: Bip001 R Toe32
    100154: Bip001 R Toe4
    100156: Bip001 R Toe41
    100158: Bip001 R Toe42
    100160: Bip001 R UpperArm
    100162: Bip001 Spine
    100164: Bip001 Spine1
    100166: Bip001 Tail
    100168: Bip001 Tail1
    100170: Bip001 Tail2
    100172: Bip001 Tail3
    100174: Bip001 U_LEye
    100176: Bip001 U_REye
    100178: //RootNode
    400000: Bip001
    400002: Bip001 D_LEye
    400004: Bip001 D_REye
    400006: Bip001 Head
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger02
    400018: Bip001 L Finger1
    400020: Bip001 L Finger11
    400022: Bip001 L Finger12
    400024: Bip001 L Finger2
    400026: Bip001 L Finger21
    400028: Bip001 L Finger22
    400030: Bip001 L Finger3
    400032: Bip001 L Finger31
    400034: Bip001 L Finger32
    400036: Bip001 L Finger4
    400038: Bip001 L Finger41
    400040: Bip001 L Finger42
    400042: Bip001 L Foot
    400044: Bip001 L Forearm
    400046: Bip001 L Hand
    400048: Bip001 L Thigh
    400050: Bip001 L Toe0
    400052: Bip001 L Toe01
    400054: Bip001 L Toe02
    400056: Bip001 L Toe1
    400058: Bip001 L Toe11
    400060: Bip001 L Toe12
    400062: Bip001 L Toe2
    400064: Bip001 L Toe21
    400066: Bip001 L Toe22
    400068: Bip001 L Toe3
    400070: Bip001 L Toe31
    400072: Bip001 L Toe32
    400074: Bip001 L Toe4
    400076: Bip001 L Toe41
    400078: Bip001 L Toe42
    400080: Bip001 L UpperArm
    400082: Bip001 Mouth
    400084: Bip001 Neck
    400086: Bip001 Pelvis
    400088: Bip001 R Calf
    400090: Bip001 R Clavicle
    400092: Bip001 R Finger0
    400094: Bip001 R Finger01
    400096: Bip001 R Finger02
    400098: Bip001 R Finger1
    400100: Bip001 R Finger11
    400102: Bip001 R Finger12
    400104: Bip001 R Finger2
    400106: Bip001 R Finger21
    400108: Bip001 R Finger22
    400110: Bip001 R Finger3
    400112: Bip001 R Finger31
    400114: Bip001 R Finger32
    400116: Bip001 R Finger4
    400118: Bip001 R Finger41
    400120: Bip001 R Finger42
    400122: Bip001 R Foot
    400124: Bip001 R Forearm
    400126: Bip001 R Hand
    400128: Bip001 R Thigh
    400130: Bip001 R Toe0
    400132: Bip001 R Toe01
    400134: Bip001 R Toe02
    400136: Bip001 R Toe1
    400138: Bip001 R Toe11
    400140: Bip001 R Toe12
    400142: Bip001 R Toe2
    400144: Bip001 R Toe21
    400146: Bip001 R Toe22
    400148: Bip001 R Toe3
    400150: Bip001 R Toe31
    400152: Bip001 R Toe32
    400154: Bip001 R Toe4
    400156: Bip001 R Toe41
    400158: Bip001 R Toe42
    400160: Bip001 R UpperArm
    400162: Bip001 Spine
    400164: Bip001 Spine1
    400166: Bip001 Tail
    400168: Bip001 Tail1
    400170: Bip001 Tail2
    400172: Bip001 Tail3
    400174: Bip001 U_LEye
    400176: Bip001 U_REye
    400178: //RootNode
    7400000: M_1001_Expr_03
    7400002: M_1001_Expr_03_Loop
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_1001_Expr_03
      takeName: M_1001_Expr_03
      firstFrame: 0
      lastFrame: 125
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: M_1001_Expr_03_Loop
      takeName: M_1001_Expr_03
      firstFrame: 37
      lastFrame: 125
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: Bip001
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 99167f5a0c7f30e41b5df88601c239a0,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
