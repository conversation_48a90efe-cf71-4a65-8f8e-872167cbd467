fileFormatVersion: 2
guid: 47ac93be6b6081b46b4dcd895e6ae920
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: <PERSON>_Head_JawEndSHJnt
    100004: <PERSON>_<PERSON>_JawSHJnt
    100006: <PERSON>_Head_TopSHJnt
    100008: <PERSON>_l_Clavicle_01_01SHJnt
    100010: <PERSON>_l_Ear_01_01SHJnt
    100012: <PERSON>_<PERSON>_Ear_01_02SHJnt
    100014: Wolf_l_Ear_01_03SHJnt
    100016: <PERSON>_l_FrontLeg_AnkleSHJnt
    100018: <PERSON>_<PERSON>_FrontLeg_BallSHJnt
    100020: Wolf_l_FrontLeg_HipSHJnt
    100022: <PERSON>_l_FrontLeg_KneeSHJnt
    100024: Wolf_l_FrontLeg_ToeSHJnt
    100026: <PERSON>_l_HindLeg_AnkleSHJnt
    100028: Wolf_l_HindLeg_BallSHJnt
    100030: <PERSON>_<PERSON>_HindLeg_HipSHJnt
    100032: <PERSON>_<PERSON>_HindLeg_Knee1SHJnt
    100034: <PERSON>_<PERSON>_<PERSON>nd<PERSON><PERSON>_Knee2SHJnt
    100036: <PERSON>_<PERSON>_Hind<PERSON>eg_ToeSHJnt
    100038: <PERSON>_<PERSON>INSHJ<PERSON>
    100040: Wolf_Neck_01SHJnt
    100042: Wolf_Neck_02SHJnt
    100044: Wolf_Neck_TopSHJnt
    100046: Wolf_r_Clavicle_01_01SHJnt
    100048: Wolf_r_Ear_01_01SHJnt
    100050: Wolf_r_Ear_01_02SHJnt
    100052: Wolf_r_Ear_01_03SHJnt
    100054: Wolf_r_FrontLeg_AnkleSHJnt
    100056: Wolf_r_FrontLeg_BallSHJnt
    100058: Wolf_r_FrontLeg_HipSHJnt
    100060: Wolf_r_FrontLeg_KneeSHJnt
    100062: Wolf_r_FrontLeg_ToeSHJnt
    100064: Wolf_r_HindLeg_AnkleSHJnt
    100066: Wolf_r_HindLeg_BallSHJnt
    100068: Wolf_r_HindLeg_HipSHJnt
    100070: Wolf_r_HindLeg_Knee1SHJnt
    100072: Wolf_r_HindLeg_Knee2SHJnt
    100074: Wolf_r_HindLeg_ToeSHJnt
    100076: Wolf_ROOTSHJnt
    100078: Wolf_Spine_01SHJnt
    100080: Wolf_Spine_02SHJnt
    100082: Wolf_Spine_03SHJnt
    100084: Wolf_Spine_04SHJnt
    100086: Wolf_Spine_TopSHJnt
    100088: Wolf_Tail_01_01SHJnt
    100090: Wolf_Tail_01_02SHJnt
    100092: Wolf_Tail_01_03SHJnt
    100094: Wolf_Tail_01_04SHJnt
    100096: Wolf_Tail_01_05SHJnt
    100098: Wolf_Tongue_01_01SHJnt
    100100: Wolf_Tongue_01_02SHJnt
    100102: Wolf_Tongue_01_03SHJnt
    100104: Wolf_Tongue_01_04SHJnt
    400000: //RootNode
    400002: Wolf_Head_JawEndSHJnt
    400004: Wolf_Head_JawSHJnt
    400006: Wolf_Head_TopSHJnt
    400008: Wolf_l_Clavicle_01_01SHJnt
    400010: Wolf_l_Ear_01_01SHJnt
    400012: Wolf_l_Ear_01_02SHJnt
    400014: Wolf_l_Ear_01_03SHJnt
    400016: Wolf_l_FrontLeg_AnkleSHJnt
    400018: Wolf_l_FrontLeg_BallSHJnt
    400020: Wolf_l_FrontLeg_HipSHJnt
    400022: Wolf_l_FrontLeg_KneeSHJnt
    400024: Wolf_l_FrontLeg_ToeSHJnt
    400026: Wolf_l_HindLeg_AnkleSHJnt
    400028: Wolf_l_HindLeg_BallSHJnt
    400030: Wolf_l_HindLeg_HipSHJnt
    400032: Wolf_l_HindLeg_Knee1SHJnt
    400034: Wolf_l_HindLeg_Knee2SHJnt
    400036: Wolf_l_HindLeg_ToeSHJnt
    400038: Wolf_MAINSHJnt
    400040: Wolf_Neck_01SHJnt
    400042: Wolf_Neck_02SHJnt
    400044: Wolf_Neck_TopSHJnt
    400046: Wolf_r_Clavicle_01_01SHJnt
    400048: Wolf_r_Ear_01_01SHJnt
    400050: Wolf_r_Ear_01_02SHJnt
    400052: Wolf_r_Ear_01_03SHJnt
    400054: Wolf_r_FrontLeg_AnkleSHJnt
    400056: Wolf_r_FrontLeg_BallSHJnt
    400058: Wolf_r_FrontLeg_HipSHJnt
    400060: Wolf_r_FrontLeg_KneeSHJnt
    400062: Wolf_r_FrontLeg_ToeSHJnt
    400064: Wolf_r_HindLeg_AnkleSHJnt
    400066: Wolf_r_HindLeg_BallSHJnt
    400068: Wolf_r_HindLeg_HipSHJnt
    400070: Wolf_r_HindLeg_Knee1SHJnt
    400072: Wolf_r_HindLeg_Knee2SHJnt
    400074: Wolf_r_HindLeg_ToeSHJnt
    400076: Wolf_ROOTSHJnt
    400078: Wolf_Spine_01SHJnt
    400080: Wolf_Spine_02SHJnt
    400082: Wolf_Spine_03SHJnt
    400084: Wolf_Spine_04SHJnt
    400086: Wolf_Spine_TopSHJnt
    400088: Wolf_Tail_01_01SHJnt
    400090: Wolf_Tail_01_02SHJnt
    400092: Wolf_Tail_01_03SHJnt
    400094: Wolf_Tail_01_04SHJnt
    400096: Wolf_Tail_01_05SHJnt
    400098: Wolf_Tongue_01_01SHJnt
    400100: Wolf_Tongue_01_02SHJnt
    400102: Wolf_Tongue_01_03SHJnt
    400104: Wolf_Tongue_01_04SHJnt
    7400000: MS_Dog_Walk_00
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: MS_Dog_Walk_00
      takeName: Take 001
      firstFrame: 1
      lastFrame: 29
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e14fa0df045793d46936fbccb7ac21a8,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
