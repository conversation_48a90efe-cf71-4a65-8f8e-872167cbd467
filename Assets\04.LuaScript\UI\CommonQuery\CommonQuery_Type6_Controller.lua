---=====================================================================
---
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---詢問視窗 大分類 6 單一物品選擇型
---@class CommonQuery_Type6_Controller
---author 李曜宇
---telephone #2890
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.16
CommonQuery_Type6_Controller = {}
local this = CommonQuery_Type6_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("CommonQuery_Type6_View", "CommonQuery_Type6_Controller", EUIOrderLayers.Peak)
--- 可全域取得的 選擇Idx
this.m_SelectIdx = 1
--- 可全域取得的 選擇物品ID
this.m_SelectItemIdx = 0
--- 介面最大Icon數量 增加後修改此處
this.m_ItemIconMax = 6
--- 屬性欄位最多幾條
local ATRRIBUTE_NUM = 3
ECommonQueryType6_SubType =
{
    ---一般模式
    General =1,
}

---初始化
function CommonQuery_Type6_Controller.Init()
    ---變體1的母物件
    this.m_Obj_Type1_Parent = this.m_ViewRef.m_Dic_Trans:Get("&Trans_SubType1")
    ---subType 1 用的UI
    this.m_Text_TitleContent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TitleContent")
    this.m_Text_Content = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content")



    this.m_Btn_Cancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Cancel").gameObject)
    this.m_Btn_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm").gameObject)
    this.m_Btn_Special = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Special").gameObject)

    this.m_Gobj_Text_Content = this.m_ViewRef.m_Dic_Trans:Get("&Text_Content").gameObject
    this.m_Gobj_Group_Icon2 = this.m_ViewRef.m_Dic_Trans:Get("&LayoutGroup_Icon_2")
    ---持有數文字區塊 另外用一個LayOutGroup存 關閉時才能自動排版
    this.m_Gobj_Group_ItemCount = {}
    this.m_Gobj_Group_ItemCount[1] = this.m_ViewRef.m_Dic_Trans:Get("&LayoutGroup_ItemCount")
    this.m_Gobj_Group_ItemCount[2] = this.m_ViewRef.m_Dic_Trans:Get("&LayoutGroup_ItemCount_2")
    --- 存Icon表
    this.m_tIcon_Items = {}
    --- 存Icon父物件
    this.m_Gobj_Icon_Items = {}
    --- 存Icon數量TMP
    this.m_TMP_ItemCount = {}
    for i = 1, this.m_ItemIconMax do
        this.m_Gobj_Icon_Items[i] = this.m_ViewRef.m_Dic_Trans:Get(GString.Format("&Icon_Item{0}",i))
        this.m_tIcon_Items[i] = IconMgr.NewItemIcon(0,this.m_Gobj_Icon_Items[i], 110, function()
            CommonQuery_Type6_Controller.UpdateSelectUI(i)
        end, false)
        ---不被IconMgr清除選擇屬性，體現在開Hint時不會被統一清除
        this.m_tIcon_Items[i].m_NeedClearSelect = false
        this.m_TMP_ItemCount[i] = this.m_ViewRef.m_Dic_TMPText:Get(GString.Format("&TMP_ItemCount_{0}",i))
    end

    --- 屬性 LayoutGroup
    this.m_Gobjs_Group_AttributeChange={}
    --- 屬性標題與屬性數值同一行還是不同行，1--不同行 2--同行 3--不顯示
    this.m_tran_AttributeUnits={}
    --- 屬性標題的TMP
    this.m_TMP_AttributeTitles = {}
    --- 屬性內容的TMP
    this.m_TMP_AttributeContents = {}
    for i = 1, ATRRIBUTE_NUM do -- 目前屬性最多三行
        this.m_Gobjs_Group_AttributeChange[i] = {}
        this.m_Gobjs_Group_AttributeChange[i]= this.m_ViewRef.m_Dic_Trans:Get("&LayOutGroup_AtrributeChange_"..i)

        this.m_tran_AttributeUnits[i]={}
        --- 屬性名稱 數值在上下
        this.m_tran_AttributeUnits[i][1] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_V_"..i)
        --- 屬性名稱 數值在同行
        this.m_tran_AttributeUnits[i][2] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_H_"..i)

        this.m_TMP_AttributeTitles[i]={}
        this.m_TMP_AttributeTitles[i][1] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_V_"..i):Find("TMP_AttributeTitle").transform.gameObject:GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        this.m_TMP_AttributeTitles[i][2] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_H_"..i):Find("TMP_AttributeTitle").transform.gameObject:GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        this.m_TMP_AttributeContents[i]={}
        this.m_TMP_AttributeContents[i][1] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_V_"..i):Find("AttributeValue/TMP_AttributeValue").transform.gameObject:GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        this.m_TMP_AttributeContents[i][2] = this.m_ViewRef.m_Dic_Trans:Get("&GObj_AttributeChangeUnit_H_"..i):Find("AttributeValue/TMP_AttributeValue").transform.gameObject:GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end

    --- 處理LayOutGroup 與 ContentSzieFitter 刷新順序造成的大小錯誤
    this.m_ContentFitter = {}
    this.m_ContentFitter[1]= this.m_Text_Content.transform.gameObject:GetComponent(typeof(ContentFitterImmediate))
    this.m_ContentFitter[2]= this.m_ViewRef.m_Dic_Trans:Get("&AutoSizeImg_BG").gameObject:GetComponent(typeof(ContentFitterImmediate))
end

function CommonQuery_Type6_Controller.Open()
    CommonQuery_Type6_Controller.Settinginformation()
    CommonQuery_Type6_Controller.ForceUpdateContentSize()
    return true
end
function CommonQuery_Type6_Controller.ForceUpdateContentSize()
    for i = 1, #this.m_ContentFitter do
        this.m_ContentFitter[i]:ForceUpdateContentSize()
    end
end
---設定中央詢問視窗 需要顯示的元件
function CommonQuery_Type6_Controller.Settinginformation()
    ---@type CommonQueryData
    local _viewData = CommonQueryMgr.GetCurrentCommonQueryData()
    if not _viewData then
        D.LogError("中央詢問視窗沒資料!!")
        return
    end
    if table.IsNullOrEmpty(_viewData.m_CustomData) then --檢查類型九資料
        D.LogError("類型9詢問視窗沒m_CustomData資料!!")
        CommonQueryMgr.ShowNextCommonQueryMgrData()
        return
    end

    ---設定確認方法
    if _viewData.m_CallBack_Confirm ~= nil then
        CommonQuery_Type6_Controller.m_Type0_CallBack_Confirm = function()
            if(type(_viewData.m_CallBack_ConfirmArgs)=="table" and next(_viewData.m_CallBack_ConfirmArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Confirm,unpack(_viewData.m_CallBack_ConfirmArgs))
            else
                pcall(_viewData.m_CallBack_Confirm)
            end
            CommonQuery_Type6_Controller.DoConfirmBtn()
        end
    else
        CommonQuery_Type6_Controller.m_Type0_CallBack_Confirm = function()
            CommonQuery_Type6_Controller.DoConfirmBtn()
        end
    end

    ---設定取消方法
    if _viewData.m_CallBack_Cancel ~= nil then
        CommonQuery_Type6_Controller.m_Type0_CallBack_Cancel = function()
            if(type(_viewData.m_CallBack_CancelArgs)=="table" and next(_viewData.m_CallBack_CancelArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Cancel,unpack(_viewData.m_CallBack_CancelArgs))
            else
                pcall(_viewData.m_CallBack_Cancel)
            end
            CommonQuery_Type6_Controller.DoCancelBtn()
            IconMgr.CancelAllClick(true)
        end
    else
        CommonQuery_Type6_Controller.m_Type0_CallBack_Cancel = function()
            CommonQuery_Type6_Controller.DoCancelBtn()
            IconMgr.CancelAllClick(true)
        end
    end

    ---設定特殊方法
    if _viewData.m_CallBack_Special ~= nil then
        CommonQuery_Type6_Controller.m_Type0_CallBack_Special = function()
            if(type(_viewData.m_CallBack_SpecialArgs)=="table" and next(_viewData.m_CallBack_SpecialArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Special,unpack(_viewData.m_CallBack_SpecialArgs))
            else
                pcall(_viewData.m_CallBack_Special)
            end
            CommonQuery_Type6_Controller.DoSpecialBtn()
        end
    else
        CommonQuery_Type6_Controller.m_Type0_CallBack_Special = function()
            CommonQuery_Type6_Controller.DoSpecialBtn()
        end
    end

    local _Assign_Btn_Confirm
    local _Assign_Btn_Cancel
    local _Assign_Btn_Special
    local _AssignTMP

    ---設定 Type1 母物件要開啟/關閉
    if _viewData.m_CommonqueryBoxType_SubType ~= ECommonQueryType6_SubType.General then
        this.m_Obj_Type1_Parent.gameObject:SetActive(false)
    else
        _Assign_Btn_Confirm = this.m_Btn_Confirm
        _Assign_Btn_Cancel = this.m_Btn_Cancel
        _Assign_Btn_Special = this.m_Btn_Special
        _AssignTMP =  this.m_Text_Content
        this.m_Obj_Type1_Parent.gameObject:SetActive(true)
    end

    ---是否要要內文 填入其他資訊
    if table.IsNullOrEmpty(_viewData.m_ContentParma) then
        _AssignTMP.text = TextData.Get(_viewData.m_ContentID)
    else
        _AssignTMP.text = GString.Format(TextData.Get(_viewData.m_ContentID) , unpack(_viewData.m_ContentParma, 1, table.maxn(_viewData.m_ContentParma)))
    end

    ---無內文時關閉物件，讓下方Icon上靠
    if _AssignTMP.text==nil or _AssignTMP.text=="" then
        this.m_Gobj_Text_Content:SetActive(false)
    else
        this.m_Gobj_Text_Content:SetActive(true)
    end

    ---設定標題
    if _viewData.m_TitleContentID ~=0 then --有標題
        if table.IsNullOrEmpty(_viewData.m_TitleParma)then
            this.m_Text_TitleContent.text = TextData.Get(_viewData.m_TitleContentID)
        else
            --  TextData.Get(_viewData.m_TitleContentID)
            this.m_Text_TitleContent.text = GString.Format(TextData.Get(_viewData.m_TitleContentID) , unpack(_viewData.m_TitleParma, 1, table.maxn(_viewData.m_TitleParma)))
        end

        this.m_Text_TitleContent.gameObject:SetActive(true)
    else
        this.m_Text_TitleContent.gameObject:SetActive(false)
    end

    if _viewData.m_Str_BTNCancel ~= 0 then --取消按鈕
        Button.SetText(_Assign_Btn_Cancel,TextData.Get(_viewData.m_Str_BTNCancel))
        Button.ClearListener(_Assign_Btn_Cancel.gameObject)
        Button.AddListener(_Assign_Btn_Cancel, EventTriggerType.PointerClick,CommonQuery_Type6_Controller.m_Type0_CallBack_Cancel)
        Button.SetAudioID(_Assign_Btn_Cancel, _viewData.m_Sound_BTNCancel)

        _Assign_Btn_Cancel.gameObject:SetActive(true)
    else
        _Assign_Btn_Cancel.gameObject:SetActive(false)
    end

    if _viewData.m_Str_BTNConfirm ~= 0 then --確認按鈕
        Button.SetText(_Assign_Btn_Confirm,TextData.Get(_viewData.m_Str_BTNConfirm))
        Button.ClearListener(_Assign_Btn_Confirm.gameObject)
        Button.AddListener(_Assign_Btn_Confirm, EventTriggerType.PointerClick,CommonQuery_Type6_Controller.m_Type0_CallBack_Confirm)
        Button.SetAudioID(_Assign_Btn_Confirm, _viewData.m_Sound_BTNConfirm)

        _Assign_Btn_Confirm.gameObject:SetActive(true)
    else
        _Assign_Btn_Confirm.gameObject:SetActive(false)
    end

    if _viewData.m_Str_BTNSpecial ~= 0 then --特殊按鈕
        Button.SetText(_Assign_Btn_Special,TextData.Get(_viewData.m_Str_BTNSpecial))
        Button.ClearListener(_Assign_Btn_Special.gameObject)
        Button.AddListener(_Assign_Btn_Special, EventTriggerType.PointerClick,CommonQuery_Type6_Controller.m_Type0_CallBack_Special)
        Button.SetAudioID(_Assign_Btn_Special, _viewData.m_Sound_BTNSpecial)

        _Assign_Btn_Special.gameObject:SetActive(true)
    else
        _Assign_Btn_Special.gameObject:SetActive(false)
    end
    ---@type CustomCommonQueryData_Type6
    this.m_CustomData = _viewData.m_CustomData
    ---設定 Type1 General 可選屬性
    if _viewData.m_CommonqueryBoxType_SubType == ECommonQueryType6_SubType.General then
        --- 是否顯示物品名稱
        _viewData.m_CustomData.m_ShowItemName = false
    end



    CommonQuery_Type6_Controller.NewDataInit()
    CommonQuery_Type6_Controller.UpdateItemCount()
    CommonQuery_Type6_Controller.UpdateSelectUI(1)

    -- 程式碼文字物件大小設定
    -- 物件推薦大小preferredHeight 約等於 字體大小*行數 + ExtraSetting Top + ExtraSetting Bottom
    --[[
    local _PreferredHeight = _AssignTMP.preferredHeight
    local _TextContentMin = 120
    local _TargetHeight = _PreferredHeight <=_TextContentMin and _TextContentMin or _PreferredHeight
    _AssignTMP.transform:GetComponent( typeof( RectTransform ) ):SetSizeWithCurrentAnchors(Axis.Vertical,_TargetHeight)]]
end
function CommonQuery_Type6_Controller.DoConfirmBtn()
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊取消按鈕
function CommonQuery_Type6_Controller.DoCancelBtn()
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊特殊按鈕
function CommonQuery_Type6_Controller.DoSpecialBtn()
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end
--- 新Data的介面Init
function CommonQuery_Type6_Controller.NewDataInit()
    for _GroupIdx = 1, ATRRIBUTE_NUM do
        for _DirectIdx = 1, 2 do
            this.m_tran_AttributeUnits[_GroupIdx][_DirectIdx].gameObject:SetActive(this.m_CustomData.m_Attribute_Dir==_DirectIdx)
        end
    end
    -- 群組物件設定
    this.m_Gobj_Group_Icon2.gameObject:SetActive(this.m_CustomData.m_Item_Count>3)
    -- Icon設定
    for i = 1, this.m_ItemIconMax do
        local _ShouldShow = i <= this.m_CustomData.m_Item_Count
        this.m_Gobj_Icon_Items[i].gameObject:SetActive(_ShouldShow)
        this.m_TMP_ItemCount[i].transform.gameObject:SetActive(_ShouldShow)
        if _ShouldShow then
            this.m_tIcon_Items[i]:RefreshIcon(this.m_CustomData.m_tItemIdx[i])
            this.m_tIcon_Items[i]:SetMask(false)
            this.m_tIcon_Items[i]:SetClickTwice(false)
            if this.m_CustomData.m_ShowItemNeed  then
                local _BagNum  = BagMgr.GetItemInBagAmount(this.m_CustomData.m_tItemIdx[i])
                local _NeedNum = this.m_CustomData.m_tItemNeed[i]
                this.m_tIcon_Items[i]:SetCount(_NeedNum,this.m_CustomData.m_UnreachNeedUseRedNumber and _BagNum < _NeedNum and "R" or "W" )
            end
        end
    end
    -- 取消按鈕設定
    this.m_Btn_Cancel.gameObject:SetActive(this.m_CustomData.m_HaveCancelBtn)
end
--- 刷新物品持有數量文字
function CommonQuery_Type6_Controller.UpdateItemCount()
    -- 開關LayOutGroup 群組
    local _ActiveNum = math.ceil(this.m_CustomData.m_Item_Count/3)
    local _MaxNum  = math.ceil(this.m_ItemIconMax/3)
    for i = 1,_MaxNum  do
        this.m_Gobj_Group_ItemCount[i].gameObject:SetActive(this.m_CustomData.m_ShowHaveAmount and i<=  _ActiveNum)
    end
    -- 設定文字
    if not this.m_CustomData.m_ShowHaveAmount then return end
    for i = 1, this.m_CustomData.m_Item_Count do
        local _BagAmount = BagMgr.GetItemInBagAmount(this.m_CustomData.m_tItemIdx[i])
        local _Enough = _BagAmount >= this.m_CustomData.m_tItemNeed[i]
        if _BagAmount <=0 then
            this.m_TMP_ItemCount[i].text = GString.StringWithStyle(TextData.Get(20111025),"RO") -- 20111025 未持有
        elseif not _Enough then
            this.m_TMP_ItemCount[i].text = GString.StringWithStyle(GString.Format(TextData.Get(20111024),GString.StringWithStyle(_BagAmount,"RO_01")),"RO") -- 20111024 持有數：
        else
            this.m_TMP_ItemCount[i].text = GString.Format(TextData.Get(20111024),GString.StringWithStyle(_BagAmount,"WO_01")) -- 20111024 持有數
        end
    end
end

--- 點不同道具要刷新的介面資訊 預設要選一個  因為屬性和屬性數值不能空
function CommonQuery_Type6_Controller.UpdateSelectUI(iSelectIdx)

    CommonQuery_Type6_Controller.SetSelect(iSelectIdx)
    CommonQuery_Type6_Controller.UpdateAttributeUnit(iSelectIdx)
    CommonQuery_Type6_Controller.UpdateBtnGroup(iSelectIdx)
    CommonQuery_Type6_Controller.ForceUpdateContentSize()
end
--- 設定當前選擇物品 與 SelectIdx
function CommonQuery_Type6_Controller.SetSelect(iSelectIdx)
    this.m_SelectIdx = iSelectIdx
    this.m_SelectItemIdx = this.m_CustomData.m_tItemIdx[iSelectIdx]
    IconMgr.SelectIcon(this.m_tIcon_Items[iSelectIdx], false)
    UIMgr.OpenIconName(this.m_CustomData.m_ShowItemName)

    for i = 1, this.m_CustomData.m_Item_Count do
        local _NotHaveItem = BagMgr.GetItemInBagAmount(this.m_CustomData.m_tItemIdx[i])<=0 and this.m_CustomData.m_ShowMaskByAmount
        this.m_tIcon_Items[i]:SetMask(_NotHaveItem)
    end

end
--- 取得確定點選時所指定的物品 與 SelectIdx
function CommonQuery_Type6_Controller.GetSelectItem()
    return this.m_SelectItemIdx,this.m_SelectIdx
end
--- 刷新屬性名稱 數值
--- @param iSelectIdx 當前選中第幾個物品
function CommonQuery_Type6_Controller.UpdateAttributeUnit(iSelectIdx)
    --- 當前選中的物品 屬性區有幾個
    local _SelectAttrbuteNum = 1

    if this.m_CustomData.m_tAttributeTitles==nil or this.m_CustomData.m_tAttributeTitles[iSelectIdx]==nil or this.m_CustomData.m_tAttributeOriginValues ==nil or this.m_CustomData.m_tAttributeOriginValues[iSelectIdx]==nil then
        for _GroupIdx = 1, ATRRIBUTE_NUM do
            this.m_Gobjs_Group_AttributeChange[_GroupIdx].gameObject:SetActive(false)
        end
        return
    end
    --- 標題內文設定
    if type(this.m_CustomData.m_tAttributeTitles[iSelectIdx])=="table" then --標題給Table
        --- @param _GroupIdx 第幾條屬性
        for _GroupIdx = 1, ATRRIBUTE_NUM do
            if this.m_CustomData.m_tAttributeTitles[iSelectIdx][_GroupIdx] ~=nil then --有標題
                if this.m_CustomData.m_tAttributeOriginValues[iSelectIdx][_GroupIdx] ==nil then --有標題沒內文
                    D.Log("[CommonQuery_Type6] SelectIdx:"..iSelectIdx.." Attribute:".._GroupIdx.." has title without content")
                    this.m_Gobjs_Group_AttributeChange[_GroupIdx].gameObject:SetActive(false)
                else --有標題有內文
                    this.m_Gobjs_Group_AttributeChange[_GroupIdx].gameObject:SetActive(true)

                    --標題
                    this.m_TMP_AttributeTitles[_GroupIdx][this.m_CustomData.m_Attribute_Dir].text = this.m_CustomData.m_tAttributeTitles[iSelectIdx][_GroupIdx]

                     --是否顯示 A->B 內文變化
                    if this.m_CustomData.m_tAttributeFinalValues~=nil and this.m_CustomData.m_tAttributeFinalValues[iSelectIdx]~=nil and this.m_CustomData.m_tAttributeFinalValues[iSelectIdx][_GroupIdx] ~=nil then
                        this.m_TMP_AttributeContents[_GroupIdx][this.m_CustomData.m_Attribute_Dir].text = GString.Format(TextData.Get(20102022),
                        GString.DoublePercentage(this.m_CustomData.m_tAttributeOriginValues[iSelectIdx][_GroupIdx]),GString.DoublePercentage(this.m_CustomData.m_tAttributeFinalValues[iSelectIdx][_GroupIdx]))

                    else --不顯示變化
                        this.m_TMP_AttributeContents[_GroupIdx][this.m_CustomData.m_Attribute_Dir].text = this.m_CustomData.m_tAttributeOriginValues[iSelectIdx][_GroupIdx]
                    end
                end
            else --沒有標題
                this.m_Gobjs_Group_AttributeChange[_GroupIdx].gameObject:SetActive(false)
            end
        end
    else --單一屬性沒送table
        for _GroupIdx = 1, ATRRIBUTE_NUM do
            if _GroupIdx ~= 1 then
                this.m_Gobjs_Group_AttributeChange[_GroupIdx].gameObject:SetActive(false)
            end
        end

        if type(this.m_CustomData.m_tAttributeOriginValues[iSelectIdx]) =="table" then
            D.Log("[CommonQuery_Type6] SelectIdx:"..iSelectIdx.." has single title but table content")
            this.m_Gobjs_Group_AttributeChange[1].gameObject:SetActive(false)
        else
            this.m_Gobjs_Group_AttributeChange[1].gameObject:SetActive(true)
            --標題
            this.m_TMP_AttributeTitles[1][this.m_CustomData.m_Attribute_Dir].text = this.m_CustomData.m_tAttributeTitles[iSelectIdx]

            --是否顯示 A->B 內文變化
            if this.m_CustomData.m_tAttributeFinalValues~=nil and this.m_CustomData.m_tAttributeFinalValues[iSelectIdx]~=nil and type(this.m_CustomData.m_tAttributeFinalValues[iSelectIdx])~="table" then
                this.m_TMP_AttributeContents[1][this.m_CustomData.m_Attribute_Dir].text = GString.Format(TextData.Get(20102022),
                GString.DoublePercentage(this.m_CustomData.m_tAttributeOriginValues[iSelectIdx]),GString.DoublePercentage(this.m_CustomData.m_tAttributeFinalValues[iSelectIdx]))

            else --不顯示變化
                this.m_TMP_AttributeContents[1][this.m_CustomData.m_Attribute_Dir].text = this.m_CustomData.m_tAttributeOriginValues[iSelectIdx]
            end
        end
    end
end
--- 刷新確認按鈕
function CommonQuery_Type6_Controller.UpdateBtnGroup(iSelectIdx)
    local _BagAmount = BagMgr.GetItemInBagAmount(this.m_CustomData.m_tItemIdx[iSelectIdx])
    local _Enough = _BagAmount >= this.m_CustomData.m_tItemNeed[iSelectIdx]
    -- 設定確認按鈕
    if not _Enough and this.m_CustomData.m_CheckConfirmNeed then
        this.m_Btn_Confirm:SetDisable()
    else
        this.m_Btn_Confirm:SetEnable()
    end
end
