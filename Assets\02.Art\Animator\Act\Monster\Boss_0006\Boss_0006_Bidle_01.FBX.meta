fileFormatVersion: 2
guid: 00bb553d612831c4581475289bfa98c0
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 L Calf
    100008: Bip001 L Clavicle
    100010: Bip001 L Finger0
    100012: Bip001 L Finger01
    100014: Bip001 L Finger02
    100016: Bip001 L Finger1
    100018: Bip001 L Finger11
    100020: Bip001 L Finger2
    100022: Bip001 L Finger21
    100024: Bip001 L Foot
    100026: Bip001 L Forearm
    100028: Bip001 L Hand
    100030: Bip001 L Thigh
    100032: Bip001 L Toe0
    100034: Bip001 L Toe01
    100036: Bip001 L Toe1
    100038: Bip001 L Toe11
    100040: Bip001 L Toe2
    100042: Bip001 L Toe21
    100044: Bip001 L Toe3
    100046: Bip001 L Toe31
    100048: Bip001 L UpperArm
    100050: Bip001 Neck
    100052: Bip001 Neck1
    100054: Bip001 Pelvis
    100056: Bip001 R Calf
    100058: Bip001 R Clavicle
    100060: Bip001 R Finger0
    100062: Bip001 R Finger01
    100064: Bip001 R Finger02
    100066: Bip001 R Finger1
    100068: Bip001 R Finger11
    100070: Bip001 R Finger2
    100072: Bip001 R Finger21
    100074: Bip001 R Foot
    100076: Bip001 R Forearm
    100078: Bip001 R Hand
    100080: Bip001 R Thigh
    100082: Bip001 R Toe0
    100084: Bip001 R Toe01
    100086: Bip001 R Toe1
    100088: Bip001 R Toe11
    100090: Bip001 R Toe2
    100092: Bip001 R Toe21
    100094: Bip001 R Toe3
    100096: Bip001 R Toe31
    100098: Bip001 R UpperArm
    100100: Bip001 Spine
    100102: Bip001 Spine1
    100104: Bone013
    100106: Bone014
    100108: Bone015
    100110: Bone016
    100112: Bone017
    100114: Bone018
    100116: Bone025
    100118: Bone026
    100120: Bone027
    100122: Bone028
    100124: Bone029
    100126: Bone030
    100128: Bone031
    100130: Bone032
    100132: Bone033
    100134: Bone034
    100136: Bone052
    100138: Bone053
    100140: Bone054
    100142: Bone055
    100144: Bone056
    100146: Bone062
    100148: Bone063
    100150: Bone064
    100152: Bone065
    100154: Bone066
    100156: Bone068
    100158: Bone069
    100160: Bone070
    100162: Bone071
    100164: Bone072
    100166: Bone073
    100168: Bone074
    100170: Bone075
    100172: Bone076
    100174: Bone077
    100176: Bone078
    100178: Bone079
    100180: Bone080
    100182: Bone081
    100184: Bone082
    100186: Bone083
    100188: Bone084
    100190: Bone085
    100192: Bone086
    100194: Bone087
    100196: Bone088
    100198: Bone089
    100200: Bone090
    100202: Bone091
    100204: Bone092
    100206: Bone093
    100208: Bone094
    100210: Bone095
    100212: Bone096
    100214: Bone097
    100216: Bone098
    100218: Bone099
    100220: Bone100
    100222: Bone101
    100224: Bone102
    100226: Bone103
    100228: Bone104
    100230: Bone105
    100232: Bone106
    100234: Bone107
    100236: Bone108
    100238: Bone109
    100240: Bone110
    100242: Bone111
    100244: Bone112
    100246: Bone113
    100248: Bone114
    100250: Bone115
    100252: //RootNode
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 L Calf
    400008: Bip001 L Clavicle
    400010: Bip001 L Finger0
    400012: Bip001 L Finger01
    400014: Bip001 L Finger02
    400016: Bip001 L Finger1
    400018: Bip001 L Finger11
    400020: Bip001 L Finger2
    400022: Bip001 L Finger21
    400024: Bip001 L Foot
    400026: Bip001 L Forearm
    400028: Bip001 L Hand
    400030: Bip001 L Thigh
    400032: Bip001 L Toe0
    400034: Bip001 L Toe01
    400036: Bip001 L Toe1
    400038: Bip001 L Toe11
    400040: Bip001 L Toe2
    400042: Bip001 L Toe21
    400044: Bip001 L Toe3
    400046: Bip001 L Toe31
    400048: Bip001 L UpperArm
    400050: Bip001 Neck
    400052: Bip001 Neck1
    400054: Bip001 Pelvis
    400056: Bip001 R Calf
    400058: Bip001 R Clavicle
    400060: Bip001 R Finger0
    400062: Bip001 R Finger01
    400064: Bip001 R Finger02
    400066: Bip001 R Finger1
    400068: Bip001 R Finger11
    400070: Bip001 R Finger2
    400072: Bip001 R Finger21
    400074: Bip001 R Foot
    400076: Bip001 R Forearm
    400078: Bip001 R Hand
    400080: Bip001 R Thigh
    400082: Bip001 R Toe0
    400084: Bip001 R Toe01
    400086: Bip001 R Toe1
    400088: Bip001 R Toe11
    400090: Bip001 R Toe2
    400092: Bip001 R Toe21
    400094: Bip001 R Toe3
    400096: Bip001 R Toe31
    400098: Bip001 R UpperArm
    400100: Bip001 Spine
    400102: Bip001 Spine1
    400104: Bone013
    400106: Bone014
    400108: Bone015
    400110: Bone016
    400112: Bone017
    400114: Bone018
    400116: Bone025
    400118: Bone026
    400120: Bone027
    400122: Bone028
    400124: Bone029
    400126: Bone030
    400128: Bone031
    400130: Bone032
    400132: Bone033
    400134: Bone034
    400136: Bone052
    400138: Bone053
    400140: Bone054
    400142: Bone055
    400144: Bone056
    400146: Bone062
    400148: Bone063
    400150: Bone064
    400152: Bone065
    400154: Bone066
    400156: Bone068
    400158: Bone069
    400160: Bone070
    400162: Bone071
    400164: Bone072
    400166: Bone073
    400168: Bone074
    400170: Bone075
    400172: Bone076
    400174: Bone077
    400176: Bone078
    400178: Bone079
    400180: Bone080
    400182: Bone081
    400184: Bone082
    400186: Bone083
    400188: Bone084
    400190: Bone085
    400192: Bone086
    400194: Bone087
    400196: Bone088
    400198: Bone089
    400200: Bone090
    400202: Bone091
    400204: Bone092
    400206: Bone093
    400208: Bone094
    400210: Bone095
    400212: Bone096
    400214: Bone097
    400216: Bone098
    400218: Bone099
    400220: Bone100
    400222: Bone101
    400224: Bone102
    400226: Bone103
    400228: Bone104
    400230: Bone105
    400232: Bone106
    400234: Bone107
    400236: Bone108
    400238: Bone109
    400240: Bone110
    400242: Bone111
    400244: Bone112
    400246: Bone113
    400248: Bone114
    400250: Bone115
    400252: //RootNode
    7400000: Boss_0006_Bidle_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Boss_0006_Bidle_01
      takeName: Boss_0006_Bidle_01
      firstFrame: 0
      lastFrame: 48
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: d218384b52d5b8948826ad90fd4440d5,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
