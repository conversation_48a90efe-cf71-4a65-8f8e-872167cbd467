﻿//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2024 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------
//
//=====================================================================

namespace BattleEditor
{
    using GameTools.Json;
    using GameTools.Log;
    using LuaInterface;
    using System;
    using System.Collections.Generic;
    using System.Collections;
    using System.Linq;
    using TMPro;
    using UnityEditor;
    using UnityEngine;
    using UnityEngine.Events;
    using UnityEngine.EventSystems;
    using UnityEngine.UI;
    using GameTools.UIExtension;
    using GameTools.Enum;



    /// <summary>
    /// 戰鬥編輯器 Edit 頁籤
    /// <AUTHOR>
    /// @modify KaneLee
    /// @telephone #2150
    /// @version 1.0
    /// @since [黃易群俠傳M] 0.91
    /// @date 2024.7.2
    /// </summary>
    public class BEPanel_ActEdit_Controller : MonoBehaviour, IBEController
    {
        private ViewRef m_ViewRef = null;
        // 存檔按鈕 ( 存成 .Json )
        private ButtonEx m_Btn_Save;
        // 測試播放按鈕
        private ButtonEx m_Btn_Play;
        // 檢查不存在ActID按鈕
        private ButtonEx m_Btn_CheckActID;
        // 讀秒 Slider
        private Slider m_Slider_PlayedSkill;
        // 讀秒字串
        private TMP_Text m_Text_TimePass;
        // 是否播放中
        private bool m_IsPlaying = false;

        // 現在編輯中的 Act
        public int m_FocusAct
        {
            get; private set;
        }

        // 現在編輯中的 Event
        private int m_FocusEvent = -1;

        #region Skill 讀取區塊
        private TMP_InputField m_Input_SkillID;
        private TMP_InputField m_Input_SkillActID;
        private TMP_Dropdown m_Dropdown_SkillGroup;
        private TMP_Text m_Text_SkillActID;
        private ButtonEx m_Btn_LoadSkillActID;
        #endregion

        #region Act、Event 顯示列表區塊
        private GameObject m_GroupBtn_ActGroup;

        private ButtonEx m_Btn_HideUI;
        //private UIGroupButtonCtrl m_GroupBtn_EventGroup;
        public List<GameObject> m_List_SequenceGroup = new List<GameObject>();
        public List<BEAct_Controller> m_List_ActID = new List<BEAct_Controller>();
        private ButtonEx m_Button_ActAdd;
        #endregion

        #region 編輯事件細節區塊
        /// <summary>
        /// 供編輯的功能頁
        /// </summary>
        public enum EEditPanel
        {// 如果有要新增功能頁，請務必保持 Act 必須位於最後一項
            Non,
            Particle,
            Sound,
            Camera,
            PostProcess,
            Delegate,
            End,
            Act
        }

        // 編輯類型
        private GameObject m_EditUnit;
        // 編輯類型下拉選單
        private TMP_Dropdown m_Dropdown_EditType;
        // 編輯頁面保存按鈕(不會存檔，只會更新 BattleEditorMgr.Inst.Edit_SkillActData)
        private GameTools.UIExtension.ButtonEx m_Btn_SaveModify;
        // 編輯頁面關閉按鈕
        private GameTools.UIExtension.ButtonEx m_Btn_CloseEditUnit;

        // Particle UI
        // Bone
        private TMP_Dropdown m_Dropdown_Bone;
        // ParticleVec
        private TMP_Dropdown m_Dropdown_ParticleVec;
        // Camera UI
        // Ease
        private TMP_Dropdown m_Dropdown_CameraEase;

        // 讀檔間隔，避免狂按猛按出現錯誤
        private float m_LoadThreshold;

        #region 編輯介面操作

        private Dictionary<EEditPanel, GameObject> m_EditPanel = new Dictionary<EEditPanel, GameObject>();
        // 現在開啟中的頁面
        private EEditPanel m_NowEditPanel;

        /// <summary>
        /// 用事件名稱取功能頁 enum
        /// </summary>
        /// <param name="iName"> EventName </param>
        /// <returns></returns>
        public EEditPanel GetEEditPanelByName( string iName )
        {
            if ( iName == AnimationEventDict.GetEventNameByEnum( EEventType.Particle ) )
                return EEditPanel.Particle;
            else if ( iName == AnimationEventDict.GetEventNameByEnum( EEventType.Sound ) )
                return EEditPanel.Sound;
            else if ( iName == AnimationEventDict.GetEventNameByEnum( EEventType.Camera ) )
                return EEditPanel.Camera;
            else if (iName == AnimationEventDict.GetEventNameByEnum( EEventType.PostProcess))
                return EEditPanel.PostProcess;
            else if ( iName == AnimationEventDict.GetEventNameByEnum( EEventType.Delegate ) )
                return EEditPanel.Delegate;
            else if ( iName == AnimationEventDict.GetEventNameByEnum( EEventType.End ) )
                return EEditPanel.End;
            else
                return EEditPanel.Non;
        }

        private void InitEditPanel()
        {
            Transform _UnitRoot = transform.Find( "EditUnit" );
            m_EditUnit = _UnitRoot.gameObject;

            // 編輯頁面 ActID、Move
            m_EditPanel.Add( EEditPanel.Act, _UnitRoot.Find( "EditAct_Root" ).gameObject );
            // 編輯頁面 OnParticle
            m_EditPanel.Add( EEditPanel.Particle, _UnitRoot.Find( "EditParticle_Root" ).gameObject );
            // 編輯頁面 OnSound
            m_EditPanel.Add( EEditPanel.Sound, _UnitRoot.Find( "EditSound_Root" ).gameObject );
            // 編輯頁面 OnCameraEffect
            m_EditPanel.Add( EEditPanel.Camera, _UnitRoot.Find( "EditCamera_Root" ).gameObject );
            // 編輯頁面 OnPostProcess
            m_EditPanel.Add( EEditPanel.PostProcess, _UnitRoot.Find( "EditPostProcess_Root" ).gameObject );
            // 編輯頁面 OnDelegate
            m_EditPanel.Add( EEditPanel.Delegate, _UnitRoot.Find( "EditDelegate_Root" ).gameObject );
            // 編輯頁面 OnEnd
            m_EditPanel.Add( EEditPanel.End, _UnitRoot.Find( "EditEnd_Root" ).gameObject );

            // 編輯類型下拉選單
            List<string> _List_EditType = Enum.GetNames( typeof( EEditPanel ) ).ToList();
            _List_EditType.RemoveAt( _List_EditType.FindIndex( x => x == "Act" ) );

            m_Dropdown_EditType = m_ViewRef.m_Dic_Dropdown.Get( "&Dropdown_EditType" );
            AddOptions( m_Dropdown_EditType, _List_EditType );
            m_Dropdown_EditType.onValueChanged.AddListener( OnClick_SwitchModify );

            // 編輯頁面關閉按鈕
            m_Btn_CloseEditUnit = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_CloseEditUnit" );
            m_Btn_CloseEditUnit.onClick.AddListener(OnClick_CloseEditUnit);
            // 編輯頁面保存按鈕
            m_Btn_SaveModify = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_SaveEdit" );
            m_Btn_SaveModify.onClick.AddListener(OnClick_SaveModify);

            // Particle
            // EBone
            m_Dropdown_Bone = m_ViewRef.m_Dic_Dropdown.Get( "&Dropdown_Bone" );
            AddOptions( m_Dropdown_Bone, Enum.GetNames( typeof( EBone ) ).ToList() );

            // EParticleVector
            m_Dropdown_ParticleVec = m_ViewRef.m_Dic_Dropdown.Get( "&Dropdown_ParticleVec" );
            AddOptions( m_Dropdown_ParticleVec, Enum.GetNames( typeof( EParticleVec ) ).ToList() );

            //Camera
            // Ease
            m_Dropdown_CameraEase = m_ViewRef.m_Dic_Dropdown.Get( "&Dropdown_CameraEase" );

            m_Input_SkillActID.onValueChanged.AddListener( UpdateEditActID );

            var _Toggle_Loop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_IsLoop" );
            _Toggle_Loop.onValueChanged.AddListener( OnClick_LoopSwitch );

            var _Toggle_ParticleLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_ParticleLoop" );
            UnityAction<bool> _ParticleLoop = x =>
            {
                OnClick_EventLoopSwitch( EEditPanel.Particle, x );
            };
            _Toggle_ParticleLoop.onValueChanged.AddListener( _ParticleLoop );

            var _Toggle_SoundLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_SoundLoop" );
            UnityAction<bool> _SoundLoop = x =>
            {
                OnClick_EventLoopSwitch( EEditPanel.Sound, x );
            };
            _Toggle_SoundLoop.onValueChanged.AddListener( _SoundLoop );

            var _Toggle_CameraLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_CameraLoop" );
            UnityAction<bool> _CameraLoop = x =>
            {
                OnClick_EventLoopSwitch( EEditPanel.Camera, x );
            };
            _Toggle_CameraLoop.onValueChanged.AddListener( _CameraLoop );

            var _Button_CreateOnEnd = m_ViewRef.m_Dic_ButtonEx.Get( "&Button_CreateOnEnd" );
            _Button_CreateOnEnd.onClick.AddListener(OnClick_CreateOnEnd);
        }

        public void UpdateEditActID( string text )
        {
            var _Data = BattleEditorMgr.Inst.Edit_SkillActData;
            _Data.m_SkillActID = ushort.Parse( text );
            BattleEditorMgr.Inst.Edit_SkillActData = _Data;
        }

        /// <summary>
        /// 顯示各事件介面
        /// </summary>
        /// <param name="iPanel"></param>
        public void ShowEditPanel( S_ActData iData, int iIndex )
        {
            EEditPanel _PanelType = EEditPanel.Act;

            CloseAllEditUnit();

            m_FocusAct = iIndex;
            // 更新一下 Animator，避免穿脫裝後的變動
            BattleEditorMgr.Inst.m_CurrentAnimator = BattleEditorMgr.Inst.m_CurrentModel.GetComponent<Animator>().runtimeAnimatorController;
            RefreshActPanelUI( iData );

            OpenEditUnit( _PanelType );

            D.Log( $"[BattleEditor] 現在編輯中的索引，Act Index: {m_FocusAct}, Event Index: {m_FocusEvent}" );
        }

        public void ShowEditPanel( IAnimationEventProduct iEvent, int iActIndex, int iEventIndex )
        {
            EEditPanel _PanelType = GetEEditPanelByName( iEvent.m_Name );

            if ( !m_EditUnit.activeSelf )
                m_EditUnit.SetActive( true );

            CloseAllEditUnit();

            m_FocusAct = iActIndex;
            m_FocusEvent = iEventIndex;

            OpenEditUnit( _PanelType );
            RefreshEventPanelUI( _PanelType, iEvent );

            D.Log( $"[BattleEditor] 現在編輯中的索引，Act Index: {m_FocusAct}, Event Index: {m_FocusEvent}" );
        }

        private void CloseAllEditUnit()
        {
            m_FocusAct = -1;
            m_FocusEvent = -1;

            foreach ( var panel in m_EditPanel )
            {
                if ( panel.Value.activeSelf )
                {
                    panel.Value.SetActive( false );
                }
            }

            m_EditUnit.SetActive( false );

            m_NowEditPanel = EEditPanel.Non;
        }

        private void OnlyClosePanel()
        {
            foreach ( var panel in m_EditPanel )
            {
                if ( panel.Value.activeSelf )
                {
                    panel.Value.SetActive( false );
                }
            }

            m_NowEditPanel = EEditPanel.Non;
        }

        private void OpenEditUnit( EEditPanel iPanel )
        {
            if ( !m_EditUnit.activeSelf )
                m_EditUnit.SetActive( true );

            if ( m_EditPanel.ContainsKey( iPanel ) )
                m_EditPanel[ iPanel ].SetActive( true );

            // Act 不可以切類型
            // 2023/09/23 新版介面都不可以切類型，已隱藏
            /*if (iPanel == EEditPanel.Act)
            {
                m_Dropdown_EditType.gameObject.SetActive(false);
            }
            else
            {
                m_Dropdown_EditType.gameObject.SetActive(true);
            }*/

            m_NowEditPanel = iPanel;
            //m_Dropdown_EditType.SetValueWithoutNotify( (int)m_NowEditPanel );

            //D.Log($"[BattleEditor] 開啟編輯頁面 Now Type: {m_NowEditPanel}, Edit Type: {m_Dropdown_EditType.captionText.text}");
        }
        #endregion

        /// <summary>
        /// 刷新Act編輯介面的 UI
        /// </summary>
        /// <param name="iData"></param>
        private void RefreshActPanelUI( S_ActData iData )
        {
#if UNITY_EDITOR
            m_ViewRef.m_Dic_TMPText.Get( "&Text_ActID_ActionSequence" ).text = iData.m_ActionSequence.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActID_ConditionalTickID" ).text = iData.m_ConditionalTickID.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActID" ).text = iData.m_ActID.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActSpeed" ).text = iData.m_ActSpeed.ToString();
            m_ViewRef.m_Dic_TMPText.Get( "&Text_Content_ActTime" ).text = iData.m_ActTime.ToString();
            m_ViewRef.m_Dic_TMPText.Get( "&Text_Content_ActMaxTime" ).text = BattleEditorMgr.Inst.GetClipLength().ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveDistance" ).text = iData.m_MoveDistance.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveTiming" ).text = iData.m_MoveStartTime.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveTime" ).text = iData.m_MoveTime.ToString();
            m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveCurve" ).text = iData.m_CurveNum.ToString();
            m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_IsLoop" ).isOn = iData.m_IsLoop;
            try
            {
                var _LoopDetail = m_EditPanel[ EEditPanel.Act ].transform.Find( "LoopUnit" ).Find( "LoopDetail" );
                _LoopDetail.gameObject.SetActive( iData.m_IsLoop );
            }
            catch ( Exception e )
            {
                D.LogError( e );
                throw;
            }

            if ( iData.m_IsLoop )
            {
                m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_LoopTime" ).text = iData.m_LoopDuration.ToString();
            }
#endif
        }

        /// <summary>
        /// 刷新各事件介面 UI
        /// </summary>
        /// <param name="iPanel"> 功能頁 Enum </param>
        /// <param name="iEvent"> 事件 </param>
        private void RefreshEventPanelUI( EEditPanel iPanel, IAnimationEventProduct iEvent )
        {
            switch ( iPanel )
            {
                case EEditPanel.Particle:
                    if ( iEvent is BEOnParticleEvent _particle )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleEvent_ConditionalTickID" ).text = _particle.m_TickID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleID" ).text = _particle.m_ID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleTiming" ).text = _particle.m_Timing.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleSpeed" ).text = _particle.m_Speed.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleScale" ).text = _particle.m_Scale.ToString();
                        m_Dropdown_Bone.value = EnumHelper.EnumValueToIndex( (EBone)_particle.m_BonePos );
                        m_Dropdown_ParticleVec.value = _particle.m_Vector;
                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_isFollow" ).isOn = _particle.m_isFollow;
                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_isFollowRotation" ).isOn = _particle.m_isFollowRotation;
                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_ParticleLoop" ).isOn = _particle.m_isLoop;
                        var _InputField = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleLoop" );
                        _InputField.text = _particle.m_LoopTimes.ToString();
                        _InputField.gameObject.SetActive( _particle.m_isLoop );
                    }
                    break;
                case EEditPanel.Sound:
                    if ( iEvent is BEOnSoundEvent _sound )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundEvent_ConditionalTickID" ).text = _sound.m_TickID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundID" ).text = _sound.m_ID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundTiming" ).text = _sound.m_Timing.ToString();

                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_SoundLoop" ).isOn = _sound.m_isLoop;
                        var _InputField = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundLoop" );
                        _InputField.text = _sound.m_LoopTimes.ToString();
                        _InputField.gameObject.SetActive( _sound.m_isLoop );
                    }
                    break;
                case EEditPanel.Camera:
                    if ( iEvent is BEOnCameraEvent _camera )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraEvent_ConditionalTickID" ).text = _camera.m_TickID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraID" ).text = _camera.m_ID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraDuration" ).text = _camera.m_Duration.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraAmp" ).text = _camera.m_Amplitude.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraFreq" ).text = _camera.m_Frequency.ToString();
                        m_Dropdown_CameraEase.value = _camera.m_EaseType;
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraTiming" ).text = _camera.m_Timing.ToString();
                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_GlobalShake" ).isOn = _camera.m_is_GlobalShake;

                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_CameraLoop" ).isOn = _camera.m_isLoop;
                        var _InputField = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraLoop" );
                        _InputField.text = _camera.m_LoopTimes.ToString();
                        _InputField.gameObject.SetActive( _camera.m_isLoop );
                    }
                    break;
                case EEditPanel.PostProcess:
                    if ( iEvent is BEOnPostProcessEvent _process )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessEvent_ConditionalTickID" ).text = _process.m_TickID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessID" ).text = _process.m_ID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessDuration" ).text = _process.m_Duration.ToString();
                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_GlobalEffect" ).isOn = _process.m_is_GlobalEffect;
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessTiming" ).text = _process.m_Timing.ToString();

                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_PostProcessLoop" ).isOn = _process.m_isLoop;
                        var _InputField = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessLoop" );
                        _InputField.text = _process.m_LoopTimes.ToString();
                        _InputField.gameObject.SetActive( _process.m_isLoop );
                    }
                    break;
                case EEditPanel.Delegate:
                    if ( iEvent is BEOnDelegateEvent _delegate )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateEvent_ConditionalTickID" ).text = _delegate.m_TickID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateID" ).text = _delegate.m_ID.ToString();
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateTiming" ).text = _delegate.m_Timing.ToString();

                        m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_DelegateLoop" ).isOn = _delegate.m_isLoop;
                        var _InputField = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateLoop" );
                        _InputField.text = _delegate.m_LoopTimes.ToString();
                        _InputField.gameObject.SetActive( _delegate.m_isLoop );
                    }
                    break;
                case EEditPanel.End:
                    if ( iEvent is BEOnEndEvent _end )
                    {
                        m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_End" ).text = _end.m_Timing.ToString();
                    }
                    break;
                default:
                    break;
            }
        }

        #region 編輯介面 OnClick
        /// <summary>
        /// 按鈕事件
        /// 關閉編輯頁面
        /// </summary>
        private void OnClick_CloseEditUnit(BaseEventData eventData)
        {
            CloseAllEditUnit();
        }

        /// <summary>
        /// 按鈕事件
        /// 切換編輯頁面類型
        /// </summary>
        private void OnClick_SwitchModify( int iType )
        {
            if ( (EEditPanel)iType == EEditPanel.Act || (EEditPanel)iType == EEditPanel.Non )
            {
                OnlyClosePanel();
                return;
            }

            BEEventFactory _EventFactory = new BEEventFactory();
            IAnimationEventProduct _DefaultEvent = _EventFactory.CreateEvent( GetEventType_ByPanelType( (EEditPanel)iType ) );
            ShowEditPanel( _DefaultEvent, m_FocusAct, m_FocusEvent );
        }

        /// <summary>
        /// 按鈕事件
        /// 保存變更內容
        /// </summary>
        private void OnClick_SaveModify(BaseEventData eventData)
        {
#if UNITY_EDITOR
            if ( m_NowEditPanel == EEditPanel.Non )
            {
                EditorUtility.DisplayDialog( "保存失敗", "編輯頁面類型為空，請確認頁面類型是否正確", "OK" );
                return;
            }

            #region Act 保存
            // 處理 ActTab 保存行為
            if ( m_NowEditPanel == EEditPanel.Act )
            {
                if ( m_FocusAct == -1 )
                    return;

                // 當前 Focus 索引大於結構 = 有新增的資料 -> 生成空的 S_ActData 以符合長度
                while ( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Count <= m_FocusAct )
                {// 通常不會進來，在按下 +號 新增按鈕時就會添加
                    S_ActData _NewData = new S_ActData();
                    _NewData.m_Ay_Event = new List<IAnimationEventProduct>();
                    BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Add( _NewData );
                }

                // 將介面上的資訊保存到 Edit_SkillActData 中
                S_ActData _ActData = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ];
                _ActData.m_ActionSequence = ushort.Parse( m_ViewRef.m_Dic_TMPText.Get( "&Text_ActID_ActionSequence" ).text );
                _ActData.m_ConditionalTickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActID_ConditionalTickID" ).text );
                _ActData.m_ActID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActID" ).text );
                _ActData.m_ActSpeed = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ActSpeed" ).text );

                // ActTime = OnEnd 時間點
                foreach ( var evnt in _ActData.m_Ay_Event )
                {
                    if ( evnt.m_Name == AnimationEventDict.GetEventNameByEnum( EEventType.End ) )
                    {
                        _ActData.m_ActTime = evnt.m_Timing;
                        break;
                    }
                }

                //_ActData.m_ActMaxTime = BattleEditorMgr.Inst.GetClipLength();

                _ActData.m_MoveDistance = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveDistance" ).text );
                _ActData.m_MoveStartTime = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveTiming" ).text );
                _ActData.m_MoveTime = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveTime" ).text );
                _ActData.m_CurveNum = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_MoveCurve" ).text );

                _ActData.m_IsLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_IsLoop" ).isOn;
                if ( _ActData.m_IsLoop )
                    _ActData.m_LoopDuration = int.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_LoopTime" ).text );

                // 更新編輯中的資料
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] = _ActData;
                // 更新介面上的 ID
                m_List_ActID[ m_FocusAct ].m_Text_TitleActID.text = _ActData.m_ActID.ToString();
                RefreshActPanelUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] );
                EditorUtility.DisplayDialog( "保存成功", "Act 保存完成!!", "確認" );
                return;
            }
            #endregion

            #region Event 保存

            // 用工廠生產與當前編輯介面相符的產品
            BEEventFactory _Factory = new BEEventFactory();
            IAnimationEventProduct _EditData = _Factory.CreateEvent( GetEventType_ByPanelType( m_NowEditPanel ) );

            // 以當前編輯介面決定何種獲取資料方式
            switch ( m_NowEditPanel )
            {
                case EEditPanel.Particle:
                    if ( _EditData is BEOnParticleEvent _particle )
                    {
                        _particle.m_TickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleEvent_ConditionalTickID" ).text );
                        _particle.m_ID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleID" ).text );
                        _particle.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleTiming" ).text );
                        _particle.m_Speed = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleSpeed" ).text );
                        _particle.m_Scale = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleScale" ).text );
                        _particle.m_BonePos = BattleEditorMgr.Inst.GetBonePos( m_Dropdown_Bone.value );
                        _particle.m_Vector = (byte)m_Dropdown_ParticleVec.value;
                        _particle.m_isFollow = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_isFollow" ).isOn;
                        _particle.m_isFollowRotation = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_isFollowRotation" ).isOn;
                        _particle.m_isLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_ParticleLoop" ).isOn;
                        if ( _particle.m_isLoop )
                        {
                            _particle.m_LoopTimes = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleLoop" ).text );
                        }
                        else
                        {
                            _particle.m_LoopTimes = 0;
                        }
                    }
                    break;
                case EEditPanel.Sound:
                    if ( _EditData is BEOnSoundEvent _sound )
                    {
                        _sound.m_TickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundEvent_ConditionalTickID" ).text );
                        _sound.m_ID = int.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundID" ).text );
                        _sound.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundTiming" ).text );
                        _sound.m_isLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_SoundLoop" ).isOn;
                        if ( _sound.m_isLoop )
                        {
                            _sound.m_LoopTimes = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundLoop" ).text );
                        }
                        else
                        {
                            _sound.m_LoopTimes = 0;
                        }
                    }
                    break;
                case EEditPanel.Camera:
                    if ( _EditData is BEOnCameraEvent _camera )
                    {
                        _camera.m_TickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraEvent_ConditionalTickID" ).text );
                        _camera.m_ID = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraID" ).text );
                        _camera.m_Duration = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraDuration" ).text );
                        _camera.m_Amplitude = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraAmp" ).text );
                        _camera.m_Frequency = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraFreq" ).text );
                        _camera.m_EaseType = (byte)m_Dropdown_CameraEase.value;
                        _camera.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraTiming" ).text );
                        _camera.m_is_GlobalShake = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_GlobalShake" ).isOn;

                        _camera.m_isLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_CameraLoop" ).isOn;
                        if ( _camera.m_isLoop )
                        {
                            _camera.m_LoopTimes = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraLoop" ).text );
                        }
                        else
                        {
                            _camera.m_LoopTimes = 0;
                        }
                    }
                    break;
                case EEditPanel.PostProcess:
                    if ( _EditData is BEOnPostProcessEvent _process )
                    {
                        _process.m_TickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessEvent_ConditionalTickID" ).text );
                        _process.m_ID = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessID" ).text );
                        _process.m_Duration = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessDuration" ).text );
                        _process.m_is_GlobalEffect = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_GlobalEffect" ).isOn;
                        _process.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessTiming" ).text );

                        _process.m_isLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_PostProcessLoop" ).isOn;
                        if ( _process.m_isLoop )
                        {
                            _process.m_LoopTimes = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_PostProcessLoop" ).text );
                        }
                        else
                        {
                            _process.m_LoopTimes = 0;
                        }
                    }
                    break;
                case EEditPanel.Delegate:
                    if ( _EditData is BEOnDelegateEvent _delegate )
                    {
                        _delegate.m_TickID = ushort.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateEvent_ConditionalTickID" ).text );
                        _delegate.m_ID = int.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateID" ).text );
                        _delegate.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateTiming" ).text );
                        _delegate.m_isLoop = m_ViewRef.m_Dic_Toggle.Get( "&Button_Toggle_DelegateLoop" ).isOn;
                        if ( _delegate.m_isLoop )
                        {
                            _delegate.m_LoopTimes = byte.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_DelegateLoop" ).text );
                        }
                        else
                        {
                            _delegate.m_LoopTimes = 0;
                        }
                    }
                    break;
                case EEditPanel.End:
                    if ( _EditData is BEOnEndEvent _end )
                    {
                        _end.m_Timing = float.Parse( m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_End" ).text );
                    }
                    break;
                default:
                    break;
            }

            if ( !BattleEditorMgr.Inst.CheckEventLength( _EditData ) )
                return;

            try
            {
                // 當前 Focus 索引大於結構 = 有新增的資料 -> 添加預設 IAnimationEventProduct 以符合長度
                while ( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ].m_Ay_Event.Count <= m_FocusEvent )
                {// 通常不會進來，在按下 +號 新增按鈕時就會添加
                    IAnimationEventProduct _DefaultEvent = _Factory.CreateEvent( EEventType.Non );
                    BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ].m_Ay_Event.Add( _DefaultEvent );
                }

                // 保存 Event
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ].m_Ay_Event[ m_FocusEvent ] = _EditData;
                if ( m_NowEditPanel == EEditPanel.End )
                {
                    var _ActData = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ];
                    _ActData.m_ActTime = _EditData.m_Timing;
                    BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] = _ActData;
                }

                // 重新排序 Data
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ].m_Ay_Event.Sort( ( a, b ) => a.m_Timing.CompareTo( b.m_Timing ) );
                // 重整 UI
                m_List_ActID[ m_FocusAct ].m_ActEventGroupCtrl.RefreshEvent( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ].m_Ay_Event );
                // 保存完 Event 索引可能會變動，強關編輯頁面確保編輯者會再點一次 Tab 更新索引
                CloseAllEditUnit();
            }
            catch ( Exception e )
            {
                EditorUtility.DisplayDialog( "保存失敗", "詳情請見錯誤訊息", "OK" );
                D.LogError( $"[BattleEditMgr] 保存失敗! Error Msg: {e}" );
            }
            #endregion
#endif
        }

        private void OnClick_LoopSwitch( bool iIsOn )
        {
            try
            {
                var _LoopDetail = m_EditPanel[ EEditPanel.Act ].transform.Find( "LoopUnit" ).Find( "LoopDetail" );
                _LoopDetail.gameObject.SetActive( iIsOn );
            }
            catch ( Exception e )
            {
                D.LogError( e );
                throw;
            }

            if ( iIsOn )
            {
                var _ActData = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ];
                m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_LoopTime" ).text = _ActData.m_LoopDuration.ToString();
            }
        }

        private void OnClick_EventLoopSwitch( EEditPanel iPanelType, bool iIsOn )
        {
            switch ( iPanelType )
            {
                case EEditPanel.Particle:
                    m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_ParticleLoop" ).gameObject.SetActive( iIsOn );
                    break;
                case EEditPanel.Sound:
                    m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SoundLoop" ).gameObject.SetActive( iIsOn );
                    break;
                case EEditPanel.Camera:
                    m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_CameraLoop" ).gameObject.SetActive( iIsOn );
                    break;
                default:
                    break;
            }
        }

        private void OnClick_CreateOnEnd(BaseEventData eventData)
        {
#if UNITY_EDITOR
            var _NowActData = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ];
            int _length = (int)( BattleEditorMgr.Inst.GetClipLength() * 100 - 1 ); // 比最大長度小一點，避免長度超過或OnEnd被跳過
            float _MaxLength = (float)( _length / 100.0 );
            int _OnEndIndex = -1;
            // 檢查有沒有OnEnd
            for ( int i = 0; i < _NowActData.m_Ay_Event.Count; i++ )
            {
                if ( _NowActData.m_Ay_Event[ i ].m_Name.Equals( AnimationEventDict.GetEventNameByEnum( EEventType.End ) ) )
                {
                    if ( EditorUtility.DisplayDialog( "警告", "已存在 OnEnd 事件，是否要覆蓋？", "確認", "取消" ) )
                    {
                        _OnEndIndex = i;
                    }

                    break;
                }
            }

            // 已存在 OnEnd 事件
            if ( _OnEndIndex > -1 )
            {
                var _Data = _NowActData.m_Ay_Event[ _OnEndIndex ] as BEOnEndEvent;
                if ( _Data == null )
                {
                    D.LogError( "[BattleEditor] 生成 OnEnd 事件失敗" );
                    return;
                }

                _Data.m_Timing = _MaxLength;
                _NowActData.m_Ay_Event[ _OnEndIndex ] = _Data;
                _NowActData.m_ActTime = _Data.m_Timing;
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] = _NowActData;
            }
            // 沒有 OnEnd 事件
            else
            {
                BEEventFactory _Factory = new BEEventFactory();
                var _OnEndEvent = _Factory.CreateEvent( EEventType.End );

                if ( _OnEndEvent is BEOnEndEvent _onEnd )
                {
                    _onEnd.m_Timing = _MaxLength;
                }
                else
                {
                    D.LogError( "[BattleEditor] 生成 OnEnd 事件失敗，事件工廠錯誤" );
                    return;
                }

                _NowActData.m_Ay_Event.Add( _OnEndEvent );
                _NowActData.m_ActTime = _OnEndEvent.m_Timing;
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] = _NowActData;
                UpdateUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData );
            }

            RefreshActPanelUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ m_FocusAct ] );
#endif
        }

        private EEventType GetEventType_ByPanelType( EEditPanel iPanel )
        {
            if ( iPanel == EEditPanel.Particle )
                return EEventType.Particle;
            else if ( iPanel == EEditPanel.Sound )
                return EEventType.Sound;
            else if ( iPanel == EEditPanel.Camera )
                return EEventType.Camera;
            else if (iPanel == EEditPanel.PostProcess)
                return EEventType.PostProcess;
            else if (iPanel == EEditPanel.Delegate)
                return EEventType.Delegate;
            else if ( iPanel == EEditPanel.End )
                return EEventType.End;

            return EEventType.Non;
        }
        #endregion

        #endregion

        private void Awake()
        {
            // 獲取各種 ref
            m_ViewRef = BattleEditorMgr.Inst.m_ViewRef;

            if ( m_ViewRef == null )
            {
                D.LogWarning( "[BattleEditor] BEPanel_ActEdit_Controller Get ViewRef failed." );
                return;
            }

            m_Input_SkillID = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SkillID" );
            m_Input_SkillActID = m_ViewRef.m_Dic_TMPInputField.Get( "&InputField_SkillActID" );
            m_Text_SkillActID = m_ViewRef.m_Dic_TMPText.Get( "&Text_Content_SkillActID" );
            m_Btn_LoadSkillActID = m_ViewRef.m_Dic_ButtonEx.Get( "&Button_LoadSkillActID" );

            m_GroupBtn_ActGroup = m_ViewRef.m_Dic_Trans.Get( "&GroupTab_ActGroup" ).gameObject;
            m_Btn_HideUI = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_HideGroupUI" );
            m_Button_ActAdd = m_ViewRef.m_Dic_ButtonEx.Get( "&Button_ActAdd" );

            m_Btn_Save = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_Save" );
            m_Btn_Play = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_Play" );
            m_Btn_CheckActID = m_ViewRef.m_Dic_ButtonEx.Get( "&Btn_CheckActID" );

            m_Slider_PlayedSkill = m_ViewRef.m_Dic_Slider.Get( "&Slider_PlaySkill" );
            m_Text_TimePass = m_Slider_PlayedSkill.transform.Find( "Text_TimePassed" ).GetComponent<TMP_Text>();

            // 取得各功能的編輯介面
            InitEditPanel();

            // ESkillGroup
            m_Dropdown_SkillGroup = m_ViewRef.m_Dic_Dropdown.Get( "&Dropdown_SkillGroup" );
            AddOptions( m_Dropdown_SkillGroup, Enum.GetNames( typeof( ESkillGroup ) ).ToList() );
        }

        void Update()
        {
            if ( m_IsPlaying )
            {
                m_Slider_PlayedSkill.value = Mathf.Floor( toLuaMgr.Inst.CallFunction<float>("BattleMgr.GetSkillPassTime") * 1000 );
                if ( m_Slider_PlayedSkill.value >= m_Slider_PlayedSkill.maxValue )
                {
                    m_IsPlaying = false;
                    return;
                }
            }

            if ( m_LoadThreshold > 0 )
            {
                m_LoadThreshold -= Time.deltaTime;
            }
            else
            {
                m_LoadThreshold = 0f;
            }
        }

        private void AddOptions( TMP_Dropdown iDrop, List<string> iList )
        {
            iDrop.ClearOptions();
            iDrop.AddOptions( iList );
        }

        private void Start()
        {
            // Add Listener
            m_Btn_LoadSkillActID.onClick.AddListener(OnClick_LoadSkillDataBySkillActID);

            m_Btn_Save.onClick.AddListener(OnClick_Save);

            m_Btn_Play.onClick.AddListener(OnClick_Play);

            m_Btn_CheckActID.onClick.AddListener(OnClick_CheckActID);

            m_Button_ActAdd.onClick.AddListener(OnClick_CreateDefaultActTab);

            m_Btn_HideUI.onClick.AddListener(OnClick_HideUI);

            m_Slider_PlayedSkill.onValueChanged.AddListener( x => m_Text_TimePass.text = x.ToString() );
        }

        #region 主介面 OnClick
        private void OnClick_Save(BaseEventData eventData)
        {
#if UNITY_EDITOR
            if ( !EditorUtility.DisplayDialog( "存檔", $"確定保存 SkillActID : {BattleEditorMgr.Inst.Edit_SkillActData.m_SkillActID}?，本操作會覆寫原有檔案", "確定", "取消" ) )
                return;

            // 更新 SkillActData 資訊
            S_SkillActData _SkillData = BattleEditorMgr.Inst.Edit_SkillActData;
            _SkillData.m_SkillActID = ushort.Parse( m_Input_SkillActID.text );

            if ( _SkillData.s_ActData == null )
                _SkillData.s_ActData = new List<S_ActData>();

            _SkillData.GetTotalTime();

            BattleEditorMgr.Inst.Edit_SkillActData = _SkillData;

            // 檢查存檔類型
            if ( (ESkillGroup)m_Dropdown_SkillGroup.value == ESkillGroup.None )
            {
                EditorUtility.DisplayDialog( "存檔失敗", "SkillAct 存檔類型不可以為 None，請檢查 SkillActID 下方的下拉選單", "OK" );
                return;
            }

            // 檢查存檔是否符合規範
            if ( !BattleEditorMgr.Inst.VerifyData() )
            {
                EditorUtility.DisplayDialog( "存檔失敗", "不符合存檔規範，詳情請見錯誤訊息", "OK" );
                return;
            }

            string _dir = BattleEditorMgr.m_DataPath + @$"{( (ESkillGroup)m_Dropdown_SkillGroup.value ).ToString()}\ActData_{BattleEditorMgr.Inst.Edit_SkillActData.m_SkillActID}.json";
            JsonTools.ToJsonFile( _dir, BattleEditorMgr.Inst.Edit_SkillActData, null, true );

            EditorUtility.DisplayDialog( "存檔成功", "請到 Assets/Editor/BattleEditor/Data 確認存檔內容是否正確，並使用散檔合併工具使其在遊戲中可以使用", "OK" );
#endif
        }

        private void OnClick_Play(BaseEventData eventData)
        {
            //D.Log( "[BattleEditor] Play!!!" );

            GameObject _Target = null;
            // 先檢查存檔格式對不對
            if ( BattleEditorMgr.Inst.VerifyData() )
            {
                //2024.07.15 Add by KaneLee 特效掛點有需要掛在 Target 上的話, 就顯示受擊模型
                foreach ( var _ActData in BattleEditorMgr.Inst.Edit_SkillActData.s_ActData )
                {
                    foreach ( var _Event in _ActData.m_Ay_Event )
                    {
                        if ( _Event is BEOnParticleEvent _ParticleEvent )
                        {
                            if ( _ParticleEvent.m_BonePos == (byte)EBone.TargetGround )
                            {
                                _Target = BattleEditorMgr.Inst.m_AffectModel;
                                break;
                            }
                        }
                    }
                }

                BattleEditorMgr.Inst.PlayEditAnimation( BattleEditorMgr.EEdittorPlayType.PlayBySkillActData, iTarget : _Target );
                m_Slider_PlayedSkill.maxValue = (int)( BattleEditorMgr.Inst.Edit_SkillActData.GetTotalTime() * 1000 );
                m_Slider_PlayedSkill.value = 0;
                m_IsPlaying = true;
            }
        }
        private void OnClick_CheckActID( BaseEventData iEventData )
        {
     #if UNITY_EDITOR
            LuaTable _allSkillData = toLuaMgr.Inst.CallFunction<LuaTable>("SkillData.GetAllActID");
            if( _allSkillData == null )
            {
                EditorUtility.DisplayDialog( "查詢失敗", "無法取得所有技能資料", "OK" );
                return;
            }
            else
            {
                LuaDictTable _Dict = _allSkillData.ToDictTable();
                // 存所有SkillData 有用的 SkillActID
                HashSet<double> _UseActIdSet = new HashSet<double>();
                foreach( DictionaryEntry _entry in _Dict )
                {
                    _UseActIdSet.Add( (double)_entry.Value );
                }
                // 取得資料夾內所有 ActData 檔案
                string _dataDir = "Assets/Editor/BattleEditor/Data";
                if( !System.IO.Directory.Exists( _dataDir ) )
                {
                    Debug.LogError( $"資料夾不存在: {_dataDir}" );
                    return;
                }
                string[] _files = System.IO.Directory.GetFiles( _dataDir, "ActData_*.json", System.IO.SearchOption.AllDirectories );
                for( int _i = 0; _i < _files.Length; _i++ )
                {
                    string _file = _files[ _i ];
                    // 取得檔名中的 ActID
                    string _fileName = System.IO.Path.GetFileNameWithoutExtension( _file );
                    string[] _parts = _fileName.Split( '_' );
                    // 檢查檔名格式是否正確 ActData_數字
                    if( _parts.Length < 2 )
                        continue;
                    if( double.TryParse( _parts[ 1 ], out double _actId ) )
                    {
                        if( !_UseActIdSet.Contains( _actId ) )
                        {
                            // 讀取Project內的資源,讓點擊後能跳到對應檔案
                            TextAsset _target = AssetDatabase.LoadAssetAtPath<TextAsset>(_file);
                            Debug.Log( $"{_file}: 沒有在武功表使用" ,_target);
                        }
                    }
                }
            }
     #endif
        }

        private void OnClick_LoadSkillDataBySkillActID( BaseEventData eventData )
        {
#if UNITY_EDITOR
            // 冷卻
            if( m_LoadThreshold > 0 )
                return;

            if( m_Dropdown_SkillGroup.value == 0 )
            {
                D.LogError( "[BattleEditor] 技能類型不可為 None" );
                return;
            }

            S_SkillActData _Data = BattleEditorMgr.Inst.Origin_SkillActData;
            _Data.m_SkillActID = ushort.Parse( m_Input_SkillActID.text );
            LuaTable _SkillActData = toLuaMgr.Inst.CallFunction<LuaTable>( "SkillActData.GetSkillActDataByIdx", m_Dropdown_SkillGroup.captionText.text, _Data.m_SkillActID );

            if( _SkillActData == null )
            {
                EditorUtility.DisplayDialog( "讀取失敗", $"讀取 Skill Act ID [{m_Input_SkillActID.text}] 失敗，沒有在 S_SkillActData 找到這筆資料，請確認編號或類型是否正確", "OK" );
                return;
            }

            string _ID = _SkillActData.RawGet<string, ushort>( "m_SkillActID" ).ToString();
            m_Text_SkillActID.text = _ID;

            _Data.m_TotalTime = _SkillActData.RawGet<string, float>( "m_TotalTime" );
            BattleEditorMgr.Inst.Origin_SkillActData = _Data;

            LuaTable _ActList = (LuaTable)_SkillActData[ "s_ActData" ];
            CreateActCtrl( _ActList, FinishUpdate );

            m_LoadThreshold = 1.0f;
#endif
        }

        private void OnClick_HideUI(BaseEventData eventData)
        {
            m_GroupBtn_ActGroup.SetActive( !m_GroupBtn_ActGroup.activeSelf );
            var _BG = m_ViewRef.m_Dic_RawImage.Get( "&RawImage_BG" ).gameObject;
            _BG.SetActive( !_BG.activeSelf );
        }
        #endregion

        public void RemoveActGroup( int iIdx )
        {
            if ( m_List_ActID == null )
                return;

            if (m_List_ActID[ iIdx ].Destory())
            {
                m_List_SequenceGroup.Remove( m_List_ActID[ iIdx ].m_GB_ActGroupParent );
                ResortActionSeqence();
                UpdateUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData );
            }
            m_List_ActID.RemoveAt( iIdx );
            ReAssignActGroupIdx();
        }

        /// <summary>
        /// 重分配 ActGroup 的索引
        /// </summary>
        private void ReAssignActGroupIdx()
        {
            for ( int i = 0; i < m_List_ActID.Count; i++ )
            {
                m_List_ActID[ i ].m_Index = i;
                m_List_ActID[ i ].m_ActEventGroupCtrl.m_ActIndex = i;
            }
        }

        public void CreateActCtrl( LuaTable iActList, Action iCallBack )
        {
            BattleEditorMgr.Inst.ClearSkillActData();

            S_SkillActData _SkillActData = BattleEditorMgr.Inst.Origin_SkillActData;
            for ( int i = 0; i < iActList.Length; i++ )
                _SkillActData.s_ActData.Add( new S_ActData() );

            BattleEditorMgr.Inst.Origin_SkillActData = _SkillActData;
            LuaTable _ActData = null;
            LuaTable _EventData = null;

            for ( int i = 0; i < iActList.Length; i++ )
            {// 於 ActGroupButton 底下創建與 ActList.Count 等量的 Btn_ActTab
                // 取得 List 中的 S_ActData
                _ActData = (LuaTable)iActList[ i + 1 ];
                ushort _ActionSequence = _ActData.RawGet<string, ushort>( "m_ActionSequence" );
                GameObject _GObj_ActGroup;
                BEAct_Controller _ActGroup_Ctrl;
                GameObject _ActGroup = GameObject.Find( $"&ActGroup_{_ActionSequence}" );

                // 確認 &ActGroup_{i} 的Unity GameObject是否已存在，並複製編輯模板
                if ( _ActGroup != null )
                {
                    _GObj_ActGroup = _ActGroup;
                    GameObject _ActTab_Sub = Instantiate( m_ViewRef.m_Dic_Trans.Get( "&Panel_ActTab_Sub" ).gameObject, _GObj_ActGroup.transform.Find( $"Viewport/Content" ) );

                    _ActTab_Sub.name = $"&Panel_ActTab_Sub_{m_List_ActID.Count}";
                    _ActGroup_Ctrl = new BEAct_Controller( _GObj_ActGroup, _ActTab_Sub, i, OnClick_ActFront, OnClick_ActBack, OnClick_DeleteAct, OnClick_ShowDetail );
                }
                else
                {
                    _GObj_ActGroup = Instantiate( m_ViewRef.m_Dic_Trans.Get( "&Panel_ActTab" ).gameObject, m_ViewRef.m_Dic_Trans.Get( "&Content_Act" ) );
                    _GObj_ActGroup.name = $"&ActGroup_{_ActionSequence}";
                    // 創建控制器並初始化 GObj
                    if ( !_GObj_ActGroup.activeSelf )
                        _GObj_ActGroup.SetActive( true );

                    GameObject _ActTab_Sub = _GObj_ActGroup.transform.Find( "Viewport/Content/&Panel_ActTab_Sub" ).gameObject;
                    _ActTab_Sub.name = $"&Panel_ActTab_Sub_{m_List_ActID.Count}";
                    _ActGroup_Ctrl = new BEAct_Controller( _GObj_ActGroup, _ActTab_Sub, i, OnClick_ActFront, OnClick_ActBack, OnClick_DeleteAct, OnClick_ShowDetail );
                    m_List_SequenceGroup.Add( _GObj_ActGroup );
                }

                ButtonEx m_Button_ActAdd_Derived = _GObj_ActGroup.transform.Find( "Viewport/Content/&Button_ActAdd_Derived" ).GetComponent<ButtonEx>();
                int _Index = m_List_SequenceGroup.Count - 1;
                m_Button_ActAdd_Derived.onClick.AddListener( ( BaseEventData eventData ) => { OnClick_CreateDefaultActTab_Derived( _GObj_ActGroup ); } );


                // 讀取 S_ActData 內容
                _ActGroup_Ctrl.ReadActData( _ActData );
                // 讀取 Event 內容
                _EventData = _ActData.RawGet<string, LuaTable>( "m_Ay_Event" );
                if ( _EventData.Length > 0 )
                {
                    _ActGroup_Ctrl.ReadEventData( _EventData );
                }

                // 加入 List 管理
                m_List_ActID.Add( _ActGroup_Ctrl );
                TMP_Text _TextInfo = m_ViewRef.m_Dic_TMPText.Get( "&Text_ActID_ActionSequence" );
                _TextInfo.text = m_List_SequenceGroup.Count.ToString();
            }

            UpdateUI( BattleEditorMgr.Inst.Origin_SkillActData.s_ActData );

            if ( iCallBack != null )
            {
                iCallBack();
            }
        }

        #region Act_Controller 按鈕事件
        private void OnClick_ActFront( int iIndex )
        {
            if ( iIndex == m_List_ActID.Count - 1 )
                return;

            S_ActData _Temp = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex ];
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex ] = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex + 1 ];
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex + 1 ] = _Temp;

            ResortActionSeqence();

            UpdateUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData );
        }

        private void OnClick_ActBack( int iIndex )
        {
            if ( iIndex == 0 )
                return;

            S_ActData _Temp = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex ];
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex ] = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex - 1 ];
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex - 1 ] = _Temp;

            ResortActionSeqence();

            UpdateUI( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData );
        }

        private void OnClick_DeleteAct( int iIndex )
        {
            RemoveActGroup( iIndex );
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.RemoveAt( iIndex );
        }

        private void OnClick_ShowDetail( int iIndex )
        {
            ShowEditPanel( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ iIndex ], iIndex );
        }

        #endregion

        /// <summary>
        /// Act + 號按鍵事件
        /// 添加新的預設 ActTab
        /// </summary>
        public void OnClick_CreateDefaultActTab(BaseEventData eventData)
        {
            // 複製模板
            GameObject _GObj_ActGroup = Instantiate( m_ViewRef.m_Dic_Trans.Get( "&Panel_ActTab" ).gameObject, m_ViewRef.m_Dic_Trans.Get( "&Content_Act" ) );
            _GObj_ActGroup.name = $"&ActGroup_{m_List_SequenceGroup.Count}";
            if ( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData == null )
            {
                S_SkillActData _SkillData = BattleEditorMgr.Inst.Edit_SkillActData;
                _SkillData.s_ActData = new List<S_ActData>();
                BattleEditorMgr.Inst.Edit_SkillActData = _SkillData;
            }

            // 創建控制器並初始化 GObj
            if ( !_GObj_ActGroup.activeSelf )
                _GObj_ActGroup.SetActive( true );

            GameObject _ActTab_Sub = _GObj_ActGroup.transform.Find( "Viewport/Content/&Panel_ActTab_Sub" ).gameObject;
            _ActTab_Sub.name = $"&Panel_ActTab_Sub_{m_List_ActID.Count}";
            BEAct_Controller _ActGroup_Ctrl = new BEAct_Controller( _GObj_ActGroup, _ActTab_Sub, BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Count, OnClick_ActFront, OnClick_ActBack, OnClick_DeleteAct, OnClick_ShowDetail );
            m_List_ActID.Add( _ActGroup_Ctrl );
            m_List_SequenceGroup.Add( _GObj_ActGroup );
            TMP_Text _TextInfo = m_ViewRef.m_Dic_TMPText.Get( "&Text_ActID_ActionSequence" );
            _TextInfo.text = m_List_SequenceGroup.Count.ToString();

            ButtonEx m_Button_ActAdd_Derived = _GObj_ActGroup.transform.Find( "Viewport/Content/&Button_ActAdd_Derived" ).GetComponent<ButtonEx>();
            int _Index = m_List_SequenceGroup.Count - 1;
            m_Button_ActAdd_Derived.onClick.AddListener( ( BaseEventData eventData ) => { OnClick_CreateDefaultActTab_Derived( _GObj_ActGroup ); } );

            S_ActData _DefaultData = new S_ActData();
            _DefaultData.m_Ay_Event = new List<IAnimationEventProduct>();
            _DefaultData.m_ActionSequence = (ushort)m_List_SequenceGroup.Count;
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Add( _DefaultData );

            ResortActTab();

            ReAssignActGroupIdx();
        }

        public void OnClick_CreateDefaultActTab_Derived( GameObject iActGroup )
        {
            D.Log( $"增加分支動作,Index: {iActGroup.name}" );

            // 複製模板
            GameObject _GObj_ActGroup = Instantiate( m_ViewRef.m_Dic_Trans.Get( "&Panel_ActTab_Sub" ).gameObject, iActGroup.transform.Find( $"Viewport/Content" ) );

            if ( BattleEditorMgr.Inst.Edit_SkillActData.s_ActData == null )
            {
                S_SkillActData _SkillData = BattleEditorMgr.Inst.Edit_SkillActData;
                _SkillData.s_ActData = new List<S_ActData>();
                BattleEditorMgr.Inst.Edit_SkillActData = _SkillData;
            }

            // 創建控制器並初始化 GObj
            if ( !_GObj_ActGroup.activeSelf )
                _GObj_ActGroup.SetActive( true );

            _GObj_ActGroup.name = $"&Panel_ActTab_Sub_{m_List_ActID.Count}";
            BEAct_Controller _ActGroup_Ctrl = new BEAct_Controller( iActGroup, _GObj_ActGroup, BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Count, OnClick_ActFront, OnClick_ActBack, OnClick_DeleteAct, OnClick_ShowDetail );
            m_List_ActID.Add( _ActGroup_Ctrl );

            S_ActData _DefaultData = new S_ActData();
            _DefaultData.m_Ay_Event = new List<IAnimationEventProduct>();
            _DefaultData.m_ActionSequence = (ushort)(m_List_SequenceGroup.IndexOf(iActGroup)+1);
            BattleEditorMgr.Inst.Edit_SkillActData.s_ActData.Add( _DefaultData );

            ResortSubActTab( _GObj_ActGroup.transform.parent.Find( "&Button_ActAdd_Derived" ) );

            ReAssignActGroupIdx();
        }

        /// <summary>
        /// 重整ActTab排序
        /// 將 + 號按鍵排到最後
        /// </summary>
        private void ResortActTab()
        {
            m_Button_ActAdd.transform.SetAsLastSibling();
        }

        /// <summary>
        /// 重整ActTab_Sub排序
        /// 將 + 號按鍵排到最下面
        /// </summary>
        private void ResortSubActTab( Transform iAddSubActButton )
        {
            iAddSubActButton.SetAsLastSibling();
        }

        /// <summary>
        /// 把BattleEditorMgr.Inst.Edit_SkillActData.s_ActData中所有的m_ActionSequence都依照其所在的ActGroup的順序重新編號
        /// </summary>
        private void ResortActionSeqence()
        {
            // 重新排序 ActGroup 的 ActionSequence
            for( int i = 0; i < m_List_ActID.Count; i++ )
            {
                S_ActData _ActData = BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ i ];
                _ActData.m_ActionSequence = (ushort)( m_List_SequenceGroup.IndexOf( m_List_ActID[ i ].m_GB_ActGroupParent ) + 1 );
                BattleEditorMgr.Inst.Edit_SkillActData.s_ActData[ i ] = _ActData;
            }
        }

        private void FinishUpdate()
        {
            ResortActTab();

            // 在所有的 &ActGroup 下的 &Button_ActAdd_Derived 按鈕，排序到最後
            for ( int i = 0; i < m_List_SequenceGroup.Count; i++ )
            {
                Transform _AddSubActButton = m_List_SequenceGroup[ i ].transform.Find( "Viewport/Content/&Button_ActAdd_Derived" );
                if ( _AddSubActButton != null )
                    ResortSubActTab( _AddSubActButton );
            }

            foreach ( var Ctrl in m_List_ActID )
            {
                Ctrl.m_ActEventGroupCtrl.ResortEventTab();
            }

            BattleEditorMgr.Inst.InitEditSkillActData();

            if ( BattleEditorMgr.Inst.Origin_SkillActData.m_TotalTime != BattleEditorMgr.Inst.Edit_SkillActData.GetTotalTime() )
            {
                D.LogWarning( "[BattleEditorMgr] 警告：原始串檔的技能時長與實際技能時常有誤差，可能是舊版本計算有誤。請保存確認新串檔是否正確" );
            }
        }

        #region Animation Event
        public void UpdateUI( List<S_ActData> iSkillActData )
        {
            for ( int i = 0; i < m_List_ActID.Count; i++ )
            {
                // 開始更新Event相關資訊
                m_List_ActID[ i ].UpdateUI( iSkillActData, m_List_ActID.Count );
            }
        }

        public void ClearEvents()
        {
            // 清除原本的ActGroup清單
            while ( m_List_SequenceGroup.Count > 0 )
            {
                GameObject.DestroyImmediate( m_List_SequenceGroup[0] );
                m_List_SequenceGroup.RemoveAt( 0 );
            }
            m_List_ActID.Clear();
            m_List_SequenceGroup.Clear();
        }
        #endregion
    }
}
