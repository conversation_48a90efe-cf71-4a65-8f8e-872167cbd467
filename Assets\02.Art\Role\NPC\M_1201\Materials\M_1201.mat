%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: M_1201
  m_Shader: {fileID: 4800000, guid: fa29df416bd74f24f83f61f76b0fc51c, type: 3}
  m_ShaderKeywords: _Emi_ON _Ocu_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 312894910facfa84caf90b396b2bd7ca, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveTex:
        m_Texture: {fileID: 2800000, guid: 1e2cf9599e3b1c041b7173b55d0e7203, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d8991b25c5ffdd44d813dacb1385f1c3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: b831ed16b29fd1c4da8e898b5e08f385, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap:
        m_Texture: {fileID: 2800000, guid: a2baabb7b18e66e40a2ec2583a3a8f60, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DeformationIntensity: 0.3
    - _DetailNormalMapScale: 1
    - _DissolveThreshold: 0
    - _DstBlend: 0
    - _Emi: 1
    - _EmissiveInten: 2
    - _GlossMapScale: 1
    - _Glossiness: 1
    - _GlossyReflections: 1
    - _Hit: 1
    - _HitRimIntensity: 1
    - _HitRimPow: 4
    - _Metallic: 1
    - _Mode: 0
    - _OccInten: 1
    - _OcclusionStrength: 1
    - _Ocu: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _ClothColor1: {r: 1, g: 1, b: 1, a: 1}
    - _ClothColor2: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _EmissiveColor: {r: 1, g: 1, b: 1, a: 1}
    - _HairColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitColor: {r: 0, g: 0, b: 0, a: 1}
    - _SkinColor: {r: 1, g: 1, b: 1, a: 1}
    - _TimeVec2: {r: 0.3, g: 0.5, b: 0, a: 0}
