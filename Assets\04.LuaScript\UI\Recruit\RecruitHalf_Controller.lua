---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---半版招募介面 Controller
---@class RecruitHalf_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.8
RecruitHalf_Controller = {}
local this = RecruitHalf_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("RecruitHalf_View", "RecruitHalf_Controller", EUIOrderLayers.HalfPage_Center, true, "")

local _MyColor_Bg = {
    UnityEngine.GradientColorKey.New(Extension.GetColor("#211006"), 0),
    UnityEngine.GradientColorKey.New(Extension.GetColor("#825648"), 1)
}
local _OtherColor_Bg = {
    UnityEngine.GradientColorKey.New(Extension.GetColor("#122232"), 0),
    UnityEngine.GradientColorKey.New(Extension.GetColor("#1A5275"), 1)
}

this.ShowArea =
{
    Center = 0,
    Right = 1,
}

--初始位置
local _Center_Pos = Vector2.zero
--開啟聊天室窗後位置
local _Right_PosX = 309.8
local _Right_PosY = -43.3

---初始化
function RecruitHalf_Controller.Init()
    this.m_Trans_RecruitHalf = this.m_ViewRef.m_Dic_Trans:Get("&Trans_RecruitHalf")
    this.m_Image_BGMask = this.m_ViewRef.m_Dic_Trans:Get("&Image_BGMask")

    this.m_Text_Info_Target = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Info_Target")
    this.m_Text_Info_Setting = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Info_Setting")
    this.m_Text_Info_Combat = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Info_Combat")

    this.m_Trans_Team = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Team")
    this.m_GObj_Memeber = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Member")

    this.m_MemberList = {}
    for i = 1, 4 do
        local _MemberObject = nil
        if i == 1 then
            _MemberObject =  this.m_GObj_Memeber
        else
            _MemberObject =  this.m_GObj_Memeber:Instantiate( this.m_GObj_Memeber )
            _MemberObject:SetParent(this.m_Trans_Team)
        end
        _MemberObject:SetSiblingIndex(i - 1)
       
        local _MmeberInfo = {}
        _MmeberInfo.m_gameObject = _MemberObject.gameObject
        _MmeberInfo.m_Btn_Info = Button.New(_MemberObject.gameObject)
        _MmeberInfo.m_Btn_Info:AddListener(EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Info(i) end)
        _MmeberInfo.m_Image_Frame = _MemberObject.gameObject:GetComponent(typeof( UIRenderChangeColor )) --要變色
        _MmeberInfo.m_Image_Bg = _MemberObject:Find("Image_Bg").gameObject:GetComponent(typeof( UIGradient ))
        _MmeberInfo.m_Image_Avatar = _MemberObject:Find("Image_Mask/Image_Avatar").gameObject:GetComponent(typeof( RawImage ))
        _MmeberInfo.m_Text_Lv = _MemberObject:Find("Text_Lv").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _MmeberInfo.m_Image_Social_1 = _MemberObject:Find("Image_Social/Image_Social_1").gameObject:GetComponent(typeof( Image ))
        _MmeberInfo.m_Image_Social_2 = _MemberObject:Find("Image_Social/Image_Social_2").gameObject:GetComponent(typeof( Image ))
        _MmeberInfo.m_Btn_Mark = Button.New(_MemberObject:Find("Button_Mark"))
        _MmeberInfo.m_Btn_Mark:AddListener(EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Kick(i) end)
        _MmeberInfo.m_Image_Leader = _MmeberInfo.m_Btn_Mark.transform:Find("Image_Leader")
        _MmeberInfo.m_Image_Kick = _MmeberInfo.m_Btn_Mark.transform:Find("Image_Kick")

        --玩家狀態
        _MmeberInfo.m_Group_State = _MemberObject:Find("Group_State")
        _MmeberInfo.m_Image_State_Bg = _MmeberInfo.m_Group_State.gameObject:GetComponent(typeof( UIRenderChangeColor )) --要變色
        _MmeberInfo.m_Image_State_Icon = _MmeberInfo.m_Group_State:Find("Image_State").gameObject:GetComponent(typeof( UIImageChange )) --要換圖
        _MmeberInfo.m_Text_State = _MmeberInfo.m_Group_State:Find("Text_State").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

        _MmeberInfo.m_Image_InfoBg = _MemberObject:Find("Image_InfoBg").gameObject:GetComponent(typeof( UIRenderChangeColor )) --要變色
        --玩家資訊
        _MmeberInfo.m_Group_Info = _MemberObject:Find("Group_Info")
        _MmeberInfo.m_Text_PlayerTitle = _MmeberInfo.m_Group_Info:Find("Text_PlayerTitle").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _MmeberInfo.m_Text_PlayerName = _MmeberInfo.m_Group_Info:Find("Text_PlayerName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

        _MmeberInfo.m_Btn_Invite = Button.New(_MemberObject:Find("Button_Invite").gameObject)
        _MmeberInfo.m_Btn_Invite:SetText(TextData.Get(1057))
        Button.AddListener(_MmeberInfo.m_Btn_Invite, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Invite() end)

        _MmeberInfo.m_Group_Combat = _MemberObject:Find("Group_Combat")
        _MmeberInfo.m_Group_Combat_Image = _MmeberInfo.m_Group_Combat.gameObject:GetComponent(typeof( UIRenderChangeColor )) --要變色
        _MmeberInfo.m_Text_PlayerCombat = _MmeberInfo.m_Group_Combat:Find("Text_PlayerCombat").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

        _MmeberInfo.m_Text_Waiting = _MemberObject:Find("Text_Waiting").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

        _MmeberInfo.m_InteractPanelAhchor = _MemberObject:Find("InteractPanelAhchor")

        table.insert(this.m_MemberList, _MmeberInfo)
    end

    this.m_Btn_Setting = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Setting"))
    Button.AddListener(this.m_Btn_Setting, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Setting() end)
    this.m_Btn_Leave = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Leave"))
    Button.AddListener(this.m_Btn_Leave, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Leave() end)
    this.m_Btn_Waiting = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Waiting"))
    Button.AddListener(this.m_Btn_Waiting, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Waiting() end)
    this.m_Btn_StartOrReady = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Start"))
    Button.AddListener(this.m_Btn_StartOrReady, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_StartOrReady() end)

    this.m_Btn_Chat = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Chat"))
    Button.AddListener(this.m_Btn_Chat, EventTriggerType.PointerClick, function() 
        UIMgr.Open(ChatController)
        RecruitHalf_Controller.SetPosition(this.ShowArea.Right)
    end)
    this.m_Btn_Broadcast = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Broadcast"))
    Button.AddListener(this.m_Btn_Broadcast, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Broadcast() end)

    this.m_Btn_Close = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Close_Gray"))
    Button.AddListener(this.m_Btn_Close, EventTriggerType.PointerClick, function() UIMgr.Close(RecruitHalf_Controller) end)

    this.m_DungeonData = RecruitMgr.GetCampaignTypeData(RecruitMgr.m_DungeonType)
    --招募視窗
    this.m_Window_Recruit = this.m_ViewRef.m_Dic_Trans:Get("&Window_Recruit")
    this.m_DropDownList_Dungeon = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_Dungeon")
    RecruitHalf_Controller.SetDropDownList_Dungeon()
    this.m_InputField_Info = this.m_ViewRef.m_Dic_TMPInputField:Get("&InputField_Info")
    this.m_DropDownList_Setting = this.m_ViewRef.m_Dic_Dropdown:Get("&Dropdown_Setting")
    RecruitHalf_Controller.SetDropDownList_Setting()
    this.m_InputField_Pass = this.m_ViewRef.m_Dic_TMPInputField:Get("&InputField_Pass")
    this.m_Btn_Cancel_Recruit = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Cancel_Recruit"))
    Button.AddListener(this.m_Btn_Cancel_Recruit, EventTriggerType.PointerClick, function()
        RecruitHalf_Controller.SetWindowActive(false)
    end)
    this.m_Btn_Comfirm_Recruit =  Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Comfirm_Recruit"))
    Button.AddListener(this.m_Btn_Comfirm_Recruit, EventTriggerType.PointerClick, function() RecruitHalf_Controller.OnClick_Comfirm_Recruit() end)

    this.m_Image_Mask = this.m_ViewRef.m_Dic_Trans:Get("&Image_Mask")
end

function RecruitHalf_Controller.Open()
    this.m_Image_BGMask.gameObject:SetActive(true)
    local _pos = UIMgr.IsVisible(ChatController) and this.ShowArea.Right or this.ShowArea.Center
    RecruitHalf_Controller.SetPosition(_pos)
    RecruitHalf_Controller.SetTeam()
    
    RecruitHalf_Controller.SetWindowActive(false)
    return true
end

function RecruitHalf_Controller.SetPosition(iArea)
    if iArea == this.ShowArea.Center then
        this.m_Image_BGMask.gameObject:SetActive(true)
        this.m_Trans_RecruitHalf.transform.anchoredPosition = _Center_Pos
    elseif iArea == this.ShowArea.Right then
        this.m_Image_BGMask.gameObject:SetActive(false)
        this.m_Trans_RecruitHalf.transform.anchoredPosition = Vector2( _Right_PosX, _Right_PosY )
    end
end

--region 隊伍資訊
function RecruitHalf_Controller.SetTeam()
    RecruitHalf_Controller.SetTeamInfo()
    RecruitHalf_Controller.SetMemberInfo()
end

---設定隊伍資訊
function RecruitHalf_Controller.SetTeamInfo()
    if RecruitMgr.m_MyTeam ~= nil then
        --設定隊伍資訊
        local _DungeonData = RecruitMgr.GetDungeonData(RecruitMgr.m_DungeonType, RecruitMgr.m_MyTeam.m_Idx)
        this.m_Text_Info_Target.text = GString.Format(TextData.Get(20500008), _DungeonData.m_LimitLv_Low) .. TextData.Get(_DungeonData.m_DungeonTitleString)
        this.m_Text_Info_Setting.text = TextData.Get(20500506 + RecruitMgr.m_MyTeam.m_Public)
        local _TotalCombat = RecruitMgr.CalTotaleCombat(RecruitMgr.m_MyTeam.m_Member)
        this.m_Text_Info_Combat.text = _TotalCombat

        if RecruitMgr.m_isLeader then
            local _readyCount = 0
            for k, v in pairs(RecruitMgr.m_MyTeam.m_Member) do
                if not RecruitMgr.IsLeader(v.m_RoleID) then
                    if v.m_Ready == 1 then
                        _readyCount = _readyCount + 1
                    end
                end
            end
            if RecruitMgr.m_MyTeam.m_Num ~= 1 and _readyCount >= 1 then
                this.m_Btn_StartOrReady:SetEnable()
            else
                this.m_Btn_StartOrReady:SetDisable()
            end
        else
            local _CanReady = RecruitMgr.CheckCanReady(RecruitMgr.m_DungeonType, _DungeonData.m_Idx)
            if _CanReady then
                this.m_Btn_StartOrReady:SetEnable()
            else
                this.m_Btn_StartOrReady:SetDisable()
            end
        end
        this.m_Btn_Setting.gameObject:SetActive(RecruitMgr.m_isLeader)
        local _btnString = ""
        if RecruitMgr.m_isLeader then
            _btnString = TextData.Get(20500120)
        else
            for k, v in pairs(RecruitMgr.m_MyTeam.m_Member) do
                if RecruitMgr.IsMyself(v.m_RoleID) then
                    _btnString = v.m_Ready == 0 and TextData.Get(20500118) or TextData.Get(20500119)
                    break
                end
            end
        end
        this.m_Btn_StartOrReady:SetText(_btnString)
        this.m_Btn_Broadcast.gameObject:SetActive(RecruitMgr.m_isLeader)
    end
end

---設定隊伍成員資訊
function RecruitHalf_Controller.SetMemberInfo()
    if RecruitMgr.m_MyTeam ~= nil then
        --設定隊員資訊
        for i = 1, 4 do
            local _Member = RecruitMgr.m_MyTeam.m_Member[i]
            RecruitHalf_Controller.SetHaveMember(i, _Member ~= nil)
            if _Member ~= nil then
                this.m_MemberList[i].m_Text_Lv.text = GString.Format(TextData.Get(530102), _Member.m_Lv)

                local _isLeader = RecruitMgr.IsLeader(_Member.m_RoleID)
                if RecruitMgr.m_isLeader or _isLeader then
                    this.m_MemberList[i].m_Btn_Mark.gameObject:SetActive(true)
                    this.m_MemberList[i].m_Image_Leader.gameObject:SetActive(_isLeader)
                    this.m_MemberList[i].m_Image_Kick.gameObject:SetActive(not _isLeader)
                else
                    this.m_MemberList[i].m_Btn_Mark.gameObject:SetActive(false)
                end

                --設定立繪
                local _HeadIconData = HeadIconData.GetHeadIconDataByIdx(_Member.m_HeadID)
                local _RoleImageName = _HeadIconData.m_2DImageName:gsub("RoleImg_", "") .. "_A"
                TextureMgr.Load(_RoleImageName, false, function(iTex)
                    this.m_MemberList[i].m_Image_Avatar.texture = iTex
                    this.m_MemberList[i].m_Image_Avatar.gameObject:SetActive(iTex ~= nil)
                end)

                local _isMe = RecruitMgr.IsMyself(_Member.m_RoleID)
                --設定左上角關係Icon
                if _isMe then
                    this.m_MemberList[i].m_Image_Social_1.gameObject:SetActive(false)
                    this.m_MemberList[i].m_Image_Social_2.gameObject:SetActive(false)
                else
                    --幫會
                    this.m_MemberList[i].m_Image_Social_1.gameObject:SetActive(false)
                    --好友
                    local _IsMyFriend = FriendMgr:GetIsCommunityByID(EFriendType.Friend, tostring(_Member.m_RoleID))
                    this.m_MemberList[i].m_Image_Social_2.gameObject:SetActive(_IsMyFriend)
                end

                --設定面板顏色
                local _State = _isMe and 1 or 0
                this.m_MemberList[i].m_Btn_Info:ChangeStateTransitionGroup(_State)
                local _Color = _isMe and _MyColor_Bg or _OtherColor_Bg
                this.m_MemberList[i].m_Image_Bg.Gradient.colorKeys = _Color
 
                --設定玩家資訊
                this.m_MemberList[i].m_Text_PlayerTitle.gameObject:SetActive(_Member.m_TitleID ~= 0)
                this.m_MemberList[i].m_Text_PlayerTitle.text = TitleData.GetTitleName(_Member.m_TitleID)
                this.m_MemberList[i].m_Text_PlayerName.text = _Member.m_Name
                this.m_MemberList[i].m_Text_PlayerCombat.text = _Member.m_EquipPoint

                this.m_MemberList[i].m_Group_State.gameObject:SetActive(true)
                local _DungeonData = RecruitMgr.GetDungeonData(RecruitMgr.m_DungeonType, RecruitMgr.m_MyTeam.m_Idx)
                --該隊員等級足夠
                if _Member.m_Lv >= _DungeonData.m_LimitLv_Low then
                    --該隊員通關次數足夠
                    if _Member.m_EnoughTimes ~= 0 then
                        this.m_MemberList[i].m_Group_State.gameObject:SetActive(_Member.m_Ready ~= 0 and not RecruitMgr.IsLeader(_Member.m_RoleID))
                        if _Member.m_Ready ~= 0 and not RecruitMgr.IsLeader(_Member.m_RoleID) then
                            this.m_MemberList[i].m_Image_State_Bg:Trigger(ESelectionState.Selected)
                            this.m_MemberList[i].m_Image_State_Icon:Trigger(ESelectionState.Selected)
                            this.m_MemberList[i].m_Text_State.text = TextData.Get(20500121)
                        end
                    else
                        this.m_MemberList[i].m_Image_State_Bg:Trigger(ESelectionState.Disabled)
                        this.m_MemberList[i].m_Image_State_Icon:Trigger(ESelectionState.Disabled)
                        this.m_MemberList[i].m_Text_State.text = TextData.Get(20500112)
                    end
                else
                    this.m_MemberList[i].m_Image_State_Bg:Trigger(ESelectionState.Disabled)
                    this.m_MemberList[i].m_Image_State_Icon:Trigger(ESelectionState.Disabled)
                    this.m_MemberList[i].m_Text_State.text = TextData.Get(20500111)
                end
            else
                this.m_MemberList[i].m_Btn_Info:ChangeStateTransitionGroup(2)
                this.m_MemberList[i].m_Image_Bg.Gradient.colorKeys = _OtherColor_Bg

                local _canInvite = TeammateMgr.CanPlayerInvite()
                this.m_MemberList[i].m_Btn_Invite.gameObject:SetActive(_canInvite)
            end
        end
    end
end

function RecruitHalf_Controller.SetHaveMember(iIdx, iShow)
    this.m_MemberList[iIdx].m_Image_Avatar.gameObject:SetActive(iShow)
    this.m_MemberList[iIdx].m_Text_Lv.gameObject:SetActive(iShow)

    this.m_MemberList[iIdx].m_Image_InfoBg.gameObject:SetActive(iShow)
    this.m_MemberList[iIdx].m_Group_Info.gameObject:SetActive(iShow)
    this.m_MemberList[iIdx].m_Group_Combat.gameObject:SetActive(iShow)

    this.m_MemberList[iIdx].m_Btn_Invite.gameObject:SetActive(not iShow)
    this.m_MemberList[iIdx].m_Text_Waiting.gameObject:SetActive(not iShow)
    this.m_MemberList[iIdx].m_Text_Waiting.text = TextData.Get(20500512)

    --之後會再判斷是否開關
    this.m_MemberList[iIdx].m_Image_Social_1.gameObject:SetActive(false)
    this.m_MemberList[iIdx].m_Image_Social_2.gameObject:SetActive(false)

    this.m_MemberList[iIdx].m_Btn_Mark.gameObject:SetActive(false)
    this.m_MemberList[iIdx].m_Group_State.gameObject:SetActive(false)
end
--endregion

--region 按鈕功能
function RecruitHalf_Controller.OnClick_Info(iIdx)
    if RecruitMgr.m_MyTeam == nil then return end

    local _Member = RecruitMgr.m_MyTeam.m_Member[iIdx]
    if _Member then
        if not RecruitMgr.IsMyself(_Member.m_RoleID) then
            if Interact_Model.RelationCheck(_Member.m_RoleID, EOpenInteractPanelFrom.FromRecruitHalfUI) then
                this.m_InteractPanelAhchor = this.m_MemberList[iIdx].m_InteractPanelAhchor
                UIMgr.Open(Interact_Controller)
            end
        end
    end
end

function RecruitHalf_Controller.OnClick_Invite()
    UIMgr.Open(Community_Controller)
end

function RecruitHalf_Controller.OnClick_Kick(iIdx)
    if RecruitMgr.m_MyTeam == nil then return end
    if RecruitMgr.m_isLeader then
        local _Member = RecruitMgr.m_MyTeam.m_Member[iIdx]
        if not RecruitMgr.IsMyself(_Member.m_RoleID) then
            local _Packet = {}
            _Packet.m_Index = TeammateMgr.GetTeammateIndex(_Member.m_RoleID)
            _Packet.m_RoleID = _Member.m_RoleID
            SendProtocol_011._001(10, _Packet)
        end
    end
end

---副本設定
function RecruitHalf_Controller.OnClick_Setting()
    RecruitHalf_Controller.OpenRecruitWindow(RecruitMgr.m_RecruitDungeonIdx)
end

---離開隊伍
function RecruitHalf_Controller.OnClick_Leave()
    local _Packet = {}
    if RecruitMgr.m_isLeader then
        local _Member = RecruitMgr.SearchNextMember()
        if _Member ~= nil then
            _Packet.m_Index = TeammateMgr.GetTeammateIndex(_Member.m_RoleID)
            _Packet.m_RoleID = _Member.m_RoleID
            SendProtocol_011._001(8, _Packet)
            SendProtocol_011._001(6, _Packet)
        else
            this.m_MyTeam = nil
            SendProtocol_011._003(2, _Packet)
        end
    else
        SendProtocol_011._001(6, _Packet)
    end
end

---背景等待
function RecruitHalf_Controller.OnClick_Waiting()
    UIMgr.Close(RecruitHalf_Controller)
    if UIMgr.IsVisible(Recruit_Controller) then
        UIMgr.Close(Recruit_Controller)
    end
end

---開始或準備
function RecruitHalf_Controller.OnClick_StartOrReady()
    if RecruitMgr.m_isLeader then
        SendProtocol_011._003(7)
    else
        local _Packet = {}
        _Packet.m_Kind = 2
        _Packet.m_RoleID = RecruitMgr.m_MyTeam.m_LeaderID
        RecruitMgr.m_isReady = not RecruitMgr.m_isReady 
        local _isReady = RecruitMgr.m_isReady and 1 or 0
        _Packet.m_Ready = _isReady
        SendProtocol_011._003(5, _Packet)
    end
end

function RecruitHalf_Controller.OnClick_Broadcast()
    SendProtocol_011._003(8)
    UIMgr.Open(ChatController)
    RecruitHalf_Controller.SetPosition(this.ShowArea.Right)
end
--endregion

--region 招募視窗
---開啟招募視窗
function RecruitHalf_Controller.SetWindowActive(iShow)
    this.m_Image_Mask.gameObject:SetActive(iShow)
    this.m_Window_Recruit.gameObject:SetActive(iShow)
end

function RecruitHalf_Controller.OpenRecruitWindow(iIdx)
    RecruitHalf_Controller.UpdateDropDownList_Dungeon()
    RecruitHalf_Controller.SetWindowActive(true)
    local _idx = iIdx == 0 and this.m_DropdownSortData[1].m_Idx or this.m_DropdownSortData[iIdx].m_Idx
    for k, v in pairs(this.m_DropdownSortData) do
        if v.m_Idx == _idx then
            local _FormatStr = TextData.Get(20500503)
            local _LvStr = GString.Format(TextData.Get(20500008), v.m_LimitLv_Low)
            local _InfoStr = GString.Format(_FormatStr, PlayerData.GetName(), _LvStr, TextData.Get(v.m_DungeonTitleString))
            this.m_InputField_Info.placeholder.text = _InfoStr
            this.m_DropDown_DungeonData:SetValueWithoutNotify(k - 1)
            RecruitMgr.m_RecruitDungeonIdx = k
            break
        end
    end
    this.m_DropDown_SettingData:SetValueWithoutNotify(RecruitMgr.m_RecruitPublic)
    this.m_InputField_Pass.text = RecruitMgr.m_RecruitPassString
end

--點擊設定招募
function RecruitHalf_Controller.SetDropDownList_Dungeon()
    local _OptionString = {}
    this.m_DropDownOptions_Dungeon = {}
    this.m_DropDownList_Dungeon:ClearOptions();
    local _SortData = RecruitMgr.DropDownSortDungeon(this.m_DungeonData)
    for k, v in pairs(_SortData) do
        local _LvStr = GString.Format(TextData.Get(20500008), v.m_LimitLv_Low)
        local _NameStr = TextData.Get(v.m_DungeonTitleString)
        local _optionName = _LvStr .. _NameStr
        table.insert(_OptionString, _optionName)
        table.insert(this.m_DropDownOptions_Dungeon, v)
    end

    this.m_DropDown_DungeonData = Dropdown:New(this.m_DropDownList_Dungeon, _OptionString, 
        function(iOption)
            RecruitMgr.m_RecruitDungeonIdx = iOption + 1
            local _DungeonData = this.m_DropdownSortData[RecruitMgr.m_RecruitDungeonIdx]
            if _DungeonData ~= nil then
                local _FormatStr = TextData.Get(20500503)
                local _LvStr = GString.Format(TextData.Get(20500008), _DungeonData.m_LimitLv_Low)
                local _InfoStr = GString.Format(_FormatStr, PlayerData.GetName(), _LvStr, TextData.Get(_DungeonData.m_DungeonTitleString))
                this.m_InputField_Info.placeholder.text = _InfoStr
            end
        end)
end

function RecruitHalf_Controller.UpdateDropDownList_Dungeon()
    local _OptionString = {}
    this.m_DropdownSortData = RecruitMgr.DropDownSortDungeon(this.m_DungeonData)
    for k, v in pairs(this.m_DropdownSortData) do
        local _LvStr = GString.Format(TextData.Get(20500008), v.m_LimitLv_Low)
        local _NameStr = TextData.Get(v.m_DungeonTitleString)
        local _optionName = _LvStr .. _NameStr
        table.insert(_OptionString, _optionName)
    end

    this.m_DropDownList_Dungeon:ClearOptions()
    local _OptionDataList = TMP_Dropdown.OptionDataList()
    for key, value in pairs(_OptionString) do
        local _OptionData = TMP_Dropdown.OptionData(value)
		_OptionDataList.options:Add(_OptionData)
    end
    this.m_DropDownList_Dungeon:AddOptions(_OptionDataList.options)
end

--點擊設定公開狀態
function RecruitHalf_Controller.SetDropDownList_Setting()
    local _OptionString = {}
    this.m_DropDownOptions_Setting = {}
    this.m_DropDownList_Setting:ClearOptions();
    local _Data = {}
    for i = 1, 2 do
        _Data[i] = {}
        _Data[i].m_Idx = i
        _Data[i].m_String = 20500505 + i
    end
    for k, v in pairs(_Data) do
        table.insert(_OptionString, v.m_String)
        table.insert(this.m_DropDownOptions_Setting, v)
    end

    this.m_DropDown_SettingData = Dropdown:New(this.m_DropDownList_Setting, _OptionString,
        function(iOption)
            RecruitMgr.m_RecruitPublic = iOption
        end)
end

-- 11-3-3.隊長確定要變更招募+關卡編號(2)+公開[0.公開  1.非公開]+Len(4)+密碼(?)+Len(4)+招募資訊(?)
function RecruitHalf_Controller.OnClick_Comfirm_Recruit()
    local _RecruitInfo = {}
    local _DungeonData = this.m_DropdownSortData[RecruitMgr.m_RecruitDungeonIdx]
    _RecruitInfo.m_Idx = _DungeonData.m_Idx
    local _recruitInfoString = ""
    if this.m_InputField_Info.text ~= "" then
        local _lv = _DungeonData.m_LimitLv_Low
        local _dungeonName = TextData.Get(_DungeonData.m_DungeonTitleString)
        _recruitInfoString = GString.Format(TextData.Get(20500504), this.m_InputField_Info.text, _lv, _dungeonName)
    else
        _recruitInfoString = this.m_InputField_Info.placeholder.text
    end
    _RecruitInfo.m_InfoString = _recruitInfoString
    RecruitMgr.m_RecruitInfoString = _recruitInfoString
    _RecruitInfo.m_Public = RecruitMgr.m_RecruitPublic
    if _RecruitInfo.m_Public == 0 then
        _RecruitInfo.m_PassString = ""
        SendProtocol_011._003(3, _RecruitInfo)
        RecruitHalf_Controller.SetWindowActive(false)
    else
        _RecruitInfo.m_PassString = this.m_InputField_Pass.text
        --只有英文或數字才給過
        if string.match(_RecruitInfo.m_PassString, "^%w+$") ~= nil then
            SendProtocol_011._003(3, _RecruitInfo)
            RecruitHalf_Controller.SetWindowActive(false)
        else
            MessageMgr.AddCenterMsg(true, TextData.Get(20500526))
        end
    end
    RecruitMgr.m_RecruitPassString = _RecruitInfo.m_PassString
end
--endregion
