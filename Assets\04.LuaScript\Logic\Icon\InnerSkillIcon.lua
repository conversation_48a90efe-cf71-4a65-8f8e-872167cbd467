---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---InnerSkillIcon 繼承自 BasicIcon
---@class InnerSkillIcon
---author Jin
---version 1.0
---since [HEM 2.0]
---date 2025.5.28
InnerSkillIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function InnerSkillIcon.Init( iParent )
    InnerSkillIcon.LoadResources( "InnerSkillIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

---建立新的 InnerSkillIcon
function InnerSkillIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam )
    local _SkillId = iIdx

    local function OnSkillClick()
        if iOnClick then
            iOnClick(iOnClickParam)
        end
    end

    local _Icon = BasicIcon:New( m_Tmp, EIconType.InnerSkill, 0, iParent, iWidth, OnSkillClick )
    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    setmetatable( _Icon, { __index = self } )

    local function OnItemPointerEnter()
        if _Icon.m_NeedSelectAction and _Icon.m_IsSelect == false and _Icon.m_Idx ~= 0 then
            _Icon:SetObjectActive(_Icon.m_Trans_Select, true)
        end
    end

    local function OnItemPointerExit()
        if _Icon.m_NeedSelectAction and _Icon.m_IsSelect == false and _Icon.m_Idx ~= 0 then
            _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
        end
    end

    _Icon:SetNeedOpenHint(false)
         :SetNeedLongPress(false)
         :SetClickTwice(false)
         :SetSelect(iOnClick ~= nil)
         :AddEvent(OnItemPointerEnter, EventTriggerType.PointerEnter)
         :AddEvent(OnItemPointerExit, EventTriggerType.PointerExit)

    -- 選擇框
    if _Icon.m_Trans_Select == nil then
        _Icon.m_Trans_Select = _Icon.transform:Find( "Image_Select" ):GetComponent( typeof( Image ) )
        _Icon.m_Tween_Select = _Icon.m_Trans_Select:GetComponent( typeof( LeanTweenVisual ) )
        _Icon.m_Tween_Select.enabled = false
        _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
    end

    ---設定遮罩
    if _Icon.m_Image_Mask == nil then
        _Icon.m_Image_Mask = _Icon.transform:Find( "Image_Mask" ):GetComponent( typeof( Image ) )
        _Icon.m_Text_Mask =  _Icon.m_Image_Mask.transform:Find("TMP_MaskText"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        _Icon:SetObjectActive(_Icon.m_Image_Mask, false)
    end

    -- 加號
    if _Icon.m_Image_Add == nil then
        _Icon.m_Image_Add = _Icon.transform:Find( "Image_Add" )
        _Icon:SetObjectActive(_Icon.m_Image_Add, false)
    end

    -- 圓形圖
    if _Icon.m_Image_Circle_Mask == nil then
        _Icon.m_Image_Circle_Mask = _Icon.transform:Find( "Image_Circle_Mask" )
        _Icon.m_Image_Circle = _Icon.m_Image_Circle_Mask.transform:Find( "Image_Circle" ):GetComponent( typeof( Image ) )
        _Icon.m_Text_Complete =  _Icon.m_Image_Circle_Mask.transform:Find("Text_Complete"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        _Icon:SetObjectActive(_Icon.m_Image_Circle_Mask, false)
    end

    -- 交換圖
    if _Icon.m_Image_Change == nil then
        _Icon.m_Image_Change = _Icon.transform:Find( "Image_Change" )
        _Icon:SetObjectActive(_Icon.m_Image_Change, false)
    end

    ---特效背景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectBack == nil then
        _Icon.m_Trans_EffectBack = _Icon.transform:Find( "Trans_EffectBack" )
    end

    ---特效前景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectFront == nil then
        _Icon.m_Trans_EffectFront = _Icon.transform:Find( "Trans_EffectFront" )
    end

    if iParent then
        _Icon.transform:SetParent( iParent )
    end

    ---不被IconMgr IconMgr.CancelAllClick() 清除選擇，體現在開Hint時不會被統一清除
    _Icon.m_NeedClearSelect = true

    _Icon:RefreshIcon(_SkillId)

    return _Icon
end

function InnerSkillIcon:OnClick(iSender)
    if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
        D.Log("CD 中")
        return
    end
    
    if self.m_OnClickFunc then
        self.m_OnClickFunc(self.m_OnClickParam)
    end
end

---重設技能 Icon
---@param iIdx number 技能 Idx
function InnerSkillIcon:RefreshIcon(iIdx)
    local _SkillId = iIdx

    local function ResetIcon()
        self.m_SkillData = nil
        self:RefreshBasicIcon(0)
        self:SetRank(ERank.None)
            :SetAdd(false)
            :SetCircle(false)
            :SetChange(false)
    end

    if iIdx == 0 then
        ResetIcon()
        do return end
    end

    local _WugongData = iIdx ~= 0 and WugongData.GetWugongDataByIdx( iIdx ) or nil
    local _SkillData = _WugongData ~= nil and SkillData.GetSkillDataByIdx( _WugongData.m_SkillDataAy[1].m_SkillId ) or nil
    if _SkillData == nil then
        ResetIcon()
        do return end
    end

    self.m_SkillData = _SkillData

    self:RefreshBasicIcon(_SkillId, self.m_SkillData:GetIconTextureName())
    self:SetRank(self.m_SkillData.m_SkillClass)
        :SetAdd(false)
        :SetCircle(false)
        :SetChange(false)

    self:ShowSelect( false )
end

--- 心法Icon專用
function InnerSkillIcon:RefreshIcon_Method(iTextureName, iRank)
    self:SetTexture(iTextureName)
    self:SetRank(iRank)
        :SetAdd(false)
        :SetCircle(false)
        :SetChange(false)

    self:ShowSelect( false )
end

--- 設定稀有度外框
function InnerSkillIcon:SetRank( iRank )
    --- 舊物品表顏色到 8 號、物品表調整完成前防呆處理 2024.01.23 Modify by 蛋糕
    if iRank > ERank.Rainbow then
        iRank = ERank.Rainbow
    end

    self:SetFrameType(iRank)

    return self
end

--- 覆寫設定外框圖
function InnerSkillIcon:SetFrameImage(iRank)
    -- 只有內功會用到外框變色
    if iRank ~= SkillData.ESkillType.InternalSkill then
        --return
    end
    
    -- 只有內功會用到外框變色
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            -- 存下所有可變色區塊
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end

    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].sprite = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].sprite
        self.m_FrameImage[key1].material = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].material
    end

    return self
end

---設定顯示遮罩
---@param iIsShow boolean 設定遮罩 true = 顯示遮罩；false = 強制關閉遮罩；nil 預設
---@param iTextID int 字串編號 遮罩打開再給編號即可
function InnerSkillIcon:SetMask( iIsShow, iText)
    if iIsShow == false then
        self:SetObjectActive(self.m_Image_Mask, false)
        return self
    end
    
    self:SetObjectActive(self.m_Image_Mask, iIsShow)
    self.m_Text_Mask.text = iIsShow and iText or ""
    
    return self
end

---設定顯示加號
---@param iIsShow boolean 設定加號 true = 顯示加號；false = 關閉加號；nil 預設
function InnerSkillIcon:SetAdd( iIsShow)
    self:SetObjectActive(self.m_Image_Add, iIsShow)
    
    return self
end

---設定顯示圓形
---@param iIsShow boolean 設定加號 true = 顯示加號；false = 關閉加號；nil 預設
---@param iStep number 穴位數
function InnerSkillIcon:SetCircle(iIsShow, iPoint)
    self:SetObjectActive(self.m_Image_Circle_Mask, iIsShow)
    if iIsShow then
        self.m_Image_Circle.fillAmount = iPoint / 9
        self.m_Text_Complete.text = math.floor((iPoint / 9) * 100) .. "%"
    end
    
    return self
end

---設定顯示交換圖
---@param iIsShow boolean 設定交換圖 true = 顯示交換圖；false = 關閉交換圖；nil 預設
function InnerSkillIcon:SetChange( iIsShow)
    self:SetObjectActive(self.m_Image_Change, iIsShow)
    
    return self
end

--- 設定選擇
function InnerSkillIcon:ShowSelect( iIsSelect )
    self.m_IsSelect = iIsSelect
    self:SetObjectActive(self.m_Trans_Select, self.m_IsSelect)

    if self.m_IsSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSelectImage
        self.m_Trans_Select.color = Color.White
        self.m_ClickOnce = true
    end
    self.m_Tween_Select.enabled = false
end

function InnerSkillIcon:ShowSettingSelect( iIsSettingSelect )
    if iIsSettingSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSettingImage
    end
    self.m_ClickOnce = nil
    self:SetMask( nil, TextData.Get(self.m_OnSelectTextId))
    self:SetObjectActive(self.m_Trans_Select, iIsSettingSelect)
    self.m_Tween_Select.enabled = iIsSettingSelect
    if iIsSettingSelect == true then
        self.m_Tween_Select:buildAllTweensAgain()
    end
end

--- 取消選擇
function InnerSkillIcon:CancelSelect(iDoSelectAction)
    self:SetMask()
    UIMgr.OpenIconName(false)
    self:SetObjectActive(self.m_Trans_Select, false)
    self.m_ClickOnce = nil

    -- 給 icon 底層因為判斷不選取，不在次觸發 action 用
    if iDoSelectAction ~= false then
        self:DoSelectAction(false)
    end
end

--- 設定稀有度特殊效果 ( 用完記得歸還 )
function InnerSkillIcon:SetRankFrameEffect(iIsActive, iUIController)
    if iIsActive == true and self.m_IsShowRankFrame == true then
        do return end
    end

    if iUIController ~= nil then 
        self:SetIconUIController(iUIController)
    end

    if iIsActive == true then
        self.m_IsShowRankFrame = true
        local _EffectID = 0
        if IconSetting.EIconRankFrameEffectId[self.m_SkillData.m_SkillClass] ~= nil then
            _EffectID = IconSetting.EIconRankFrameEffectId[self.m_SkillData.m_SkillClass]
        else
            -- D.LogError("self.m_ItemData.m_Rarity: " .. self.m_ItemData.m_Rarity)
            return
        end

        EffectMgr.PlayEffectWithPos(
            EEffectType.UI, _EffectID, self.m_Trans_EffectFront, Vector3.zero, true, 
            function(iHash)
                self.iRankFrameHash = iHash
            end, nil, nil, nil)

    else
        if self.iRankFrameHash ~= nil then
            self.m_UIController = nil
            EffectMgr.ReturnEffect(self.iRankFrameHash)
            self.iRankFrameHash = nil
        end

        if self.m_IsShowRankGrandPrize == true then
            self:SetRankGrandPrizeEffect(false, iUIController)
        end
        self.m_IsShowRankFrame = false
    end
end