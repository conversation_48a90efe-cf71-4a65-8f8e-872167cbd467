---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("UI/BattleText/BattleText_Model")

---處理戰鬥文字控制
---@class BattleText_Controller
---author WereHsu
---version 1.0
---since [黃易群俠傳M] 0.90
---date 2023.02.07
BattleText_Controller = {}
local this = BattleText_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("BattleText_View", "BattleText_Controller", EUIOrderLayers.FullPage)

---@type table<string, BattleText>
this.m_BattleTextPool = {}

local m_TestCountDown = Time.time

local m_PlayingBigBuffWord = nil

---初始化
function BattleText_Controller.Init()
    --region UI存放區
    ---@type Transform
    this.m_Root_BattleText = this.m_ViewRef.m_Dic_Trans:Get("&Pool_BattleText")
    this.m_BattleTextObj = this.m_ViewRef.m_Dic_Trans:Get("&Obj_BattleText")
    --endregion

    --先把資料定義好
    BattleText_Model.Init()

    BattleText_Model.m_GObjPool_BattleText = Extension.GetGameObjPool(BattleText_Model.BATTLE_TEXT_POOL_NUM,0,this.BattleText_Reset,this.BattleText_Init)
end

---建立新的測試數字
local function CreateNewTestText()
    local _TestValue = math.floor(Random.Range(500, 2000)) -- Mathf.RoundToInt( math.random(500,2000))
    local _TestBonusValue = 0
    local _DelayTime = nil

    if BattleText_Model.m_RandomTest_Enable then
        local _RandomType = BattleText_Model.m_RandomRange[math.floor(Random.Range(1, #BattleText_Model.m_RandomRange))]
        BattleText_Controller.ChangeType(_RandomType)

    elseif BattleText_Model.m_TickTest_Enable then
        if BattleText_Model.m_TickTest_Now < BattleText_Model.m_TickTest_Begin or BattleText_Model.m_TickTest_Now > BattleText_Model.m_TickTest_End then
            BattleText_Model.m_TickTest_Now = BattleText_Model.m_TickTest_Begin
        end
        local _RandomType = Random.Range(0, 2)
        local _TestType = EBattleTextType.TickWord_1 - 1 + BattleText_Model.m_TickTest_Now
        --爆擊率25%
        if _RandomType > 1.5 then
            _TestType = _TestType + BATTLETEXT_TICK_CRIT_ADD
        end
        BattleText_Controller.ChangeType(_TestType)
        BattleText_Model.m_TickTest_Now = BattleText_Model.m_TickTest_Now + 1
    end

    if BattleText_Model.m_BonusTest_Enable then
        _TestBonusValue = math.floor(_TestValue / 3)
    end

    if BattleText_Model.m_Test_Style == EBattleTextType.UseWugong or 
       BattleText_Model.m_Test_Style == EBattleTextType.ChangeWugong then
        _TestValue = "牛郎十八棍"
    end
    if BattleText_Model.m_Test_Style == EBattleTextType.DebuffWord then
        _TestValue = "流血"
        --_DelayTime = 3
    end

    if BattleText_Model.m_Test_Style == EBattleTextType.BuffBigWord then
        _TestValue = "牛郎十八棍"
    end

    local _TestController = RoleMgr.m_RC_Player.m_HUDController
    local _IsOther = BattleText_Model.m_Test_Style > 100 or 
                     BattleText_Model.m_Test_Style == EBattleTextType.Default or 
                     BattleText_Model.m_Test_Style == EBattleTextType.Critical
    if _IsOther and SelectMgr.m_TargetController then
        _TestController = SelectMgr.m_TargetController.m_HUDController
    end
    local _NewBattleText = BattleText_Model.GetNewBattleText(_TestController, _TestValue, BattleText_Model.m_Test_Style, _DelayTime, _TestBonusValue)
    this.m_BattleTextPool[_NewBattleText.GObj.name] = _NewBattleText
    --就你大字嬌貴，給Queue管播放
    if BattleText_Model.m_Test_Style == EBattleTextType.BuffBigWord or 
       BattleText_Model.m_Test_Style == EBattleTextType.DebuffWord then
        BattleText_Model.m_BuffBigWordQueue:Enqueue(_NewBattleText)
    else
        this.m_BattleTextPool[_NewBattleText.GObj.name]:Play()
    end
end

function BattleText_Controller.Update()
    
    if BattleText_Model.m_Test_Enable and Input.GetKeyDown(KeyCode.H) then
        if ChatMgr.m_EnterMode then
            return
        end
        CreateNewTestText()
    end

    if BattleText_Model.m_BuffBigWordQueue:Size() > 0 and m_PlayingBigBuffWord == nil then
        m_PlayingBigBuffWord = BattleText_Model.m_BuffBigWordQueue:Dequeue()
        m_PlayingBigBuffWord:Play()
    end

    if BattleText_Model.m_AutoTest_Enable and Time.time > m_TestCountDown + BattleText_Model.m_AutoTest_Frequency then
        CreateNewTestText()
        m_TestCountDown = Time.time
    end

    for key, value in pairs(this.m_BattleTextPool) do
        value:Update()
    end

end

---受擊文字
local m_ValueKind_HP =
{
    [EValueKind.Miss] =
    {
        Player = EBattleTextType.Miss,
        Other = EBattleTextType.Enemy_Miss
    },
    [EValueKind.LessHp] = 
    {
        Player = EBattleTextType.Hurt,
        Other = EBattleTextType.Default
    },
    [EValueKind.AddHp] =     
    {
        Player = EBattleTextType.MakeHP,
        Other = EBattleTextType.MakeHP
    },
    [EValueKind.State] =     
    {
        Player = EBattleTextType.State,
        Other = EBattleTextType.State
    },

    [EValueKind.CriLessHp] =     
    {
        Player = EBattleTextType.Hurt,
        Other = EBattleTextType.Critical
    },
    [EValueKind.Dodge] =     
    {
        Player = EBattleTextType.Dodge,
        Other = EBattleTextType.Enemy_Dodge
    },
    [EValueKind.Assimilate] =     
    {
        Player = EBattleTextType.Assimilate,
        Other = EBattleTextType.Enemy_Assimilate
    },
    [EValueKind.ResistNegative] =     
    {
        Player = EBattleTextType.ResistNegative,
        Other = EBattleTextType.Enemy_ResistNegative
    },
    [EValueKind.BloodShield] =     
    {
        Player = EBattleTextType.BloodShield,
        Other = EBattleTextType.Enemy_BloodShield
    },
    [EValueKind.EnegyShield] =     
    {
        Player = EBattleTextType.EnegyShield,
        Other = EBattleTextType.Enemy_EnegyShield
    },
    [EValueKind.MagNormal] =     
    {
        Player = EBattleTextType.Hurt,
        Other = EBattleTextType.MagAtk
    },
    [EValueKind.MagCrit] =     
    {
        Player = EBattleTextType.Hurt,
        Other = EBattleTextType.MagCritical
    },
}


---跳血文字判定
function BattleText_Controller.SetHPBattleText(iRoleController, iValue, iEValueKind, iTickNumber, iBonusValue)
    if m_ValueKind_HP[iEValueKind] == nil then
        return
    end
    
    local _BattleTextType = EBattleTextType.Default

    if iRoleController == RoleMgr.m_RC_Player then
        _BattleTextType = m_ValueKind_HP[iEValueKind].Player
    else
        if iTickNumber and iTickNumber > 0 and not iEValueKind == EValueKind.Dodge then

            -- 這邊加判定打出來的數值是不是內功 RM#126413 Add By Chang
            if(iEValueKind == EValueKind.MagNormal or iEValueKind == EValueKind.MagCrit) then

                _BattleTextType = EBattleTextType.TickMagWord_1 - 1 + iTickNumber
                if iEValueKind == EValueKind.MagCrit then
                    _BattleTextType = _BattleTextType + BATTLETEXT_TICK_CRIT_ADD
                end

            else
                _BattleTextType = EBattleTextType.TickWord_1 - 1 + iTickNumber
                if iEValueKind == EValueKind.CriLessHp then
                    _BattleTextType = _BattleTextType + BATTLETEXT_TICK_CRIT_ADD
                end
            end


        else
            _BattleTextType = m_ValueKind_HP[iEValueKind].Other

        end
    end


    BattleText_Controller.CreateBattleText(iRoleController, iValue, _BattleTextType, 0, iBonusValue)
end

---Tick效果字
function BattleText_Controller.SetTickBattleText(iRoleController, iValue, iETickValueKind)
    --效果種類 1.回復hp 2.回復mp 3.回復sp 4.回罡氣 11減少hp 12減少mp 13 減少sp 14.扣罡氣  21.吸HP 22.吸MP 31.引爆HP 32.引爆MP 33.反彈傷害HP
    if ETickValueKind[iETickValueKind] then
        BattleText_Controller.CreateBattleText(iRoleController, iValue, ETickValueKind[iETickValueKind])
    end
end

---寵物效果字(暫用)
function BattleText_Controller.SetPetBattleText(iRoleController, iValue, iEPetValueKind)
--效果種類 1.回復hp 2.回復mp 3.回復sp 4.回罡氣 11減少hp 12減少mp 13 減少sp 14.扣罡氣 15爆擊傷害 16.MISS  21.吸HP 22.吸MP 31.引爆HP 32.引爆MP 33.反彈傷害HP
    if EPetValueKind[iEPetValueKind] then
        BattleText_Controller.CreateBattleText(iRoleController, iValue, EPetValueKind[iEPetValueKind])
    end
end

---跳控場文字
function BattleText_Controller.SetDebuffBattleText(iSID, iValue, iDelayTime)
    if string.IsNullOrEmpty(iValue) then
        return
    end

    local _RC = RoleMgr.Get(iSID)
    if _RC == nil then
        _RC = NPCMgr.GetNPCController(iSID)
    end
    if _RC then
        return BattleText_Controller.CreateBattleText(_RC, ICON_STR .. iValue, EBattleTextType.DebuffWord, iDelayTime)
    end

end

---生出跳血文字
function BattleText_Controller.CreateBattleText(iRoleController, iValue, iEBattleTextType, iDelayTime, iBonusValue)

    local _NewBattleText = BattleText_Model.GetNewBattleText(iRoleController.m_HUDController, iValue, iEBattleTextType, iDelayTime, iBonusValue)
    this.m_BattleTextPool[_NewBattleText.GObj.name] = _NewBattleText

    --就你大字嬌貴，給Queue管播放
    if iEBattleTextType == EBattleTextType.BuffBigWord or
       iEBattleTextType == EBattleTextType.DebuffWord then
        BattleText_Model.m_BuffBigWordQueue:Enqueue(_NewBattleText)
    else
        this.m_BattleTextPool[_NewBattleText.GObj.name]:Play()
    end

    return _NewBattleText
end

--region Pool相關函數

---回收並重新設定Pool的Object
---@param iGObj GameObject 重設的物件
function BattleText_Controller.BattleText_Reset(iGObj)
    if iGObj ~= nil then
        iGObj:SetActive(false)
        iGObj.name = "Stored BattleText"
        iGObj.transform.localPosition = Vector3.zero
    end
end

---產出Pool物件
function BattleText_Controller.BattleText_Init(iGObj)
    if iGObj ~= nil then
        local _Trans = iGObj.transform
        _Trans:SetParent(this.m_Root_BattleText)
        _Trans.localPosition = Vector3.zero
        _Trans.localScale = Vector3.one

        local _InstantiateBattleText = this.m_BattleTextObj:Instantiate()
        _InstantiateBattleText.name = this.m_BattleTextObj.name
        _InstantiateBattleText.transform:SetParent(iGObj.transform)
        _InstantiateBattleText.transform.localScale = Vector3.one
        _InstantiateBattleText.transform.localPosition = Vector3.zero

    end

end

--endregion

--region 金手指專用區

---切換測試用資料
---@param iType EBattleTextType 欲測試的類型
function BattleText_Controller.ChangeType(iType)
    BattleText_Model.m_Test_Style = iType
end

---設定BattleStyle的參數(Editor版附帶存成json功能)
---@param iTextAy InputField 跟BattleTextData結構一樣的InputField
function BattleText_Controller.ModifyBattleTextStyle(iTextAy)

    local _DBT = BattleTextData.GetData(BattleText_Model.m_Test_Style)
    for key, value in pairs(iTextAy) do
        ---資料為數字要保持他的數字型態
        local _value = nil       
        if type(value) == "table" then    
            for k,v in pairs(value) do   
                _value = tonumber(v.text)
                if _value then
                    _DBT[key][k] = _value
                    _value = nil
                else
                    _DBT[key][k] = v.text
                end
                
            end
        else
            _value = tonumber(value.text)
            if _value then
                _DBT[key] = _value
                _value = nil
            else
                _DBT[key] = value.text
            end
        end 
    end

    
    if ProjectMgr.IsEditor() then
        BattleText_Model.SaveAyBattleTextData()
    end

end

---戰鬥文字撥放完畢
---@param iBattleText BattleText 撥放完的戰鬥文字
function BattleText_Controller.BattleTextOncomplete(iBattleText)

    if m_PlayingBigBuffWord == iBattleText then
        m_PlayingBigBuffWord = nil
    end

    this.m_BattleTextPool[iBattleText.GObj.name] = nil
    iBattleText:ClearData()
    BattleText_Model.Store(iBattleText)--.m_GObjPool_BattleText:Store(iBattleText.GObj) 
end
--endregion
