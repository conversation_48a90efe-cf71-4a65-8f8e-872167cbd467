﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ExtensionWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("Extension");
		<PERSON><PERSON>RegFunction("Quit", Quit);
		<PERSON><PERSON>RegFunction("GetDeviceVersion", GetDeviceVersion);
		<PERSON><PERSON>unction("SetCullDistance", SetCullDistance);
		<PERSON><PERSON>unction("GetEncoding", GetEncoding);
		<PERSON><PERSON>Function("ToUTF8Bytes", ToUTF8Bytes);
		<PERSON><PERSON>RegFunction("ToDefultBytes", ToDefultBytes);
		<PERSON><PERSON>RegFunction("BytesToString", BytesToString);
		<PERSON><PERSON>RegFunction("StringToBytes", StringToBytes);
		<PERSON><PERSON>RegFunction("StringTrim", StringTrim);
		<PERSON><PERSON>RegFunction("AddMissingComponent", AddMissingComponent);
		<PERSON><PERSON>RegFunction("IsUnityObjectNull", IsUnityObjectNull);
		<PERSON><PERSON>unction("GetTransformPositionValue", GetTransformPositionValue);
		<PERSON><PERSON>RegFunction("GetTransformLocalPositionValue", GetTransformLocalPositionValue);
		L.RegFunction("GetTransformLocalEulerAngleValue", GetTransformLocalEulerAngleValue);
		L.RegFunction("GetStr", GetStr);
		L.RegFunction("GetColor", GetColor);
		L.RegFunction("GetGameObjPool", GetGameObjPool);
		L.RegFunction("CreatePrefabObjPool", CreatePrefabObjPool);
		L.RegFunction("StartPath", StartPath);
		L.RegFunction("StartPathImmediately", StartPathImmediately);
		L.RegFunction("GetPath", GetPath);
		L.RegFunction("TryGetdepthOfField", TryGetdepthOfField);
		L.RegFunction("GetFileCRC", GetFileCRC);
		L.RegFunction("HaveNetwrok", HaveNetwrok);
		L.RegFunction("InitIAP", InitIAP);
		L.RegFunction("BuyPoint", BuyPoint);
		L.RegFunction("FinishBuy", FinishBuy);
		L.RegFunction("QueryReceipt", QueryReceipt);
		L.RegFunction("SetStateFileCheck", SetStateFileCheck);
		L.RegVar("IsApplePlatform", get_IsApplePlatform, null);
		L.RegVar("IsAndroidPlatform", get_IsAndroidPlatform, null);
		L.RegVar("IsStandalone", get_IsStandalone, null);
		L.RegVar("IsMacOS", get_IsMacOS, null);
		L.RegVar("IsEditor", get_IsEditor, null);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Quit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Extension.Quit();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDeviceVersion(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = Extension.GetDeviceVersion();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCullDistance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float[] arg0 = ToLua.CheckNumberArray<float>(L, 1);
			Extension.SetCullDistance(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEncoding(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			System.Text.Encoding o = Extension.GetEncoding(arg0);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToUTF8Bytes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			byte[] o = Extension.ToUTF8Bytes(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToDefultBytes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			byte[] o = Extension.ToDefultBytes(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BytesToString(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int, byte[]>(L, 1))
			{
				int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
				byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
				string o = Extension.BytesToString(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Text.Encoding, byte[]>(L, 1))
			{
				System.Text.Encoding arg0 = (System.Text.Encoding)ToLua.ToObject(L, 1);
				byte[] arg1 = ToLua.CheckByteBuffer(L, 2);
				string o = Extension.BytesToString(arg0, arg1);
				LuaDLL.lua_pushstring(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Extension.BytesToString");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StringToBytes(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2 && TypeChecker.CheckTypes<int, string>(L, 1))
			{
				int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				byte[] o = Extension.StringToBytes(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else if (count == 2 && TypeChecker.CheckTypes<System.Text.Encoding, string>(L, 1))
			{
				System.Text.Encoding arg0 = (System.Text.Encoding)ToLua.ToObject(L, 1);
				string arg1 = ToLua.ToString(L, 2);
				byte[] o = Extension.StringToBytes(arg0, arg1);
				ToLua.Push(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Extension.StringToBytes");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StringTrim(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			string o = Extension.StringTrim(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddMissingComponent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			System.Type arg1 = ToLua.CheckMonoType(L, 2);
			UnityEngine.Component o = Extension.AddMissingComponent(arg0, arg1);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsUnityObjectNull(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 1);
			bool o = Extension.IsUnityObjectNull(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTransformPositionValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 1);
			float arg1;
			float arg2;
			float arg3;
			Extension.GetTransformPositionValue(arg0, out arg1, out arg2, out arg3);
			LuaDLL.lua_pushnumber(L, arg1);
			LuaDLL.lua_pushnumber(L, arg2);
			LuaDLL.lua_pushnumber(L, arg3);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTransformLocalPositionValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 1);
			float arg1;
			float arg2;
			float arg3;
			Extension.GetTransformLocalPositionValue(arg0, out arg1, out arg2, out arg3);
			LuaDLL.lua_pushnumber(L, arg1);
			LuaDLL.lua_pushnumber(L, arg2);
			LuaDLL.lua_pushnumber(L, arg3);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTransformLocalEulerAngleValue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 1);
			float arg1;
			float arg2;
			float arg3;
			Extension.GetTransformLocalEulerAngleValue(arg0, out arg1, out arg2, out arg3);
			LuaDLL.lua_pushnumber(L, arg1);
			LuaDLL.lua_pushnumber(L, arg2);
			LuaDLL.lua_pushnumber(L, arg3);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStr(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Color arg0 = ToLua.ToColor(L, 1);
			string o = Extension.GetStr(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.Color o = Extension.GetColor(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGameObjPool(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			LuaFunction arg2 = ToLua.CheckLuaFunction(L, 3);
			LuaFunction arg3 = ToLua.CheckLuaFunction(L, 4);
			GameTools.Utilities.ObjectPool<UnityEngine.GameObject> o = Extension.GetGameObjPool(arg0, arg1, arg2, arg3);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreatePrefabObjPool(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
			UnityEngine.Quaternion arg2 = ToLua.ToQuaternion(L, 3);
			GameTools.Utilities.GameObjectPool o = Extension.CreatePrefabObjPool(arg0, arg1, arg2);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartPath(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			Pathfinding.Seeker arg0 = (Pathfinding.Seeker)ToLua.CheckObject<Pathfinding.Seeker>(L, 1);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
			LuaFunction arg3 = ToLua.CheckLuaFunction(L, 4);
			object[] arg4 = ToLua.ToParamsObject(L, 5, count - 4);
			Extension.StartPath(arg0, arg1, arg2, arg3, arg4);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartPathImmediately(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			Pathfinding.Seeker arg0 = (Pathfinding.Seeker)ToLua.CheckObject<Pathfinding.Seeker>(L, 1);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
			LuaFunction arg3 = ToLua.CheckLuaFunction(L, 4);
			object[] arg4 = ToLua.ToParamsObject(L, 5, count - 4);
			Extension.StartPathImmediately(arg0, arg1, arg2, arg3, arg4);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Pathfinding.Seeker arg0 = (Pathfinding.Seeker)ToLua.CheckObject<Pathfinding.Seeker>(L, 1);
			UnityEngine.Vector3 arg1 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 arg2 = ToLua.ToVector3(L, 3);
			System.Collections.Generic.List<UnityEngine.Vector3> o = Extension.GetPath(arg0, arg1, arg2);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TryGetdepthOfField(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Rendering.PostProcessing.PostProcessVolume arg0 = (UnityEngine.Rendering.PostProcessing.PostProcessVolume)ToLua.CheckObject(L, 1, typeof(UnityEngine.Rendering.PostProcessing.PostProcessVolume));
			UnityEngine.Rendering.PostProcessing.DepthOfField o = Extension.TryGetdepthOfField(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFileCRC(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.IO.FileStream arg0 = (System.IO.FileStream)ToLua.CheckObject<System.IO.FileStream>(L, 1);
			long o = Extension.GetFileCRC(arg0);
			LuaDLL.tolua_pushint64(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HaveNetwrok(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Extension.HaveNetwrok();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitIAP(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			LuaFunction arg0 = ToLua.CheckLuaFunction(L, 1);
			LuaFunction arg1 = ToLua.CheckLuaFunction(L, 2);
			LuaFunction arg2 = ToLua.CheckLuaFunction(L, 3);
			string[] arg3 = ToLua.CheckStringArray(L, 4);
			string arg4 = ToLua.CheckString(L, 5);
			Extension.InitIAP(arg0, arg1, arg2, arg3, arg4);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BuyPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			Extension.BuyPoint(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FinishBuy(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			Extension.FinishBuy(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int QueryReceipt(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Extension.QueryReceipt();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetStateFileCheck(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Extension.SetStateFileCheck();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsApplePlatform(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, Extension.IsApplePlatform);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsAndroidPlatform(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, Extension.IsAndroidPlatform);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsStandalone(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, Extension.IsStandalone);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsMacOS(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, Extension.IsMacOS);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsEditor(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushboolean(L, Extension.IsEditor);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

