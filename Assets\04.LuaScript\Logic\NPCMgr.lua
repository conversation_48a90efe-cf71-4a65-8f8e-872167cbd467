---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/SystemSettingData/NPCSetting")

---NPC管理
---@class NPCMgr
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.5.24
NPCMgr = {}
local this = NPCMgr

this.m_IS_SHOWNPCID = false and ProjectMgr.IsDebug() and ProjectMgr.IsShowDebug()

this.m_IS_SHOWNPCEVENTID = false and ProjectMgr.IsDebug() and ProjectMgr.IsShowDebug()

---@class ENPCFightMode NPC的戰鬥模式
ENPCFightMode = {
    ---無功能
    None = 0,
    ---觀賞NPC
    Display = 1,
    ---不可被攻擊
    CanNotBeAttacked = 2,
    ---被動攻擊
    PassiveAttack = 3,
    ---主動攻擊
    ActiveAttack = 4,
    ---不反擊
    DonNotFightBack = 5,
    ---攻擊不受擊
    AttackNotHit = 6,
    ---需檢查後攻擊
    AttackAfterCheck = 7,
    ---不可被玩家攻擊、可被npc攻擊、不反擊
    Week = 8
}

---@type Transform
this.m_NPCParent = nil

---@type Transform
this.m_NPC_Active = nil

---@type Transform
this.m_NPC_Stored = nil

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 角色物件池
this.m_GObjPool_NPC = {}

---@type table<NPCSID,RoleController>
this.m_Table_ActiveNPC = {}

---@type table<FlagID,table<SID,FlagShowDataType>>
this.m_Table_CanSealNPC = {}

---@type table<NPCID,table<Index,RoleController>>
local m_Table_StoreNPC = {}

---@type table<number,Vector2>
local m_Table_FakePlayerPos = {}

---@type number UpdateCD
this.m_Timer = 0

local NPC_ROOT_NAME = "NPC_Root"
local NPC_ACTIVE_NAME = "NPC_Active"
local NPC_STORED_NAME = "NPC_Stored"

--local m_Thread_ReleaseNPC = {}

---初始化
function NPCMgr.Init()
    this.m_NPCParent = GameObject.New(NPC_ROOT_NAME).transform
    this.m_NPC_Active = GameObject.New(NPC_ACTIVE_NAME).transform
    this.m_NPC_Active:SetParent(this.m_NPCParent)
    this.m_NPC_Stored = GameObject.New(NPC_STORED_NAME).transform
    this.m_NPC_Stored:SetParent(this.m_NPCParent)

    this.m_GObjPool_NPC = Extension.GetGameObjPool(100, 0, NPCMgr.RC_Reset, NPCMgr.RC_Init)
    SelectMgr.SetOnTargetClicked(
        SelectMgr.ETargetType.NPC,
        function(iGObj)
            NPCMgr.OnClick(iGObj)
        end
    )
    PlayerData.Get(EPalyerData.Flags).SetNotify("MovingFlag_OnChange", this.RefreshCollectionHUD)

end

--- 設定測試資訊
--- editor 會修改物件名稱，顯示使用/pool 中的物件數量
local function SetRootObjectName()
    if ProjectMgr.IsEditor() then
        this.m_NPC_Active.name =
            NPC_ACTIVE_NAME .. "_" .. this.m_NPC_Active.childCount .. "_" .. table.Count(this.m_Table_ActiveNPC)
        local _TempCount = 0
        for key, value in pairs(m_Table_StoreNPC) do
            _TempCount = _TempCount + table.Count(value)
        end
        this.m_NPC_Stored.name = NPC_STORED_NAME .. "_" .. this.m_NPC_Stored.childCount .. "_" .. table.Count(m_Table_StoreNPC).."_".._TempCount
    end
end

--- 永標變動時隱藏
local function SealSceneNPC(iFlag)
    if this.m_Table_CanSealNPC[iFlag] then
        for k,v in pairs(this.m_Table_CanSealNPC[iFlag]) do
            local _NpcRC = this.m_Table_ActiveNPC[k]
            if _NpcRC then
                local _isHaveflag =  PlayerData.IsHaveStaticFlag(iFlag)
                -- 1:持有永標不可見 2:持有永標可見
                if v.HaveFlag == 1 then
                    if not _isHaveflag then
                        _NpcRC:SetBeSealed(true )
                    else
                        _NpcRC:SetBeSealed(false ,v.ActParam)
                    end
                elseif v.HaveFlag == 2 then
                    if _isHaveflag then
                        _NpcRC:SetBeSealed(true ,v.ActParam)
                    else
                        _NpcRC:SetBeSealed(false)
                    end
                end
            end
        end
    end
end


--- 一次全載入時塞資料並隱藏
---@param iSID uint NPCSID
---@param iFlagShowDataType FlagShowDataType 顯示方法
---@return bool 產出時是否要顯示
local function SetSceneSealNPC(iSID, iFlagShowDataType)
    local _BeSealed = false
    for i = 1 , #iFlagShowDataType do
        local _FlagShowData = iFlagShowDataType[i]
        if _FlagShowData.EventFlagID > 0 then

            if not this.m_Table_CanSealNPC[_FlagShowData.EventFlagID] then
                local _notifyID = PlayerData_Flags[EFlags.Static].SetNotify(_FlagShowData.EventFlagID,
                    function(iGetFlag)
                        SealSceneNPC(_FlagShowData.EventFlagID)
                    end)
                this.m_Table_CanSealNPC[_FlagShowData.EventFlagID] = {}
                this.m_Table_CanSealNPC[_FlagShowData.EventFlagID].m_NotifyID = _notifyID
            end

            if this.m_Table_CanSealNPC[_FlagShowData.EventFlagID][iSID] then
                this.m_Table_CanSealNPC[_FlagShowData.EventFlagID][iSID] = {}
            end
            this.m_Table_CanSealNPC[_FlagShowData.EventFlagID][iSID] = _FlagShowData
            if not _BeSealed then
                local _isHaveflag =  PlayerData.IsHaveStaticFlag(_FlagShowData.EventFlagID)
                -- 1:持有永標可見 2:持有永標不可見
                if _FlagShowData.HaveFlag == 1 then
                    _BeSealed = not _isHaveflag
                elseif _FlagShowData.HaveFlag == 2 then
                    _BeSealed = _isHaveflag
                end
            end
        end
    end

    return _BeSealed

end

local function ClearAllSealNPCData()
    for k,v in pairs(this.m_Table_CanSealNPC) do
        if type(v) == "table" then
            for key, value in pairs(v) do
                v[key] = nil
            end
        end
        PlayerData_Flags[EFlags.Static].RemoveNotify(k, v.m_NotifyID)
        this.m_Table_CanSealNPC[k] = nil
    end
end

--- 建立一隻 NPC ( 真正創建的地方 )
local function BuildOneNPC(iSceneNPCData, iCreateDoneCallBack)
    local _EventNPCPlantData = EventNPCPlant.GetByPlantID(iSceneNPCData.m_PlantID)

    if _EventNPCPlantData == nil then
        D.LogWarning(GString.GetTextWithColor("種植檔無資料 SID:".. iSceneNPCData.m_SID, Color.Red))
        return
    end

    if _EventNPCPlantData.m_EventState == 1 then
        -- 一隻
        local _EventNPCAttr = EventNPCAttribute:Get(_EventNPCPlantData.m_SerialNo)
        if _EventNPCAttr.m_NPCMode == EventNPCMode.CopyPlayer or _EventNPCAttr.m_NPCMode == EventNPCMode.CopyPlayerCanMove then
            --把他的座標記下來
            m_Table_FakePlayerPos[iSceneNPCData.m_SID] = Vector2.New(iSceneNPCData.m_PositionX, iSceneNPCData.m_PositionZ)
            --NPCMode == 13 => 類玩家的NPC
            --感覺不用先產甚麼
            -- m_Table_FakePlayer[iSceneNPCData.m_SID] = {}
            -- m_Table_FakePlayer[iSceneNPCData.m_SID].m_EventAttr = _EventNPCAttr
        else
            local _NPCData = NPCData:Get(_EventNPCAttr.m_NPCID)
            if _NPCData then
                _NPCData = _NPCData:GetNPCData_PVE()
            else
                D.LogError("[單隻NPC] NPCData 沒有資料，NPCID : ".._EventNPCAttr.m_NPCID.."，種植ID : "..iSceneNPCData.m_PlantID)
                return
            end
            local _NPC_CreateData = RoleCreateData:NewNPC(iSceneNPCData, _NPCData, _EventNPCAttr, _EventNPCPlantData.m_EventID)
            --_NPC_CreateData.m_EventID = _EventNPCPlantData.m_EventID

            NPCMgr:CreateNPC(_NPC_CreateData, _EventNPCPlantData, _EventNPCAttr, iCreateDoneCallBack)
            MapMgr.AddCharacter(_NPC_CreateData, _EventNPCAttr.m_NPCMode)
        end
    elseif _EventNPCPlantData.m_EventState == 2 then
        -- 一群
        if iSceneNPCData.m_GroupID >= 1 and iSceneNPCData.m_GroupID <= 5 --[[_EventCrowdNPC.m_NPCCrowdAy.Count]] then
            local _EventCrowdNPC = EventCrowdNPC:Get(_EventNPCPlantData.m_SerialNo)
            local _EventNPC = _EventCrowdNPC.m_NPCCrowdAy[iSceneNPCData.m_GroupID] ---@type NPCCrowdUClient
            local _NPCData = NPCData:Get(_EventNPC.m_NPCID)
            if _NPCData ~= nil then
                _NPCData = _NPCData:GetNPCData_PVE()
                local _NPC_CreateData = RoleCreateData:NewCrowd(iSceneNPCData, _NPCData, _EventNPC, _EventNPCPlantData.m_EventID)
                --_NPC_CreateData.m_EventID = _EventNPCPlantData.m_EventID

                NPCMgr:CreateNPC(_NPC_CreateData, _EventNPCPlantData, _EventNPC, iCreateDoneCallBack)
				MapMgr.AddCharacter(_NPC_CreateData, _EventNPC.m_NPCMode)
            else
                D.LogError("[場景NPC] 一群NPC找不到NPC Data NPCID: " .. _EventNPC.m_NPCID)
            end
        end
    elseif _EventNPCPlantData.m_EventState == 3 then
        -- 漫遊
        local _EventRoamNPC = EventRoamNPC:Get(_EventNPCPlantData.m_SerialNo)
        local _NPCData = NPCData:Get(_EventRoamNPC.m_OriginNPCIdx):GetNPCData_PVE()
        local _NPC_CreateData = RoleCreateData:NewNPC(iSceneNPCData, _NPCData, _EventRoamNPC, _EventNPCPlantData.m_EventID)
        --_NPC_CreateData.m_EventID = _EventNPCPlantData.m_EventID

        NPCMgr:CreateNPC(_NPC_CreateData, _EventNPCPlantData, _EventRoamNPC, iCreateDoneCallBack)
		MapMgr.AddCharacter(_NPC_CreateData, _EventRoamNPC.m_NPCMode)
    elseif _EventNPCPlantData.m_EventState == 4 then
        --機關
        local _EventDeviceNPC = EventDeviceNPC:Get(_EventNPCPlantData.m_SerialNo)
        local _EventNPCAttr = EventNPCAttribute:Get(_EventNPCPlantData.m_SerialNo)

        local _Distance = _EventDeviceNPC.m_CollisionRadius

        if _Distance == 0 then
            if _EventNPCAttr then
                _Distance = _EventNPCAttr.m_CollisionRadius
            end
        end
        GearMgr.New(_EventNPCPlantData, iSceneNPCData,_EventDeviceNPC, _Distance)
    end
end

--- 建立一隻假玩家NPC
---@param iRoleIdx number
function NPCMgr.BuildFakePlayer(iNPCSID, iRoleIdx, iPlayerData)
    if m_Table_FakePlayerPos[iNPCSID] then
        iPlayerData.m_PositionX = m_Table_FakePlayerPos[iNPCSID].x
        iPlayerData.m_PositionZ = m_Table_FakePlayerPos[iNPCSID].y
    end

    local _NPC_CreateData = RoleCreateData:NewPlayer(iPlayerData)
    -- if m_Table_FakePlayer[iNPCSID] then
    --     _NPC_CreateData.m_NPCID = m_Table_FakePlayer[iNPCSID].m_EventAttr.m_NPCID
    -- end

    _NPC_CreateData.m_NPCID =iNPCSID
    _NPC_CreateData.m_SID = iNPCSID
    _NPC_CreateData.m_AppearData.m_IsSelf = false
    ---初次見面的登場方式 :0.一般進場 1.取消隱身進場 2:地圖傳送進場
    _NPC_CreateData.m_IsFirstShowType = ETeleportInType.ShowUpFromSneak
    --寫死讓他當13
    _NPC_CreateData.m_NPCMode = EventNPCMode.CopyPlayer

    local _Cosmetic = _NPC_CreateData.m_AppearData.m_Cosmetics

    if _Cosmetic[_NPC_CreateData.m_AppearData.m_WeaponType] == 0 then
        _Cosmetic[_NPC_CreateData.m_AppearData.m_WeaponType] = _Cosmetic[1]
    end


    ---含狀態機已經完全產出完畢
    local _doAfterCreateDone = function(iRC)
        this.m_Table_ActiveNPC[iNPCSID] = iRC
    end

    --從pool取出來
    local _GameObj = this.m_GObjPool_NPC:Get()
    _GameObj.name = iNPCSID.."_"..iRoleIdx:tostring()
    _GameObj.layer = Layer.NPC
    _GameObj.tag = "NPC"
    _GameObj:SetActive(true)

    local _RC
    --如果場景已有這個角色，把他移動過來
    -- if m_Table_FakePlayer[iRoleIdx:tostring()] then
    --     --TODO: 如果有重複的就call AppearanceMgr改資料?
    --     m_Table_FakePlayer[iRoleIdx:tostring()].m_SID = iNPCSID
    --     m_Table_FakePlayer[iRoleIdx:tostring()].m_NPCID = iNPCSID
    --     m_Table_FakePlayer[iRoleIdx:tostring()]:SetPosition(_NPC_CreateData.m_LocalPos)
    --     m_Table_FakePlayer[iRoleIdx:tostring()].AppearData = _NPC_CreateData.m_AppearData
    --     -- RoleMgr.m_RC_Player:UpdateEnhance(iChangePos, iChangeGrowTime, iChangeRarity)
    --     -- m_Table_FakePlayer[iRoleIdx:tostring()]:UpdateWeapon(_NPC_CreateData.m_AppearData.m_WeaponType)
    --     -- m_Table_FakePlayer[iRoleIdx:tostring()]:UpdateBody()
    --     this.m_Table_ActiveNPC[iNPCSID] = m_Table_FakePlayer[iRoleIdx:tostring()]
    -- else
        _RC = RoleController.New(_GameObj,_NPC_CreateData, _doAfterCreateDone)
    --end

    _RC.m_TalkAction = {}
    _RC.m_TalkAction.m_TalkMode = 0
    _RC.m_TalkAction.m_NormalTalkID = 0
    _RC.m_TalkAction.m_FightTalkID = 0

    MoveMgr.DebugLog("假玩家出生點 x:" .. iPlayerData.m_PositionX .. " y:"..iPlayerData.m_PositionZ)
    --_GameObj = nil
end

---生出玩家假NPC
function NPCMgr.BuildPlayerNPC(iCount, iPacket)
    -- local _CreateDoneCallBack = function(iRC)
    --     --只有一隻NPC加入時
    --     if SearchMgr.m_IsInitialized then
    --         if RoleMgr.GetIsInVisibleRange(iRC.transform.localPosition, SearchSetting.SEARCH_RANGE_ATTACKABLE_NPC) then
    --             SearchMgr.RefreshTable(ESearchKind.EnemyNPC)
    --         end
    --     end
    -- end

    --local _AllPlayerNpc = {}
    for i = 1, iCount do
        --local _TempNpc = {}
        local _NPCSID = iPacket:ReadUInt32()

        if _NPCSID > 0 then
            local _RoleID = iPacket:ReadUInt64()
            local _InSceneNpcData = ScenePlayerData:New(iPacket)

            --local _TempNPCPos = RoleCreateData.GetNPCLocalPos(_TempNpc.m_InSceneNpcData.m_PositionX, _TempNpc.m_InSceneNpcData.m_PositionZ)
            --local _TempPlayerPos = RoleMgr.GetSelfPlayerPos()
            --_TempNpc.m_Distance = Vector3.Distance(_TempPlayerPos, _TempNPCPos)
            --table.insert(_AllPlayerNpc, _TempNpc)
            this.BuildFakePlayer(_NPCSID, _RoleID, _InSceneNpcData)
        end
    end


end

--- 建立登入 NPC ( 接口 )
function NPCMgr.BuildLoginNPC(iCount, iPacket)
    local _AllServerNpc = {}
    for i = 1, iCount do
        local _TempNpc = {}
        _TempNpc.m_InSceneNpcData = InSceneNpcData:New(iPacket)
        local _TempNPCPos =
            RoleCreateData.GetNPCLocalPos(_TempNpc.m_InSceneNpcData.m_PositionX, _TempNpc.m_InSceneNpcData.m_PositionZ)
        local _TempPlayerPos = RoleMgr.GetSelfPlayerPos()
        _TempNpc.m_Distance = Vector3.Distance(_TempPlayerPos, _TempNPCPos)
        table.insert(_AllServerNpc, _TempNpc)
    end

    table.sort(
        _AllServerNpc,
        function(k1, k2)
            return k1.m_Distance < k2.m_Distance
        end
	)

    --先把隱藏角色的資料刪乾淨
    ClearAllSealNPCData()

	-- 測試用 ( 一幀全生，可能會比較好看到 Error )
	for key, value in pairs(_AllServerNpc) do
		BuildOneNPC(value.m_InSceneNpcData)
	end

    -- 跨場景找NPC
    SearchMgr.CrossSceneSearch()

end

--- 建立一隻 NPC ( 接口 )
function NPCMgr.BuildOneNPC(iPacket)
    local _SceneNPCData = InSceneNpcData:New(iPacket)
    local _CreateDoneCallBack = function(iRC)
        --只有一隻NPC加入時
        if SearchMgr.m_IsInitialized then
            if RoleMgr.GetIsInVisibleRange(iRC.transform.localPosition, SearchSetting.SEARCH_RANGE_ATTACKABLE_NPC) then
                SearchMgr.RefreshTable(ESearchKind.EnemyNPC)
            end
        end
    end

    BuildOneNPC(_SceneNPCData, _CreateDoneCallBack)
end

--- 取得 NPC RoleController
local function GetNpcObj(iNpcId, iCreateRoleData, iCreateDoneCallBack)
    ---@type RoleController
    local _Result
    if m_Table_StoreNPC[iNpcId] and table.Count(m_Table_StoreNPC[iNpcId]) > 0 then
        _Result = table.First(m_Table_StoreNPC[iNpcId])
        table.remove(m_Table_StoreNPC[iNpcId], 1)
        --為避免Dead影響回收SID須提前設置
        _Result.m_SID = iCreateRoleData.m_SID
        --死的要重設
        if _Result.m_StateController then
            _Result.m_StateController:ChangeState(EStateType.Idle)
        end
        _Result:SetRoleCreateData(iCreateRoleData)
        --抽出來的RC要做的更新
        _Result.m_MoveController:StopMove(true)
        _Result:SetPosition(iCreateRoleData.m_LocalPos)
        _Result:SetRotation(iCreateRoleData.m_LocalRot)
        _Result.m_HUDController.m_HUDData = iCreateRoleData.m_HUDData
        iCreateDoneCallBack(_Result)
        _Result:RefreshRelationShip()
        _Result:ModelFadeIn()
    else
        --從pool取出來
        local _GameObj = this.m_GObjPool_NPC:Get()

        --不該有東西在這裡面的(一起砍select應該沒問題)
        if _GameObj.transform.childCount > 0 then
            for i = 0 , _GameObj.transform.childCount -1 do
                local _child = _GameObj.transform:GetChild(i)
                if _child.name ~= "Select" then
                    local _childName = _child.name
                    _child.gameObject:Destroy()
                    ResourceMgr.Unload(_childName)
                end
            end
        end
        _Result = RoleController.New(_GameObj, iCreateRoleData, iCreateDoneCallBack)
    end

    _Result.m_Lv = iCreateRoleData.m_Lv
    _Result.gameObject.name = tostring(iCreateRoleData.m_SID) .. "_" .. tostring(iCreateRoleData.m_EventID) .. "_" .. iCreateRoleData.m_NPCAppearData.m_ModelName
    _Result.gameObject.layer = Layer.NPC
    _Result.gameObject.tag = "NPC"

    _Result:UpdateHUD(EHUD_UpdateChecker.Name)
    ---提供紀錄hash方法 方便後續歸還音效
    local _FillSoundHash = function(iHash)
        _Result.m_SoundHash = iHash
    end
    ---NPC有常駐音效的話 播放音效
    if iCreateRoleData.m_NPCAppearData.m_DebutSoundID ~= 0 and iCreateRoleData.m_NPCAppearData.m_DebutSoundID then
        AudioMgr.PlayAudio_LongTermNPC(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.SFX, iCreateRoleData.m_NPCAppearData.m_DebutSoundID,  _Result.gameObject.transform, true, true, _FillSoundHash, 1)
    end

    return _Result
end

---@param iCreateRoleData RoleCreateData
---@param iEventNPCPlantData EventNPCPlant 主種植檔
---@param iNPCEventData EventNPCAttribute 副種植檔
---@param iNPCEventData EventRoamNPC 漫遊副種植檔
---@param iNPCEventData EventCrowdNPC 群怪副種植檔
---@param iCreateDoneCallBack function
function NPCMgr:CreateNPC(iCreateRoleData, iEventNPCPlantData, iNPCEventData, iCreateDoneCallBack)

    ---@param iRC RoleController
    local _doAfterCreateDone = function(iRC)
        if this.m_Table_ActiveNPC[iCreateRoleData.m_SID] then
            D.LogWarning("NPC還沒死透就收到重生: ".. this.m_Table_ActiveNPC[iCreateRoleData.m_SID].m_DebugIdentifyName)
            NPCMgr.RemoveActiveNPC(iCreateRoleData.m_SID)
        end

        iRC.transform:SetParent(this.m_NPC_Active)
        this.m_Table_ActiveNPC[iCreateRoleData.m_SID] = iRC

        if iNPCEventData.m_FlagShowDataType then
            --- 是否被隱藏，沒動作參數直接關掉
            iRC:SetBeSealed(SetSceneSealNPC(iCreateRoleData.m_SID, iNPCEventData.m_FlagShowDataType))
        end

        SetRootObjectName()

        if iCreateRoleData.m_MoveSpeed then
            local _EndPos = GFunction.ServerPosToScenePosWithoutHeight(Vector2(iCreateRoleData.m_TargetPosX, iCreateRoleData.m_TargetPosZ))
            local _StartPos = iCreateRoleData.m_LocalPos

            NPCMgr.SetNPCMovement(iCreateRoleData.m_SID, _StartPos, _EndPos, iCreateRoleData.m_MoveSpeed)
        end

        -- 泡泡框
        iRC.m_TalkAction = {}
        iRC.m_TalkAction.m_TalkMode = 0
        iRC.m_TalkAction.m_NormalTalkID = 0
        iRC.m_TalkAction.m_FightTalkID = 0

        if iNPCEventData.m_Attribute then
            iRC.m_TalkAction.m_TalkMode = iNPCEventData.m_Attribute.m_TalkMode
            iRC.m_TalkAction.m_NormalTalkID = iNPCEventData.m_Attribute.m_NormalTalkID -- 自主發話編號
            iRC.m_TalkAction.m_FightTalkID = iNPCEventData.m_Attribute.m_FightTalkID -- 戰鬥發話編號
        end
        BubbleMgr.StartBubble(iRC.m_SID)

        if iCreateDoneCallBack then
            iCreateDoneCallBack(iRC)
        end
        iRC:UpdatHUDHP(iCreateRoleData.m_HUDData.m_MaxHp,iCreateRoleData.m_HUDData.m_CurHp)

    end

    iCreateRoleData:SetNPCMode(iNPCEventData.m_NPCMode)

    ---@type RoleController
    local _RC

    _RC = GetNpcObj(iCreateRoleData.m_NPCID, iCreateRoleData, _doAfterCreateDone)

    _RC.m_CharacterController = Extension.AddMissingComponent(_RC.gameObject, typeof(CharacterController))
    _RC.m_CharacterController.radius = iCreateRoleData:GetRadius()
    _RC.m_CharacterController.height = iCreateRoleData:GetHeight()

    EffectMgr.PlayEffectWithParent(EEffectType.Model, ModelSetting.m_NpcShowEffect, _RC.transform)

end

--- 真的移除 NPC
---@param iRC RoleController
function NPCMgr.ReleaseNpc(iRC)
    if iRC.m_BoundingSphereIdx then
        CullingGroupMgr.Inst:ReleaseBounding(iRC.m_BoundingSphereIdx)
    end
    -- 釋放地圖Icon
    MapMgr.RemoveCharacter_withID(iRC.m_SID, MapMgr.ECharacterType.NPC, iRC.m_NPCID)
    iRC:ReleaseHUD()
    --iRC:GetModelController():IsRelease()
    if not Extension.IsUnityObjectNull(iRC.gameObject) then
        this.m_GObjPool_NPC:Store(iRC.gameObject)
    end
    iRC = nil
    SetRootObjectName()
end

---切場景或登出，都把它放到store裡面在一起放掉
local function RemoveNPCModelController()
    for _npcid,_RCTable in pairs(m_Table_StoreNPC) do
        for k,_rc in pairs(_RCTable) do
            NPCMgr.ReleaseNpc(_rc)
            _RCTable[k] = nil
        end
        m_Table_StoreNPC[_npcid] = nil
    end

    if BattleMgr.m_IsAffectModelUpdate then
        NPCMgr.ReleaseNpc(BattleMgr.GetBE_RC())
        BattleMgr.SetBE_RC(nil)
    end
end

--- 釋放所有 NPC
function NPCMgr.ResetAll()
    for key, value in pairs(this.m_Table_ActiveNPC) do
        NPCMgr.RemoveActiveNPC(value.m_SID)
    end
    table.Clear(this.m_Table_ActiveNPC)
    RemoveNPCModelController()
end

-- 把暫時用不到的 NPC 放到 pool
local function StoreNPC(iSID)
    if this.m_Table_ActiveNPC[iSID] then
        --把要釋放的NPC存取到Store裡
        local _NpcId = this.m_Table_ActiveNPC[iSID].m_NPCID
        local _RC = this.m_Table_ActiveNPC[iSID]
        if not m_Table_StoreNPC[_NpcId] then
            m_Table_StoreNPC[_NpcId] = {}
        end

        _RC.Time = System.DateTime.Now
        table.insert(m_Table_StoreNPC[_NpcId], _RC)
        _RC.m_MoveController:StopMove(true)
        _RC:SetPosition(Vector3(0, 0, 0))
        if not Extension.IsUnityObjectNull(_RC.transform) then
            _RC.transform:SetParent(this.m_NPC_Stored)
            if ProjectMgr.IsEditor() then
                _RC.gameObject.name = "Stored Role_" .. tostring(_NpcId)
            end
        end

        this.m_Table_ActiveNPC[iSID] = nil
    end
end

--- 釋放單隻 NPC
---@param iSID Number NPC SID
function NPCMgr.RemoveActiveNPC(iSID)
    BuffMgr.ClearRoleBuffData(iSID)
    if this.m_Table_ActiveNPC[iSID] then
        ---如果NPC有常駐音效hash的紀錄 歸還他
        if this.m_Table_ActiveNPC[iSID].m_SoundHash then
            AudioMgr.ReturnAudio(this.m_Table_ActiveNPC[iSID].m_SoundHash)
            this.m_Table_ActiveNPC[iSID].m_SoundHash = nil
        end
        StoreNPC(iSID)
    end

    SetRootObjectName()
end

function NPCMgr.OnClick(iGObj)
    local _RC = NPCMgr.GetNPCControllerGObj(iGObj)
    if _RC then
        _RC:OnClick()
    end
end

--region 找到NPC的RoleController方法

---用場景物件名稱找到NPC的RoleController
---@param iSID ushort 場景物件名稱GameObject.Name
function NPCMgr.GetNPCController(iSID)
    if type(iSID) == "string" then
        iSID = tonumber(iSID)
    end
    if this.m_Table_ActiveNPC[iSID] ~= nil then
        return this.m_Table_ActiveNPC[iSID]
    end
end

---找尋場景是否有符合RoleController.m_NPCID的對象
---@param iNPCID ushort 要找的RoleController.m_NPCID
function NPCMgr.GetNPCControllerNPCID(iNPCID)
    for key, value in pairs(this.m_Table_ActiveNPC) do
        if value.m_NPCID == iNPCID then
            return value
        end
    end
end

---直接用場景物件找到NPC的RoleController
function NPCMgr.GetNPCControllerGObj(iGObj)
    for key, value in pairs(this.m_Table_ActiveNPC) do
        if value.gameObject == iGObj then
            return value
        end
    end
end

---直接用場景物件找到NPC的RoleController
function NPCMgr.GetNPCControllerEventID(iEventID)
    local _Return = {}
    for key, value in pairs(this.m_Table_ActiveNPC) do
        if value.m_NPCEventController and value.m_NPCEventController.m_EventID == iEventID then
            table.insert(_Return, value)
            --return value
        end
    end
    return _Return
end
--endregion

function NPCMgr.NPCDie(iID)
    if this.m_Table_ActiveNPC[iID] then
        if this.m_Table_ActiveNPC[iID].m_StateController then
            this.m_Table_ActiveNPC[iID].m_StateController:ChangeState(EStateType.Dead)
        else
			D.LogError("這隻NPC居然不會死 m_StateController掉了: ".. this.m_Table_ActiveNPC[iID].m_DebugIdentifyName)
        end
    end
end

---NPC撥放情境動作
function NPCMgr.NPCDoAnim(iData, iActId, isLoop)
    if this.m_Table_ActiveNPC[iData] then
        this.m_Table_ActiveNPC[iData]:DoAnim(iActId, isLoop)
    end
end

---NPC開始移動
---@param iStartPos Vector3 起始點Y為0的Vector3
---@param iEndPos Vector3 終點Y為0的Vector3
function NPCMgr.SetNPCMovement(iSID, iStartPos, iEndPos, iSpeed)

    if this.m_Table_ActiveNPC[iSID] ~= nil then
        --local _StartPos = MoveMgr.GetFloorPosition(iStartPos)
        iEndPos:GetFloorPosition()

        this.m_Table_ActiveNPC[iSID]:OtherVectorMove(iEndPos, iSpeed)

        if this.m_Table_ActiveNPC[iSID] == SelectMgr.m_TargetController then
            MoveMgr.DebugLog(("9-5 NPC開始移動 起點 x ="..iStartPos.x..", y ="..iStartPos.z))
            MoveMgr.DebugLog(("9-5 NPC開始移動 終點 x ="..iEndPos.x..", y ="..iEndPos.z))
        end

    end
end

---取消上次位移後，直接從目前位置到點
function NPCMgr.StopNPCMovement(iSID, iEndPos)

    if this.m_Table_ActiveNPC[iSID] ~= nil then
        if iEndPos == nil then
            iEndPos =
                Vector3(
                this.m_Table_ActiveNPC[iSID].transform.localPosition.x,
                0,
                this.m_Table_ActiveNPC[iSID].transform.localPosition.z
            )
        end
        --- 原地停止 NPC 移動

        iEndPos:GetFloorPosition()
        if this.m_Table_ActiveNPC[iSID].m_CurrentCulling and this.m_Table_ActiveNPC[iSID].m_CurrentCulling < ECullingState.OUT_OF_RANGE then
            this.m_Table_ActiveNPC[iSID]:MoveToTargetPos(nil, iEndPos, 42)
        else
            this.m_Table_ActiveNPC[iSID]:SetPosition(iEndPos)
        end
        if this.m_Table_ActiveNPC[iSID] == SelectMgr.m_TargetController then
            MoveMgr.DebugLog(("9-6 NPC結束移動 終點 x ="..iEndPos.x..", y ="..iEndPos.z))

        end
    end
end

---@type table<number, RoleController>
NPCMgr.m_ActiveCullingTable = {}

function NPCMgr.Update()

    for k, v in pairs(this.m_Table_ActiveNPC) do
        v:Update()
    end

    if BattleMgr.m_IsAffectModelUpdate then
        local _EditorRC = BattleMgr.GetBE_RC()
        if _EditorRC then
            _EditorRC:Update()
        end
    end

end

function NPCMgr.LateUpdate()
    -- CullingGroupEvent 變動後更新
    for k, v in pairs(this.m_ActiveCullingTable) do
        if v:CullingUpdate() then
            this.m_ActiveCullingTable[k] = nil
        end
    end

    for k, v in pairs(this.m_Table_ActiveNPC) do
        if v.m_CurrentCulling and v.m_CurrentCulling < ECullingState.OUT_OF_RANGE --[[v.gameObject.activeSelf and RoleMgr.GetIsInVisibleRange(v.transform.position, 50)]] then
            v:LateUpdate()
        end
    end

    if BattleMgr.m_IsAffectModelUpdate then
        local _EditorRC = BattleMgr.GetBE_RC()
        if _EditorRC then
            _EditorRC:LateUpdate()
        end
    end
end

function NPCMgr.RC_Init(iGObj)
    if iGObj ~= nil then
        iGObj.transform:SetParent(this.m_NPC_Stored)
        iGObj.transform.localPosition = Vector3.zero
        iGObj.transform.localScale = Vector3.one

        Extension.AddMissingComponent(iGObj, typeof(CharacterController))
    end
end

function NPCMgr.RC_Reset(iGObj)
    if iGObj ~= nil then
        --iGObj:SetActive(false)
        iGObj.transform:SetParent(this.m_NPC_Stored)
    end
end

--- 刷新 HUD 名稱
function NPCMgr.RefreshHUDName()
    for key, value in pairs(this.m_Table_ActiveNPC) do
        value:UpdateHUD(EHUD_UpdateChecker.Name)
    end
end

---動標更新時需要重刷採集物HUD
function NPCMgr.RefreshCollectionHUD(iFlag)
    -- TODO: 待優化搜尋方式
    for key, value in pairs(this.m_Table_ActiveNPC) do
        if value.m_NPCMode == EventNPCMode.Collection then
            value:CullingUpdate()
        else
            value:UpdateHUD(EHUD_UpdateChecker.Icon)
        end
    end
end

function NPCMgr.OnUnrequire()
    if this.m_NPCParent.gameObject then
        this.m_NPCParent.gameObject:Destroy()
    end
    return true
end

return NPCMgr
