---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Class Describes 腳色屬性介面>永丹子分頁 用來顯示永丹的以實用狀況以及資訊 
---@class RoleAttribute_Page_Attribute_Detail_Medicine
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.2.2
RoleAttribute_Page_Attribute_Detail_Medicine = {}
local this = RoleAttribute_Page_Attribute_Detail_Medicine

---目前的篩選介面 index 篩選介面開啟後 並點擊相關按鍵後造成的index變化
local m_YuDanFilterChipType = EChipFilterChipType.AllChip
local m_YuDanFilterChipComplete = EChipFilterChipComplete.AllChip

---目前的篩選index 顯示中的所有永丹是依照這個 篩選規則來決定是否顯示
local m_LastYuDanFilterChipType = EChipFilterChipType.AllChip
local m_LastYuDanFilterChipComplete = EChipFilterChipComplete.AllChip
---永丹 篩選介面 永丹類型相關的按鍵文字
local ChipFilterChipTypeStringIDTable = 
{
    20111003,
    20111004,
    20111005,
    20111006,
}

 ---永丹 篩選介面 完成度相關的按鍵文字
 local ChipFilterChipCompleteStringIDTable = 
 {
    20111003,
    20111007,
    20111008,
 }

 ---目前要顯示的永丹(晶片訊息)
 local m_CurrentShowTable = {}
 ---篩選 永丹類別按鍵table
local m_YuDanTypeBtnTable = {}
 ---篩選 永丹完成度按鍵table
local m_YuDanCompleteBtnTable = {}

---   永丹吃滿 顯示用的圖片
local m_FullUseIconName = "Chip_but002_D"
--- 永單位吃滿 顯示用的圖片
local m_NonFullUseIconName = "Chip_but001_D"

--- 因為還沒有商城 背包沒有對應永丹道具時 先反灰 之後有商城會移除
local _BtnGreyColor = Extension.GetColor("#6A6A6A")

---初始化 晶片篩選 晶片類型 按鍵
 local InitialChipTypeBtns = function()

    ---永丹 篩選介面 永丹類型相關的按鍵
    for i = 1, table.Count(EChipFilterChipType) do
        local _ItemObject = nil
        -- 第一個 用本人
        if i == 1 then
            _ItemObject = this.m_GObj_YuDanTypeBtn
        -- 其他 產生新的物件
        else
            _ItemObject = this.m_GObj_YuDanTypeBtn:Instantiate(this.m_GObj_YuDanTypeBtn)
        end
        _ItemObject:SetParent(this.m_GObj_YuDanTypeBtnParent)
        
        local _ButtonEx = Button.New(_ItemObject.gameObject)
        _ButtonEx:SetText(TextData.Get(ChipFilterChipTypeStringIDTable[i]))

        local _UISet = {}
        _UISet.m_YuDanType = i
        table.insert(m_YuDanTypeBtnTable,_UISet)
    end

    this.m_GroupBtn_YuDanType = GroupButton.New(this.m_GObj_YuDanTypeBtnParent)
    this.m_GroupBtn_YuDanType:ReacquireAllButtonsUnderGroup()

        for i = 1, this.m_GroupBtn_YuDanType.m_UIGroupBtnCtrl.ButtonCount do

		    GroupButton.AddListenerByIndex(this.m_GroupBtn_YuDanType,i,EventTriggerType.PointerClick,function()
                m_YuDanFilterChipType = i
		    end)
	    end

    ---永丹 篩選介面 完成度相關的按鍵
    for i = 1, table.Count(EChipFilterChipComplete) do
        local _ItemObject = nil
        -- 第一個 用本人
        if i == 1 then
            _ItemObject = this.m_GObj_YuDanCompleteBtn
        -- 其他 產生新的物件
        else
            _ItemObject = this.m_GObj_YuDanCompleteBtn:Instantiate(this.m_GObj_YuDanCompleteBtn)
        end
        _ItemObject:SetParent(this.m_GObj_YuDanCompleteBtnParent)
        
        local _ButtonEx = Button.New(_ItemObject.gameObject)

        _ButtonEx:SetText(TextData.Get(ChipFilterChipCompleteStringIDTable[i]))
        
        local _UISet = {}
        _UISet.m_YuDanComplete = i
        table.insert(m_YuDanCompleteBtnTable,_UISet)
    end

    this.m_GroupBtn_YuDanComplete = GroupButton.New(this.m_GObj_YuDanCompleteBtnParent)
    this.m_GroupBtn_YuDanComplete:ReacquireAllButtonsUnderGroup()

        for i = 1, this.m_GroupBtn_YuDanComplete.m_UIGroupBtnCtrl.ButtonCount do

		    GroupButton.AddListenerByIndex(this.m_GroupBtn_YuDanComplete,i,EventTriggerType.PointerClick,function()
                m_YuDanFilterChipComplete = i
		    end)
	    end
end

---初始化
function RoleAttribute_Page_Attribute_Detail_Medicine.Init(iController)

    --設定RolAttribute_Controller
    this.m_Controller = iController.m_UIController
    --設定ViewRef
    this.m_ViewRef = iController.m_ViewRef
    --永丹介面本體 
    this.m_Page_GameObject = this.m_ViewRef.m_Dic_Trans:Get("&YudanPage").gameObject

    --單項永丹 物件
    this.m_MedicineUnit = this.m_ViewRef.m_Dic_Trans:Get("&MedicineUnit").gameObject

    --ScrollView 項目掛載的 parent
    this.m_ContentMedicineList = this.m_ViewRef.m_Dic_Trans:Get("&ContentSubAttributeList_Medicine")
    
    --UIScrollView 掛腳本的物件
    this.m_ScrollView_Medicine = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Medicine").gameObject

    --設定有用的資料 永丹表不開放 或者 從永丹表取得的物品ID無法在物品表取得訊息的資料 不會在介面顯示
    RoleAttribute_Page_Attribute_Detail_Medicine:SetAvailableList()

    ---換語言重登的初始化 不知為何 this.m_ScrollView 會有數值 所以強制清空 
    this.m_ScrollView = nil

    --篩選這次要顯示的永丹
    RoleAttribute_Page_Attribute_Detail_Medicine.FilterCurrentYuDanTable()

    --ScrollView 腳本 本身初始化
    this.m_ScrollView = ScrollView.Init(this.m_ScrollView_Medicine,false,this.m_MedicineUnit, this.ReturnCount,this.AfterReuseItemInit,this.AfterReuseItemIndexUpdate,true)

    --- 登記觀察者 使用永丹
    GStateObserverManager.Register(EStateObserver.UpdateYuDan, this)
    --- 登記觀察者 背包資訊變化
    GStateObserverManager.Register(EStateObserver.BagDataRefresh, this)

    ---移動到細節屬性介面的最上方 按鍵
    this.m_ToTopScrollBtn = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ScrollToTop_Medicine").gameObject
    Button.AddListener(this.m_ToTopScrollBtn, EventTriggerType.PointerClick, 
                            function() RoleAttribute_Page_Attribute_Detail_Medicine.ClickToTopItem() end)


    ---永丹/晶片種類篩選介面
    this.m_GObj_ChipGroupPanel = this.m_ViewRef.m_Dic_Trans:Get("&ChipGroupPanel").gameObject


    ---按鍵 開啟/關閉永丹/晶片種類篩選介面
    this.m_Btn_ChipGroupPanel = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ChipGroupPanel").gameObject
    Button.AddListener(this.m_Btn_ChipGroupPanel, EventTriggerType.PointerClick, function() 
                            RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanGroupPanel( not this.m_GObj_ChipGroupPanel.activeSelf)
                        end)

    ---永丹篩選按鍵 晶片類型
    this.m_GObj_YuDanTypeBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_ChipType")
    ---永丹篩選按鍵 晶片類型 要掛的父物件
    this.m_GObj_YuDanTypeBtnParent = this.m_ViewRef.m_Dic_Trans:Get("&ChipTypeBtnParent")

    ---永丹篩選按鍵 晶片完成狀況
    this.m_GObj_YuDanCompleteBtn = this.m_ViewRef.m_Dic_Trans:Get("&Button_ChipComplete")
    ---永丹篩選按鍵 晶片完成狀況 要掛的父物件
    this.m_GObj_YuDanCompleteBtnParent = this.m_ViewRef.m_Dic_Trans:Get("&ChipCompleteBtnParent")

    InitialChipTypeBtns()

    ---永單篩選介面 確認使用目前篩選結果 按鍵
    this.m_Btn_ConfirmCheckChipType = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ConfirmCheckChipType").gameObject
    Button.AddListener(this.m_Btn_ConfirmCheckChipType, EventTriggerType.PointerClick, function() 
                                RoleAttribute_Page_Attribute_Detail_Medicine.FilterCurrentYuDanTable()
                                RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanGroupPanel(false)
                                m_LastYuDanFilterChipComplete = m_YuDanFilterChipComplete
                                m_LastYuDanFilterChipType = m_YuDanFilterChipType
                            end)

    --永丹加乘效果 Scrollview單項物件
    this.m_YuDanUnit = this.m_ViewRef.m_Dic_Trans:Get("&YudanEffectUnit").gameObject
    
    --永丹加乘效果ScrollView 項目掛載的 parent
    this.m_Content_YudanEffect = this.m_ViewRef.m_Dic_Trans:Get("&ContentYuDanEffect")

    --UIScrollView 掛腳本的物件
    this.m_ScrollView_YuDanEffect_Script = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_YuDanEffect").gameObject

    --ScrollView 腳本 本身初始化
    this.m_ScrollView_YuDanEffect = ScrollView.Init(this.m_ScrollView_YuDanEffect_Script,false,this.m_YuDanUnit, this.ReturnCount_YuDanEffect,this.AfterReuseItemInit_YuDanEffect,this.AfterReuseItemIndexUpdate_YuDanEffect,true)

    ---物件 - 所有永丹的加乘結果頁面
    this.m_Gobj_YuDanEffectPage = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_YuDanEffectPage").gameObject

    ---按鍵 開啟所有永丹的加乘結果頁面
    this.m_Btn_ShowYudanEffectPage = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ShowYudanEffectPage").gameObject
    Button.AddListener(this.m_Btn_ShowYudanEffectPage, EventTriggerType.PointerClick, function() 
                                RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanEffectPage(true)
                        end)

    ---按鍵 關閉所有永丹的加乘結果頁面
    this.m_Btn_CloseYuDanEffectPage= this.m_ViewRef.m_Dic_Trans:Get("&Btn_CloseYuDanEffectPage").gameObject
    Button.AddListener(this.m_Btn_CloseYuDanEffectPage, EventTriggerType.PointerClick, function() 
                                RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanEffectPage(false)
                        end)
    ---移動到細節屬性介面的最上方 按鍵
    this.m_ToTopScrollBtn_YuDanEffect = this.m_ViewRef.m_Dic_Trans:Get("&Btn_ScrollToTop_YuDanEffect").gameObject
    Button.AddListener(this.m_ToTopScrollBtn_YuDanEffect, EventTriggerType.PointerClick, 
                            function() RoleAttribute_Page_Attribute_Detail_Medicine.ClickToTopItem_YuDanEffect() end)

    
    GroupButton.OnPointerClickByIndex(this.m_GroupBtn_YuDanType, EChipFilterChipType.AllChip)
    GroupButton.OnPointerClickByIndex(this.m_GroupBtn_YuDanComplete, EChipFilterChipComplete.AllChip)

end

function RoleAttribute_Page_Attribute_Detail_Medicine.OnDestroy()
    GStateObserverManager.UnRegister(EStateObserver.UpdateYuDan, this)
    GStateObserverManager.UnRegister(EStateObserver.BagDataRefresh, this)
    return true
end

---Update
function RoleAttribute_Page_Attribute_Detail_Medicine.Update()

    if this.m_Page_GameObject.activeSelf then
        
        --ScrollView 返回頂端的按鍵  ScrollView 在最頂部時不顯示
        this.m_ToTopScrollBtn:SetActive(this.m_ContentMedicineList.localPosition.y > 0)

        --ScrollView 永丹效果返回頂端的按鍵  ScrollView 在最頂部時不顯示
        this.m_ToTopScrollBtn_YuDanEffect:SetActive(this.m_Content_YudanEffect.localPosition.y > 0)
    end
end


---ScrollView
---移動到細節屬性介面的最上方
function RoleAttribute_Page_Attribute_Detail_Medicine.ClickToTopItem()
    ScrollView.UpdateToFirst(this.m_ScrollView)
end

--ScrollView 初始化必須配置 初始化Scroll 下面的項目
function RoleAttribute_Page_Attribute_Detail_Medicine.AfterReuseItemInit(iItem,iRowIdx)
    if iItem == nil then
        return
    end

    ---初始化的時候生成icon 並掛到 item下方
    local _YuDan= {}
    _YuDan.gameObject = iItem.m_GObj
    _YuDan.m_GObj_YuDanDataSet = _YuDan.gameObject.transform:Find( "GObj_YuDanDataSet")
    _YuDan.m_MedicineIconParent = _YuDan.m_GObj_YuDanDataSet:Find( "MedicineIcon")
    iItem.m_Icon = IconMgr.NewItemIcon(0, _YuDan.m_MedicineIconParent, 70)
    iItem.m_Icon.gameObject.transform.localPosition = Vector3(2,-1.5,0)
    iItem.m_Icon.m_TMP_Count.text = ""
    iItem.m_Icon:SetClickTwice( false ) 

end

--ScrollView 初始化必須配置 更新Scroll 下面的項目
function RoleAttribute_Page_Attribute_Detail_Medicine.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    if iItem == nil  then
        return
    elseif iRowIdx > this.ReturnCount() then
        iItem.m_GObj:SetActive(iRowIdx <= this.ReturnCount())
        return
    end

    RoleAttribute_Page_Attribute_Detail_Medicine.SetYuDanListRowItem(iItem )
    iItem.m_GObj:SetActive(iRowIdx <= this.ReturnCount())
end

---將永丹訊息 刷新到對應UI
function RoleAttribute_Page_Attribute_Detail_Medicine.SetYuDanListRowItem(iItem)

    local _DataType = m_CurrentShowTable[iItem.m_Index]

    

    if _DataType.m_IsYuDanData == true then
        
        --永丹串表的資訊
        local _YuDanData = TakemedicineData.GetTakemedicineDataByIdx(m_CurrentShowTable[iItem.m_Index].m_IDx)
        --永丹紀錄在物品串表的串表資料
        local _YuDanDataFromItem = ItemData.GetItemDataByIdx(_YuDanData.m_ItemID)


        local _YuDan= {}
        _YuDan.gameObject = iItem.m_GObj
        _YuDan.m_GObj_YuDanDataSet = _YuDan.gameObject.transform:Find( "GObj_YuDanDataSet")
        _YuDan.m_GObj_YudanTypeSet = _YuDan.gameObject.transform:Find( "GObj_YuDanTypeSet")

        _YuDan.m_MedicineIconParent = _YuDan.m_GObj_YuDanDataSet:Find( "MedicineIcon")
        _YuDan.m_MedicineNameTMP = _YuDan.m_GObj_YuDanDataSet:Find("MedicineNameTMP"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _YuDan.m_MedicineEffectTMP = _YuDan.m_GObj_YuDanDataSet:Find("MedicineEffectTMP"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _YuDan.m_MedicineConditionTMP = _YuDan.m_GObj_YuDanDataSet:Find("MedicineConditionTMP"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        --- 對應永丹的 物品ID 流水後 之後使用永丹按鍵需要此資訊 在資料刷新時刷新此資訊
        _YuDan.m_ItemID =0

        _YuDan.m_UseBtn = _YuDan.m_GObj_YuDanDataSet:Find("UseMedicineBtn")

        _YuDan.gameObject:SetActive(true)

        --永丹使用次數
        local _MedicienTime = PlayerData_Attributes.GetYuDanUseCodition(_YuDanData.m_Idx) .."/" .. tostring(_YuDanData.m_UseLimit)
        --填入永丹名稱
        _YuDan.m_MedicineNameTMP.text = TextData.Get(_YuDanDataFromItem.m_Idx_ItemNameText)
        --填入永丹效果說明
        _YuDan.m_MedicineEffectTMP.text = TextData.Get(_YuDanData.m_ItemInfo)
        --填入永丹 使用次數/最大次數
        _YuDan.m_MedicineConditionTMP.text = _MedicienTime
        --填入永丹的物品ID流水號
        _YuDan.m_ItemID = _YuDanDataFromItem.m_Idx
        iItem.m_Icon:RefreshIcon(_YuDanDataFromItem.m_Idx)
        --檢查是否有對應永丹物品 
        local _WithYuDanInBag = RoleAttribute_Page_Attribute_Detail_Medicine.CheckHasYuDanByItemID(_YuDan.m_ItemID)
        --檢查對應類型永丹道具能不能被使用
        local _CandUse = ( PlayerData_Attributes.GetYuDanUseCodition(_YuDanData.m_Idx) < _YuDanData.m_UseLimit)

        if _CandUse then
            _YuDan.m_UseBtn.gameObject:GetComponent(typeof(Image)).color = (_WithYuDanInBag) and Color.White or _BtnGreyColor
            SpriteMgr.Load(m_NonFullUseIconName,_YuDan.m_UseBtn.gameObject:GetComponent(typeof(Image)))
        else
            _YuDan.m_UseBtn.gameObject:GetComponent(typeof(Image)).color = Color.White
            SpriteMgr.Load(m_FullUseIconName,_YuDan.m_UseBtn.gameObject:GetComponent(typeof(Image)))
        end

        Button.ClearListener(_YuDan.m_UseBtn)
        --在屬性介面直接使用永丹
        Button.AddListener(_YuDan.m_UseBtn, EventTriggerType.PointerClick, 
        function() 
                            if _WithYuDanInBag and _CandUse then
                                RoleAttribute_Page_Attribute_Detail_Medicine.UseYuDan(_YuDan.m_ItemID, 1) 
                            elseif _WithYuDanInBag == false and _CandUse == true then
                                D.Log("引導去商城")
                               ---To Do 引導去商城
                            else
                                ---To Do 已經吃滿要怎麼處理
                            end
                            
                        end)

        _YuDan.m_GObj_YuDanDataSet.gameObject:SetActive(true)
        _YuDan.m_GObj_YudanTypeSet.gameObject:SetActive(false)

    ---要填入的是 攻擊/防禦/其他 的屬性文字
    elseif _DataType.m_IsYuDanData == false then

        local _YuDan= {}
        _YuDan.gameObject = iItem.m_GObj
        _YuDan.m_GObj_YuDanDataSet = _YuDan.gameObject.transform:Find("GObj_YuDanDataSet")
        _YuDan.m_GObj_YudanTypeSet = _YuDan.gameObject.transform:Find("GObj_YuDanTypeSet")

        _YuDan.m_TMP_Type = _YuDan.m_GObj_YudanTypeSet:Find("TMP_Type"):GetComponent(typeof(TMPro.TextMeshProUGUI))        
        _YuDan.m_TMP_Type.text = TextData.Get(_DataType.m_YudanTypeStringID) 

        _YuDan.m_GObj_YuDanDataSet.gameObject:SetActive(false)
        _YuDan.m_GObj_YudanTypeSet.gameObject:SetActive(true)

    end
end

-- 從串表取得永丹種類 數量
function RoleAttribute_Page_Attribute_Detail_Medicine.ReturnCount()
    return table.Count(m_CurrentShowTable)
end

-- 判斷背包是否有對應的永丹物品
---@param iItemID number 永丹對應的物品Idx
---@return boolean 是否有此物品
function RoleAttribute_Page_Attribute_Detail_Medicine.CheckHasYuDanByItemID(iItemID)
    return BagMgr.IsItemInBag( iItemID, nil )
end

---使用在屬性面板 使用永丹
---@param iItemID number 永丹物品的流水號
---@param iAmount number 永丹物品的使用數量
function RoleAttribute_Page_Attribute_Detail_Medicine.UseYuDan(iItemID,iAmount)
    ---todo 接使用物品協定
    BagMgr.UseItembyItemIdx(iItemID,iAmount)
end

---開啟屬性永丹分頁
function RoleAttribute_Page_Attribute_Detail_Medicine.Open()
    
    RoleAttribute_Page_Attribute_Detail_Medicine.ClickToTopItem()
    this.m_Page_GameObject:SetActive(true)

    ---關閉裝備介面
    UIMgr.Close(Equipment_Controller)
end

---關閉屬性永丹分頁
function RoleAttribute_Page_Attribute_Detail_Medicine.Close()
    this.m_Page_GameObject:SetActive(false)
    RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanGroupPanel( false)
end


--- 使用永丹的資訊有變化時 需要更新介面資訊
---@param iEStateObserver EStateObserver 登記的觀察者類型
function RoleAttribute_Page_Attribute_Detail_Medicine:OnStateChanged(iEStateObserver)

    if iEStateObserver == EStateObserver.UpdateYuDan then
        ScrollView.Update(this.m_ScrollView)
        ScrollView.Update(this.m_ScrollView_YuDanEffect)
    end

    ---更新背包時 可能造成永丹是否在背包有對應物品的狀態更改 所以必須刷新
    if iEStateObserver == EStateObserver.BagDataRefresh then
        ScrollView.Update(this.m_ScrollView)
    end
end


---防止無資料數字進到顯示列表造成卡住 1.永丹表不開放 2.永丹串表有資料但物品表沒有資料時 則這筆資料不在介面顯示
function RoleAttribute_Page_Attribute_Detail_Medicine:SetAvailableList()

    ---可以在 屬性>永丹列表 被顯示的永丹流水號
    this.m_AvailableYuDanTable = {}
    local _tableIndex = 1

    for i = 1, TakemedicineData.m_DataCount do
        
        ---來自永丹表的資料
        local _YuDanData = TakemedicineData.GetTakemedicineDataByIdx(i)
        
        ---來自物品表的資料
        local _YuDanDataFromItem

        ---永丹資料取到空值 或者 對應的物品ID = 0 則不從物品表取資料
        if _YuDanData ~= nil  and _YuDanData.m_ItemID ~= 0 then
            _YuDanDataFromItem = ItemData.GetItemDataByIdx(_YuDanData.m_ItemID)
        end
        
        ---兩種資料都有取得時 才會被加到可顯示名單
        if _YuDanData ~=nil and _YuDanDataFromItem ~= nil then
            this.m_AvailableYuDanTable[_tableIndex] = i
            _tableIndex = _tableIndex +1
        end
    end
end

---開啟/關閉遠丹篩選介面
---@param iShowPanel bool 是否要顯示晶片篩選面板的介面
function RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanGroupPanel(iShowPanel)
    this.m_GObj_ChipGroupPanel:SetActive(iShowPanel)

    ---因為有可能點完篩選 不按確定套用篩選改按其他地方關閉篩選介面 所以每次開啟篩選頁面 要根據當前套用的篩選規則修改相關顯示
    if iShowPanel then
        GroupButton.OnPointerClickByIndex(this.m_GroupBtn_YuDanType, m_LastYuDanFilterChipType)
        GroupButton.OnPointerClickByIndex(this.m_GroupBtn_YuDanComplete, m_LastYuDanFilterChipComplete)
    else
        m_YuDanFilterChipType = m_LastYuDanFilterChipType
        m_YuDanFilterChipComplete = m_LastYuDanFilterChipComplete
    end
end

---篩選並紀錄目前要顯示的永丹
function RoleAttribute_Page_Attribute_Detail_Medicine.FilterCurrentYuDanTable()
    ---清空table
    m_CurrentShowTable = {}

    ---避免篩選後m_CurrentShowTable count數變太少 無法刷新/關閉 預先關閉所有重複使用物件
    for i = 1, this.m_ContentMedicineList.childCount do
        this.m_ContentMedicineList:GetChild(i-1).gameObject:SetActive(false)
    end

    ---建立第一次篩選table
    local _TempYudanTable = {}
    ---從有效的永丹表去處理
    for i = 1, table.Count(this.m_AvailableYuDanTable) do
        local _YuDanData = TakemedicineData.GetTakemedicineDataByIdx(this.m_AvailableYuDanTable[i])

        ---是否符合種類篩選需求
        local _PassChipTypeFilter = (m_YuDanFilterChipType == EChipFilterChipType.AllChip or _YuDanData.m_TypeGroup == (m_YuDanFilterChipType -1))
        ---是否已吃滿
        local  _IsCHipFull = (PlayerData_Attributes.GetYuDanUseCodition(_YuDanData.m_Idx) == (_YuDanData.m_UseLimit))
        ---是否符合完成度篩選需求
        local _PassChipCompleteFilter 

        if m_YuDanFilterChipComplete == EChipFilterChipComplete.AllChip then
            _PassChipCompleteFilter = true
        elseif m_YuDanFilterChipComplete == EChipFilterChipComplete.CompletedChip then
            _PassChipCompleteFilter = (_IsCHipFull)
        elseif m_YuDanFilterChipComplete == EChipFilterChipComplete.NotCompletedChip then
            _PassChipCompleteFilter = (not _IsCHipFull)
        end

        local _FilledYadanData = {}
        _FilledYadanData.m_IDx = this.m_AvailableYuDanTable[i]
        _FilledYadanData.m_TypeGroup = _YuDanData.m_TypeGroup
        _FilledYadanData.m_IsYuDanData = true

        if _PassChipTypeFilter and _PassChipCompleteFilter and _YuDanData.m_isNoRoleAttrShow ~= 1 then
            table.insert(_TempYudanTable,_FilledYadanData)
        end
    end

    ---有符合條件的才做後續處理
    if table.Count(_TempYudanTable) == 0 then
        ---沒有符合條件的 不做處理
    else
        ---加入介面顯示分別用的 攻擊 /防禦 / 其他  類別顯示資訊

        ---只要顯示單一類別
        if m_YuDanFilterChipType ~= EChipFilterChipType.AllChip then
       
            local _FilledYudanTypeData = {}
            _FilledYudanTypeData.m_IsYuDanData = false
            _FilledYudanTypeData.m_YudanTypeStringID = ChipFilterChipTypeStringIDTable[m_YuDanFilterChipType]

            table.insert(m_CurrentShowTable,_FilledYudanTypeData)

            for key ,value in pairs(_TempYudanTable) do
                table.insert(m_CurrentShowTable,value)
            end
    
        ---類別為全部
        else
            ---排序 小的往前移動
            local function Compare_YuDan(iValue_A, iValue_B)
                    if iValue_A == nil and iValue_B == nil then
                        return 0
                    elseif iValue_A == nil then
                        return true
                    elseif iValue_B == nil then
                        return false
                    elseif iValue_A == iValue_B then
                        return 0
                    else
                        return iValue_A > iValue_B
                    end
                end
            ---排序 依據 種類 ID 排序
            local function ItemSortFunction(iData_A, iData_B)
                -- 防呆
                if iData_A == nil or iData_B == nil then
                    return false
                end
        
                --- 排序順序規則
                local _fields = {"m_TypeGroup", "m_IDx",}

                -- 不是一樣的就回傳比對結果
                for _, field in ipairs(_fields) do
                    local comparisonResult = Compare_YuDan(iData_B[field], iData_A[field])
                    if comparisonResult ~= 0 then
                        return comparisonResult
                    end
                end
                return false
            end

            ---先排序
            table.sort(_TempYudanTable,ItemSortFunction) 

            ---各種類的最前面 要加顯示分別用的 攻擊 /防禦 / 其他  類別顯示資訊
            local _StartIndex = 1
            ---依照enum 順序 從攻擊開始將資料加入 
            for i = EChipFilterChipType.AttackChip , EChipFilterChipType.OtheChip do

                local _FilledYudanTypeData = {}
                _FilledYudanTypeData.m_IsYuDanData = false
                _FilledYudanTypeData.m_YudanTypeStringID = ChipFilterChipTypeStringIDTable[i]
                table.insert(m_CurrentShowTable,_FilledYudanTypeData)
            
                ---加入相同種類的資料
                for j = _StartIndex, table.Count(_TempYudanTable) do

                        if _TempYudanTable[j].m_TypeGroup == (i-1) then
                            table.insert(m_CurrentShowTable,_TempYudanTable[j])
                            _StartIndex = _StartIndex +1
                        else 
                            break
                        end
                end
            end
        end
    end    
    
    if this.m_ScrollView ~= nil then
        ScrollView.UpdateToFirst(this.m_ScrollView)
    end

end

---開啟/關閉 永丹所有加乘效果頁面
---@param iShowPanel bool 是否開永丹的加乘效果頁面
function RoleAttribute_Page_Attribute_Detail_Medicine.ShowYudanEffectPage(iShowPanel)
    this.m_Gobj_YuDanEffectPage:SetActive(iShowPanel)
end




--- 永丹加乘效果 有幾個
function RoleAttribute_Page_Attribute_Detail_Medicine.ReturnCount_YuDanEffect()
    local _EffectTable = PlayerData_Attributes.GetSingleYuDanEffectTable()

    local _Count
    if _EffectTable == nil or  table.Count(_EffectTable) == 0 then
        _Count = 1
    else
        _Count = table.Count(_EffectTable)
    end

    return _Count
end

--永丹加乘效果 ScrollView 初始化必須配置
function RoleAttribute_Page_Attribute_Detail_Medicine.AfterReuseItemInit_YuDanEffect(iItem,iRowIdx)
    if iItem == nil then
        return
    end

    RoleAttribute_Page_Attribute_Detail_Medicine.SetYuDanEffectListRowItem(iItem, iRowIdx)
end

--ScrollView 初始化必須配置 更新Scroll 下面的項目
function RoleAttribute_Page_Attribute_Detail_Medicine.AfterReuseItemIndexUpdate_YuDanEffect(iItem, iRowIdx)
    if iItem == nil then
        return
    end

    RoleAttribute_Page_Attribute_Detail_Medicine.SetYuDanEffectListRowItem(iItem, iRowIdx)
    iItem.m_GObj:SetActive(iRowIdx <= this.ReturnCount_YuDanEffect())
end

---永丹效果ScrollView刷新訊息
function RoleAttribute_Page_Attribute_Detail_Medicine.SetYuDanEffectListRowItem(iItem, iRowIdx)

    local _YuDanEffect= {}
    local _YuDanEffectData =  PlayerData_Attributes.GetSingleYuDanEffect(iRowIdx)

    _YuDanEffect.gameObject = iItem.m_GObj
    --TMP稱號加成效果名稱
    _YuDanEffect.m_TMP_TitleName =  _YuDanEffect.gameObject.transform:Find( "TMP_EffectName").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
    ---TMP稱號加成數值
    _YuDanEffect.m_TMP_EffectValue =  _YuDanEffect.gameObject.transform:Find( "TMP_EffectValue").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

    ---特殊狀況 如果都沒有加成 只顯示一個無
    if _YuDanEffectData == nil  then
        _YuDanEffect.m_TMP_TitleName.text = TextData.Get(510111)
        _YuDanEffect.m_TMP_EffectValue.text = ""
        return
    end

    local _AttrString, _ValueString 
    ---依據是否為 戰鬥類加成效果 取得顯示訊息
    if _YuDanEffectData.m_IsBattleEffect == true then
         _AttrString, _ValueString = GValue.AttributeToString(_YuDanEffectData.m_SingleEnhanceType , _YuDanEffectData.m_TotalValue,false)
    else
        _AttrString = TextData.Get(_YuDanEffectData.m_NonBattleEffectID)
        _ValueString = "+"..tostring(_YuDanEffectData.m_TotalValue)
    end

        _YuDanEffect.m_TMP_TitleName.text = _AttrString
        _YuDanEffect.m_TMP_EffectValue.text = _ValueString
end


---移動到細節屬性介面的最上方
function RoleAttribute_Page_Attribute_Detail_Medicine.ClickToTopItem_YuDanEffect()
    ScrollView.UpdateToFirst(this.m_ScrollView_YuDanEffect)
end
