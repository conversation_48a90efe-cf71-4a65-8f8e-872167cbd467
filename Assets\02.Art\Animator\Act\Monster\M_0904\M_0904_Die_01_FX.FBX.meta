fileFormatVersion: 2
guid: 073ee0efae96ba64d8f6f7be5ca1ead9
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: M_905_Part_1
    100004: M_905_Part_2
    100006: M_905_Part_3
    100008: Object002_Part_1
    100010: Object002_Part_2
    100012: Object002_Part_3
    100014: Object002_Part_4
    100016: Object002_Part_5
    100018: Object004_Part_1
    100020: Object004_Part_2
    100022: Object004_Part_3
    100024: Object006_Part_1
    100026: Object006_Part_2
    100028: Object008_Part_1
    100030: Object008_Part_2
    100032: Object010_Part_1
    100034: Object010_Part_2
    100036: Object010_Part_3
    100038: Object012_Part_1
    100040: Object012_Part_2
    100042: Object012_Part_3
    100044: Object014_Part_1
    100046: Object014_Part_2
    100048: Object016_Part_1
    100050: Object016_Part_2
    100052: Object016_Part_3
    100054: Object016_Part_4
    100056: Object016_Part_5_Part_1
    100058: Object016_Part_5_Part_2
    100060: Object016_Part_5_Part_3
    100062: Object018_Part_1
    100064: Object018_Part_2
    100066: Object_Part_1
    100068: Object_Part_2
    100070: Object_Part_3
    100072: Object_Part_4
    100074: Die_FX
    100076: Bip001
    100078: Die_FX_Obj
    100080: DM_Die_FX_01
    100082: DM_Die_FX_02
    100084: DM_Die_FX_03
    100086: DM_Die_FX_04
    100088: DM_Die_FX_05
    100090: DM_Die_FX_06
    100092: DM_Die_FX_07
    100094: DM_Die_FX_08
    100096: DM_Die_FX_09
    100098: DM_Die_FX_10
    100100: DM_Die_FX_11
    100102: DM_Die_FX_12
    100104: DM_Die_FX_13
    100106: DM_Die_FX_14
    100108: DM_Die_FX_15
    100110: DM_Die_FX_16
    100112: DM_Die_FX_17
    100114: DM_Die_FX_18
    100116: DM_Die_FX_19
    100118: DM_Die_FX_20
    100120: DM_Die_FX_21
    100122: DM_Die_FX_22
    100124: DM_Die_FX_23
    100126: DM_Die_FX_24
    100128: DM_Die_FX_25
    100130: DM_Die_FX_26
    100132: DM_Die_FX_27
    100134: DM_Die_FX_28
    100136: DM_Die_FX_29
    100138: DM_Die_FX_30
    100140: DM_Die_FX_31
    100142: DM_Die_FX_32
    100144: Die_FX_Bone
    400000: //RootNode
    400002: M_905_Part_1
    400004: M_905_Part_2
    400006: M_905_Part_3
    400008: Object002_Part_1
    400010: Object002_Part_2
    400012: Object002_Part_3
    400014: Object002_Part_4
    400016: Object002_Part_5
    400018: Object004_Part_1
    400020: Object004_Part_2
    400022: Object004_Part_3
    400024: Object006_Part_1
    400026: Object006_Part_2
    400028: Object008_Part_1
    400030: Object008_Part_2
    400032: Object010_Part_1
    400034: Object010_Part_2
    400036: Object010_Part_3
    400038: Object012_Part_1
    400040: Object012_Part_2
    400042: Object012_Part_3
    400044: Object014_Part_1
    400046: Object014_Part_2
    400048: Object016_Part_1
    400050: Object016_Part_2
    400052: Object016_Part_3
    400054: Object016_Part_4
    400056: Object016_Part_5_Part_1
    400058: Object016_Part_5_Part_2
    400060: Object016_Part_5_Part_3
    400062: Object018_Part_1
    400064: Object018_Part_2
    400066: Object_Part_1
    400068: Object_Part_2
    400070: Object_Part_3
    400072: Object_Part_4
    400074: Die_FX
    400076: Bip001
    400078: Die_FX_Obj
    400080: DM_Die_FX_01
    400082: DM_Die_FX_02
    400084: DM_Die_FX_03
    400086: DM_Die_FX_04
    400088: DM_Die_FX_05
    400090: DM_Die_FX_06
    400092: DM_Die_FX_07
    400094: DM_Die_FX_08
    400096: DM_Die_FX_09
    400098: DM_Die_FX_10
    400100: DM_Die_FX_11
    400102: DM_Die_FX_12
    400104: DM_Die_FX_13
    400106: DM_Die_FX_14
    400108: DM_Die_FX_15
    400110: DM_Die_FX_16
    400112: DM_Die_FX_17
    400114: DM_Die_FX_18
    400116: DM_Die_FX_19
    400118: DM_Die_FX_20
    400120: DM_Die_FX_21
    400122: DM_Die_FX_22
    400124: DM_Die_FX_23
    400126: DM_Die_FX_24
    400128: DM_Die_FX_25
    400130: DM_Die_FX_26
    400132: DM_Die_FX_27
    400134: DM_Die_FX_28
    400136: DM_Die_FX_29
    400138: DM_Die_FX_30
    400140: DM_Die_FX_31
    400142: DM_Die_FX_32
    400144: Die_FX_Bone
    2100000: M_0904
    2300000: M_905_Part_1
    2300002: M_905_Part_2
    2300004: M_905_Part_3
    2300006: Object002_Part_1
    2300008: Object002_Part_2
    2300010: Object002_Part_3
    2300012: Object002_Part_4
    2300014: Object002_Part_5
    2300016: Object004_Part_1
    2300018: Object004_Part_2
    2300020: Object004_Part_3
    2300022: Object006_Part_1
    2300024: Object006_Part_2
    2300026: Object008_Part_1
    2300028: Object008_Part_2
    2300030: Object010_Part_1
    2300032: Object010_Part_2
    2300034: Object010_Part_3
    2300036: Object012_Part_1
    2300038: Object012_Part_2
    2300040: Object012_Part_3
    2300042: Object014_Part_1
    2300044: Object014_Part_2
    2300046: Object016_Part_1
    2300048: Object016_Part_2
    2300050: Object016_Part_3
    2300052: Object016_Part_4
    2300054: Object016_Part_5_Part_1
    2300056: Object016_Part_5_Part_2
    2300058: Object016_Part_5_Part_3
    2300060: Object018_Part_1
    2300062: Object018_Part_2
    2300064: Object_Part_1
    2300066: Object_Part_2
    2300068: Object_Part_3
    2300070: Object_Part_4
    3300000: M_905_Part_1
    3300002: M_905_Part_2
    3300004: M_905_Part_3
    3300006: Object002_Part_1
    3300008: Object002_Part_2
    3300010: Object002_Part_3
    3300012: Object002_Part_4
    3300014: Object002_Part_5
    3300016: Object004_Part_1
    3300018: Object004_Part_2
    3300020: Object004_Part_3
    3300022: Object006_Part_1
    3300024: Object006_Part_2
    3300026: Object008_Part_1
    3300028: Object008_Part_2
    3300030: Object010_Part_1
    3300032: Object010_Part_2
    3300034: Object010_Part_3
    3300036: Object012_Part_1
    3300038: Object012_Part_2
    3300040: Object012_Part_3
    3300042: Object014_Part_1
    3300044: Object014_Part_2
    3300046: Object016_Part_1
    3300048: Object016_Part_2
    3300050: Object016_Part_3
    3300052: Object016_Part_4
    3300054: Object016_Part_5_Part_1
    3300056: Object016_Part_5_Part_2
    3300058: Object016_Part_5_Part_3
    3300060: Object018_Part_1
    3300062: Object018_Part_2
    3300064: Object_Part_1
    3300066: Object_Part_2
    3300068: Object_Part_3
    3300070: Object_Part_4
    4300000: Object010_Part_1
    4300002: Object010_Part_2
    4300004: Object010_Part_3
    4300006: Object016_Part_5_Part_1
    4300008: Object016_Part_5_Part_2
    4300010: Object016_Part_5_Part_3
    4300012: Object004_Part_1
    4300014: Object004_Part_2
    4300016: Object004_Part_3
    4300018: Object008_Part_2
    4300020: Object008_Part_1
    4300022: Object006_Part_2
    4300024: Object006_Part_1
    4300026: M_905_Part_3
    4300028: M_905_Part_2
    4300030: M_905_Part_1
    4300032: Object018_Part_2
    4300034: Object018_Part_1
    4300036: Object016_Part_4
    4300038: Object016_Part_3
    4300040: Object016_Part_2
    4300042: Object016_Part_1
    4300044: Object014_Part_2
    4300046: Object014_Part_1
    4300048: Object002_Part_5
    4300050: Object002_Part_4
    4300052: Object002_Part_3
    4300054: Object002_Part_2
    4300056: Object002_Part_1
    4300058: Object_Part_4
    4300060: Object_Part_3
    4300062: Object_Part_2
    4300064: Object_Part_1
    4300066: Object012_Part_3
    4300068: Object012_Part_2
    4300070: Object012_Part_1
    4300072: Die_FX_Obj
    7400000: M_0904_Die_01_FX
    9500000: //RootNode
    13700000: Die_FX_Obj
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: M_0904
    second: {fileID: 2100000, guid: ac07feb2e12f91b4a88ef785ad133e01, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0904_Die_01_FX
      takeName: M_0904_Die_01
      firstFrame: 0
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 1
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: efb3f663637474f448c78c496b6911e1,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
