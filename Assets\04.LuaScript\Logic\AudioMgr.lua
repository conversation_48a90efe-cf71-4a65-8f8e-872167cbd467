---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Logic/SystemSettingData/AudioSetting")

---音效管理
---@class AudioMgr
---author 詩惠
---version 1.0
---since [ProjectBase] 0.1
---date 2023.1.17
AudioMgr = {}
local this = AudioMgr

---是否為音效測試
this.m_IS_AUDIO_TEST = false
---是否顯示音效除錯訊息
this.m_IS_AUDIOMGR_LOG = false

---@type table<number, AudioData> 正在使用的音效
local _Table_PlayingAudio = {}

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 音效物件池
local _GObjPool_Audio = {}

local _MixerName = "MasterMixer"
--- 播放中背景音效 (Hash)
local _PlayingBGM = 0
--- 淡入中的背景音效 (Hash)
local _FadingBGM = 0

--- 是否 淡入 / 淡出 Master
local _IsFadeMaster = ""
--- 暫存音量用
local _MasterFadeVol = 0

--- 是否完成初始化
local _FinishInit = false

---@class EMixerGroup 混音器種類
AudioMgr.EMixerGroup = {
    Master = 0,
    -- 請看 Proposal\00_GDD\17_音樂音效
    --- 語音
    VOCAL = 1,
    --- 背景音樂 ( 群組 )
    BGM = 2,
    --- 一般背景
    BGM_NRM = 3,
    --- 戰鬥背景 ( PVP )
    BGM_ATK = 4,
    --- 音效 ( 群組 )
    SFX = 5,
    --- 王攻擊
    BOSS_ATK = 6,
    --- 王招式
    BOSS_SKL = 7,
    --- 非玩家攻擊
    NPC_ATK = 8,
    --- 非玩家招式
    NPC_SKL = 9,
    --- 玩家攻擊
    PC_ATK = 10,
    --- 玩家招式
    PC_SKL = 11,
    --- 機關
    GEAR = 12,
    --- 環境
    ENV = 13,
    --- 介面
    UI = 14,
    --- 環境音效群組 ( 包含機關跟環境音 )
    ENV_GROUP = 15
}

---@class EAudioType 音效播放種類對應名稱
AudioMgr.EAudioType = {
    ---背景音效
    BGM = "BGM_{0}",
    ---聲音音效
    Sound = "Sound_{0}",
    ---RTM人聲音效
    Voice = "Voice_{0}",
}

--- 取得音效名稱
function AudioMgr.GetAudioName(iAudioType, iAudioId)
    return GString.Format(iAudioType, GValue.Zero_stuffing(iAudioId, 5))
end

function AudioMgr.ShowLog(iStr, iColorStr, iIsError)
    if this.m_IS_AUDIOMGR_LOG and (iIsError ~= nil or not iIsError) then
        iColorStr = string.IsNullOrEmpty(iColorStr) and "yellow" or iColorStr
        D.Log(GString.Format("<color={0}>[AudioMgr]{1}</color>", iColorStr, iStr))
    end

    if iIsError then
        D.LogError(GString.Format("<color={0}>[AudioMgr]{1}</color>", iColorStr, iStr))
    end
end

local function ResetAudio(iGObj)
    this.ShowLog("ResetAudio")
    iGObj:SetActive(false)
end

local function OneTimeInitAudio(iGObj)
    this.ShowLog("OneTimeInitAudio")
    iGObj:AddComponent(typeof(AudioSource))
    iGObj:SetActive(false)
end

local function ResetPlayingAudio(iHashCode, iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio, iIsFollow, iLoop, iName, iIsActive, iSpeed)
    if ProjectMgr.IsEditor() then
        _Table_PlayingAudio[iHashCode].AudioObj.name = iName
    end

    _Table_PlayingAudio[iHashCode].AudioType = iAudioType
    _Table_PlayingAudio[iHashCode].iAudioId = iAudioId
    _Table_PlayingAudio[iHashCode].MixerGroup = iMixerGroup
    _Table_PlayingAudio[iHashCode].Time = Time.deltaTime
    _Table_PlayingAudio[iHashCode].IsImportantAudio = iIsImportantAudio
    _Table_PlayingAudio[iHashCode].AudioSource = _Table_PlayingAudio[iHashCode].AudioObj:GetComponent(typeof(AudioSource))
    _Table_PlayingAudio[iHashCode].AudioSource.outputAudioMixerGroup = this.m_MasterAudioMixerGroupAy[tostring(table.GetKey(AudioMgr.EMixerGroup, iMixerGroup))]
    _Table_PlayingAudio[iHashCode].AudioSource.volume = 1

    _Table_PlayingAudio[iHashCode].AudioSource.minDistance = AudioSetting.m_DefalutAudioSourceSetting.MinDistance
    _Table_PlayingAudio[iHashCode].AudioSource.maxDistance = AudioSetting.m_DefalutAudioSourceSetting.MaxDistance
    _Table_PlayingAudio[iHashCode].AudioSource.spread = AudioSetting.m_DefalutAudioSourceSetting.Spread
    _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = AudioSetting.m_DefalutAudioSourceSetting.SpatialBlend
    _Table_PlayingAudio[iHashCode].AudioSource.rolloffMode = AudioSetting.m_DefalutAudioSourceSetting.RolloffMode

    if not iIsFollow then
        if iAudioType == AudioMgr.EAudioType.BGM then
            _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(this.m_BGMRoot)
        elseif iAudioType == AudioMgr.EAudioType.Sound  then
            _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(this.m_SoundRoot)
        elseif iAudioType == AudioMgr.EAudioType.Voice then
            _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(this.m_VoiceRoot)
        end
        _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = iParent.localPosition
    else
        _Table_PlayingAudio[iHashCode].AudioObj.transform:SetParent(iParent)
        _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = Vector3.zero
    end

    if iAudioType == AudioMgr.EAudioType.BGM then
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 0
    elseif iAudioType == AudioMgr.EAudioType.Sound then
        -- 除了 UI 音效以外其他都要 3D 音效
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = iMixerGroup == AudioMgr.EMixerGroup.UI and 0 or 1
        -- _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 0
    elseif iAudioType == AudioMgr.EAudioType.Voice then
        _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = 0
    end

    _Table_PlayingAudio[iHashCode].AudioSource.loop = iLoop

    --- 聲音播放速度
    _Table_PlayingAudio[iHashCode].AudioSource.pitch = iSpeed and iSpeed or 1

    local _AudioName = this.GetAudioName(iAudioType, iAudioId)

    if iAudioId ~= 0 then
        local _DebugTraceback = debug.traceback()
        ResourceMgr.Load(_AudioName, function(iObj)
            if not Extension.IsUnityObjectNull(iObj) and _Table_PlayingAudio[iHashCode] then
                _Table_PlayingAudio[iHashCode].AudioSource.clip = iObj
                _Table_PlayingAudio[iHashCode].AudioObj:SetActive(iIsActive)
                -- Audio 被啟動後將可被回收的旗標開起來
                _Table_PlayingAudio[iHashCode].m_CanBeClear = true
                this.ShowLog("Audio Source is Active :" .. iHashCode)
            else
                D.LogError("找不到音效: " .. _AudioName .. "\n" .. _DebugTraceback)
            end
        end)
    end
end

local function CalculateVolume(iHashCode, iVol)
    local _SettingVol = 1
    if _Table_PlayingAudio[iHashCode].AudioType == AudioMgr.EAudioType.BGM then
        _SettingVol = SettingMgr.m_AudioVolume.BGM
    elseif _Table_PlayingAudio[iHashCode].AudioType == AudioMgr.EAudioType.Sound then
        _SettingVol = SettingMgr.m_AudioVolume.Sound
    elseif _Table_PlayingAudio[iHashCode].AudioType == AudioMgr.EAudioType.Voice then
        _SettingVol = SettingMgr.m_AudioVolume.Voice
    end

    -- this.ShowLog("iVol: "..iVol)
    -- this.ShowLog("_SettingVol: ".._SettingVol)
    local tempVol = iVol * _SettingVol
    -- this.ShowLog("tempVol: "..tempVol)
    return tempVol
end

---初始化
function AudioMgr.Init( iCallback )
    this.m_AudioMgr = GameObject.New("AudioMgr").transform
    -- this.m_AudioMgr:SetParent(GameObject.Find("MainCamera").transform)

    this.m_BGMRoot = GameObject.New("BGMRoot").transform
    this.m_SoundRoot = GameObject.New("SoundRoot").transform
    this.m_VoiceRoot = GameObject.New("VoiceRoot").transform
    this.m_BGMRoot:SetParent(this.m_AudioMgr)
    this.m_SoundRoot:SetParent(this.m_AudioMgr)
    this.m_VoiceRoot:SetParent(this.m_AudioMgr)

    _GObjPool_Audio =
        Extension.GetGameObjPool(
        AudioSetting.m_MaxAudioPoolCount[table.GetKey(EDeviceLevel, SettingMgr.m_DeviceLevel)],
        0,
        ResetAudio,
        OneTimeInitAudio
    )

    ResourceMgr.Load(
        _MixerName,
        function(iObj)
            if not Extension.IsUnityObjectNull(iObj) then
                this.m_AudioMixerObj = iObj
                local _MixerGroup = iObj:FindMatchingGroups("Master");
                this.m_MasterAudioMixerGroupAy = {}
                for i = 0, _MixerGroup.Length - 1 do
                    this.m_MasterAudioMixerGroupAy[_MixerGroup[i].name] = _MixerGroup[i]
                end

                _FinishInit = true

                this.PlaySpecialBGM(AudioMgr.EMixerGroup.BGM_NRM, AudioSetting.m_PlayingBGMKind.DownLoadBGM, true)

                for key, value in pairs(SettingMgr.m_AudioSwitch) do
                    this.SetMixerGrouVolume(AudioMgr.EMixerGroup[key], value == true and SettingMgr.m_AudioVolume[key] or 0)
                end
            end

            if iCallback then
                iCallback()
            end
        end,
        false
    )
end

--- 更新計時
local m_AudioUpdateTimeCount = 0
--- 特效刷新函式
function AudioMgr.Update()
    m_AudioUpdateTimeCount = m_AudioUpdateTimeCount + Time.deltaTime
    if m_AudioUpdateTimeCount >= AudioSetting.m_RefreshAudioPoolSecond then
        m_AudioUpdateTimeCount = 0
        for key, value in pairs(_Table_PlayingAudio) do
            -- 地塊特效不檢查回收
            if Extension.IsUnityObjectNull(value.AudioSource)
               or Extension.IsUnityObjectNull(value.AudioObj) then
                _Table_PlayingAudio[key] = nil
            elseif not value.AudioSource or (not value.AudioSource.loop and not value.AudioSource.isPlaying) then
                -- 避免出現音效還沒撥放就被回收的情況
                if value.m_CanBeClear ~= nil and value.m_CanBeClear == true then
                    this.ShowLog("回收: " .. value.AudioObj.name)
                    this.ReturnAudio(key)
                end
            end
        end
    end

    if not string.IsNullOrEmpty(_IsFadeMaster) then

        local _Vol = SettingMgr.m_AudioVolume.Master

        -- 設定_MasterFadeVol 始值
        if _MasterFadeVol == nil then
            _MasterFadeVol = _Vol
        end

        if _IsFadeMaster == "FadeOut" then
            _MasterFadeVol = Mathf.Lerp(_MasterFadeVol, 0, HEMTimeMgr.m_DeltaTime * AudioSetting.m_FadeOutSpeed)
            if _MasterFadeVol <= 0.01 then  -- 避免無限接近
                _MasterFadeVol = 0
                _IsFadeMaster = ""
            end
            AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, _MasterFadeVol)

        elseif _IsFadeMaster == "FadeIn" then
            _MasterFadeVol = Mathf.Lerp(_MasterFadeVol, _Vol, HEMTimeMgr.m_DeltaTime * AudioSetting.m_FadeInSpeed)
            if math.abs(_MasterFadeVol - _Vol) < 0.01 then  -- 避免無限接近
                _MasterFadeVol = _Vol
                _IsFadeMaster = ""
            end
            AudioMgr.SetMixerGrouVolume(AudioMgr.EMixerGroup.Master, _MasterFadeVol)
        end
    end

    -- 淡入中的音效不是 0 -> 正在切換音效中 (淡入音效中)
    if _FadingBGM ~= 0 then
        --- 有播放中的音效 -> 需要淡出
        if _PlayingBGM ~= 0 then
            local tempVolume = _Table_PlayingAudio[_PlayingBGM].AudioSource.volume - (AudioSetting.m_FadeOutSpeed * Time.deltaTime)
            if tempVolume > 0 then
                this.SetAudioVolume(_PlayingBGM, tempVolume)
            else
                this.SetAudioVolume(_PlayingBGM, 0)
                this.ReturnAudio(_PlayingBGM)
                _PlayingBGM = 0
            end
        end

        --- 淡入音效中
        if _PlayingBGM == 0 or AudioSetting.m_FadeInStartVolume > _Table_PlayingAudio[_PlayingBGM].AudioSource.volume then
            local tempBGMVolume = SettingMgr.m_AudioVolume.BGM
            local tempVolume
            if _Table_PlayingAudio[_FadingBGM] then
                tempVolume = _Table_PlayingAudio[_FadingBGM].AudioSource.volume + (AudioSetting.m_FadeInSpeed * Time.deltaTime)
            else
                tempVolume = 0
            end
            if tempVolume < tempBGMVolume then
                this.SetAudioVolume(_FadingBGM, tempVolume)
            else
                this.SetAudioVolume(_FadingBGM, tempBGMVolume)
                if _PlayingBGM == 0 then
                    _PlayingBGM = _FadingBGM
                    _FadingBGM = 0
                    _Table_PlayingAudio[_PlayingBGM].AudioObj.name = this.GetAudioName(_Table_PlayingAudio[_PlayingBGM].AudioType,
                        _Table_PlayingAudio[_PlayingBGM].iAudioId) .."_".._PlayingBGM.."_PlayingBGM"
                end
            end
        end
    end
end

---檢查可否播放音效
---只有 Sound 音效類型才會拿來判斷刪除
local function CheckCanPlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio)
    local _Result = true
    if not _FinishInit then
        this.ShowLog("音效系統尚未初始化完成: " .. iAudioId, "gray", true)
        _Result = false
    end

    if iAudioId == 0 then
        this.ShowLog("音效編號錯誤: " .. iAudioId, "gray", true)
        _Result = false
    end

    if iParent == nil then
        this.ShowLog("父物件 == nil: " .. iAudioId, "gray", true)
        _Result = false
    end

    if iAudioType == AudioMgr.EAudioType.BGM and SettingMgr.m_BGMVolume == 0 then
        _Result = false
    elseif iAudioType == AudioMgr.EAudioType.Sound and SettingMgr.m_SoundVolume == 0 then
        _Result = false
    elseif iAudioType == AudioMgr.EAudioType.Voice and SettingMgr.m_VoiceVolume == 0 then
        _Result = false
    end

    -- 可播放音效是否滿了
    if _Result and table.Count(_Table_PlayingAudio) >= AudioSetting.m_MaxAudioPlayingCount[table.GetKey(EDeviceLevel, SettingMgr.m_DeviceLevel)] then
        local _MinTime = Time.time;
        local _MinTimeKey = 0;
        local _ImprotantMinTime = Time.time;
        local _ImprotantMinTimeKey = 0;
        for key, value in pairs(_Table_PlayingAudio) do
            if value.AudioType == this.EAudioType.Sound and value.Time < _MinTime then
                if value.IsImportantAudio then
                    _ImprotantMinTime = value.Time
                    _ImprotantMinTimeKey = key
                else
                    _MinTime = value.Time
                    _MinTimeKey = key
                end
            end
        end

        -- 有沒有不重要效可以刪
        if _MinTimeKey == 0 then
            -- 要播的效是重要效
            if iIsImportantAudio then
                -- 找不到最舊的重要效 -> 不播了
                if _ImprotantMinTimeKey == 0 then
                    _Result = false
                    this.ShowLog("音效池滿了，且找不到效可以移除 (效系統有問題RRR，請洽相關人員)", "gray", true)
                -- 移除最舊的重要音效
                else
                    this.ReturnAudio(_ImprotantMinTimeKey)
                end
            -- 不是重要效 -> 不播了
            else
                _Result = false
            end
        else
            this.ReturnAudio(_MinTimeKey)
        end
    end

    return _Result
end

---播放任何音效都會進到這
---@param iAudioType EAudioType 音效種類
---@param iMixerGroup EMixerGroup MixerGroup
---@param iAudioId number uint 音效編號
---@param iParent Transform 座標
---@param iIsFollow boolean 要不要跟隨父物件
---@param IsLoop boolean 要不要循環播放
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
---@param iSpeed number 播放速度
local function PlayingAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, IsLoop, iIsImportantAudio, iLoadCompleteDelegate, iSpeed)
    if CheckCanPlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio) then
        local _TempObj = _GObjPool_Audio:Get()
        local _HashCode = _TempObj:GetHashCode()
        this.ShowLog("Playing: " .. _HashCode)
        _Table_PlayingAudio[_HashCode] = {}
        _Table_PlayingAudio[_HashCode].AudioObj = _TempObj
        ResetPlayingAudio(_HashCode, iAudioType, iMixerGroup, iAudioId, iParent, iIsImportantAudio,
            iIsFollow, IsLoop, this.GetAudioName(iAudioType, iAudioId) .."_".._HashCode, true, iSpeed)

        if iLoadCompleteDelegate then
            iLoadCompleteDelegate(_HashCode)

        end
    end
end

---還回音效
---@param iAudioType EAudioType 音效播放種類 iAudioType
---@param iHashCode number int 音效 HashCode
function AudioMgr.ReturnAudio(iHashCode)
    if not _Table_PlayingAudio[iHashCode] then
        do
            return
        end
    end

    this.ShowLog("ReturnAudio Hash: "..iHashCode, "orange")
    -- 歸還時 Id 給 0
    local _DefaultId = 0
    ResetPlayingAudio(iHashCode, _Table_PlayingAudio[iHashCode].AudioType, _Table_PlayingAudio[iHashCode].MixerGroup, _DefaultId,
    _Table_PlayingAudio[iHashCode].AudioObj.transform.parent, false, false, false,
            this.GetAudioName(_Table_PlayingAudio[iHashCode].AudioType, _DefaultId) .."_"..iHashCode, false)
    _GObjPool_Audio:Store(_Table_PlayingAudio[iHashCode].AudioObj)
    _Table_PlayingAudio[iHashCode] = nil
end

---還回場景環境音效
function AudioMgr.ReturnSceneEnvironmentAudio()
    if this.m_SceneEnvironmentAudio then
        local _tempCount = table.Count(this.m_SceneEnvironmentAudio)
        for i = 0, _tempCount do
            this.ReturnAudio(this.m_SceneEnvironmentAudio[i])
            this.m_SceneEnvironmentAudio[i] = nil
        end
    end
end

---調整音效聲音大小
---@param iHashCode int 音效 HashCode
---@param iVol float 聲音大小 ( 0~1 )
function AudioMgr.SetAudioVolume(iHashCode, iVol)
    if _Table_PlayingAudio[iHashCode] then
        _Table_PlayingAudio[iHashCode].AudioSource.volume =  iVol
    else
        this.ShowLog("SetAudioVolume 找不到音效: "..iHashCode)
    end
end

---調整混音器聲音大小
---可控制 AudioMgr.MixerGroup.Master / BGM / SFX / UI / VOCAL / ENV_GROUP
---@param iMixerGroup AudioMgr.MixerGroup 混音器種類
---@param iVol float 聲音大小 ( 0~1 )
function AudioMgr.SetMixerGrouVolume(iMixerGroup, iVol)
    local _MixerKey = tostring(table.GetKey(AudioMgr.EMixerGroup, iMixerGroup))
    -- 混音器是從 -80db ~ 20db
    -- D.Log("SetMixerGrouVolume_iMixerGroup : ".. iMixerGroup .. " - _MixerKey : ".. _MixerKey)
    local _Volume = iVol * (math.abs(SettingMgr.m_AudioMaxVolume[_MixerKey].Max - SettingMgr.m_AudioMaxVolume[_MixerKey].Min)) + SettingMgr.m_AudioMaxVolume[_MixerKey].Min
    --- 靜音按鈕控制
    if SettingMgr.m_AudioSwitch[_MixerKey] == true then
        this.m_MasterAudioMixerGroupAy[_MixerKey].audioMixer:SetFloat(_MixerKey, iVol > 0 and _Volume or SettingMgr.AudioMixerDB_Lowest)
        -- D.Log( "MixerKey : ".._MixerKey .."   - ".. tostring( this.m_MasterAudioMixerGroupAy[_MixerKey].audioMixer))
    else
        this.m_MasterAudioMixerGroupAy[_MixerKey].audioMixer:SetFloat(_MixerKey, SettingMgr.AudioMixerDB_Lowest)
    end
end

--- 播放 CG 處理
---@param iPlayCG boolean 是否播放 CG
function AudioMgr.PlayCG(iPlayCG)
    if iPlayCG then
        -- 播放 GC master 淡出
        _MasterFadeVol = SettingMgr.m_AudioVolume.Master
        _IsFadeMaster = "FadeOut"
    else
        -- 結束 GC master 淡入
        _MasterFadeVol = 0
        _IsFadeMaster = "FadeIn"
    end
end

---播放特殊背景音效
---@param iPlayingBGMKind AudioSetting.PlayingBGMKind 播放的特殊背景音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iIsFade boolean 要不要淡入淡出
function AudioMgr.PlaySpecialBGM(iPlayingBGMKind, iMixerGroup, iIsFade)
    this.PlayBGM(iPlayingBGMKind, iMixerGroup, iIsFade, true)
end

---播放背景音效 (預設為重要音效)
---@param iAudioId number 播放的背景音效編號
---@param iIsFade boolean 要不要淡入淡出
---@param IsLoop boolean 要不要循環播放
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlayBGM(iMixerGroup, iAudioId, iIsFade, IsLoop, iLoadCompleteDelegate)
    -- 重複播放中的背景音效 ID 且，沒有在但入淡出中的，不用淡入淡出
    -- TODO 還沒測過
    if _PlayingBGM and _Table_PlayingAudio[_PlayingBGM] and _Table_PlayingAudio[_PlayingBGM].iAudioId == iAudioId and _FadingBGM == 0 then
        if iLoadCompleteDelegate then
            iLoadCompleteDelegate()
        end
        do return end
    end

    PlayingAudio(AudioMgr.EAudioType.BGM, iMixerGroup, iAudioId, this.m_BGMRoot, false, IsLoop, true,
        function(iHashCode)
            if iIsFade then
                if _FadingBGM ~= 0 then
                    local tempHash = _PlayingBGM
                    _PlayingBGM = _FadingBGM
                    if tempHash ~= 0 then
                        this.SetAudioVolume(tempHash, 0)
                        this.ReturnAudio(tempHash)
                    end
                end
                _FadingBGM = iHashCode
                this.SetAudioVolume(_FadingBGM, 0)
            else
                local tempHash = _FadingBGM
                _PlayingBGM = iHashCode
                _FadingBGM = 0
                if tempHash ~= 0 then
                    this.SetAudioVolume(tempHash, 0)
                    this.ReturnAudio(tempHash)
                end
                this.SetAudioVolume(_PlayingBGM, 1)
            end

            if iLoadCompleteDelegate then
                iLoadCompleteDelegate()
            end
    end)
end

---播放一般音效
---@param iAudioType AudioMgr.AudioType 音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iAudioId uint 音效編號
---@param iParent Transform 父物件
---@param iIsFollow boolean 要不要跟隨
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack ( 會回傳 Hash，需要控制可取用 )
function AudioMgr.PlayAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, iIsImportantAudio, iLoadCompleteDelegate, iSpeed)
    PlayingAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, false, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end, iSpeed)
end

---播放特殊NPC 自帶長期音效
---@param iAudioType AudioMgr.AudioType 音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iAudioId uint 音效編號
---@param iParent Transform 父物件
---@param iIsFollow boolean 要不要跟隨
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack ( 會回傳 Hash，需要控制可取用 )
function AudioMgr.PlayAudio_LongTermNPC(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, iIsImportantAudio, iLoadCompleteDelegate, iSpeed)
    PlayingAudio(iAudioType, iMixerGroup, iAudioId, iParent, iIsFollow, true, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end, iSpeed)
end

---播放 UI 音效
---@param iAudioId uint 音效編號
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlayUIAudio(iAudioId, iIsImportantAudio, iLoadCompleteDelegate)
    PlayingAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.UI, iAudioId, this.m_SoundRoot, false, false, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end)
end

---播放場景環境音效 (預設為重要音效)
---@param iAudioType AudioMgr.AudioType 音效種類
---@param iMixerGroup AudioMgr.MixerGroup MixerGroup
---@param iAudioId uint 音效編號
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlaySceneEnvironmentAudio(iEnvironmentSound)
    if not this.m_SceneEnvironmentAudio then
        this.m_SceneEnvironmentAudio = {}
    end

    PlayingAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.ENV, tonumber(iEnvironmentSound.SoundName), this.m_SoundRoot, false, iEnvironmentSound.Loop, true,
        function(iHashCode)
            table.insert(this.m_SceneEnvironmentAudio, iHashCode)
            _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = iEnvironmentSound.Pos
            _Table_PlayingAudio[iHashCode].AudioSource.minDistance = iEnvironmentSound.MinDis
            _Table_PlayingAudio[iHashCode].AudioSource.maxDistance = iEnvironmentSound.MaxDis
            _Table_PlayingAudio[iHashCode].AudioSource.spread = iEnvironmentSound.Spread
            _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = iEnvironmentSound.SpatialBlend
            _Table_PlayingAudio[iHashCode].AudioSource.rolloffMode = iEnvironmentSound.RolloffMode
            _Table_PlayingAudio[iHashCode].AudioSource.dopplerLevel = 0 --不會有都卜勒效應，音效將不會根據攝影機的移動速度改變音高或尖銳度
            _Table_PlayingAudio[iHashCode].AudioSource:SetCustomCurve(AudioSourceCurveType.CustomRolloff, iEnvironmentSound.AnimationCurve)
            this.SetAudioVolume(iHashCode, iEnvironmentSound.Volume)
    end)
end

---播放自定義音效
---@param iEnvironmentSound table C# CustomSound
---@param iParent Transform 父物件
function AudioMgr.PlayCustomEnvironmentAudio(iEnvironmentSound, iParent)
    if not this.m_SceneEnvironmentAudio then
        this.m_SceneEnvironmentAudio = {}
    end

    PlayingAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.ENV, tonumber(iEnvironmentSound.SoundName), iParent, iEnvironmentSound.m_isFollow, iEnvironmentSound.Loop, false,
            function(iHashCode)
                table.insert(this.m_SceneEnvironmentAudio, iHashCode)
                _Table_PlayingAudio[iHashCode].AudioObj.transform.localPosition = iEnvironmentSound.Pos
                _Table_PlayingAudio[iHashCode].AudioSource.minDistance = iEnvironmentSound.MinDis
                _Table_PlayingAudio[iHashCode].AudioSource.maxDistance = iEnvironmentSound.MaxDis
                _Table_PlayingAudio[iHashCode].AudioSource.spread = iEnvironmentSound.Spread
                _Table_PlayingAudio[iHashCode].AudioSource.spatialBlend = iEnvironmentSound.SpatialBlend
                _Table_PlayingAudio[iHashCode].AudioSource.rolloffMode = iEnvironmentSound.RolloffMode
                _Table_PlayingAudio[iHashCode].AudioSource.dopplerLevel = 0 --不會有都卜勒效應，音效將不會根據攝影機的移動速度改變音高或尖銳度
                _Table_PlayingAudio[iHashCode].AudioSource:SetCustomCurve(AudioSourceCurveType.CustomRolloff, iEnvironmentSound.AnimationCurve)
                this.SetAudioVolume(iHashCode, iEnvironmentSound.Volume)
            end)
end

---播放 UI 音效 人聲
---@param iAudioId uint 音效編號
---@param iIsImportantAudio boolean 是不是重要的音效
---@param iLoadCompleteDelegate function 載入完成 CallBack
function AudioMgr.PlayUIAudio_Voice(iAudioId, iIsImportantAudio, iLoadCompleteDelegate)
    PlayingAudio(AudioMgr.EAudioType.Voice, AudioMgr.EMixerGroup.VOCAL, iAudioId, this.m_SoundRoot, false, false, iIsImportantAudio, function(iHashCode)
            if iLoadCompleteDelegate then
                iLoadCompleteDelegate(iHashCode)
            end
    end)
end

function AudioMgr.OnUnrequire()
    if this.m_AudioMgr.gameObject ~=nil then
        this.m_AudioMgr.gameObject:Destroy()
    end
    return true
end
return AudioMgr
