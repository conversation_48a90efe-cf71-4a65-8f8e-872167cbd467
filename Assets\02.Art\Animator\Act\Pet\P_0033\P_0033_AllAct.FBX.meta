fileFormatVersion: 2
guid: 22ba245ba17ac0c4e801f7e43f30b72e
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bip001
  - first:
      1: 100002
    second: Bip001 Head
  - first:
      1: 100004
    second: Bip001 L Hand
  - first:
      1: 100006
    second: Bip001 R Hand
  - first:
      1: 100008
    second: Bone014
  - first:
      1: 100010
    second: Bone016
  - first:
      1: 100012
    second: Bone017
  - first:
      1: 100014
    second: Bone020
  - first:
      1: 100016
    second: Bone021
  - first:
      1: 100018
    second: Bone022
  - first:
      1: 100020
    second: Bone024
  - first:
      1: 100022
    second: Bone025
  - first:
      1: 100024
    second: Bone026
  - first:
      1: 100026
    second: Bone027
  - first:
      1: 100028
    second: Bone028
  - first:
      1: 100030
    second: Bone029
  - first:
      1: 100032
    second: Bone031
  - first:
      1: 100034
    second: Bone032
  - first:
      1: 100036
    second: Bone033
  - first:
      1: 100038
    second: Bone036
  - first:
      1: 100040
    second: Bone037
  - first:
      1: 100042
    second: Bone038
  - first:
      1: 100044
    second: Bone039
  - first:
      1: 100046
    second: Bone040
  - first:
      1: 100048
    second: Bone041
  - first:
      1: 100050
    second: Bone042
  - first:
      1: 100052
    second: Bone044
  - first:
      1: 100054
    second: Bone045
  - first:
      1: 100056
    second: Bone046
  - first:
      1: 100058
    second: Bone048
  - first:
      1: 100060
    second: Bone049
  - first:
      1: 100062
    second: Bone050
  - first:
      1: 100064
    second: Bone051
  - first:
      1: 100066
    second: Bone052
  - first:
      1: 100068
    second: Bone053
  - first:
      1: 100070
    second: Bone054
  - first:
      1: 100072
    second: Bone058
  - first:
      1: 100074
    second: Bone059
  - first:
      1: 100076
    second: Bone060
  - first:
      1: 100078
    second: Bone061
  - first:
      1: 100080
    second: head
  - first:
      1: 100082
    second: P_0033
  - first:
      1: 100084
    second: //RootNode
  - first:
      1: 100086
    second: Bone055
  - first:
      1: 100088
    second: Bone056
  - first:
      1: 100090
    second: Bone057
  - first:
      4: 400000
    second: Bip001
  - first:
      4: 400002
    second: Bip001 Head
  - first:
      4: 400004
    second: Bip001 L Hand
  - first:
      4: 400006
    second: Bip001 R Hand
  - first:
      4: 400008
    second: Bone014
  - first:
      4: 400010
    second: Bone016
  - first:
      4: 400012
    second: Bone017
  - first:
      4: 400014
    second: Bone020
  - first:
      4: 400016
    second: Bone021
  - first:
      4: 400018
    second: Bone022
  - first:
      4: 400020
    second: Bone024
  - first:
      4: 400022
    second: Bone025
  - first:
      4: 400024
    second: Bone026
  - first:
      4: 400026
    second: Bone027
  - first:
      4: 400028
    second: Bone028
  - first:
      4: 400030
    second: Bone029
  - first:
      4: 400032
    second: Bone031
  - first:
      4: 400034
    second: Bone032
  - first:
      4: 400036
    second: Bone033
  - first:
      4: 400038
    second: Bone036
  - first:
      4: 400040
    second: Bone037
  - first:
      4: 400042
    second: Bone038
  - first:
      4: 400044
    second: Bone039
  - first:
      4: 400046
    second: Bone040
  - first:
      4: 400048
    second: Bone041
  - first:
      4: 400050
    second: Bone042
  - first:
      4: 400052
    second: Bone044
  - first:
      4: 400054
    second: Bone045
  - first:
      4: 400056
    second: Bone046
  - first:
      4: 400058
    second: Bone048
  - first:
      4: 400060
    second: Bone049
  - first:
      4: 400062
    second: Bone050
  - first:
      4: 400064
    second: Bone051
  - first:
      4: 400066
    second: Bone052
  - first:
      4: 400068
    second: Bone053
  - first:
      4: 400070
    second: Bone054
  - first:
      4: 400072
    second: Bone058
  - first:
      4: 400074
    second: Bone059
  - first:
      4: 400076
    second: Bone060
  - first:
      4: 400078
    second: Bone061
  - first:
      4: 400080
    second: head
  - first:
      4: 400082
    second: P_0033
  - first:
      4: 400084
    second: //RootNode
  - first:
      4: 400086
    second: Bone055
  - first:
      4: 400088
    second: Bone056
  - first:
      4: 400090
    second: Bone057
  - first:
      21: 2100000
    second: P_0033
  - first:
      43: 4300000
    second: P_0033
  - first:
      74: 7400000
    second: idle
  - first:
      74: 7400002
    second: run
  - first:
      74: 7400004
    second: hit
  - first:
      74: 7400006
    second: weak
  - first:
      74: 7400008
    second: atk1
  - first:
      74: 7400010
    second: skl
  - first:
      74: 7400012
    second: win
  - first:
      74: 7400014
    second: ready
  - first:
      74: 7400016
    second: die
  - first:
      74: 7400018
    second: atk2
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: P_0033
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: run
      takeName: Take 001
      internalID: 0
      firstFrame: 25
      lastFrame: 55
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: hit
      takeName: Take 001
      internalID: 0
      firstFrame: 60
      lastFrame: 70
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: weak
      takeName: Take 001
      internalID: 0
      firstFrame: 75
      lastFrame: 105
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk1
      takeName: Take 001
      internalID: 0
      firstFrame: 112
      lastFrame: 149
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: skl
      takeName: Take 001
      internalID: 0
      firstFrame: 153
      lastFrame: 292
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: win
      takeName: Take 001
      internalID: 0
      firstFrame: 298
      lastFrame: 363
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ready
      takeName: Take 001
      internalID: 0
      firstFrame: 370
      lastFrame: 390
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: die
      takeName: Take 001
      internalID: 0
      firstFrame: 395
      lastFrame: 412
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk2
      takeName: Take 001
      internalID: 0
      firstFrame: 420
      lastFrame: 456
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 0.45
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.45
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: e96275aa2d039d849946f4f45caf30d2,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
