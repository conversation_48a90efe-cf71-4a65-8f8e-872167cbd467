fileFormatVersion: 2
guid: bdf7a1b89fab13f47b923ed4ac947e8f
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bip001
  - first:
      1: 100002
    second: Bip001 Head
  - first:
      1: 100004
    second: Bip001 L Calf
  - first:
      1: 100006
    second: Bip001 L Clavicle
  - first:
      1: 100008
    second: Bip001 L Foot
  - first:
      1: 100010
    second: Bip001 L Forearm
  - first:
      1: 100012
    second: Bip001 L Hand
  - first:
      1: 100014
    second: Bip001 L HorseLink
  - first:
      1: 100016
    second: Bip001 L Thigh
  - first:
      1: 100018
    second: Bip001 L UpperArm
  - first:
      1: 100020
    second: Bip001 Neck
  - first:
      1: 100022
    second: Bip001 Neck1
  - first:
      1: 100024
    second: Bip001 Pelvis
  - first:
      1: 100026
    second: Bip001 R Calf
  - first:
      1: 100028
    second: Bip001 R Clavicle
  - first:
      1: 100030
    second: Bip001 R Foot
  - first:
      1: 100032
    second: Bip001 R Forearm
  - first:
      1: 100034
    second: Bip001 R Hand
  - first:
      1: 100036
    second: Bip001 R HorseLink
  - first:
      1: 100038
    second: Bip001 R Thigh
  - first:
      1: 100040
    second: Bip001 R UpperArm
  - first:
      1: 100042
    second: Bip001 Spine
  - first:
      1: 100044
    second: Bip001 Spine1
  - first:
      1: 100046
    second: Bip001 Spine2
  - first:
      1: 100048
    second: Bone001
  - first:
      1: 100050
    second: Bone002
  - first:
      1: 100052
    second: Bone003
  - first:
      1: 100054
    second: Bone005
  - first:
      1: 100056
    second: Bone006
  - first:
      1: 100058
    second: Bone007
  - first:
      1: 100060
    second: Bone009
  - first:
      1: 100062
    second: Bone009(mirrored)
  - first:
      1: 100064
    second: Bone013
  - first:
      1: 100066
    second: Bone015
  - first:
      1: 100068
    second: Bone015(mirrored)
  - first:
      1: 100070
    second: Bone016
  - first:
      1: 100072
    second: Bone016(mirrored)
  - first:
      1: 100074
    second: P_0029
  - first:
      1: 100076
    second: //RootNode
  - first:
      1: 100078
    second: weapon_point
  - first:
      4: 400000
    second: Bip001
  - first:
      4: 400002
    second: Bip001 Head
  - first:
      4: 400004
    second: Bip001 L Calf
  - first:
      4: 400006
    second: Bip001 L Clavicle
  - first:
      4: 400008
    second: Bip001 L Foot
  - first:
      4: 400010
    second: Bip001 L Forearm
  - first:
      4: 400012
    second: Bip001 L Hand
  - first:
      4: 400014
    second: Bip001 L HorseLink
  - first:
      4: 400016
    second: Bip001 L Thigh
  - first:
      4: 400018
    second: Bip001 L UpperArm
  - first:
      4: 400020
    second: Bip001 Neck
  - first:
      4: 400022
    second: Bip001 Neck1
  - first:
      4: 400024
    second: Bip001 Pelvis
  - first:
      4: 400026
    second: Bip001 R Calf
  - first:
      4: 400028
    second: Bip001 R Clavicle
  - first:
      4: 400030
    second: Bip001 R Foot
  - first:
      4: 400032
    second: Bip001 R Forearm
  - first:
      4: 400034
    second: Bip001 R Hand
  - first:
      4: 400036
    second: Bip001 R HorseLink
  - first:
      4: 400038
    second: Bip001 R Thigh
  - first:
      4: 400040
    second: Bip001 R UpperArm
  - first:
      4: 400042
    second: Bip001 Spine
  - first:
      4: 400044
    second: Bip001 Spine1
  - first:
      4: 400046
    second: Bip001 Spine2
  - first:
      4: 400048
    second: Bone001
  - first:
      4: 400050
    second: Bone002
  - first:
      4: 400052
    second: Bone003
  - first:
      4: 400054
    second: Bone005
  - first:
      4: 400056
    second: Bone006
  - first:
      4: 400058
    second: Bone007
  - first:
      4: 400060
    second: Bone009
  - first:
      4: 400062
    second: Bone009(mirrored)
  - first:
      4: 400064
    second: Bone013
  - first:
      4: 400066
    second: Bone015
  - first:
      4: 400068
    second: Bone015(mirrored)
  - first:
      4: 400070
    second: Bone016
  - first:
      4: 400072
    second: Bone016(mirrored)
  - first:
      4: 400074
    second: P_0029
  - first:
      4: 400076
    second: //RootNode
  - first:
      4: 400078
    second: weapon_point
  - first:
      21: 2100000
    second: P_0029
  - first:
      43: 4300000
    second: P_0029
  - first:
      74: 7400000
    second: idle
  - first:
      74: 7400002
    second: run
  - first:
      74: 7400004
    second: ready
  - first:
      74: 7400006
    second: hit
  - first:
      74: 7400008
    second: weak
  - first:
      74: 7400010
    second: atk1
  - first:
      74: 7400012
    second: win
  - first:
      74: 7400014
    second: die
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: P_0029
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: run
      takeName: Take 001
      internalID: 0
      firstFrame: 35
      lastFrame: 47
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ready
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: hit
      takeName: Take 001
      internalID: 0
      firstFrame: 55
      lastFrame: 65
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: weak
      takeName: Take 001
      internalID: 0
      firstFrame: 70
      lastFrame: 100
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk1
      takeName: Take 001
      internalID: 0
      firstFrame: 105
      lastFrame: 135
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: win
      takeName: Take 001
      internalID: 0
      firstFrame: 140
      lastFrame: 170
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: die
      takeName: Take 001
      internalID: 0
      firstFrame: 175
      lastFrame: 190
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 07adb9e5e6f02c8479baae69f93c41ce,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
