---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---仿效EffectMgr 管理所有表情動態
---@class lua
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2025.1.3
EmojiMgr = {}
local this = EmojiMgr

this.m_EmojiPrefabPool = {}
this.m_TablePlayingTable = {}


local EMOJI_ROOT_NAME = "Emoji_Root"
local EMOJI_STORED_NAME = "Emoji_Stored"


---初始化
function EmojiMgr.Init()
    this.m_EmojiRoot = GameObject.New(EMOJI_ROOT_NAME).transform
    this.m_EmojiStored = GameObject.New(EMOJI_STORED_NAME).transform
    this.m_EmojiStored:SetParent(this.m_EmojiRoot)
end

---生成所有的Emoji Table
function EmojiMgr.BuildAllEmoji()

    local _EmojiDic = EmojiData.GetAllDicData()
    for key,value in pairs(_EmojiDic) do
        this.m_EmojiPrefabPool[value.m_FileName] = {}
    end
end

---從Emoji名稱取得 取得 Emoji prefab
---@param iEmojiName string Emoji的名字
function EmojiMgr.GetEmojiByName(iEmojiName,iDelegate)

    local _SetEmojiPlayingTable = function(iObj,iHashCode,iEmojiName,iDelegate,iIsUseOld)
        this.m_TablePlayingTable[iHashCode] = {}
        this.m_TablePlayingTable[iHashCode].m_GObj = iObj
        this.m_TablePlayingTable[iHashCode].m_PrefabName = iEmojiName
        this.m_TablePlayingTable[iHashCode].m_HashCode = iHashCode

        if iIsUseOld then
            this.m_EmojiPrefabPool[iEmojiName][iHashCode] = nil
        end

        if iDelegate then
            iDelegate(iHashCode,this.m_TablePlayingTable[iHashCode].m_GObj)
        end

    end

    local _TempObj
    ---有table 且有物件
    if this.m_EmojiPrefabPool[iEmojiName] and table.Count(this.m_EmojiPrefabPool[iEmojiName]) > 0 then
        _TempObj = table.First(this.m_EmojiPrefabPool[iEmojiName])
        local _HashCode = _TempObj.m_EmojiData.m_GObj:GetHashCode()
        _SetEmojiPlayingTable(_TempObj.m_EmojiData.m_GObj,_HashCode, iEmojiName,iDelegate,true)
    else
        ResourceMgr.Load(iEmojiName, function(iObj)
            local _HashCode
            if not Extension.IsUnityObjectNull(iObj) then
                _TempObj = iObj
                _HashCode = _TempObj:GetHashCode()
                _SetEmojiPlayingTable(_TempObj, _HashCode,iEmojiName,iDelegate,false)
            end
        end)
    end
end

---歸還Emoji
---@param iHashCode 物件的HashCode
---@param iNeedChangeLayer bool 是否要切換layer 回收時默認將所有layer都改成UI 理論上只有生給HUD使用的emoji需要改回去其他會默認Default
function EmojiMgr.ReturnEmojiByNameAndHashCode(iHashCode,iNeedChangeLayer)

    if  this.m_TablePlayingTable[iHashCode] == nil or
        this.m_TablePlayingTable[iHashCode].m_GObj == nil then
        return
    end

    local _PrefabName = this.m_TablePlayingTable[iHashCode].m_PrefabName

    ---歸還的時候 幫對應prefab 放到對應物件名稱/HashCode 的裡面
    if this.m_EmojiPrefabPool[_PrefabName] == nil then
        this.m_EmojiPrefabPool[_PrefabName] = {}
    end

    if this.m_EmojiPrefabPool[_PrefabName][iHashCode] == nil then
        this.m_EmojiPrefabPool[_PrefabName][iHashCode] = {}
    end

    ---回收時讓layer變成default
    if iNeedChangeLayer == true then
        local _AllTrans = this.m_TablePlayingTable[iHashCode].m_GObj:GetComponentsInChildren(typeof(UnityEngine.Transform)):ToTable()
        for key,value in pairs(_AllTrans) do
            value.gameObject.layer = Layer.default
        end
    end

    this.m_EmojiPrefabPool[_PrefabName][iHashCode].m_EmojiData = {}
    this.m_EmojiPrefabPool[_PrefabName][iHashCode].m_EmojiData.m_GObj = this.m_TablePlayingTable[iHashCode].m_GObj

    this.m_EmojiPrefabPool[_PrefabName][iHashCode].m_EmojiData.m_GObj:SetActive(false)
    this.m_EmojiPrefabPool[_PrefabName][iHashCode].m_EmojiData.m_GObj.transform:SetParent(this.m_EmojiStored)

    local _ImageTable =  this.m_EmojiPrefabPool[_PrefabName][iHashCode].m_EmojiData.m_GObj:GetComponentsInChildren(typeof(UnityEngine.UI.Image)):ToTable()

    ---因為從簡易聊天區歸還物件時 物件可能被調整過透明度 (配合簡易聊天區的淡出效果) 所以回收時要把透明定設定回來
    for key ,value in pairs (_ImageTable) do
        value.color = Color.White
    end

    this.m_TablePlayingTable[iHashCode] = nil
end

function EmojiMgr.OnUnrequire()
    if this.m_EmojiRoot then
        this.m_EmojiRoot.gameObject:Destroy()
    end
    return true
end
return EmojiMgr
