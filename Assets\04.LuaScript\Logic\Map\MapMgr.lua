---控制包含世界地圖、中地圖、小地圖、城戰地圖等共用資訊
require("Logic/Map/MapIconSetting")
---@class MapMgr
MapMgr = {}
local this = MapMgr

this.m_IsDebug = true
this.m_IsInit = false
this.m_MapScale = 0
this.m_ScaleLevel = 0

--region Debug 功能
--- Debug 類型
EDebugType = {
    Log = 1,
    LogWarning = 2,
    LogError = 3,
    DrawRay = 4
}

---Debug功能
---@param iMsg 訊息
---@param iType EDebugType
function MapMgr.Debugger(iMsg, iType, iWorldPos, iDirection)
    if this.m_IsDebug then
        if iType == EDebugType.Log then
            D.Log("[MapMgr] " .. iMsg)
        elseif iType == EDebugType.LogWarning then
            D.LogWarning("[MapMgr] " .. iMsg)
        elseif iType == EDebugType.LogError then
            D.LogError("[MapMgr] " .. iMsg .. debug.traceback())
        elseif iType == EDebugType.DrawRay then
            D.DrawRay(iWorldPos, iDirection)
        end
    end
end
--endregion

---預設地圖大小 = 512
this.MAP_SIZE = 32
---小地圖比例尺
this.MAP_SCALE_LEVEL = { 150, 200, 250, 260, 300, 350, 400, 450, 500 }
---Map Logo字串
this.m_MapLogoString = "Logo_Map"
---大地圖存檔字串
this.m_LoveAreaString = "m_LoveAreaData"
---大地圖標記類型
this.m_NowPointType = 0
---是否開啟大地圖
this.m_IsOpenMap = false

---地圖貼圖預設名稱 = "map_"
local MAP_TEXTURE_HEAD = "map_"
---地圖刷新間隔
local MAP_UPDATE = 3
local m_MiniMapDefaultImage = nil
local m_BigMapDefaultImage = nil
local m_CurrentMapID = 0
local m_UpdateTime = MAP_UPDATE

---已被隱藏的地圖索引
local m_HidenIndex = {}

---地圖這顯示的角色分類
---@class MapMgr.ECharacterType
MapMgr.ECharacterType = {
    Player = 1,     -- 玩家、其他玩家...
    NPC = 2         -- 各類NPC
}

function MapMgr.Init()
    MapIconSetting:Init(function()
        this.CreatePlayerIcon()
        this.CreateClickPointIcon()
        this.CreatePlayerViewCone()
    end)

    this.CreateMapRoot()
    this.CreatePool()
    this.CreateMapMesh()
    this.CreateBigMapMesh()
    this.CreateMapEffect()
    this.m_IsInit = true
end

function MapMgr.Update()
    if not this.m_IsInit or RoleMgr.m_RC_Player == nil then
        return
    end

    -- 刷新玩家
    this.UpdatePlayerIcon()

    -- 刷新玩家鏡頭範圍
    this.UpdatePlayerViewCone()

    -- 檢查場景地圖是否為正常顯示，否則不做更新
    if m_CurrentMapID == nil or m_CurrentMapID == 0 then
        return
    end

    this.UpdatePin()

    if m_UpdateTime > 0 then
        m_UpdateTime = m_UpdateTime - Time.deltaTime
        return
    end

    -- 刷新靜態Icon
    this.UpdateStaticIcon()
    -- 刷新動態Icon
    this.UpdateDynamicIcon()

    m_UpdateTime = MAP_UPDATE
end

function MapMgr.Release()
    this.ClearAllPool()
    m_CurrentMapID = nil
end

---刷新全地圖
function MapMgr.RefreshMap()
    ---更新地圖比例
    local function ConfigureMap(iMapData)
        if iMapData.m_SmallMapScale < table.Count(this.MAP_SCALE_LEVEL) then
            this.m_ScaleLevel = this.MAP_SCALE_LEVEL[iMapData.m_SmallMapScale +1]
        else
            this.m_ScaleLevel = this.MAP_SCALE_LEVEL[this.MAP_SCALE_LEVEL.Count]
        end

        this.m_MapScale = this.MAP_SIZE / this.m_ScaleLevel
    end

    -- 取得所在場景編號
    local _SceneID = SceneMgr.GetSceneID()
    if not _SceneID then
        return
    end

    -- 檢查 所在場景 與 當前MapID 是否相符
    if not m_CurrentMapID or m_CurrentMapID ~= _SceneID then
        local _MapData = SceneAttributeData.Get(_SceneID)
        if not _MapData or _MapData.m_MapNoise == 1 then
            m_CurrentMapID = 0
            this.m_MapMesh.m_MeshRender.material.mainTexture = m_MiniMapDefaultImage
            this.m_NoSignalText.text = TextData.Get(520310)
            this.HideClickPoint()
            this.EnableGlitch()
            MiniMap_Controller.RefreshMap()

            return
        end

        -- 換大地圖
        -- 更新比例
        ConfigureMap(_MapData)
        -- 更新圖片
        this.ChangeMapTexture(_MapData.m_PrefabSName)
        -- 更新傳送點
        this.UpdateTeleportPointIcon()
        this.DisableGlitch()
    end
end

function MapMgr.GetCurrentUI()
    return this.m_CurrentUI
end

function MapMgr.SetCurrentUI(iStr)
    if this.m_CurrentUI == iStr then
        return
    end

    --範圍顯示需要改在延遲開啟結束後
    -- 切換顯示小地圖的行為
    if iStr == MiniMap_Controller.m_UIView then
        this.m_Camera.transform.localPosition = Vector3(this.MAP_SIZE/2, this.MAP_SIZE/2, 0)
        -- TODO:之後換讀玩家存檔
        this.m_Camera.orthographicSize = this.MAP_SIZE/16
        this.m_Camera.rect = Rect.New(0, 0, 1, 1)
        MiniMap_Controller.RefreshMap()
        this.m_Camera.gameObject:SetActive(true)
        this.m_MapMesh.m_MeshObj.gameObject:SetActive(true)
        this.m_BCamera.gameObject:SetActive(false)
        this.m_BigMapMesh.m_MeshObj.gameObject:SetActive(false)

        this.m_DynamicPoolRoot.gameObject:SetActive(true)
        this.m_StaticPoolRoot.gameObject:SetActive(true)
    -- 切換顯示中地圖的行為
    elseif iStr == Map_Controller.m_UIView then
        this.m_BCamera.transform.localPosition = Vector3(this.MAP_SIZE/2, this.MAP_SIZE/2, 0)
        this.m_BCamera.orthographicSize = this.MAP_SIZE/2
        this.m_BCamera.rect = Rect.New(0, 0, 1, 1)
        this.m_Camera.gameObject:SetActive(false)
        this.m_MapMesh.m_MeshObj.gameObject:SetActive(false)
        this.m_BCamera.gameObject:SetActive(true)
        this.m_BigMapMesh.m_MeshObj.gameObject:SetActive(true)
        --開啟大地圖不顯示
        this.m_DynamicPoolRoot.gameObject:SetActive(false)
        this.m_StaticPoolRoot.gameObject:SetActive(false)
    end

    this.m_CurrentUI = iStr
end

--region Pool 相關
local m_StorePosition = Vector3.New(-10, 0, 10)
--region 字典
--- 傳送點 (因為S不會傳所以傳送點另外管，沒有SID也沒有NpcMode，不過不會變動只需要進出場更新就好)
this.m_Dict_TeleportPoint = {}
--- 靜態角色 Dictionary
this.m_Dict_StaticCharacter = {}
--- 動態角色 Dictionary
this.m_Dict_DynamicCharacter = {}
--- 標記組
this.m_Dict_Pin = {}
--endregion

--region 池
---資料會比換場完成先傳進來，先暫存下來
this.m_StaticPool = {}
this.m_DynamicPool = {}
this.m_PinPool = {}
---地圖Icon池分類Enum
---@class MapMgr.EPoolType
this.EPoolType = {
    Static = 1,
    Dynamic = 2,
    Pin = 3,
    Teleport = 4
}
--endregion

function MapMgr.CreatePool()
    this.m_StaticPool = Extension.GetGameObjPool(
            10,
            0,
            this.PoolReset,
            this.PoolObjInit
    )

    this.m_StaticPoolRoot = GameObject.New("StaticPool_Root")
    this.m_StaticPoolRoot.transform:SetParent(this.m_MapRoot.transform)
    this.m_StaticPoolRoot.transform.localPosition = Vector3.zero
    this.m_StaticPoolRoot.layer = Layer.UIModel

    this.m_DynamicPool = Extension.GetGameObjPool(
            100,
            0,
            this.PoolReset,
            this.PoolObjInit
    )

    this.m_DynamicPoolRoot = GameObject.New("DynamicPool_Root")
    this.m_DynamicPoolRoot.transform:SetParent(this.m_MapRoot.transform)
    this.m_DynamicPoolRoot.transform.localPosition = Vector3.zero
    this.m_DynamicPoolRoot.layer = Layer.UIModel

    this.m_PinPool = Extension.GetGameObjPool(
            10,
            0,
            this.PoolReset,
            this.PoolObjInit
    )

    this.m_PinPoolRoot = GameObject.New("PinPool_Root")
    this.m_PinPoolRoot.transform:SetParent(this.m_MapRoot.transform)
    this.m_PinPoolRoot.transform.localPosition = Vector3.zero
    this.m_PinPoolRoot.layer = Layer.UIModel
end

function MapMgr.PoolObjInit(iObj)
    Extension.AddMissingComponent(iObj, typeof(UnityEngine.SpriteRenderer))
end

function MapMgr.PoolReset(iObj)
    iObj.transform.localPosition = m_StorePosition
    iObj.name = MapIconSetting.m_Str_IconStore
end

function MapMgr.ClearAllPool()
    for key, value in pairs(this.m_Dict_StaticCharacter) do
        this.StorePoolObj(this.EPoolType.Static, value)
    end
    this.m_Dict_StaticCharacter = {}

    for key, value in pairs(this.m_Dict_TeleportPoint) do
        this.StorePoolObj(this.EPoolType.Static, value)
    end
    this.m_Dict_TeleportPoint = {}

    for key, value in pairs(this.m_Dict_DynamicCharacter) do
        this.StorePoolObj(this.EPoolType.Dynamic, value)
    end
    this.m_Dict_DynamicCharacter = {}
end

---歸還
---@param iPoolType MapMgr.EPoolType
---@param iData 字典內容 不傳Index是因為RoleID過長必須轉string，這樣傳進來的格式不一致
function MapMgr.StorePoolObj(iPoolType, iData)
    if not iPoolType or not iData or not iData.m_Obj then
        return
    end

    local _Pool, _Dict = this.GetPoolByType(iPoolType)
    if not _Pool or not _Dict then
        this.Debugger("未能獲取Icon池或Icon字典。", EDebugType.LogError)
        return
    end

    _Pool:Store(iData.m_Obj)
    iData.m_Obj = nil
    table.removeKey(_Dict, iData.m_Index)
end

--- 依物件池分類拿對應資料
function MapMgr.GetPoolByType(iPoolType)
    -- 靜態角色
    if iPoolType == this.EPoolType.Static then
        return this.m_StaticPool, this.m_Dict_StaticCharacter, this.m_StaticPoolRoot
    -- 動態角色
    elseif iPoolType == this.EPoolType.Dynamic then
        return this.m_DynamicPool, this.m_Dict_DynamicCharacter, this.m_DynamicPoolRoot
    -- 標記
    elseif iPoolType == this.EPoolType.Pin then
        return this.m_PinPool, this.m_Dict_Pin, this.m_PinPoolRoot
    -- 傳送點
    elseif iPoolType == this.EPoolType.Teleport then
        return this.m_StaticPool, this.m_Dict_TeleportPoint, this.m_StaticPoolRoot
    end
end
--endregion

--region MapMesh 相關
---一個Mesh頂點數 = 4
local PERMESH_VERTICES = 4
---一個Mesh三角頂點數 = 6
local PERMESH_TRIANGLE = 6
---一個UV的頂點數 = 4
local PERUV_VERTICES = 4
---ShaderName = "Unlit/Texture"
local BACKGROUD_SHADER = "UI/Default"

function MapMgr.CreateMapRoot()
    this.m_MapRoot = GameObject.New("Map_Root")
    this.m_MapRoot.transform.localPosition = Vector3(420,0,0)
    this.m_MapRoot.transform.localScale = Vector3.one

    this.m_MapRoot.layer = Layer.UIModel

    local _MapRectTrans = Extension.AddMissingComponent(this.m_MapRoot, typeof(UnityEngine.RectTransform))
    _MapRectTrans.anchorMin = Vector2.zero
    _MapRectTrans.anchorMax = Vector2.zero
    _MapRectTrans.sizeDelta = Vector2.one
    _MapRectTrans.pivot = Vector2.zero

    this.m_MapMesh = {}
    this.m_BigMapMesh = {}
end

function MapMgr.CreateMapMesh()
    local _Mesh = this.GenerateMesh(1)
    local _MeshMaterial = Material.New(Shader.Find(BACKGROUD_SHADER))

    TextureMgr.Load(this.m_MapLogoString, true, function(iTex)
        m_MiniMapDefaultImage = iTex
        _MeshMaterial.mainTexture = m_MiniMapDefaultImage
        this.m_MapMesh.m_MeshRender, this.m_MapMesh.m_MeshObj = this.GenerateMeshGameObj(_Mesh, _MeshMaterial)
        this.m_MapMesh.m_MeshObj.name = "MapMesh"

        --region Camera相關
        --生成MapCamera
        this.CreateMapCamera()
        --endregion
    end)
end

function MapMgr.CreateBigMapMesh()
    local _Mesh = this.GenerateMesh(1)
    local _MeshMaterial = Material.New(Shader.Find(BACKGROUD_SHADER))

    TextureMgr.Load(this.m_MapLogoString, true, function(iTex)
        m_BigMapDefaultImage = iTex
        _MeshMaterial.mainTexture = m_BigMapDefaultImage
        this.m_BigMapMesh.m_MeshRender, this.m_BigMapMesh.m_MeshObj = this.GenerateMeshGameObj(_Mesh, _MeshMaterial)
        this.m_BigMapMesh.m_MeshObj.name = "BigMapMesh"

        --region Camera相關
        --生成MapCamera
        this.CreateBigMapCamera()
        --endregion
    end)
end

function MapMgr.CreateMapEffect()
    ResourceMgr.Load("MapEffect",
        function(iAsset)
            this.m_MapEffect = iAsset
        end)
end

---生成新 Mesh
---@param iCount 內含幾個頂點組
function MapMgr.GenerateMesh(iCount)
    local _Mesh = UnityEngine.Mesh.New()

    --初始化Mesh四頂點
    local _AllVertices = {
        [1] = Vector3.zero,
        [2] = Vector3.New(0, this.MAP_SIZE, 0),
        [3] = Vector3.New(this.MAP_SIZE, this.MAP_SIZE, 0),
        [4] = Vector3.New(this.MAP_SIZE, 0, 0)
    }

    --初始化兩三角形
    local _Triangles = {}
    --總頂點數 iCount*2*3
    for i = 1, iCount do
        local _startVertex = (i-1) * PERMESH_VERTICES
        _Triangles[(i-1) * PERMESH_TRIANGLE + 1] = _startVertex
        _Triangles[(i-1) * PERMESH_TRIANGLE + 2] = _startVertex +1
        _Triangles[(i-1) * PERMESH_TRIANGLE + 3] = _startVertex +2
        _Triangles[(i-1) * PERMESH_TRIANGLE + 4] = _startVertex
        _Triangles[(i-1) * PERMESH_TRIANGLE + 5] = _startVertex +2
        _Triangles[(i-1) * PERMESH_TRIANGLE + 6] = _startVertex +3
    end

    --初始化UV
    local _UVs = {
        [1] = Vector2.zero,
        [2] = Vector2.New(0, 1),
        [3] = Vector2.New(1, 1),
        [4] = Vector2.New(1, 0)
    }

    _Mesh.vertices = _AllVertices
    _Mesh.uv = _UVs
    _Mesh.triangles = _Triangles
    _Mesh.bounds = Bounds.New((Vector3(this.MAP_SIZE/2, this.MAP_SIZE/2, 0)), Vector3.one * this.MAP_SIZE)

    return _Mesh
end

---生成 Mesh Object
---@param iMesh 地圖Mesh
---@param iMaterial 地圖材質
function MapMgr.GenerateMeshGameObj(iMesh, iMaterial)
    local _map = GameObject.New()
    _map.layer = Layer.UIModel
    _map.transform:SetParent(this.m_MapRoot.transform)
    _map.transform.localPosition = Vector3.New(0, 0, 4)
    _map.transform.localScale = Vector3.one

    local _render = Extension.AddMissingComponent(_map, typeof(UnityEngine.MeshRenderer))
    _render.shadowCastingMode = EShadowCastingMode.Off
    _render.receiveShadows = false
    _render.material = iMaterial

    local _MeshFilter = Extension.AddMissingComponent(_map, typeof(UnityEngine.MeshFilter))
    _MeshFilter.mesh = iMesh

    local _MeshCollider = Extension.AddMissingComponent(_map, typeof(UnityEngine.MeshCollider))

    return _render, _map
end

---更換地圖底圖
function MapMgr.ChangeMapTexture(iID)
    --讀場景圖
    TextureMgr.Load(MAP_TEXTURE_HEAD..iID, true, function(iTex)
        if iTex == nil then
            this.m_MapMesh.m_MeshRender.material.mainTexture = m_MiniMapDefaultImage
            m_CurrentMapID = 0
            return
        end

        this.m_MapMesh.m_MeshRender.material.mainTexture = iTex
        m_CurrentMapID = iID

        --更新小地圖
        MiniMap_Controller.RefreshMap()
    end)
end

function MapMgr.ChangeBigMapTexture(iID)
    --讀場景圖
    TextureMgr.Load(MAP_TEXTURE_HEAD..iID, true, function(iTex)
        if iTex == nil then
            this.m_BigMapMesh.m_MeshRender.material.mainTexture = m_BigMapDefaultImage
            return
        end

        this.m_BigMapMesh.m_MeshRender.material.mainTexture = iTex
    end)
end
--endregion

--region Icon 相關

--region 各式角色
---添加角色進入清單
---@param iData RoleCreateData
---@param iType EventNPCMode
function MapMgr.AddCharacter(iData, iType)
    if iData.m_RoleID then
        this.AddPlayerToList(iData.m_RoleID)
    elseif iData.m_NPCID then
        this.AddNpcToList(iData, iType)
    end
end

---從清單移除角色
---@param iIndex number SID
---@param iCharacterType MapMgr.ECharacterType
---@param iNpcID number NPCData.m_ID(玩家不傳)
function MapMgr.RemoveCharacter_withID(iIndex, iCharacterType, iNpcID)
    if iCharacterType == this.ECharacterType.NPC then
        local _Data = NPCData:Get(iNpcID)
        if _Data then
            this.RemoveCharacter(iIndex, iCharacterType, _Data.m_NpcMod)
        else
            --看是否為假玩家的
        end
    elseif iCharacterType == this.ECharacterType.Player then
        --this.StorePoolObj(this.EPoolType.Dynamic, this.m_Dict_DynamicCharacter[tostring(iIndex)])
        this.RemoveCharacter(iIndex, iCharacterType)
    end
end

---從清單移除角色
---@param iIndex number SID
---@param iCharacterType MapMgr.ECharacterType
---@param iNpcID number iNpcMode(玩家不傳)
function MapMgr.RemoveCharacter(iIndex, iCharacterType, iNpcMode)
    if iCharacterType == this.ECharacterType.NPC then
        local _Dict, _GroupType = this.CheckNpcType(iNpcMode)
        if not _Dict or not _GroupType then
            return
        end

        -- 這邊可以改一下
        if _Dict[iIndex] then
            local _PoolType = MapIconSetting:GetMapPoolTypeByNPCMode(_Dict[iIndex].m_NPCMode)
            this.StorePoolObj(_PoolType, _Dict[iIndex])
        end
    elseif iCharacterType == this.ECharacterType.Player then
        this.StorePoolObj(this.EPoolType.Dynamic, this.m_Dict_DynamicCharacter[tostring(iIndex)])
    end
end

---將Npc資料加入清單
---會將資料分別塞入對應的清單中
---@param iData RoleCreateData
---@param iType EventNPCMode
function MapMgr.AddNpcToList(iData, iType)
    if not iData.m_SID then
        return
    end

    local _Dict, _GroupType = this.CheckNpcType(iType)
    if not _Dict or not _GroupType then
        return
    end

    if _Dict[iData.m_SID] and _Dict[iData.m_SID].m_Obj then
        --this.Debugger("Npc SID: "..iData.m_SID.." 已存在地圖Icon池中，並試圖再添加新的Character", EDebugType.LogError)
        local _DictContent = _Dict[iData.m_SID]
        this.RemoveCharacter(_DictContent.m_Index, _DictContent.m_Character, _DictContent.m_NPCMode)
    end

    _Dict[iData.m_SID] = {}
    _Dict[iData.m_SID].m_Index = iData.m_SID
    _Dict[iData.m_SID].m_Character = this.ECharacterType.NPC
    _Dict[iData.m_SID].m_GroupType = _GroupType
    _Dict[iData.m_SID].m_NPCMode = iType
    _Dict[iData.m_SID].m_Obj = nil
    _Dict[iData.m_SID].m_SpriteRenderer = nil

    if iData.m_EventID then
        _Dict[iData.m_SID].m_EventID = iData.m_EventID
    end
end

function MapMgr.AddPlayerToList(iRoleID)
    if not iRoleID then
        return
    end
    -- 長度太長，先轉成string
    local _Str_RoleID = tostring(iRoleID)
    if this.m_Dict_DynamicCharacter[_Str_RoleID] and this.m_Dict_DynamicCharacter[_Str_RoleID].m_Obj then
        this.Debugger("玩家 ID: ".._Str_RoleID.." 已存在地圖Icon池中，並試圖再添加相同的RoleID", EDebugType.LogError)
        return
    end

    this.m_Dict_DynamicCharacter[_Str_RoleID] = {}
    this.m_Dict_DynamicCharacter[_Str_RoleID].m_Index = _Str_RoleID
    this.m_Dict_DynamicCharacter[_Str_RoleID].m_Character = this.ECharacterType.Player
    this.m_Dict_DynamicCharacter[_Str_RoleID].m_GroupType = EMapGroupType.OtherPlayer
    this.m_Dict_DynamicCharacter[_Str_RoleID].m_Obj = nil
    this.m_Dict_DynamicCharacter[_Str_RoleID].m_SpriteRenderer = nil
end

---檢查該Npc類型
---@param iType EventNPCMode
---@return table,EMapGroupType 所屬於的Dictionary,EMapGroupType
function MapMgr.CheckNpcType(iType)
    --可戰鬥Npc
    if iType == EventNPCMode.Fight or
        iType == EventNPCMode.FightCannotDie then
        return this.m_Dict_DynamicCharacter, EMapGroupType.Monster
    --任務
    elseif iType == EventNPCMode.Mission then
        return this.m_Dict_StaticCharacter, EMapGroupType.QuestRelatedNPC
    --傳送點、驛站
    elseif iType == EventNPCMode.TransPoint or
            iType == EventNPCMode.Station then
        return this.m_Dict_StaticCharacter, EMapGroupType.Teleporter
    --習武導師
    elseif iType == EventNPCMode.Mentor then
        return this.m_Dict_StaticCharacter, EMapGroupType.Mentor
    --商店、兌換、倉庫...類
    elseif iType == EventNPCMode.Weapon or
            iType == EventNPCMode.Armor or
            iType == EventNPCMode.Item or
            iType == EventNPCMode.Exchange or
            iType == EventNPCMode.Sigma or
            iType == EventNPCMode.Fraternity or
            iType == EventNPCMode.Boat or
            iType == EventNPCMode.AnimalTrainer or
            iType == EventNPCMode.Warehouse then
        return this.m_Dict_StaticCharacter, EMapGroupType.Merchant
    else
        -- 部分NPC種類不需要在地圖上顯示，故不提醒
        --this.Debugger("該 EventNPCMode:" .. iType .. " 並未被添加進 Pool 中", EDebugType.LogError)
        return false, false
    end
end

--[[ 已廢棄但保留做法，這是用mapnpc串檔生成的靜態角色資料，現在改為依靠S端提供Npc資訊 + FloorBlockController生成傳送點
---用場景ID取事件檔
---更新靜態NPC資料
---@param iID 場景ID
function MapMgr.UpdateStaticNpcData(iID)

    local _EventMapNpcs = EventMapNPC.GetEventMapNPC(iID)
    local _OccupiedIndex = 1

    this.m_AllStaticNpcData = {}
    for i = 1, table.Count(_EventMapNpcs)do
        if _EventMapNpcs[i].m_Sources == 1 then
            -- 初始化該索引內容
            this.m_AllStaticNpcData[_OccupiedIndex] = {}
            this.m_AllStaticNpcData[_OccupiedIndex].Occupied = true
            this.m_AllStaticNpcData[_OccupiedIndex].Data = _EventMapNpcs[i]
            this.m_AllStaticNpcData[_OccupiedIndex].m_Obj = nil
            this.m_AllStaticNpcData[_OccupiedIndex].m_SpriteRenderer = nil

            --- 依Npc類型轉換Icon分類
            --任務
            if _EventMapNpcs[i].m_NPCMode == EventNPCMode.Mission then
                this.m_AllStaticNpcData[_OccupiedIndex].Type = EMapGroupType.QuestRelatedNPC
                _OccupiedIndex = _OccupiedIndex + 1
            --商店、兌換、倉庫...類
            elseif _EventMapNpcs[i].m_NPCMode == EventNPCMode.Weapon or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Armor or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Item or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Exchange or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Sigma or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Fraternity or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Boat or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.AnimalTrainer or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Warehouse then
                this.m_AllStaticNpcData[_OccupiedIndex].Type = EMapGroupType.Merchant
                _OccupiedIndex = _OccupiedIndex + 1
            --傳送點
            elseif _EventMapNpcs[i].m_NPCMode == EventNPCMode.TransPoint or
                    _EventMapNpcs[i].m_NPCMode == EventNPCMode.Station then
                this.m_AllStaticNpcData[_OccupiedIndex].Type = EMapGroupType.Teleporter
                _OccupiedIndex = _OccupiedIndex + 1
            --習武導師
            elseif _EventMapNpcs[i].m_NPCMode == EventNPCMode.Mentor then
                this.m_AllStaticNpcData[_OccupiedIndex].Type = EMapGroupType.Mentor
                _OccupiedIndex = _OccupiedIndex + 1
            end
        end
    end
end]]

---更新靜態角色顯示
---注意：只處理顯示部分，盡量不要對池做操作
---如果有資訊不同步，請在別處處理，保持功能單一
function MapMgr.UpdateStaticIcon()
    for key, value in pairs(this.m_Dict_StaticCharacter) do
        --[[if _StaticCharacter.Occupied == false or not this.CheckVisable(_StaticCharacter.Type) then
            --不顯示的移到攝影機外
            this.HideIcon(key, this.EPoolType.Static)
        end]]

        --檢查任務型角色，需要按照任務階段顯示Icon
        if value.m_NPCMode == EventNPCMode.Mission then
            --檢查任務ID，決定用哪個Icon
            local _MissionType = EventMgr:TryGetMissionID(value.m_EventID, value.m_NPCMode)

            if _MissionType then
                local _IconType = MapIconSetting.MissionTypeToMapIcon:GetCase(_MissionType)
                if _IconType ~= EMapIconsType.Non then
                    --更新Icon
                    this.UpdateIconSprite(key, _IconType, this.EPoolType.Static)
                end
            else
                --移到攝影機外
                this.HideIcon(key, this.EPoolType.Static)
            end

        else
            --依角色類型決定Icon
            local _MapIconType = MapIconSetting.NpcModeToMapIcon:GetCase(value.m_NPCMode)
            if _MapIconType ~= EMapIconsType.Non then
                --更新Icon
                this.UpdateIconSprite(key, _MapIconType, this.EPoolType.Static)
            end
        end

        --更新位置
        local _RC = NPCMgr.m_Table_ActiveNPC[key]
        if _RC then
            local _Pos = _RC.transform.localPosition
            this.UpdateIconPosition(key,_Pos * this.m_MapScale, this.EPoolType.Static)
        else
            --this.Debugger("Can't get RoleController with SID: "..key, EDebugType.LogError)
        end
    end
end

---更新動態角色顯示
---注意：只處理顯示部分，盡量不要對池內部做操作
---如果有資訊不同步，請在別處處理，保持功能單一
function MapMgr.UpdateDynamicIcon()
    local _DynamicPoolType = this.EPoolType.Dynamic
    for key, value in pairs(this.m_Dict_DynamicCharacter) do
        --[[
        if _DynamicCharacter.Occupied == false or not this.CheckVisable(_DynamicCharacter.Type) then
            --不顯示的移到攝影機外
            this.HideIcon(_index)
        else]]

        -- 其他玩家
        if value.m_GroupType == EMapGroupType.OtherPlayer then
            local _RoleController = RoleMgr.Get(key)
            if _RoleController then
                --todo: 顯示玩家，暫時通通顯示成其他玩家
                --this.UpdateIconScaling(key, DEFAULT_QUAD_SIZE)
                this.UpdateIconSprite(key, EMapIconsType.OtherPlayer, _DynamicPoolType)
                this.UpdateIconPosition(key, _RoleController.transform.localPosition * this.m_MapScale, _DynamicPoolType)

                --[[if _DynamicCharacter.AppearanceNeedToBeUpdated then
                    -- 敵方單位
                    --if  then
                    --    this.UpdateIconTexture(_index, EMapGroupType.Enemy)
                    --else
                    --  在PVP區域，但是同隊
                    --  if  then
                    --      this.UpdateIconTexture(_index, EMapGroupType.Teammate)
                    --  else
                    --      this.UpdateIconTexture(_index, EMapGroupType.OtherPlayer)
                    --  end
                    --end
                    _DynamicCharacter.AppearanceNeedToBeUpdated = false
                end]]
            else
                this.HideIcon(key, _DynamicPoolType)
            end
        -- 隊友
        elseif value.m_GroupType == EMapGroupType.Teammate then
            local _RoleController = RoleMgr.Get(key)
            if _RoleController then
                --this.UpdateIconScaling(key, DEFAULT_QUAD_SIZE)
                this.UpdateIconSprite(key, EMapIconsType.Teammate, _DynamicPoolType)
                this.UpdateIconPosition(key, _RoleController.transform.localPosition * this.m_MapScale, _DynamicPoolType)
            else
                this.HideIcon(key, _DynamicPoolType)
            end
        elseif value.m_GroupType == EMapGroupType.Monster then
            local _ActiveData = NPCMgr.m_Table_ActiveNPC[key]
            if not _ActiveData then
                this.HideIcon(key, _DynamicPoolType)
            else
                local _NpcData = NPCData:Get(_ActiveData.m_NPCID)
                if not _NpcData then
                    this.HideIcon(key, _DynamicPoolType)
                else
                    --this.UpdateIconScaling(key, DEFAULT_QUAD_SIZE)
                    this.UpdateIconSprite(key, EMapIconsType.Monster, _DynamicPoolType)
                    this.UpdateIconPosition(key, _ActiveData.transform.localPosition * this.m_MapScale, _DynamicPoolType)
                end
            end
        end
    end
end
--endregion

--region 傳送點
---添加傳送點
function MapMgr.AddTeleportPoint(iPos)
    local _MaxIndex = table.Count(this.m_Dict_TeleportPoint)
    this.m_Dict_TeleportPoint[_MaxIndex + 1] = {}
    this.m_Dict_TeleportPoint[_MaxIndex + 1].m_Pos = iPos
    this.m_Dict_TeleportPoint[_MaxIndex + 1].m_NPCMode = EventNPCMode.TransPoint
end
--[[
---清除傳送點(全)
function MapMgr.ClearTeleportPoint()
    for key, value in pairs(this.m_Dict_TeleportPoint) do
        if value.m_Obj then
            this.m_StaticPool:Store(value.m_Obj)
            value.m_Obj = nil
            value.m_SpriteRenderer = nil
        end
    end

    this.m_Dict_TeleportPoint = {}
end]]

---更新傳送點
function MapMgr.UpdateTeleportPointIcon()
    for key, value in pairs(this.m_Dict_TeleportPoint) do
        local _MapIconType = MapIconSetting.NpcModeToMapIcon:GetCase(value.m_NPCMode)
        if _MapIconType ~= EventNPCMode.TransPoint then
            --更新Icon
            this.UpdateIconSprite(key, _MapIconType, this.EPoolType.Teleport)
            this.UpdateIconPosition(key,value.m_Pos * this.m_MapScale, this.EPoolType.Teleport)
        else
            this.HideIcon(key, this.EPoolType.Teleport)
        end
    end
end
--endregion

--region 玩家
function MapMgr.CreatePlayerIcon()
    if this.m_PlayerIcon then
        this.Debugger("Already have Player Icon!!!", EDebugType.LogWarning)
        return
    end

    local _PlayerIcon = GameObject.New("PlayerIcon")
    _PlayerIcon.transform:SetParent(this.m_MapRoot.transform)
    _PlayerIcon.transform.localPosition = Vector3.zero
    _PlayerIcon.layer = Layer.UIModel

    local _SpriteRenderer = Extension.AddMissingComponent(_PlayerIcon, typeof(UnityEngine.SpriteRenderer))
    local _IsSuccess, _Data = {}
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Player, _Data)
    if _IsSuccess then
        _PlayerIcon.transform.localScale = _Data.m_Scale
        _SpriteRenderer.sprite = _Data.m_Sprite
    else
        this.Debugger("MapIconScrObj 中未含有 IconType: ".. EMapIconsType.Player)
    end

    this.m_PlayerIcon = _PlayerIcon
end

---更新玩家Icon
function MapMgr.UpdatePlayerIcon()
    if not this.m_PlayerIcon then
        return
    end

    if UIMgr.IsVisible(Map_Controller) then
        this.m_PlayerIcon:SetActive(false)
        return
    end

    local _isNotInNoMapScene = not (m_CurrentMapID == nil or m_CurrentMapID == 0)
    if this.m_PlayerIcon.activeSelf ~= _isNotInNoMapScene then
        this.m_PlayerIcon:SetActive(_isNotInNoMapScene)
    end

    if RoleMgr.m_RC_Player ~= nil then
        local _playerPos = RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.GetPlayerRC().transform)
        local _Position = Vector3.New(this.m_MapScale * _playerPos.x, this.m_MapScale * _playerPos.z, -4.5)
        this.m_PlayerIcon.transform.localPosition = _Position
        local _EulerAngles = RoleMgr.m_RC_Player:GetlocalEulerAngle()
        this.m_PlayerIcon.transform.localRotation = Quaternion.Euler(Vector3(0, 0, -_EulerAngles.y))
    end
end
--endregion

--region 玩家鏡頭方向範圍

--- 玩家視野範圍物件名稱
---@type string
local m_PlayerViewCone_Name = "PlayerViewCone"

--- 主攝影機物件名稱
---@type string
local m_MainCamera_Name = "MainCamera"

--- 玩家視野範圍顏色
---@type Color
local m_PlayerViewConeColor = Extension.GetColor("#61FFEC46")

--- 負責生成一個表示玩家視野範圍的物件，並設置其屬性（例如位置、材質、顏色等）。
function MapMgr.CreatePlayerViewCone()

    if this.m_PlayerViewCone then
        this.Debugger("Already have "..m_PlayerViewCone_Name.."!!!", EDebugType.LogWarning)
        return
    end

    local _PlayerViewCone = GameObject.New(m_PlayerViewCone_Name)
    _PlayerViewCone.transform:SetParent(this.m_MapRoot.transform)

    -- 設定PlayerViewCone的初始位置, 這裡的Z軸位置是1, 需要在其他圖示的下面
    _PlayerViewCone.transform.localPosition = Vector3(0, 0, 1)
    _PlayerViewCone.layer = Layer.UIModel

    local _SpriteRenderer = Extension.AddMissingComponent(_PlayerViewCone, typeof(UnityEngine.SpriteRenderer))
    local _IsSuccess, _Data = {}
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.PlayerViewCone, _Data)
    if _IsSuccess then

        _PlayerViewCone.transform.localScale = _Data.m_Scale
        _SpriteRenderer.sprite = _Data.m_Sprite
        _SpriteRenderer.color = m_PlayerViewConeColor
        _SpriteRenderer.material = _Data.m_Material
    else
        this.Debugger("MapIconScrObj 中未含有 IconType: ".. EMapIconsType.PlayerViewCone)
    end

    -- 設定PlayerViewCone
    this.m_PlayerViewCone = _PlayerViewCone
end

---更新玩家鏡頭方向範圍
function MapMgr.UpdatePlayerViewCone()
    if not this.m_PlayerViewCone then
        return
    end

    if UIMgr.IsVisible(Map_Controller) then
        this.m_PlayerViewCone:SetActive(false)
        return
    end

    local _IsNotInNoMapScene = not (m_CurrentMapID == nil or m_CurrentMapID == 0)

    if this.m_PlayerViewCone.activeSelf ~= _IsNotInNoMapScene then
        this.m_PlayerViewCone:SetActive(_IsNotInNoMapScene)
    end

    local _MainCamera_Transform = GameObject.Find(m_MainCamera_Name) and GameObject.Find(m_MainCamera_Name).transform

    if this.m_PlayerIcon ~= nil and _MainCamera_Transform ~= nil then

        local _PlayerPos = this.m_PlayerIcon.transform.localPosition
        local _MainCamEulerAngle = GFunction.GetTransformLocalEulerAngles( _MainCamera_Transform )

        -- 確保角度在 0 到 360 度之間
        _MainCamEulerAngle.y = (_MainCamEulerAngle.y ) % 360

        local _Radians = math.rad(_MainCamEulerAngle.y)
        local _PosX = _PlayerPos.x + math.sin(_Radians)
        local _PosY = _PlayerPos.y + math.cos(_Radians)
        local _Position = Vector3(_PosX, _PosY, this.m_PlayerViewCone.transform.localPosition.z)

        this.m_PlayerViewCone.transform.localPosition = _Position
        this.m_PlayerViewCone.transform.localRotation = Quaternion.Euler(Vector3(0, 0, -_MainCamEulerAngle.y))
    end
end
--endregion

--region 目標點相關
function MapMgr.CreateClickPointIcon()
    if this.m_ClickPointIcon then
        this.Debugger("Already have WayPoint Icon!!!", EDebugType.LogWarning)
        return
    end

    local _PointIcon = GameObject.New("WayPointIcon")
    _PointIcon.transform:SetParent(this.m_MapRoot.transform)
    _PointIcon.transform.localPosition = m_StorePosition
    _PointIcon.layer = Layer.UIModel

    local _SpriteRenderer = Extension.AddMissingComponent(_PointIcon, typeof(UnityEngine.SpriteRenderer))
    local _IsSuccess, _Data = {}
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.ClickPoint, _Data)
    if _IsSuccess then
        _PointIcon.transform.localScale = _Data.m_Scale
        _SpriteRenderer.sprite = _Data.m_Sprite
    else
        this.Debugger("MapIconScrObj 中未含有 IconType: ".. EMapIconsType.ClickPoint)
    end

    this.m_ClickPointIcon = _PointIcon
end

function MapMgr.UpdateClickPointIcon(iPos, iScale)
    local _isNotInNoMapScene = not (m_CurrentMapID == nil or m_CurrentMapID == 0)
    if _isNotInNoMapScene then
        local _Pos = Vector3(iPos.x * this.m_MapScale, iPos.z * this.m_MapScale, -4.5)
        this.m_ClickPointIcon.transform.localPosition = _Pos
        --this.m_ClickPointIcon.transform.localScale = iScale
    end
end

function MapMgr.HideClickPoint()
    this.m_ClickPointIcon.transform.localPosition = m_StorePosition
end

function MapMgr.GetNavigateToClickPoint()
    --取得在地圖介面上的位置
    local _isSuccess, _Position = RectTransformUtility.ScreenPointToLocalPointInRectangle(
            Map_Controller.m_RectTrans_Map,
            Input.mousePosition,
            UIMgr.m_UICamera,
            nil
    )

    if _isSuccess and _Position then
        return _Position
    else
        return nil
    end
end
--endregion

--region 標記

---添加標記Icon
---@param iIconType EMapIconsType
---@param iPos Vector3
---@param iIsClickable boolean
---@param iCallBack function
function MapMgr.AddPin(iIconType, iPos, iIsClickable, iCallBack)
    local _MaxIndex = table.Count(this.m_Dict_TeleportPoint)
    local _Data = {}
    _Data.m_Index = _MaxIndex + 1
    _Data.m_Pos = iPos
    _Data.m_IconType = iIconType
    _Data.m_IsClickable = iIsClickable
    if _Data.m_IsClickable then
        _Data.m_CallBack = iCallBack
    end
    this.m_Dict_Pin[_MaxIndex + 1] = _Data
    return _Data
end

---更新標記顯示
function MapMgr.UpdatePin()
    for k, v in pairs(this.m_Dict_Pin) do
        if v.m_IconType ~= EMapIconsType.Non then
            this.UpdateIconSprite(k, v.m_IconType, this.EPoolType.Pin)
            this.UpdateIconPosition(k,v.m_Pos * this.m_MapScale, this.EPoolType.Pin)
        else
            this.HideIcon(k, this.EPoolType.Pin)
        end
    end
end

---清除標記
function MapMgr.RemovePin(iIndex)
    if this.m_Dict_Pin[iIndex] and this.m_Dict_Pin[iIndex].m_Obj then
        this.StorePoolObj(this.EPoolType.Pin, this.m_Dict_Pin[iIndex])
    end
end
--endregion

--region Icon處理
---更新Icon Sprite
function MapMgr.UpdateIconSprite(iIndex, iIconType, iPoolType)
    local _Pool, _DataList, _PoolRoot = this.GetPoolByType(iPoolType)
    if _Pool == nil or _DataList == nil or _DataList[iIndex] == nil then
        this.Debugger("無法取得池或Data列表，Pool Type = "..iPoolType .. ". Index = " .. iIndex, EDebugType.LogError)
        return
    end

    if not _DataList[iIndex].m_Obj then
        _DataList[iIndex].m_Obj = {}
        _DataList[iIndex].m_Obj = _Pool:Get()
        _DataList[iIndex].m_Obj.transform:SetParent(_PoolRoot.transform)
        _DataList[iIndex].m_Obj.transform.localPosition = Vector3.zero
        _DataList[iIndex].m_Obj.layer = Layer.UIModel
        _DataList[iIndex].m_Obj.name = MapIconSetting.m_Str_IconPrefix .. iIndex
    end

    if not _DataList[iIndex].m_SpriteRenderer then
        _DataList[iIndex].m_SpriteRenderer = {}
        _DataList[iIndex].m_SpriteRenderer = _DataList[iIndex].m_Obj:GetComponent(typeof(UnityEngine.SpriteRenderer))
    end

    local _IsSuccess, _Data = {}
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(iIconType, _Data)
    if _IsSuccess then
        _DataList[iIndex].m_Obj.transform.localScale = _Data.m_Scale
        _DataList[iIndex].m_SpriteRenderer.sprite = _Data.m_Sprite
        _DataList[iIndex].m_IconType = iIconType
    else
        this.Debugger("MapIconScrObj 中未含有 IconType: ".. iIconType)
    end
end

--更新Icon Position
function MapMgr.UpdateIconPosition(iIndex, iPosition, iPoolType)
    local _Pool, _DataList = this.GetPoolByType(iPoolType)
    if _Pool == nil or _DataList == nil or _DataList[iIndex] == nil then
        this.Debugger("無法取得池或Data列表，Pool Type = "..iPoolType .. ". Index = " .. iIndex, EDebugType.LogError)
        return
    end

    if not _DataList[iIndex].m_Obj then
        return
    end

    if m_HidenIndex[iIndex] then
        return
    end
    --TODO: 要按照Icon類型排前後順序
    _DataList[iIndex].m_Obj.transform.localPosition = Vector3.New(iPosition.x , iPosition.z, 0)
end

function MapMgr.HideIcon(iIndex, iPoolType)
    local _Pool, _DataList = this.GetPoolByType(iPoolType)
    if _Pool == nil or _DataList == nil or _DataList[iIndex] == nil then
        this.Debugger("無法取得池或Data列表，Pool Type = "..iPoolType .. ". Index = " .. iIndex, EDebugType.LogError)
        return
    end

    if not _DataList[iIndex].m_Obj then
        return
    end

    ---被hide了，這邊設true是為了在updateiconpos的時候不會被更新位置
    m_HidenIndex[iIndex] = true

    _DataList[iIndex].m_Obj.transform.localPosition = m_StorePosition
end
--endregion

--endregion

--region Camera
function MapMgr.CreateMapCamera()
    ResourceMgr.Load("MapCamera", function(iAsset)
        local _mapCamera = iAsset
        _mapCamera.transform:SetParent(this.m_MapRoot.transform)
        _mapCamera.transform.localPosition = Vector3.New(this.MAP_SIZE/2, this.MAP_SIZE/2, 1)
        _mapCamera.transform.localScale = Vector3.one

        ---地圖攝影機
        this.m_Camera = Extension.AddMissingComponent(_mapCamera, typeof(Camera))
        this.m_Camera.orthographic = true
        this.m_Camera.orthographicSize = this.MAP_SIZE/2
        this.m_Camera.nearClipPlane = -5
        this.m_Camera.farClipPlane = 5
        this.m_Camera.cullingMask = 2 ^ Layer.UIModel
        ResourceMgr.Load("MiniMapRenderTexture", function(iMiniMapRenderTextureAsset)
            this.m_MiniMapRT = iMiniMapRenderTextureAsset
            this.SetMapRenderTexture(this.m_MiniMapRT)
        end)

        this.m_GlitchCtrl = Extension.AddMissingComponent(_mapCamera, typeof(GlitchCtrl))
        this.m_NoSignalImg = _mapCamera.transform:Find("Canvas_NoSignal")

        local _NoSignalPanel = this.m_NoSignalImg:Find("Panel_NoSignal")
        this.m_NoSignalText = _NoSignalPanel:Find("Text_NoSignal")
        this.m_NoSignalText = this.m_NoSignalText:GetComponent(typeof( TMPro.TextMeshProUGUI ))
    end)

end

function MapMgr.CreateBigMapCamera()
    ResourceMgr.Load("BigMapCamera", function(iAsset)
        local _BigmapCamera = iAsset
        _BigmapCamera.transform:SetParent(this.m_MapRoot.transform)
        _BigmapCamera.transform.localPosition = Vector3.New(this.MAP_SIZE/2, this.MAP_SIZE/2, 1)
        _BigmapCamera.transform.localScale = Vector3.one

        ---地圖攝影機
        this.m_BCamera = Extension.AddMissingComponent(_BigmapCamera, typeof(Camera))
        this.m_BCamera.orthographic = true
        this.m_BCamera.orthographicSize = this.MAP_SIZE/2
        this.m_BCamera.nearClipPlane = -5
        this.m_BCamera.farClipPlane = 5
        this.m_BCamera.cullingMask = 2 ^ Layer.UIModel
        ResourceMgr.Load("AreaMapRenderTexture", function(iAreaMapRenderTextureAsset)
            this.m_AreaMapRT = iAreaMapRenderTextureAsset
            this.m_BCamera.targetTexture = this.m_AreaMapRT
        end)

        this.m_BigMapGlitchCtrl = Extension.AddMissingComponent(_BigmapCamera, typeof(GlitchCtrl))
        this.m_BigMapNoSignalImg = _BigmapCamera.transform:Find("Canvas_BigMapNoSignal")

        local _BigMapNoSignalPanel = this.m_BigMapNoSignalImg:Find("Panel_BigMapNoSignal")
        this.m_BigMapNoSignalText = _BigMapNoSignalPanel:Find("Text_BigNoSignal")
        this.m_BigMapNoSignalText = this.m_BigMapNoSignalText:GetComponent(typeof( TMPro.TextMeshProUGUI ))
    end)
end

---@param iRT UnityEngine.RenderTexture
function MapMgr.SetMapRenderTexture(iRT)
    this.m_Camera.targetTexture = iRT
end

--region 大地圖相關功能

---1.點介面 2.點NPC按鈕 3.點NPCIcon 4.點任務目標 5.能量礦脈(右邊清單) 6.能量礦脈(中間地圖) 7.點區域 8.點區域Icon 9.點世界地圖
EClickType = {
    UI = 1,
    NPC = 2,
    NPCIcon = 3,
    Target = 4,
    Mine = 5,
    MineIcon = 6,
    Area = 7,
    AreaIcon = 8,
    WorldMap = 9,
}

--釋放不使用的物件池元件(先別釋放 太頻繁會造成堆積跟浪費)
function MapMgr.ReleasePoolObj(icount, itable, iPoolUnit)
    local _tableCount = table.Count(itable)
    local _count = icount
    if _tableCount > _count then
        for i = _count + 1, _tableCount  do
            itable[i].gameObject:SetActive(false)
            --iPoolUnit:Store(itable[i].gameObject)
            --itable[i] = nil
        end
    end
end

--檢查NPC Icon是否換成任務推演Icon
function MapMgr.CheckIconType(iMod, iEventID)
    local _GetIconFromMapIconSetting = false
    local _IconType = 0
    --檢查任務型角色，需要按照任務階段顯示Icon
    if iMod == EventNPCMode.Mission then
        --檢查任務ID，決定用哪個Icon
        local _MissionType = EventMgr:TryGetMissionID(iEventID, iMod)
        if _MissionType then
            local _MissionIconType = MapIconSetting.MissionTypeToMapIcon:GetCase(_MissionType)
            if _MissionIconType ~= EMapIconsType.Non then
                _GetIconFromMapIconSetting = true
                _IconType = _MissionIconType
            end
        else
            _GetIconFromMapIconSetting = true
            _IconType = MapIconSetting.NpcModeToMapIcon:GetCase(iMod)
        end
    else
        --依角色類型決定Icon
        local _MapIconType = MapIconSetting.NpcModeToMapIcon:GetCase(iMod)
        if _MapIconType ~= EMapIconsType.Non then
            --更新Icon
            _GetIconFromMapIconSetting = true
            _IconType = _MapIconType
        end
    end
    return _GetIconFromMapIconSetting, _IconType
end

--用EventID取NPC資料
function MapMgr.GetNPCByEventID(iSceneID, iEventID)
    local _NPCData = EventMapNpc.GetEventMapNPC(iSceneID)
    for k, _data in pairs(_NPCData) do
        if _data.m_EventID == iEventID then
            return _data
        end
    end
    return nil
end

--篩選任務目標
function MapMgr.CheckMissionTarget(iSceneID)
    local _MissionData = {}
    for k, _data in pairs(Tracing_Model.m_MissionList) do
        if _data.SceneID == iSceneID and _data.Finish == EventTipType.Undone then
            table.insert(_MissionData, _data)
        end
    end
    return _MissionData
end

function MapMgr.SetMapPointer(iPos, iType)
    SendProtocol_004._015(iPos, iType)
end

function MapMgr.GetMapPointer(iData)
    local _Msg = iData.m_Msg
    SendProtocol_013.SendChat(iData.m_Classify, _Msg)
    Map_Controller.ShowMark(iData)
end

---確認艙室效果標記數量有沒有達到該地圖需求數量
function MapMgr.CheckTimeMachineEffectFlag(iSceneID)
    local _FlagCount = PlayerData.GetTimeMachineEffect(2,false)
    if _FlagCount == 0 then
        return false
    else
        local _MineData = EnergyMineData.GetMapMineData(iSceneID)
        for k, v in pairs(_MineData) do
            return _FlagCount >= v.m_UnlockLevel
        end
    end
end

---確認地圖和礦脈永標是否都有取得
function MapMgr.CheckHaveFlag(iSceneID)
    local _HaveFlag = MapMgr.CheckWorldMapFlag(iSceneID)
    local _HaveMineFlag = MapMgr.CheckMineFlag(iSceneID)
    return _HaveFlag and _HaveMineFlag
end

---確認地圖永標是否有取得
function MapMgr.CheckWorldMapFlag(iSceneID)
    local _MapData = WorldMapData.GetMapDataByIdx(iSceneID)
    if _MapData == nil then return false end

    local _HaveFlag = true
    for k, v in pairs(_MapData.m_StaticFlags) do
        if v ~= 0 and not PlayerData.IsHaveStaticFlag(v) then
            _HaveFlag = false
            break
        end
    end
    return _HaveFlag
end

---確認礦脈永標是否有取得
function MapMgr.CheckMineFlag(iSceneID)
    local _MineData = EnergyMineData.GetMapMineData(iSceneID)
    if _MineData == nil then return false end

    local _HaveMineFlag = true
    for k, v in pairs(_MineData) do
        if v.m_ActiveFlag ~= 0 and not PlayerData.IsHaveStaticFlag(v.m_ActiveFlag) then
            _HaveMineFlag = false
            break
        end
    end

    return _HaveMineFlag
end

function MapMgr.CheckHaveMine(iSceneID)
    local _MineData = EnergyMineData.GetMapMineData(iSceneID)
    return table.Count(_MineData) ~= 0
end

function MapMgr.CalComponentHeight(iType, iGroup)
    local _Height = 0
    local _FixHeight = (iType == EClickType.NPC or iType == EClickType.Mine or iType == EClickType.AreaIcon) and 0 or 10
    for k, v in pairs(iGroup) do
        if v.gameObject.activeSelf then
            _Height = _Height + v.gameObject:GetComponent("RectTransform").sizeDelta.y
        end
    end
    return _Height + _FixHeight
end
--endregion

function MapMgr.EnableGlitch(iIgnoreImg)
    local _GlitchCtrl = nil
    -- 切換顯示小地圖的行為
    if this.m_CurrentUI == MiniMap_Controller.m_UIView then
        _GlitchCtrl = this.m_GlitchCtrl
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.CRT, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Block, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Jitter, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Digital, true)
        if not iIgnoreImg then
            if this.m_NoSignalImg and not this.m_NoSignalImg.gameObject.activeSelf then
                this.m_NoSignalImg.gameObject:SetActive(true)
            end
        end
        MiniMap_Controller.EnableGlitch()
    -- 切換顯示中地圖的行為
    elseif this.m_CurrentUI == Map_Controller.m_UIView then
        _GlitchCtrl = this.m_BigMapGlitchCtrl
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.CRT, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Block, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Jitter, true)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Digital, true)
        if not iIgnoreImg then
            if this.m_BigMapNoSignalImg and not this.m_BigMapNoSignalImg.gameObject.activeSelf then
                this.m_BigMapNoSignalImg.gameObject:SetActive(true)
            end
        end
        this.m_BigMapNoSignalText.text = TextData.Get(520310)
        Map_Controller.EnableGlitch()
    end
end

function MapMgr.DisableGlitch()
    local _GlitchCtrl = nil

    -- 切換顯示小地圖的行為
    if this.m_CurrentUI == MiniMap_Controller.m_UIView then
        _GlitchCtrl = this.m_GlitchCtrl
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.CRT, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Block, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Jitter, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Digital, false)

        if this.m_NoSignalImg and this.m_NoSignalImg.gameObject.activeSelf then
            this.m_NoSignalImg.gameObject:SetActive(false)
        end

        MiniMap_Controller.DisableGlitch()

    -- 切換顯示中地圖的行為
    elseif this.m_CurrentUI == Map_Controller.m_UIView then

        _GlitchCtrl = this.m_BigMapGlitchCtrl
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.CRT, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Block, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Jitter, false)
        _GlitchCtrl:EnableGlitch(GlitchCtrl.GlitchType.Digital, false)

        if this.m_BigMapNoSignalImg and this.m_BigMapNoSignalImg.gameObject.activeSelf then
            this.m_BigMapNoSignalImg.gameObject:SetActive(false)
        end

        Map_Controller.DisableGlitch()
    end
end
--endregion
function MapMgr.OnUnrequire()
    if this.m_MapRoot.gameObject~=nil then
        this.m_MapRoot.gameObject:Destroy()
    end
    return true
end


return MapMgr
