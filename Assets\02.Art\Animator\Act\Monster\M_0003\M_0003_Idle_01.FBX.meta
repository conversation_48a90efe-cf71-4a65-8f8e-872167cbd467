fileFormatVersion: 2
guid: 733125ecb044edf47985733f45d22303
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001 Head
    100002: Bip001 Spine1
    100004: Dummy004
    100006: Dummy005
    100008: Dummy008
    100010: Dummy009
    100012: Floor
    100014: Foot_01
    100016: Foot_01_1
    100018: Foot_02
    100020: Foot_02_01
    100022: Foot_03
    100024: Foot_03_01
    100026: //RootNode
    400000: Bip001 Head
    400002: Bip001 Spine1
    400004: Dummy004
    400006: Dummy005
    400008: Dummy008
    400010: Dummy009
    400012: Floor
    400014: Foot_01
    400016: Foot_01_1
    400018: Foot_02
    400020: Foot_02_01
    400022: Foot_03
    400024: Foot_03_01
    400026: //RootNode
    2100000: No Name
    2300000: Floor
    3300000: Floor
    4300000: Floor
    7400000: M_0003_Idle_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0003_Idle_01
      takeName: M_0003_Idle_01
      firstFrame: 0
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: Bip001 Spine1
        weight: 1
      - path: Bip001 Spine1/Bip001 Head
        weight: 1
      - path: Bip001 Spine1/Dummy004
        weight: 1
      - path: Bip001 Spine1/Dummy005
        weight: 1
      - path: Bip001 Spine1/Dummy008
        weight: 1
      - path: Bip001 Spine1/Dummy009
        weight: 1
      - path: Bip001 Spine1/Foot_01
        weight: 1
      - path: Bip001 Spine1/Foot_01/Foot_01_1
        weight: 1
      - path: Bip001 Spine1/Foot_02
        weight: 1
      - path: Bip001 Spine1/Foot_02/Foot_02_01
        weight: 1
      - path: Bip001 Spine1/Foot_03
        weight: 1
      - path: Bip001 Spine1/Foot_03/Foot_03_01
        weight: 1
      - path: Floor
        weight: 1
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 4311273aa8d47f34b8bc8ef7d6d42727,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
