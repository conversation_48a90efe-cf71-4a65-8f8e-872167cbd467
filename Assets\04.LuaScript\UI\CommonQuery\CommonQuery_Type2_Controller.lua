---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---詢問視窗 大分類 2 物品+數值變化型
---@class lua
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.25
CommonQuery_Type2_Controller = {}
local this = CommonQuery_Type2_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("CommonQuery_Type2_View", "CommonQuery_Type2_Controller", EUIOrderLayers.Peak)

---最大的物品顯示數量
local m_MaxItemAmount = 3
---最大的屬性描述數量
local m_MaxValueDescriptionAmount =3

---以下為自適應調整用
---標準prefab的預設頁面高度
local m_StandardHeight_Content
---標準prefab的預設文字行數
local m_PreAssignLine = 4


---箭頭使用的字串
local m_Arrow_String = " → " 

---高度參數
---從介面的header 到 ContentArea 的高度
local m_HeadToContent = 115.24

---TMP 使用Style

---Icon使用 數量不足 
local m_Icon_Lack = "RO_01"
---Icon使用 數量足夠 
local m_Icon_Enough = "WO_01"

---屬性區前值 文字 
local m_Pre_Word_Stye = "PO"

---屬性區 後值 文字 增益 以及 條件區 有達成
local m_Post_Word_Positive_Style = "G"

---屬性區 後值 文字 減益 以及 條件區 沒達成
local m_Post_Word_Negative_Style = "RO_01"

local m_IsConfirmBtnActive = true

---大份類2下的所有變體
ECommonQueryType2_SubType = 
{
    ---一般模式
    General =201,
    ---前後數值差異型
    ValueChange_PrePost = 202,
    ---屬性顯示數量變化(0~2)
    AttributeShowAmountChange = 203,
    ---Icon數量變化
    IconAmountChange = 204,
    ---無屬性顯示型
    NoAttributeDescription = 205,
    ---首X次不消耗的情況 在icon顯示免費
    IconWithFree = 206,
    ---不顯示數量(不可堆疊物品)
    IconWithNoAmount = 207,
    ---變化屬性不是數字而是字串
    AttributeValueIsString = 208,
    ---數量條件不足
    LessAmount = 209,
    ---顯示玩家持有量 屬性描述位置調整 (合成專用的介消 不只要顯示消耗貨幣跟文字 所以不能走 205 變體)
    PlayerItemAmount_SideAttributeTMP = 210,
}

---Icon 貨幣物品編號ID
local m_ECurrencyItemID = { 95009, 95010, 95011 }

---初始化
function CommonQuery_Type2_Controller.Init()

    ---面板物件 
    this.m_Obj_Panel = this.m_ViewRef.m_Dic_Trans:Get("&ContentPanel").gameObject
    ---TMP 標題
    this.m_TMP_TitleContent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TitleContent")
    this.m_Text_Content = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content")
    this.m_Btn_Cancel = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Cancel")
    this.m_Btn_Confirm = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm")
    this.m_Btn_Special = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Special")

    ---物品Icon 顯示區域
    ---顯示Icon時 父物件
    this.m_ItemIconArea = this.m_ViewRef.m_Dic_Trans:Get("&ItemIconArea")
    ---顯示Icon 且數量小於3000時的父物件 
    this.m_Less3000Set = this.m_ViewRef.m_Dic_Trans:Get("&Less3000Set")

    local _IconNamePre = "&ItemIcon"
    this.m_ItemIconTable = {}
    for i = 1, m_MaxItemAmount do
        this.m_ItemIconTable[i] = {}
        local _IconName = _IconNamePre .. i
        this.m_ItemIconTable[i].m_gameObject = this.m_ViewRef.m_Dic_Trans:Get(_IconName).gameObject
        this.m_ItemIconTable[i].m_ItemIcon = IconMgr.NewItemIcon(0,this.m_ItemIconTable[i].m_gameObject.transform, 110)
        this.m_ItemIconTable[i].m_ItemIcon.m_TMP_Count.text = ""
        this.m_ItemIconTable[i].m_ItemIcon:SetClickTwice( false ) 

        ---可能出現PetIcon 參數先開著
        this.m_ItemIconTable[i].m_PetIcon = IconMgr.NewPetIcon(0,this.m_ItemIconTable[i].m_gameObject.transform, 110)
        this.m_ItemIconTable[i].m_PetIcon.m_TMP_Count.text = ""
        this.m_ItemIconTable[i].m_PetIcon:SetClickTwice( false ) 


        ---萬一超過3000 需要使用 額外顯示的TMP 因此要將icon 設定到最上面
        this.m_ItemIconTable[i].m_ItemIcon.gameObject.transform:SetAsFirstSibling()
    end

    ---鎖定用鎖頭
    this.m_LockImageSet = this.m_ViewRef.m_Dic_Trans:Get("&LockImageSet")

    ---顯示Icon 且數量大於3000時的父物件
    this.m_More3000Set = this.m_ViewRef.m_Dic_Trans:Get("&More3000Set")

    local _IconName_NoValuePre = "&ItemIcon_NoValue"
    this.m_ItemIconTable_NoValue = {}
    for i = 1, m_MaxItemAmount do
        this.m_ItemIconTable_NoValue[i] = {}
        local _IconName = _IconName_NoValuePre .. i
        this.m_ItemIconTable_NoValue[i].m_gameObject = this.m_ViewRef.m_Dic_Trans:Get(_IconName).gameObject
        this.m_ItemIconTable_NoValue[i].m_TMP_Amount_BackUp = this.m_ItemIconTable_NoValue[i].m_gameObject.transform:Find("Img_BackGround/TMP_Amount_BackUp").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_ItemIconTable_NoValue[i].m_ItemIcon = IconMgr.NewItemIcon(0,this.m_ItemIconTable_NoValue[i].m_gameObject.transform, 60)
        this.m_ItemIconTable_NoValue[i].m_ItemIcon.transform.localPosition = Vector3(-185,0,0)
        this.m_ItemIconTable_NoValue[i].m_ItemIcon.m_TMP_Count.text = ""
        this.m_ItemIconTable_NoValue[i].m_ItemIcon:SetClickTwice( false ) 
        ---將icon 設定到最上面 (從左到右特別排列)
        this.m_ItemIconTable_NoValue[i].m_ItemIcon.gameObject.transform:SetAsFirstSibling()
    end

    ---顯示Image時 父物件 
    this.m_ItemImageArea = this.m_ViewRef.m_Dic_Trans:Get("&OnLyImageArea")
    this.m_Img_OnlyImage = this.m_ViewRef.m_Dic_Image:Get("&OnlyImage1")
    
    ---屬性變化描述區域    
    ---描述區域父物件
    this.m_GObj_ValueDescriptionArea = this.m_ViewRef.m_Dic_Trans:Get("&ValueDescriptionArea").gameObject   
    local _TMPNamePre = "&Description"
    this.m_DescriptionTable = {}
    for i = 1, m_MaxValueDescriptionAmount do
        this.m_DescriptionTable[i] = {}
        local _TMPName = _TMPNamePre .. i
        this.m_DescriptionTable[i].m_gameObject = this.m_ViewRef.m_Dic_Trans:Get(_TMPName).gameObject
        ---TMP 屬性/數值文字說明區
        this.m_DescriptionTable[i].m_TMP_Description = this.m_DescriptionTable[i].m_gameObject.transform:Find("TMP_ValueDescription").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        
        ---TMP 屬性/數值文字
        this.m_DescriptionTable[i].m_TMP_ValueDetail = this.m_DescriptionTable[i].m_gameObject.transform:Find("Img_NumberBackGround/TMP_ValueDetail").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
    end

    ---資源消耗區
    this.m_ConsumeTable = {}
    local _ConsumeUnitNamePre = "&Consume"
    for i = 1, m_MaxItemAmount do
        this.m_ConsumeTable[i] = {}
        local _IconName = _ConsumeUnitNamePre .. i
        this.m_ConsumeTable[i].m_gameObject = this.m_ViewRef.m_Dic_Trans:Get(_IconName).gameObject
        this.m_ConsumeTable[i].m_TMP_CousumeAmount = this.m_ConsumeTable[i].m_gameObject.transform:Find("Img_BackGround/TMP_Amount").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        this.m_ConsumeTable[i].m_Img_Resource = this.m_ConsumeTable[i].m_gameObject.transform:Find("Img_BackGround/Btn_ConsumeResource").gameObject:GetComponent(typeof(Image))
    end

    ---次數操作區
    ---加一次按鍵
    this.m_Btn_AddOne = this.m_ViewRef.m_Dic_Trans:Get("&Button_AddOne")
    Button.AddListener(this.m_Btn_AddOne, EventTriggerType.PointerDown,function() CommonQuery_Type2_Controller.ChangeTimes(true)end)
    Button.AddListener(this.m_Btn_AddOne, EventTriggerType.PointerUp,function() CommonQuery_Type2_Controller.StopPrintNumberChange()end)

    this.m_Btn_ReduceOne = this.m_ViewRef.m_Dic_Trans:Get("&Button_ReduceOne")
    Button.AddListener(this.m_Btn_ReduceOne, EventTriggerType.PointerDown,function() CommonQuery_Type2_Controller.ChangeTimes(false)end)
    Button.AddListener(this.m_Btn_ReduceOne, EventTriggerType.PointerUp,function() CommonQuery_Type2_Controller.StopPrintNumberChange()end)
    ---設成上限
    this.m_Btn_SetMax = this.m_ViewRef.m_Dic_Trans:Get("&Button_SetMax")
    Button.AddListener(this.m_Btn_SetMax, EventTriggerType.PointerClick,function() CommonQuery_Type2_Controller.SetToMaxTimes() end)
    ---設成下限
    this.m_Btn_SetMin = this.m_ViewRef.m_Dic_Trans:Get("&Button_SetMin")
    Button.AddListener(this.m_Btn_SetMin, EventTriggerType.PointerClick,function() CommonQuery_Type2_Controller.SetToMinTimes() end)
    ---點擊呼叫數字小鍵盤 &Button_SelectNum 
    this.m_Btn_SelectNum = this.m_ViewRef.m_Dic_Trans:Get("&Button_SelectNum")
    Button.AddListener(this.m_Btn_SelectNum, EventTriggerType.PointerClick,
        function() 
                            NumberPanel_Controller.OpenNumberPanel(
                            this.m_CurrentTimes,
                            this.m_MaxTime,
                            CommonQuery_Type2_Controller.SetToSpecificTimesByNumberPanel,
                            CommonQuery_Type2_Controller.SetToSpecificTimesByNumberPanel)

                            ---設定完畢後開啟NumberPanel
                            UIMgr.Open(NumberPanel_Controller)
                        end)
    ---顯示目前次數的TMP
    this.TMP_CurrentTime = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_CurrentTime")
    ---
    ---已達上限提示用TMP &TMP_UpLimit
    this.m_TMP_UpLimit = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_UpLimit")
    ---上下限由面板開啟時的帶入table 決定 此處先分別給 3000 跟 1
    ---上限
    this.m_MaxTime = 3000
    ---下限
    this.m_MinTime = 1
    ---目前次數
    this.m_CurrentTimes = 1

    this.TMP_CurrentTime.text = this.m_CurrentTimes
    ---次數變化時 要連動甚麼區域的修改
    ---次數變化會變動IconBox訊息
    this.m_LinkToIconBox = false
    ---次數變化會變動DescriptionBox訊息
    this.m_LinkToDescriptionBox = false
    ---次數變化會變動ConsumeBox訊息
    this.m_LinkToConsumeBox = false
    ---次數設定完成後 UI對應變化
    CommonQuery_Type2_Controller.PostTimeChange(true)

    ---條件需求區
    ---條件需求的的標題
    this.m_TMP_RequireTitle = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_RequireTitle")

    this.m_RequireUITable = {}
    local _ConsumeUnitNamePre = "&TMP_Require"
    for i = 1, m_MaxItemAmount do
        this.m_RequireUITable[i] = {}
        local _IconName = _ConsumeUnitNamePre .. i
        this.m_RequireUITable[i].m_TMP = this.m_ViewRef.m_Dic_TMPText:Get(_IconName)
    end

    ---prefab 排版 用4行去排版 計算原始高度
    m_StandardHeight_Content = m_PreAssignLine*this.m_Text_Content.fontSize + (m_PreAssignLine-1)*(this.m_Text_Content.fontSize/2)

    ---各區域排列父物件
    ---內文區 父物件
    this.m_TextBox = this.m_ViewRef.m_Dic_Trans:Get("&TextBox").gameObject
    ---Icon區 父物件 
    this.m_IconBox = this.m_ViewRef.m_Dic_Trans:Get("&IconBox").gameObject
    ---描述區 父物件 
    this.m_DescriptionBox = this.m_ViewRef.m_Dic_Trans:Get("&DescriptionBox").gameObject
    ---資源消耗區
    this.m_ConsumeBox = this.m_ViewRef.m_Dic_Trans:Get("&ConsumeBox").gameObject
    ---次數操作區
    this.m_TimesBox = this.m_ViewRef.m_Dic_Trans:Get("&TimesBox").gameObject
    ---條件需求區
    this.m_RequireBox = this.m_ViewRef.m_Dic_Trans:Get("&RequireBox").gameObject
    ---按鍵區
    this.m_LineBtnBox = this.m_ViewRef.m_Dic_Trans:Get("&LineBtnBox").gameObject
    ---內容區父物件 &ContentArea
    this.m_ContentArea = this.m_ViewRef.m_Dic_Trans:Get("&ContentArea")

end

---Update
function CommonQuery_Type2_Controller.Update()

end

function CommonQuery_Type2_Controller.Open()
    CommonQuery_Type2_Controller.Settinginformation()
    return true 
end

---設定中央詢問視窗 需要顯示的元件
function CommonQuery_Type2_Controller.Settinginformation()

    local _viewData = CommonQueryMgr.GetCurrentCommonQueryData()
    if not _viewData then
        D.LogError("中央詢問視窗沒資料!!")
        return
    end
    
    ---設定確認方法
    if _viewData.m_CallBack_Confirm ~= nil then
        CommonQuery_Type2_Controller.m_CallBack_Confirm = function()
            if(type(_viewData.m_CallBack_ConfirmArgs)=="table" and next(_viewData.m_CallBack_ConfirmArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Confirm,unpack(_viewData.m_CallBack_ConfirmArgs))
            else
                pcall(_viewData.m_CallBack_Confirm)
            end
            CommonQuery_Type2_Controller.DoConfirmBtn()
        end
    else
        CommonQuery_Type2_Controller.m_CallBack_Confirm = function()
            CommonQuery_Type2_Controller.DoConfirmBtn()
        end
    end

    ---設定取消方法
    if _viewData.m_CallBack_Cancel ~= nil then
        CommonQuery_Type2_Controller.m_CallBack_Cancel = function()
            if(type(_viewData.m_CallBack_CancelArgs)=="table" and next(_viewData.m_CallBack_CancelArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Cancel,unpack(_viewData.m_CallBack_CancelArgs))
            else
                pcall(_viewData.m_CallBack_Cancel)
            end
            CommonQuery_Type2_Controller.DoCancelBtn()
        end
    else
        CommonQuery_Type2_Controller.m_CallBack_Cancel = function()
            CommonQuery_Type2_Controller.DoCancelBtn()
        end
    end
    
    ---設定特殊方法
    if _viewData.m_CallBack_Special ~= nil then
        CommonQuery_Type2_Controller.m_CallBack_Special = function()
            if(type(_viewData.m_CallBack_SpecialArgs)=="table" and next(_viewData.m_CallBack_SpecialArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Special,unpack(_viewData.m_CallBack_SpecialArgs))
            else
                pcall(_viewData.m_CallBack_Special)
            end
            CommonQuery_Type2_Controller.DoSpecialBtn()
        end
    else
        CommonQuery_Type2_Controller.m_CallBack_Special = function()
            CommonQuery_Type2_Controller.DoSpecialBtn()
        end
    end
    
    ---設定標題 內文
    CommonQuery_Type2_Controller.RenewTitleAndContent(_viewData)

    ---如果有介消編號 但是沒有客製化table 表示是從SellMgr開啟
    ---在這邊生成簡易版本(只有iconArea的資訊的 table) 
    if _viewData.m_CostNum ~= 0 and _viewData.m_CustomData == nil then

        -- 取得介消資料
        local _SellResult = SellMgr.m_ConsumeData
        local _IconID = ""
        -- 資料空的做防呆
        --理論上應該要有資料
        if _SellResult ~= nil then
            -- 取得Icon 貨幣種類 1.遊戲幣 2.黃易幣 3.時空幣 else 物品ID
            if _SellResult.m_ConsumeType <= table.Count(m_ECurrencyItemID) then
                _IconID = m_ECurrencyItemID[_SellResult.m_ConsumeType]
            else 
                _IconID = _SellResult.m_ConsumeID
            end
        else
            D.Log("沒有客製化table 也沒有SellMgr.m_CustonData 無法做後續資料顯示處理")
        end

        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
        _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon,{_IconID},{_SellResult.m_ConsumeNum},{true},false)
        _viewData.m_CustomData = _Type2Data

    end
    

    ---類別2 UI賦予數值
    if _viewData.m_CustomData ~= nil then

        ---物品區資料顯示
        this.m_IconBox:SetActive( _viewData.m_CustomData.m_IconBoxData ~= nil)
        if _viewData.m_CustomData.m_IconBoxData ~= nil then

            this.m_More3000Set.gameObject:SetActive(false)
            this.m_Less3000Set.gameObject:SetActive(false)
            this.m_ItemImageArea.gameObject:SetActive(false)

            ---根據顯示狀態以及數量 有三種可能的顯示方式
            --- Icon 模式 且 數量小於 3000
            --- Icon 模式 且 數量大於 3000
            --- Image 模式
            if  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Icon and _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == false then
                this.m_Less3000Set.gameObject:SetActive(true)
                for i = 1, table.Count(this.m_ItemIconTable) do
                    if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~=nil and 
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~= nil and
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID ~= 0 then
                        
                            this.m_ItemIconTable[i].m_gameObject:SetActive(true)
                            this.m_ItemIconTable[i].m_ItemIcon:RefreshIcon(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)

                            local _TMPStyle 

                            if type(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount) == "number" and 
                            _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_ChekEnough  then
                                local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)
                                _TMPStyle = _BagItemAmount >= _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount and m_Icon_Enough or m_Icon_Lack
                            else
                                ---不是數字 或者不需要比大小 默認用白色
                                _TMPStyle = m_Icon_Enough
                            end

                            this.m_ItemIconTable[i].m_ItemIcon:SetCount(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount,_TMPStyle)

                            this.m_ItemIconTable[i].m_PetIcon.gameObject:SetActive(false)
                            this.m_ItemIconTable[i].m_ItemIcon.gameObject:SetActive(true)
                    else
                            this.m_ItemIconTable[i].m_gameObject:SetActive(false)
                    end
                end 

                this.m_LockImageSet.gameObject:SetActive(_viewData.m_CustomData.m_IconBoxData.m_ShowLock)

            elseif  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Icon and _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == true then
                this.m_More3000Set.gameObject:SetActive(true)
                for i = 1, table.Count(this.m_ItemIconTable_NoValue) do
                    if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~=nil and 
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~= nil and
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID ~= 0 then
                        
                            this.m_ItemIconTable_NoValue[i].m_gameObject:SetActive(true)
                            this.m_ItemIconTable_NoValue[i].m_ItemIcon:RefreshIcon(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)

                            this.m_ItemIconTable_NoValue[i].m_ItemIcon:SetCount("")
                            
                            local _TMPStyle 
                            if type(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount) == "number" and 
                            _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_ChekEnough then
                                local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)
                                _TMPStyle = _BagItemAmount >= _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount and m_Icon_Enough or m_Icon_Lack
                            else
                                ---不是數字 或者不需要比大小 默認用白色
                                _TMPStyle = m_Icon_Enough
                            end

                            local _FinaleStr = GString.StringWithStyle(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount,_TMPStyle)

                            this.m_ItemIconTable_NoValue[i].m_TMP_Amount_BackUp.text = _FinaleStr

                            this.m_ItemIconTable[i].m_PetIcon.gameObject:SetActive(false)
                            this.m_ItemIconTable[i].m_ItemIcon.gameObject:SetActive(true)
                    else
                            this.m_ItemIconTable_NoValue[i].m_gameObject:SetActive(false)
                    end
                end 
            elseif  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Image then
                this.m_ItemImageArea.gameObject:SetActive(true)
                SpriteMgr.Load(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[1],this.m_Img_OnlyImage)
            elseif _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.PetIcon then
                this.m_Less3000Set.gameObject:SetActive(true)
                for i = 1, table.Count(this.m_ItemIconTable) do
                    if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~=nil and 
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~= nil and
                        _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID ~= 0 then
                        
                            this.m_ItemIconTable[i].m_gameObject:SetActive(true)
                            this.m_ItemIconTable[i].m_PetIcon:RefreshIcon(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)

                            ---寵物數量 Style一律先用 m_Icon_Enough
                            local _TMPStyle = m_Icon_Enough

                            this.m_ItemIconTable[i].m_ItemIcon:SetCount(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount,_TMPStyle)

                            this.m_ItemIconTable[i].m_PetIcon.gameObject:SetActive(true)
                            this.m_ItemIconTable[i].m_ItemIcon.gameObject:SetActive(false)
                    else
                            this.m_ItemIconTable[i].m_gameObject:SetActive(false)
                    end
                end 

            end
        end
        

        ---屬性說明區
        this.m_DescriptionBox:SetActive( _viewData.m_CustomData.m_DescriptionData ~= nil)
        if _viewData.m_CustomData.m_DescriptionData ~= nil then

            ---數值說明設定
            for i = 1, table.Count(this.m_DescriptionTable) do

                local _NoDescriptionArea = _viewData.m_CustomData.m_DescriptionData.m_Attribute[1] == nil or _viewData.m_CustomData.m_DescriptionData.m_Attribute[1] ==""

                if _NoDescriptionArea then
                    ---第一條就沒有 表示不需要數值說明
                    this.m_GObj_ValueDescriptionArea:SetActive(false)
                    break
                else
                    this.m_GObj_ValueDescriptionArea:SetActive(true)
                end

                ---檢查對應的table欄位有沒有填入訊息
                if _viewData.m_CustomData.m_DescriptionData.m_Attribute[i] ~=nil and _viewData.m_CustomData.m_DescriptionData.m_Attribute[i] ~=""  then
                    this.m_DescriptionTable[i].m_gameObject:SetActive(true)
                    this.m_DescriptionTable[i].m_TMP_Description.text = _viewData.m_CustomData.m_DescriptionData.m_Attribute[i]

                    ---先判斷是否有後值 決定前值要不要加箭頭
                    local _NeedPostValue = _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] ~= "" and  _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] ~= nil
                
                    local _FinalString = _viewData.m_CustomData.m_DescriptionData.m_PreDescription[i]
                
                    if _viewData.m_CustomData.m_DescriptionData.m_AddPercentTable[i] then
                        _FinalString = _FinalString .."%"
                    end

                    if _NeedPostValue then
                        _FinalString = _FinalString .. m_Arrow_String
                    end
                
                    _FinalString = GString.StringWithStyle(_FinalString,m_Pre_Word_Stye)

                    if type(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]) == "number" then
                        ---如果前值 後值都是數字要比大小 判斷對應字體
                        if type(_viewData.m_CustomData.m_DescriptionData.m_PreDescription[i]) == "number" and type(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]) == "number"  then

                            local _PostString = _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]

                            if _viewData.m_CustomData.m_DescriptionData.m_AddPercentTable[i] then
                                _PostString = _PostString .. "%"
                            end

                            if _viewData.m_CustomData.m_DescriptionData.m_PreDescription[i] > _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]  then
                                _FinalString = _FinalString .. GString.StringWithStyle(_PostString,m_Icon_Lack)
                            else
                                _FinalString = _FinalString .. GString.StringWithStyle(_PostString,m_Post_Word_Positive_Style)
                            end
                        else
                            local _PostTMPStyle = _viewData.m_CustomData.m_DescriptionData.m_PostBetterTable[i] and m_Post_Word_Positive_Style or m_Post_Word_Negative_Style
                            _FinalString = _FinalString .. GString.StringWithStyle(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i],_PostTMPStyle)
                        end
                    else
                        if _NeedPostValue then
                            local _PostTMPStyle = _viewData.m_CustomData.m_DescriptionData.m_PostBetterTable[i] and m_Post_Word_Positive_Style or m_Post_Word_Negative_Style
                            
                            ---判斷文字是不是要用數字類別的style 再判斷後值是否比前值大
                            if _viewData.m_CustomData.m_DescriptionData.m_UseNumberStyleTable[i] then
                                _PostTMPStyle = _viewData.m_CustomData.m_DescriptionData.m_PostBetterTable[i] and m_Post_Word_Positive_Style or m_Icon_Lack
                            else
                                _PostTMPStyle = _viewData.m_CustomData.m_DescriptionData.m_PostBetterTable[i] and m_Post_Word_Positive_Style or m_Post_Word_Negative_Style
                            end
                            
                            
                            _FinalString = _FinalString .. GString.StringWithStyle(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i],_PostTMPStyle)
                        end
                    end

                    this.m_DescriptionTable[i].m_TMP_ValueDetail.text = _FinalString
                else
                    this.m_DescriptionTable[i].m_gameObject:SetActive(false)
                end

            end
        end

        ---資源消耗區
        this.m_ConsumeBox:SetActive( _viewData.m_CustomData.m_ConsumeData ~= nil)
        if _viewData.m_CustomData.m_ConsumeData ~= nil then
            for i = 1, table.Count(this.m_ConsumeTable) do
                if _viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i] ~=nil and 
                    _viewData.m_CustomData.m_ConsumeData.m_ConsumeAmountTable[i] ~= nil then
                    
                        this.m_ConsumeTable[i].m_gameObject:SetActive(true)

                        ---根據填入的道具ID 取得圖片 
                        local _SpriteName =""
	                    local _ItemData = ItemData.GetItemDataByIdx(_viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i])
	                    if _ItemData then
		                    _SpriteName = _ItemData:GetItemTextureName()
	                    end

                        SpriteMgr.Load(_SpriteName,this.m_ConsumeTable[i].m_Img_Resource)
                        
                        ---根據玩家擁有的數量判斷是否足夠 使用不同TMP style
                        local _TMPStyle 
                        ---要消耗的資源可能是貨幣 也可能是物品 需要分開判斷
                        local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i])

                        _TMPStyle = _BagItemAmount >= _viewData.m_CustomData.m_ConsumeData.m_ConsumeAmountTable[i] and m_Icon_Enough or m_Icon_Lack

                        local _FinalStr = GString.StringWithStyle(_viewData.m_CustomData.m_ConsumeData.m_ConsumeAmountTable[i],_TMPStyle)

                        this.m_ConsumeTable[i].m_TMP_CousumeAmount.text = _FinalStr
                else
                        this.m_ConsumeTable[i].m_gameObject:SetActive(false)
                end
            end 
        end
        
        ---次數操作區
        this.m_TimesBox:SetActive( _viewData.m_CustomData.m_TimesData ~= nil)
        if _viewData.m_CustomData.m_TimesData ~= nil then
            ---設定最大/最小值 並將當前值設定成最小值
            this.m_MaxTime = _viewData.m_CustomData.m_TimesData.m_Max
            this.m_MinTime = _viewData.m_CustomData.m_TimesData.m_Min
            this.m_CurrentTimes = this.m_MinTime
            this.TMP_CurrentTime.text = this.m_CurrentTimes
            
            ---開啟次數刷新區時 也要判斷是否達到上限來顯示 達到上限提示文字
            ---大部分狀況都不會到上限 特例 最大值等於最小值時會達到上限
            local _ShowHint = (this.m_MaxTime == this.m_MinTime) and true or  false
            CommonQuery_Type2_Controller.ShowTouchUpLimit(_ShowHint)
        end

        ---條件字串區
        this.m_RequireBox:SetActive(_viewData.m_CustomData.m_RequireData ~= nil)
        if _viewData.m_CustomData.m_RequireData ~= nil then
            ---條件字串區 標題文字
            this.m_TMP_RequireTitle.text = _viewData.m_CustomData.m_RequireData.m_Title

            ---各個條件文字說明 並根據是否已完成條件 切換顯示TMP Style
            for i = 1, table.Count(this.m_RequireUITable) do
                if _viewData.m_CustomData.m_RequireData.m_RequiresTable[i]~= nil then
                    this.m_RequireUITable[i].m_TMP.gameObject:SetActive(true)
                    
                    local _TMPStyle = _viewData.m_CustomData.m_RequireData.m_RequiresTable[i].m_Pass and m_Post_Word_Positive_Style or m_Post_Word_Negative_Style

                    local _FinalString = GString.StringWithStyle(_viewData.m_CustomData.m_RequireData.m_RequiresTable[i].m_Description, _TMPStyle)

                    this.m_RequireUITable[i].m_TMP.text = _FinalString
                else
                    this.m_RequireUITable[i].m_TMP.gameObject:SetActive(false)
                end
            end
        end

    end
      
    if _viewData.m_Str_BTNCancel ~= 0 then --取消按鈕
        Button.SetText(this.m_Btn_Cancel,TextData.Get(_viewData.m_Str_BTNCancel))
        Button.ClearListener(this.m_Btn_Cancel.gameObject)
        Button.AddListener(this.m_Btn_Cancel, EventTriggerType.PointerClick,CommonQuery_Type2_Controller.m_CallBack_Cancel)
        Button.SetAudioID(this.m_Btn_Cancel, _viewData.m_Sound_BTNCancel)
        
        this.m_Btn_Cancel.gameObject:SetActive(true)
    else
        this.m_Btn_Cancel.gameObject:SetActive(false)
    end
    
    if _viewData.m_Str_BTNConfirm ~= 0 then --確認按鈕
        Button.SetText(this.m_Btn_Confirm,TextData.Get(_viewData.m_Str_BTNConfirm))
        Button.ClearListener(this.m_Btn_Confirm.gameObject)
        Button.AddListener(this.m_Btn_Confirm, EventTriggerType.PointerClick,CommonQuery_Type2_Controller.m_CallBack_Confirm)
        Button.SetAudioID(this.m_Btn_Confirm, _viewData.m_Sound_BTNConfirm)

        this.m_Btn_Confirm.gameObject:SetActive(true)
    else
        this.m_Btn_Confirm.gameObject:SetActive(false)
    end
    
    if _viewData.m_Str_BTNSpecial ~= 0 then --特殊按鈕
        Button.SetText(this.m_Btn_Special,TextData.Get(_viewData.m_Str_BTNSpecial))
        Button.ClearListener(this.m_Btn_Special.gameObject)
        Button.AddListener(this.m_Btn_Special, EventTriggerType.PointerClick,CommonQuery_Type2_Controller.m_CallBack_Special)
        Button.SetAudioID(this.m_Btn_Special, _viewData.m_Sound_BTNSpecial)

        this.m_Btn_Special.gameObject:SetActive(true)
    else
        this.m_Btn_Special.gameObject:SetActive(false)
    end

    m_IsConfirmBtnActive = _viewData.m_CustomData.m_IsConfirmBtnActive
    ---按鍵status 設定區 (Disable 或者 Enable )
    if  _viewData.m_CustomData.m_IsConfirmBtnActive == false  then
       Button.SetDisable(this.m_Btn_Confirm,false)
    else
        Button.SetEnable(this.m_Btn_Confirm)
    end

    ---自適應 UI面板高度調整
    local _AdjustUIPosAndHeight = function()
        local _RectTransform =this.m_Obj_Panel:GetComponent("RectTransform")
       
        ---文字區域 計算高度
        local _RectTransform_Text_Content = this.m_Text_Content.gameObject:GetComponent("RectTransform")
        local _RectTransform_TextBox = this.m_TextBox:GetComponent("RectTransform")

        _RectTransform_TextBox.sizeDelta =  _RectTransform_Text_Content.sizeDelta

       
        ---Icon區域 計算高度
        local _RectTransform_IconBox = this.m_IconBox:GetComponent("RectTransform")
        ---根據顯示方式的不同 會需要設定不同的高度
        if this.m_IconBox.activeSelf == false then
            _RectTransform_IconBox.sizeDelta = Vector2(_RectTransform_IconBox.sizeDelta.x,0)
        elseif  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Icon then
            if _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == false then
                local _RectTransform_Less3000Set = this.m_Less3000Set.gameObject:GetComponent("RectTransform")
                _RectTransform_IconBox.sizeDelta = _RectTransform_Less3000Set.sizeDelta
            elseif _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == true then
                local _RectTransform_More3000Set = this.m_More3000Set.gameObject:GetComponent("RectTransform")
                ---因為排版需求 必須額外提供給Icon區的高度
                local _AdditionDeltaY = 17.23
                local _NewSize = _RectTransform_More3000Set.sizeDelta                
                _NewSize.y = _NewSize.y + _AdditionDeltaY
                _RectTransform_More3000Set.sizeDelta = _NewSize
                
                _RectTransform_IconBox.sizeDelta = _RectTransform_More3000Set.sizeDelta
            end
        elseif  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Image then
            local _RectTransform_OnlyImageSet = this.m_ItemImageArea.gameObject:GetComponent("RectTransform")
            _RectTransform_IconBox.sizeDelta = _RectTransform_OnlyImageSet.sizeDelta
        end

        ---屬性描述區域 
        local _RectTransform_DescriptionBox = this.m_DescriptionBox:GetComponent("RectTransform")
        local _RectTransform_ValueDescriptionArea = this.m_GObj_ValueDescriptionArea:GetComponent("RectTransform")

        if this.m_GObj_ValueDescriptionArea.activeSelf then
            _RectTransform_DescriptionBox.sizeDelta = _RectTransform_ValueDescriptionArea.sizeDelta
        else
            _RectTransform_DescriptionBox.sizeDelta = Vector2(_RectTransform_ValueDescriptionArea.sizeDelta.x,0)
        end

        ---資源消耗區
        ---資源消耗區 顯示3個消耗時 整個區域的高度
        local _Max_Height_ConsumeBox = 266.76
        ---資源消耗區 單一個消耗UI 高度
        local _UnitConsume_H = 50
        ---資源消耗區 消耗UI間的間隔
        local _UnitConsume_Space = 17
        local _RectTransfor_ConsumeBox = this.m_ConsumeBox:GetComponent("RectTransform")
        if this.m_ConsumeBox.activeSelf then
            local _LastFillIndex = 0
            ---判斷缺幾行 扣除 缺少行數 * (高度+間隔)
            for i = 1, table.Count(_viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable) do
                if _viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i] == nil then
                    break
                else
                    _LastFillIndex = i
                end
            end
            local _LackLayer = table.Count(this.m_ConsumeTable) - _LastFillIndex
            local _Final_Height = _Max_Height_ConsumeBox - _LackLayer*(_UnitConsume_H+_UnitConsume_Space) 
            _RectTransfor_ConsumeBox.sizeDelta = Vector2(_RectTransfor_ConsumeBox.sizeDelta.x,_Final_Height)
        end

        ---次數操作區 不會有缺項問題 所以不需要調整

        ---條件需求區
        ---條件需求區 顯示3個消耗時 整個區域的高度
        local _Max_Height_RequireBox = 215.8
        ---條件需求區 單一個消耗UI 高度
        local _UnitRequire_H = 28
        ---條件需求區 消耗UI間的間隔
        local _UnitRequire_Space = 15
        local _RectTransfor_ConsumeBox = this.m_RequireBox:GetComponent("RectTransform")
        if this.m_RequireBox.activeSelf then
            local _LastFillIndex = 0
            ---判斷缺幾行 扣除 缺少行數 * (高度+間隔)
            for i = 1, table.Count(_viewData.m_CustomData.m_RequireData.m_RequiresTable) do
                if _viewData.m_CustomData.m_RequireData.m_RequiresTable[i] == nil or 
                _viewData.m_CustomData.m_RequireData.m_RequiresTable[i] == ""
                 then
                    break
                else
                    _LastFillIndex = i
                end
            end 
            local _LackLayer = table.Count(this.m_RequireUITable) - _LastFillIndex
            local _Final_Height = _Max_Height_RequireBox - _LackLayer*(_UnitRequire_H+_UnitRequire_Space) 

            ---因為有ContentSizeFFilter的調整  所以要強制刷新排列
            local _RequireDetail = this.m_RequireBox.transform:Find("RequireDetail")
            LayoutRebuilder.ForceRebuildLayoutImmediate(_RequireDetail)
            _RectTransfor_ConsumeBox.sizeDelta = Vector2(_RectTransfor_ConsumeBox.sizeDelta.x,_Final_Height)
        end

        ---調整最下面按鍵區域高度
        local _RectTransfor_LineBtnBox =  this.m_LineBtnBox:GetComponent("RectTransform")
        local _BtnBoxContactTimeBox_Height = 154.6144
        local _BtnBoxContactOtherBox_Height = 182.6144
        ---次數操作區域如果直接跟按鍵區域接觸 要使用較低的按鍵區域高度 其他狀況維持不變
        ---按照美術排版 次數操作區域 下面只會有 條件需求區   所以用 次數操作區開啟中 且 條件需求區關閉中 來做判斷
        if this.m_TimesBox.activeSelf and  this.m_RequireBox.activeSelf == false then
            _RectTransfor_LineBtnBox.sizeDelta = Vector2(_RectTransfor_LineBtnBox.sizeDelta.x,_BtnBoxContactTimeBox_Height)
        else
            _RectTransfor_LineBtnBox.sizeDelta = Vector2(_RectTransfor_LineBtnBox.sizeDelta.x,_BtnBoxContactOtherBox_Height)
        end
       

        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_ContentArea)
        ---內文區域 高度調整
        local _RectTransform_ContentArea = this.m_ContentArea:GetComponent("RectTransform")
        _RectTransform.sizeDelta = Vector2(_RectTransform.sizeDelta.x ,_RectTransform_ContentArea.sizeDelta.y + m_HeadToContent)

        ---調整位置 高度置中
        local _ScreenResolution = SettingMgr.GetScreenResolution()

        local  _HeightFixValue = 2.6
        local _NewPosY = _RectTransform.sizeDelta.y/2 - _ScreenResolution.y/2 + _HeightFixValue
        this.m_Obj_Panel.transform.localPosition = Vector2(this.m_Obj_Panel.transform.localPosition.x,_NewPosY)

    end

    ---推遲0.05秒等相關UI參數才會刷新 才能正確設定位置
    LeanTween.delayedCall(0.05, System.Action(
        function()
            _AdjustUIPosAndHeight()
        end)
    )
    
end

---預設 點擊確定按鈕
function CommonQuery_Type2_Controller.DoConfirmBtn()
    D.Log("CommonQuery_Type2_Controller Confirm Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊取消按鈕
function CommonQuery_Type2_Controller.DoCancelBtn()
    D.Log("CommonQuery_Type2_Controller Cancel Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊特殊按鈕
function CommonQuery_Type2_Controller.DoSpecialBtn()
    D.Log("CommonQuery_Type2_Controller Special Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---判斷是不是要用 最基本的介面消費(變體205)
function CommonQuery_Type2_Controller.IsUsingType205ToPay(iPageID)
    local _IsUsingType205ToPay = false

    if iPageID == ECommonQueryType2_SubType.NoAttributeDescription then
        return true
    end

    return _IsUsingType205ToPay
end


---次數操作區相關
---修改次數
---@param iIsAdding bool 是增加或者減少
function CommonQuery_Type2_Controller.ChangeTimes(iIsAdding)
    --如果確定按鈕不能按就不能加減次數
    if m_IsConfirmBtnActive == false then
        return
    end
   
    ---在長按的過程中 這次是第幾次加數值
    local _AddStep = 1
    ---每10次一個區間 添加每次典籍的增加量
    local _StepGroup = 10
    ---每個區間增加的數量
    local _AddAmountTable = {1,10,100,1000}
    ---隔多久時間 加/減數值
    local _Period = 0.5

    this.NumberValueCoroutine = nil
    this.NumberValueCoroutine = coroutine.start(function()

        local _TouchLimitAmount = false
        
        while _TouchLimitAmount == false  do

            local _CurrentAddGroup = (Mathf.Floor(_AddStep/_StepGroup) + 1 < 5) and Mathf.Floor(_AddStep/_StepGroup) + 1 or 4
            local _AddAmount = _AddAmountTable[_CurrentAddGroup]
            
            if iIsAdding then
                this.m_CurrentTimes = this.m_CurrentTimes + _AddAmount
                _AddStep = _AddStep + 1
            else
                this.m_CurrentTimes = this.m_CurrentTimes - _AddAmount
                _AddStep = _AddStep + 1
            end

            if this.m_CurrentTimes >= this.m_MaxTime then
                _TouchLimitAmount = true
                this.m_CurrentTimes = this.m_MaxTime
            elseif this.m_CurrentTimes <= this.m_MinTime then
                _TouchLimitAmount = true
                this.m_CurrentTimes = this.m_MinTime
            end

            CommonQuery_Type2_Controller.PostTimeChange()
            coroutine.wait(_Period)

        end

        coroutine.stop()
    end)

end

---數字逐漸變化中止
function CommonQuery_Type2_Controller.StopPrintNumberChange()
    if  this.NumberValueCoroutine ~= nil then
        coroutine.stop(this.NumberValueCoroutine)
        this.NumberValueCoroutine = nil
    end
end

---設定成最大值
function CommonQuery_Type2_Controller.SetToMaxTimes()
    --如果確定按鈕不能按就不能加減次數
    if m_IsConfirmBtnActive == false then
        return
    end

    this.m_CurrentTimes = this.m_MaxTime
    CommonQuery_Type2_Controller.PostTimeChange()
end

---設定成最小值
function CommonQuery_Type2_Controller.SetToMinTimes()
    --如果確定按鈕不能按就不能加減次數
    if m_IsConfirmBtnActive == false then
        return
    end
    
    this.m_CurrentTimes = this.m_MinTime
    CommonQuery_Type2_Controller.PostTimeChange()
end

---設定成特定值
---@param iTime number 要設定成幾次
function CommonQuery_Type2_Controller.SetToSpecificTimesByNumberPanel(iTime)
    
    if iTime ~= nil then
        this.m_CurrentTimes = iTime
    end
    
    if this.m_CurrentTimes >= this.m_MaxTime then
        this.m_CurrentTimes = this.m_MaxTime
    elseif this.m_CurrentTimes <= this.m_MinTime then
        this.m_CurrentTimes = this.m_MinTime
    end

    ---更新層級到Peak最上層
    UIMgr.UpdateUIOrderByUIController(CommonQuery_Type2_Controller)
    
    CommonQuery_Type2_Controller.PostTimeChange()
end

---次數修改後 UI不需刷新對應參數
function CommonQuery_Type2_Controller.PostTimeChange(iIsInitial)
    
    this.TMP_CurrentTime.text = this.m_CurrentTimes
    CommonQuery_Type2_Controller.ShowTouchUpLimit(this.m_CurrentTimes >= this.m_MaxTime)

    local _viewData = CommonQueryMgr.GetCurrentCommonQueryData()

    if _viewData.m_CustomData == nil or _viewData.m_CustomData.m_TimesData == nil or iIsInitial == true then
        return
    end

    ---刷新Icon區
    if _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Icon].m_TimeImpact and _viewData.m_CustomData.m_IconBoxData then
        
        if  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Icon and _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == false then
            for i = 1, table.Count(this.m_ItemIconTable) do
                if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~=nil and 
                    _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~= nil and
                    _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID ~= 0 then
                    
                        local _TMPStyle 
                        if type(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount) == "number" then

                            local _PostTimeAmount = _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount + 
                            _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Icon].m_SinleTimeTable[i]*(this.m_CurrentTimes-1) 

                            ---判斷是否需要檢查背包中數量 因為icon區資訊 可能是消耗需求物品 也可能是 預期獲得物品
                            if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_ChekEnough  then
                                local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)
                                _TMPStyle = _BagItemAmount >= _PostTimeAmount and m_Icon_Enough or m_Icon_Lack
                            else
                                _TMPStyle = m_Icon_Enough
                            end

                            this.m_ItemIconTable[i].m_ItemIcon:SetCount(_PostTimeAmount,_TMPStyle)
                        
                        else
                            ---不是數字無法比大小 默認用白色
                            _TMPStyle = m_Icon_Enough
                            this.m_ItemIconTable[i].m_ItemIcon:SetCount(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount,_TMPStyle)
                        end
                end
            end 

        elseif  _viewData.m_CustomData.m_IconBoxData.m_DataType == EIconBoxDataType.Icon and _viewData.m_CustomData.m_IconBoxData.m_IsOver3000 == true then
            for i = 1, table.Count(this.m_ItemIconTable_NoValue) do
                if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~=nil and 
                    _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i] ~= nil and
                    _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID ~= 0 then
                    
                        local _TMPStyle 
                        if type(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount) == "number" then
                            local _PostTimeAmount = _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount + 
                            _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Icon].m_SinleTimeTable[i]*(this.m_CurrentTimes-1) 

                            ---判斷是否需要檢查背包中數量 因為icon區資訊 可能是消耗需求物品 也可能是 預期獲得物品
                            if _viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_ChekEnough   then
                                local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconID)
                                _TMPStyle = _BagItemAmount >= _PostTimeAmount and m_Icon_Enough or m_Icon_Lack
                            else
                                _TMPStyle = m_Icon_Enough
                            end
                            local _FinaleStr = GString.StringWithStyle(_PostTimeAmount,_TMPStyle)
                            this.m_ItemIconTable_NoValue[i].m_TMP_Amount_BackUp.text = _FinaleStr
                        else
                            ---不是數字無法比大小 默認用白色
                            _TMPStyle = m_Icon_Enough

                            local _FinaleStr = GString.StringWithStyle(_viewData.m_CustomData.m_IconBoxData.m_IconDetail[i].m_IconAmount,_TMPStyle)
                            this.m_ItemIconTable_NoValue[i].m_TMP_Amount_BackUp.text = _FinaleStr
                        end
                end
            end
        end
    end

    ---刷新屬性區
    if _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Description].m_TimeImpact and _viewData.m_CustomData.m_DescriptionData then

        local _IsConfirmBtnOK = true

        ---數值說明設定
        for i = 1, table.Count(this.m_DescriptionTable) do

            ---檢查對應的table欄位有沒有填入訊息
            if _viewData.m_CustomData.m_DescriptionData.m_Attribute[i] ~=nil and _viewData.m_CustomData.m_DescriptionData.m_Attribute[i] ~=""  then
               
                ---先判斷是否有後值 決定前值要不要加箭頭
                local _NeedPostValue = _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] ~= "" and  _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] ~= nil
            
                local _FinalString = _viewData.m_CustomData.m_DescriptionData.m_PreDescription[i]
            
                if _viewData.m_CustomData.m_DescriptionData.m_AddPercentTable[i] then
                    _FinalString = _FinalString .."%"
                end

                if _NeedPostValue then
                    _FinalString = _FinalString .. m_Arrow_String
                end
            
                _FinalString = GString.StringWithStyle(_FinalString,m_Pre_Word_Stye)

                if type(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]) == "number" then
                    ---如果前值 後值都是數字要比大小 判斷對應字體
                    if type(_viewData.m_CustomData.m_DescriptionData.m_PreDescription[i]) == "number" and type(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]) == "number"  then

                        local _PoseTimeValue = 0
                        -- 當每升一次不是固定數值時
                        if(_viewData.m_CustomData.m_IsNumberNotFix) then
                            _PoseTimeValue = 
                            _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Description].m_SinleTimeTable[i][this.m_CurrentTimes]
                        else
                            _PoseTimeValue = _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] +
                            _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Description].m_SinleTimeTable[i]*(this.m_CurrentTimes-1)
                        end

                        -- 變負數的話 確認按鈕要變灰色
                        if (_PoseTimeValue == nil) or (_PoseTimeValue < 0) then
                            _PoseTimeValue = 0
                            _IsConfirmBtnOK = false
                        end

                        local _PostString = _PoseTimeValue

                        if _viewData.m_CustomData.m_DescriptionData.m_AddPercentTable[i] then
                            _PostString = _PostString .. "%"
                        end

                        if _viewData.m_CustomData.m_DescriptionData.m_PreDescription[i] > _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i]  then
                            _FinalString = _FinalString .. GString.StringWithStyle(_PostString,m_Icon_Lack)
                        else
                            _FinalString = _FinalString .. GString.StringWithStyle(_PostString,m_Post_Word_Positive_Style)
                        end
                    else
                        local _PoseTimeValue = 0
                        -- 當每升一次不是固定數值時
                        if(_viewData.m_CustomData.m_IsNumberNotFix) then
                            _PoseTimeValue = _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Description].m_SinleTimeTable[i][this.m_CurrentTimes]
                        else
                            _PoseTimeValue = _viewData.m_CustomData.m_DescriptionData.m_PostDescription[i] +
                            _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Description].m_SinleTimeTable[i]*(this.m_CurrentTimes-1) 
                        end

                        _FinalString = _FinalString .. GString.StringWithStyle(_PoseTimeValue,m_Post_Word_Positive_Style)
                    end
                else
                    if _NeedPostValue then
                        _FinalString = _FinalString .. GString.StringWithStyle(_viewData.m_CustomData.m_DescriptionData.m_PostDescription[i],m_Post_Word_Positive_Style)
                    end
                end

                this.m_DescriptionTable[i].m_TMP_ValueDetail.text = _FinalString
            else
                this.m_DescriptionTable[i].m_gameObject:SetActive(false)
            end

        end

        if(_IsConfirmBtnOK) then
            Button.SetEnable(this.m_Btn_Confirm)
        else
            Button.SetDisable(this.m_Btn_Confirm,false)
        end
        
    end

    ---刷新消耗區
    if _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Consume].m_TimeImpact and _viewData.m_CustomData.m_ConsumeData then
        
        for i = 1, table.Count(this.m_ConsumeTable) do
            if _viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i] ~=nil and 
                _viewData.m_CustomData.m_ConsumeData.m_ConsumeAmountTable[i] ~= nil then

                    local _PostTimeAmount = _viewData.m_CustomData.m_ConsumeData.m_ConsumeAmountTable[i] + 
                    _viewData.m_CustomData.m_TimesData.m_LinkInfo[ETimeImpactType.Consume].m_SinleTimeTable[i]*(this.m_CurrentTimes-1) 

                    local _TMPStyle 
                    local _BagItemAmount = CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(_viewData.m_CustomData.m_ConsumeData.m_ConsumeIDTable[i])
                    _TMPStyle = _BagItemAmount >= _PostTimeAmount and m_Icon_Enough or m_Icon_Lack

                    local _FinalStr = GString.StringWithStyle(_PostTimeAmount,_TMPStyle)

                    this.m_ConsumeTable[i].m_TMP_CousumeAmount.text = _FinalStr

            end
        end 
    end
end

---達到上限 顯示對應 TMP通知
function CommonQuery_Type2_Controller.ShowTouchUpLimit(iShowObj)
    this.m_TMP_UpLimit.gameObject:SetActive(iShowObj)
end


---刷新標題/內文 (理論上只會在開啟時刷新一次)
---@param iViewData table 從CommonQueryMgr帶來的table
function CommonQuery_Type2_Controller.RenewTitleAndContent(iViewData)

    ---是否要要內文 填入其他資訊
    if table.IsNullOrEmpty(iViewData.m_ContentParma) then
        this.m_Text_Content.text = TextData.Get(iViewData.m_ContentID)
    else
        this.m_Text_Content.text = GString.Format(TextData.Get(iViewData.m_ContentID) , unpack(iViewData.m_ContentParma, 1, table.maxn(iViewData.m_ContentParma))) 
    end
        
    ---設定標題
    if iViewData.m_TitleContentID ~=0 then --有標題
        if table.IsNullOrEmpty(iViewData.m_TitleParma)then
            this.m_TMP_TitleContent.text = TextData.Get(iViewData.m_TitleContentID)
        else
            this.m_TMP_TitleContent.text = GString.Format(TextData.Get(iViewData.m_TitleContentID) , unpack(iViewData.m_TitleParma, 1, table.maxn(iViewData.m_TitleParma)))
        end
        
        this.m_TMP_TitleContent.gameObject:SetActive(true)
    else
        this.m_TMP_TitleContent.gameObject:SetActive(false)
    end
end

---取得TimeBox的數字
---@return number TimeBox目前顯示的數字
function CommonQuery_Type2_Controller.GetCurrentTimes()
    return this.m_CurrentTimes
end

---根據道具ID 取得玩家有的數量 
---@param itemID number 要檢查的itemID 
---@param _BagItemAmount number 玩家擁有的數量
function CommonQuery_Type2_Controller.GetPlayerOwnAmountByItemID(itemID)
    
    local _BagItemAmount = 0 
    ---因為itemID 可能對應到貨幣 必須要判斷是否為貨幣類
    if table.ContainsKey(EItemIDResourceTable, itemID) then
        --如果是貨幣類 找玩家資料
        _BagItemAmount = PlayerData.GetCurrency(EItemIDResourceTable[itemID])
    else
        --如果是物品 找背包
        _BagItemAmount = BagMgr.GetItemInBagAmount(itemID)
    end

    return _BagItemAmount
end


--[[
    
    -- 以下整串塞到 Main_Controller.Update() 可測試
    -----
    if Input.GetKeyDown(KeyCode.M) then

		--- 測試用塞的假資料
		local _QueryData2 = {m_MessageType = 0,m_UITypeID=210,m_TitleIDX = 20300042,m_ContentIDX=9592,m_BTNConfirmIDX=1000,m_BTNCancelIDX=20103002,m_BTNSpecialIDX=0}
			
	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V1 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V1:BuildIconBoxTable(EIconBoxDataType.Icon,{10001,60001,12001},{1005,2005,1},{true,true,true},false)
		_Type2Data_V1:BuildConfirmBtnStatus(false)

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體1") end,nil,
	   nil,nil,nil,nil,_Type2Data_V1)

	   
	   
	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V2 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V2:BuildIconBoxTable(EIconBoxDataType.Icon,{60001,12001,10001},{1005,2005,1},{false,false,false},false)

	   _Type2Data_V2:BuildDescriptionBoxTable({"A","B"},{3,5},{"G",6},{false,true,false},{true,false})


	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體2") end,nil,
	   nil,nil,nil,nil,_Type2Data_V2)


	   
	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V3 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V3:BuildIconBoxTable(EIconBoxDataType.Icon,{60001,12001,10001},{1005,2005,1},{false,false,false},false)

	   _Type2Data_V3:BuildDescriptionBoxTable({"A","B"},{3,5},{"G",6},{false,true,false},{true,false})

	   _Type2Data_V3:BuildConsumeTable({95004,95011},{123,456})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體3") end,nil,
	   nil,nil,nil,nil,_Type2Data_V3)

	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V3_1 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V3_1:BuildIconBoxTable(EIconBoxDataType.Image,{"MainIcon_001_D"},{},{},false)

	   _Type2Data_V3_1:BuildDescriptionBoxTable({"A","B"},{3,5},{"G",6},{false,true,false},{true,false})

	   _Type2Data_V3_1:BuildConsumeTable({95004,95011},{123,456})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體3-1") end,nil,
	   nil,nil,nil,nil,_Type2Data_V3_1)


	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V4 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V4:BuildIconBoxTable(EIconBoxDataType.Icon,{60001,12001,10001},{1005,2005,1},{false,false,false},false)

	   _Type2Data_V4:BuildDescriptionBoxTable({"A","B"},{3,5},{"G",6},{false,true,false},{true,false})

	   _Type2Data_V4:BuildTimeTable(159,1,{1,2,3},{0,25},{11,22,33})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體4") end,nil,
	   nil,nil,nil,nil,_Type2Data_V4)

	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V5 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V5:BuildIconBoxTable(EIconBoxDataType.Icon,{60001,12001,10001},{1005,2005,1},{false,false,false},false)

	   _Type2Data_V5:BuildTimeTable(159,1,{1,2,3},{0,25},{11,22,33})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體5") end,nil,
	   nil,nil,nil,nil,_Type2Data_V5)

	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V6 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V6:BuildIconBoxTable(EIconBoxDataType.Icon,{60001,12001,10001},{1005,2005,1},{false,false,false},false)

	   _Type2Data_V6:BuildDescriptionBoxTable({"A","B"},{3,5},{"G",6},{false,true,false},{true,false})

	   _Type2Data_V6:BuildTimeTable(159,1,{1,2,3},{0,25},{11,22,33})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體6") end,nil,
	   nil,nil,nil,nil,_Type2Data_V6)

	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V7 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V7:BuildIconBoxTable(EIconBoxDataType.Icon,{60001},{1005},{false},true)

	   _Type2Data_V7:BuildConsumeTable({95004,95011},{123,456})

	   _Type2Data_V7:BuildTimeTable(159,1,{1,2,3},{0,25},{11,22,33})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體7") end,nil,
	   nil,nil,nil,nil,_Type2Data_V7)


	   --- 測試用塞的假資料 類別2專屬 customData
	   local _Type2Data_V8 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)

	   ---給icon模式 使用的icnoBox 填資料範例 
	   _Type2Data_V8:BuildIconBoxTable(EIconBoxDataType.Icon,{60001},{1005},{false},false)

	   _Type2Data_V8:BuildRequireTable("條件需求客製化",{"玩家等級達20以上開放","條件二(已達成)","條件三(未達成)"},{true,true,false})

	   CommonQueryMgr.AddNewInform(_QueryData2,{},{}, function() print(" 點擊Type2測試 變體4") end,nil,
	   nil,nil,nil,nil,_Type2Data_V8)
		
   end
]]
