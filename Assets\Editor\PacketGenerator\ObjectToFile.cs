//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2022 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    ////////////////////////////////////////////////////////////-
//
//=====================================================================

using GameTools.Editor.ScriptGeneration;
using System;
using UnityEngine.UIElements;
/// <summary>
/// 要轉成lua struct的物件、class、struct都可寫在這
/// <AUTHOR>
/// @version 1.0
/// @since HEM 2.0
/// @date 2022.7.18
/// </summary>
[StringFileSerializeAttribute]
public class SkillData
{
    public ushort m_Idx; //招式ID
    public byte m_SkillType; //招式欄位 1.普攻 2.絕學1 3.絕學2 4.絕學3 5.絕學4 6.核心技 7.翻滾/衝刺/迴避 8.? 101.內功(暫)
    public byte m_SkillClass; //Skill 階級
    public ushort m_NextSkillId; //下一階 Skill ID
    public ushort m_OriginalSkillId; //最初 Skill ID
    public byte m_UseType; //施放類型
    public ushort m_SkillOrderIdx; //招式串接
    public byte m_SkillOrder; //招式順序
    public ushort m_OriginalSource; //集氣招式來源
    public ushort m_TimeParm; //時間參數
    public byte m_OpenLV; //開放等級
    public uint m_SingleCD; //獨立冷卻時間 ( 毫秒、公式表 )
    public uint m_CommonCD; //共通冷卻時間 ( 毫秒 )
    public ushort m_LimitRange; //攻擊範圍
    public uint m_NameId; //招式名稱
    public uint m_CallSkillTextId; //喊招字串
    public ushort m_WugongID; //武功編號(反查)
    [ExTable(1)]
    public WugongData m_WugongData; //武功資料(反查)
    public bool m_MoveBreak; //可否移動中斷( 0.否 1.是 )
    public byte m_MoveType; //位移類型( 0.無 1.對人位移(衝鋒) 2.對點位移(突進) )
    public byte m_MoveDir; //位移方向( 0.無 1.前方 2.後方 )
    public ushort m_MoveDistance; //位移距離
    public ushort m_ActId; //動作檔ID
    public ushort m_SkillTime; //招式時間

    [ArrayAttribute ( 8 )]
    public Tick [] m_TickAy;
    public byte m_IconType; //Icon類型
    public ushort m_IconId; //Icon編號

    public ushort m_SpendMpFormulaId; //內力消耗 公式編號
    public ushort m_SpendMpParameter; //內力消耗 公式參數

    public ushort m_SpendSpFormulaId; //真氣消耗 公式編號
    public ushort m_SpendSpParameter; //真氣消耗 公式參數

    public ushort m_SpendHpFormulaId; //氣血消耗 公式編號
    public ushort m_SpendHpParameter; //氣血消耗 公式參數

    public byte m_TargetKind; //招式目標種類( 0.自己 1.Npc 2.玩家 3.全部 4.無 )
    public byte m_TargetGroup; //招式目標陣營( 1.敵方 2.隊伍 3.除了敵方 4.公會 5.全部 6.全部不含自己 )
    public byte m_SendTargetProtcol; //此招式會歸類為指定目標技能 (填 1 會送協定 8-1 8-2)
    public uint m_InfoStrId; //效果說明字串

    [ArrayAttribute ( 4 )]
    public ushort [] m_StateAry;
    [ArrayAttribute ( 4 )]
    public ushort [] m_StateFormulaAry;

    public ushort m_WugongDemoMode; //演武模式
    public ushort m_WugongDemoRange; //演武範圍參數
    public ushort m_AutoSkillKind; //掛機技能分類
    public bool m_ToBattleState; //施放後進入戰鬥狀態
    public byte m_OnDrivingLaunch; //駕駛坐騎才可使用
    public ushort m_LaunchEffectId; //自帶特效編號
    public ushort m_LaunchSoundId; //自帶音效編號
    //public byte m_ChangeSkillColor; //更換招式顏色 暫時不使用
    public byte m_SkillDivide;  //技能分類
    public ushort m_AttributesNum; //套用距離屬性修正值編號

}

public struct Tick
{
    public ushort TickId; //tick編號
    public ushort TickTime; //Tick時間
    [ExTable(1)]
    public TickData m_TickData; // TickData 反查
}

[StringFileSerializeAttribute]
public struct TickData
{
    public ushort Idx; //TickID
    public byte ConditionTarget; //TICK生效條件目標
    public byte ConditionType; //TICK生效條件類型
    public byte ConditionBuff; //TICK生效條件BUFF / BUFF群組
    public ushort HitEffectId; //傷害光影
    public byte TickEffectCalculation; //Tick生效條件運算
    public ushort TickEffectValue; //Tick生效條件數值
    public ushort HitEffectPos; //傷害特效掛點
    public byte DamageNumberOffsetType; //傷害數字偏移種類
    public ushort FlyEffectId; //飛行物特效編號
    public ushort BuffId; //附加buff 5.30 (改為S)
    public ushort AlertEffectId; //Buff警戒特效ID
    public ushort BaseDamageMultiply; //Tick基礎傷害倍率
    public ushort BaseGangChiMultiply; //Tick基礎罡氣倍率

    /*
    public ushort   EDamParam;            //敵人傷害參數
    public ushort   EInterForceParam;     //敵人內力參數
    public ushort   ERealChiParam;        //敵人真氣餐數
    public ushort   EGangChiParam;        //敵人罡氣參數

    public ushort   SDamParam;          //自己傷害參數
    public ushort   SHealParam;         //自己回復參數
    public ushort   SInterForceParam;   //自己內力參數
    public ushort   SRealChiParam;      //自己真氣餐數
    public ushort   SGangChiParam;      //自己罡氣參數
    */

    public byte RangeType; //範圍類型 ( 0.不做事 1.單體 2.線性 3.圓 4.算兩點的線性 )
    public ushort RangeStartOffset; //範圍起使點面相的偏移量

    public byte RangeTarget; //範圍目標 ( 0自身 1目標 )
    public byte TargetKind; //目標種類
    public byte TargetCamp; //目標陣營 ( 1敵方 2自己 3友方 12.敵方 我方 13.自己+友方 123.所有人  )

    public ushort RangeFacing; //範圍面向
    public ushort BasicDistance; //基礎射程
    public ushort BasicWidth; //基礎寬度

    public byte AlertType; //警戒類型 ( 0不做事 1不做事 2線性 3圓形 )
    public ushort AlertStartOffset; //警戒起始點面相的偏移量
    public byte AlertCamp; //警戒陣營 ( 1敵方 2自己 3友方 12.敵方 我方 13.自己+友方 123.所有人  )

    public ushort AlertFacing; //警戒面向
    public ushort AlertTime; //警戒時間
    public ushort AlertDistance; //警戒射程
    public ushort AlertWidth; //警戒寬度

    public byte MoveType; //位移類型 ( 0.不位移 1.向前 2.向後 3.向目標 4.背向目標 )

    public ushort SpecialHitEffectBuffId; //特定BUFF編號顯示傷害特效
    public ushort SpecialHitEffectId; //特定傷害光影
    public ushort TriggerSound;     // 觸發音效(技能)
    public byte BeHitSoundType;    // 觸發受擊音效(武器類型)

}

[StringFileSerializeAttribute]
public struct WugongData
{
    public ushort Idx; //武功ID
    public ushort InWeaponNum; //該武功在武器的編號(?)
    public ushort PassiveBuff; //係數編號/被動武學BUFFID
    public uint CallSkillTextId; //喊你媽字串ID

    public uint NameIdx; //武功名稱字串
    public byte Type; //武功子類別
    public byte IsPassive; //是否是被動(0:主動, 1:被動)
    public byte Weapon; //武器類別
    public byte IconType; //智障類型
    public ushort IconId; //圖片ID
    public byte EnergyCount; //集氣次數　
    [myatt ( 8 )]
    public ushort [] SkillIdxAry; //1~8式
    [ExTable(8)]
    public SkillData[] SkillDataAry; //1~8式
    public uint TextIdx; //效果說明文字

    public uint TeacherNameIdx; //導師名稱字串
    public ushort TeacherSceneId; //導師場景編號
    public ushort TeacherPosX; //導師位置X
    public ushort TeacherPosY; //導師位置X
    public ushort TransferID; //傳送ID
    public byte MaxLv; //武功等級上限
    public byte LeanMaxLv; //習武等級上限
    public ushort ExpendCoefficientA; //消耗武點係數A
    public ushort ExpendCoefficientB; //消耗武典係數B
    public ushort UnlockRoleLv; //學習解鎖角色等級
    public ushort TeachLimitMark; //學習限制永標
    [myatt ( 2 )]
    public PropertyData [] LimitPropertyAry; //限制屬性與數值
    public ushort FrontWugongId; //學習前置武功
    public byte FrontWugongLv; //學期前置武功等級
    [myatt ( 7 )]
    public uint [] UpLvInfoAry; //升級描述字串ID( lv:1~50, 51~255, 1~25, 26~255, 100~255, 110~255, 120~255  )
    public uint ItemIdx; //對應秘笈道具編號
    public byte IsSkillDeletable; //武功是否可刪除(0: 不可刪除 1:可刪除 2:測試武功(反正判斷就放這) )
    public byte SortingLevel; //排序優先度(1為優先 0為一般)
    //[myatt ( 4 )]目前沒有用到 先註解掉
    //public string WugongColorAry1; //武功顏色
}

public struct PropertyData
{
    public byte Type;
    public uint Value;
}


[StringFileSerializeAttribute]
public struct PartyData
{
    /// <summary>
    /// 編號Idx
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 時裝編號男
    /// </summary>
    [myatt( 3 )]
    public uint[] m_SuitID_M;
    /// <summary>
    /// 時裝編號女
    /// </summary>
    [myatt( 3 )]
    public uint[] m_SuitID_W;
    /// <summary>
    /// 時裝武器編號
    /// </summary>
    [myatt( 3 )]
    public uint[] m_SuitWeaponID;
    /// <summary>
    /// 座騎物品編號
    /// </summary>
    [myatt( 3 )]
    public uint[] m_RideItemID;
    /// <summary>
    /// 日刪永標
    /// </summary>
    public uint m_DailyMark;
    /// <summary>
    /// BUFF編號（新）
    /// </summary>
    public uint m_Buff_New;
    /// <summary>
    /// BUFF編號（舊）
    /// </summary>
    public uint m_Buff_Old;
    /// <summary>
    /// 上架時間_年
    /// </summary>
    public ushort m_StartYear;
    /// <summary>
    /// 上架時間_月
    /// </summary>
    public byte m_StartMonth;
    /// <summary>
    /// 上架時間_日
    /// </summary>
    public byte m_StartDay;
    /// <summary>
    /// 上架時間_時
    /// </summary>
    public byte m_StartHour;
    /// <summary>
    /// 下架時間_年
    /// </summary>
    public ushort m_EndYear;
    /// <summary>
    /// 下架時間_月
    /// </summary>
    public byte m_EndMonth;
    /// <summary>
    /// 下架時間_日
    /// </summary>
    public byte m_EndDay;
    /// <summary>
    /// 下架時間_時
    /// </summary>
    public byte m_EndHour;
    /// <summary>
    /// 是否為特殊節日
    /// </summary>
    public byte m_IsHoliday;
}

/// <summary>
/// 生活技能
/// </summary>
[StringFileSerializeAttribute]
public struct LiveSkillData
{
    /// <summary>
    /// 等級
    /// </summary>
    public byte m_Level;
    /// <summary>
    /// 累計需求經驗
    /// </summary>
    public uint m_ExpCap;
}

/// <summary>
/// 情侶資料
/// </summary>
[StringFileSerializeAttribute]
public struct LoveData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 狀態 0.單身1.情侶2.夫妻 ???
    /// </summary>
    public byte m_State;
    /// <summary>
    /// 等級字串
    /// </summary>
    public uint m_LvStrID;
    /// <summary>
    /// 說明字串
    /// </summary>
    public uint m_Note; 
    /// <summary>
    /// 親密度需求武點值
    /// </summary>
    public ushort m_NeedDevotion;
    /// <summary>
    /// 親密度升級值
    /// </summary>
    public ushort m_DevotionLv;
    /// <summary>
    /// 獎勵物品
    /// </summary>
    public uint m_RewardItemID;
    /// <summary>
    /// //數量
    /// </summary>
    public byte m_ItemCount;          
    /// <summary>
    /// //獎勵福袋
    /// </summary>
    public uint m_RewardBagID;        
    /// <summary>
    /// //數量
    /// </summary>
    public byte m_BagCount;           
    /// <summary>
    /// //情侶技能
    /// </summary>
    public uint m_SkillID;            
    /// <summary>
    /// //離婚費用
    /// </summary>
    public uint m_Money;              
}

/// <summary>
/// 情侶動作資料
/// </summary>
[StringFileSerializeAttribute]
public struct LoveActionData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 男動作檔編號
    /// </summary>
    public ushort m_ManActID;
    /// <summary>
    /// 女動作檔編號
    /// </summary>
    public ushort m_WomanActID;
    /// <summary>
    /// 動作永標
    /// </summary>
    public ushort m_ActMark;
}

/// <summary>
/// 寵物升級要用的東西
/// </summary>
public struct PetUpgradeCost
{
    /// <summary>
    /// 消耗物品種類1
    /// </summary>
    public uint CostItemID1;
    /// <summary>
    /// 消耗物品數量1
    /// </summary>
    public ushort CostItemCount1;
    /// <summary>
    /// 消耗物品種類2
    /// </summary>
    public uint CostItemID2;
    /// <summary>
    /// 消耗物品數量2
    /// </summary>
    public ushort CostItemCount2;
    /// <summary>
    /// 消耗物品種類3
    /// </summary>
    public uint CostItemID3;
    /// <summary>
    /// 消耗物品數量3
    /// </summary>
    public ushort CostItemCount3;
    /// <summary>
    /// 消耗代幣類型
    /// </summary>
    public ushort CostTokenKind;
    /// <summary>
    /// 消耗代幣數量
    /// </summary>
    public uint CostTokenCount;
}

[StringFileSerializeAttribute]
public struct PetData
{
    /// <summary>
    /// 寵物ID
    /// </summary>
    public ushort PetID;
    /// <summary>
    /// 寵物Icon
    /// </summary>
    public uint Pet2DIcon;
    /// <summary>
    /// 品階顯示
    /// </summary>
    public byte PetRank;
    /// <summary>
    /// 寵物名稱高度
    /// </summary>
    public ushort PetNameHeight;
    /// <summary>
    /// 寵物展示縮放大小
    /// </summary>
    public ushort PetModelScale;
    /// <summary>
    /// 寵物展示高度偏移
    /// </summary>
    public ushort PetModelOffsetY;
    /// <summary>
    /// 寵物展示高度偏移
    /// </summary>
    public ushort PetModelOffsetX;
    /// <summary>
    /// 貼地類型
    /// </summary>
    public byte GroundType;
    /// <summary>
    /// 召喚發話
    /// </summary>
    public uint SummonBubbleText;
    /// <summary>
    /// 待機發話1
    /// </summary>
    public uint IdelBubbleText1;
    /// <summary>
    /// 待機發話2
    /// </summary>
    public uint IdelBubbleText2;
    /// <summary>
    /// 待機發話3
    /// </summary>
    public uint IdelBubbleText3;
    /// <summary>
    /// 戰鬥發話1
    /// </summary>
    public uint BattleBubbleText1;
    /// <summary>
    /// 戰鬥發話2
    /// </summary>
    public uint BattleBubbleText2;
    /// <summary>
    /// NPC編號
    /// </summary>
    public uint NPCID;
    /// <summary>
    /// 初始星等
    /// </summary>
    public byte BeginRank;
    /// <summary>
    /// 寵物類型(人/獸..)
    /// </summary>
    public byte PetType;
    /// <summary>
    /// 初始戰力值
    /// </summary>
    public ushort BeginAtk;
    /// <summary>
    /// 基礎屬性
    /// </summary>
    public ushort BasicAttribute;
    /// <summary>
    /// 主動技隨機群組編號
    /// </summary>
    public ushort SkillGroup;
    /// <summary>
    /// 被動技隨機群組編號
    /// </summary>
    public ushort PassiveGroup;
    /// <summary>
    /// 四星常駐buff編號
    /// </summary>
    public ushort PassiveBuff;
    /// <summary>
    /// 五星收藏的永標
    /// </summary>
    public ushort CollectFlag;
    /// <summary>
    /// 五星收藏的獎勵物品編號
    /// </summary>
    public uint CollectItemID;
    /// <summary>
    /// 對應組件物品編號
    /// </summary>
    public uint PartItemID;
    /// <summary>
    /// 對應骨架物品編號
    /// </summary>
    public uint BoneItemID;
    /// <summary>
    /// 製造用的代幣種類
    /// </summary>
    public ushort ProduceTokenKind;
    /// <summary>
    /// 製造用的代幣數量
    /// </summary>
    public uint ProduceTokenCost;
    /// <summary>
    /// 不可被融合
    /// </summary>
    public byte IsNoFuse;
    /// <summary>
    /// 升級素材
    /// </summary>
    [myatt(4)]
    public PetUpgradeCost [] PetUpgradeCosts;
}
//public struct PetData
//{
//    /// <summary>
//    /// 寵物ID
//    /// </summary>
//    public ushort PetID;
//    /// <summary>
//    /// 寵物物品ID
//    /// </summary>
//    public uint PetItemID;
//    /// <summary>
//    /// 寵物名稱ID
//    /// </summary>
//    public uint PetNameID;
//    /// <summary>
//    /// 2D Icon 編號
//    /// </summary>
//    public ushort _2DIcon;
//    /// <summary>
//    /// 寵物hint編號
//    /// </summary>
//    public uint HintID;
//    /// <summary>
//    /// 3D 模型
//    /// </summary>
//    public string ModelName;
//    /// <summary>
//    /// 外型變色
//    /// </summary>
//    public string ModelColor;
//    /// <summary>
//    /// 寵物品質
//    /// </summary>
//    public ushort Rank;
//    /// <summary>
//    /// 寵物天賦係別屬性（1.負重系 2.陪伴系 3.販賣系）
//    /// </summary>
//    public byte Genus;
//    /// <summary>
//    /// 蛋等級
//    /// </summary>
//    public byte EggLv;
//    /// <summary>
//    /// 蛋名稱
//    /// </summary>
//    public uint EggName;
//    /// <summary>
//    /// 蛋需求能量
//    /// </summary>
//    public uint EggEnergy;
//    /// <summary>
//    /// 體型大小
//    /// </summary>
//    public ushort BodyScale;
//    /// <summary>
//    /// 展示大小
//    /// </summary>
//    public ushort ShowScale;
//    /// <summary>
//    /// 最大壽命
//    /// </summary>
//    public byte MaxLife;
//    /// <summary>
//    /// 最大活力
//    /// </summary>
//    public ushort MaxPower;
//    /// <summary>
//    /// 最大精神
//    /// </summary>
//    public ushort MaxSpirit;
//    /// <summary>
//    /// 貼地類型
//    /// </summary>
//    public byte onGroundType;
//    /// <summary>
//    /// 經驗加成
//    /// </summary>
//    public byte ExpPlus;
//    /// <summary>
//    /// 攻擊力
//    /// </summary>
//    public uint Attack;
//    /// <summary>
//    /// 攻擊速度
//    /// </summary>
//    public byte AtkSpd;
//    /// <summary>
//    /// 爆擊率
//    /// </summary>
//    public byte AtkCri;
//    /// <summary>
//    /// 絕對傷害
//    /// </summary>
//    public ushort AtkAbs;
//    /// <summary>
//    /// 破罡
//    /// </summary>
//    public uint ShdBrk;
//    /// <summary>
//    /// 永標
//    /// </summary>
//    public ushort Sign;
//    /// <summary>
//    /// 進化10永標
//    /// </summary>
//    public ushort MaxSign;
//    /// <summary>
//    /// NPCID
//    /// </summary>
//    public uint NpcID;
//    /// <summary>
//    /// 收回消耗黃易幣
//    /// </summary>
//    public uint Cost;
//    /// <summary>
//    /// 顯示高度
//    /// </summary>
//    public ushort ShowH;
//    /// <summary>
//    /// 對應的商店編號
//    /// </summary>
//	public byte StoreID;
//    /// <summary>
//    /// 蛋物品ID
//    /// </summary>
//    public uint EggItemID;
//    /// <summary>
//    /// 攻擊距離
//    /// </summary>
//    public ushort AtkDis;
//}

/// <summary>
/// 寵物數值表
/// </summary>
[StringFileSerializeAttribute]
public struct PetAttributeData
{
    /// <summary>
    /// 數值編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 基礎攻擊倍率(千分比)
    /// </summary>
    public ushort m_Atk;
    /// <summary>
    /// 基礎攻速
    /// </summary>
    public ushort m_AtkSpeed;
    /// <summary>
    /// 基礎爆擊率(千分比)
    /// </summary>
    public ushort m_Criticle;

}

/// <summary>
/// 寵物技能
/// </summary>
[StringFileSerializeAttribute]
public struct PetSkillData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// BUFF ID
    /// </summary>
    public ushort m_EffID;
    /// <summary>
    /// 寵物技能名稱ID
    /// </summary>
    public uint m_NameID;
    /// <summary>
    /// 效果說明
    /// </summary>
    public uint m_EffNoteID;
    /// <summary>
    /// 可學習係別屬性
    /// </summary>
    public byte m_LimitGenus;
    /// <summary>
    /// 技能類型
    /// </summary>
    public byte m_SkillType;
    /// <summary>
    /// 技能類型組ID
    /// </summary>
    public byte m_SkillGroupID;
    /// <summary>
    /// 可學習寵物品質
    /// </summary>
    public byte m_LinitRank;
    /// <summary>
    /// 學習所需武點
    /// </summary>
    public uint m_NeedSkillPoint;
    /// <summary>
    /// 學習所需活力
    /// </summary>
    public byte m_NeedPower;
    /// <summary>
    /// 消耗活力值
    /// </summary>
    public byte m_UsePower;
    /// <summary>
    /// 消耗精神值
    /// </summary>
    public byte m_UseSpirit;
    /// <summary>
    /// 特效
    /// </summary>
    public ushort m_Effect;
    /// <summary>
    /// 師承名稱ID
    /// </summary>
    public uint m_TeacherID;
    /// <summary>
    /// 師承場景
    /// </summary>
    public ushort m_TeacherScene;
    /// <summary>
    /// 師承位置X
    /// </summary>
    public ushort m_TeacherX;
    /// <summary>
    /// 師承位置Y
    /// </summary>
    public ushort m_TeacherY;
    /// <summary>
    /// 寵物進化等級可學習
    /// </summary>
    public byte m_SkillCanLearnLV;
    /// <summary>
    /// 技能最高可學習等級
    /// </summary>
    public byte m_SkillMaxLearnLV;
    /// <summary>
    /// 習武說明字串
    /// </summary>
    public uint m_LearnWugongTextID;
    /// <summary>
    /// 技能寵物系別判斷字串
    /// </summary>
    public uint m_SkillTypeTextID;
    /// <summary>
    /// 技能類型字串
    /// </summary>
    public uint m_SkillTypeMainTextID;
    /// <summary>
    /// 技能群組分類字串
    /// </summary>
    public uint m_SkillTypeSubTextID;
}


/// <summary>
/// 寵物天賦
/// </summary>
[StringFileSerializeAttribute]
public struct PetTalentData
{
    /// <summary>
    /// 索引
    /// </summary>
    public byte m_Idx;                  
    /// <summary>
    /// 天賦類型
    /// </summary>
    public byte m_Genus;                 
    /// <summary>
    /// 進化等級
    /// </summary>
    public byte m_EvoLv;                 
    /// <summary>
    /// 天賦名稱
    /// </summary>
    public uint m_Name;                  
    /// <summary>
    /// Icon 類型
    /// </summary>
    public ushort m_IconType;            
    /// <summary>
    /// Icon 編號
    /// </summary>
    public ushort m_IconId;              
    /// <summary>
    /// Hint 說明
    /// </summary>
    public uint m_Hint;                  
    /// <summary>
    /// 天賦編號
    /// </summary>
    public byte m_TalentNumber;       
}

/// <summary>
/// 寵物商店
/// </summary>
[StringFileSerializeAttribute]
public struct PetStoreData
{
    /// <summary>
    /// 索引
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 商店名稱字串ID
    /// </summary>
    public uint m_StroeName;
    /// <summary>
    /// 商店屬性[1買 2賣]
    /// </summary>
    public byte m_Pet_Att;
    /// <summary>
    /// 每買賣一次扣除活力值_LV1
    /// </summary>
    public PetStoreLimitData[] m_SpendPower_LV1;
    /// <summary>
    /// 一日買賣商店限制次數_LV1
    /// </summary>
    public PetStoreLimitData m_OpenTime_LV1;
    /// <summary>
    /// 每買賣一次扣除活力值_LV2
    /// </summary>
    public PetStoreLimitData m_SpendPower_LV2;
    /// <summary>
    /// 一日買賣商店限制次數_LV2
    /// </summary>
    public PetStoreLimitData m_OpenTime_LV2;
    /// <summary>
    /// 每買賣一次扣除活力值_LV3
    /// </summary>
    public PetStoreLimitData m_SpendPower_LV3;
    /// <summary>
    /// 一日買賣商店限制次數_LV3
    /// </summary>
    public PetStoreLimitData m_OpenTime_LV3;
    /// <summary>
    /// 每買賣一次扣除活力值_LV4
    /// </summary>
    public PetStoreLimitData m_SpendPower_LV4;
    /// <summary>
    /// 一日買賣商店限制次數_LV4
    /// </summary>
    public PetStoreLimitData m_OpenTime_LV4;
    /// <summary>
    /// 每買賣一次扣除活力值_LV5
    /// </summary>
    public PetStoreLimitData m_SpendPower_LV5;
    /// <summary>
    /// 一日買賣商店限制次數_LV5
    /// </summary>
    public PetStoreLimitData m_OpenTime_LV5;
    /// <summary>
    /// //寵物商店物品資料
    /// </summary>
    [myatt( 5 )]
    public PetStoreItemData[] m_PetStoreItemDataAy;      
}

public struct PetStoreLimitData
{
    public byte m_PetTalentIdx;             //對應寵物天賦表編號(用來查進化等級)
    public ushort m_Value;				    //每買賣一次扣除的活力值 / 一日買賣商店限制次數
}

public struct PetStoreItemData
{
    public byte m_ItemOpemTalent;         //天賦等級開放商品(對應寵物天賦表編號)
    public uint m_ItemID;                 //商品ID
    public ushort m_ItemLimitCount;       //商品限制數量
}

/// <summary>
/// 寵物收集能力加成
/// </summary>
[StringFileSerializeAttribute]
public struct PetPowerData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 蒐集寵物等級
    /// </summary>
    public ushort m_Level;
    /// <summary>
    /// 蒐集效果
    /// </summary>
    [myatt( 20 )]
    public sItemBaseAttribute[] m_EffectData;
}

/// <summary>
/// 成就內容
/// </summary>
[StringFileSerializeAttribute]
public struct AchievementContent
{
    public ushort AchieveID;    //成就ID
    public ushort TagID;    //分頁編號
    public byte SubTagID;   //子分頁編號(1~5)
}

/// <summary>
/// 成就分頁、福利分頁
/// </summary>
[StringFileSerializeAttribute]
public struct ActivityTag
{
    public byte TagType;        // 分頁類型
    public byte TagID;          // 分頁編號 ,0=總覽
    public uint Name;           // 分頁按鈕字串
    public string AchievementICON;  //成就ICON
    [myatt(5)]
    public uint[] SubPageName;  //子分頁按鈕字串
    public byte Banner;         // 分頁標題圖片BannerID
    public string Picture;      // 分頁主視覺立繪
    public uint Hint;           // 說明字串
    public byte Typesetting;    // 版型
    public ushort Value;     // 版型參數(介消)
    public ushort NumFlag;      // 計數永標
    public ushort AutoDBSwitchID;// AutoDB ID
    public string SeasonPassID; // 通行證商品 ID
}

/// <summary>
/// 福利活動內容
/// </summary>
[StringFileSerializeAttribute]
public struct ActivityContent
{
    public ushort ID;
    public ushort AutoDBSwitchID;
    public ushort TagID;
}

/// <summary>
/// 成就獎勵
/// </summary>
public struct AchievementReward
{
	public uint ItemID;
	public ushort Num;
}

/// <summary>
/// 成就條件
/// 舊黃易M叫做AchievementData
/// </summary>
[StringFileSerializeAttribute]
public struct AchievementID
{
	/// <summary>
	/// 成就ID
	/// </summary>
	public ushort ID;
	/// <summary>
	/// 達成條件字串
	/// </summary>
	public uint Info;
	/// <summary>
	/// 計數標記
	/// </summary>
	public ushort Flag;
	/// <summary>
	/// 資料來源
	/// </summary>
	public byte Source;
	/// <summary>
	/// 變數值
	/// </summary>
	public uint SourceValue;
	/// <summary>
	/// 運算子
	/// </summary>
	public byte Operator;
	/// <summary>
	/// 數值
	/// </summary>
	public uint Value;
	/// <summary>
	/// 成就獎勵
	/// </summary>
	[myatt(5)]
	public AchievementReward[] Rewards;
	/// <summary>
	/// 兌換扣除物品
	/// </summary>
	public uint CostID;
	/// <summary>
	/// 扣除物品數量
	/// </summary>
	public ushort CostNum;
	/// <summary>
	/// 領獎標記
	/// </summary>
	public ushort RewardFlag;
	public uint UIItemNum;

	/// <summary>
	/// 成就 資料來源
	// None = 0,           // 無
	//Level = 1,          // 等級
	//Feature = 2,        // 心性
	//WuGong = 3,         // 外功
	//Skill = 4,          // 內功
	//SpFunction = 5,     // 特殊功能(計數標計?)
	//StatusPoint = 6,    // 主屬性投點
	//SkillPoint = 7,     // 武功點數(計數標計?)
	//Death = 8,          // 死亡(計數標計?)
	//Sign = 9,           // 簽到(計數標計?)
	//Strengthen = 10,    // 強化(計數標計?)
	//Money = 11,         // 持有金錢
	//ItemUse = 12,       // 使用物品(計數標計?)
	//ItemGet = 13,       // 獲得物品(計數標計?)
	//Friend = 14,        // 好友數量
	//Combine = 15,       // 合成(計數標計?)
	//LifeSkill = 16,     // 採集(計數標計?)
	//Flag = 17,          // 永標
	//NumFlag = 18,       // 計數永標
	//MovingFlag = 19,    // 動標
	//PVP = 20,           // PVP(計數標計?)
	//OnlineTime = 21,    // 在線時間(計數標計?)
	//SuperChips = 22,    // 超級晶片
	//YoungDan = 23,      // 永丹使用次數
	/// </summary>
	public byte SourceType; //本來是 Enum EAchieveSrc:byte

	/// <summary>
	/// 成就 控制符號 None = 0 ,Equal = 1, GreaterThan = 2, GreaterThanOrEqual = 3, LessThan = 4, LessThanOrEqual = 5, NotEqual = 6,
	/// </summary>
	public byte OperatorType;// 本來是 Enum EAchieveOp:byte
}

/// <summary>
/// 信箱資料
/// </summary>
[StringFileSerializeAttribute]
public struct MailData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 信件類型 1.系統獎勵 2.活動連結 3.文字公告 4.好友申請
    /// </summary>
    public EMailType m_Type;
    /// <summary>
    /// 信件標題
    /// </summary>
    public uint m_TitleStrID;
    /// <summary>
    /// 信件內容
    /// </summary>
    public uint m_ContentStrID;
    /// <summary>
    /// 信件ICON
    /// </summary>
    public byte m_IconNum;
    /// <summary>
    /// 保存天數 0=永久
    /// </summary>
    public byte m_SaveDays;
    /// <summary>
    /// 連結
    /// </summary>
    public string m_LinkStr;
    /// 獎勵
    /// </summary>
    [myatt( 10 )]
    public MailReward[] m_RewardAy;
}

/// <summary>
/// 信箱獎勵
/// </summary>
public struct MailReward
{
    /// <summary>
    /// 類型
    /// </summary>
    public ERewardType m_RewardType;
    /// <summary>
    /// 編號
    /// </summary>
    public uint m_Num;
    /// <summary>
    /// 數量
    /// </summary>
    public uint m_Count;
}

/// <summary>
/// 信件類型
/// </summary>
public enum EMailType : byte
{
    TextMail = 0,
    SysReward = 1,
    SysLink = 2,
    SysText = 3,
    FriendMail = 4,MaxSixe = FriendMail,
}

/// <summary>
/// 獎勵類型
/// </summary>
public enum ERewardType : byte
{
    /// <summary>
    /// 無
    /// </summary>
    Non = 0,
    /// <summary>
    /// 時空幣
    /// </summary>
    SpaceCoin = 1,
    /// <summary>
    /// 黃易幣
    /// </summary>
    HEMCoin = 2,
    /// <summary>
    /// 遊戲幣
    /// </summary>
    GameCoin = 3,
    /// <summary>
    /// 物品
    /// </summary>
    Item = 4,
    /// <summary>
    /// 男玩家物品
    /// </summary>
    ManItem = 5,
    /// <summary>
    /// 女玩家物品
    /// </summary>
    WomanItem = 6,
}

/// <summary>
/// 轉轉樂串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct GamblingData
{
    /// <summary>
    /// 索引
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 獎品分類
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 物品Idx
    /// </summary>
    public uint m_Idx_Item;
    /// <summary>
    /// 數量
    /// </summary>
    public ushort m_Count;
    /// <summary>
    /// 裝備強化+幾
    /// </summary>
    public byte m_EquipStrengthen;
    /// <summary>
    /// 銅盤次數
    /// </summary>
    public ushort m_Limit_Bronze;
    /// <summary>
    /// 白銀盤次數
    /// </summary>
    public ushort m_Limit_Siver;
    /// <summary>
    /// 黃金盤次數
    /// </summary>
    public ushort m_Limit_Gold;
}

/// <summary>
/// 寶石表
/// </summary>
[StringFileSerializeAttribute]
public struct GemData
{
    /// <summary>
    /// 寶石效果編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 寶石屬性數值
    /// </summary>
    public sItemBaseAttribute m_GemAttribute;
    /// <summary>
    /// 耐久度增加上限
    /// </summary>
    public byte m_GemDurability;
    /// <summary>
    /// 寶石物品ID
    /// </summary>
    public uint m_GemItemIdx;
    /// <summary>
    /// 強化值
    /// </summary>
    public byte m_GemGrowTime;
    /// <summary>
    /// 寶石說明字串ID
    /// </summary>
    public uint m_GemTextIdx;
    /// <summary>
    /// 強度值
    /// </summary>
    public ushort m_GemStrengthValue;
}

/// <summary>
/// 福袋
/// </summary>
[StringFileSerializeAttribute]
public struct FuDaiData
{
    /// <summary>
    /// 福袋編號
    /// </summary>
    public uint m_Idx;
    /// <summary>
    /// 福袋種類
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 總機率
    /// </summary>
    public ushort m_TotalProbability;   
    /// <summary>
    /// 掉寶數量
    /// </summary>
    public byte m_DropNum;
    /// <summary>
    /// 福袋獎品
    /// </summary>
    [myatt( 30 )]
    public FuDaiReward[] m_Rewards;     //福袋獎品
}

public struct FuDaiReward
{
    /// <summary>
    /// 物品種類
    /// </summary>
    public byte m_ItemType;             
    /// <summary>
    /// 物品編號
    /// </summary>
    public uint m_ItemIdx;              
    /// <summary>
    /// 物品數量
    /// </summary>
    public byte m_Num;                  
    /// <summary>
    /// 物品機率
    /// </summary>
    public ushort m_Probability;        
    /// <summary>
    /// 強化值
    /// </summary>
    public byte m_EnhanceValue;          
    /// <summary>
    /// 廣播訊息
    /// </summary>
    public byte m_Broadcast;            
}


/// <summary>
/// 係數表
/// </summary>
[StringFileSerializeAttribute]
public struct FormulaData
{
    public ushort m_Idx;
    public byte m_FormulaIdx;
    public ushort m_A;
    public ushort m_B;
    public ushort m_C;
    public ushort m_D;
}

/// <summary>
/// 貨幣串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct CurrencyData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 貨幣圖示 ICON類型
    /// </summary>
    public byte m_IconType;
    /// <summary>
    /// 貨幣圖示 ICON流水號
    /// </summary>
    public ushort m_IconIdx;
    /// <summary>
    /// 貨幣字串
    /// </summary>
    public uint m_StringIdx;
    /// <summary>
    /// 貨幣種類 0=無 1=遊戲幣 2=黃易幣 3=時空幣 4=福緣 5=武點 6=威望 7=聲譽 8=量子源 9=物品 10=記數永標 11=負重
    /// </summary>
    public byte m_Kind;
    /// <summary>
    /// 貨幣種類 參數 0~8=免填 9=物品編號ID 10=記數永標ID
    /// </summary>
    public uint m_Param;
}

/// <summary>
/// 公告串檔資料, 註解已補在toLua
/// </summary>
[StringFileSerializeAttribute]
public struct CoverData
{
    /// <summary>
    /// 索引
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 平台 0.共通 1.iOS 2.Android 3.myCard 4.天空龍 5.其他平台
    /// </summary>
    public byte m_Version;
    /// <summary>
    /// 分頁標題文字索引
    /// </summary>
    public uint m_Title;
    /// <summary>
    /// 分頁標題Icon
    /// </summary>
    public string m_Icon;
    /// <summary>
    /// 分頁美術圖索引
    /// </summary>
    public byte m_BannerIdx;
    /// <summary>
    /// 分頁籤顯示開始時間
    /// </summary>
    public CoverTime m_TagStart;
    /// <summary>
    /// 分頁籤顯示結束時間
    /// </summary>
    public CoverTime m_TagEnd;
    /// <summary>
    /// 活動功能限時開始時間
    /// </summary>
    public CoverTime m_ActStart;
    /// <summary>
    /// 活動功能限時結束時間
    /// </summary>
    public CoverTime m_ActEnd;
    /// <summary>
    /// 按鈕類型功能資料
    /// </summary>
    [myatt( 4 )]
    public CoverButton[] m_Buttons;
    /// <summary>
    /// 物品排版顯示
    /// </summary>
    public byte m_ItemShowType;

    /// <summary>
    /// 物品內容
    /// </summary>
    [myatt( 6 )]
    public CoverItem[] m_Items;
}

public struct CoverTime
{
    public ushort m_Year; //年
    public byte m_Month;  //月
    public byte m_Day;    //日
    public byte m_Hour;   //時
}

public struct CoverButton
{
    /// <summary>
    /// 功能類型 0.不使用 1.介面連結 2.NPC巡路 3.外部連結
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 按鈕的文字索引
    /// </summary>
    public uint m_Title;
    /// <summary>
    /// 網頁連結網址
    /// </summary>
    public string m_Link;
    /// <summary>
    /// 呼叫的 UI 索引
    /// </summary>
    public byte m_UIIdx;
    /// <summary>
    /// 呼叫的 UI 分頁
    /// </summary>
    public byte m_UIValue;
    /// <summary>
    /// NPC巡路事件索引
    /// </summary>
    public uint m_NpcEventId;
}

public struct CoverItem
{
    /// <summary>
    /// 物品索引
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 物品數量
    /// </summary>
    public uint m_Num;
    /// <summary>
    /// 強化值
    /// </summary>
    public byte m_Grow;
}


/// <summary>
/// 效果串檔表
/// </summary>
[StringFileSerializeAttribute]
public struct BuffData
{
    public ushort m_Idx;          //索引
    public byte m_Priority;       //Buff優先度
    public ushort m_CoverBuffGroup;      //覆蓋BUFF群組
    public byte m_BuffKind;       //Buff類型 ( 0:正面 1:負面 )
    public byte m_ChangeSkillType;//改變技能欄位
    public ushort m_ChangeSkillId;  //改變Skillid
    public byte m_ModelScale;     //改變模型大小 0:不改變 50:變為50% 200:兩倍大小
    public byte m_FullScreenCover;//畫面周圍遮罩 1:全罩隱身
    public byte m_SpecialPerform; //特殊表現種類 1:普通隱身 2:移動殘影 3:天地一氣殘影 4.彈藥特效
    public uint m_ShowDebuffIconId;//強控Debuff ( 秀暈眩、倒地 )
    public uint m_ShowBuffWordId;//顯示buff的大字
    public byte m_IconType;       //圖片規則用
    public ushort m_IconName;       //圖片名稱
    public uint m_EffectIdx;       //Buff持續特效索引
    public uint m_BuffDeathEffect;//Buff被消失特效
    public uint m_BuffJoinEffect; //Buff加入特效
    public byte m_EffectPos;      //Buff持續特效位置( 例:護盾的特效位置 )　
    public byte m_BuffDeathPos;      //Buff被消失特效位置　
    public byte m_BuffJoinPos;      //Buff加入特效位置
    public uint m_SoundIdx;       //音效索引
    public byte m_ActNum;         //動作編號 ( 0:無 1:暈眩 2:擊倒 )
    public byte m_OfflineHold;    //下線保留
    public byte m_DeadHold;       //死亡保留
    public byte m_FaceTo;         //改變面相 ( 是否面向施法者 0:否 1:是 )
    public byte m_LimitAtk;       //限制放招類型 ( 0.無 1.全限制 2.禁止外功 3.禁止內功 )
    public byte m_LimitMove;      //限制移動 ( 0:否 1:是 )
    public ushort m_TimeFormulaIdx;  //持續時間公式ID ( ID如為100 值接讀TimeParameter為持續時間用 )
    public ushort m_TimeParameter;  //持續時間參數
    public uint m_OnceTime;       //效果頻率
    public byte m_StackCount;     //堆疊次數
    public ushort m_MagicRadius;    //魔法陣半徑
    public ushort m_MagicCamp;      //魔法陣陣營
    public ushort m_MagicEffectIdx;  //魔法陣特效索引
    public ushort m_CostBuffId;  //消耗的BuffID
    public byte m_CostBuffAmount;     //消耗Buff的量

    [myatt( 4 )]
    public BuffEffectData[] m_Ay_BuffEffect; //特效資料
    public uint m_InfoTextIdx;          //說明字串
    public uint m_NameTextIdx;          //名稱字串
    public byte m_Delete;               //是否可手動刪除
}

public struct BuffEffectData
{
    public ushort m_EffectKind;     //給予屬性的種類編號
    public ushort m_UnlockLevel;    //解鎖此效果的等級需求
    public ushort m_FormulaIdx;      //公式索引
    public ushort m_Parameter;      //參數
}

/// <summary>
/// Banner串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct BannerData
{
    public byte m_Idx;
    public byte m_Type;
    public byte m_Platform;
    public string m_ImgName;
    public byte m_UIIdx;
    public byte m_OpenKind;
    public byte m_UIParam1;
    public byte m_UIParam2;
    public string m_Url;
}

/// <summary>
/// 伺服器活動開關
/// </summary>
[StringFileSerializeAttribute]
public struct AutoDBSwitchData
{
    public ushort m_Idx;
    public byte m_Type;
    public byte m_WeekDelay;
    public byte m_Daily;
    public ushort m_StartYear;
    public byte m_StartMonth;
    public byte m_StartDay;
    public byte m_StartHour;
    public byte m_StartMinute;
    public ushort m_EndYear;
    public byte m_EndMonth;
    public byte m_EndDay;
    public byte m_EndHour;
    public byte m_EndMinute;
    public byte m_ActivityNotifyType;   //[0:None 1:Stage 2:QuestList]
    public ushort m_IdxByNotifyType;     //Stage or QuestList ID
    public byte m_NotifyBtnExistTime;   //提示按鈕存在時間
    public uint m_ActivityNotifyString; //活動提示字串
    public ushort m_ActivityFlag;       //活動永標
}

/// <summary>
/// 共用詢問視窗資料串檔
/// </summary>
[StringFileSerializeAttribute]
public struct CommonQuery
{
    /// <summary>
    /// 訊息編號
    /// </summary>
    public ushort m_IDX;
    /// <summary>
    /// 訊息類型
    /// </summary>
    public byte m_MessageType;
    /// <summary>
    /// 倒數類型
    /// </summary>
    public byte m_CountingType;
    /// <summary>
    /// 倒數時間
    /// </summary>
    public byte m_CountingTime;
    /// <summary>
    /// 需求介消編號
    /// </summary>
    public ushort m_CostIDX;
    /// <summary>
    /// 標題字串 Index
    /// </summary>
    public uint m_TitleIDX;
    /// <summary>
    /// 內容字串 Index
    /// </summary>
    public uint m_ContentIDX;
    /// <summary>
    /// 確認按鈕字串 Index
    /// </summary>
    public uint m_BTNConfirmIDX;
    /// <summary>
    /// 取消按鈕字串 Index
    /// </summary>
    public uint m_BTNCancelIDX;
    /// <summary>
    /// 特殊按鈕字串 Index
    /// </summary>
    public uint m_BTNSpecialIDX;
    /// <summary>
    /// 視窗出現音效
    /// </summary>
    public ushort m_WindowSound;
    /// <summary>
    /// 確定音效
    /// </summary>
    public ushort m_ConfirmSound;
    /// <summary>
    /// 取消音效
    /// </summary>
    public ushort m_CancelSound;
    /// <summary>
    /// 特殊音效
    /// </summary>
    public ushort m_SpecialSound;
}

/// <summary>
/// 屬性名詞表
/// </summary>
[StringFileSerializeAttribute]
public struct StatusNameData
{
    public ushort m_Idx;           //索引
    public uint m_TextIdx;         //字串索引
    public uint m_ShortTextIdx;    //縮寫字串索引
    public byte m_INCOrDEC;        //數值增減 [0.增加 1.減少]
    public byte m_ValueType;       //數值單位 [0.整數 1.百分比 2.千分比]
    public byte m_FormateType;     //顯示格式 [0.整數 1.小數點後1位 2.小數點後2位]
    public byte m_ShowPercent;     //有無百分號 [0.不顯示 1.顯示]
}

/// <summary>
/// VIP消費等級資料
/// </summary>
[StringFileSerializeAttribute]
public struct CashRewardData
{
    public byte m_Level;          // 消費等級 (VIP)
    public uint m_InfoTextIdx;    // 等級描述字串
    public uint m_ExpCap;         // 經商經驗 (30日累計總經商經驗)
    public bool m_NotLevelDown;   // 是否會降級
    public byte m_HandlingFee;    // 交易手續費折扣
    public byte Dm_iscount;       // 消費折扣
    public uint m_RewardInfo;     // 獎勵字串
    public string m_BgColor;      // 底板顏色
}

/// <summary>
/// 文字劇情演出串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct CaptionTheaterData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_ID;
    /// <summary>
    /// 底圖字串 沒填=預設全黑底圖 ,有填=載入指定圖檔
    /// </summary>
    public string m_BGStr;
    /// <summary>
    /// BGMID 沒填就播原本的BGM
    /// </summary>
    public ushort m_BGM;
    /// <summary>
    /// 字幕內容
    /// </summary>
    public uint m_StrID;
    /// <summary>
    /// 文字位置     0=靠上 1=置中 2=靠下
    /// </summary>
    public byte m_TextAlignment;
    /// <summary>
    /// 打字機 開關   0=無打字機效果 ,1=有打字機效果
    /// </summary>
    public byte m_Typewriter;
    /// <summary>
    /// 允許略過開關  0=不可略過 ,1=可略過
    /// </summary>
    public byte m_Skip;
    /// <summary>
    /// 撥放完成後自動結束開關  0=關閉 ,1=開啟
    /// </summary>
    public bool m_CloseWhenPlayEnd;
    /// <summary>
    /// 結束接續流水號 沒填就關閉UI
    /// </summary>
    public ushort m_NextID;
}

/// <summary>
/// 金流獎勵資料
/// </summary>
[StringFileSerializeAttribute]
public struct CashBonusData
{
    /// <summary>
    /// 編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 內容物陣列
    /// </summary>
    [myatt( 10 )]
    public CashBonusItem[] m_Contents;
}

/// <summary>
/// 金流獎勵內容物
/// </summary>
public struct CashBonusItem
{
    /// <summary>
    /// 物品ID
    /// </summary>
    public uint m_ItemIdx;

    /// <summary>
    /// 物品數量
    /// </summary>
    public uint m_Count;
}

/// <summary>
/// 特殊按鈕串表
/// </summary>
[StringFileSerializeAttribute]
public struct SpecialSkillBtnData
{
    /// <summary>
    /// 功能ID
    /// </summary>
    public ushort m_Idx;          
    /// <summary>
    /// Icon 類別
    /// </summary>
    public byte m_IconType;       
    /// <summary>
    /// Icon 編號
    /// </summary>
    public uint m_IconIdx;         
    /// <summary>
    /// 功能字串
    /// </summary>
    public uint m_FunctionTextIdx; 
    /// <summary>
    /// 點故字串
    /// </summary>
    public uint m_AllusionTextIdx; 
    /// <summary>
    /// 描述字串
    /// </summary>
    public uint m_DesTextIdx;      
    /// <summary>
    /// 限制等級
    /// </summary>
    public ushort m_LimitLV;      
    /// <summary>
    /// 戰鬥可施展
    /// </summary>
    public bool m_UseInBattle;
}


/// <summary>
/// BOSS 傳送資料表
/// </summary>
[StringFileSerializeAttribute]
public struct BossGPSData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public byte m_Idx;

    /// <summary>
    /// 追蹤器ID
    /// </summary>
    public uint m_ItemIdx;

    /// <summary>
    /// 種植事件ID
    /// </summary>
    public uint m_EventIdx;

    /// <summary>
    /// 場景ID
    /// </summary>
    public uint m_SceneIdx;

    /// <summary>
    /// 傳送ID
    /// </summary>
    public ushort m_TepeportIdx;

    /// <summary>
    /// NPCID
    /// </summary>
    public uint m_NPCIdx;
}


public enum ENotificationType : byte
{
    Everyday,         //每日
    Odd,              //每周一三五七
    Even,             //每周二四六
    EveryMonday,      //每周一
    EveryTuesday,     //每周二
    EveryWednesday,   //每周三
    EveryThursday,    //每周四
    EveryFriday,      //每周五
    EverySaturday,    //每周六
    EverySunday,      //每周日
    MondayToFriday,   //每周一至周五
    SaturdayAndSunday,//每周六日
    OddWeekEnd,       //每周一三五六七
    EvenWeekEnd,      //每周二四五六七
}

/// <summary>
/// 時分
/// </summary>
public struct HourAndMinute
{
    public byte m_Hour;
    public byte m_Min;
}

/// <summary>
/// 年月日時分 自帶DateTime 拜託用一下吧
/// </summary>
public struct YearToMinute
{
    public ushort m_Year;
    public byte m_Month;
    public byte m_Day;
    public byte m_Hour;
    public byte m_Min;
}

/// <summary>
/// 推播資料
/// </summary>
[StringFileSerializeAttribute]
public struct NotificationData
{
    /// <summary>
    /// 串檔IDX
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 版本 0:全版本 其他依照ELanguage
    /// </summary>
    public byte m_Version;
    /// <summary>
    /// 排序位置
    /// </summary>
    public byte m_Order;
    /// <summary>
    /// 需求等級
    /// </summary>
    public byte m_Lv;
    /// <summary>
    /// 推播條件
    /// </summary>
    public ENotificationType m_Condition;
    /// <summary>
    /// 推播時間
    /// </summary>
    [myatt( 3 )]
    public HourAndMinute[] m_Time;
    /// <summary>
    /// 推播起始時間
    /// </summary>
    public YearToMinute m_Begin;
    /// <summary>
    /// 推播結束時間
    /// </summary>
    public YearToMinute m_End;
    /// <summary>
    /// 標題字串IDX
    /// </summary>
    public uint m_TitleTIdx;
    /// <summary>
    /// 內容字串IDX
    /// </summary>
    public uint m_ContentTIdx;
}


/// <summary>
/// 城戰地圖
/// </summary>
[StringFileSerializeAttribute]
public struct SiegeMapData
{
    /// <summary>
    /// 場景編號
    /// </summary>
    public ushort m_SceneIdx;
    /// <summary>
    /// 城市編號 1.揚州 2.南陽 3.長安
    /// </summary>
    public byte m_CastleType;
    /// <summary>
    /// 場景類型 1.城戰 2.資格賽
    /// </summary>
    public byte m_SceneType;
    /// <summary>
    /// 關聯的攻城區場景編號
    /// </summary>
    public ushort m_FortSceneIdx;
    /// <summary>
    /// 關聯的皇宮區場景編號
    /// </summary>
    public ushort m_PalaceSceneIdx;
}


/// <summary>
/// 活動寶箱串表
/// </summary>

[StringFileSerializeAttribute]
public struct ActBoxData
{
    public byte m_Idx;             //活動寶箱ID
    public uint m_NameTIdx;           //寶箱名稱
    public string m_ImgName;      //寶箱圖示
    public uint m_FuDaiIdx;        //福帶編號
    public uint m_LimitNumTIdx;    //剩餘次數字串
    public uint m_ConditionTIdx;   //條鍵字串
    public byte m_ConditionType;  //兌換條件類型(物品型=0 計時型=1)
    public uint m_Condition;      //兌換條件(物品型=需求物品ID 計時型=記數標記)
    public uint m_CostItemIdx;         //需求物品ID
    public ushort m_CostItemNum;      //兌換需求(物品型=物品數量 計時型=分鐘)
    public ushort m_RewardFlag;   //領獎記數標記
    public byte m_RewardLimit;    //領獎上限次數
}

/// <summary>
/// UI129 連結 ActBoxData 資料串表
/// </summary>
[StringFileSerializeAttribute]
public struct ActBoxContentData
{
    public byte m_Idx;
    [myatt( 5 )]
    public ushort[] m_ContentBoxIDs;
}

/// <summary>
/// 介消串表
/// </summary>
[StringFileSerializeAttribute]
public struct SellData
{
    public ushort m_Idx;             //介消編號
    public ushort m_ContNumFlag;    //記數永標
    public byte m_BuyMaxCount;    //購買次數
    public byte m_RequireFlag;    //要求價格旗標[0.不可貨幣支付 1.可貨幣支付]
    /// <summary>
    /// 介消價格資料[1.遊戲幣 2.黃易幣 3.時空幣]
    /// </summary>
    [myatt(3)]
    public SellCurrencyData[] m_SellCurrencyData;
    /// <summary>
    /// 記數動標 - 購買成功清除
    /// </summary>
    public ushort m_SuccessFlag_Change;
    /// <summary>
    /// 記數永標 - 購買成功清除
    /// </summary>
    public ushort m_SuccessFlag_Fixed;
    public uint m_ItemOne;        //物品A
    public uint m_ItemTwo;        //物品B
    /// <summary>
    /// 需求物品數量，填 0 代表不可使用
    /// </summary>
    public uint m_ItemNeedNum;
    /// <summary>
    /// 需求物品計算公式，填 0 代表不做計算
    /// </summary>
    public byte m_ItemFormulaIndex;
    /// <summary>
    /// 公式計算參數
    /// </summary>
    public ushort m_FormulaParam_1;
    /// <summary>
    /// 公式計算參數
    /// </summary>
    public ushort m_FormulaParam_2;
    public uint m_DescriptionTIdx; //功能描述
    public uint m_FinishTIdx;      //完成系統訊息
    public uint m_UIItemIdx;       //介面顯示物品
    public uint m_TitleTIdx;        //介面Title字串編號
    /// <summary>
    /// 永丹群組類別
    /// </summary>
    public byte m_TakemedicineGroup;
    /// <summary>
    /// 是否能看廣告
    /// </summary>
    public byte m_IsAllow_AD;
    /// <summary>
    /// 廣告觀看次數判斷記數永標
    /// </summary>
    public ushort m_FixedFlag_AD;
    /// <summary>
    /// 廣告次數
    /// </summary>
    public byte m_Count_AD;

}
/// <summary>
/// 介消價格資料
/// </summary>
public struct SellCurrencyData
{
    /// <summary>
    /// 需求貨幣金額，填 0 代表不可使用
    /// </summary>
    public uint m_CurrencyNum;
    /// <summary>
    /// 金額計算公式，填 0 代表不做計算
    /// </summary>
    public byte m_FormulaIndex;
    /// <summary>
    /// 公式計算參數
    /// </summary>
    public ushort m_Param_1;
    /// <summary>
    /// 公式計算參數
    /// </summary>
    public ushort m_Param_2;
}

/// <summary>
/// 城戰塔資料串表
/// </summary>
[StringFileSerializeAttribute]
public struct SiegeTurretData
{
    /// <summary>
    /// 場景編號
    /// </summary>
    public ushort m_SceneIdx;
    /// <summary>
    /// 事件編號
    /// </summary>
    public uint m_EventIdx;
    /// <summary>
    /// 排序編號(1-12)
    /// </summary>
    public byte m_Number;
    /// <summary>
    /// NP區段
    /// </summary>
    public byte m_NPSection;
    /// <summary>
    /// 座標X
    /// </summary>
    public ushort m_X;
    /// <summary>
    /// 座標Y
    /// </summary>
    public ushort m_Y;
    /// <summary>
    /// 塔於地圖中的名稱字串
    /// </summary>
    public uint m_NameTIdx;
    /// <summary>
    /// 所在位置資訊[1.揚州 2.南陽 3.長安]
    /// </summary>
    public byte m_City;
}

/// <summary>
/// 戰鬥服 物品所對映的顏色串表
/// </summary>
[StringFileSerializeAttribute]
public struct DyeingColorData
{
    /// <summary>
    /// 物品編號
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 組織永標 [1~200]
    /// </summary>
    public ushort m_OrgFlagIdx;
    /// <summary>
    /// 部位編號 idx : 第幾個部位 ( 共五個, 依序為:0.武器 1.青龍 2.白虎 3.玄武 4.朱雀 )
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 顏色
    /// </summary>
    public string m_Color;
}

/// <summary>
/// 武器轉換表
/// </summary>
[StringFileSerializeAttribute]
public struct ChangeweaponData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_ChangeIdx;
    /// <summary>
    /// 轉換的物品武器資料陣列
    /// </summary>
    [myatt( 5 )]
    public uint[] m_Ay_ChangeItemIdx;
    /// <summary>
    /// 轉換的物品工具資料陣列
    /// </summary>
    [myatt( 2 )]
    public uint[] m_Ay_ChangeTool;
}

/// <summary>
/// 活動技能串檔
/// </summary>
[StringFileSerializeAttribute]
public struct ActivitySkillData
{
    /// <summary>
    /// buffid
    /// </summary>
    public ushort m_BuffIdx;
    /// <summary>
    /// Icon資料
    /// </summary>
    [myatt( 4 )]
    public ActivitySkillIcon[] m_Ay_IconData;
}
/// <summary>
/// 活動技能icon
/// </summary>
public struct ActivitySkillIcon
{
    /// <summary>
    /// 技能類型
    /// </summary>
    public byte m_IconType;
    /// <summary>
    /// 技能圖編號
    /// </summary>
    public ushort m_IconIdx;
    /// <summary>
    /// 送給Server編號
    /// </summary>
    public ushort m_ForServerNum;
    /// <summary>
    /// 花費的物品
    /// </summary>
    public uint m_SpendItem;
    /// <summary>
    /// 花費物品的數量
    /// </summary>
    public byte m_Count;
    /// <summary>
    /// 消耗的內力
    /// </summary>
    public uint m_CostMP;
    /// <summary>
    /// 消耗的真氣
    /// </summary>
    public uint m_CostSP;
    /// <summary>
    /// 名稱
    /// </summary>
    public uint m_TitleStrID;
    /// <summary>
    /// 內容
    /// </summary>
    public uint m_ContentStrID;
    /// <summary>
    /// 施放技能所需物品
    /// </summary>
    public uint m_SkillNeedEquipment;
}

/// <summary>
/// 合成表
/// </summary>
[StringFileSerializeAttribute]
public struct ComposeData
{
    /// <summary>
    /// 合成編號 (串表編號)
    /// </summary>
    public ushort m_Idx;
    
     /// <summary>
    /// 可能成品
    /// </summary>
    [myatt( 3 )]
    public ComposeProductData[] m_ComposeProduct;
    
    /// <summary>
    /// 頁籤編號
    /// </summary>
    public ushort m_PageID;
    /// <summary>
    /// 製作開啟(艙室效果)
    /// </summary>
    public ushort m_ChamberID;
    /// <summary>
    /// 製作開啟(艙室數值)
    /// </summary>
    public byte m_ChamberValue;
    /// <summary>
    /// 是否可以高效列印
    /// </summary>
    public byte m_AllowHighEffecioncy;
    /// <summary>
    /// 合成材料
    /// </summary>
    [myatt( 6 )]
    public ComposeItemData[] m_Ay_ComposeItemData;
}
/// <summary>
/// 物品表
/// </summary>
[StringFileSerializeAttribute]
public struct ItemData
{
    /// <summary>
    /// 物品索引
    /// </summary>
    public uint m_Idx;                            //物品索引
    /// <summary>
    /// 獲得時轉換為綁定物品ID
    /// </summary>
    public uint m_BindingChangeId;
    /// <summary>
    /// 獲得時轉換為記數標記 - 對應此物品的記數永標編號
    /// </summary>
    public ushort m_ItemFlagIdx;
    /// <summary>
    /// 物品種類
    /// </summary>
    public byte m_Type;                           //物品種類
    /// <summary>
    /// 排列順序
    /// </summary>
    public ushort m_SortOrder;                      //排列順序
    /// <summary>
    /// 物品名稱字串索引
    /// </summary>
    public uint m_Idx_ItemNameText;              //物品名稱字串索引
    /// <summary>
    /// 物品說明字串索引
    /// </summary>
    public uint m_Idx_ItemInfoText;                        //物品說明字串索引
    /// <summary>
    /// 等級限制
    /// </summary>
    public ushort m_Limit_Lv;                        //等級限制
    /// <summary>
    /// 最大等級限制
    /// </summary>
    public ushort m_Limit_LvMax;                     //最大等級限制
    /// <summary>
    /// 提煉編號 - 提煉上階等級
    /// </summary>
    public ushort m_ID_RefiningUpper;                //提煉編號
    /// <summary>
    /// 稀有度
    /// </summary>
    public EItemRarity m_Rarity;                  //物品稀有度
    /// <summary>
    /// 裝備位置
    /// </summary>
    public byte m_EquipPosition;                 //裝備位置
    /// <summary>
    /// Icon流水編號_男
    /// </summary>
    public uint m_ID_Picture;
    /// <summary>
    /// Icon流水編號_女
    /// </summary>
    public uint m_ID_Picture_Female;
    /// <summary>
    /// 時裝、武器外觀ID ( 對應外觀表 )
    /// </summary>
    public ushort m_ID_SpecialAppearance;               //時裝、武器外觀ID ( 對應外觀表 )
    /// <summary>
    /// 時裝、武器外觀ID ( 對應外觀表 )
    /// </summary>
    public ushort m_ID_SpecialAppearance_Female;             //時裝、武器外觀ID ( 對應外觀表 )
    /// <summary>
    /// 掉落物形象 (3D 模型 )
    /// </summary>
    public byte m_ID_3DModelEquip;                   //掉落物形象 (3D 模型 )
    /// <summary>
    /// 套裝編號
    /// </summary>
    public ushort m_ID_Suit;               //套裝編號
    /// <summary>
    /// 是否可堆疊
    /// </summary>
    public bool m_IsCanStack;                        //是否可堆疊
    /// <summary>
    /// 鑲嵌洞數
    /// </summary>
    public byte m_ID_MosaicCount;                    //鑲嵌洞數
    /// <summary>
    /// 重量
    /// </summary>
    public ushort m_Weight;                       //重量
    /// <summary>
    /// 物品價格
    /// </summary>
    public uint m_Price;                          //物品價格
    /// <summary>
    /// 耐久度
    /// </summary>
    public ushort m_Durability;                     //耐久度
    /// <summary>
    /// 使用種類(0=耐久度 1=限時 2=鎖定 3=大罐藥水)
    /// </summary>
    public DurabilityLimitType m_UseType;         //使用種類(0=耐久度 1=限時 2=鎖定 3=大罐藥水)
    /// <summary>
    /// 交易鎖/綁定限制
    /// </summary>
    public bool m_TransactionLock;                //交易鎖/綁定限制
    /// <summary>
    /// 物品限制
    /// </summary>
    public byte m_Limit_Item;                      //物品限制
    /// <summary>
    /// 使用限制 0可主動使用 1不可主動使用
    /// </summary>
    public byte m_Limit_Using;                     //物品使用限制 0可主動使用 1不可主動使用
    /// <summary>
    /// Buff編號/武功編號
    /// </summary>
    public ushort m_ID_BuffOrWugong;                    //Buff編號/武功編號
    /// <summary>
    /// 冷卻時間
    /// </summary>
    public uint m_CoolDownTime;                  //冷卻時間
    /// <summary>
    /// 物品基本屬性 * 5
    /// </summary>
    [myatt(5)]
    public sItemBaseAttribute[] m_Attributes;  //物品基本屬性 * 5
    /// <summary>
    /// 裝備積分倍率
    /// </summary>
    public ushort m_EquipScoreRate;               //裝備積分倍率
    /// <summary>
    /// 裝備積分基礎值
    /// </summary>
    public ushort m_EquipScoreBase;               //裝備積分基礎值
    /// <summary>
    /// 強化編號
    /// </summary>
    public ushort m_ID_Strengthen;                 //強化編號
    /// <summary>
    /// 轉換編號
    /// </summary>
    public ushort m_ID_Change;                     //轉換編號
    /// <summary>
	/// 分解編號
	/// </summary>
	public ushort m_ID_Decompose;                  //分解編號
}

/// <summary>
/// 物品種類對應各種資料(字串Idx、歸屬哪個頁籤...等)
/// </summary>
[StringFileSerializeAttribute]
public struct TabData
{
    /// <summary>
    /// 物品種類
    /// </summary>
    public byte m_ItemKind;

    /// <summary>
    /// 種類名稱字串
    /// </summary>
    public uint m_ItemKindName;

    /// <summary>
    /// 選中預設字串
    /// </summary>
    public uint m_SelectDefaultTextId;

    /// <summary>
    /// 母分類(頁籤)
    /// </summary>
    public byte m_MomTabs;

    /// <summary>
    /// 子分類(頁籤)
    /// </summary>
    public byte m_ChildTabs;

    /// <summary>
    /// 強化功能背包物品顯示
    /// </summary>
    public byte m_StrengthenTabs;

    /// <summary>
    /// 融合功能背包物品顯示
    /// </summary>
    public byte m_MixTabs;

    /// <summary>
    /// 精煉功能背包物品顯示
    /// </summary>
    public byte m_RefiningTabs;

    /// <summary>
    /// 鑲嵌功能背包物品顯示
    /// </summary>
    public byte m_MosaicTabs;

    /// <summary>
    /// 轉換功能背包物品顯示
    /// </summary>
    public byte m_ChangeTabs;

    /// <summary>
    /// 掛機拾取背包物品顯示
    /// </summary>
    public byte m_AutoPickUpTabs;

    /// <summary>
    /// 快捷設定UI016頁籤
    /// </summary>
    public byte m_QuickListTabs;
}

/// <summary>
/// 物品稀有度
/// </summary>
public enum EItemRarity : byte
{
    Non,
    /// <summary>
    /// 白色(一般掉寶物品)
    /// </summary>
    General = 1,
    /// <summary>
    /// 黃色(任務卷軸)
    /// </summary>
    Reel = 2,
    /// <summary>
    /// 橘色(合成物品)
    /// </summary>
    Compound = 3,
    /// <summary>
    /// 綠色(任務物品)
    /// </summary>
    Mission = 4,
    /// <summary>
    /// 紫色(套裝)
    /// </summary>
    SpecialEquip = 5,
    /// <summary>
    ///藍色(較好的掉寶物品)
    /// </summary>
    Batter = 6,
    /// <summary>
    /// 紅色(特殊物品)
    /// </summary>
    Special = 7,
    /// <summary>
    /// 商城物品
    /// </summary>
    MallItem = 8,
}
/// <summary>
/// Icon類型數值
/// </summary>
public enum EIcomType : byte
{
    Non = 0,
    Icon01 = 1,
    Icon02 = 2,
    Icon03 = 3,
    Icon04 = 4,
    Icon05 = 5,
    Icon06 = 6,
    Icon07 = 7,
    Icon08 = 8,
    Icon09 = 9,
    Icon10 = 10,
    Icon11 = 11,
    Icon12 = 12,
}
/// <summary>
/// 耐久度/限時 限制種類
/// </summary>
public enum DurabilityLimitType
{
    /// <summary>
    /// 耐久度
    /// </summary>
    Durability = 0,
    /// <summary>
    /// 可用的時間(剩餘天數)
    /// </summary>
    UseOnTime = 1,
    /// <summary>
    /// 時間到可用(封印天數)
    /// </summary>
    TimeUpUse = 2,
    /// <summary>
    /// 大罐藥水
    /// </summary>
    BigWater = 3,
}

/// <summary>
/// 合成材料
/// </summary>
public struct ComposeItemData
{
    /// <summary>
    /// 材料物品ID
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 材料數量
    /// </summary>
    public uint m_ItemCount;
}

/// <summary>
/// 合成材料 可能成品區
/// </summary>
public struct ComposeProductData
{
    /// <summary>
    /// 成品物品ID
    /// </summary>
    public uint m_ProductIdx;
    /// <summary>
    /// 生成數量
    /// </summary>
    public byte m_ProductAmount;
    /// <summary>
    /// 生成機率
    /// </summary>
    public byte m_ProductPossibility;
}


/// <summary>
/// 合成Tab串表
/// </summary>
[StringFileSerializeAttribute]
public struct ComposeTabsData
{
    /// <summary>
    /// 頁籤編號
    /// </summary>
    public ushort m_Idx;

    /// <summary>
    /// 頁籤字串編號
    /// </summary>
    public uint m_PageStringIdx;

    /// <summary>
    /// 頁籤圖示Icon圖號
    /// </summary>
    public byte m_PageIcon;

    /// <summary>
    /// 解鎖用艙室效果編號
    /// </summary>
    public ushort m_UnLockChamberID;

    /// <summary>
    /// 解鎖用艙室效果數值
    /// </summary>
    public byte m_UnLockChamberValue;

    /// <summary>
    /// 開啟年分
    /// </summary>
    public ushort m_OpenYear;

    /// <summary>
    /// 開啟月
    /// </summary>
    public byte m_OpenMonth;

    /// <summary>
    /// 開啟日
    /// </summary>
    public byte m_OpenDay;

    /// <summary>
    /// 開啟時(幾點)
    /// </summary>
    public byte m_OpenHour;

    /// <summary>
    /// 關閉年分
    /// </summary>
    public ushort m_CloseYear;

    /// <summary>
    /// 關閉月
    /// </summary>
    public byte m_CloseMonth;

    /// <summary>
    /// 關閉日
    /// </summary>
    public byte m_CloseDay;

    /// <summary>
    /// 關閉時(幾點)
    /// </summary>
    public byte m_CloseHour;

}

/// <summary>
/// 斷線訊息資料
/// </summary>
[StringFileSerializeAttribute]
public struct DisconnectData
{
    /// <summary>
    /// 斷線編號
    /// </summary>
    public byte m_ErrorCode;

    /// <summary>
    /// 字串編號
    /// </summary>
    public uint m_TextIdx;
    /// <summary>
    /// 顯示類型(0:中央訊息 1:確認視窗)
    /// </summary>
    public byte m_ShowType;
}

/// <summary>
/// 歷程促銷串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct CourseRewardsData
{
    /// <summary>
    /// 歷程促銷編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 時空幣
    /// </summary>
    public ushort m_Diamond;
    /// <summary>
    /// Google Cashflow 索引
    /// </summary>
    public ushort m_GoogleCashflowIdx;
    /// <summary>
    /// Apple Cashflow 索引
    /// </summary>
    public ushort m_AppleCashflowIdx;
    /// <summary>
    /// Mycard Cashflow 索引
    /// </summary>
    public ushort m_MycardCashflowIdx;
    /// <summary>
    /// Steam Cashflow 索引
    /// </summary>
    public ushort m_SteamCashflowIdx;
    /// <summary>
    /// 立繪 Banner 索引
    /// </summary>
    public byte m_BannerIdx_;
    /// <summary>
    /// 優惠 Banner 索引
    /// </summary>
    public byte m_BannerIdx_2;
    /// <summary>
    /// 優惠數值字串 索引
    /// </summary>
    public uint m_BannerTIdx;
    /// <summary>
    /// 對話框字串 索引
    /// </summary>
    public uint m_TalkTIdx;
    /// <summary>
    /// 物品內容
    /// </summary>
    [myatt( 6 )]
    public CourseItemData[] m_Ay_ItemData;
}

public struct CourseItemData
{
    /// <summary>
    /// 物品編號
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 物品數量
    /// </summary>
    public ushort m_Count;
}

/// <summary>
/// 分解串檔資料
/// </summary>
[StringFileSerializeAttribute]
public struct DecomposeData
{
    /// <summary>
    /// 分解索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 金額公式索引
    /// </summary>
    public ushort m_FormulaIdx_Money;
    /// <summary>
    /// 金額參數
    /// </summary>
    public ushort m_Param_Money;
    /// <summary>
    /// 數量公式索引
    /// </summary>
    public ushort m_FormulaIdx_Count;
    /// <summary>
    /// 機率公式索引
    /// </summary>
    public ushort m_FormulaIdx_Prob;
    /// <summary>
    /// 物品參數陣列
    /// </summary>
    [myatt( 6 )]
    public DecomposeParam[] m_Ay_Param;
}

public struct DecomposeParam
{
    /// <summary>
    /// 數量參數
    /// </summary>
    public ushort m_ValuesParam;
    /// <summary>
    /// 機率參數
    /// </summary>
    public ushort m_ProbParam;
}

/// <summary>
/// 偏性等級串表
/// </summary>
[StringFileSerializeAttribute]
public struct FeatureData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 偏性值( 最大上限 )
    /// </summary>
    public uint m_BiasPoint;
    /// <summary>
    /// 偏性等級
    /// </summary>
    public byte m_BiasLV;
    /// <summary>
    /// 偏性點
    /// </summary>
    public byte m_FactionPoint;
    /// <summary>
    /// 佛解鎖內功
    /// </summary>
    public ushort m_Buddhism;
    /// <summary>
    /// 道解鎖內功
    /// </summary>
    public ushort m_Taoist;
    /// <summary>
    /// 魔解鎖內功
    /// </summary>
    public ushort m_Dvil;
    /// <summary>
    /// 附加屬性_佛
    /// </summary>
    public byte m_BuddhismProp;
    /// <summary>
    /// 屬性數值_佛
    /// </summary>
    public byte m_BuddhismVal;
    /// <summary>
    /// 附加屬性_道
    /// </summary>
    public byte m_TaoistProp;
    /// <summary>
    /// 屬性數值_道
    /// </summary>
    public byte m_TaoistVal;
    /// <summary>
    /// 附加屬性_魔
    /// </summary>
    public byte m_DvilProp;
    /// <summary>
    /// 屬性數值_魔
    /// </summary>
    public byte m_DvilVal;
}

/// <summary>
/// 強化物品表
/// </summary>
[StringFileSerializeAttribute]
public struct EnhanceItemData
{
    /// <summary>
    /// 強化編號
    /// </summary>
    public ushort m_Idx;

    /// <summary>
    /// 強化編號
    /// </summary>
    public uint m_ItemIdx;

    /// <summary>
    /// 使用類型
    /// </summary>
    public byte m_CanUseType;

    /// <summary>
    /// 對應物品類型
    /// </summary>
    public byte m_CanUseKind;

    /// <summary>
    /// 對應稀有度
    /// </summary>
    public byte m_CanUseRarity;

    /// <summary>
    /// 等級限制種類
    /// </summary>
    public byte m_LVLimitType;

    /// <summary>
    /// 可使用的最低等
    /// </summary>
    public ushort m_MinLv;

    /// <summary>
    /// 強可使用的最高等
    /// </summary>
    public ushort m_MaxLv;

    /// <summary>
    /// 綁定效果
    /// </summary>
    public byte m_Binding;

    /// <summary>
    /// 介消編號
    /// </summary>
    public ushort m_Idx_Sell;
}

public enum EventNPCMode : byte
{
    Non = 0,
    NonUse = 1, //不使用
    NotAppear = 2,  //不出現
    MissionWalk = 3,    //ㄧ般npc
    Mission = 4,	//任務NPC
    Dungeon = 5,    //副本NPC
    Fight = 6,	//練功(戰鬥)NPC
    Mentor = 7, //習武NPC
    Weapon = 8, //武器商人
    Armor = 9,	//防具商人
    Item = 10,   //雜貨
    Station = 11,   //驛站
    Warehouse = 12,	//錢莊
    CopyPlayer = 13,   //複製玩家NPC
    Collection = 14,   //採集類
    NotCloseNPC = 15,   //不近看NPC
    FreeActionNPC = 16,   //NPC自主移動專用
    TransPoint = 17,   //傳送點
    Scoring = 18,   //計分型機關不移動型
    Exchange = 19,   //特殊商人
    Sigma = 20,	//綜合商人
    Fraternity = 21,	//幫會總管
    Boat = 22,	//船夫
    RebirthPoint = 23,   //重生點
    AnimalTrainer = 24,   //馴獸師馴化師
    Guard = 34,   //守衛
    MasterPoint = 51,   //練功怪提示點
}

[StringFileSerializeAttribute]
public struct EventMapNPC
{
    public byte Sources;      // 來源類型
    public uint EventIdIndex; // 事件編號的編號
    public byte NameKind;     // 名稱類別
    public uint NPCNum;       // NPC字串
    public EventNPCMode NPCMode;
    public ushort SceneID;
    public ushort PositionX;
    public ushort PositionY;
}


/// <summary>
/// 強化特效表
/// </summary>
[StringFileSerializeAttribute]
public struct EnhanceFXData
{
    /// <summary>
    /// 強化編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 武器強化特效
    /// </summary>
    public ushort m_Idx_WeaponFX;
    /// <summary>
    /// 防具強化特效
    /// </summary>
    public ushort m_Idx_ArmorFX;
    /// <summary>
    /// 飾品強化特效
    /// </summary>
    public ushort m_Idx_Accessory;
}

/// <summary>
/// 強化特效顏色表
/// </summary>
[StringFileSerializeAttribute]
public struct EnhanceColorData
{
    /// <summary>
    /// 顏色 ID 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 顏色色碼
    /// </summary>
    [myatt(8)]
    public string[] m_TColor;
}

/// <summary>
/// 場景屬性表
/// </summary>
[StringFileSerializeAttribute]
public struct SceneAttributeData
{
    /// <summary>
    /// 場景ID
    /// </summary>
    public ushort m_SceneID;
    /// <summary>
    /// 來原場景ID
    /// </summary>
    public ushort m_OirginalSceneID;
    /// <summary>
    /// 分區流水號
    /// </summary>
    public byte m_PartitionSn;
    /// <summary>
    /// 場景名稱字串ID
    /// </summary>
    public uint m_SceneNameID;
    /// <summary>
    /// 跨地圖連結編號
    /// </summary>
    public ushort m_MapLinkID;
    /// <summary>
    /// 小地圖比例尺
    /// </summary>
    public byte m_SmallMapScale;
    /// <summary>
    /// Loading圖
    /// </summary>
    public ushort m_LoadingBG;
    /// <summary>
    /// 場景描述
    /// </summary>
    public uint m_SceneDescribe;
    /// <summary>
    /// 場景高度
    /// </summary>
    public byte m_SceneHeight;
    /// <summary>
    /// RTM編號
    /// </summary>
    public ushort m_RTMID;
    /// <summary>
    /// RTM標記
    /// </summary>
    public ushort m_RTMFlag;
    /// <summary>
    /// 與玩家交易
    /// </summary>
    public bool m_IsTransaction;
    /// <summary>
    /// PVP副本
    /// </summary>
    public bool m_IsPVP;
    /// <summary>
    /// 可PK 場景
    /// </summary>
    public bool m_Allow_PK;
    /// <summary>
    /// 隊伍功能
    /// </summary>
    public byte m_IsTeam;
    /// <summary>
    /// 座騎使用(0: 不可以騎 1:可以騎 2:可以騎 但是不可共乘)
    /// </summary>
    public byte m_SceneRideType;
    /// <summary>
    /// 技能使用
    /// </summary>
    public byte m_AreaType;
    /// <summary>
    /// 物品使用
    /// </summary>
    public bool m_IsItem;
    /// <summary>
    /// 限制物品種類1~5
    /// </summary>
    [myatt(5)]
    public byte[] m_RestrictKindAy;
    /// <summary>
    /// 限制物品1~10
    /// </summary>
    [myatt(10)]
    public uint[] m_RestrictItemAy;
    /// <summary>
    /// 場景檔案
    /// </summary>
    public ushort m_PrefabSName;
    /// <summary>
    /// 場景雜訊 0 = 無, 1 = 有
    /// </summary>
    public byte m_MapNoise;
    /// <summary>
    /// 是否有動畫
    /// </summary>
    public bool m_IsTimeLine;
    /// <summary>
    /// 機關檔案
    /// </summary>
    public ushort m_PrefabGName;
    /// <summary>
    /// 音樂檔名
    /// </summary>
    public ushort m_SceneMusic;
    /// <summary>
    /// 場景音效
    /// </summary>
    public ushort m_SceneSound;
    /// <summary>
    /// 可否開放玩家進入 2.0新增
    /// </summary>
    public ushort m_IsPlayerCanAccess;
    /// <summary>
    /// 可否傳進場景
    /// </summary>
    public byte m_IsEnterAble;
    /// <summary>
    /// 可否傳出場景(含切換分流)
    /// </summary>
    public byte m_IsExitAble;
    /// <summary>
    /// 死亡處罰
    /// </summary>
    public bool m_IsPunish;
    /// <summary>
    /// 原地復活
    /// </summary>
    public bool m_IsRelive;
    /// <summary>
    /// 場景類型
    /// </summary>
    public ESceneType m_SceneType;
    /// <summary>
    /// 場景類型
    /// </summary>
    public ESceneOpenUIMode m_OpenUIMode;
    /// <summary>
    /// 效能制服開關 (0. 關閉 1. 1993、1994 2. 啟用-陣營顏色 3. 啟用-用幫會染色)
    /// </summary>
    public byte m_PerformanceUniformEnable;
    /// <summary>
    /// 離開按鈕開關 (0. 關閉 1. 啟用)
    /// </summary>
    public byte m_OutBtnEnable;
    /// <summary>
    /// 倒數時間開關 (0. 關閉 1. 啟用)
    /// </summary>
    public byte m_TimeEnable;
    /// <summary>
    /// 後製Profile編號
    /// </summary>
    public byte m_PostProcessingProfile;
    /// <summary>
    /// 可否掛機 (0. 關閉 1. 啟用)
    /// </summary>
    public bool m_CanAuto;
    /// <summary>
    /// 限制技能1~5 Modify by Ryan #57974
    /// </summary>
    [myatt(10)]
    public ushort[] m_RestrictSkillAy;
    /// <summary>
    /// 匿名顯示
    /// </summary>
    public byte m_AnonymousDisplay;
    /// <summary>
    /// 關聯場景
    /// </summary>
    public ushort m_RelateID;
    /// <summary>
    /// 場景標記類型
    /// </summary>
    public eMapPointAuthorityType m_SceneMarkType;
}

/// <summary>
/// 場景類型
/// </summary>
public enum ESceneType : byte
{
    /// <summary>
    /// 一般場景
    /// </summary>
    Normal = 0,

    /// <summary>
    /// PVP副本
    /// </summary>
    PVPDungeon = 1,


    /// <summary>
    /// 城市
    /// </summary>
    City = 2,

    /// <summary>
    /// 攻城戰
    /// </summary>
    GuildWar = 3,
    /// <summary>
    /// 天外天
    /// </summary>
    WorldBossWar = 4,
}

/// <summary>
/// 場景開起UI模式
/// </summary>
public enum ESceneOpenUIMode : byte
{
    /// <summary>
    /// 正常
    /// </summary>
    non,
    /// <summary>
    /// 修羅王
    /// </summary>
    ActivityShowLo,
    /// <summary>
    /// 炸彈客
    /// </summary>
    ActivityBoom,
    /// <summary>
    /// 選邊站
    /// </summary>
    ActivityChoose,
}

/// <summary>
/// 標記權限種類
/// </summary>
public enum eMapPointAuthorityType
{
    /// <summary>
    /// (一般地圖使用)標記權限：隊伍成員
    /// </summary>
    Teams = 0,
    /// <summary>
    /// (城戰使用)標記權限：幫會幹部
    /// </summary>
    Guild = 1,
    /// <summary>
    /// (戰場)標記權限：對長可發給同陣營
    /// </summary>
    Leader = 2,
    /// <summary>
    /// (暫時只有炸彈客)事件：顯示寶箱位置
    /// </summary>
    Event = 3,
    /// <summary>
    /// 都沒有
    /// </summary>
    none
}

public struct sItemBaseAttribute
{
    /// <summary>
    /// 基本屬性種類
    /// </summary>
    public EAttributes m_Kind; // 基本屬性種類
    /// <summary>
    /// 基本屬性數值
    /// </summary>
    public ushort m_Value; // 基本屬性數值
}
/// <summary>
/// 屬性效果值
/// </summary>
public enum EAttributes : ushort
{
    AddSTR = 1,     //加-力量
    SubSTR = 2,     //減-力量
    AddAGI = 3,     //加-敏捷
    SubAGI = 4,     //減-敏捷
    AddCON = 5,     //加-體魄
    SubCON = 6,     //減-體魄
    AddINT = 7,     //加-真元
    SubINT = 8,     //減-真元
    AddMEN = 9,     //加-精神
    SubMEN = 10,    //減-精神
    AddLoad = 11,    //加-負重
    SubLoad = 12,    //減-負重

    AddATK = 13,    //加-外功攻擊
    SubATK = 14,    //減-外功攻擊
    AddDEF = 15,    //加-外功防禦
    SubDEF = 16,    //減-外功防禦
    AddSkillATK = 17,    //加-內功攻擊
    SubSkillATK = 18,    //減-內功攻擊
    AddSkillDEF = 19,    //加-內功防禦
    SubSkillDEF = 20,    //減-內功防禦

    AddMaxHurt = 21,    //加-最大傷害
    SubMaxHurt = 22,    //減-最大傷害
    AddCriticalRate = 23,    //加-爆擊率
    SubCriticalRate = 24,    //減-爆擊率
    AddCritical = 25,    //加-爆擊傷害
    SubCritical = 26,    //減-爆擊傷害
    AddUnloading = 27,    //加-卸勁率
    SubUnloading = 28,    //減-卸勁率
    AddHit = 29,    //加-命中
    SubHit = 30,    //減-命中
    AddDodge = 31,    //加-迴避
    SubDodge = 32,    //減-迴避

    AddHP = 33,    //加-氣血上限
    SubHP = 34,    //減-氣血上限
    AddReturnHP = 35,    //加-自動回血
    SubReturnHP = 36,    //減-自動回血
    AddMeditateHP = 37,    //加-打坐回血
    SubMeditateHP = 38,    //減-打坐回血
    AddHPcure = 39,    //加-氣血藥效果
    SubHPcure = 40,    //減-氣血藥效果

    AddMP = 41,    //加-內力上限
    SubMP = 42,    //減-內力上限
    AddReturnMP = 43,    //加-自動回內
    SubReturnMP = 44,    //減-自動回內
    AddMeditateMP = 45,    //加-打坐回內
    SubMeditateMP = 46,    //減-打坐回內
    AddMPcure = 47,    //加-內力藥效果
    SubMPcure = 48,    //減-內力藥效果

    AddSP = 49,    //加-真氣上限
    SubSP = 50,    //減-真氣上限
    AddReturnSP = 51,    //加-回真倍率
    SubReturnSP = 52,    //減-回真倍率
    AddMeditateSP = 53,    //加-打坐回真倍率
    SubMeditateSP = 54,    //減-打坐回真倍率

    AddDP = 55,    //加-罡氣上限
    SubDP = 56,    //減-罡氣上限
    AddReturnDP = 57,    //加-罡氣回復
    SubReturnDP = 58,    //減-自動回罡
    AddMeditateDP = 59,    //加-打坐回罡
    SubMeditateDP = 60,    //減-打坐回罡

    AddATKSpeed = 61,    //加-攻擊速度
    SubATKSpeed = 62,    //減-攻擊速度
    AddMoveSpeed = 63,    //加-移動速度
    SubMoveSpeed = 64,    //減-移動速度
    AddReturnSpeed = 65,    //加-回復速度
    SubReturnSpeed = 66,    //減-回復速度

    AddHurtMagnification = 67,    //加-傷害倍率
    SubHurtMagnification = 68,    //減-傷害倍率
    AddDecDamageMagnification = 69,    //加-減傷倍率
    SubDecDamageMagnification = 70,    //減-減傷倍率
    AddRideSpeed = 71,    //加-座騎速度
    SubRideSpeed = 72,    //減-座騎速度
    AddEXPRate = 73,    //加-經驗增加率
    SubEXPRate = 74,    //減-經驗增加率
    AddSkillPointRate = 75,    //加-武點增加率
    SubSkillPointRate = 76,    //減-武點增加率
    AddDepletion = 77,    //加-化勁
    SubDepletion = 78,    //減-化勁
    AddATKRange = 79,    //加-攻擊範圍
    SubATKRange = 80,    //減-攻擊範圍
    AddBeCure = 81,    //加-受到的治療量
    SubBeCure = 82,    //減-受到的治療量

    AddHPRate = 83,    //加-氣血上限倍率
    SubHPRate = 84,    //減-氣血上限倍率
    AddMPRate = 85,    //加-內力上限倍率
    SubMPRate = 86,    //減-內力上限倍率
    AddHPRecRate = 87,    //加-自動回血倍率
    SubHPRecRate = 88,    //減-自動回血倍率
    AddMPRecRate = 89,    //加-自動回內倍率
    SubMPRecRate = 90,    //減-自動回內倍率

    AddBlade = 91,   //加-刀法傷害倍率
    SubBlade = 92,   //減-刀法傷害倍率
    AddSword = 93,   //加-劍法傷害倍率
    SubSword = 94,   //減-劍法傷害倍率
    AddFight = 95,   //加-拳法傷害倍率
    SubFight = 96,   //減-拳法傷害倍率
    AddSpear = 97,   //加-槍法傷害倍率
    SubSpear = 98,   //減-槍法傷害倍率
    AddStick = 99,   //加-棍法傷害倍率
    SubStick = 100,  //減-棍法傷害倍率

    AddATKRate = 101,   //加-外功攻擊倍率
    SubATKRate = 102,   //減-外功攻擊倍率
    AddDEFRate = 103,   //加-外功防禦倍率
    SubDEFRate = 104,   //減-外功防禦倍率
    AddSkillDEFRate = 105,   //加-內功防禦倍率
    SubSkillDEFRate = 106,   //減-內功防禦倍率

    AddColdDownRate = 107,   //加-冷卻時間倍率
    SubColdDownRate = 108,   //減-冷卻時間倍率

    AddHPDepletionRate = 109,    //加-氣血消耗倍率
    SubHPDepletionRate = 110,    //減-氣血消耗倍率
    AddMPDepletionRate = 111,    //加-內力消耗倍率
    SubMPDepletionRate = 112,    //減-內力消耗倍率
    AddSPDepletionRate = 113,    //加-真氣消耗倍率
    SubSPDepletionRate = 114,    //減-真氣消耗倍率
    AddMeditateMPRate = 115,    //加-打坐回內倍率
    SubMeditateMPRate = 116,    //減-打坐回內倍率

    AddMagAtkRate = 117,      //加-內功攻擊倍率
    SubMagAtkRate = 118,      //減-內功攻擊倍率
    AddAccumulateRate = 119,      //加-集氣時間倍率
    SubAccumulateRate = 120,      //減-集氣時間倍率
    AddMagColdRate = 121,     //加-內功冷卻時間倍率
    SubMagColdRate = 122,	  //減-內功冷卻時間倍率

    AddSPRate = 123,    //加-真氣上限倍率
    SubSPRate = 124,    //減-真氣上限倍率

    AddASpdRate = 125,    //加-攻擊速度倍率
    SubASpdRate = 126,    //減-攻擊速度倍率
    AddMSpdRate = 127,    //加-移動速度倍率
    SubMSpdRate = 128,    //減-移動速度倍率
    AddRSpdRate = 129,    //加-坐騎速度倍率
    SubRSpdRate = 130,    //減-坐騎速度倍率

    AddEnduranceRate = 131,    //加-耐久度倍率
    SubEnduranceRate = 132,    //減-耐久度倍率
    AddItemUseDelay = 133,    //加-物品延遲
    SubItemUseDelay = 134,    //減-物品延遲

    AddSitSkPointRate = 135,    //加-打坐武點倍率
    SubSitSkPointRate = 136,    //減-打坐武點倍率
    AddUseSkPointRate = 137,    //加-武點消耗倍率
    SubUseSkPointRate = 138,    //減-武點消耗倍率

    AddAtkIncHurtRate = 139,    //加-外攻增傷倍率
    SubAtkIncHurtRate = 140,    //減-外攻增傷倍率
    AddMagIncHurtRate = 141,    //加-內攻增傷倍率
    SubMagIncHurtRate = 142,    //減-內攻增傷倍率
    AddAtkDecHurtRate = 143,    //加-外攻減傷倍率
    SubAtkDecHurtRate = 144,    //減-外攻減傷倍率
    AddMagDecHurtRate = 145,    //加-內攻減傷倍率
    SubMagDecHurtRate = 146,    //減-內攻減傷倍率
    AddDirectDamage = 147,    //加-真武傷害
    SubDirectDamage = 148,    //減-真武傷害
    AddItemDropRate = 149,    //加-掉落率
    SubItemDropRate = 150,    //減-掉落率
    AddSellPrice = 151,    //加-物品賣價
    SubSellPrice = 152,    //減-物品賣價
    AddAtkHurtDepletion = 153,    //加-外攻化傷
    SubAtkHurtDepletion = 154,    //減-外攻化傷
    AddMagHurtDepletion = 155,    //加-內攻化傷
    SubMagHurtDepletion = 156,    //減-內攻化傷

    AddWpSuccRate = 157, //加-武器合成機率
    SubWpSuccRate = 158, //減-武器合成機率
    AddAmSuccRate = 159, //加-防具合成機率
    SubAmSuccRate = 160, //減-防具合成機率
    AddFusionSPDRate = 161, //加-合成物品速度倍率
    SubFusionSPDRate = 162, //減-合成物品速度倍率
    AddBuffTimeRate = 163, //加-內功增益狀態時間
    SubBuffTimeRate = 164, //減-內功增益狀態時間
    AddBuffTime = 165, //加-內功增益狀態時間
    SubBuffTime = 166, //減-內功增益狀態時間
    AddSkillUseEnergy = 167, //加-外功消耗內力
    SubSkillUseEnergy = 168, //減-外功消耗內力
    AddSkillUseEnergyRate = 169, //加-外功消耗內力倍率
    SubSkillUseEnergyRate = 170, //減-外功消耗內力倍率
    AddBuffUseEnergy = 171, //加-內功消耗內力
    SubBuffUseEnergy = 172, //減-內功消耗內力
    AddBuffUseEnergyRate = 173, //加-內功消耗內力倍率
    SubBuffUseEnergyRate = 174, //減-內功消耗內力倍率
    AddSpecialDamage = 175, //加-經脈攻擊
    SubSpecialDamage = 176, //減-經脈攻擊
    AddRestSkillPointRate = 177, //加-打坐武點倍率
    SubRestSkillPointRate = 178, //減-打坐武點倍率
    AddSpecialDefend = 179, //加-經脈防禦
    SubSpecialDefend = 180, //減-經脈防禦
    AddEquipProtect = 181, //加-裝備維護
    SubEquipProtect = 182, //減-裝備維護
    AddSingleBloodDistance = 194, //加-增加單人補血武功有效距離 (只有加沒有減)
}

/// <summary>
/// 一般套裝表
/// </summary>
[StringFileSerializeAttribute]
public struct SuitData
{
    public ushort m_Idx; //套裝ID
    public uint SuitNameID; //套裝名
    public byte SuitCount; //全套件數
    [myatt(5)]
    public uint[] ItemID;         //套裝物品編號
    [myatt(5)]
    public uint[] ItemIDA;        //套裝物品編號A
    public uint SpecialItem;    //特殊物品 EX:戰騎
    [myatt(6)]
    public SuitEffect[] EffectData;     //套裝效果
}

/// <summary>
/// 一般套裝效果
/// </summary>
public struct SuitEffect
{
    /// <summary>
    /// 效果開啟件數
    /// </summary>
    public byte SuitCount;
    /// <summary>
    /// 基本屬性
    /// </summary>
    public sItemBaseAttribute Attribute;
}

/// <summary>
/// 強化數值
/// </summary>
[StringFileSerializeAttribute]
public struct EnhanceData
{
    /// <summary>
    /// 強化編號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 物品強化增加屬性_基本
    /// </summary>
    [myatt(1)]
    public sEnhanceAttribute m_EnhanceAttribute_Base;
    /// <summary>
    /// 物品強化增加屬性_附加
    /// </summary>
    [myatt(1)]
    public sEnhanceAttribute m_EnhanceAttribute_Add;
    /// <summary>
    /// +7獎勵屬性 * 2
    /// </summary>
    [myatt(2)]
    public sItemBaseAttribute[] m_RewardAttribute_7;
    /// <summary>
    /// +9獎勵屬性 * 2
    /// </summary>
    [myatt(2)]
    public sItemBaseAttribute[] m_RewardAttribute_9;
    /// <summary>
    /// +10獎勵屬性 * 5
    /// </summary>
    [myatt(5)]
    public sItemBaseAttribute[] m_RewardAttribute_10;
    /// <summary>
    /// 開光額外屬性
    /// </summary>
    public EAttributes m_AddAttributeKind;
    /// <summary>
    /// +11~+15強化額外屬性數值
    /// </summary>
    [myatt(5)]
    public ushort[] m_AddAttributeValue_11_15;
    /// <summary>
    /// +15獎勵屬性 * 6
    /// </summary>
    [myatt(6)]
    public sItemBaseAttribute[] m_RewardAttribute_15;
}

/// <summary>
/// 物品強化增加屬性
/// </summary>
public struct sEnhanceAttribute
{
    /// <summary>
    /// 基本屬性
    /// </summary>
    public EAttributes AttributeKind;
    /// <summary>
    /// +1~+15強化增加屬性數值
    /// </summary>
    [myatt(15)]
    public ushort[] EnhanceAttributeValue;
}

/// <summary>
/// 永丹效果
/// </summary>
[StringFileSerializeAttribute]
public struct TakemedicineData
{
    public ushort m_Idx;               //流水號
    public uint m_ItemID;              //永丹物品ID
    public uint m_ItemInfo;            //永丹效果說明
    public byte m_Origin;              //出處ID
    public byte m_MedicineGroup;       //永丹群組類別
    public uint m_StoreID;             //永丹商城ID
    public uint m_BindedMedicineID;    //綁定永丹ID
}

/// <summary>
/// 教學步驟串檔
/// </summary>
[StringFileSerializeAttribute]
public struct TeachingStepsData
{
    /// <summary>
    /// 教學步驟的 Index
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 被教學介面的 Index
    /// </summary>
    public byte m_UIIndex;
    /// <summary>
    /// 教學類型
    /// </summary>
    public byte m_ETeachType;
    /// <summary>
    /// 教學元件類型
    /// </summary>
    public byte m_ETeachComponentType;
    /// <summary>
    /// 教學元件名稱 教學類型為打字機模式時 此資料對應字串
    /// </summary>
    public string m_TeachUnitName;
    /// <summary>
    /// 教學類型為 ETeachType.TypingMeching 時的標題字串ID
    /// </summary>
    public uint m_TeachTitleName;
    /// <summary>
    /// 教學步驟 在此教學內的順序
    /// </summary>
    public byte m_TeachUnitIndex;
    /// <summary>
    /// 延遲秒數
    /// </summary>
    public byte m_DelaySec;
    /// <summary>
    /// 可否略過
    /// </summary>
    public byte m_IsCanSkip;
    /// <summary>
    /// 鎖定遮罩圖片樣式
    /// </summary>
    public byte m_TeachFocusMaskImageType;
    /// <summary>
    /// 教學說明位置
    /// </summary>
    public byte m_TeachInfoPosition;
    /// <summary>
    /// 教學說明底板樣式
    /// </summary>
    public byte m_TeachInfoFrameType;
    /// <summary>
    /// 教學說明顯示的 NPCID
    /// </summary>
    public uint m_TeachInfoNPCID;
    /// <summary>
    /// 教學字串 ID
    /// </summary>
    public uint m_TeachStringID;
    /// <summary>
    /// 教學語音 ID
    /// </summary>
    public ushort m_TeachSoundID;
    /// <summary>
    /// 連接步驟
    /// </summary>
    public ushort m_NextStepID;
    /// <summary>
    /// 教學後要開啟的介面(編號)
    /// </summary>
    public byte m_OpenUIID;
    /// <summary>
    /// 教學要檢查的永標
    /// </summary>
    public ushort m_FlagID;
}

/// <summary>
/// 生活技能字串資訊
/// </summary>
[StringFileSerializeAttribute]
public struct LivingSkillinfoData
{
    public byte m_Idx;                 //流水號
    public byte m_LivindSkillID;       //生活技能ID
    public uint m_NameIDx;             //名稱字串
    public byte m_HasLevel;            //是否有等級
    public ushort m_UnLockFlag;        //解鎖永標
}

/// <summary>
/// 頭像串表
/// </summary>
[StringFileSerializeAttribute]
public struct HeadIconData
{
    /// <summary>
    ///頭像ID
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    ///顯示順序
    /// </summary>
    public ushort m_ShowPriority;
    /// <summary>
    ///取得條件類型 0 ~ 3
    /// </summary>
    public byte m_QualifyType;
    /// <summary>
    ///條件_標記ID 永標ID 計數永標ID
    /// </summary>
    public ushort m_Qualif_MarkID;
    /// <summary>
    ///條件_計數值 永標 計數值
    /// </summary>
    public uint m_Qualif_Count;
    /// <summary>
    ///醒目提示 0=無 , 1=困難 , 2=活動, 3=付費
    /// </summary>
    public byte m_HightLightHint;
    /// <summary>
    ///獲得字串說明
    /// </summary>
    public uint m_GetStringID;
    /// <summary>
    ///2D圖檔名
    /// </summary>
    public string m_2DImageName;
    /// <summary>
    ///shader檔名 0=無
    /// </summary>
    public string m_Shader;
}

/// <summary>
/// 頭框串表 (頭像外框)
/// </summary>
[StringFileSerializeAttribute]
public struct HeadFrameData
{
    /// <summary>
    ///頭框ID
    /// </summary>
    public byte m_Idx;
    /// <summary>
    ///顯示順序
    /// </summary>
    public ushort m_ShowPriority;
    /// <summary>
    ///取得條件類型 0 ~ 3
    /// </summary>
    public byte m_QualifyType;
    /// <summary>
    ///條件_標記ID 永標ID 計數永標ID
    /// </summary>
    public ushort m_Qualif_MarkID;
    /// <summary>
    ///條件_計數值 永標 計數值
    /// </summary>
    public uint m_Qualif_Count;
    /// <summary>
    ///醒目提示 0=無 , 1=困難 , 2=活動, 3=付費
    /// </summary>
    public byte m_HightLightHint;
    /// <summary>
    ///獲得字串說明
    /// </summary>
    public uint m_GetStringID;
    /// <summary>
    ///2D圖檔名
    /// </summary>
    public string m_2DImageName;
    /// <summary>
    ///shader檔名 0=無
    /// </summary>
    public string m_Shader;
}

/// <summary>
/// 時光機Data
/// </summary>
[StringFileSerializeAttribute]
public struct TimeMachineData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 艙房編號
    /// </summary>
    public byte m_RoomIdx;
    /// <summary>
    /// 艙房名稱字串編號
    /// </summary>
    public uint m_RoomNameIdx;
    /// <summary>
    /// 艙房說明字串編號
    /// </summary>
    public uint m_RoomDescribeIdx;
    /// <summary>
    /// 艙室階段
    /// </summary>
    public byte m_FixStep;
    /// <summary>
    /// 修復狀態
    /// </summary>
    public byte m_FixState;
    /// <summary>
    /// 修復進度 % (千分位)
    /// </summary>
    public ushort m_FixPercentage;
    /// <summary>
    /// 解鎖順序
    /// </summary>
    public byte m_UnlockOrder;
    /// <summary>
    /// 介面引導任務動標
    /// </summary>
    public ushort m_GuideMoveFlag;
    /// <summary>
    /// 啟動/修復/改造需求材料資料
    /// </summary>
    [myatt(3)]
    public RoomFixItem[] m_RoomFixItemData;
    /// <summary>
    /// 艙室效果資料
    /// </summary>
    [myatt(3)]
    public RoomEffect[] m_RoomFixEffectData;
    /// <summary>
    /// 晶片效果資料
    /// </summary>
    [myatt(3)]
    public RoomEffect[] m_RoomChipEffectData;
    /// <summary>
    /// 艙室icon
    /// </summary>
    public byte m_RoomIconIdx;
    /// <summary>
    /// 艙室圖號(外)
    /// </summary>
    public byte m_OutSideImageNumber;
    /// <summary>
    /// 艙室圖號(內)
    /// </summary>
    public ushort m_InSideImageNumber;
    /// <summary>
    /// 艙室開啟介面編號
    /// </summary>
    public byte m_OpenUIIdx;
}

public struct RoomFixItem
{
    /// <summary>
    /// 修復需求材料編號
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 修復需求材料數量
    /// </summary>
    public ushort m_Num;
}

public struct RoomEffect
{
    /// <summary>
    /// 時光機艙室/晶片時光機艙室效果記數永標種類
    /// </summary>
    public ushort m_EffectKind;
    /// <summary>
    /// 時光機艙室/晶片時光機艙室效果記數永標數值
    /// </summary>
    public ushort m_EffectValue;
}

/// <summary>
/// 時光機艙室效果對照表
/// </summary>
[StringFileSerializeAttribute]
public struct TMRoomEffectData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 效果記數永標
    /// </summary>
    public byte m_EffectFlag;
    /// <summary>
    /// 效果名稱字串
    /// </summary>
    public uint m_EffectName;
    /// <summary>
    /// 效果顯示型態
    /// </summary>
    public byte m_EffectKind;
    /// <summary>
    /// 標記數值
    /// </summary>
    public byte m_EffectValue;
    /// <summary>
    /// 效果描述字串
    /// </summary>
    public uint m_EffectDescribe;
    /// <summary>
    /// 數值單位[1.純數值 2.數值代入 3.純描述]
    /// </summary>
    public byte m_ValueKind;
    /// <summary>
    /// 小數點顯示(小數點類型)
    /// </summary>
    public byte m_Pointkind;
    /// <summary>
    /// 顯示百分號
    /// </summary>
    public bool m_ShowPercentage;
    /// <summary>
    /// 升級預覽時是否顯示變化
    /// </summary>
    public bool m_ShowChange;
}

/// <summary>
/// 主選單資料
/// </summary>
[StringFileSerializeAttribute]
public struct MenuData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 按鈕 Group 編號
    /// </summary>
    public byte m_GroupID;
    /// <summary>
    /// 按鈕 字串編號
    /// </summary>
    public uint m_ButtonTextID;
    /// <summary>
    /// 按鈕 Icon 名稱
    /// </summary>
    public string m_ButtonIconName;
    /// <summary>
    /// 選單 UI 開關機制類別
    /// </summary>
    public byte m_MenuActiveKind;
    /// <summary>
    /// 按鈕開啟的 UIIndex
    /// </summary>
    public byte m_OpenUIIndex;
    /// <summary>
    /// 按鈕開啟的 UI 頁籤編號
    /// </summary>
    public byte m_OpenTabIndex;
    /// <summary>
    /// 按鈕開啟的 UI 開啟參數
    /// </summary>
    public byte m_OpenParam;
    /// <summary>
    /// 開放等級
    /// </summary>
    public byte m_Lv;
    /// <summary>
    /// 開放永標
    /// </summary>
    public ushort m_Flag;
    /// <summary>
    /// 特殊開放檢查
    /// </summary>
    public ushort m_SpecialOpeningInspection;
    /// <summary>
    /// 未開放時 是否要隱藏
    /// </summary>
    public bool m_IsNeedHideWhenNotOpen;
    /// <summary>
    /// 未開放時的提示字串編號
    /// </summary>
    public uint m_NotOpenPromptTextID;
    /// <summary>
    /// m_MenuActiveKind 為事件時 S 寫死事件 ID
    /// </summary>
    public byte m_EvtID;
}
/// <summary>
/// 武裝升級(等級)
/// </summary>
[StringFileSerializeAttribute]
public struct LevelData
{
    /// <summary>
    /// 等級
    /// </summary>
    public ushort m_Level;
    /// <summary>
    /// 需求經驗
    /// </summary>
    public UInt64 m_ExpNeed;
    /// <summary>
    /// 最低維持能量
    /// </summary>
    public uint m_EnergyNeed;
    /// <summary>
    /// 升級提升效果
    /// 效果數值
    /// </summary>
    [myatt( 5 )]
    public LevelUpgradeEffect[] m_UpgradeEffect;
    /// <summary>
    /// 升級獲得戰技點
    /// </summary>
    public uint m_SkillGet;
    /// <summary>
    /// 副本經驗基數( 每6秒獲得經驗)
    /// </summary>
    public uint m_DungeonExp;
    /// <summary>
    /// 副本武點基數( 每6秒獲得武點)
    /// </summary>
    public ushort m_DungeonSkillPoint;
    /// <summary>
    /// 遊戲幣基數
    /// </summary>
    public ushort m_CurrencyBase;
    /// <summary>
    /// 黃易幣基數
    /// </summary>
    public ushort m_HECurrencyBase;
}
public struct LevelUpgradeEffect
{
    /// <summary>
    /// 升級提升效果
    /// </summary>
    public ushort m_UpgradeEff;
    /// <summary>
    /// 升級效果數值
    /// </summary>
    public ushort m_UpgradeValue;
}

/// <summary>
/// 能量礦脈
/// </summary>
[StringFileSerializeAttribute]
public struct EnergyMineData
{
    /// <summary>
    /// 礦脈編號
    /// </summary>
    public ushort m_MineID;
    /// <summary>
    /// 艙室第幾階段解鎖
    /// </summary>
    public byte m_UnlockLevel;
    /// <summary>
    /// 礦脈所在地圖
    /// </summary>
    public ushort m_MineMap;
    /// <summary>
    /// 所在座標X
    /// </summary>
    public ushort m_PosX;
    /// <summary>
    /// 所在座標Y
    /// </summary>
    public ushort m_PosY;
    /// <summary>
    /// 能量獲取效率
    /// </summary>
    public uint m_EnergyEfficiency;
    /// <summary>
    /// 礦脈類型
    /// </summary>
    public byte m_MineType;
    /// <summary>
    /// 啟動前置永標
    /// </summary>
    public ushort m_NeedFlag;
    /// <summary>
    /// 觸發不產NPC事件
    /// </summary>
    public uint m_TriggerEvent;
    /// <summary>
    /// 礦脈已啟動永標
    /// </summary>
    public ushort m_ActiveFlag;
    /// <summary>
    /// 雷達顯示用任務動標
    /// </summary>
    public ushort m_RadarDynMark;
}

/// <summary>
/// 探索日誌獎勵資料
/// </summary>
[StringFileSerializeAttribute]
public struct ExplorePoint_Prize
{
    /// <summary>
    /// 獎勵編號(流水號)
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 獎勵分數
    /// </summary>
    public uint m_RewardPoints;
    /// <summary>
    /// 領獎永標(一般永標)
    /// </summary>
    public ushort m_GetRewardFlag;
    /// <summary>
    /// 獎勵種類
    /// 1.時空晶鑽
    /// 2.物品
    /// 後面有沒有在加牡災
    /// </summary>
    public byte m_RewardKind;
    /// <summary>
    /// 物品編號
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 數量
    /// </summary>
    public ushort m_Amount;
}

/// <summary>
/// 任務獎勵
/// </summary>
[StringFileSerializeAttribute]
public struct QuestPrize
{
    /// <summary>
    /// 任務章節編號
    /// </summary>
    public ushort m_MissionChapter;
    /// <summary>
    /// 開放等級
    /// </summary>
    public ushort m_OpenLevel;
    /// <summary>
    /// 前置永標
    /// </summary>
    public ushort m_OpenFlag;
    /// <summary>
    /// 章節名稱
    /// </summary>
    public uint m_ChapterName;
    /// <summary>
    /// 章節大綱
    /// </summary>
    public uint m_ChapterInfo;
    /// <summary>
    /// 章節圖片
    /// </summary>
    public ushort m_ChampterImg;
    /// <summary>
    /// 領獎永標
    /// </summary>
    public ushort m_PrizeFlag;
    /// <summary>
    /// 獲得積分
    /// </summary>
    public ushort m_ReceivePoints;
    /// <summary>
    /// 獎勵種類
    /// 獎勵編號
    /// 數值
    /// </summary>
    [myatt(6)]
    public PrizeGroup[] m_UpgradeEffect;
}
public struct PrizeGroup
{
    /// <summary>
    /// 獎勵種類
    /// </summary>
    public byte m_Kind;
    /// <summary>
    /// 獎勵編號
    /// </summary>
    public uint m_Idx;
    /// <summary>
    /// 數值
    /// </summary>
    public ushort m_Value;
}
/// <summary>
/// 物品組合資料
/// </summary>
[StringFileSerializeAttribute]
public struct CombineItem
{
    /// <summary>
    /// 標的物 ID
    /// </summary>
    public uint m_TargetItemID;
    /// <summary>
    /// Hint植入字串編號 / 要填入Hint標題的組合類型字串編號[開鎖/組合]
    /// </summary>
    public uint m_KindStringID;
    /// <summary>
    /// 需求材料資訊
    /// </summary>
    [myatt(5)]
    public CombineNeedItemInfo[] m_CombineNeedItemInfo;
}
public struct CombineNeedItemInfo
{
    /// <summary>
    /// 組合物品需求材料編號
    /// </summary>
    public uint m_NeedItemID;
    /// <summary>
    /// 組合物品需求材料數量
    /// </summary>
    public ushort m_NeedItemNum;
}

/// <summary>
/// 快速發話訊息
/// </summary>
[StringFileSerializeAttribute]
public struct QuickMessageData
{
    /// <summary>
    /// 流水號 ID
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 字串ID
    /// </summary>
    public uint m_SentenceId;
}

/// <summary>
/// 快速表情
/// </summary>
[StringFileSerializeAttribute]
public struct EmojiData
{
    /// <summary>
    /// 流水號 ID
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 解鎖永標ID
    /// </summary>
    public ushort m_UnlockStaticFlag;
    /// <summary>
    /// 表情檔案
    /// </summary>
    public string m_FileName;

}
/// <summary>
/// 寵物合成資料
/// </summary>
[StringFileSerializeAttribute]
public struct PetFuse
{
    /// <summary>
    /// 融合編號
    /// </summary>
    public ushort m_FuseID;
    /// <summary>
    /// 稀有寵物出現標示種類
    /// </summary>
    public byte m_RarePetShowIcon;
    /// <summary>
    /// 特定配方 寵物編號 1~3
    /// </summary>
    [myatt(3)]
    public ushort[] m_SpecialRecipePet;
    /// <summary>
    /// 特定配方 道具編號 4~6
    /// </summary>
    [myatt(3)]
    public uint[] m_SpecialRecipeItem;
    /// <summary>
    /// 融合需求貨幣種類
    /// </summary>
    public ushort m_NeedCostType;
    /// <summary>
    /// 融合需求貨幣數量
    /// </summary>
    public uint m_NeedCostNumber;
    /// <summary>
    /// 融合需求骨架物品編號
    /// </summary>
    public uint m_SkeletonID;
    /// <summary>
    /// 總機率
    /// </summary>
    public ushort m_TotalChanceRate;
    /// <summary>
    /// 抽選福袋編號 & 機率
    /// </summary>
    [myatt(10)]
    public FusePetLottery[] m_Lottery;
}

public struct FusePetLottery
{
    /// <summary>
    /// 抽選福袋編號
    /// </summary>
    public uint m_NeedItemID;
    /// <summary>
    /// 抽選機率
    /// </summary>
    public ushort m_NeedItemNum;
}

/// <summary>
/// 神秘商人
/// </summary>
[StringFileSerializeAttribute]
public struct MysticShopData
{
    /// <summary>
    /// 神秘商人IDX(流水號 ID)
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 商人類型: 0=神秘商人, 1=收藏家
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 刷新計數標記
    /// 0=不可刷新
    /// </summary>
    public ushort m_FlagStaticNum; 
    /// <summary>
    /// 免費刷新次數
    /// </summary>
    public byte m_FreeRefreshTimes;
    /// <summary>
    /// 刷新消耗時空幣(鑽石): 實際消耗公式=(記數次數-2)*[刷新消耗時空幣]
    /// </summary>
    public byte m_DiamondCost;
    /// <summary>
    /// 商人名稱字串
    /// </summary>
    public uint m_NPCnameIdx;
    /// <summary>
    /// 商人對白字串
    /// </summary>
    public uint m_NPCcontentIdx;
    /// <summary>
    /// 商人立繪
    /// </summary>
    public string m_NPCimage;
}

/// <summary>
/// 神秘商品
/// </summary>
[StringFileSerializeAttribute]
public struct MysticItemData
{
    /// <summary>
    /// 商品IDX(流水號 ID)
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 買過檢查一般永標: 0=不檢查, 無對應永標則重Roll
    /// </summary>
    public ushort m_FlagStatic;
    /// <summary>
    /// 可重複買: 0=不可重複買, 1=可重複買
    /// </summary>
    public byte m_RepeatType;
    /// <summary>
    /// 購買貨幣種類: 0=無, 1=遊戲幣, 2=黃易幣, 3=時空幣, >1000=物品
    /// </summary>
    public uint m_CurrencyType;
    /// <summary>
    /// 購買價格
    /// </summary>
    public uint m_Price;
    /// <summary>
    /// 收藏家貨幣種類: 0=無, 1=遊戲幣, 2=黃易幣, 3=時空幣, >1000=物品
    /// </summary>
    public uint m_CollectorCurrencyType;
    /// <summary>
    /// 收藏家兌換需求(m_CollectorCurrencyType 需求數量)
    /// </summary>
    public uint m_CollectorExchangeNeeds;
}

/// 隨機技能群組資料
/// </summary>
[StringFileSerializeAttribute]
public struct RandomSkill
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;

    /// <summary>
    /// 隨機群組編號
    /// </summary>
    public ushort m_RandomGruopSerialNum;

    /// <summary>
    /// 總機率
    /// </summary>
    public ushort m_TotalProbability;

    /// <summary>
    /// 技能編號
    /// </summary>
    public ushort m_SkillID;

    /// <summary>
    /// 機率
    /// </summary>
    public ushort m_Probability;
}

/// <summary>
/// 兌換商店
/// </summary>
[StringFileSerializeAttribute]
public struct ExchangeShopData
{
    /// <summary>
    /// 兌換商店編號
    /// </summary>
    public ushort m_ShopID;
    /// <summary>
    /// 功能選單開啟是否顯示
    /// </summary>
    public byte m_IsMenuShow;
    /// <summary>
    /// 商店標題字串
    /// </summary>
    public uint m_TitleString;
    /// <summary>
    /// 商店標題圖示
    /// </summary>
    public string m_TitleImage;
    /// <summary>
    /// 商店形象圖檔
    /// </summary>
    public string m_ShopImage;
    /// <summary>
    /// NPC 對白字串
    /// </summary>
    public uint m_NPCTalkString;
    /// <summary>
    /// 問號說明字串
    /// </summary>
    public uint m_QuestionString;
    /// <summary>
    /// 活動引導 Campaign IDX
    /// </summary>
    public ushort m_CampaignIDX;
    /// <summary>
    /// 類別頁籤資料
    /// </summary>
    [myatt(10)]
    public CategoryTab[] m_CategoryTabData;
    /// <summary>
    /// 顯示貨幣資料
    /// </summary>
    [myatt(3)]
    public ShowCurrency[] m_ShowCurrencyData;
}

public struct CategoryTab
{
    /// <summary>
    /// 類別頁籤字串
    /// </summary>
    public uint m_TabString;
    /// <summary>
    /// 類別頁籤圖示
    /// </summary>
    public string m_TabImage;
}

public struct ShowCurrency
{
    /// <summary>
    /// 類別頁籤字串
    /// </summary>
    public byte m_ShowCurrency;
}

/// <summary>
/// 兌換物品
/// </summary>
[StringFileSerializeAttribute]
public struct ExchangeItemData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 兌換商店編號
    /// </summary>
    public ushort m_ShopID;
    /// <summary>
    /// 類別頁籤
    /// </summary>
    public byte m_CategoryTab;
    /// <summary>
    /// 兌換物品編號
    /// </summary>
    public uint m_ItemID;
    /// <summary>
    /// 兌換物品數量
    /// </summary>
    public byte m_ItemQuantity;
    /// <summary>
    /// 兌換限制標記 ID
    /// </summary>
    public ushort m_LimitMarkID;
    /// <summary>
    /// 兌換限制類型
    /// </summary>
    public byte m_LimitType;
    /// <summary>
    /// 兌換限制次數
    /// </summary>
    public byte m_LimitCount;
    /// <summary>
    /// 消耗需求資料
    /// </summary>
    [myatt(3)]
    public ConsumeData[] m_ConsumeData;
}
public struct ConsumeData
{
    /// <summary>
    /// 需求消耗類型
    /// </summary>
    public byte m_NeedType;
    /// <summary>
    /// 需求物品 ID
    /// </summary>
    public uint m_NeedItemID;
    /// <summary>
    /// 需求記數永標 ID
    /// </summary>
    public ushort m_NeedMarkID;
    /// <summary>
    /// 需求消耗數量
    /// </summary>
    public uint m_NeedQuantity;
}

/// <summary>
/// 兌換條件
/// </summary>
[StringFileSerializeAttribute]
public struct ExchangeConditionData
{
    /// <summary>
    /// 條件編號
    /// </summary>
    public ushort m_ConditionID;
    /// <summary>
    /// 開始年
    /// </summary>
    public ushort m_StartYear;
    /// <summary>
    /// 開始月
    /// </summary>
    public byte m_StartMonth;
    /// <summary>
    /// 開始日
    /// </summary>
    public byte m_StartDay;
    /// <summary>
    /// 開始時
    /// </summary>
    public byte m_StartHour;
    /// <summary>
    /// 結束年
    /// </summary>
    public ushort m_EndYear;
    /// <summary>
    /// 結束月
    /// </summary>
    public byte m_EndMonth;
    /// <summary>
    /// 結束日
    /// </summary>
    public byte m_EndDay;
    /// <summary>
    /// 結束時
    /// </summary>
    public byte m_EndHour;
    /// <summary>
    /// 玩家等級範圍下限
    /// </summary>
    public ushort m_LvLowerLimit;
    /// <summary>
    /// 玩家等級範圍上限
    /// </summary>
    public ushort m_LvUpperLimit;
    /// <summary>
    /// 一般永標
    /// </summary>
    public ushort m_MarkID;
    /// <summary>
    /// 兌換商店編號
    /// </summary>
    public ushort m_ShopID;
}

/// <summary>
/// 寵物派遣資料
/// </summary>
[StringFileSerializeAttribute]
public struct PetMission
{
    /// <summary>
    /// 派遣任務編號
    /// </summary>
    public ushort m_MissionID;
    /// <summary>
    /// 派遣任務圖編號
    /// </summary>
    public ushort m_FileName;
    /// <summary>
    /// 派遣任務標題字串編號
    /// </summary>
    public uint m_TitleStringID;
    /// <summary>
    /// 派遣任務描述字串編號
    /// </summary>
    public uint m_DetailStringID;
    /// <summary>
    /// 派遣種類限制
    /// </summary>
    public byte m_PetKindRequire;
    /// <summary>
    /// 派遣星等最低需求
    /// </summary>
    public byte m_PetLevelRequire;
    /// <summary>
    /// 派遣寵物數量上限
    /// </summary>
    public byte m_PetNumberLimit;
    /// <summary>
    /// 派遣需求時間
    /// </summary>
    public ushort m_NeedTime;
    /// <summary>
    /// 派遣基本獎勵掉寶編號
    /// </summary>
    public ushort m_BasicItemID;
    /// <summary>
    /// 派遣額外掉落
    /// </summary>
    [myatt(2)]
    public MissionPetExtraReward[] m_ExtraDrops;

}

public struct MissionPetExtraReward
{
    /// <summary>
    /// 派遣戰力獎勵 掉寶編號
    /// </summary>
    public ushort m_ItemID;
    /// <summary>
    /// 戰力獎勵需求
    /// </summary>
    public ushort m_PowerRequire;
}

/// <summary>
/// 強化機率表
/// </summary>
[StringFileSerializeAttribute]
public struct EnhanceProbData
{
    /// <summary>
    /// 強化機率ID
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 對應物品類型
    /// </summary>
    public byte m_CanUseKind;
    /// <summary>
    /// 對應強化等級
    /// </summary>
    public byte m_CanUseEnhanceLV;
    /// <summary>
    /// 機率 - 爆裝/退裝
    /// </summary>
    public byte m_Broken;
    /// <summary>
    /// 機率 - 無損
    /// </summary>
    public byte m_Safe;
}

/// <summary>
/// 兌換卡表
/// </summary>
[StringFileSerializeAttribute]
public struct ExchangeCardData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public uint m_Idx;
    /// <summary>
    /// 種類
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 數值
    /// </summary>
    public ushort m_Value;
    /// <summary>
    /// 獎勵
    /// </summary>
    [myatt(15)]
    public uint[] m_TReward;
}

/// <summary>
/// 幫會領地資料
/// </summary>
[StringFileSerializeAttribute]
public struct GuildDomainData
{
    /// <summary>
    /// 索引
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 內文描述字串ID
    /// </summary>
    public uint m_ID_Description;
    /// <summary>
    /// 建築物種類(0無 1=幫會商店 2=議事廳 3=練武堂 4=聚寶閣)
    /// </summary>
    public byte m_BuildingKind;
    /// <summary>
    /// 建築物等級
    /// </summary>
    public byte m_LV_Building;
    /// <summary>
    /// 建築物Icon字串
    /// </summary>
    public string m_String_BuildingIcon;
    /// <summary>
    /// 效果參數 (建築類型=1 參數=兌換商店ID 建築類型=2、3 效果參數=BuffID)
    /// </summary>
    public ushort m_EffectParam;
    /// <summary>
    /// 免費次數
    /// </summary>
    public byte m_FreeTimes;
    /// <summary>
    /// 上限次數
    /// </summary>
    public byte m_LimitTimes;
    /// <summary>
    /// 寶箱最大數量*3 順序 銅 銀 金
    /// </summary>
    [myatt(3)]
    public byte[] m_TreasureChestMax;
}

/// <summary>
/// 幫會贈禮資料
/// </summary>
[StringFileSerializeAttribute]
public struct GuildGiftData
{
    /// <summary>
    /// 幫會禮包等級
    /// </summary>
    public byte m_Lv_GuildGift;
    /// <summary>
    /// 升級所需求經驗
    /// </summary>
    public uint m_Exp_UpgradeNeed;
    /// <summary>
    /// 禮包對應的福袋ID
    /// </summary>
    public uint m_ID_LuckyBag;
    /// <summary>
    /// 大禮包解鎖所需求數值
    /// </summary>
    public uint m_Value_OpenLuckyBagNeed;
    /// <summary>
    /// 大禮包圖示名稱
    /// </summary>
    public string m_Name_LuckyBagImage;
}

/// <summary>
/// 組織等級資料
/// </summary>
[StringFileSerializeAttribute]
public struct GuildLvData
{
    /// <summary>
    /// 組織等級
    /// </summary>
    public byte m_Lv_Guild;
    /// <summary>
    /// 所需求名聲
    /// </summary>
    public uint m_ReputationNeed;
    /// <summary>
    /// 所需求金錢
    /// </summary>
    public uint m_MoneyNeed;
    /// <summary>
    /// 成員上限
    /// </summary>
    public byte m_Count_MemberMax;
    /// <summary>
    /// 倉庫上限
    /// </summary>
    public ushort m_Count_DepotMax;
}

/// <summary>
/// 禁用字串資料
/// </summary>
[StringFileSerializeAttribute]
public struct IllegalNameData
{
    /// <summary>
    /// 編號
    /// </summary>
    public uint m_Idx;
    /// <summary>
    /// 禁用字
    /// </summary>
    public string m_Text;
}

/// <summary>
/// 商城資料
/// </summary>
[StringFileSerializeAttribute]
public struct MallData
{
    /// <summary>
    /// 索引
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 分類頁籤
    /// </summary>
    public ushort m_Group;
    /// <summary>
    /// 物品編號
    /// </summary>
    public uint m_ItemIdx;
    /// <summary>
    /// 促銷編號
    /// </summary>
    public ushort m_MallEventID;
    /// <summary>
    /// 商品數量
    /// </summary>
    public uint m_ItemNum;
    /// <summary>
    /// 幣值 [ 0=未上架(防呆) 1=時空幣 2=黃易幣 3=遊戲幣 9=免費 ]
    /// </summary>
    public byte m_CostType;
    /// <summary>
    /// 價格
    /// </summary>
    public uint m_Cost;
    /// <summary>
    /// 等級限制
    /// </summary>
    public ushort m_LimitLv;
    /// <summary>
    /// 購買總數限制
    /// </summary>
    public byte m_LimitCount;
    /// <summary>
    /// 次數限制 標記類型 [ 0=無 1=個人計數永標(日刪) 2=個人計數永標(周刪) 3=個人永標(限單次購買) 4=個人計數永標(月刪) ]
    /// </summary>
    public byte m_FlagType;
    /// <summary>
    /// 標記ID
    /// </summary>
    public ushort m_FlagID;
    /// <summary>
    /// 周循環
    /// </summary>
    public byte m_Loop;
    /// <summary>
    /// 特價
    /// </summary>
    public uint m_OnSellCost;
    /// <summary>
    /// 特價類型 [0:折扣 1:增值]
    /// </summary>
    public byte m_OnSellType;
}

/// <summary>
/// NPC 資料
/// </summary>
[StringFileSerializeAttribute]
public struct NPCData
{
    /// <summary>
    /// NPC編號
    /// </summary>
    public uint m_ID;
    /// <summary>
    /// 名稱字串編號
    /// </summary>
    public uint m_NameID;
    /// <summary>
    /// 稱號字串編號
    /// </summary>
    public uint m_TitleID;
    /// <summary>
    /// 外觀模型
    /// </summary>
    public string m_Appearance;
    /// <summary>
    /// 2D頭像
    /// </summary>
    public string m_HeadIcon;
    /// <summary>
    /// 模型顏色(1) 皮膚
    /// </summary>
    public string m_ColorSkin;
    /// <summary>
    /// 模型顏色(2) 頭髮
    /// </summary>
    public string m_ColorHair;
    /// <summary>
    /// 模型顏色(3) 衣服A
    /// </summary>
    public string m_ColorclothA;
    /// <summary>
    /// 模型顏色(4) 衣服B
    /// </summary>
    public string m_ColorclothB;
    /// <summary>
    /// 模型顏色(5) 自發光
    /// </summary>
    public string m_ColorEmission;
    /// <summary>
    /// 模型縮放
    /// </summary>
    public ushort m_Scale;
    /// <summary>
    /// 碰撞半徑
    /// </summary>
    public ushort m_Radius;
    /// <summary>
    /// 碰撞高度
    /// </summary>
    public ushort m_Height;
    /// <summary>
    /// 對話模型位置Y軸偏移
    /// </summary>
    public ushort m_TalkModelOffsetY;
    /// <summary>
    /// HUD是否顯示(0:顯示) Fix By 文達 20200208
    /// </summary>
    public byte m_ShowHUD;
    /// <summary>
    /// 武器動作資源包
    /// </summary>
    public uint m_AcName;
    /// <summary>
    /// 顯示標記
    /// </summary>
    public ushort m_ShowFlag;
    /// <summary>
    /// UI顯示設定
    /// </summary>
    public byte m_UIShow;
    /// <summary>
    /// RTM顯示
    /// </summary>
    public byte m_RTM;
    /// <summary>
    /// NPC類型 (對照啥小?) NPCCrowdUClient 的 NPC 模式
    /// </summary>
    public byte m_NpcMod;
    /// <summary>
    /// NPC等級
    /// </summary>
    public ushort m_Lv;
    /// <summary>
    /// NPC階級
    /// </summary>
    public ushort m_Grade;
    /// <summary>
    /// 權重(搜尋)
    /// </summary>
    public byte m_Priority;
    /// <summary>
    /// 初始動作
    /// </summary>
    public ushort m_InitAct;
    /// <summary>
    /// 死亡特效
    /// </summary>
    public ushort m_DeathEffect;
    /// <summary>
    /// 偏性類型
    /// </summary>
    public byte m_Bias;
    /// <summary>
    /// 走速
    /// </summary>
    public byte m_WalkSpeed;
    /// <summary>
    /// 跑速
    /// </summary>
    public byte m_RunSpeed;
    /// <summary>
    /// 攻擊速度
    /// </summary>
    public byte m_AttSpeed;
    /// <summary>
    /// 異變造型
    /// </summary>
    public ushort m_MutationSkin;
    /// <summary>
    /// 異變特效
    /// </summary>
    public byte m_MutationEffect;
    /// <summary>
    /// 異變縮放
    /// </summary>
    public byte m_MutationScale;
    /// <summary>
    /// 困難模式下副本NPC ID
    /// </summary>
    public uint m_DifficultNpcId;
    /// <summary>
    /// 超他X難模式下副本NPC ID
    /// </summary>
    public uint m_FuckinDifficultNpcId;
    /// <summary>
    /// 低階版外觀模型
    /// </summary>
    public string m_LowSpecAppearance;
    /// <summary>
    /// 低階版2D頭像
    /// </summary>
    public string m_LowSpecHeadIcon;
    /// <summary>
    /// 對話圖
    /// </summary>
    public string m_DialogueTexture;
    /// <summary>
    /// 出場特效
    /// </summary>
    public ushort m_DebutEffectId;
    /// <summary>
    /// 出場音效
    /// </summary>
    public ushort m_DebutSoundId;
    /// <summary>
    /// 出場特效縮放
    /// </summary>
    public ushort m_EffectScale;
    /// <summary>
    /// 受擊音效材質類型
    /// </summary>
    public byte m_MaterialSound;
}

/// <summary>
/// 任務資料-任務圖鑑相關
/// </summary>
[StringFileSerializeAttribute]
public struct QuestListData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 任務種類---只有他在用的那種編號
    /// </summary>
    public byte m_MissionType;
    /// <summary>
    /// 分頁樣式
    /// </summary>
    public byte m_PageStyle;
    /// <summary>
    /// 分頁頁碼
    /// </summary>
    public byte m_PageIndex;
    /// <summary>
    /// 定位點索引
    /// </summary>
    public ushort m_Position;
    /// <summary>
    /// 資料型式
    /// </summary>
    public byte m_DataType;
    /// <summary>
    /// Line樣式
    /// </summary>
    public byte m_LineType;
    /// <summary>
    /// 前置永標
    /// </summary>
    [myatt(3)]
    public ushort[] m_LimitStaticSign;
    /// <summary>
    /// 任務事件ID
    /// </summary>
    public uint m_MissionEventID;
    /// <summary>
    /// 動標
    /// </summary>
    public ushort m_DynamicSign;
    /// <summary>
    /// 永標
    /// </summary>
    public ushort m_StaticSign;
    /// <summary>
    /// 所需等級
    /// </summary>
    public ushort m_NeedLevel;
    /// <summary>
    /// 起始地圖
    /// </summary>
    public ushort m_BeginMapID;
    /// <summary>
    /// 起始NPC
    /// </summary>
    public uint m_BeginNPC;
    /// <summary>
    /// 所在座標X
    /// </summary>
    public uint m_PoistionX;
    /// <summary>
    /// 所在座標Y
    /// </summary>
    public uint m_PoistionY;
    /// <summary>
    /// 特殊獎勵
    /// </summary>
    public uint m_SpecialItem;
    /// <summary>
    /// 特殊說明
    /// </summary>
    public uint m_SpecialDes;
    /// <summary>
    /// 傳送ID(對應傳送表)
    /// </summary>
    public ushort m_TeleportID;
}

/// <summary>
/// 隨機名字資料
/// </summary>
[StringFileSerializeAttribute]
public struct RandomNameData
{
    /// <summary>
    /// 命名種類
    /// </summary>
    public byte m_Type;
    /// <summary>
    /// 啟用組合
    /// </summary>
    public byte m_Open;
    /// <summary>
    /// 姓
    /// </summary>
    public string m_LastName;
    /// <summary>
    /// 男組合名
    /// </summary>
    public string m_MixName_Male;
    /// <summary>
    /// 女組合名
    /// </summary>
    public string m_MixName_Female;
    /// <summary>
    /// 男全名
    /// </summary>
    public string m_FirstName_Male;
    /// <summary>
    /// 女全名
    /// </summary>
    public string m_FirstName_Female;
}

/// <summary>
/// 組隊平台資料
/// </summary>
[StringFileSerializeAttribute]
public struct StageData
{
    /// <summary>
    /// 招募編號
    /// </summary>
    public ushort m_ID; 
    /// <summary>
    /// 關卡圖ID (Loading_slot)
    /// </summary>
    public ushort m_LoadingPicID; 
    /// <summary>
    /// 關卡類型
    /// </summary>
    public byte m_StageType; 
    /// <summary>
    /// 關卡最小人數
    /// </summary>
    public byte m_LimintMinMember; 
    /// <summary>
    /// 動態標記 (次數判斷動標)
    /// </summary>
    public ushort m_DPlotBookFlag; 
    /// <summary>
    /// 前置條件動態標記
    /// </summary>
    public ushort m_PreDPlotBookFlag; 
    /// <summary>
    /// 前置條件動態標記未達成時顯示的字串
    /// </summary>
    public uint m_PreDPlotBookFlagNotPassTextID;
    /// <summary>
    /// 事件ID
    /// </summary>
    public uint m_EventID; 
    /// <summary>
    /// 遊玩限制次數
    /// </summary>
    public byte m_PlayTimesLimit; 
    /// <summary>
    /// 打過顯示一般永標 (活動打過就不能玩)
    /// </summary>
    public ushort m_PlayFlag; 
    /// <summary>
    /// 限制等級 (最小)
    /// </summary>
    public ushort m_LevelLimitMin; 
    /// <summary>
    /// 限制聲譽
    /// </summary>
    public ushort m_ReputationLimit; 
    /// <summary>
    /// 開始月
    /// </summary>
    public byte m_BeginMonth; 
    /// <summary>
    /// 開始日
    /// </summary>
    public byte m_BeginDate; 
    /// <summary>
    /// 結束月
    /// </summary>
    public byte m_EndMonth; 
    /// <summary>
    /// 結束日
    /// </summary>
    public byte m_EndDate; 
    /// <summary>
    /// 出現日 (星期幾)
    /// </summary>
    public byte m_EventDay; 
    /// <summary>
    /// 開啟時段 (時)
    /// </summary>
    public byte m_BeginHour; 
    /// <summary>
    /// 開啟時段 (分)
    /// </summary>
    public byte m_BeginMinute; 
    /// <summary>
    /// 結束時段 (時)
    /// </summary>
    public byte m_EndHour; 
    /// <summary>
    /// 結束時段 (分)
    /// </summary>
    public byte m_EndMinute; 
    /// <summary>
    /// 介消編號
    /// </summary>
    public ushort m_AddLimitTimesID; 
    /// <summary>
    /// 入場費用種類
    /// </summary>
    public byte m_CoseType; 
    /// <summary>
    /// 入場數值
    /// </summary>
    public uint m_Cost; 
    /// <summary>
    /// 進入副本按鈕字串
    /// </summary>
    public uint m_EnterButtonText; 
    /// <summary>
    /// 介消ID
    /// </summary>
    public ushort m_PassUseMoneyID; 
    /// <summary>
    /// 掃蕩觸發事件 (>0代表有開啟掃蕩功能)
    /// </summary>
    public uint m_PassEventID; 
    /// <summary>
    /// 掃蕩文字說明
    /// </summary>
    public uint m_PassTextID; 
    /// <summary>
    /// 招募文字說明
    /// </summary>
    public uint m_HireTextID; 
    /// <summary>
    /// 所屬分頁
    /// </summary>
    public byte m_BelongType; 
    /// <summary>
    /// 篩選類別
    /// </summary>
    public byte m_FilterType; 
    /// <summary>
    /// 限制等級 (最大)
    /// </summary>
    public ushort m_LevelLimitMax; 
    /// <summary>
    /// 未解鎖描述字串
    /// </summary>
    public uint m_LockedTextID; 
    /// <summary>
    /// 篩選按鈕1對應招募ID
    /// </summary>
    public ushort m_FilterBtnOneToID; 
    /// <summary>
    /// 篩選按鈕2對應招募ID
    /// </summary>
    public ushort m_FilterBtnTwoToID; 
    /// <summary>
    /// 篩選按鈕3對應招募ID
    /// </summary>
    public ushort m_FilterBtnThreeToID; 
    /// <summary>
    /// 篩選按鈕4對應招募ID
    /// </summary>
    public ushort m_FilterBtnFourToID; 
    /// <summary>
    /// 條件不符 隱藏
    /// </summary>
    public byte m_HideByPolicy; 
}

/// <summary>
/// 稱號串檔
/// </summary>
[StringFileSerializeAttribute]
public struct TitleData
{
    /// <summary>
    /// 稱號ID
    /// </summary>
    public ushort m_Idx; 
    /// <summary>
    /// 權重
    /// </summary>
    public byte m_Order; 
    /// <summary>
    /// 顯示頁籤 (類型) 1. 一般 2. 活動 3. 特殊 4. 生活
    /// </summary>
    public byte m_UIPage; 
    /// <summary>
    /// 獲得稱號的條件類型 1. 聲譽 2. 魅力 3. 威望值(善惡值) 4. 一般永標 5. 動標 6. 記數永標 7. 生活技能等級
    /// </summary>
    public byte m_GetRequireType; 
    /// <summary>
    /// 條件參數1
    /// </summary>
    public uint m_Param_1; 
    /// <summary>
    /// 條件參數2
    /// </summary>
    public uint m_Param_2; 
    /// <summary>
    /// 稱號字串ID
    /// </summary>
    public uint m_TextID_Name; 
    /// <summary>
    /// 聊天用稱號字串 (含色碼)
    /// </summary>
    public uint m_TextID_ChatName; 
    /// <summary>
    /// 說明字串ID
    /// </summary>
    public uint m_TexIDt_Describe;
    /// <summary>
    /// 常駐屬性
    /// </summary>
    [myatt(3)]
    public PropertiesData[] m_GetPropertiesAy;
    /// <summary>
    /// 裝備屬性
    /// </summary>
    [myatt(3)]
    public PropertiesData[] m_EquipePropertiesAy;
}
public struct PropertiesData
{
    /// <summary>
    /// 常駐屬性類型
    /// </summary>
    public ushort m_Attr_ID;
    /// <summary>
    /// 常駐屬性參數
    /// </summary>
    public ushort m_Value;
}

/// <summary>
/// 衣櫃資料
/// </summary>
[StringFileSerializeAttribute]
public struct WardrobeData
{
    /// <summary>
    /// 時裝套裝ID
    /// </summary>
    public ushort m_SuitID; 
    /// <summary>
    /// 套裝名稱字串ID
    /// </summary>
    public uint m_SuitNameTextID; 
    /// <summary>
    /// 簡介字串編號
    /// </summary>
    public uint m_SuitInfoTextID; 
    /// <summary>
    /// 取得字串編號
    /// </summary>
    public uint m_SourceTextID; 
    /// <summary>
    /// 套裝編號 (讀加成表)
    /// </summary>
    public ushort m_AdditionID; 
    /// <summary>
    /// 類型
    /// </summary>
    public byte m_Kind;
    /// <summary>
    /// 品階
    /// </summary>
    public byte m_Rarity;
    /// <summary>
    /// 關聯套裝 (較低階品)
    /// </summary>
    public ushort m_LinkSuitID1;
    /// <summary>
    /// 關聯套裝 (較高階品)
    /// </summary>
    public ushort m_LinkSuitID2;
    /// <summary>
    /// 性別
    /// </summary>
    public byte m_Gender;
    /// <summary>
    /// 形象圖號 - 男
    /// </summary>
    public ushort m_BGID_Man;
    /// <summary>
    /// 形象圖號 - 女
    /// </summary>
    public ushort m_BGID_Woman;
    /// <summary>
    /// 物品ID
    /// </summary>
    [myatt(11)]
    public uint[] m_ItemIDAy;
}

/// <summary>
/// 武功預覽表
/// </summary>
[StringFileSerializeAttribute]
public struct WugongPreviewData
{
    /// <summary>
    /// 演武ID
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// SkillActID
    /// </summary>
    public uint m_SkillActID;
    /// <summary>
    /// 位移類型
    /// </summary>
    public byte m_MoveType;
    /// <summary>
    /// 位移方向
    /// </summary>
    public byte m_MoveDir;
    /// <summary>
    /// 位移距離
    /// </summary>
    public ushort m_MoveDistance;
    /// <summary>
    /// 招式時間
    /// </summary>
    public ushort m_SkillTime;
    /// <summary>
    /// TickData
    /// </summary>
    [ArrayAttribute ( 8 )]
    public Tick [] m_TickAy;
    /// <summary>
    /// 鏡頭距離
    /// </summary>
    public ushort m_CameraDistance;
    /// <summary>
    /// 敵方NPC數量
    /// </summary>
    public byte m_NPC_Enemy_Amount;
    /// <summary>
    /// 敵方NPC距離
    /// </summary>
    public ushort m_NPC_Enemy_Distance;
    /// <summary>
    /// 友方NPC數量
    /// </summary>
    public byte m_NPC_Friend_Amount;
    /// <summary>
    /// 友方NPC距離
    /// </summary>
    public ushort m_NPC_Friend_Distance;
}

/// <summary>
/// 圖鑑蒐集各單項資料
/// </summary>
[StringFileSerializeAttribute]
public struct CollectData
{
    /// <summary>
    /// 圖鑑蒐集各單項資料流水號
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// 收藏編號
    /// </summary>
    public ushort m_CollectionNumber;
    /// <summary>
    /// 達成條件字串編號
    /// </summary>
    public uint m_CollectionConditionStringID;
    
    /// <summary>
    /// 單一物品/寵物ID 以及 蒐集狀態 共6項
    /// </summary>
    [myatt(6)]
    public CollectDataItemSet[] m_CollectDataItemSet;
    
    /// <summary>
    /// 前置永標 
    /// </summary>
    public ushort m_PreFlag;
    /// <summary>
    /// 已領獎永標
    /// </summary>
    public ushort m_ReceiveRewardFlag;
    /// <summary>
    /// 可獲得探索積分  
    /// </summary>
    public ushort m_ExplorationPoint;
    
    /// <summary>
    /// 此圖鑑蒐集完成後可獲得的獎勵 共2項
    /// </summary>
    [myatt(2)]
    public CollectDataRewardSet[] m_CollectDataRewardSet;
    
    /// <summary>
    /// 完成後給動標
    /// </summary>  
    public ushort m_MovingFlag;
}


/// <summary>
/// 圖鑑蒐集 單一個要蒐集的物品或寵物 ID以及蒐藏狀態
/// </summary>
public struct CollectDataItemSet
{
    /// <summary>
    /// 已蒐藏永標
    /// </summary>
    public ushort m_CollectedFlag;
    /// <summary>
    /// 物品或寵物ID流水號
    /// </summary>
    public uint m_ItemID;
}

/// <summary>
/// 圖鑑蒐集獎勵 單一獎勵的類別 編號 數量
/// </summary>
public struct CollectDataRewardSet
{
    /// <summary>
    /// 獎勵類別
    /// </summary>
    public byte m_RewardType;
    /// <summary>
    /// 獎勵編號
    /// </summary>
    public uint m_RewardID;
    /// <summary>
    /// 獎勵數量
    /// </summary>
    public ushort m_RewardNum;
}

/// <summary>
/// 圖鑑分頁資料
/// </summary>
[StringFileSerializeAttribute]
public struct CollectTitleData
{
    /// <summary>
    /// 圖鑑分頁資料流水號
    /// </summary>
    public byte m_Idx;
    /// <summary>
    /// 圖鑑分頁字串編號
    /// </summary>
    public uint m_PageStringID;
}

/// <summary>
/// 的演武特規資料串表
/// </summary>
[StringFileSerializeAttribute]
public struct WugongNewPreview
{
    /// <summary>
    /// 演武ID
    /// </summary>
    public ushort m_Idx;
    /// <summary>
    /// SkillActID
    /// </summary>
    public uint m_SkillActID;
    /// <summary>
    /// 位移類型
    /// </summary>
    public byte m_MoveType;
    /// <summary>
    /// 位移方向
    /// </summary>
    public byte m_MoveDir;
    /// <summary>
    /// 位移距離
    /// </summary>
    public ushort m_MoveDistance;
    /// <summary>
    /// 招式時間
    /// </summary>
    public ushort m_SkillTime;
    /// <summary>
    /// TickData
    /// </summary>
    [ArrayAttribute ( 8 )]
    public Tick [] m_TickAy;
    /// <summary>
    /// 鏡頭距離
    /// </summary>
    public ushort m_CameraDistance;
    /// <summary>
    /// 敵方NPC數量
    /// </summary>
    public byte m_NPC_Enemy_Amount;
    /// <summary>
    /// 敵方NPC距離
    /// </summary>
    public ushort m_NPC_Enemy_Distance;
    /// <summary>
    /// 友方NPC數量
    /// </summary>
    public byte m_NPC_Friend_Amount;
    /// <summary>
    /// 友方NPC距離
    /// </summary>
    public ushort m_NPC_Friend_Distance;
}
/// <summary>
/// 內功資料
/// </summary>
[StringFileSerializeAttribute]
public struct InnerBookData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_Num;
    /// <summary>
    /// 心法編號
    /// </summary>
    public byte m_MethodID;
    /// <summary>
    /// 心法名稱字串
    /// </summary>
    public uint m_MethodString;
    /// <summary>
    /// 篇章名稱字串
    /// </summary>
    public uint m_ChapterString;
    /// <summary>
    /// icon編號
    /// </summary>
    public uint m_IconID;
    /// <summary>
    /// 心法階段
    /// </summary>
    public byte m_Step;
    /// <summary>
    /// 升級道具需求數
    /// </summary>
    public byte m_NeedCount;
    /// <summary>
    /// 進階道具物品編號
    /// </summary>
    public uint m_ItemID;
    /// <summary>
    /// 心法屬性
    /// </summary>
    [myatt(2)]
    public AttributeData m_Attribute;
    /// <summary>
    /// 內功武功編號
    /// </summary>
    public ushort m_WugongID;
    /// <summary>
    /// 靈氣獲取每運行5分鐘
    /// </summary>
    public byte m_SpiritPerFiveMin;
    /// <summary>
    /// 心法被動效果屬性修正值
    /// </summary>
    public ushort m_FixValue;
    /// <summary>
    /// 屬性修正值公式ID
    /// </summary>
    public ushort m_FormulaID;
    /// <summary>
    /// 靈氣解放開放
    /// </summary>
    public ushort m_Unlock;
    /// <summary>
    /// 靈氣解放屬性資料
    /// </summary>
    [myatt(2)]
    public SpiritData m_SpiritData;
    /// <summary>
    /// 靈氣解放消耗每小時
    /// </summary>
    public ushort m_HourCost;
}

public struct AttributeData
{
    /// <summary>
    /// 心法屬性值
    /// </summary>
    public byte Value;
}

public struct SpiritData
{
    /// <summary>
    /// 靈氣解放屬性修正值
    /// </summary>
    public ushort m_AttrFix;

    /// <summary>
    /// 靈氣解放屬性數值
    /// </summary>
    public ushort m_AttrValue;
}

/// <summary>
/// 內功靈脈資料
/// </summary>
[StringFileSerializeAttribute]
public struct InnerMeridianData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public byte m_ID;
    /// <summary>
    /// 靈脈編號
    /// </summary>
    public byte m_Num;
    /// <summary>
    /// 靈脈階段
    /// </summary>
    public byte m_Step;
    /// <summary>
    /// 貫通靈氣消耗基礎值
    /// </summary>
    public ushort m_AuraCost;
    /// <summary>
    /// 貫通靈氣消耗穴位加乘值
    /// </summary>
    public ushort m_AuraCostAdd;
    /// <summary>
    /// 靈脈進階需求靈氣值
    /// </summary>
    public ushort m_NeedCount;
    /// <summary>
    /// 穴位效果
    /// </summary>
    [myatt(3)]
    public EffectData m_Effect;
    /// <summary>
    /// 心法屬性加成屬性修正值
    /// </summary>
    public ushort m_MethodAttrFix;
    /// <summary>
    /// 心法屬性加成數值
    /// </summary>
    public ushort m_MethodAttrValue;
}

/// <summary>
/// 穴位效果單筆資料結構
/// </summary>
public struct EffectData
{
    /// <summary>
    /// 穴位效果屬性修正值
    /// </summary>
    public ushort m_AttrFix;

    /// <summary>
    /// 穴位效果數值
    /// </summary>
    public ushort m_AttrValue;
}

/// <summary>
/// 內功靈功資料
/// </summary>
[StringFileSerializeAttribute]
public struct InnerMeridianSkillData
{
    /// <summary>
    /// 流水號
    /// </summary>
    public ushort m_ID;
    /// <summary>
    /// 靈脈編號
    /// </summary>
    public byte m_Num;
    /// <summary>
    /// 用途定義
    /// </summary>
    public byte m_Define;
    /// <summary>
    /// 內功武功編號
    /// </summary>
    public ushort m_WugongID;
    /// <summary>
    /// 內功品階
    /// </summary>
    public byte m_SkillGrade;
    /// <summary>
    /// 升級道具需求數
    /// </summary>
    public byte m_NeedCount;
    /// <summary>
    /// 隨機機率
    /// </summary>
    public ushort m_RandProb;
}