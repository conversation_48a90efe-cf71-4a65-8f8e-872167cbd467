---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---選擇控制(Input Controll)
---@class SelectMgr
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.5.26

SelectMgr = {}
local this = SelectMgr

this.m_RayMask_Normal =
    2 ^ Layer.Boss + 2 ^ Layer.SceneGround + 2 ^ Layer.Player + 2 ^ Layer.NPC + 2 ^ Layer.RTMInvisible +
    2 ^ Layer.RTMModel
this.m_RayMask_IgnorePlayer =
    2 ^ Layer.Boss + 2 ^ Layer.SceneGround + 2 ^ Layer.NPC + 2 ^ Layer.RTMInvisible + 2 ^ Layer.RTMModel

---@type boolean 是否偵測輸入
local m_Active = false

---@type GameObject
---選取的物件
this.m_TargetGObj = nil

---@type RoleController|GearController
---選取目標的roleController
---取用時請注意
---可能為空、可能為GearController
this.m_TargetController = nil

---確定要與他互動的目標
---@type RoleController
this.m_InterActTarget = nil

---切換目標時的函數
local m_Table_OnTargetChange = {}

---與目標互動
local m_Table_OnTargetInteract = {}

---初始化時會用現存EventSys
local m_pointerData = {}
local m_RaycastResult = {}

---選取後物件腳下的圓圈圈
local m_SelectFrame = nil
local m_redFrame = nil
local m_greenFrame = nil
this.RED_FRAME_ID = 3
this.GREEN_FRAME_ID = 4

this.ETargetType = {
    Player = "Player",
    NPC = "NPC",
    --Floor = "Floor",
    Interactive = "Interactive",
}

--region 金手指設定
SelectMgr.m_ShowDebugMsg = true
---移動相關的除錯訊息
function SelectMgr.DebugLog(iMessageStr, iColorStr)
    if not SelectMgr.m_ShowDebugMsg then
        do
            return
        end
    end

    if iColorStr == nil then
        D.Log(GString.Format("[MoveLog] {0}", iMessageStr))
    else
        D.Log(GString.Format("<color={0}>[MoveLog] {1}</color>", iColorStr, iMessageStr))
    end
end
--endregion

local _CulCount = 0

---計算玩家與位移目標的交會點
local function CulSpeedRate(iPlayerMoveController, iEnemyMoveController, iEnemyEndPos, iStartRate)
    --local _NextRate = iStartRate
    _CulCount = _CulCount + 1
    
    local _VecPos = (iEnemyEndPos - iEnemyMoveController.transform.localPosition) * iStartRate + iEnemyMoveController.transform.localPosition
    if _CulCount > 5 then
        return _VecPos
    end
    local _disPlayer = Vector3.Distance(iPlayerMoveController.transform.localPosition, _VecPos)
    local _disEnemy = Vector3.Distance(iEnemyMoveController.transform.localPosition, _VecPos)

    if _disPlayer * iEnemyMoveController.m_HorizontalSpeed + SearchSetting.m_ChaceDistance < _disEnemy * iPlayerMoveController.m_HorizontalSpeed then
        return CulSpeedRate(iPlayerMoveController, iEnemyMoveController, iEnemyEndPos, iStartRate * 0.8)
    elseif _disPlayer * iEnemyMoveController.m_HorizontalSpeed - SearchSetting.m_ChaceDistance > _disEnemy * iPlayerMoveController.m_HorizontalSpeed then
        return CulSpeedRate(iPlayerMoveController, iEnemyMoveController, iEnemyEndPos, iStartRate * 1.2)
    else
        D.Log("執行CulSpeedRate 幾次: ".. _CulCount)
        return _VecPos
    end
end

function SelectMgr.Init()
    m_Active = false
    m_Table_OnTargetInteract[this.ETargetType.Player] = {}
    m_Table_OnTargetInteract[this.ETargetType.NPC] = {}
    m_Table_OnTargetInteract[this.ETargetType.Interactive] = {}
    m_pointerData = UnityEngine.EventSystems.PointerEventData.New(UnityEngine.EventSystems.EventSystem.current)
    m_RaycastResult = System.Collections.Generic.List_UnityEngine_EventSystems_RaycastResult()
end

function SelectMgr.SetActive(iBoolean)
    m_Active = iBoolean
end

function SelectMgr.SetOnTargetClicked(iKey, iFunc)
    m_Table_OnTargetInteract[iKey][table.Count(m_Table_OnTargetInteract[iKey]) + 1] = iFunc
end

function SelectMgr.SetOnTargetChanged(iFunc)
    return table.insert(m_Table_OnTargetChange, iFunc)
end

function SelectMgr.UnregistOnTargetChanged(iFunc)
    for k,v in pairs(m_Table_OnTargetChange) do
        if v == iFunc then
            m_Table_OnTargetChange[k] = nil
        end
    end
end


function SelectMgr.Update()
    if not m_Active then
        return
    end

    --region 目標太遠所以消失
    --如果有已選擇互動的目標則不取消選取對象
    --TODO:選取對象消失或死亡也要做
    if this.m_TargetGObj ~= nil and not Extension.IsUnityObjectNull(this.m_TargetGObj) and this.m_InterActTarget == nil then
        if this.WithTargetDistance() > DISAPPEAR_DISTANCE and not this.m_TargetController.m_isModelVisible then
            this.ClearSelect()
        end
    end
    --endregion

    ---正在點著快捷鍵
    if not HotKeyMgr.PC_CheckHotkeyUseSkill() then

        ---@type HotKeyUnit
        local _HotKeyUnit = HotKeyMgr.GetHotKeyUnit( HotKeyMgr.m_NowClickPCHotkey.m_Area, HotKeyMgr.m_NowClickPCHotkey.m_Row, HotKeyMgr.m_NowClickPCHotkey.m_Num )

        if _HotKeyUnit and
            (_HotKeyUnit.m_SkillData.m_UseType == EHotKeyUseKind.Direction or _HotKeyUnit.m_SkillData.m_UseType == EHotKeyUseKind.AppointPos) then
            return
        end
    end

    --region PC
    if Extension.IsStandalone then
        if Input.GetMouseButtonDown(0) then
            local _Ray = CameraMgr.GetScreenPointToRay(Input.mousePosition)
            local _MaxDistance = 1000
            local _LayerMask = this.m_RayMask_Normal --m_RayMask_IgnorePlayer --看是不是要忽略其他玩家

            --先檢查玩家點到的地方是否有其他UI
            m_pointerData.position = Input.mousePosition

            if UnityEngine.EventSystems.EventSystem.current ~= nil then
                UnityEngine.EventSystems.EventSystem.current:RaycastAll(m_pointerData, m_RaycastResult)
            end
            
            --想排除的狀況(例如相機旋轉不想遮擋到點擊尋路)
            if (m_RaycastResult.Count > 0) then
                for i = 1, m_RaycastResult.Count do
                    if  UIMgr.GetUIStage(Main_Controller) < EUIStage.Initialized or not Main_Controller.m_IsInitCameraZone or 
                            (m_RaycastResult[i-1].gameObject ~= Main_SubCtrl_Joystick.m_GObj_DragZone.gameObject 
                            and (not BattleEditor_Controller.m_DragZone_Trans or m_RaycastResult[i-1].gameObject ~= BattleEditor_Controller.m_DragZone_Trans.gameObject)) then
                        return
                    end
                end
            end
            
            --如果沒有其他UI的話則繼續
            local _allHits = Physics.RaycastAll(_Ray, _MaxDistance, _LayerMask)
            local _Ay_Hits = {}

            for i = 0, _allHits.Length - 1 do
                _Ay_Hits[i + 1] = _allHits[i]
            end


            if #_Ay_Hits > 0 then

                --戳到東西重置Tab順序
                SearchMgr.ResetTabIndex()

                --戳到東西取消戰鬥
                BattleMgr.SetAutoNormalBattle(false)

                table.sort(_Ay_Hits,function(k1,k2) return k1.distance < k2.distance end)

                -- 自己擋在前面的話會穿過去
                local _Hit = _Ay_Hits[1] --似乎是從0開始
                if _Hit.collider:CompareTag(this.ETargetType.Player) then
                    if _Hit.transform.gameObject == RoleMgr.m_RC_Player.gameObject then
                        if #_Ay_Hits > 1 then
                            _Hit = _Ay_Hits[2]
                        else
                            return
                        end
                    end
                end

                --有戳到物件就斷在這
                if this.ETargetType[_Hit.transform.gameObject.tag] then
                    if this.m_TargetGObj ~= _Hit.transform.gameObject then
                        if SelectMgr.ChangeTarget(_Hit.transform.gameObject, true) then
                            return
                        -- else
                        --     ---未切換成功，可能是互動物件不是可互動目標
                        --     if _Hit.transform.gameObject.tag == this.ETargetType.Interactive then
                        --     end
                        end
                    else
                        this.InteractSelected(_Hit.transform.gameObject)
                        return
                    end
                end


                --戳到地板則是在這裡處理
                local _Distance = nil
                for i = 1, #_Ay_Hits do
                    if  _Ay_Hits[i].collider.gameObject.layer == Layer.SceneGround then
                        if _Distance == nil or _Ay_Hits[i].distance < _Distance then
                            _Distance = _Ay_Hits[i].distance
                            _Hit = _Ay_Hits[i]
                        end
                    end
                end

                if _Hit.collider.gameObject.layer == Layer.SceneGround then
                    MoveMgr.DebugLog("點擊地板")
                    local pos = Vector3.zero
                    pos.x = _Hit.point.x
                    pos.y = _Hit.point.y
                    pos.z = _Hit.point.z
                    this.m_InterActTarget = nil
                    SearchMgr.ClearSearchTarget()
                    MoveMgr.PlayerSelfPathFind(pos)
                end

            end
        end
    end

    --endregion



end

---有點到目標的選取目標
---@param iGObj 被選取的gameObject GameObject
function SelectMgr.ChangeTarget(iGObj, iClearAuto)
    local _Tag = iGObj.tag
    --D.Log("[SelectMgr]點到 tag: " .. _Tag .. "物件名稱: " .. iGObj.name, "#8EEC68")

    if iClearAuto then
        SearchMgr.ClearAutoSearch()
    end

    if Extension.IsUnityObjectNull(m_SelectFrame) then
        this.CreateSelectFrame()
    end

    if _Tag == this.ETargetType.Interactive then
        if not GearMgr.IsGObjInteractable(iGObj) then
            return false
        end
    end

    this.m_TargetGObj = iGObj
    --m_TargetGObj_InstanceID = this.m_TargetGObj:GetInstanceID()
    local _lastTarget

    if this.m_TargetController then
        _lastTarget = this.m_TargetController
    end

    --目前以物件名稱去抓取Controller 之後或許有更好的寫法
    if _Tag == this.ETargetType.NPC then
        -- 戰鬥編輯器用
        if BattleMgr.m_IS_BATTLEEDITOR_DEBUG then
            this.m_TargetController = BattleMgr.GetBE_RC()
        else
            this.m_TargetController = NPCMgr.GetNPCControllerGObj(iGObj)
            if this.m_TargetController == nil then
                this.m_TargetController = PetMgr.GetPetControllerGObj(iGObj)
            end
        end
    elseif _Tag == this.ETargetType.Player then
        this.m_TargetController = RoleMgr.Get(tonumber(iGObj.name))
    elseif _Tag == this.ETargetType.Interactive then
        this.m_TargetController = GearMgr.GetGearController(iGObj)
    end

    --目標都一樣不用繼續做了
    if _lastTarget == this.m_TargetController then
        --_lastTarget = nil
        return false
    end

    for k,v in pairs(m_Table_OnTargetChange) do
        v(this.m_TargetController, _lastTarget)
    end

    this.OnSelectEffect()

    --選擇目標為NPC 或是 選擇目標為Player
    if _Tag == this.ETargetType.NPC or _Tag == this.ETargetType.Player then
        Main_SubCtrl_TargetInfo.UpdateTargetInfo()
    else
        Main_SubCtrl_TargetInfo.SetTargetInfoVisiable(false)
    end

    if this.m_TargetController == nil then
        this.m_TargetGObj = nil
        return false
    end

    return true
end

---跟選取中的目標互動
---@param iGObj GameObject 被選取的gameObject
---@param iCompleteDelegate function 到位後攻擊
function SelectMgr.InteractSelected(iGObj, iCompleteDelegate)
	if not this.m_TargetController then
		do return end
	end

    if RoleMgr.m_RC_Player == nil then 
        do return end
    end

    local _Tag = iGObj.tag

    ---與對象的距離
    local _distance = this.WithTargetDistance()

    ---可互動的範圍
    local _interactDistance = this.m_TargetController.m_Radius + RoleMgr.m_RC_Player.m_Radius * SearchSetting.m_ContactDistanceRate

    if this.m_TargetController.m_Relation and this.m_TargetController.m_Relation[ERelatType.Enemy] then
        if iCompleteDelegate then
            _interactDistance = BattleMgr.GetLimitRange() + 0.2 --補上一些偏差值看看
        end
    end

    -- 在範圍內就直接互動
    if _distance <= _interactDistance  then
        for key, value in pairs(m_Table_OnTargetInteract[_Tag]) do
            if value == nil then
                table.remove(m_Table_OnTargetInteract[_Tag], key)
            else
                local _Rotation = RoleMgr.m_RC_Player.transform.localPosition - iGObj.transform.localPosition
                _Rotation.y = 0
                RoleMgr.m_RC_Player:SetRotation(_Rotation * - 1)

                --執行互動的動作
                value(iGObj)
                
                if iCompleteDelegate then
                    iCompleteDelegate()
                end

                SearchMgr.ClearSearchTarget()

            end
        end
    else
        --範圍外則移動置目標面前再進行互動
        local _toPosVector = nil
        --保持安全社交距離 *0.8
        -- local _RadiusSum = this.m_TargetController.m_Radius + RoleMgr.m_RC_Player.m_Radius * SearchSetting.m_ContactDistanceRate
        ---根據對象的標籤，互動會變得不同
        local _interActFunc = nil
        this.m_InterActTarget = this.m_TargetController

        if _Tag == this.ETargetType.Player then
            _interActFunc = function()
                if this.m_InterActTarget then
                    this.ChangeAndInterAct(this.m_InterActTarget)
                end
            end
        elseif _Tag == this.ETargetType.NPC or _Tag == this.ETargetType.Interactive then
            --與遠距離NPC的互動 抵達時做事
            _interActFunc = function()
                if this.m_InterActTarget then
                    this.InteractSelected(iGObj, iCompleteDelegate)

                end
            end
        end
 
        local _CulDestination = function(iPos)
            local _ReturnPos
            local _distanceRate
            local _Rotation
            --我與目標的距離含半徑(計算向量倍率)
            _distanceRate = 1 - (BattleMgr.GetLimitRange()/this.WithTargetDistance())
            if _distanceRate == 1 then
                _distanceRate = SearchSetting.m_ContactDistanceRate
            end
            _Rotation = iPos - RoleMgr.m_RC_Player.transform.localPosition
            _ReturnPos = _Rotation * _distanceRate + RoleMgr.m_RC_Player.transform.localPosition
            
            return _ReturnPos
        end
        

        ---計算後的終點
        local _VecPos
        -- local _distanceRate
        -- local _Rotation
        --抓到了 這人是敵人
        if this.m_InterActTarget.m_Relation and this.m_InterActTarget.m_Relation[ERelatType.Enemy] then

            -- 若NPC位移中 計算終點與落點
            if this.m_InterActTarget.m_StateController and this.m_InterActTarget.m_StateController:GetStateType() == EStateType.Move and this.WithTargetDistance() > SearchSetting.m_ChaceDistance then  
                local _NPCMoveController = this.m_InterActTarget.m_MoveController
                local _MoveController = RoleMgr.m_RC_Player.m_MoveController
                if _NPCMoveController then


                    local _Pos = _NPCMoveController.FullPathOnAstar[_NPCMoveController.m_CurrentWaypointIndex + 1]
                    if _Pos then

                        --local _angleA, _angleB, _angleC = GFunction.GetPointAngle2D(_MoveController.transform.localPosition, _NPCMoveController.transform.localPosition, _Pos)

                        -- local _PlayerNextDistance = Vector3.Distance(_MoveController.transform.localPosition, _Pos)
                        -- ---敵人與下一個點的距離
                        -- local _EnemyNextDistance = Vector3.Distance(_NPCMoveController.transform.localPosition, _Pos)
                        _CulCount = 0
                        _VecPos = CulSpeedRate(_MoveController, _NPCMoveController, _Pos, 0.5)
                        -- --校正值
                        -- if _EnemyNextDistance * _MoveController.m_HorizontalSpeed > _PlayerNextDistance * _NPCMoveController.m_HorizontalSpeed or 
                        -- --玩家距離終點比距離物件遠
                        --    this.WithTargetDistance() < _PlayerNextDistance then
                        --     _speedRate = _PlayerNextDistance * _NPCMoveController.m_HorizontalSpeed / 
                        --     (_EnemyNextDistance * _MoveController.m_HorizontalSpeed + _PlayerNextDistance * _NPCMoveController.m_HorizontalSpeed) -- 0.1
                        -- else
                        --     _speedRate = _EnemyNextDistance * _MoveController.m_HorizontalSpeed / 
                        --     (_EnemyNextDistance * _MoveController.m_HorizontalSpeed + _PlayerNextDistance * _NPCMoveController.m_HorizontalSpeed) --+ 0.1
                        -- end

                        -- if _speedRate > 1 then
                        --     _speedRate = 1
                        -- elseif _speedRate < 0 then
                        --     _speedRate = 0
                        -- end



                        --_VecPos = (_Pos - _NPCMoveController.transform.localPosition) * _speedRate+ _NPCMoveController.transform.localPosition
                        _VecPos = _CulDestination(_VecPos)

                    end
                end
            else
                --我與目標的距離含半徑(計算向量倍率)
                _VecPos = _CulDestination(this.m_TargetController.transform.localPosition)
            end

        else
            --一般人就站到他面前
            BattleMgr.SetAutoNormalBattle(false)
            _toPosVector = this.m_TargetController:GetRotationObj().localRotation * Vector3.forward
            _VecPos = this.m_InterActTarget.transform.position + _toPosVector * _interactDistance
        end

        if _VecPos then
            if iCompleteDelegate then
                if RoleMgr.m_RC_Player.m_MoveController:PathFinding(_VecPos, _interActFunc) == false then
                    -- 間隔時間太短，等一下再送尋路
                    HEMTimeMgr.DoFunctionDelay(MoveMgr.MIN_PATHFIND_INTERVAL, function()
                        if (BattleMgr.m_IsAutoNormalBattle or AutoBattleMgr.m_AutoBattleState ~= AutoBattleMgr.EAutoBattleState.Stop) 
                                and BattleMgr.m_MoveToTargetDelay then
                            BattleMgr.m_MoveToTargetDelay()
                        end
                    end)
                end
            else
                MoveMgr.PlayerSelfPathFind(
                    _VecPos,
                    _interActFunc
                )
            end
        end
    end
end

---切換目標並選取
---@param iRC RoleController 被選取的RC
function SelectMgr.ChangeAndInterAct(iRC)
    this.ChangeTarget(iRC.gameObject, true)
    this.InteractSelected(iRC.gameObject)
end

---清除選取中的目標
function SelectMgr.ClearSelect()
    local _lastTarget = this.m_TargetController

    this.m_TargetGObj = nil
    this.m_TargetController = nil

    for k,v in pairs(m_Table_OnTargetChange) do
        v(this.m_TargetController, _lastTarget)
    end

    if not Extension.IsUnityObjectNull(m_SelectFrame) then
        m_SelectFrame:SetActive(false)
    end
    ---關閉目標資訊介面
    Main_SubCtrl_TargetInfo.SetTargetInfoVisiable(false)
end

---計算與目標距離 參照HEM待確定規則
function SelectMgr.WithTargetDistance()
    local _distance = 0
    if RoleMgr.m_RC_Player and this.m_TargetGObj ~= nil and this.m_TargetController then
        _distance = Vector3.Distance(RoleMgr.m_RC_Player.transform.position, this.m_TargetGObj.transform.position)
        _distance = _distance - this.m_TargetController.m_Radius
    end
    return _distance
end

--region 選取後物件腳下的圓圈圈

---製作圓圈圈
function SelectMgr.CreateSelectFrame()
    m_SelectFrame = GameObject("Select")
    m_redFrame = GameObject("RedFrame")
    m_greenFrame = GameObject("GreenFrame")
    local _isImportant = true
    m_redFrame.transform:SetParent(m_SelectFrame.transform)
    m_greenFrame.transform:SetParent(m_SelectFrame.transform)
    EffectMgr.PlayEffectWithParent(EEffectType.Model, this.RED_FRAME_ID, m_redFrame.transform, _isImportant)
    EffectMgr.PlayEffectWithParent(EEffectType.Model, this.GREEN_FRAME_ID, m_greenFrame.transform, _isImportant)
end

---當物件被選取時重設並移動圓圈圈
function SelectMgr.OnSelectEffect()
    if this.m_TargetController then
        m_SelectFrame:SetActive(true)

        m_SelectFrame.transform:SetParent(this.m_TargetController.transform)
        m_SelectFrame.transform.localPosition = Vector3.zero
        m_SelectFrame.transform.localScale = Vector3.one

        m_redFrame:SetActive(false)
        m_greenFrame:SetActive(true)
        if this.m_TargetController.m_Relation and this.m_TargetController.m_Relation[ERelatType.Enemy] then
            m_redFrame:SetActive(true)
            m_greenFrame:SetActive(false)
        end
        m_SelectFrame.transform.localScale = Vector3.one * this.m_TargetController.m_Radius
    end
end

--endregion
return SelectMgr
