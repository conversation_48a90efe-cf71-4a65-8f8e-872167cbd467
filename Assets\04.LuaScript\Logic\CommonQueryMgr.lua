---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---客製化輸入資料 table
require("UI/CommonQuery/CommonQuery_Model")


---詢問視窗管理器
---@class CommonQueryMgr
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.9
CommonQueryMgr = {}
local this = CommonQueryMgr

---@type CommonQueryData
-- 要存入堆疊內的預設資料結構
local CommonQueryData = {
    ---訊息編號
    m_IDX = 0,
    ---訊息類型(視窗類型)
    m_MessageType = 0,
    ---使用的UI類別
    m_UITypeID = 1,
    ---倒數類型
    m_CountingType = 0,
    ---倒數時間
    m_CountingTime = 0,
    ---需求介消編號
    m_CostNum = 0,
    ---標題字串編號
    m_TitleContentID = 0,
    ---內容字串編號
    m_ContentID = 0,
    ---內容字串參數
    m_ContentParma = {},
    ---按鈕1(左)字串
    m_Str_BTNConfirm = 0,
    ---按鈕2(右)字串
    m_Str_BTNCancel = 0,
    ---按鈕3(中)字串
    m_Str_BTNSpecial = 0,
    ---視窗出現聲音
    m_Sound_UIShow = 0,
    ---按鈕1點擊聲音
    m_Sound_BTNConfirm = 0,
    ---按鈕2點擊聲音
    m_Sound_BTNCancel = 0,
    ---按鈕3點擊聲音
    m_Sound_BTNSpecial = 0,
    ---確定按鈕回呼函式
    m_CallBack_Confirm = nil,
    ---確定按鈕回呼函式參數
    m_CallBack_ConfirmArgs ={},
    ---取消按鈕回呼函式
    m_CallBack_Cancel = nil,
    ---取消按鈕回呼函式參數
    m_CallBack_CancelArgs ={},
    ---特殊按鈕回呼函式
    m_CallBack_Special = nil,
    ---特殊按鈕回呼函式參數
    m_CallBack_SpecialArgs ={}
}
--- 要存入堆疊內前要根據串檔資料和回呼函式建立新的檔案結構
function CommonQueryData:NewData(iCommonQueryData, iTitleParam ,iContentParam, iCallBackConfirm, iCallBack_ConfirmArgs, iCallBackCancel, iCallBack_CancelArgs, iCallBackSpecial, iCallBack_SpecialArgs)
    local new = {}
    setmetatable(new, { __index = CommonQueryData })

    new.m_IDX = iCommonQueryData.m_IDX
    new.m_MessageType = iCommonQueryData.m_MessageType
    new.m_UITypeID = iCommonQueryData.m_UITypeID
    new.m_CountingType = iCommonQueryData.m_CountingType
    new.m_CountingTime = iCommonQueryData.m_CountingTime
    new.m_CostNum = iCommonQueryData.m_CostIDX
    new.m_TitleContentID = iCommonQueryData.m_TitleIDX
    new.m_TitleParma = iTitleParam
    new.m_ContentID = iCommonQueryData.m_ContentIDX
    new.m_ContentParma = iContentParam
    new.m_Str_BTNConfirm = iCommonQueryData.m_BTNConfirmIDX
    new.m_Str_BTNCancel = iCommonQueryData.m_BTNCancelIDX
    new.m_Str_BTNSpecial = iCommonQueryData.m_BTNSpecialIDX
    new.m_Sound_UIShow = iCommonQueryData.m_WindowSound
    new.m_Sound_BTNConfirm = iCommonQueryData.m_ConfirmSound
    new.m_Sound_BTNCancel = iCommonQueryData.m_CancelSound
    new.m_Sound_BTNSpecial = iCommonQueryData.m_SpecialSound
    new.m_CallBack_Confirm = iCallBackConfirm
    new.m_CallBack_ConfirmArgs = iCallBack_ConfirmArgs
    new.m_CallBack_Cancel = iCallBackCancel
    new.m_CallBack_CancelArgs = iCallBack_CancelArgs
    new.m_CallBack_Special = iCallBackSpecial
    new.m_CallBack_SpecialArgs = iCallBack_SpecialArgs

    return new
end

--endregion

--- 類型3 輸入字串型 iCustomData 需要的資料結構
CustomCommonQueryData_Type3 = {
    ---給別的UI取用的參數
    ---輸入的字串
    m_InfoString = "",

    ---NewData要帶入的參數
    ---輸入類型
    m_InputType = ECommonQueryType3_InputType.Standard,
    ---預設字串
    m_Placeholder = "",
    ---條件字串
    m_ConditionString = "",
    ---是否自己判斷關視窗
    m_IsSelfClose = true,
    ---是否在開啟後不給按確認
    m_FirstOpenCanConfirm = false,
    ---字數上限
    m_MaxLength = 0,
    ---到上限時要顯示的字串
    m_MaxLengthStr = "",
}

--- @param string iPlaceholder 預設字串
function CustomCommonQueryData_Type3.NewData(iInputType, iPlaceholder, iConditionString, iIsSelfClose, iFirstOpenCanConfirm, iMaxLength, iMaxLengthStr)
    local new = {
        m_InputType = iInputType,
        m_Placeholder = iPlaceholder,
        m_ConditionString = iConditionString,
        m_IsSelfClose = iIsSelfClose,
        m_FirstOpenCanConfirm = iFirstOpenCanConfirm,
        m_MaxLength = iMaxLength,
        m_MaxLengthStr = iMaxLengthStr,
    }
    setmetatable(new, { __index = CustomCommonQueryData_Type3 })
	return new
end

--- 類型5 大量物品顯示視窗 iCustomData 需要的資料結構
CustomCommonQueryData_Type5 = {
    m_ItemData = {},
    m_SizeAndPosData = {},
}

--- @param table iData 物品資料
function CustomCommonQueryData_Type5.NewData(iItemData)
    local new = {
        m_ItemData = iItemData,
    }
    setmetatable(new, { __index = CustomCommonQueryData_Type5 })
	return new
end

--- 類型9 單一物品選擇型 iCustomData 需要的資料結構
---@class CustomCommonQueryData_Type6
CustomCommonQueryData_Type6 = {
    --------- New 的時候必須要的屬性 ---------
    --- 物品數量最少一個
    m_Item_Count = 1,
    --- 物品n ID
    m_tItemIdx = {0,0,0,0,0,0},
    --- 讓確認亮起來，需要的物品數量
    m_tItemNeed = {1,1,1,1,1,1},
    --------- New 完後可選屬性 ---------
    --- 屬性標題與屬性數值同一行還是不同行，1--不同行 2--同行
    m_Attribute_Dir = 1,
    --- 是否有取消按紐
    m_HaveCancelBtn = true,
    --- 是否確定按紐要檢查需求數量，開啟時若背包數量小於持有數量，會Disable 確定按紐
    m_CheckConfirmNeed = true,
    --- 是否要顯示身上持有數
    m_ShowHaveAmount = true,
    --- 是否要在ItemIcon上顯示需要的數量
    m_ShowItemNeed = false,
    --- 需要數量大於背包數量，是否需要用紅字顯示需要的數量ItemIcon
    m_UnreachNeedUseRedNumber = true,
    --- 數量為0時道具顯示遮罩
    m_ShowMaskByAmount = true,
    --- 是否顯示物品名稱
    m_ShowItemName = true,
}
--- 要存入堆疊內前要根據串檔資料和回呼函式建立新的檔案結構
--- itAttributeTitles,itAttributeOriginValues,itAttributeFinalValues的格式
--- e.g. {{物品1屬性1,物品1屬性2,物品1屬性3},{物品2屬性1,物品2屬性2,物品2屬性3},.....}
--- @param number iItem_Count 物品數量
--- @param table itItemID 物品n ID
--- @param table itItemNeed 讓確認亮起來，需要的物品數量
--- @param table itAttributeTitles 點物品n時出現的屬性標題
--- @param table itAttributeOriginValues 物品n 要顯示的原屬性數值 預設會包NU_，文字要自己包字型
--- @param table itAttributeFinalValues 物品n 要顯示的屬性數值最終值
function CustomCommonQueryData_Type6.NewData(iItem_Count,itItemID,itItemNeed,itAttributeTitles,itAttributeOriginValues,itAttributeFinalValues)
    local new = {
        m_Item_Count = iItem_Count,
        m_tItemIdx = itItemID,
        m_tItemNeed = itItemNeed,
        m_tAttributeTitles = itAttributeTitles,
        m_tAttributeOriginValues = itAttributeOriginValues,
        m_tAttributeFinalValues = itAttributeFinalValues,
    }
    setmetatable(new, { __index = CustomCommonQueryData_Type6 })
	return new
end
--- 單一物品選擇型 iCustomData 需要的資料結構
---@class CustomCommonQueryData_Type7
CustomCommonQueryData_Type7 = {
    --------- New 的時候必須要的屬性 ---------
    ---顯示折扣
    m_ShowDiscount = false,
    ---折扣字串
    m_DiscountIdx = 0,
    ---圖示字串
    m_Pic = 0,
    ---儲值文字
    m_ItemTxt = "",
    ---儲值幣值圖字串
    m_MoneyIconTxt = "",
    ---儲值金額
    m_Money = 0
}

--- 要存入堆疊內前要根據串檔資料和回呼函式建立新的檔案結構
--- @param bool iShowDiscount 是否顯示折扣
--- @param number iDisStrIdx  折扣字串編號
--- @param number iPicStrIdx  圖示字串編號
--- @param string iItemTxt    儲值文字
--- @param string iMoneyIconTxt 儲值幣值圖字串
--- @param string iMoney      儲值金額
function CustomCommonQueryData_Type7.NewData(iShowDiscount, iDisStrIdx, iPicStrIdx, iItemTxt, iMoneyIconTxt, iMoney)
    local _NewData =
    {
        m_ShowDiscount = iShowDiscount,
        m_DiscountIdx = iDisStrIdx,
        m_Pic = iPicStrIdx,
        m_ItemTxt = iItemTxt,
        m_MoneyIconTxt = iMoneyIconTxt,
        m_Money = iMoney,
    }
    setmetatable(_NewData, { __index = CustomCommonQueryData_Type7 })
    return _NewData
end

ECommonUIPrefabType =
{
    ---純字串
    OnlyString = 1,
    ---物品+數值變化型
    Item_ValueVariation = 2,
    ---物品、數值顯示與數量輸入
    StringInput = 3,
    ---機率顯示列表
    ShowProbability = 4,
    ---大量物品顯示視窗
    ManyItemDisplay = 5,
    ---單一物品選擇型
    UnitItemSelection = 6,
    ---儲值引導
    DepositCashGuide  = 7,
}

---初始化
function CommonQueryMgr.Init()

    ---用來記錄有哪些類別的 commonquery
    this.m_CommonqueryTypeTable = {}
    ---todo 有哪些類別要添加
    ---  this.m_CommonqueryTypeTable[i] = CommonQueryTypeController_i
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.OnlyString] = CommonQuery_Type1_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.Item_ValueVariation] = CommonQuery_Type2_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.StringInput] = CommonQuery_Type3_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.ShowProbability] = CommonQuery_Type4_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.ManyItemDisplay] = CommonQuery_Type5_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.UnitItemSelection] = CommonQuery_Type6_Controller
    this.m_CommonqueryTypeTable[ECommonUIPrefabType.DepositCashGuide] = CommonQuery_Type7_Controller

    ---建立空的堆疊訊息
    this.m_InformationList = {}

    ---目前開啟的訊息全部資料
    this.m_CurrentInfomation = nil
    ---目前開啟的UI 開啟下一個訊息時 先關閉目前訊息適用的UIcontroller
    this.m_CurrentUseUI = nil
    ---詢問視窗(不管目前使用哪個大分類) 是否開啟中
    this.m_IsShowCommonQueryPrefab = false

end

---添加新的訊息
---@param iUseQueryData number|table Commonquery串表對應的流水號|如果沒有Commonquery串表訊息 允許使用者自行填入使用者生成的CommonQueryData
---@param iTitleParam table Content傳入參數
---@param iContentParam table Content傳入參數
---@param iCallBack_Confirm function 確認按鈕回呼函式
---@param iCallBack_ConfirmArgs table 確認按鈕回呼函式參數
---@param iCallBack_Cancel function 取消按鈕回呼函式
---@param iCallBack_CancelArgs table 取消按鈕回呼函式參數
---@param iCallBack_Special function 特殊按鈕回呼函式
---@param iCallBack_SpecialArgs table 特殊按鈕回呼函式參數
---@param iCustomData table 因應各種不同分類變體時可能需要額外填入的屬性道具等等資訊
function CommonQueryMgr.AddNewInform(iUseQueryData,iTitleParam,iContentParam,iCallBack_Confirm,iCallBack_ConfirmArgs,iCallBack_Cancel,iCallBack_CancelArgs,iCallBack_Special,iCallBack_SpecialArgs,iCustomData)
    local _WaitingData = {}

    ---根據收到的iUseQueryData類別 判斷是否要從串表取資料
    local _CommonQueryData
    if type(iUseQueryData) == "number"  then
        _CommonQueryData = CommonQuery.GetCommonQueryByIdx(iUseQueryData)
    elseif type(iUseQueryData) == "table"  then
        _CommonQueryData = iUseQueryData
    end

    if _CommonQueryData == nil  then
        return
    end

    ---要顯示的資料
    local _NeedShowData
    _NeedShowData = CommonQueryData:NewData(_CommonQueryData, iTitleParam,iContentParam,iCallBack_Confirm,iCallBack_ConfirmArgs,iCallBack_Cancel,iCallBack_CancelArgs,iCallBack_Special,iCallBack_SpecialArgs)

    --- 串表 解讀出來的CommonQueryData
    _WaitingData = _NeedShowData

    ---UI 大分類
    _WaitingData.m_CommonqueryBoxType = Mathf.Floor(_NeedShowData.m_UITypeID/100)
    ---UI 分類變體
    _WaitingData.m_CommonqueryBoxType_SubType = _NeedShowData.m_UITypeID%100
    --- 客製化填入資訊
    _WaitingData.m_CustomData = iCustomData

    table.insert(this.m_InformationList,_WaitingData)

    ---如果目前沒有顯示詢問視窗 則要顯示
    if this.m_IsShowCommonQueryPrefab == false then
        CommonQueryMgr.ShowNextCommonQueryMgrData()
    end
end

---移除目前顯示中訊息同時顯示下一則以及更新排序index
function CommonQueryMgr.ShowNextCommonQueryMgrData()

    ---有資料要顯示資料 沒資料關閉視窗
    if CommonQueryMgr.CheckHasInformation() then
        CommonQueryMgr.DataOrderShift()

        local _IsUseSameUI = false

        _IsUseSameUI = this.m_CurrentUseUI == this.m_CommonqueryTypeTable[this.m_CurrentInfomation.m_CommonqueryBoxType]

        ---如果要使用不同類別的UI(不同 XXX_controller)
        if this.m_CurrentUseUI ~=nil and _IsUseSameUI == false then
            UIMgr.Close(this.m_CurrentUseUI)
        end

        CommonQueryMgr.ShowCurrentInfo(_IsUseSameUI)
        this.m_IsShowCommonQueryPrefab = true
    else
        UIMgr.Close(this.m_CurrentUseUI)
        UIMgr.Close(CommonQuery_BlackBackGround_Controller)
        this.m_CurrentUseUI = nil
        this.m_IsShowCommonQueryPrefab = false
    end
end

---判斷訊息駐列是否有訊息存在
function CommonQueryMgr.CheckHasInformation()
    return (table.Count(this.m_InformationList) > 0)
end

---顯示訊息
---@param iIsUseSameUI bool 是否使用相同UI
function CommonQueryMgr.ShowCurrentInfo(iIsUseSameUI)
    local _BoxType = this.m_CurrentInfomation.m_CommonqueryBoxType

    this.m_CurrentUseUI = this.m_CommonqueryTypeTable[_BoxType]

    ---先開黑色底板
    if UIMgr.IsVisible(CommonQuery_BlackBackGround_Controller) == false and
       UIMgr.IsVisible(Teach_Controller) == false and -- 教學介面作用時, 不使用黑幕避免遮罩效果重複 Modify by 凌傑#121469 2025.0110
       UIMgr.IsVisible(InnerBook_Controller) == false then -- 內功介面開啟時, 不使用黑幕避免遮罩效果重複
        UIMgr.Open(CommonQuery_BlackBackGround_Controller)
    end

    if iIsUseSameUI then
        this.m_CommonqueryTypeTable[_BoxType].Settinginformation()
    else
        UIMgr.Open(this.m_CurrentUseUI)
    end
end

---將第一筆資料取出 並將其他資料依序下移index
function CommonQueryMgr.DataOrderShift()
    this.m_CurrentInfomation = this.m_InformationList[1]
    for i = 1, table.Count(this.m_InformationList)  do

        if i ~= table.Count(this.m_InformationList) then
            this.m_InformationList[i] = this.m_InformationList[i+1]
        else
            this.m_InformationList[i] = nil
        end
    end
end

---取得當前的CommonQueryData
function CommonQueryMgr.GetCurrentCommonQueryData()
    return this.m_CurrentInfomation
end



---生成客製化table
---@param iECommonUIPrefabType ECommonUIPrefabType 詢問視窗類別
function CommonQueryMgr.GetNewCommonQueryData(iECommonUIPrefabType)

    if iECommonUIPrefabType == ECommonUIPrefabType.Item_ValueVariation then
        return  CommonQuery_Model:New_Type2()
    elseif iECommonUIPrefabType == ECommonUIPrefabType.ShowProbability then
        return  CommonQuery_Model:New_Type4()
    else
        return  CommonQuery_Model:New_Type2()
    end
end

return CommonQueryMgr
