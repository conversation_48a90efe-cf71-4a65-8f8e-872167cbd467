---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---Class Describes 掉落物管理(經驗光球/怪物掉寶道具) 產生後的行為 (行動路徑/使用特效/音效 )
---@class lua
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.19
DropMgr = {}
local this = DropMgr

local DROP_ROOT_NAME = "Drop_Root"
local DROP_POOL_NAME = "ObjPool"
local DROP_ACTIVE_NAME = "Drop_Active"

--掉落特效光球
this.DROP_EXP_EFFECT = 60009
--戰利品吸到玩家身上特效
this.DROP_ARRIVE_PLAYER_EFFECT = 60018
--掉落物碰到地面音效
this.DROP_TOUCHGROUND_SOUND = 12810
--戰利品吸到玩家身上音效
this.DROP_ARRIVE_PLAYER_SOUND = 33004
---掉落物飛回身上時要延遲多久回收物件(讓拖尾的特效不會瞬間消失)
local DELAY_DISAPPEAR_TIME = 0.2

--掉落物的3D模型 目前只有四種
EDrop3DModelType = {
    --- 袋子
    OB_1001 = 1,
    --- 包裹
    OB_1002 = 2,
    --- 五角形盒子
    OB_1003 = 3,
    --- 正方形盒子
    OB_1004 = 4,
    --- for迴圈矩陣用
    Max =5,
}

---掉落物的來源
EDropFromType =
{
    ---金手指測試用的
    GoldFinger = 1,
    ---小怪掉落
    DropFromMob = 2,
    ---Boss掉落
    DropFromBoss = 3,
    ---寶箱掉落
    DropeFromTreasureBox = 4,

}


---初始化
function DropMgr.Init()
    this.m_DropRoot = GameObject.New(DROP_ROOT_NAME).transform
    this.m_ObjPool = GameObject.New(DROP_POOL_NAME).transform
    this.m_ActiveObj = GameObject.New(DROP_ACTIVE_NAME).transform
    this.m_ObjPool:SetParent(this.m_DropRoot)
    this.m_ActiveObj:SetParent(this.m_DropRoot)

    --載入飛行prefab
    local _Drop_PrefabName = "FlyingObjController"  --已經裝好 PathDesingr 的 prefab 名稱
    ResourceMgr.Load(_Drop_PrefabName,function(iObject)
        if iObject then
            this.m_DropUnit = iObject

            this.m_GObjPool_DropUnit = Extension.CreatePrefabObjPool(this.m_DropUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

            this.m_DropUnit.transform:SetParent(this.m_DropRoot)
        else
            D.LogError("找不到物件: " .. _Drop_PrefabName)
        end
    end)

    --3D Model 物件池
    this.m_m_DropModelPoolGroups = {}
    local _PoolObjName_PreWord = "DropModelPool_OB_100"
    local _Drop_Prefab_3DModelType_PreWord = "OB_100"

    for i = 1, EDrop3DModelType.Max-1 do
        this.m_m_DropModelPoolGroups[i] = {}
        local _PoolName = _PoolObjName_PreWord..i
        this.m_m_DropModelPoolGroups[i].m_PoolObject = GameObject.New(_PoolName).transform
        this.m_m_DropModelPoolGroups[i].m_PoolObject:SetParent(this.m_DropRoot)

        local _PoolObjName = _Drop_Prefab_3DModelType_PreWord..i

        -- 產生 掉落物 3D model type 物件池
     ResourceMgr.Load(_PoolObjName,function(iObject)
        if iObject then
            this.m_m_DropModelPoolGroups[i].m_Drop_ModelUnit = iObject

            this.m_m_DropModelPoolGroups[i].m_GObjPool_Drop_ModelTypeGroups = Extension.CreatePrefabObjPool(this.m_m_DropModelPoolGroups[i].m_Drop_ModelUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

            this.m_m_DropModelPoolGroups[i].m_Drop_ModelUnit.transform:SetParent(this.m_m_DropModelPoolGroups[i].m_PoolObject)
        else
            D.LogError("找不到物件: " .. _Drop_Prefab_3DModelType_PreWord)
        end
    end)

    end

     --載入Path紀錄物件
    local _ParameterRecord = "PathParameterRecords"
    ResourceMgr.Load(_ParameterRecord,function(iObject)
         if iObject then
             this.m_ParameterRecordObj = iObject

             this.m_ParameterRecordObj.transform:SetParent(this.m_DropRoot)

              --蒐集Path紀錄物件中 全部的PathParameterRecord資訊
             local _ChildCount = this.m_ParameterRecordObj.transform.childCount
             local _EffectIndex =1

             this.m_ParameterList = {}

             for i = 1, _ChildCount do
                local _Recoed = this.m_ParameterRecordObj.transform:GetChild(i-1).gameObject:GetComponent("PathParameterRecord")
                if _Recoed ~= nil then
                    this.m_ParameterList[_EffectIndex] = _Recoed
                    _EffectIndex = _EffectIndex + 1
                end

            end
        else
            D.LogError("找不到物件: " .. _ParameterRecord)
        end
    end)

    --金手指測試使用
    this.m_IsGoldFinggerTest =false
    --已經噴出幾顆
    this.m_GoldFingerAlreadyDropCount = 0
    --最多有幾顆 此處設定出參數欄位 實際會噴幾顆根據套用的 PathParameterRecord決定
    this.m_GoldFingerAlreadyDropCountMax = 3
    --是否為金手指週期性掉落測試
    this.m_GoldFingerPeriodDrop =false

end

---Update
function DropMgr.Update()

    --金手指
    if ProjectMgr.IsDebug() then
        -- this.m_GoldFingerAlreadyDropCount = 0  表示噴出去的光球都已經回收 用在 單個掉落的金手指測試
        if this.m_IsGoldFinggerTest and this.m_ParameterList[EDropFromType.GoldFinger].m_UsePeriodInject ==false and this.m_GoldFingerAlreadyDropCount == 0 then
            DropMgr.GoldFingerForceDrop()
        --  用在週期性掉落的金手指測試
        elseif this.m_IsGoldFinggerTest and this.m_ParameterList[EDropFromType.GoldFinger].m_UsePeriodInject == true and this.m_GoldFingerPeriodDrop == false then
            DropMgr.GoldFingerForceDrop()
        end
    end

end

---產生經驗掉落
---@param iRoleController RoleController 產生的瞬間掛在哪個RC
function DropMgr.CreateExpDrop(iRoleController)

    --防呆 沒有RoleController 直接不演掉落特效
    if iRoleController == nil then
        return
    end

    --PathDesigner腳本
    local _ExpDrop

    --從物件池取物件
    local _Unit
    _Unit = this.m_GObjPool_DropUnit:Get()

    _Unit:SetActive(true)
    _ExpDrop = _Unit:GetComponent("PathDesigner")

    -- 沒取到腳本 不演
    if _ExpDrop == nil then
        return
    end

    ---先將物件移到定點 避免掛上特效後再移動到定點 會被系統認為有移動而生成不必要的軌跡
    _Unit.transform.position = iRoleController.transform.position

    _ExpDrop.gameObject.transform:SetParent(this.m_ActiveObj)

    local _PlayerRC = RoleMgr.GetPlayerRC()
    if _PlayerRC == nil then
        return
    end

    --掛經驗光球特效
    EffectMgr.PlayEffectWithParent(EEffectType.FlyToPlayer, DROP_EXP_EFFECT, _ExpDrop.gameObject.transform,
            false,function(iHashCode)
                                _ExpDrop:AddEffectHashCode(iHashCode)
                                end)


    local iFixGoalVector3 = Vector3(0,_PlayerRC.m_Height/2,0)
    -- 填入 PathDesigner component 所需資訊 並開始計算 執行 leantween路徑
    _ExpDrop:FillDropData(iRoleController.transform.position,DropMgr.DropsTouchGroundAction,DropMgr.DropsArrivePlayer,Vector3.GetFloorPosition,iFixGoalVector3)

    _ExpDrop.m_PlayerRC = _PlayerRC.gameObject

    _ExpDrop.m_IdlePool = this.m_ObjPool


    --決定使用甚麼參數 目前默認為使用小怪模式 需要討論要從甚麼地方決定使用甚麼
    local _PathIndex = EDropFromType.DropFromMob

    ---如果是金手指測試
    if ProjectMgr.IsDebug() and this.m_IsGoldFinggerTest  then
            _PathIndex = EDropFromType.GoldFinger
    end

    if this.m_ParameterList[_PathIndex]~=nil then
        _ExpDrop:DecidePathByPathParameterRecord(this.m_ParameterList[_PathIndex])
    else
        _ExpDrop:DecidePath()
    end
    _ExpDrop:ExecuatePath()
end


--產生道具掉落
---@param iRoleController RoleController 產生的瞬間掛在哪個RC
---@param iItemID number 掉落道具物的ID
---@param iEDropFromType EDropFromType 掉落物的來源種類
---@param iInitialPosition Vector3 掉落物來源怪物死掉時的位置(因為死亡延遲噴發 原本綁的RC 有可能因為被回收而跑到奇怪的位置)
function DropMgr.CreateItemDrop(iRoleController,iItemID,iEDropFromType,iInitialPosition)

    if iItemID ==nil or iRoleController == nil then
        return
    end

    local _ItemDrop

    --從物件池取物件
    local _Unit
    _Unit = this.m_GObjPool_DropUnit:Get()

    ---先將物件移到定點 避免掛上特效後再移動到定點 會被系統認為有移動而生成不必要的軌跡
    if iInitialPosition == nil then
        _Unit.transform.position = iRoleController.transform.position
    else
        _Unit.transform.position = iInitialPosition
    end


    _Unit:SetActive(true)
    _ItemDrop = _Unit:GetComponent("PathDesigner")

    if _ItemDrop == nil then
        return
    end

    _ItemDrop.gameObject.transform:SetParent(this.m_ActiveObj)

    local _PlayerRC = RoleMgr.GetPlayerRC()
    if _PlayerRC == nil then
        return
    end

    local _ItemData = ItemData.GetItemDataByIdx(iItemID)

    --用物品ID判斷階級並掛對應特效
    local _Rarity = _ItemData.m_Rarity
    local _EffectID = DropMgr.GetDropsEffect(_Rarity)

    EffectMgr.PlayEffectWithParent(EEffectType.FlyToPlayer, _EffectID, _ItemDrop.gameObject.transform,
                                false,function(iHashCode)
                                    _ItemDrop:AddEffectHashCode(iHashCode)
                                end)

    --用物品ID判斷取得3D掉落物物
    local _3DModelIndex = math.floor(_ItemData.m_ID_3DModelEquip/10)

    local _3Dmodel = DropMgr.GetDrops3DModel(_3DModelIndex)

    if _3Dmodel~=nil then
        _3Dmodel:SetActive(true)
        _3Dmodel.transform:SetParent(_ItemDrop.gameObject.transform)
        _3Dmodel.transform.localPosition =Vector3(0,0.3,0)

        _ItemDrop.m_3DModelType = _3DModelIndex
        _ItemDrop.m_3DModel = _3Dmodel
    end
    local iFixGoalVector3 = Vector3(0,_PlayerRC.m_Height/2,0)
    -- 填入 PathDesigner component 所需資訊 並開始計算 執行 leantween路徑
    _ItemDrop:FillDropData(_Unit.transform.position,DropMgr.DropsTouchGroundAction,DropMgr.DropsArrivePlayer,Vector3.GetFloorPosition,iFixGoalVector3)

    _ItemDrop.m_PlayerRC = _PlayerRC.gameObject
    _ItemDrop.m_IdlePool = this.m_ObjPool

    --決定使用甚麼參數
    local _PathIndex = iEDropFromType

    if this.m_ParameterList[_PathIndex]~=nil then
        _ItemDrop:DecidePathByPathParameterRecord(this.m_ParameterList[_PathIndex])
    else
        _ItemDrop:DecidePath()
    end
    _ItemDrop:ExecuatePath()
end

this.DropEffect =
{
    [ERank.White] = 60019,
    [ERank.Green] = 60012,
    [ERank.Blue] = 60016,
    [ERank.Purple] = 60015,
    [ERank.Red] = 60017,
    [ERank.Gold] = 60017,
}

--利用物品階級取得對應光球特效
function DropMgr.GetDropsEffect(iRarity)

    if iRarity ==nil then
        iRarity = 1
    end

    return this.DropEffect[iRarity]

    -- --白
    -- if iRarity ==ERank.White then
    --     return 60019
    -- --綠
    -- elseif iRarity ==ERank.Green then
    --     return 60012
    -- --藍
    -- elseif iRarity ==ERank.Blue then
    --     return 60016
    -- --紫
    -- elseif iRarity ==ERank.Purple then
    --     return 60015
    -- --紅
    -- elseif iRarity ==ERank.Red then
    --     return 60017
    -- --商城色
    -- elseif iRarity ==ERank.Gold then
    --     return 60017
    -- end
end

---利用物品3D掉落物資訊取得3D物件池中model
---@param iModelIndex EDrop3DModelType 掉落物3D模型的物件代號
function DropMgr.GetDrops3DModel(iModelIndex)

    ---輸入的index 為空值 或者找不到對應的模型物件池 默認使用 OB_1001
    if iModelIndex ==nil or this.m_m_DropModelPoolGroups[iModelIndex] == nil then
        iModelIndex = EDrop3DModelType.OB_1001
    end

    return this.m_m_DropModelPoolGroups[iModelIndex].m_GObjPool_Drop_ModelTypeGroups:Get()
end

---歸還3D物件池model
---@param iController PathDesigner 回收物件時 物件身上的掉落物腳本
function DropMgr.Recycle3DModel(iController)

    ---要回收到哪一個物件池
    local _ReturnPoolIndex = 0
    ---輸入的 iController.m_3DModelType 為空值 或者找不到對應的模型物件池 默認收回 OB_1001 那個物件的物件池
    if iController.m_3DModelType == nil or this.m_m_DropModelPoolGroups[iController.m_3DModelType] ==nil  then
        _ReturnPoolIndex = EDrop3DModelType.OB_1001
    else
        _ReturnPoolIndex = iController.m_3DModelType
    end

    this.m_m_DropModelPoolGroups[_ReturnPoolIndex].m_GObjPool_Drop_ModelTypeGroups:Store(iController.m_3DModel)
    iController.m_3DModel.transform:SetParent(this.m_m_DropModelPoolGroups[_ReturnPoolIndex].m_PoolObject.transform)

    iController.m_3DModel:SetActive(false)

    iController.m_3DModelType =0;
    iController.m_3DModel =nil
end

--物件碰到地上要做甚麼反應
function DropMgr.DropsTouchGroundAction()
    local _PlayerRC = RoleMgr.GetPlayerRC()
    if _PlayerRC then
        AudioMgr.PlayAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.ENV, this.DROP_TOUCHGROUND_SOUND, _PlayerRC.transform)
    end
end

--物件抵達玩家要做甚麼反應
---@param iController PathDesigner 回收物件時 物件身上的掉落物腳本
function DropMgr.DropsArrivePlayer(iPathDesiner)
    local _PlayerRC = RoleMgr.GetPlayerRC()

     --找不到玩家的RoleController就不演出音效特效 可能因為登出造成玩家RoleController變成空值
     if _PlayerRC then
        AudioMgr.PlayAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.ENV, this.DROP_ARRIVE_PLAYER_SOUND, _PlayerRC.transform)
        EffectMgr.PlayEffectWithParent(EEffectType.FlyToPlayer, this.DROP_ARRIVE_PLAYER_EFFECT, _PlayerRC.transform)
    end

    ---延遲回收相關 讓拖尾特效演出
    LeanTween.delayedCall(DELAY_DISAPPEAR_TIME, System.Action(
            function()
                for i = 1, iPathDesiner.gameObject.transform.childCount do
                    iPathDesiner.gameObject.transform:GetChild(i-1).gameObject:SetActive(false);
                end

                for i = iPathDesiner.m_EffectIDList.Count-1, 0 do
                    EffectMgr.ReturnEffect(iPathDesiner.m_EffectIDList[i])
                    iPathDesiner:RemoveEffectHashCode()
                end

                --有掛3D物件 才需要歸還
                if iPathDesiner.m_3DModel~=nil then
                    DropMgr.Recycle3DModel(iPathDesiner)
                end

                --還給物件池
                this.m_GObjPool_DropUnit:Store(iPathDesiner.gameObject)


                --金手指測試掉落物
                if this.m_IsGoldFinggerTest then
                    this.m_GoldFingerAlreadyDropCount = this.m_GoldFingerAlreadyDropCount-1
                    ---金手指 測試週期掉落
                    if this.m_GoldFingerAlreadyDropCount == 0 and  this.m_ParameterList[EDropFromType.GoldFinger].m_UsePeriodInject == true then
                        this.m_GoldFingerPeriodDrop =false
                    end
                end
            end))
end

---金手指測試使用
function DropMgr.GoldFingerForceDrop()

    local _PlayerRC = RoleMgr.GetPlayerRC()

    --從列表清單取得一次要掉落幾顆
    if this.m_ParameterList[EDropFromType.GoldFinger].m_DropCount > 0 then
        this.m_GoldFingerAlreadyDropCountMax = this.m_ParameterList[EDropFromType.GoldFinger].m_DropCount
    end

    if this.m_ParameterList[EDropFromType.GoldFinger].m_UsePeriodInject == false then
        ---一次掉全部
        for i = 1, this.m_GoldFingerAlreadyDropCountMax do
            DropMgr.CreateExpDrop(_PlayerRC)
        end

        this.m_GoldFingerAlreadyDropCount = this.m_GoldFingerAlreadyDropCountMax
    else
        ---分成數次掉落 bool m_GoldFingerPeriodDrop 設定成true
        this.m_GoldFingerPeriodDrop = true
        DropMgr.PeriodDropItem(EDropFromType.GoldFinger , _PlayerRC,nil)
    end

end

---多次噴發掉落所有光球
---@param iEDropFromType EDropFromType 掉落類別
---@param iRoleController RoleController  掉落物剛生成時要綁在哪個RC 一般來說是怪的RoleController
---@param iItemIDTable table<number> 掉落物的列表
---@param iInitialPosition Vector3 掉落物來源怪物死掉時的位置(因為死亡延遲噴發 原本綁的RC 有可能因為被回收而跑到奇怪的位置)
---@param iNPCID number 噴完掉寶後要清除紀錄
function DropMgr.PeriodDropItem(iEDropFromType , iRoleController,iItemIDTable,iInitialPosition,iNPCID)

    ---有多少道具要噴
    local _TotalDrop
    if iItemIDTable == nil then
        _TotalDrop = this.m_ParameterList[iEDropFromType].m_DropCount
    else
        _TotalDrop = table.Count(iItemIDTable)
    end

    --- 單次噴幾個
    local _InjectCount = this.m_ParameterList[iEDropFromType].m_InjectCountOneTime
    --- 單次噴幾個的隨機值數值 (控制上限 下限)
    local _InjectCountRange = this.m_ParameterList[iEDropFromType].m_InjectCountOneTimeRange
    --- 兩次噴發間隔時間
    local _InjectPeriod = this.m_ParameterList[iEDropFromType].m_InjectPeriod/1000

    --每一次噴發的數量要不同
    --數量下限
    local _DownLimit = (_InjectCount-_InjectCountRange > 0) and (_InjectCount-_InjectCountRange) or 1
    --數量上限
    local _UpLimit = _InjectCount+_InjectCountRange

    ---還剩下幾個還沒被分配
    local _LeftCount = _TotalDrop

    ---掉落數量table
    local _DropAmountTable = {}
    ---掉落數量table index
    local _DropAmountTableFillIndex = 0

    ---分配掉落
    while _LeftCount > 0 do
        local _ThisTimeInject = math.floor(Random.Range(_DownLimit,_UpLimit+1))
        _DropAmountTableFillIndex = _DropAmountTableFillIndex + 1

       if _LeftCount >= _ThisTimeInject then
            _DropAmountTable[_DropAmountTableFillIndex] = _ThisTimeInject
            _LeftCount = _LeftCount - _ThisTimeInject
       else
            _DropAmountTable[_DropAmountTableFillIndex] = _LeftCount
            _LeftCount = 0
       end
    end

    ---呼叫tableID的哪一個參數
    local _AskItemIdIndex = 1

    if this.m_IsGoldFinggerTest then
         ---已經掉落
         this.m_GoldFingerAlreadyDropCount = 0
    end

    ---建立LeanTween
    local _Seq = LeanTween.sequence()

    local _Total = 0
    for i = 1, table.Count(_DropAmountTable) do

        ---第一次的掉落 不需要設定延遲
        local _DelayTime = (i == 1) and 0 or _InjectPeriod

        _Seq:append(LeanTween.delayedCall(_DelayTime, System.Action(
            function()
                for j = 1, _DropAmountTable[i] do

                    ---如果沒有填入 iItemIDTable  代表是使用金手指測試 所以掉經驗光球
                    if iItemIDTable == nil  then
                        DropMgr.CreateExpDrop(iRoleController)
                        this.m_GoldFingerAlreadyDropCount = this.m_GoldFingerAlreadyDropCount +1
                    elseif iItemIDTable[_AskItemIdIndex] ~=nil then
                        DropMgr.CreateItemDrop(iRoleController,iItemIDTable[_AskItemIdIndex],iEDropFromType,iInitialPosition)
                        _AskItemIdIndex = _AskItemIdIndex+1
                    end
                end
                _Total = _Total +  _DropAmountTable[i]
            end)
        ));
    end

    ---噴完後要清空紀錄對應NPCID的table
    if iNPCID~=nil then
        this.m_PeriodeInjectCollect[iNPCID] = nil
    end
end


this.m_PeriodeInjectCollect = {}

---建立延遲掉落清單By NPCID
---@param iNPCID number 要噴射料落物的NPCID
function DropMgr.BuildPeriodeInjectCollect(iNPCID)
    local _NPCRC = NPCMgr.GetNPCController(iNPCID)
    if _NPCRC then
        this.m_PeriodeInjectCollect[iNPCID] = {}
        this.m_PeriodeInjectCollect[iNPCID].m_InitialPosition = _NPCRC.gameObject.transform.position
        this.m_PeriodeInjectCollect[iNPCID].m_ItemID = {}
    else
        this.m_PeriodeInjectCollect[iNPCID] = nil
    end
end

---填入掉落物資訊
---@param iNPCID number 要噴射料落物的NPCID
---@param iItemID number 要掉落物品的串表ID
function DropMgr.FillItemIDData(iNPCID,iItemID)
    if this.m_PeriodeInjectCollect[iNPCID] == nil then
        DropMgr.BuildPeriodeInjectCollect(iNPCID)
        --this.m_PeriodeInjectCollect[iNPCID].m_ItemID = {}
    end
    if this.m_PeriodeInjectCollect[iNPCID] then
        table.insert(this.m_PeriodeInjectCollect[iNPCID].m_ItemID,iItemID)
    end
end

---執行週期性掉落
---@param iNPCID number 暫存的哪一個NPCID的掉落LeanTween要演出
function DropMgr.ExecuatePeriodDrop(iNPCID)
    if this.m_PeriodeInjectCollect[iNPCID] ~= nil then
        local _NPCRC = NPCMgr.GetNPCController(iNPCID)
        if _NPCRC then
            DropMgr.PeriodDropItem(EDropFromType.DropFromMob , _NPCRC,this.m_PeriodeInjectCollect[iNPCID].m_ItemID,this.m_PeriodeInjectCollect[iNPCID].m_InitialPosition,iNPCID)
        end
    end

end

function DropMgr.OnUnrequire()
    if this.m_DropRoot.gameObject ~=nil then
        this.m_DropRoot.gameObject:Destroy()
    end
    return true
end
return DropMgr
