fileFormatVersion: 2
guid: 49ff798a723c50c449faf37786153bf0
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Crocodile_Head_JawEndSHJnt
    100002: Crocodile_Head_JawSHJnt
    100004: Crocodile_Head_TopSHJnt
    100006: Crocodile_l_Clavicle_01_01SHJnt
    100008: Crocodile_l_FrontLeg_AnkleSHJnt
    100010: Crocodile_l_FrontLeg_BallSHJnt
    100012: Crocodile_l_FrontLeg_HipSHJnt
    100014: Crocodile_l_FrontLeg_KneeSHJnt
    100016: Crocodile_l_FrontLeg_ToeSHJnt
    100018: Crocodile_l_HindLeg_AnkleSHJnt
    100020: Crocodile_l_HindLeg_BallSHJnt
    100022: Crocodile_l_HindLeg_HipSHJnt
    100024: Crocodile_l_HindLeg_KneeSHJnt
    100026: Crocodile_l_HindLeg_ToeSHJnt
    100028: Crocodile_MAINSHJnt
    100030: Crocodile_Neck_01SHJnt
    100032: Crocodile_Neck_02SHJnt
    100034: Crocodile_Neck_TopSHJnt
    100036: Crocodile_r_Clavicle_01_01SHJnt
    100038: Crocodile_r_FrontLeg_AnkleSHJnt
    100040: Crocodile_r_FrontLeg_BallSHJnt
    100042: Crocodile_r_FrontLeg_HipSHJnt
    100044: Crocodile_r_FrontLeg_KneeSHJnt
    100046: Crocodile_r_FrontLeg_ToeSHJnt
    100048: Crocodile_r_HindLeg_AnkleSHJnt
    100050: Crocodile_r_HindLeg_BallSHJnt
    100052: Crocodile_r_HindLeg_HipSHJnt
    100054: Crocodile_r_HindLeg_KneeSHJnt
    100056: Crocodile_r_HindLeg_ToeSHJnt
    100058: Crocodile_ROOTSHJnt
    100060: Crocodile_Spine_01SHJnt
    100062: Crocodile_Spine_02SHJnt
    100064: Crocodile_Spine_03SHJnt
    100066: Crocodile_Spine_04SHJnt
    100068: Crocodile_Spine_TopSHJnt
    100070: Crocodile_Tail_01_01SHJnt
    100072: Crocodile_Tail_01_02SHJnt
    100074: Crocodile_Tail_01_03SHJnt
    100076: Crocodile_Tail_01_04SHJnt
    100078: Crocodile_Tail_01_05SHJnt
    100080: Crocodile_Tail_01_06SHJnt
    100082: Crocodile_Tail_01_07SHJnt
    100084: Crocodile_Tail_01_08SHJnt
    100086: //RootNode
    400000: Crocodile_Head_JawEndSHJnt
    400002: Crocodile_Head_JawSHJnt
    400004: Crocodile_Head_TopSHJnt
    400006: Crocodile_l_Clavicle_01_01SHJnt
    400008: Crocodile_l_FrontLeg_AnkleSHJnt
    400010: Crocodile_l_FrontLeg_BallSHJnt
    400012: Crocodile_l_FrontLeg_HipSHJnt
    400014: Crocodile_l_FrontLeg_KneeSHJnt
    400016: Crocodile_l_FrontLeg_ToeSHJnt
    400018: Crocodile_l_HindLeg_AnkleSHJnt
    400020: Crocodile_l_HindLeg_BallSHJnt
    400022: Crocodile_l_HindLeg_HipSHJnt
    400024: Crocodile_l_HindLeg_KneeSHJnt
    400026: Crocodile_l_HindLeg_ToeSHJnt
    400028: Crocodile_MAINSHJnt
    400030: Crocodile_Neck_01SHJnt
    400032: Crocodile_Neck_02SHJnt
    400034: Crocodile_Neck_TopSHJnt
    400036: Crocodile_r_Clavicle_01_01SHJnt
    400038: Crocodile_r_FrontLeg_AnkleSHJnt
    400040: Crocodile_r_FrontLeg_BallSHJnt
    400042: Crocodile_r_FrontLeg_HipSHJnt
    400044: Crocodile_r_FrontLeg_KneeSHJnt
    400046: Crocodile_r_FrontLeg_ToeSHJnt
    400048: Crocodile_r_HindLeg_AnkleSHJnt
    400050: Crocodile_r_HindLeg_BallSHJnt
    400052: Crocodile_r_HindLeg_HipSHJnt
    400054: Crocodile_r_HindLeg_KneeSHJnt
    400056: Crocodile_r_HindLeg_ToeSHJnt
    400058: Crocodile_ROOTSHJnt
    400060: Crocodile_Spine_01SHJnt
    400062: Crocodile_Spine_02SHJnt
    400064: Crocodile_Spine_03SHJnt
    400066: Crocodile_Spine_04SHJnt
    400068: Crocodile_Spine_TopSHJnt
    400070: Crocodile_Tail_01_01SHJnt
    400072: Crocodile_Tail_01_02SHJnt
    400074: Crocodile_Tail_01_03SHJnt
    400076: Crocodile_Tail_01_04SHJnt
    400078: Crocodile_Tail_01_05SHJnt
    400080: Crocodile_Tail_01_06SHJnt
    400082: Crocodile_Tail_01_07SHJnt
    400084: Crocodile_Tail_01_08SHJnt
    400086: //RootNode
    7400000: P_0026_Expr_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: P_0026_Expr_01
      takeName: P_0026_Expr_01_v2
      firstFrame: 0
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: bcb9eed9d2b36ee47bd7144020fa459d,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
