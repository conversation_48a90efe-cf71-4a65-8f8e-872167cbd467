---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---請輸入結構描述, 用途
---寵物合成介面 Controller 控制寵物合成的，寵物介面下的一個頁面。
---@class PetFuse_Controller
---author <PERSON> Wei
---telephone #2892
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.19
PetFuse_Controller = {}
local this = PetFuse_Controller

--- 仿生骨架道具號碼 寵物融合一定會需要的道具 基本款式這個
local FUSE_STATIC_PART = 85303

--- 最多會用到 3 個道具來融合
local FUSE_MAX_ITEM_USE = 3

--- 融合 Icon 大小
local FUSE_ICON_SIZE = 115

--- 種類 75 才是融合道具
local NEED_ITEM_TYPE = 75

--- 第三個道具可能會有開啟或關閉的狀況 前兩個保持開啟
local FIRST_ITEM = 1

local SECOND_ITEM = 2

local THIRD_ITEM = 3

--- 融合只是一般道具時
local NORMAL_ITEM = 1

--- 融合是特殊道具時
local SPECIAL_ITEM = 2

--- 融合時間
local FUSE_TIME = 2

--- 格子背景顏色 亮
local LIGHT_BG_COLOR = Extension.GetColor("#55caff")

--- 格子背景顏色 暗
local DARK_BG_COLOR = Extension.GetColor("#1e3044")

--- 字串 "選擇要製造的自律者"
local PICK_YOUR_PET     = 20323209

--- 字串 "選擇自律者或組件素材"
local PICK_PET_OR_ITEM  = 20323210

--- 字串 "沒有可製造的自律者組件"
local NO_PETITEM        = 20323211

--- 字串 "沒有可用的自律者或組件素材"
local NO_PET_AND_ITEM   = 20323212

--- 字串 "訊號遺失"
local SIGNAL_MISSING = 20323204

--- 字串 "隨機製造"
local RANDOM_MAKE = 20323203

--- 字串 "屬性預覽"
local SHOW_ATTRIBUTE = 20323213

--- 字串 "結果預測"
local SHOW_RESULT_RATE = 20323214

--- 字串 "已達自律者持有上限，無法製造"
local PET_LIMIT_STR = 20323220

--- 融合倒數
this.m_FuseTimeCountDown = 0

--- 是否開始倒數
this.m_IsFuseCountDown = false

--- 製作種類
local EFuseMode =
{
    --- 指定製作
    PetRecipe = 1,

    --- 隨機製作
    PetRandom = 2,
}

--- 機率增加圖
local ERate =
{
    --- 一格
    OneUp = 1,

    --- 兩格
    TwoUp = 2,

    --- 三格
    ThreeUp = 3,

}

local function GetShotUpRateImgStr(iNum)

    if(iNum == ERate.OneUp) then
        return "Common_pet_icon006"
    elseif(iNum == ERate.TwoUp) then
        return "Common_pet_icon07"
    elseif(iNum == ERate.ThreeUp) then
        return "Common_pet_icon008"
    else
        return nil --"Common_pet_icon009"
    end

end

local function GetMoneyTypeImgStr(iNum)

    if(iNum == ECurrencyType.Money ) then
        return "icon063051"
    elseif(iNum == ECurrencyType.HEMCoin) then
        return "icon063052"
    elseif(iNum == ECurrencyType.Diamond) then
        return "icon063053"
    else
        return nil
    end

end

--- 取融合資料
local function GetFuseData()

    if(this.m_Icons[FIRST_ITEM].m_SID == 0 or (this.m_Icons[THIRD_ITEM].m_SID == 0 and this.m_Icons[THIRD_ITEM].m_Parent.gameObject.activeSelf == true)) then
        return nil
    end

    local _FuseUseData = {}

    --- 如果不是 0 那這格就是道具 不是寵物 然後第三格已經被關掉了 代表這個道具是特殊道具
    if(this.m_Icons[FIRST_ITEM].m_ItemID ~= 0 and this.m_Icons[THIRD_ITEM].m_Parent.gameObject.activeSelf == false) then

        -- 取一下道具 Data
        local _ItemData = ItemData.GetItemDataByIdx(this.m_Icons[FIRST_ITEM].m_ItemID)

        _FuseUseData = Pet_Model.GetFuseNumberTable(_ItemData.m_Attributes[1].m_Value)

    else

        -- 都會寫寵物 ID 所以查寵物表就好
        local _FusePetID = Pet_Model.GetFuseCheckPetIDTable(this.m_Icons[FIRST_ITEM].m_PetID)

        -- 查有沒有找到東西
        if(_FusePetID ~= nil) then

            for i = 1, table.Count(_FusePetID) do
                local _FuseTable = Pet_Model.GetFuseNumberTable(_FusePetID[i])

                for j = 1, table.Count(_FuseTable.m_SpecialRecipePet) do

                    -- 找看有沒有一樣的
                    if(_FuseTable.m_SpecialRecipePet[j] == this.m_Icons[THIRD_ITEM].m_PetID and this.m_Icons[FIRST_ITEM].m_PetID ~= this.m_Icons[THIRD_ITEM].m_PetID) then

                        _FuseUseData = _FuseTable
                        break
                    end

                end

            end

        end

    end

    -- 沒有資料就給通用
    if(table.IsNullOrEmpty(_FuseUseData)) then

        -- 通用結果
        _FuseUseData = Pet_Model.GetFuseNumberTable(1)

    end

    return _FuseUseData

end

--- 顯示屬性預覽 or 抽獎機率
local function ShowDetailOrProbability()

    if this.m_FuseMode == EFuseMode.PetRecipe then
        -- 點擊"屬性預覽"
        if this.m_Icons[FIRST_ITEM].m_ItemID ~= nil then
            local _ItemData = ItemData.GetItemDataByIdx(this.m_Icons[FIRST_ITEM].m_ItemID)
            if(_ItemData == nil) then
                D.LogWarning("找不到對應的寵物組件物品ID")
                return
            end
            -- 物品表中:物品種類75(寵物組件)的「基本1 屬性基礎數值」為寵物ID
            local _PetHintData = {PetHint_Controller.DataType.PetID, _ItemData.m_Attributes[1].m_Value}
            HintMgr_Controller.OpenHint(EHintType.PetHint, _PetHintData)
        else
            D.LogWarning("請放上寵物組件")
        end

    elseif this.m_FuseMode == EFuseMode.PetRandom then
        -- 點擊"結果預測"
        local _FuseUseData = GetFuseData()

        if(_FuseUseData == nil) then
            return
        end

        -- 計算道具出現機率用
        ---@param table iMainCounts 福袋機率 table
        ---@param table iSubCounts 福袋底下的道具機率 table
        ---@param table iItemIDs 道具ID table
        ---@return table _IDs 返回道具ID
        ---@return table _Rates 返回抽取機率
        local function f_CalculateAndSortRates(iMainCounts, iSubCounts, iItemIDs)
            local _mainTotal = 0
            local _subTotals = {}
            local _detailedRates = {}

            -- 計算主項目總和
            for _, _mainCount in pairs(iMainCounts) do
                _mainTotal = _mainTotal + _mainCount
            end

            -- 計算細項總和
            for _mainIndex, _subCounts in pairs(iSubCounts) do
                _subTotals[_mainIndex] = 0
                for _, _subCount in pairs(_subCounts) do
                    _subTotals[_mainIndex] = _subTotals[_mainIndex] + _subCount
                end
            end

            -- 計算細項機率
            for _mainIndex, _mainCount in pairs(iMainCounts) do
                local _mainRate = (_mainCount / _mainTotal) * 100 -- 主項目全局機率
                for _subIndex, _subCount in pairs(iSubCounts[_mainIndex]) do
                    -- 細項全局機率
                    local _subRate = math.floor((_subCount / _subTotals[_mainIndex]) * _mainRate * 100) / 100
                    local _itemID = iItemIDs[_mainIndex][_subIndex]
                    if _subRate > 0 then
                        table.insert(_detailedRates, {
                            itemID = _itemID,
                            rate = _subRate
                        })
                    end
                end
            end

            -- 按機率排序（由小到大） 如果機率一樣大就用 ID 來排
            table.sort(_detailedRates, function(a, b)

                if a.rate == b.rate then
                    return a.itemID < b.itemID
                end

                return a.rate < b.rate

            end)

            local _IDs, _Rates = {}, {}
            for _, _detail in pairs(_detailedRates) do
                table.insert(_IDs, _detail.itemID)
                table.insert(_Rates, _detail.rate)
            end

            return _IDs, _Rates
        end

        local _PackIDs = {}
        local _PackRates = {}
        local _ItemIDs = {}
        local _ItemRates = {}

        for _key, _value in pairs(_FuseUseData.m_Lottery) do

            if(_value ~= nil and _value.m_NeedItemID ~= 0 and _value.m_NeedItemNum ~= 0) then
                table.insert(_PackIDs, _value.m_NeedItemID)
                table.insert(_PackRates, _value.m_NeedItemNum)
            end

        end

        for _key, _value in pairs(_PackIDs) do

            local _FudaiData = FuDaiData.GetFuDaiDataByIdx(_value)

            if(_FudaiData ~= nil) then

                local _IDs = {}
                local _Rates = {}
                for _k, _v in pairs(_FudaiData.m_Rewards) do

                    table.insert(_IDs, _v.m_ItemIdx)
                    table.insert(_Rates, _v.m_Probability)

                end

                table.insert(_ItemIDs, _IDs)
                table.insert(_ItemRates, _Rates)

            end

        end

        local _IDTable = {}
        local _ReturnItemRates = {}
        _IDTable, _ReturnItemRates = f_CalculateAndSortRates(_PackRates, _ItemRates, _ItemIDs)

        local _CommonQueryIDX = 513

        local _Type4Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.ShowProbability)
        _Type4Data:Type4_BuildData(EIconType.Pet,_IDTable, _ReturnItemRates, {})

        CommonQueryMgr.AddNewInform(
            _CommonQueryIDX,
            {},
            {},
            function ()
                            end,
            {},
            nil,
            {},
            nil,
            {},
            _Type4Data)
    end

end

--- 抓那些 UI 的東西
local function InitialUI()

    -- 沒有訊號
    this.m_SignalMissing = this.m_ViewRef.m_Dic_Trans:Get("&Image_SignalMissing")

    -- 訊號字串
    this.m_SignalMissing_Text = this.m_ViewRef.m_Dic_Trans:Get("&Text_SignalMissing"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )

    -- 消耗道具部分
    this.m_PanelItems = this.m_ViewRef.m_Dic_Trans:Get("&Panel_Items")
    this.m_PanelIcons = this.m_PanelItems:Find("Panel_Icons")
    this.m_Icons = {}

    for i = 1, FUSE_MAX_ITEM_USE do
        local _Str = "Image_IconFrame_" .. i
        this.m_Icons[i] = {}
        this.m_Icons[i].m_Parent = this.m_PanelIcons:Find(_Str)
        -- ItemIcon 是暫時的 需要等 PetIcon 完成，再看要用哪種方法做
        this.m_Icons[i].m_Item = IconMgr.NewItemIcon(0, this.m_Icons[i].m_Parent.gameObject.transform, FUSE_ICON_SIZE, function()this.m_Icons[i].m_Item.m_LongPressCallback[1](this.m_Icons[i].m_Item) end)
        --this.m_Icons[i].Item:SetLongPress(false)
        this.m_Icons[i].m_Item:SetClickTwice(false)
        if(i ~= SECOND_ITEM) then
            this.m_Icons[i].m_PetIcon = IconMgr.NewPetIcon(0, this.m_Icons[i].m_Parent.gameObject.transform, FUSE_ICON_SIZE)
            --this.m_Icons[i].m_PetIcon:SetLongPress(false)
            this.m_Icons[i].m_PetIcon:SetClickTwice(false)
        end
        -- 記一下 SID 後面比較方便
        this.m_Icons[i].m_SID = 0

        this.m_Icons[i].m_Name = this.m_Icons[i].m_Parent:Find("Text_Name"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )

        if(i == SECOND_ITEM) then

            PetFuse_Controller.SetItem2()

        end

    end

    -- Icon 道具或寵物移除按鈕 2 不需要
    this.m_CloseIcon1 = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_X_1")
    Button.AddListener(this.m_CloseIcon1, EventTriggerType.PointerClick, function()
        PetFuse_Controller.ClearPetIcon(FIRST_ITEM)
        PetFuse_Controller.CheckLightUp()
        this.m_CloseIcon1.gameObject:SetActive(false)
        Pet_Model.ClearPetModel()

        -- 格子顏色關閉
        this.m_Icons[FIRST_ITEM].m_Parent:GetComponent(typeof(Image)).color = DARK_BG_COLOR

        -- 看是不是隨機製作寵物 是的話第三格開啟
        if(this.m_FuseMode == EFuseMode.PetRandom) then
            this.m_Icons[THIRD_ITEM].m_Parent.gameObject:SetActive(true)

            -- 順便顯示這東西稀有不稀有
            local _Str = GetShotUpRateImgStr(0)
            if(_Str == nil) then
                -- 特殊寵物機率關掉
                this.m_RarePetShowUpRate.gameObject:SetActive(false)
            else
                -- 特殊寵物機率開啟
                this.m_RarePetShowUpRate.gameObject:SetActive(true)
                SpriteMgr.Load( _Str, this.m_RarePetShowUpRateImg)
            end
        end
    end)

    this.m_CloseIcon3 = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_X_3")
    Button.AddListener(this.m_CloseIcon3, EventTriggerType.PointerClick, function()
        PetFuse_Controller.ClearPetIcon(THIRD_ITEM)
        PetFuse_Controller.CheckLightUp()
        this.m_CloseIcon3.gameObject:SetActive(false)

        -- 格子顏色關閉
        this.m_Icons[THIRD_ITEM].m_Parent:GetComponent(typeof(Image)).color = DARK_BG_COLOR
    end)

    -- 放大鏡按鈕
    this.m_ShowBtn = this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Detail")
    Button.AddListener(this.m_ShowBtn, EventTriggerType.PointerClick, function()
        ShowDetailOrProbability()
    end)

    -- 寵物 3D 顯示
    this.m_PetShowCanvas = this.m_ViewRef.m_Dic_Trans:Get("&PetShowCanvas")

    -- 放大鏡後面的字串
    this.m_ShowBtn_Text = this.m_ViewRef.m_Dic_Trans:Get("&Button_Detail"):Find("Text_Detail"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )

    -- 消耗道具滿足要亮起的光線 直衡兩條
    this.m_VerticalLine = this.m_PanelItems:Find("Image_VerticalLine_Light")
    this.m_HorizontalLine = this.m_PanelItems:Find("Image_HorizontalLine_Light")

    -- 稀有自律者出現率
    this.m_RarePetShowUpRate = this.m_ViewRef.m_Dic_Trans:Get("&Text_RarePetShowUpRate")
    this.m_RarePetShowUpRateImg = this.m_RarePetShowUpRate:Find("Image_ShowUpRate"):GetComponent( typeof( Image ) )

    -- 消告花費
    this.m_Cost = this.m_ViewRef.m_Dic_Trans:Get("&Image_Cost")
    this.m_ImgCoin = this.m_Cost:Find("Image_Coin")
    this.m_CoinKind = 0
    this.m_CostNumber = 0
    this.m_CostTxt = this.m_Cost:Find("Text_CostMoney"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )

    -- 確認按鈕
    this.m_BtnConfirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_ConfirmFuse"))
    Button.AddListener(this.m_BtnConfirm, EventTriggerType.PointerClick,
    function()

        -- 可以改變條件的一個數字
        local _ChangeFuseNum = 0

        -- 這邊還要檢查是不是隨機合成 如果是的話兩隻寵物的又要可以合
        if(this.m_FuseMode == EFuseMode.PetRandom) then

            -- 第一格和第三格都是寵物的情況
            if(this.m_Icons[FIRST_ITEM].m_ItemID == 0 and this.m_Icons[THIRD_ITEM].m_ItemID == 0) then
                _ChangeFuseNum = 1
            end

        end

        -- 如果還沒有達到寵物上限就可以製作 + _ChangeFuseNum 是為了可以分辨是不是兩格都是寵物
        if(Pet_Model.GetPlayerPetCount() >= Pet_Model.GetCarryPetLimit() + _ChangeFuseNum) then

            -- 中央訊息告知寵物上限了
            MessageMgr.AddCenterMsg(true, TextData.Get(PET_LIMIT_STR))

            return

        else

            this.m_FuseTimeCountDown = 0
            this.m_IsFuseCountDown = true
            PetFuse_Controller.LoadingSetting(0, "0%", true)

        end

    end)

    this.m_BtnCancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_CancelFuse"))
    Button.AddListener(this.m_BtnCancel, EventTriggerType.PointerClick,
    function()
        this.m_FuseTimeCountDown = 0
        this.m_IsFuseCountDown = false
        PetFuse_Controller.LoadingSetting(0, "0%", false)
    end)

    -- 製作倒數
    this.m_MakingLoading = this.m_ViewRef.m_Dic_Trans:Get("&Image_MakingLoading")
    this.m_MakingSlider = this.m_MakingLoading:Find("Slider"):GetComponent("Slider")
    this.m_MakingPercent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Percent")

    --- 製作目前的模式是
    this.m_FuseMode = EFuseMode.PetRecipe
end

--- 設定製作中畫面需要的東西
---@param number iSliderValue 跑條數值
---@param string iPercentText 幾趴顯示
---@param bool iActiveBool 要不要開啟介面
function PetFuse_Controller.LoadingSetting(iSliderValue, iPercentText, iActiveBool)

    this.m_MakingSlider.value = iSliderValue
    this.m_MakingPercent.text = iPercentText
    this.m_MakingLoading.gameObject:SetActive(iActiveBool)

end

---初始化
function PetFuse_Controller.Init(iController)

    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    InitialUI()

end

--- 重製融合時間相關
local function ReSetFuseTime()

    this.m_FuseTimeCountDown = 0
    this.m_IsFuseCountDown = false
    PetFuse_Controller.LoadingSetting(0, "0%", false)

end

---Update
function PetFuse_Controller.Update()

    -- 倒數
    if(this.m_IsFuseCountDown == true) then

        this.m_FuseTimeCountDown = this.m_FuseTimeCountDown + HEMTimeMgr.m_DeltaTime
        local _Percentage = math.ceil(this.m_FuseTimeCountDown / FUSE_TIME * 100)
        this.m_MakingSlider.value = _Percentage / 100
        this.m_MakingPercent.text = "".._Percentage.."%"

        -- 跑滿就合 要看哪個模式
        if(_Percentage >= 100 and this.m_FuseMode == EFuseMode.PetRecipe) then

            ReSetFuseTime()
            SendProtocol_020._020_02(this.m_Icons[FIRST_ITEM].m_ItemID, this.m_Icons[SECOND_ITEM].m_Item.m_Idx, this.m_Icons[FIRST_ITEM].m_PetID)

        elseif(_Percentage >= 100 and this.m_FuseMode == EFuseMode.PetRandom) then

            ReSetFuseTime()

            --- 這邊要分寵物道具和寵物兩個 table 再塞到協定裡
            local _PetItem = {}
            local _Pet = {}

            -- 第一格是不是道具 這格是道具才會有 ID
            if(this.m_Icons[FIRST_ITEM].m_ItemID ~= 0) then
                table.insert(_PetItem, this.m_Icons[FIRST_ITEM])
            elseif(next(this.m_Icons[FIRST_ITEM])) then
                table.insert(_Pet, this.m_Icons[FIRST_ITEM])
            end

            -- 第三格是不是道具 這格是道具才會有 ID
            if(this.m_Icons[THIRD_ITEM].m_ItemID ~= 0) then
                table.insert(_PetItem, this.m_Icons[THIRD_ITEM])
            elseif(next(this.m_Icons[THIRD_ITEM]) and this.m_Icons[THIRD_ITEM].m_SID ~= 0) then -- 多檢查一個 SID 是不是 0 來看有沒有東西
                table.insert(_Pet, this.m_Icons[THIRD_ITEM])
            end

            SendProtocol_020._020_04(this.m_Icons[SECOND_ITEM].m_Item.m_Idx, _PetItem, _Pet)

        end

    end

end

--- 寵物能不能被融合 在隨機製作時不需要被顯示的寵物
---@param table iPetDat 寵物資料
---@return bool _IsCanFused 寵物能不能被融合結果
local function CanPetFuse(iPetData)

    local _IsCanFused = false

    -- 寵物指定製作沒有限制寵物能不能被融合
    if(this.m_FuseMode == EFuseMode.PetRecipe)then

        return true

    elseif(iPetData.m_IsNoFuse == 0)then

        return true

    end

    return _IsCanFused

end

--- 取背包寵物相關道具 會過一層能不能被融合的判定
---@return table _ItemList 寵物道具 table
local function PetItemFromBag()

    -- 道具列表
    local _ItemList = {}

    -- 取背包種類為標記的 然後開始找
    local _TBag = BagMgr.GetBagTable(EBagType.Component)	-- 取得背包資料

    for _key, _value in pairs(_TBag) do

        -- 規則上塞選符合的道具
        if(_value.m_ItemData.m_Type == NEED_ITEM_TYPE and
        (_value.m_ItemData.m_Attributes[1].m_Kind == NORMAL_ITEM or
        (this.m_FuseMode == EFuseMode.PetRandom and _value.m_ItemData.m_Attributes[1].m_Kind == SPECIAL_ITEM))) then

            -- 創建 table
            local _Table = PetList_Controller.CreateEmptyListDataTable()

            -- 寵物 ID
            _Table.m_ID = _value.m_ItemData.m_Attributes[1].m_Value

            -- 寵物 ID 去抓寵物資料
            local _PetData = PetData.GetPetDataByIdx(_Table.m_ID)

            -- 還要過一層能不能融合判斷 如果是特殊道具沒抓到寵物資料也沒關西
            if(_PetData and CanPetFuse(_PetData) or _value.m_ItemData.m_Attributes[1].m_Kind == SPECIAL_ITEM) then

                -- 填的是 npc 編號來去抓名稱
                local _String = TextData.Get(_value.m_ItemData.m_Idx_ItemNameText)
                --TextData.Get(NPCData:Get(_PetData.m_NPCID).m_NameID)

                -- 寵物名字
                _Table.m_Name = _String

                -- 寵物種類 一般道具再加就好
                if(_value.m_ItemData.m_Attributes[1].m_Kind == NORMAL_ITEM) then
                    _Table.m_Type = _PetData.m_PetType
                end

                -- 寵物頭像
                _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_value.m_ItemData.m_ID_Picture, 6)
                --ICON_STR .. GValue.Zero_stuffing(_PetData.m_Pet2DIcon, 6)

                -- 顯數數量
                _Table.m_IsShowNumber = true

                -- 數量
                _Table.m_Number = _value.m_Count

                -- 列表中種類
                _Table.m_DataType = EPetDataType.ITEM

                -- Side_Btn_Animation
                _Table.m_SID = _value.m_SID

                -- 道具才要設
                _Table.m_ItemID = _value.m_ItemIdx

                table.insert(_ItemList, _Table)

            end

        end

    end

    return _ItemList

end

--- 取玩家寵物 會過一層能不能被融合的判定
local function PetFromPlayer()

    local _MyPetData = PetMgr.GetPlayerPetData()

    local _StackPetDataToNumber = {}
    for _k, _v in pairs(_MyPetData) do

        local _PetData = PetData.GetPetDataByIdx(_v.m_PetID)
        _v.m_PetName = TextData.Get(NPCData:Get(_PetData.m_NPCID).m_NameID)

        -- 只能要不是最愛的寵物
        if(not _v.m_IsPetLoved and CanPetFuse(_PetData)) then
            if(_StackPetDataToNumber[_v.m_PetID]) then
                local _Number = _StackPetDataToNumber[_v.m_PetID].m_Number + 1
                _StackPetDataToNumber[_v.m_PetID].m_Number = _StackPetDataToNumber[_v.m_PetID].m_Number + 1
                _StackPetDataToNumber[_v.m_PetID].m_EachData[_Number] = _v
            else
                _StackPetDataToNumber[_v.m_PetID] = _v
                _StackPetDataToNumber[_v.m_PetID].m_Number = 1
                _StackPetDataToNumber[_v.m_PetID].m_EachData = {}
                _StackPetDataToNumber[_v.m_PetID].m_EachData[1] = _v
            end
        end


    end

    local _ShowData = {}
    for _k, _v in pairs(_StackPetDataToNumber) do

        -- 創建 table
        local _Table = PetList_Controller.CreateEmptyListDataTable()
        _Table.m_ID = _v.m_PetID
        _Table.m_Name = _v.m_PetName
        _Table.m_Type = _v.m_BasicData.m_PetType
        _Table.m_Icon = _v.m_BasicData:GetPetTextureName()
        _Table.m_IsShowNumber = true
        _Table.m_Number = _v.m_Number
        _Table.m_DataType = EPetDataType.PET
        _Table.m_SID = _v.m_PetSlot
        _Table.m_AllData = _v.m_EachData

        table.insert(_ShowData, _Table)
    end

    return _ShowData

end

--- 取玩家寵物 第二種 企劃不要第一種
local function PlayerPet()

        -- 整理完基本排序的
        local _MyPetData = Pet_Model.ArrangePetListOrder(EPetArrange[1].Power, true)

        -- 沒東西就 return
        if(table.IsNullOrEmpty(_MyPetData)) then
            PetList_Controller.SetShowData({})
            return {}
        end

        -- 第一個固定是跟隨中寵物
        -- 招喚中寵物 SID
        local _SummonedPet = PetMgr.GetSummonedPetPlot()

        -- 忙的寵物 (派遣中 或 招喚中)
        local _BusyPetData = {}

        -- 其它閒閒沒事做的
        local _OtherPets = {}

        for _, _pet in pairs(_MyPetData) do

            if(_pet.m_PetSlot == _SummonedPet or _pet.m_PetState == EMissionMissionState.Working or _pet.m_PetState == EMissionMissionState.CollectAble) then

                table.insert(_BusyPetData, _pet)

            else

                table.insert(_OtherPets, _pet)

            end

        end

        -- 清空原表
        _MyPetData = {}

        -- 先放不忙的寵物
        for _, _pet in pairs(_OtherPets) do
            table.insert(_MyPetData, _pet)  -- 把其它寵物都加進來
        end

        -- 如果 _BusyPetData 有東西才執行 insert
        if(next(_BusyPetData)) then
            for _, _pet in pairs(_BusyPetData) do
                table.insert(_MyPetData, _pet)
            end
        end


        -- 設定寵物列表要用的資料
        local _ShowData = {}
        for _k, _v in pairs(_MyPetData) do

            local _State = false
            if(_v.m_PetState == EMissionMissionState.Working or _v.m_PetState == EMissionMissionState.CollectAble) then
                _State = true
            end

            -- 創建 table
            local _Table = PetList_Controller.CreateEmptyListDataTable()
            _Table.m_ID = _v.m_PetID
            _Table.m_Name = _v.m_PetName
            _Table.m_Type = _v.m_BasicData.m_PetType
            _Table.m_Icon = ICON_STR .. GValue.Zero_stuffing(_v.m_BasicData.m_Pet2DIcon, 6)
            _Table.m_Star = _v.m_StarLv
            _Table.m_State = _State
            _Table.m_IsShowLikePet = true
            _Table.m_IsPetLoved = _v.m_IsPetLoved
            _Table.m_IsShowNumber = false
            _Table.m_IsClickCover = true
            _Table.m_Number = _v.m_Power
            _Table.m_DataType = EPetDataType.PET
            _Table.m_SID = _v.m_PetSlot

            table.insert(_ShowData, _Table)
        end

        return _ShowData

end

--- 開啟介面
function PetFuse_Controller.Open(iParam)

    -- 設定寵物顯示
    if (Extension.IsUnityObjectNull(this.m_PetShowRT)) then
        --- 把 RenderTexture 讀進來
        ResourceMgr.Load("PetRenderTexture", function(iAsset)
            this.m_PetShowRT = iAsset
            AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
            AppearanceMgr.SetClearFlagToSolidColor()
            AppearanceMgr.EnableCamera(true)
        end)
    else
        AppearanceMgr.SetTargetTexture(this.m_PetShowRT)
        AppearanceMgr.SetClearFlagToSolidColor()
        AppearanceMgr.EnableCamera(true)
    end

    -- 設定二格道具
    PetFuse_Controller.SetItem2()

    -- 所需錢 0
    this.m_ImgCoin.gameObject:SetActive(false)
    this.m_CoinKind = 0
    this.m_CostNumber = 0
    this.m_CostTxt.text = ""

    -- 確認按鈕改關閉
    Button.SetDisable(this.m_BtnConfirm, false)

    -- 打開是固定製作
    PetFuse_Controller.Option2Click(this.m_FuseMode)

    PetList_Controller.SetPanelOption1On(false)

    -- 要是不是空的就是指定要點選那個道具的
    if(next(iParam)) then

        -- 道具列表
        local _ItemList = PetItemFromBag()

        if(next(_ItemList)) then

            for _k, _v in pairs(_ItemList) do

                if(_v.ItemID == iParam[1]) then
                    PetFuse_Controller.PetListClick(_v)
                    PetList_Controller.Refresh()
                    break
                end
            end

        end
    end

    -- 調整一下 相機的設定
    AppearanceMgr.TurnOnPhysicalCameraAndSetToFixValue()

    return true
end

function PetFuse_Controller.OnDestroy()
    return true
end

function PetFuse_Controller.Close()

    AppearanceMgr.SetTargetTexture(nil)
    AppearanceMgr.EnableCamera(false)
    Pet_Model.ClearPetModel()

end

--- 列表可使用顯示按鈕的第二種
function PetFuse_Controller.Option2Click(iNumber)

    -- 清除格子内資料
    PetFuse_Controller.ClearPetIcon(FIRST_ITEM)
    PetFuse_Controller.ClearPetIcon(THIRD_ITEM)

    this.m_CloseIcon1.gameObject:SetActive(false)
    this.m_CloseIcon3.gameObject:SetActive(false)

    Pet_Model.ClearPetModel()

    -- 設定製作模式
    this.m_FuseMode = iNumber

    -- 第一按鈕是指定 二是隨機
    if(iNumber == EFuseMode.PetRecipe) then

        -- 指定製造
        PetFuse_Controller.SetPetRecipeMakeListData()

        -- 指定用不到第三個格子
        this.m_Icons[THIRD_ITEM].m_Parent.gameObject:SetActive(false)

        -- 設定製作中間的字串
        this.m_SignalMissing_Text.text = TextData.Get(SIGNAL_MISSING)

        -- 設定放大鏡後面的字串
        this.m_ShowBtn_Text.text = TextData.Get(SHOW_ATTRIBUTE)

    else

        -- 隨機製造
        PetFuse_Controller.SetPetRandomMakeListData()

        -- 一般會用到第三格 除了特殊道具
        this.m_Icons[THIRD_ITEM].m_Parent.gameObject:SetActive(true)

        -- 設定製作中間的字串
        this.m_SignalMissing_Text.text = TextData.Get(RANDOM_MAKE)

        -- 設定放大鏡後面的字串
        this.m_ShowBtn_Text.text = TextData.Get(SHOW_RESULT_RATE)

    end

    PetFuse_Controller.CheckLightUp()

end

function PetFuse_Controller.RefreshAfterProtocol()

    PetFuse_Controller.Option2Click(this.m_FuseMode)

end

--- 清除合成道具 Icon 資料
---@type number iNumber 要清哪個
function PetFuse_Controller.ClearPetIcon(iNumber)

    PetList_Controller.DeleteSelectedItem(this.m_Icons[iNumber].m_SID)
    this.m_Icons[iNumber].m_Item:RefreshIcon(0)
    this.m_Icons[iNumber].m_ItemID = nil
    this.m_Icons[iNumber].m_Item.gameObject:SetActive(true)
    this.m_Icons[iNumber].m_PetIcon.gameObject:SetActive(false)

    this.m_Icons[iNumber].m_IsData = false

    this.m_Icons[iNumber].m_SID = 0

    this.m_Icons[iNumber].m_Name.text = ""

    this.m_Icons[iNumber].m_ItemID = 0

    -- 格子顏色關閉
    this.m_Icons[iNumber].m_Parent:GetComponent(typeof(Image)).color = DARK_BG_COLOR

    PetList_Controller.Refresh()

    this.m_ImgCoin.gameObject:SetActive(false)
    this.m_CoinKind = 0
    this.m_CostNumber = 0
    this.m_CostTxt.text = ""
end

--- 寵物點選 有成功設訂的話 return true
function PetFuse_Controller.PetListClick(iData)

    -- 塞到格子中
    -- 要確認格子是不是有被顯示 & 有沒有資料了才能給他塞資料
    -- 能塞的只有 1 和 3，2 是固定的

    -- 塞入資料用
    --@param number iSelf 要塞入的
    --@param number iOther 另一個的
    --@param table iItemData 道具資料
    --@return bool 有木有成功
    local function f_InputData(iSelf, iOther, iItemData)

        -- 這邊要檢查給的資料數量到底夠不夠，所以要看一下另一邊的格子有沒有同樣的，有的話數量又只有 1 的 return false，自身如果不是 Item 跳過
        if (this.m_Icons[iOther].m_IsData == true and
            iItemData.m_ID == this.m_Icons[iOther].m_PetID and
            iItemData.m_Number == 1 and iItemData.m_ItemID ~= 0) then
            return false
        end

        local _SID = iItemData.m_SID
        -- 要拿和另一格子不同的道具
        if (iItemData.m_SID == this.m_Icons[iOther].m_SID) then
            for _k, _v in pairs(iItemData.m_AllData) do
                if (_SID ~= _v.m_PetSlot) then
                    _SID = _v.m_PetSlot
                    break
                end
            end
        end

        -- 如果不是道具要看 SID 是不是重複的
        if (_SID == this.m_Icons[iOther].m_SID and iItemData.m_ItemID == 0) then
            return false
        end

        local _IsPet = iItemData.m_ItemID == 0 -- 是否是寵物
        this.m_Icons[iSelf].m_Item.gameObject:SetActive(not _IsPet)
        this.m_Icons[iSelf].m_PetIcon.gameObject:SetActive(_IsPet)

        if _IsPet then
            this.m_Icons[iSelf].m_PetIcon:RefreshIcon(iData.m_ID)
        else
            this.m_Icons[iSelf].m_Item:RefreshIcon(iItemData.m_ItemID)
        end

        -- 寵物 ID
        this.m_Icons[iSelf].m_PetID = iItemData.m_ID

        -- 道具 ID
        this.m_Icons[iSelf].m_ItemID = iItemData.m_ItemID

        -- Server ID
        this.m_Icons[iSelf].m_SID = _SID

        -- 名字顯示
        this.m_Icons[iSelf].m_Name.text = iItemData.m_Name

        -- 有沒有資料了
        this.m_Icons[iSelf].m_IsData = true

        -- 設定選擇到表中
        PetList_Controller.SetSelectedItem(_SID, iItemData.m_ID)

        -- 格子顏色開啟
        this.m_Icons[iSelf].m_Parent:GetComponent(typeof(Image)).color = LIGHT_BG_COLOR

        return true
    end

    -- 如果是道具，要檢查他是不是特別道具
    if (iData.m_ItemID ~= 0) then

        -- 取一下道具 Data
        local _ItemData = ItemData.GetItemDataByIdx(iData.m_ItemID)

        -- 看看是不是特殊道具
        if (_ItemData.m_Attributes[1].m_Kind == SPECIAL_ITEM) then

            -- 把第三格關起來
            this.m_Icons[THIRD_ITEM].m_Parent.gameObject:SetActive(false)

            -- 檢查第一格有沒有道具
            if (this.m_Icons[FIRST_ITEM].m_IsData) then

                -- 清除
                PetFuse_Controller.ClearPetIcon(FIRST_ITEM)
                PetFuse_Controller.ClearPetIcon(THIRD_ITEM)
                f_InputData(FIRST_ITEM, THIRD_ITEM, iData)
                this.m_CloseIcon1.gameObject:SetActive(true)

            else
                -- 清除
                PetFuse_Controller.ClearPetIcon(THIRD_ITEM)
                this.m_CloseIcon3.gameObject:SetActive(false)
                f_InputData(FIRST_ITEM, THIRD_ITEM, iData)
                this.m_CloseIcon1.gameObject:SetActive(true)
            end
        end
    end

    -- 指定模式，有點寵物列表就塞進第一格
    if (this.m_FuseMode == EFuseMode.PetRecipe) then

        PetFuse_Controller.ClearPetIcon(FIRST_ITEM)

        -- 塞資料
        local _IsOn = f_InputData(FIRST_ITEM, THIRD_ITEM, iData)

        -- 開啟 X
        this.m_CloseIcon1.gameObject:SetActive(_IsOn)

        -- 模型顯示要打開
        Pet_Model.SetPetModel(iData.m_ID)

    -- 判斷那個 Icon 可以塞資料
    elseif (this.m_Icons[FIRST_ITEM].m_Parent.gameObject.activeSelf == true and this.m_Icons[FIRST_ITEM].m_IsData == false) then

        -- 塞資料
        local _IsOn = f_InputData(FIRST_ITEM, THIRD_ITEM, iData)

        -- 開啟 X
        this.m_CloseIcon1.gameObject:SetActive(_IsOn)

    elseif (this.m_Icons[THIRD_ITEM].m_Parent.gameObject.activeSelf == true and this.m_Icons[THIRD_ITEM].m_IsData == false) then

        -- 塞資料
        local _IsOn = f_InputData(THIRD_ITEM, FIRST_ITEM, iData)

        -- 開啟 X
        this.m_CloseIcon3.gameObject:SetActive(_IsOn)
    end

    -- 檢查用多少錢
    PetFuse_Controller.CheckCost()

    -- 檢查亮起
    PetFuse_Controller.CheckLightUp()
end

--- 資料放上後要查一下是不是所有格子都滿了 滿了的話線要亮起
function PetFuse_Controller.CheckLightUp()

    -- 一定要檢查第一個 然後看第二格有沒有夠數量 最後是判斷有沒有需要第三格 如果需要 第三格有沒有資料
    if(this.m_Icons[FIRST_ITEM].m_Parent.gameObject.activeSelf == true and this.m_Icons[FIRST_ITEM].m_IsData == true and
        -- 這邊晚些等企劃給道具 ID 要去背包找看看有沒有這個道具 然後看數量夠不夠
        (this.m_Icons[THIRD_ITEM].m_Parent.gameObject.activeSelf == true and this.m_Icons[THIRD_ITEM].m_IsData == true or
        this.m_Icons[THIRD_ITEM].m_Parent.gameObject.activeSelf == false)) then

        this.m_VerticalLine.gameObject:SetActive(true)
        this.m_HorizontalLine.gameObject:SetActive(true)

        -- 確認按鈕開啟
        local _InBag = BagMgr.GetFirstSaveItemDataByItemIdx(FUSE_STATIC_PART)
        local _CostByCoin = PlayerData.GetCurrency(this.m_CoinKind)
        if(_InBag and _CostByCoin >= this.m_CostNumber) then

            -- 確認按鈕開啟
            Button.SetEnable(this.m_BtnConfirm)

        else
            -- 確認按鈕改關閉
            Button.SetDisable(this.m_BtnConfirm, false)

        end


        -- 沒訊號關閉
        if(this.m_FuseMode == EFuseMode.PetRecipe) then
            this.m_SignalMissing.gameObject:SetActive(false)
        end

    else

        this.m_VerticalLine.gameObject:SetActive(false)
        this.m_HorizontalLine.gameObject:SetActive(false)

        -- 確認按鈕改關閉
        Button.SetDisable(this.m_BtnConfirm, false)

        -- 沒訊號開啟
        this.m_SignalMissing.gameObject:SetActive(true)

    end

end

--- 檢查要用多少錢
function PetFuse_Controller.CheckCost()

    if(this.m_FuseMode == EFuseMode.PetRecipe) then

        if(this.m_Icons[FIRST_ITEM]) then

            local _PetData = PetData.GetPetDataByIdx(this.m_Icons[FIRST_ITEM].m_PetID)

            this.m_CoinKind = _PetData.m_ProduceTokenKind
            local _CoinStr = GetMoneyTypeImgStr(this.m_CoinKind )
            this.m_ImgCoin.gameObject:SetActive(true)
            local _Img = this.m_ImgCoin:GetComponent(typeof(Image))
            SpriteMgr.Load( _CoinStr, _Img)
            this.m_CostNumber = _PetData.m_ProduceTokenCost
            this.m_CostTxt.text = _PetData.m_ProduceTokenCost

        end

    else

        local FuseData = GetFuseData()

        if(FuseData) then

            -- 設顯示會使用多少錢錢
            this.m_CoinKind = FuseData.m_NeedCostType
            local _CoinStr = GetMoneyTypeImgStr(this.m_CoinKind )
            local _Img = this.m_ImgCoin:GetComponent(typeof(Image))
            SpriteMgr.Load( _CoinStr, _Img)

            this.m_ImgCoin.gameObject:SetActive(true)
            this.m_CostNumber = FuseData.m_NeedCostNumber
            this.m_CostTxt.text = FuseData.m_NeedCostNumber

            -- 順便顯示這東西稀有不稀有
            local _Str = GetShotUpRateImgStr(FuseData.m_RarePetShowIcon)
            if(_Str == nil) then
                -- 特殊寵物機率關掉
                this.m_RarePetShowUpRate.gameObject:SetActive(false)
            else
                -- 特殊寵物機率開啟
                this.m_RarePetShowUpRate.gameObject:SetActive(true)
                SpriteMgr.Load( _Str, this.m_RarePetShowUpRateImg)
            end

        end

    end

end

--- 目前只有一種但可能會處其他的
function PetFuse_Controller.SetItem2()
    -- 道具資料
    local _ItemData = ItemData.GetItemDataByIdx(FUSE_STATIC_PART)
    local _InBag = BagMgr.GetFirstSaveItemDataByItemIdx(FUSE_STATIC_PART)

    this.m_Icons[SECOND_ITEM].m_Item:SetTexture(ICON_STR .. GValue.Zero_stuffing(_ItemData.m_ID_Picture, 6))
    this.m_Icons[SECOND_ITEM].m_Item.m_Idx = FUSE_STATIC_PART
    this.m_Icons[SECOND_ITEM].m_IsData = true

    this.m_Icons[SECOND_ITEM].m_Name.text = TextData.Get(_ItemData.m_Idx_ItemNameText)

    -- 找背包裡面有沒有道具
    if(_InBag) then
        this.m_Icons[SECOND_ITEM].m_SID = _InBag.m_SID
        this.m_Icons[SECOND_ITEM].m_Item.m_Image_CD.gameObject:SetActive(false)
    else
        this.m_Icons[SECOND_ITEM].m_Item.m_Image_CD.gameObject:SetActive(true)
    end
end

function PetFuse_Controller.SetPetRecipeMakeListData()

    -- 道具列表
    local _ItemList = PetItemFromBag()

    -- 給清單資料
    PetList_Controller.SetShowData(_ItemList)

    if (next(_ItemList)) then
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(PICK_YOUR_PET), "W"))
    else
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(NO_PETITEM), "W"))
    end

    PetList_Controller.SetExtraValue(false, 0)

end

function PetFuse_Controller.SetPetRandomMakeListData()

    -- 道具列表
    local _ItemList = PetItemFromBag()

    -- 身上寵物
    local _PlayerPet = PlayerPet()

    if (next(_ItemList) or next(_PlayerPet)) then
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(PICK_PET_OR_ITEM), "W"))
    else
        PetList_Controller.SetExtraInfo(true, GString.StringWithStyle(TextData.Get(NO_PET_AND_ITEM), "W"))
    end

    -- 合起兩個 table
    local _CombineTable = {}
    for _, _v in pairs(_ItemList) do
        table.insert(_CombineTable, _v)
    end

    for _, _v in pairs(_PlayerPet) do
        table.insert(_CombineTable, _v)
    end

    -- 給資料清單
    PetList_Controller.SetShowData(_CombineTable)

end

--- 設定寵物畫布開關
---@param bool iOn 畫布要開嗎?
function PetFuse_Controller.SetPetCanvasShow(iOn)

    this.m_PetShowCanvas.gameObject:SetActive(iOn)

end
