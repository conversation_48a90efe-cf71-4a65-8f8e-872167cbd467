﻿//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2022 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    //////////////////////////////////////////////////////////////////////////////////////////-
//
//=====================================================================

using GameTools;
using GameTools.Log;
using GameTools.Project;
using TMPro;
using UnityEngine;


/// <summary>
/// Loading UI
/// <AUTHOR>
/// @version 1.0
/// @since [黃易群俠傳M] 0.50
/// @date 2022.7.15
/// </summary>
public class UILoading : MonoBehaviour
{
    /// <summary>
    /// Debug資訊
    /// </summary>
    [SerializeField]
    private TMP_Text m_Text_DebugMessage;

    /// <summary>
    /// 版本資訊(畫面左下)
    /// </summary>
    [SerializeField]
    private TMP_Text m_Text_VersionMessage;

    /// <summary>
    /// 顯示資訊(中間直式文字)
    /// </summary>
    [SerializeField]
    private TMP_Text m_Text_DownLoad;

    /// <summary>
    /// 顯示細節(中下橫式文字)
    /// </summary>
    [SerializeField]
    private TMP_Text m_TMP_Detail;

    /// <summary>
    /// Percentage數值顯示
    /// </summary>
    [Header("Percentage數值顯示")]
    [SerializeField]
    private TMP_Text m_TMP_SliderPercentage;



    private bool m_IsDirty;
    private float m_Progress;
    private bool m_IsCheck = false;

    private CoroutineMgr.CoroutineEx m_CoroutineMgr;
    private void Start()
    {
        m_Text_DebugMessage.gameObject.SetActive( ProjectMgr.IsDebug() );
    }
    void Update()
    {
        if( m_IsDirty )
        {
            m_IsDirty = false;
        }
    }
    /// <summary>
    /// 設定 Loading UI 顯示
    /// </summary>
    /// <param name="iActive"></param>
    public void SetUIActive( bool iActive )
    {
        gameObject.SetActive( iActive );
    }
    /// <summary>
    /// 設定 Debug 資訊
    /// </summary>
    /// <param name="iInfo">顯示的訊息</param>
    public void SetDebugInfo( string iInfo )
    {
        m_Text_DebugMessage.gameObject.SetActive( ProjectMgr.IsDebug() && !string.IsNullOrEmpty( iInfo ) );
        if( iInfo != m_Text_DebugMessage.text )
        {
            if( ProjectMgr.IsDebug() )
            {
                m_Text_DebugMessage.gameObject.SetActive( true );
            }
            else
            {
                m_Text_DebugMessage.gameObject.SetActive( false );
                return;
            }
            m_Text_DebugMessage.text = iInfo;
            m_IsDirty = true;
        }
    }

    public void SetDetail( string iInfo )
    {
        if( iInfo != m_TMP_Detail.text )
        {
            m_TMP_Detail.gameObject.SetActive( true );
            m_TMP_Detail.text = iInfo;
            m_IsDirty = true;
        }
    }
    /// <summary>
    /// 設定 進程數值
    /// </summary>
    /// <param name="iProgress">進度數值</param>
    public void SetProgress( float iProgress )
    {
        if( iProgress != m_Progress )
        {
            m_Progress = iProgress;
            m_IsDirty = true;
        }
    }
    /// <summary>
    /// 顯示提示訊息
    /// </summary>
    /// <param name="iInfo"></param>
    public void ShowHintBox( string iInfo )
    {
        D.Log( iInfo );
        m_IsCheck = string.IsNullOrEmpty( iInfo );
    }

    public async System.Threading.Tasks.Task WaitUserClick()
    {
        while ( !m_IsCheck )
        {
            await System.Threading.Tasks.Task.Yield();
        }
    }

    public void OnClick_OK()
    {
        m_IsCheck = true;
    }
    /// <summary>
    /// 設定下載訊息
    /// </summary>
    /// <param name="iMsg"></param>
    /// <param name="iProgress"></param>
    public void SetDownLoadInfo( string iMsg, float iProgress )
    {
        m_Text_DownLoad.gameObject.SetActive(true);
        m_TMP_SliderPercentage.gameObject.SetActive(true);

        m_Text_DownLoad.text = iMsg;
        m_TMP_SliderPercentage.text = $"{Mathf.FloorToInt(iProgress * 100)}";
    }

    public void DownloadFinish()
    {
        m_Text_DownLoad.gameObject.SetActive( false );
        m_TMP_SliderPercentage.gameObject.SetActive(false);

        m_Text_DownLoad.text = "";
        m_TMP_SliderPercentage.text = "0";

        CoroutineMgr.StopAllCustomCoroutineEx(m_CoroutineMgr);
        m_CoroutineMgr = null;
    }
    /// <summary>
    /// 設定版本資訊
    /// </summary>
    /// <param name="iExeVersion">執行黨版本</param>
    /// <param name="iResourceVersion">資源版本</param>
    public void SetVersionInfo(string iExeVersion, string iResourceVersion)
    {
        m_Text_VersionMessage.text = string.Format(ResTextGetter.GetResText("Version"), iExeVersion, iResourceVersion);
    }
}
