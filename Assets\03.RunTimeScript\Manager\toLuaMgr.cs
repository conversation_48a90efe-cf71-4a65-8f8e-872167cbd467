﻿//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2021 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------
//
//=====================================================================

using LuaInterface;
using System;

/// <summary>
/// Lua 管理器
/// <AUTHOR>
/// @version 1.0
/// @since [HEM] 2.0
/// @date 2021.10.8
/// </summary>
public class toLuaMgr : MonoSingleton<toLuaMgr>
{
    private LuaFileUtils m_LuaLoader;
    private LuaState m_LuaState;
    private LuaLooper m_LuaLooper;

    /// <summary>
    /// 判斷 Game.Init 是否完成
    /// </summary>
    public bool m_IsGameInit = false;

    public override void Do()
    {
        base.Do();
    }

    protected override void Init()
    {
        base.Init();
    }

    /// <summary>
    /// 初始化 Lua 底層
    /// </summary>
    public void RunLuaInit()
    {
        //Reset
        Close();
        //Init Loader
#if UNITY_EDITOR
        if( ResourceMgr.m_EditorUseRemoteAssets )
        {
            m_LuaLoader = new LuaResLoader();
        }
        else
        {
            m_LuaLoader = new LuaEditorLoader();
        }
#else
        m_LuaLoader = new LuaResLoader();
#endif

        //Init Lua
        m_LuaState = new LuaState();

        m_LuaState.OpenLibs( LuaDLL.luaopen_pb );
        //m_LuaState.OpenLibs(LuaDLL.luaopen_sproto_core);
        //m_LuaState.OpenLibs(LuaDLL.luaopen_protobuf_c);
        m_LuaState.OpenLibs( LuaDLL.luaopen_lpeg );
        m_LuaState.OpenLibs( LuaDLL.luaopen_bit );
        m_LuaState.OpenLibs( LuaDLL.luaopen_socket_core );

        m_LuaState.LuaGetField( LuaIndexes.LUA_REGISTRYINDEX, "_LOADED" );
        m_LuaState.OpenLibs( LuaDLL.luaopen_cjson );
        m_LuaState.LuaSetField( -2, "cjson" );

        m_LuaState.OpenLibs( LuaDLL.luaopen_cjson_safe );
        m_LuaState.LuaSetField( -2, "cjson.safe" );

#if UNITY_EDITOR
        LuaConst.openLuaSocket = true;

        m_LuaState.BeginPreLoad();
        m_LuaState.RegFunction( "socket.core", LuaOpen_Socket_Core );
        m_LuaState.RegFunction( "mime.core", LuaOpen_Mime_Core );
        m_LuaState.EndPreLoad();
#endif

        m_LuaState.LuaSetTop( 0 );

        LuaBinder.Bind( m_LuaState );
        DelegateFactory.Init();
        LuaCoroutine.Register( m_LuaState, this );

        //Init Path
        //#if ( UNITY_EDITOR && !EditorUseRemoteAssets ) || TestLua
        //        if ( AppConst.DebugMode )
        //        {
        //            string rootPath = AppConst.FrameworkRoot;
        //            m_LuaState.AddSearchPath( rootPath + "/Lua" );
        //            m_LuaState.AddSearchPath( rootPath + "/ToLua/Lua" );
        //        }
        //        else
        //        {
        //            m_LuaState.AddSearchPath( Util.DataPath + "lua" );
        //        }
        //#else
        //            m_LuaState.AddSearchPath(Application.persistentDataPath + "/Lua");
        //#if iOSFirstPack
        //            m_LuaState.AddSearchPath(Application.streamingAssetsPath + "/Lua");
        //#endif
        //#endif

        //Init Bundle
        // if (m_LuaLoader.beZip)
        // {
        // m_LuaLoader.AddBundle("Lua/lua.unity3d");
        // // m_LuaLoader.AddBundle("lua/lua_3rd_cjson.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_cjson.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_jit.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_lpeg.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_misc.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_protobuf.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_socket.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_system.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_system_reflection.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_system_injection.unity3d");
        // m_LuaLoader.AddBundle("Lua/lua_unityengine.unity3d");

        // m_LuaLoader.AddBundle("lua/lua_common.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_logic.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_view.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_controller.unity3d");

        // m_LuaLoader.AddBundle("lua/lua_math.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_3rd_luabitop.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_3rd_pbc.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_3rd_pblua.unity3d");
        // m_LuaLoader.AddBundle("lua/lua_3rd_sproto.unity3d");
        // }

        //Start Lua
        m_LuaState.Start(); //启动LUAVM

        //Start Looper
        m_LuaLooper = gameObject.AddComponent<LuaLooper>();
        m_LuaLooper.luaState = m_LuaState;
    }

    public void DoFile( string iFilename )
    {
        m_LuaState.DoFile( iFilename );
    }

    public void DoString( string iStr )
    {
        m_LuaState.DoString( iStr );
    }

    /// <summary>
    /// 純Call Lua Function, 沒有回傳
    /// </summary>
    /// <param name="iFuncName"> Lua function name </param>
    /// <param name="iArgs"> 帶入的參數 </param>
    public void CallFunction( string iFuncName, params object[] iArgs )
    {
        if( m_LuaState == null )
            return;

        //D.Log( $"{ iFuncName } Call" );
        LuaFunction _Func = m_LuaState.GetFunction( iFuncName );
        if( _Func != null )
        {
            //2022.08.30 Modify By KK 如果使用 Call( iArgs ) 會導致傳入toLua 變為 System.object[]
            //用以下方式 才會變成 Table~~
            _Func.BeginPCall();
            _Func.PushArgs( iArgs );
            _Func.PCall();
            _Func.EndPCall();
        }
    }

    /// <summary>
    /// Call Lua Function, 有回傳, 請指定回傳型別
    /// </summary>
    /// <typeparam name="R"> 你的回傳型別 </typeparam>
    /// <param name="iFuncName"> Lua function name </param>
    /// <param name="iArgs"> 帶入的參數 </param>
    /// <returns></returns>
    public R CallFunction<R>( string iFuncName, params object[] iArgs )
    {
        //D.Log( $"{ iFuncName } Call" );
        LuaFunction _Func = m_LuaState.GetFunction( iFuncName );
        if( _Func != null )
        {
            return _Func.InvokeEx<R>( iArgs );
        }
        return default;
    }

    public void Call<T>( System.Object iCallBack, T[] iObjs )
    {
        if( iCallBack is LuaFunction )
        {
            switch( iObjs.Length )
            {
                case 1:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ] );
                    break;
                case 2:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ] );
                    break;
                case 3:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ] );
                    break;
                case 4:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ] );
                    break;
                case 5:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ], iObjs[ 4 ] );
                    break;
                case 6:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ], iObjs[ 4 ], iObjs[ 5 ] );
                    break;
                case 7:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ], iObjs[ 4 ], iObjs[ 5 ], iObjs[ 6 ] );
                    break;
                case 8:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ], iObjs[ 4 ], iObjs[ 5 ], iObjs[ 6 ], iObjs[ 7 ] );
                    break;
                case 9:
                    ( (LuaFunction)iCallBack ).Call( iObjs[ 0 ], iObjs[ 1 ], iObjs[ 2 ], iObjs[ 3 ], iObjs[ 4 ], iObjs[ 5 ], iObjs[ 6 ], iObjs[ 7 ], iObjs[ 8 ] );
                    break;
                    //default:
                    //    ((LuaFunction)iCallBack).Call(iObjs as UnityEngine.Object[]);
                    //    break;
            }
        }
    }

    public void CallFromArray<T>( System.Object iCallBack, T[] iObjs )
    {
        if( iCallBack is LuaFunction )
        {
            ( (LuaFunction)iCallBack ).CallFromArray( iObjs as UnityEngine.Object[] );
        }
    }

    public LuaFunction GetFunction( string iFuncName )
    {
        return m_LuaState.GetFunction( iFuncName );
    }

    public void LuaGC()
    {
        m_LuaState.LuaGC( LuaGCOptions.LUA_GCCOLLECT );
    }

    public void Close()
    {
        m_IsGameInit = false;
        if (m_LuaLooper != null)
        {
            //刪除 LuaLooper 元件
            UnityEngine.Object.Destroy(m_LuaLooper);
            m_LuaLooper.Destroy();
            m_LuaLooper = null;
        }

        if( m_LuaState != null )
        {
            m_LuaState.Dispose();
            m_LuaState = null;
        }

        if( m_LuaLoader != null )
        {
            m_LuaLoader.Dispose();
            m_LuaLoader = null;
        }
    }

    public LuaState GetLuaState()
    {
        return m_LuaState;
    }

    [MonoPInvokeCallbackAttribute( typeof( LuaCSFunction ) )]
    static int LuaOpen_Socket_Core( IntPtr L )
    {
        return LuaDLL.luaopen_socket_core( L );
    }

    [MonoPInvokeCallbackAttribute( typeof( LuaCSFunction ) )]
    static int LuaOpen_Mime_Core( IntPtr L )
    {
        return LuaDLL.luaopen_mime_core( L );
    }
}
