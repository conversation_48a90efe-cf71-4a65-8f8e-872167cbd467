---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---Icon 設定 參數控制
---@class IconSetting
---author Hui
---version 1.0
---since [ProjectBase] 0.1
---date 2023.12.13
IconSetting = {}
local this = IconSetting

---@class EIconFrameItemType 物品框架種類
this.EIconFrameItemType = {}
this.EIconFrameItemType[ERank.None] = "Frame_Square_None"
this.EIconFrameItemType[ERank.White] = "Frame_Square_White"
this.EIconFrameItemType[ERank.Green] = "Frame_Square_Green"
this.EIconFrameItemType[ERank.Blue] = "Frame_Square_Blue"
this.EIconFrameItemType[ERank.Purple] = "Frame_Square_Purple"
this.EIconFrameItemType[ERank.Red] = "Frame_Square_Red"
this.EIconFrameItemType[ERank.Gold] = "Frame_Square_Gold"
this.EIconFrameItemType[ERank.DarkGold] = "Frame_Square_DarkGold"
this.EIconFrameItemType[ERank.Rainbow] = "Frame_Square_Rainbow"

---@class EIconFrameOtherType 其他框架種類
this.EIconFrameOtherType = {}
this.EIconFrameOtherType[EOtherItemIconFrameType.None] = "Frame_Square_None"
this.EIconFrameOtherType[EOtherItemIconFrameType.AutoUse] = "Frame_Square_AutoUse"

---@class EIconRankFrameEffectId 物品稀有度外框特效
this.EIconRankFrameEffectId = {}
-- this.EIconRankFrameEffectId[ERank.White] = 40123
this.EIconRankFrameEffectId[ERank.Purple] = 40123
this.EIconRankFrameEffectId[ERank.Red] = 40124
this.EIconRankFrameEffectId[ERank.Gold] = 40125
this.EIconRankFrameEffectId[ERank.DarkGold] = 40126 -- 暗金
this.EIconRankFrameEffectId[ERank.Rainbow] = 40127 -- 彩

---@class EIconRankGrandPrizeEffectId 物品稀有度大獎特效
this.EIconRankGrandPrizeEffectId = {}
-- this.EIconRankGrandPrizeEffectId[ERank.White] = 40128
this.EIconRankGrandPrizeEffectId[ERank.Purple] = 40128
this.EIconRankGrandPrizeEffectId[ERank.Red] = 40129
this.EIconRankGrandPrizeEffectId[ERank.Gold] = 40130
this.EIconRankGrandPrizeEffectId[ERank.DarkGold] = 40131 -- 暗金
this.EIconRankGrandPrizeEffectId[ERank.Rainbow] = 40132 -- 彩

---@class EIconFrameSkillType 技能框架種類
this.EIconFrameSkillType = {}
this.EIconFrameSkillType[SkillData.ESkillType.NormalAtk] = "Frame_Circle_Atk_Normal"
this.EIconFrameSkillType[SkillData.ESkillType.Skill_1] = "Frame_Circle_Atk_Skill"
this.EIconFrameSkillType[SkillData.ESkillType.Skill_2] = "Frame_Circle_Atk_Skill"
this.EIconFrameSkillType[SkillData.ESkillType.Skill_3] = "Frame_Circle_Atk_Skill"
this.EIconFrameSkillType[SkillData.ESkillType.Skill_4] = "Frame_Circle_Atk_Skill"
this.EIconFrameSkillType[SkillData.ESkillType.CoreSkill] = "Frame_Circle_Atk_CoreSkill"
this.EIconFrameSkillType[SkillData.ESkillType.MoveSkill] = "Frame_Circle_Atk_Skill"
this.EIconFrameSkillType[SkillData.ESkillType.InternalSkill] = "Frame_Square_Skill"

---@class IconStatusId 物品狀態文字
this.IconStatusId = {}
this.IconStatusId[EItemStatus.Normal] = 0
this.IconStatusId[EItemStatus.SafeTransaction] = 3150
this.IconStatusId[EItemStatus.Auction] = 3152
this.IconStatusId[EItemStatus.Riding] = 3264
this.IconStatusId[EItemStatus.Probe ] = 3265
this.IconStatusId[EItemStatus.Sell] = 3154
this.IconStatusId[EItemStatus.ProbePlace] = 3515
this.IconStatusId[EItemStatus.StealItem] = 3441
this.IconStatusId[EItemStatus.TransactionLock] = 3204
this.IconStatusId[EItemStatus.RideLock] = 3264
this.IconStatusId[EItemStatus.Binding] = 0 -- 綁定狀態用鎖頭表示，不需要字串
this.IconStatusId[EItemStatus.DressUp] = 0
this.IconStatusId[EItemStatus.TryOn] = 0
this.IconStatusId[EItemStatus.StealItemLock] = 0

--- 物品最大數量顯示上限
this.m_ITEMMAXCOUNT = 3000
--- 物品數量超過 3000 的字體大小
this.IconTextSizeSmall = 23

--- Icon 框架讀取起始參數
this.m_IconFrameReadValueStart = 1
--- Icon 框架讀取結束參數
this.m_IconFrameReadValueEnd = 2

---@class ESpecialTextureType 特殊圖片種類
this.ESpecialTextureType = 
{
    --- + 號
    Add = {m_SpriteName = "MainBtn_095", m_Color = "#c0c1c1"} -- #c0c1c1
}

--- 幾秒後算長按
this.m_WaitLongPressTime = 0.2
--- 長按秒數
this.m_LongPressTime = 0.8

this.ETwoStepTextId = {
    None = "",
    TakeOff = "TakeOff",
    Cancel = "Cancel",
    Set = "Set",
}

--- 啟用兩階段點選時，選中的顯示文字
this.m_SelectTextId = {}
this.m_SelectTextId[this.ETwoStepTextId.TakeOff] = 1003
this.m_SelectTextId[EItemType.None] = 1004
this.m_SelectTextId[this.ETwoStepTextId.Cancel] = 1001
this.m_SelectTextId[this.ETwoStepTextId.Set] = 20201076

--- 戰鬥 icon 放大尺寸
this.m_AtkIconScale = 1.2
--- 戰鬥 icon 放大尺寸動作時間
this.m_AtkIconScaleTime = 0.2

--- 預設 CD 遮罩透明度
this.m_CDMaskAlpha = 198 / 255

--- 物品選擇框 圖片名稱
this.m_IconSelectImageName = "IconFrame_16"
--- 物品設定框 圖片名稱
this.m_IconSetImageName = "IconFrame_15"

--- 我的最愛 圖片名稱
this.m_IconHeartImageName = "MainBtn_036"
--- 新物品 圖片名稱
this.m_IconNewItemImageName = "MainBtn_038"
--- 裝備中 圖片名稱
this.m_IconWearImageName = "MainBtn_070"

--- BuffIcon 層數顯示尺寸 (左上玩家資訊 & 目標資訊)
this.m_BuffIconCountSize = Vector3.New(1.5, 1.5, 1.5)

--region Buff Icon 相關
--- 是否閃爍
this.m_IsShining = true
--- 是否使用螢光棒
this.m_IsLighting = true
--- 要不要黑色遮照
this.m_IsMask = true

-- --- 閃爍頻率訊息提示
-- this.m_MessageShow = false

--- 閃爍開始 Alpha
this.m_TweenAlphaFrom = 0.3
--- 閃爍結束 Alpha
this.m_TweenAlphaTo = 1

--- 閃爍設定
--- [流水號 (請照順序)] = {m_ShiningTime = 小於幾秒開始閃爍, m_ShiningFrequency = 閃爍頻率}
this.m_ShowShiningTimeAy = 
{
    [1] = {m_ShiningTime = 5, m_ShiningFrequency = 0.5},
    [2] = {m_ShiningTime = 2, m_ShiningFrequency = 0.15},
}
--endregion Buff Icon 相關

--region 招式 icon 下方提示字串

--- Icon 顯示招式類型 = 字串編號
this.m_IconHotkeyText = 
{
    [0] = 0,
    [1] = 50000001,
    [2] = 50000002,
    [3] = 50000003,
    [4] = 50000004,
    [5] = 50000005,
    [6] = 50000006,   
    [7] = 50000007,
    [8] = 50000008,
    [9] = 50000009,
    [10] = 50000010,
}

--- 加在後面，給 PC 版用的 ( {0} 拿來顯示 PC 快捷按鍵的 )
this.m_IconHotkeyPCKeyText = "[{0}]"

--- 給普攻用的向下的距離
this.m_IconHotkeyText_NormalPos = Vector3.New(0,-79.5,0)
--endregion 招式 icon 下方提示字串

---@class ItemSetting.EMultSelectGroup 多選群組
this.EMultSelectGroup = 
{
    Bag = "Bag",
}

---@class ESkillCoreFrameColor
this.ESkillCoreFrameColor = {
    [EWeaponType.Knife] = {Extension.GetColor("#54F937"), Extension.GetColor("#B4FBAF")},
    [EWeaponType.Sword] = {Extension.GetColor("#F93937"), Extension.GetColor("#FBAFB1")},
    [EWeaponType.Fist] = {Extension.GetColor("#F98C37"), Extension.GetColor("#FBCCAF")},
    [EWeaponType.Pike] = {Extension.GetColor("#37A5F9"), Extension.GetColor("#AFF1FB")},
    [EWeaponType.Stick] = {Extension.GetColor("#9D37F9"), Extension.GetColor("#D6AFFB")},
}
