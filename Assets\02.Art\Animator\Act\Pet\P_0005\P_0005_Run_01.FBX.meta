fileFormatVersion: 2
guid: 297a549156a56bf40ade28a2e09a2959
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001 Head
    100002: Bip001 Spine1
    100004: //RootNode
    100006: WildRabbit_Head_JawSHJnt
    100008: WildRabbit_l_Clavicle_01_01SHJnt
    100010: WildRabbit_l_Ears_01_01SHJnt
    100012: WildRabbit_l_Ears_01_02SHJnt
    100014: WildRabbit_l_Ears_01_03SHJnt
    100016: WildRabbit_l_FrontLeg_AnkleSHJnt
    100018: WildRabbit_l_FrontLeg_BallSHJnt
    100020: WildRabbit_l_FrontLeg_HipSHJnt
    100022: WildRabbit_l_FrontLeg_KneeSHJnt
    100024: WildRabbit_l_FrontLeg_ToeSHJnt
    100026: WildRabbit_l_HindLeg_AnkleSHJnt
    100028: WildRabbit_l_HindLeg_BallSHJnt
    100030: WildRabbit_l_HindLeg_HipSHJnt
    100032: WildRabbit_l_HindLeg_KneeSHJnt
    100034: WildRabbit_l_HindLeg_ToeSHJnt
    100036: WildRabbit_MAINSHJnt
    100038: WildRabbit_Neck_01SHJnt
    100040: WildRabbit_Neck_02SHJnt
    100042: WildRabbit_Neck_TopSHJnt
    100044: WildRabbit_Nose_01_01SHJnt
    100046: WildRabbit_Nose_01_02SHJnt
    100048: WildRabbit_r_Clavicle_01_01SHJnt
    100050: WildRabbit_r_Ears_01_01SHJnt
    100052: WildRabbit_r_Ears_01_02SHJnt
    100054: WildRabbit_r_Ears_01_03SHJnt
    100056: WildRabbit_r_FrontLeg_AnkleSHJnt
    100058: WildRabbit_r_FrontLeg_BallSHJnt
    100060: WildRabbit_r_FrontLeg_HipSHJnt
    100062: WildRabbit_r_FrontLeg_KneeSHJnt
    100064: WildRabbit_r_FrontLeg_ToeSHJnt
    100066: WildRabbit_r_HindLeg_AnkleSHJnt
    100068: WildRabbit_r_HindLeg_BallSHJnt
    100070: WildRabbit_r_HindLeg_HipSHJnt
    100072: WildRabbit_r_HindLeg_KneeSHJnt
    100074: WildRabbit_r_HindLeg_ToeSHJnt
    100076: WildRabbit_ROOTSHJnt
    100078: WildRabbit_Spine_01SHJnt
    100080: WildRabbit_Spine_02SHJnt
    100082: WildRabbit_Spine_03SHJnt
    100084: WildRabbit_Spine_TopSHJnt
    100086: WildRabbit_Tail_01_01SHJnt
    400000: Bip001 Head
    400002: Bip001 Spine1
    400004: //RootNode
    400006: WildRabbit_Head_JawSHJnt
    400008: WildRabbit_l_Clavicle_01_01SHJnt
    400010: WildRabbit_l_Ears_01_01SHJnt
    400012: WildRabbit_l_Ears_01_02SHJnt
    400014: WildRabbit_l_Ears_01_03SHJnt
    400016: WildRabbit_l_FrontLeg_AnkleSHJnt
    400018: WildRabbit_l_FrontLeg_BallSHJnt
    400020: WildRabbit_l_FrontLeg_HipSHJnt
    400022: WildRabbit_l_FrontLeg_KneeSHJnt
    400024: WildRabbit_l_FrontLeg_ToeSHJnt
    400026: WildRabbit_l_HindLeg_AnkleSHJnt
    400028: WildRabbit_l_HindLeg_BallSHJnt
    400030: WildRabbit_l_HindLeg_HipSHJnt
    400032: WildRabbit_l_HindLeg_KneeSHJnt
    400034: WildRabbit_l_HindLeg_ToeSHJnt
    400036: WildRabbit_MAINSHJnt
    400038: WildRabbit_Neck_01SHJnt
    400040: WildRabbit_Neck_02SHJnt
    400042: WildRabbit_Neck_TopSHJnt
    400044: WildRabbit_Nose_01_01SHJnt
    400046: WildRabbit_Nose_01_02SHJnt
    400048: WildRabbit_r_Clavicle_01_01SHJnt
    400050: WildRabbit_r_Ears_01_01SHJnt
    400052: WildRabbit_r_Ears_01_02SHJnt
    400054: WildRabbit_r_Ears_01_03SHJnt
    400056: WildRabbit_r_FrontLeg_AnkleSHJnt
    400058: WildRabbit_r_FrontLeg_BallSHJnt
    400060: WildRabbit_r_FrontLeg_HipSHJnt
    400062: WildRabbit_r_FrontLeg_KneeSHJnt
    400064: WildRabbit_r_FrontLeg_ToeSHJnt
    400066: WildRabbit_r_HindLeg_AnkleSHJnt
    400068: WildRabbit_r_HindLeg_BallSHJnt
    400070: WildRabbit_r_HindLeg_HipSHJnt
    400072: WildRabbit_r_HindLeg_KneeSHJnt
    400074: WildRabbit_r_HindLeg_ToeSHJnt
    400076: WildRabbit_ROOTSHJnt
    400078: WildRabbit_Spine_01SHJnt
    400080: WildRabbit_Spine_02SHJnt
    400082: WildRabbit_Spine_03SHJnt
    400084: WildRabbit_Spine_TopSHJnt
    400086: WildRabbit_Tail_01_01SHJnt
    7400000: P_0005_Run_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: P_0005_Run_01
      takeName: P_0005_Run_01
      firstFrame: 1
      lastFrame: 13
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 732e08c3a248c8e4fb59baa7ea7f19bb,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
