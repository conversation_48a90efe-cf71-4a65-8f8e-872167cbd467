---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Class Describes
---@class lua
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.10
NumberPanel_Controller = {}
local this = NumberPanel_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("NumberPanel_View", "NumberPanel_Controller", EUIOrderLayers.Peak)

this.m_MaxValue = 0
this.m_MinValue = 0
this.m_CurrentValue = 0
this.m_InitialValue = 0


---初始化
function NumberPanel_Controller.Init()

    this.m_NumberPanelObj = this.m_ViewRef.m_Dic_Trans:Get("&NumberPanelObj")

    ---所有外觀像數字按鍵的按鍵 添加共通的UI效果方法
    this.m_PanelButtons ={}

    local _NumberBtnStr = "&Btn_No"
    this.m_NumberButtons = {}
    ---數字鍵  0到9
    for i = 1, 10 do
       
        local _NumberIndex = i%10 -- 取餘數
        local _Str = _NumberBtnStr.._NumberIndex
        this.m_NumberButtons[i] =  this.m_ViewRef.m_Dic_Trans:Get(_Str)

        Button.AddListener(this.m_NumberButtons[i], EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickNumber(_NumberIndex) end)

        NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_NumberButtons[i])
    end
    
    --退一位 按鍵
    this.m_ReduceTen_Btn = this.m_ViewRef.m_Dic_Trans:Get("&ReduceTen_Btn")   
    Button.AddListener(this.m_ReduceTen_Btn, EventTriggerType.PointerClick, NumberPanel_Controller.ClickReduceTen)     
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_ReduceTen_Btn)

    --數值歸零 按鍵
    this.m_AC_Btn = this.m_ViewRef.m_Dic_Trans:Get("&AC_Btn")
    Button.AddListener(this.m_AC_Btn, EventTriggerType.PointerClick, NumberPanel_Controller.ClickAC)
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_AC_Btn)

    ---當前數值+1(底部) 按鍵
    this.m_AddOne_Bottom_Btn = this.m_ViewRef.m_Dic_Trans:Get("&AddOne_Bottom_Btn")
    Button.AddListener(this.m_AddOne_Bottom_Btn, EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickAdd(1) end)
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_AddOne_Bottom_Btn)

    ---當前數值+10 按鍵
    this.m_AddTen_Btn = this.m_ViewRef.m_Dic_Trans:Get("&AddTen_Btn")
    Button.AddListener(this.m_AddTen_Btn, EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickAdd(10) end)
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_AddTen_Btn)

    ---當前數值+100 按鍵
    this.m_AddHundred_Btn = this.m_ViewRef.m_Dic_Trans:Get("&AddHundred_Btn")
    Button.AddListener(this.m_AddHundred_Btn, EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickAdd(100) end)
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_AddHundred_Btn)

    ---設定為最大值 按鍵
    this.m_MaxNum_Btn = this.m_ViewRef.m_Dic_Trans:Get("&MaxNum_Btn")
    Button.AddListener(this.m_MaxNum_Btn, EventTriggerType.PointerClick, NumberPanel_Controller.ClickMax)
    NumberPanel_Controller.CollectAllNumberTypeButtons(this.m_MaxNum_Btn)

    ---點擊 / 滑鼠進出演出效果
    for i = 1, table.Count(this.m_PanelButtons) do
        
        Button.AddListener(this.m_PanelButtons[i].m_Btn, EventTriggerType.PointerDown, function() this.m_PanelButtons[i].m_FeedBackSet:SetActive(true)  end)  
        --Button.AddListener(this.m_PanelButtons[i].m_Btn, EventTriggerType.PointerUp, function() this.m_PanelButtons[i].m_FeedBackSet:SetActive(false) end)
        
        Button.AddListener(this.m_PanelButtons[i].m_Btn, EventTriggerType.PointerEnter, function() this.m_PanelButtons[i].m_FeedBackSet:SetActive(true)  end)  
        Button.AddListener(this.m_PanelButtons[i].m_Btn, EventTriggerType.PointerExit, function() this.m_PanelButtons[i].m_FeedBackSet:SetActive(false) end)  
    
    end

    ---非數字按鍵類 按鍵賦值
    --數值顯示區數值-1 按鍵
    this.m_ReduceOne_Btn = this.m_ViewRef.m_Dic_Trans:Get("&ReduceOne_Btn")
    Button.AddListener(this.m_ReduceOne_Btn, EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickAdd(-1) end)
    --數值顯示區數值+1 按鍵
    this.m_AddOne_Btn = this.m_ViewRef.m_Dic_Trans:Get("&AddOne_Btn")
    Button.AddListener(this.m_AddOne_Btn, EventTriggerType.PointerClick, function() NumberPanel_Controller.ClickAdd(1) end)
    ---取消按鍵
    this.m_Cancel_Btn = this.m_ViewRef.m_Dic_Trans:Get("&Cancel_Btn")
    Button.AddListener(this.m_Cancel_Btn, EventTriggerType.PointerClick, NumberPanel_Controller.ClickCancel)
    ---確定按鍵
    this.m_Confirm_Btn = this.m_ViewRef.m_Dic_Trans:Get("&Confirm_Btn")
    Button.AddListener(this.m_Confirm_Btn, EventTriggerType.PointerClick, NumberPanel_Controller.ClickConfirm)

    --指派 顯示Text &CurrentAmountText
    this.m_CurrentAmountText = this.m_ViewRef.m_Dic_TMPText:Get("&CurrentAmountText")
    this.m_NumberPanelObj.gameObject:SetActive(false)
end

function NumberPanel_Controller.Update()
    --面板有被打開 才判斷有沒有按對應數字鍵
    if this.m_NumberPanelObj ~= nil and this.m_NumberPanelObj.gameObject ~= nil and this.m_NumberPanelObj.gameObject.activeSelf == true then
        NumberPanel_Controller.CheckKeyNumberByKeyBoard() 
    end
end

function NumberPanel_Controller.ClickNumber(iNum)
    this.m_CurrentValue = this.m_CurrentValue*10+iNum
    NumberPanel_Controller.RenewPanelValue()
end

function NumberPanel_Controller.ClickAC()
    this.m_CurrentValue = this.m_InitialValue
    NumberPanel_Controller.RenewPanelValue()
end

-- 加或減都放在這邊
function NumberPanel_Controller.ClickAdd(iNum)
    this.m_CurrentValue = this.m_CurrentValue+iNum
    NumberPanel_Controller.RenewPanelValue()
end

function NumberPanel_Controller.ClickMax()
    this.m_CurrentValue = this.m_MaxValue
    NumberPanel_Controller.RenewPanelValue()
end

--退一位
function NumberPanel_Controller.ClickReduceTen()
    this.m_CurrentValue = math.floor(this.m_CurrentValue/10)
    NumberPanel_Controller.RenewPanelValue()
end

function NumberPanel_Controller.ClickCancel()

    if this.m_Canceldelegate ~=nil then
        this.m_Canceldelegate()
    end

    NumberPanel_Controller.CloseNumberPanel()
end

function NumberPanel_Controller.ClickConfirm()

    if this.m_Confirmdelegate ~=nil then
        this.m_Confirmdelegate(this.m_CurrentValue)
    end

    NumberPanel_Controller.CloseNumberPanel()
end

---設定初始數值 可允許的最大值
---@param iPreAssignValue number 鍵盤一顯示時 希望當前數值區顯示的數字
---@param iMax number 此次鍵盤操作允許的最大值
function NumberPanel_Controller.SetInitialData(iPreAssignValue,iMax)
    this.m_InitialValue = iPreAssignValue
    this.m_MaxValue = iMax
    this.m_MinValue = 0

    this.m_CurrentValue = this.m_InitialValue

    NumberPanel_Controller.RenewPanelValue()
end

function NumberPanel_Controller.RenewPanelValue()
    NumberPanel_Controller.ValueLimit()
    this.m_CurrentAmountText.text = this.m_CurrentValue
end

---@param iPreAssignValue int 數字鍵盤預設要顯示的數字
---@param iMaxValue int 數字鍵盤能輸入最大值
---@param iConfirmDelegate int 數字鍵盤按下確定後的委任方法
function NumberPanel_Controller.OpenNumberPanel(iPreAssignValue,iMaxValue,iConfirmDelegate,iCancelDelegate)
    NumberPanel_Controller.SetInitialData(iPreAssignValue,iMaxValue)
    
    this.m_Confirmdelegate = iConfirmDelegate
    this.m_Canceldelegate = iCancelDelegate

    ---更新層級到Peak最上層
    UIMgr.UpdateUIOrderByUIController(NumberPanel_Controller)

    this.m_NumberPanelObj.gameObject:SetActive(true)

end


function NumberPanel_Controller.CloseNumberPanel()
    this.m_MinValue = 0
    this.m_CurrentValue = 0
    this.m_InitialValue = 0 
    this.m_Confirmdelegate =nil
    this.m_Canceldelegate =nil
    this.m_NumberPanelObj.gameObject:SetActive(false)
end

function NumberPanel_Controller.ValueLimit()
    --卡住上下限
    if this.m_CurrentValue > this.m_MaxValue then
        this.m_CurrentValue = this.m_MaxValue 
    elseif this.m_CurrentValue < this.m_MinValue then
        this.m_CurrentValue = this.m_MinValue 
    end
end

---利用電腦的鍵盤輸入數字
function NumberPanel_Controller.CheckKeyNumberByKeyBoard()
    if ChatMgr.m_EnterMode then
        return
    end
    if Input.GetKeyDown(KeyCode.Alpha1) or Input.GetKeyDown(KeyCode.Keypad1) then
        NumberPanel_Controller.ClickNumber(1)
    elseif Input.GetKeyDown(KeyCode.Alpha2) or Input.GetKeyDown(KeyCode.Keypad2) then
        NumberPanel_Controller.ClickNumber(2)
    elseif Input.GetKeyDown(KeyCode.Alpha3) or Input.GetKeyDown(KeyCode.Keypad3) then
        NumberPanel_Controller.ClickNumber(3)
    elseif Input.GetKeyDown(KeyCode.Alpha4) or Input.GetKeyDown(KeyCode.Keypad4) then
        NumberPanel_Controller.ClickNumber(4)
    elseif Input.GetKeyDown(KeyCode.Alpha5) or Input.GetKeyDown(KeyCode.Keypad5)then
        NumberPanel_Controller.ClickNumber(5)
    elseif Input.GetKeyDown(KeyCode.Alpha6) or Input.GetKeyDown(KeyCode.Keypad6)then
        NumberPanel_Controller.ClickNumber(6)
    elseif Input.GetKeyDown(KeyCode.Alpha7) or Input.GetKeyDown(KeyCode.Keypad7) then
        NumberPanel_Controller.ClickNumber(7)
    elseif Input.GetKeyDown(KeyCode.Alpha8) or Input.GetKeyDown(KeyCode.Keypad8) then
        NumberPanel_Controller.ClickNumber(8)
    elseif Input.GetKeyDown(KeyCode.Alpha9) or Input.GetKeyDown(KeyCode.Keypad9) then
        NumberPanel_Controller.ClickNumber(9)
    elseif Input.GetKeyDown(KeyCode.Alpha0) or Input.GetKeyDown(KeyCode.Keypad0)then
        NumberPanel_Controller.ClickNumber(0)
    end
end


---將所有外觀跟數字類岸間相同的按鍵一起蒐集 方便一起添加按鍵演出效果
---@param iBtn Transform 各個按鍵的transform
function NumberPanel_Controller.CollectAllNumberTypeButtons(iBtn)
    local _Index = table.Count(this.m_PanelButtons)+1
    this.m_PanelButtons[_Index] = {}
    this.m_PanelButtons[_Index].m_Btn = iBtn
    this.m_PanelButtons[_Index].m_FeedBackSet = iBtn:Find("FeedBackSet").gameObject
end



