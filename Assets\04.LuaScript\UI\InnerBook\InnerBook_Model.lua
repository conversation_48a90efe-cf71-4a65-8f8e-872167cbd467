﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功系統 Model
---@class InnerBook_Model
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.25
InnerBook_Model = {}
local this = InnerBook_Model

---內功字串
this.m_InnerString = 20112024

---內功系統頁籤
EInnerBookTab = {
    ---心法運行
    MethodRun = 1,
    ---靈脈貫通
    SpiritVeins = 2,
    ---六脈靈功
    SixSpirit = 3,
    ---靈氣解放
    SpiritLiberation = 4,
}

---各頁面字串
this.m_EPageName = {
    [EInnerBookTab.MethodRun] = 20112025,
    [EInnerBookTab.SpiritVeins] = 20112026,
    [EInnerBookTab.SixSpirit] = 20112027,
    [EInnerBookTab.SpiritLiberation] = 20112028,
}

---各狀態屬性對應圖示
this.m_AttMark = {
    [ESelectionState.Normal] = {
        --奇、正、全
        [1] = { "MainIcon_192", "MainIcon_193", "MainIcon_194" },
        --陰、陽、融
        [2] = { "MainIcon_195", "MainIcon_196", "MainIcon_197" },
    },
    [ESelectionState.Highlighted] = {
        --奇、正、全
        [1] = { "MainIcon_198", "MainIcon_199", "MainIcon_200" },
        --陰、陽、融
        [2] = { "MainIcon_201", "MainIcon_202", "MainIcon_203" },
    },
    [ESelectionState.Selected] = {
        --奇、正、全
        [1] = { "MainIcon_204", "MainIcon_205", "MainIcon_206" },
        --陰、陽、融
        [2] = { "MainIcon_207", "MainIcon_208", "MainIcon_209" },
    },
}

---靈脈對應屬性
this.m_MeridianMark = {
    [1] = {1, 1, 2, 2, 2, 2},
    [2] = {1, 2, 1, 2, 1, 2},
}

---心法資料
this.m_MethodData = {}

---正在運行的心法
this.m_RunningMethod = nil

---心法最高階級
this.m_MethodMaxStep = 7

---靈氣累積最大值
this.m_MaxSpiritValue = 50000

---靈氣累積值
this.m_SpiritValue = 1685

---靈脈資料
this.m_MeridianData = {}

---靈脈數量
this.m_MeridianCount = 6

---穴位最大數量
this.m_AcuMaxCount = 9

---當前選擇靈脈Idx
this.m_MeridianIdx = 1

---當前選擇靈脈資料
this.m_CurrentMeridian = {}

---當前裝備靈功
this.m_CurrentWugongData = {0, 0, 0, 0, 0, 0}

---強調色Style
this.m_EmphasizeStyle = "G"

---普通色Style
this.m_NormalStyle = "PO"

---當前解放的靈氣
this.m_CurrentLiberationSpirit = 0

---顯示進階或獲得心法、武功視窗
local function ShowCommonReward(iData)
    local _RewardData = {}
    _RewardData.m_RewardTitle = iData.m_RewardTitle
    _RewardData.m_RewardSubDatas = {}
    _RewardData.m_RewardSubDatas_Text = {}
    local _Data = {}
    _Data.m_IconType = EIconType.InnerSkill
    _Data.m_WugongID =  iData.m_WugongID
    _Data.m_IconTextureName = iData.m_IconTextureName
    _Data.m_Step = iData.m_Step
    _RewardData.m_RewardSubDatas[1] = {}
    table.insert(_RewardData.m_RewardSubDatas[1],_Data)
    
    local _subData = {}
    local _RewardSubData = CommonRewardMgr.GetRewardSubDataType(CommonRewardSubDataType.TextType)
    _RewardSubData.m_Title = GString.StringWithStyle(iData.m_Title, this.m_EmphasizeStyle)
    _RewardSubData.m_Content = " "
    table.insert(_subData, _RewardSubData)
    table.insert(_RewardData.m_RewardSubDatas_Text,_subData)
    CommonRewardMgr.AddNewReward(CommonRewardType.ItemAndText, _RewardData )
end

--靈脈進階開視窗
local function ShowMeridianAdvance(iStep)
    local _EffectReward = CommonRewardMgr.GetRewardType(CommonRewardType.TimeShipUpgrade)
    local _Title = TextData.Get(20101006)
    _EffectReward.m_RewardTitle = _Title

    local _subData = {}
    _subData.m_Title = TextData.Get(20101009)
    local _RewardSubData = CommonRewardMgr.GetRewardSubDataType(CommonRewardSubDataType.TextType)
    _RewardSubData.m_Title = TextData.Get(20112030 + this.m_MeridianIdx)
    _RewardSubData.m_Content = TextData.Get(21002050 + iStep)
    table.insert(_subData, _RewardSubData)
    table.insert(_EffectReward.m_RewardSubDatas, _subData)
    CommonRewardMgr.AddNewReward(CommonRewardType.TimeShipUpgrade, _EffectReward)
end

---內功心法資料
function InnerBook_Model.SetMethodData(iData)
    for k, v in pairs(iData) do
        this.m_MethodData[k] = {}
        this.m_MethodData[k].m_Step = v.m_Step
        this.m_MethodData[k].m_Lv = v.m_Lv
    end

    InnerBook_Model.SetInitMethodData()
end

---設定初始心法資訊
function InnerBook_Model.SetInitMethodData()
    local _MethodCount = InnerBookData.GetDataCount()
    for i = 1, _MethodCount do
        if this.m_MethodData[i] == nil then
            this.m_MethodData[i] = {m_Step = 0, m_Lv = 1}
        end
    end
end

---更新心法資料
function InnerBook_Model.UpdateMethodData(iData)
    local _PerMethodData = {m_Step = this.m_MethodData[iData.m_ID].m_Step, m_Lv = this.m_MethodData[iData.m_ID].m_Lv}
    this.m_MethodData[iData.m_ID].m_Step = iData.m_Step
    this.m_MethodData[iData.m_ID].m_Lv = iData.m_Lv
    local _NowMethodData = this.m_MethodData[iData.m_ID]

    --如果是心法領悟或進階要開共用獎勵視窗
    if _PerMethodData.m_Step == 0 or _NowMethodData.m_Step > _PerMethodData.m_Step then
        local _Data = InnerBookData.GetInnerBookDataByIdx(iData.m_ID)
        local _Step = _NowMethodData.m_Step
        
        local _CommonReward = {}
        _CommonReward.m_RewardTitle = 21002015
        _CommonReward.m_WugongID = _Data[_Step].m_WugongID
        _CommonReward.m_Title = GString.Format(TextData.Get(10101010), TextData.Get(_Data[_Step].m_MethodString), TextData.Get(_Data[_Step].m_ChapterString))
        _CommonReward.m_IconTextureName = WugongData.GetWugongDataByIdx(_Data[_Step].m_WugongID):GetIconTextureName()
        _CommonReward.m_Step = _Step
        ShowCommonReward(_CommonReward)

        if this.m_RunningMethod ~= nil and this.m_RunningMethod.m_MethodID == iData.m_ID then
            InnerBook_Model.SetCurrentMethod(iData.m_ID)
        end
    end
    
    GStateObserverManager.Notify(EStateObserver.UpdateMethodData, iData.m_ID)
end

---目前運行心法
function InnerBook_Model.SetCurrentMethod(iID)
    if iID ~= 0 then
        local _data = InnerBookData.GetInnerBookDataByIdx(iID)
        local _Step = InnerBook_Model.m_MethodData[iID].m_Step
        this.m_RunningMethod = _Step == 0 and _data[_Step + 1] or _data[_Step]
    else
        this.m_RunningMethod = nil
    end

    if InnerBook_Controller.m_PageGameObject[EInnerBookTab.MethodRun] ~= nil and
       InnerBook_Controller.m_PageGameObject[EInnerBookTab.MethodRun].activeSelf then
        GStateObserverManager.Notify(EStateObserver.UpdateCurMethod)
    end
end

---目前靈氣值
function InnerBook_Model.SetSpiritValue(iValue)
    this.m_SpiritValue = iValue

    if table.Count(InnerBook_Controller.m_PageGameObject) > 0 then
        if InnerBook_Controller.m_PageGameObject[EInnerBookTab.MethodRun].activeSelf then
            GStateObserverManager.Notify(EStateObserver.UpdateCurMethod)
        elseif InnerBook_Controller.m_PageGameObject[EInnerBookTab.SpiritVeins].activeSelf then
            GStateObserverManager.Notify(EStateObserver.UpdateSpiritVeins, this.m_MeridianIdx)
        elseif InnerBook_Controller.m_PageGameObject[EInnerBookTab.SpiritLiberation].activeSelf then
            GStateObserverManager.Notify(EStateObserver.UpdateSpiritLiberation)
        end
    end
end

this.m_SpiritUpdateType = {
    ---靈脈進階
    Spirit = 1,
    ---靈功推演
    Wugong = 2,
}

--靈脈資料(單筆)
function InnerBook_Model.SetPerSpiritData(iData, iType)
    local _PerMeridianData = {m_Step = this.m_MeridianData[iData.m_MeridianID].m_Step, m_Point = this.m_MeridianData[iData.m_MeridianID].m_Point}
    this.m_MeridianData[iData.m_MeridianID].m_Step = iData.m_Step
    this.m_MeridianData[iData.m_MeridianID].m_Point = iData.m_Point
    this.m_MeridianData[iData.m_MeridianID].m_WugongData = iData.m_WugongData
    local _NowMeridianData = this.m_MeridianData[iData.m_MeridianID]
    
    if iType == this.m_SpiritUpdateType.Spirit then    
        --靈脈進階
        if _NowMeridianData.m_Step > _PerMeridianData.m_Step then
            ShowMeridianAdvance(_NowMeridianData.m_Step)
        end
        GStateObserverManager.Notify(EStateObserver.UpdateSpiritVeins, this.m_MeridianIdx)

    elseif iType == this.m_SpiritUpdateType.Wugong then
        if _NowMeridianData.m_Step > 3 and _NowMeridianData.m_WugongData[iData.m_WugongStep].m_Lv == 1 then
            --靈功推演
            local _CommonReward = {}
            _CommonReward.m_RewardTitle = 21002112
            _CommonReward.m_WugongID = iData.m_WugongID
            _CommonReward.m_Title = WugongData.GetWugongDataByIdx(iData.m_WugongID).m_Name
            _CommonReward.m_IconTextureName = nil
            _CommonReward.m_Step = nil
            ShowCommonReward(_CommonReward)
            GStateObserverManager.Notify(EStateObserver.UpdateSixSpirit, this.m_MeridianIdx, iData.m_WugongStep)
        end
    end
end

---靈脈資料(全部)
function InnerBook_Model.SetAllSpiritData(iData)
    this.m_MeridianData = iData
end

---目前裝備靈功
function InnerBook_Model.SetCurrentWugong(iData)
    this.m_CurrentWugongData = iData
    GStateObserverManager.Notify(EStateObserver.UpdateSixSpirit, this.m_MeridianIdx)
end

---確認是否有裝備該武功
function InnerBook_Model.CheckCurrentWugong(iIdx)
    local _WugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[iIdx]
    return _WugongData ~= nil and this.m_CurrentWugongData[this.m_MeridianIdx] == _WugongData.m_ID
end

---設定靈氣解放資訊
function InnerBook_Model.SetSpiritLiberation(iMethodID)
    this.m_CurrentLiberationSpirit = iMethodID
    if InnerBook_Controller.m_PageGameObject[EInnerBookTab.SpiritLiberation].activeSelf then
        GStateObserverManager.Notify(EStateObserver.UpdateSpiritLiberation)
    end
end