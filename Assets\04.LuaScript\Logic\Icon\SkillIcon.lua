---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---SkillIcon 繼承自 BasicIcon
---@class SkillIcon
---author KK
---version 1.0
---since [HEM 2.0]
---date 2022.9.30
SkillIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function SkillIcon.Init( iParent )
    SkillIcon.LoadResources( "SkillIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

---建立新的 SkillIcon
function SkillIcon:New( iIdx, iParent, iWidth, iOnClick, iOnClickParam, iOnPointerDown, iOnPointerUp, iOnDrag )
    local _Icon
    local _DragFunc = nil
    local _SkillData = SkillData.GetSkillDataByIdx( iIdx )

    _Icon = BasicIcon:New( m_Tmp, EIconType.Skill, 0, iParent, iWidth, 
    function(iSelf, iSender)
        _Icon:OnClick(iSender)
    end, 
    function(iSelf, iSender)
        _Icon:OnPointerDown(iSender)
    end,
    function(iSelf, iSender)
        _Icon:OnPointerUp(iSender)
    end )

    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    _Icon:SetNeedOpenHint(true)
         :SetNeedLongPress(true)
         :SetLongPressCallback(BasicIcon.OpenBasicHint)
         :SetClickTwice(true)
         :AddEvent(_DragFunc, EventTriggerType.Drag)
    
    setmetatable( _Icon, { __index = self } )
    -- 選擇框
    if _Icon.m_Trans_Select == nil then
        _Icon.m_Trans_Select = _Icon.transform:Find( "Image_Select" ):GetComponent( typeof( Image ) )
        _Icon.m_Tween_Select = _Icon.m_Trans_Select:GetComponent( typeof( LeanTweenVisual ) )
        _Icon.m_Tween_Select.enabled = false
        _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
    end

    ---設定遮罩
    if _Icon.m_Image_Mask == nil then
        _Icon.m_Image_Mask = _Icon.transform:Find( "Image_Mask" ):GetComponent( typeof( Image ) )
        _Icon.m_Text_Mask =  _Icon.m_Image_Mask.transform:Find("TMP_MaskText"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end
    _Icon:SetObjectActive(_Icon.m_Image_Mask, false)

    _Icon.m_SkillData = _SkillData
    _SkillData = nil

    _Icon.m_OnClickFunc = iOnClick
    _Icon.m_OnClickParam = iOnClickParam
    _Icon.m_OnPointerDownFunc = iOnPointerDown
    _Icon.m_OnPointerUpFunc = iOnPointerUp
    _Icon.m_OnUpdateFunc = iOnDrag
    ---依照 技能 設定相關資料
    _Icon:RefreshIcon(iIdx)

    return _Icon
end

function SkillIcon:OnClick(iSender)
    if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
        D.Log("CD 中")
        return
    end
    
    if self.m_OnClickFunc then
        self.m_OnClickFunc(self.m_OnClickParam)
    end
end

function SkillIcon:OnPointerDown(iEventData)
    if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
        D.Log("CD 中")
        return
    end

    local _TempPos = iEventData.position

    if self.m_SkillData.m_UseType == EHotKeyUseKind.Power then
        self.m_OnPointerDownFunc(_TempPos)
    elseif self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
        if self.m_OnPointerDownFunc(_TempPos) then
            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.OnPointerDown( self.gameObject.transform.position, _TempPos)
            end
        end
    end
end

function SkillIcon:OnPointerUp(iEventData)
    if self.m_SkillData.m_UseType == EHotKeyUseKind.Power then
        self.m_OnPointerUpFunc()
    elseif self.m_SkillData and self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
        if CDMgr.IsCD( EIconType.Skill, self.m_Idx ) then
            D.Log("CD 中")
            return
        end
        
        self.m_OnPointerUpFunc(iEventData.position)
    end
end

function SkillIcon:OnDrag(iEventData)
    if self.m_SkillData.m_UseType == EHotKeyUseKind.Direction then
        -- 戰鬥按紐搖桿
        local _Pos = Vector2.New(iEventData.position.x, iEventData.position.y)
        
        if(_Pos ~= nil) then
            if not (HotKey_Controller and HotKey_Controller.m_Trans_HoverObj) then
                do return end
            end
            local _IsSuccess
            _IsSuccess, _Pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
                HotKey_Controller.m_Trans_HoverObj, _Pos, UIMgr.m_UICamera, _Pos )
            end
            
            if self.m_OnUpdateFunc(_Pos) then
                if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                    HotKey_Controller.OnUpdateHover(_Pos)
                end
            end
    end
end

---重設技能 Icon
---@param iIdx number 技能 Idx
function SkillIcon:RefreshIcon(iIdx)
    self.m_SkillData = iIdx ~= 0 and SkillData.GetSkillDataByIdx( iIdx ) or nil
    ---依照 物品 設定相關資料
    if self.m_SkillData then
        self:RefreshBasicIcon(iIdx, self.m_SkillData:GetIconTextureName())
        self:SetName(self.m_SkillData:SkillName())
            :SetOnSelectText()
            :SetFrameType(self.m_SkillData.m_SkillType)
    else
        self:RefreshBasicIcon(iIdx)
        self:SetName()
            :SetOnSelectText()
            :SetFrameType(SkillData.ESkillType.InternalSkill)
    end
end

--- 覆寫獲取外框物件
function SkillIcon:GetFrameObj(iRank)
    local _FrameObjTable = IconMgr.m_FrameIcon.m_SkillFrameIcon[iRank]
    if not _FrameObjTable then
        return nil
    end

    return _FrameObjTable.m_GameObject
end

--- 覆寫設定外框圖
function SkillIcon:SetFrameImage(iRank)
    -- 只有內功會用到外框變色
    if iRank ~= SkillData.ESkillType.InternalSkill then
        return
    end
    
    -- 只有內功會用到外框變色
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            -- 存下所有可變色區塊
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end

    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].sprite = IconMgr.m_FrameIcon.m_SkillFrameIcon[iRank][key1].sprite
    end

    return self
end

---刷新CD遮罩
---覆寫BasicIcon
function SkillIcon:UpdateCD()
    if self.m_Idx == 0 then
        do return end
    end

    -- CD
    local _Time, _Percent = CDMgr.GetInfo( self.m_Type, self.m_Idx )

    -- CD 遮罩顏色
    local _Color = Color.Black
    
    local _SkillData = SkillData.GetSkillDataByIdx(self.m_Idx)
    if _SkillData then
            self.m_TMP_CD.text = string.format( "%.1f", _Time )
            self:SetObjectActive(self.m_TMP_CD, _Time > 0)
    end

    _Color.a = IconSetting.m_CDMaskAlpha
    
    self.m_Image_CD.fillAmount = _Percent
    self:SetObjectActive(self.m_Image_CD, _Percent > 0)
    self.m_Image_CD.color = _Color
end

---設定顯示遮罩
---@param iIsShow boolean 設定遮罩 true = 顯示遮罩；false = 強制關閉遮罩；nil 預設
---@param iTextID int 字串編號 遮罩打開再給編號即可
function SkillIcon:SetMask( iIsShow, iTextID)
    if iIsShow == false then
        self:SetObjectActive(self.m_Image_Mask, false)
        return self
    end
    
    self:SetObjectActive(self.m_Image_Mask, iIsShow)
    self.m_Text_Mask.text = iIsShow and TextData.Get(iTextID) or ""
    
    return self
end

--- 設定選擇
function SkillIcon:ShowSelect( iIsSelect )
    self:SetMask( iIsSelect, self.m_OnSelectTextId)
    self:SetObjectActive(self.m_Trans_Select, iIsSelect)
end

function SkillIcon:ShowSettingSelect( iIsSettingSelect )
    if iIsSettingSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSettingImage
    end
    self.m_ClickOnce = nil
    self:SetMask( nil, self.m_OnSelectTextId)
    self:SetObjectActive(self.m_Trans_Select, iIsSettingSelect)
    self.m_Tween_Select.enabled = iIsSettingSelect
    if iIsSettingSelect == true then
        self.m_Tween_Select:buildAllTweensAgain()
    end
end

--- 取消選擇
function SkillIcon:CancelSelect(iDoSelectAction)
    self:SetMask()
    UIMgr.OpenIconName(false)
    self:ShowSelect( false )
    self.m_ClickOnce = nil

    -- 給 icon 底層因為判斷不選取，不在次觸發 action 用
    if iDoSelectAction ~= false then
        self:DoSelectAction(false)
    end
end

--region 開關 Hint
function SkillIcon:OpenHint()
    local _HintInfo = {
        {
            SkillHint_Controller.Left,
            self.m_SkillData
        },
    }
    HintMgr_Controller.OpenHint(EHintType.SkillHint, _HintInfo)
end

function SkillIcon:CloseHint()
    
end
--endregion 開關 Hint
