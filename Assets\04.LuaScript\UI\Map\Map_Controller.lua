---地圖控制器
---@class Map_Controller
---<AUTHOR>
---@date 2022.10.17

local Map_Ctrl = {}

function Map_Ctrl:Inst()
    if self.Instance == nil then
        self.Instance = self.Instance or {}
        setmetatable(self.Instance, self)
        self.index = self
    end
    return self.Instance
end
Map_Controller = Map_Ctrl:Inst()
local this = Map_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Map_View", "Map_Controller", EUIOrderLayers.FullPage, true, "bg_007")

this.m_WorldData = {}
this.m_WorldGroupNum = 0
this.m_NowSceneID = 0
this.m_ClickSceneID = 0

--世界標籤
local m_Table_WorldTabs = {}
--大地圖圖示
local m_Table_WorldMapUnit = {}
--全部區域
local m_Table_AreaUnit = {}
--我的最愛
local m_Table_LoveAreaUnit = {}
--空間定位
local m_Table_LocationUnit = {}
--當前副本
local m_Table_DungeonUnit = {}
--區域NPC-右邊按鈕
local m_Table_NPCUnit = {}
--區域礦脈-右邊按鈕
local m_Table_MineUnit = {}
--區域NPC-中間圖示
local m_Table_NPCIconUnit = {}
--區域Icon-任務目標
local m_Table_TargetUnit = {}
--區域礦脈-中間圖示
local m_Table_MineIconUnit = {}

--- 摺疊區域或NPC true:打開 false:摺疊
this.m_IsFoldArea = {true, true, true, true}
this.m_IsFoldNPC = {true, true, true, true, true, true}
this.m_IsShowNPC = {true, true, true, true, true, true}

--- 是否解鎖礦脈
this.m_isUnlockMine = true

local _isRefresh = false
local _isNotInteractive = false
local _needSaveLove = false
local _DelayMaterialTime = 0.1
local _isMark = false
local _MarkExistTime = 15

this.m_WorldSelectIdx = 0
this.m_AreaSelectIdx = 0
this.m_NpcSelectIdx = 0
this.m_LoveSelectIdx = 0
this.m_MineSelectIdx = 0

local CommonQueryIDX = 57

---區域類型
EAreaType = {
    ---全部區域
    All = 1,
    ---我的最愛
    Love = 2,
    ---空間定位
    Location = 3,
    ---當前副本
    Dungeon = 4,
    ---NPC
    Npc = 5,
    ---世界
    World = 6,
    ---礦脈
    Mine = 7
}

---左邊區域字串
local EAreaString = {
    [EAreaType.All] = "AllArea",
    [EAreaType.Love] = "Love",
    [EAreaType.Location] = "Location",
    [EAreaType.Dungeon] = "Dungeon"
}

---NPC字串
local ENPCString = {
    [1] = "Mineral",
    [2] = "Business",
    [3] = "Crusade",
    [4] = "Other",
    [5] = "Teleport",
    [6] = "Tutor"
}

local TransString = "&Trans_"
local ButtonTitleString = "&ButtonTitle_"
local GroupString = "&Group_"
local ContentString = "&Content_"
local ImageFrame = "&Image_Frame_"
local ButtonEyeString = "&ButtonEye_"
local ImageArrowString = "&Image_Arrow_"

---被複製出來的Mask材質
local m_ClonedMaterial = nil
local m_IsEnableGlitch = false

local m_IsRatio219 = true
local MapSize = 0
local MapRatio = 0

local CanInteractive = false
local Group_Right_Width = 0
local Group_Left_Width = 0

--移動或傳送用的座標
this.m_InteractivePos = {}

---雜訊切場效果資料
local m_UIGlitchEffectData = 
{
    ---開啟時用的雜訊效果值
    Open = 
    {
        [GlitchCtrl.GlitchType.CRT] =
        {
            m_Frequency = 0.05,
            m_Threshold = 0.5,
            m_HoldTime = 0.1
        },
    },
    ---一般時用的雜訊效果值
    Common = 
    {
        [GlitchCtrl.GlitchType.CRT] =
        {
            m_Frequency = 5,
            m_Threshold = 5,
            m_HoldTime = 0.1
        },
        [GlitchCtrl.GlitchType.Block] =
        {
            m_Frequency = 5,
            m_Threshold = 0.5,
            m_HoldTime = 0.4
        },
        [GlitchCtrl.GlitchType.Jitter] =
        {
            m_Frequency = 5,
            m_Threshold = 0.5,
            m_HoldTime = 0.05
        },
    }
}

local m_isClickLeft = false
local m_NowClickLeftPosY = 0
local m_isClickRight = false
local m_NowClickRightPosY = 0

---區域標籤-顯示NPC按鈕的箭頭 旋轉角度
local _ArrowPointDown = Vector3(0,0,90)
local _ArrowPointRight = Vector3(0,0,180)

local m_FoldHeight = 0
local m_NpcHeight = 0

---初始化
function Map_Controller.Init()
    ---主視窗元件
    this.m_Main_Panel = this.m_ViewRef.m_Dic_Trans:Get("&Main_Panel")
    this.m_RectTrans_Main = this.m_Main_Panel:GetComponent("RectTransform")
    ---左邊功能區塊(動畫用)
    this.m_Group_Left = this.m_ViewRef.m_Dic_Trans:Get("&Group_Left")
    this.m_Animation_Left = this.m_Group_Left.gameObject:GetComponent("UIAnimation")
    this.m_RectTrans_Left = this.m_Group_Left:GetComponent("RectTransform")
    ---右邊功能區塊(動畫用)
    this.m_Group_Right = this.m_ViewRef.m_Dic_Trans:Get("&Group_Right")
    this.m_Animation_Right = this.m_Group_Right.gameObject:GetComponent("UIAnimation")
    this.m_RectTrans_Right = this.m_Group_Right:GetComponent("RectTransform")

    -- 主要 Panel
	this.m_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&Main_Panel")

	--- 資源列表
	local _CurrentResourceTable =
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }

    this.OnHintButtonClick = function() CommonQueryMgr.AddNewInform(CommonQueryIDX) end
	--要產生的位置(父物件) 新版上方條
	this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_MainPanel, 0, TextData.Get(375), _CurrentResourceTable)

    --region 世界頁籤
    this.m_ScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Tabs")
    this.m_ReuseItem = this.m_ViewRef.m_Dic_Trans:Get("&Button_WorldMap").gameObject
    this.m_ScrollView_Tabs = ScrollView.Init(this.m_ScrollView, true, this.m_ReuseItem,
        Map_Controller.GetTabsCount, Map_Controller.AfterReuseItemInit,
        Map_Controller.AfterReuseItemIndexUpdate, true)
    --endregion

    --region世界地圖相關
    this.m_Image_WorldMap_Mask = this.m_ViewRef.m_Dic_Trans:Get("&Image_Mask")
    this.m_Trans_WorldMap = this.m_ViewRef.m_Dic_Trans:Get("&Image_WorldMap")
    this.m_Image_WorldMap_LeftLine = this.m_ViewRef.m_Dic_Trans:Get("&Image_WorldMap_LeftLine")
    this.m_Image_WorldMap_BottomLine = this.m_ViewRef.m_Dic_Trans:Get("&Image_WorldMap_BottomLine")
    this.m_Image_WorldMap = this.m_Trans_WorldMap:GetComponent("RawImage")
    this.m_Animation_WorldMap = this.m_Trans_WorldMap.gameObject:GetComponent("UIAnimation")
    this.m_Image_Select = this.m_ViewRef.m_Dic_Trans:Get("&Image_Select")
    this.m_Image_Select.gameObject:SetActive(false)
    --endregion
    
    --region 左邊顯示區域
    this.m_Group_ContentArea = this.m_ViewRef.m_Dic_Trans:Get("&Content_Area")

    ---區域標籤(點擊可進行區域內容縮放)
    this.m_ButtonTitle_Area = {}
    ---區域標籤的箭頭
    this.m_ButtonTitle_Area_Arrow = {}
    ---顯示區域
    this.m_Group_Area = {}
    ---區域內容(把要顯示的東西塞在這裡)
    this.m_Content_Area = {}
    ---內容外框底圖
    this.m_ImageFrame_Area = {}
    for k, v in pairs(EAreaString) do
        this.m_ButtonTitle_Area[k] = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get(ButtonTitleString .. v))
        this.m_ButtonTitle_Area_Arrow[k] = this.m_ButtonTitle_Area[k].gameObject.transform:Find("Image_Arrow").gameObject:GetComponent("Image")
        this.m_Group_Area[k] = this.m_ViewRef.m_Dic_Trans:Get(GroupString .. v)
        this.m_Content_Area[k] = this.m_ViewRef.m_Dic_Trans:Get(ContentString .. v)
        this.m_ImageFrame_Area[k] = this.m_ViewRef.m_Dic_Trans:Get(ImageFrame .. v)
    end
    for i = 1, table.Count(this.m_ButtonTitle_Area) do
        this.m_ButtonTitle_Area[i]:AddListener(EventTriggerType.PointerClick, function()
            local _activeObj = Map_Controller.CheckAreaContent(this.m_Content_Area[i])
            if _activeObj then
                this.m_IsFoldArea[i] = not this.m_IsFoldArea[i]
                this.m_Group_Area[i].gameObject:SetActive(this.m_IsFoldArea[i])
                this.m_ButtonTitle_Area_Arrow[i].rectTransform.localEulerAngles = this.m_IsFoldArea[i] and _ArrowPointDown or _ArrowPointRight
            end
            _isNotInteractive = true
        end)
    end
    --endregion

    --region 右邊顯示區域
    ---區域標籤1.商人 2.討伐 3.其他 4.出入口 5.導師
    this.m_FoldNPCTrans = {}
    ---區域標籤-顯示區域按鈕(點擊可進行區域內容顯示)1.商人 2.討伐 3.其他 4.出入口 5.導師
    this.m_FoldNPCButton = {}
    ---區域標籤-顯示NPC按鈕(點擊可進行地圖上NPC開關顯示)1.商人 2.討伐 3.其他 4.出入口 5.導師
    this.m_FoldNPCButton_Eye = {}
    ---區域標籤-顯示NPC按鈕的箭頭 用來提示所屬的NPC細節是否被展開
    this.m_FoldNPC_Arrow = {}
    ---顯示區域
    this.m_Group_NPC = {}
    ---區域內容(把要顯示的東西塞在這裡)
    this.m_Content_NPC = {}
    for k, v in pairs(ENPCString) do
        this.m_FoldNPCTrans[k] = this.m_ViewRef.m_Dic_ButtonEx:Get(TransString .. v)
        this.m_FoldNPCButton[k] = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get(ButtonTitleString .. v))
        this.m_FoldNPCButton_Eye[k] = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get(ButtonEyeString .. v))
        this.m_FoldNPC_Arrow[k] = this.m_ViewRef.m_Dic_Image:Get(ImageArrowString .. v) 
        this.m_Group_NPC[k] = this.m_ViewRef.m_Dic_Trans:Get(GroupString .. v)
        this.m_Content_NPC[k] = this.m_ViewRef.m_Dic_Trans:Get(ContentString .. v)
    end
    m_FoldHeight = this.m_FoldNPCTrans[1].transform.rect.height
    for i = 1, table.Count(ENPCString) do
        --區域標籤
        this.m_FoldNPCButton[i]:AddListener( EventTriggerType.PointerClick, function()
            local _activeObj = Map_Controller.CheckNpcContent(i)
            if _activeObj then
                this.m_IsFoldNPC[i] = not this.m_IsFoldNPC[i]
                this.m_Group_NPC[i].gameObject:SetActive(this.m_IsFoldNPC[i])
            end
            local _State = this.m_IsShowNPC[i] and 1 or 0
            this.m_FoldNPCButton_Eye[i]:ChangeStateTransitionGroup(_State)
            _isNotInteractive = true

            ---設定箭頭的角度指向
            ---
            if this.m_IsFoldNPC[i] then
                this.m_FoldNPC_Arrow[i].rectTransform.localEulerAngles = _ArrowPointDown
            else
                this.m_FoldNPC_Arrow[i].rectTransform.localEulerAngles = _ArrowPointRight
            end
            

        end)
        --眼睛按鈕
        local _State = this.m_IsShowNPC[i] and 1 or 0
        this.m_FoldNPCButton_Eye[i]:ChangeStateTransitionGroup(_State)
        this.m_FoldNPCButton_Eye[i]:AddListener( EventTriggerType.PointerClick, function()
            this.m_IsShowNPC[i] = not this.m_IsShowNPC[i]
            local _State = this.m_IsShowNPC[i] and 1 or 0
            this.m_FoldNPCButton_Eye[i]:ChangeStateTransitionGroup(_State)
            if i ~= 1 then
                for k, v in pairs(m_Table_NPCIconUnit) do
                    if v.SceneID == this.m_NowSceneID and v.Group == i then
                        v.gameObject:SetActive(this.m_IsShowNPC[i])
                    end
                end
            else
                for k, v in pairs(m_Table_MineIconUnit) do
                    if v.SceneID == this.m_NowSceneID then
                        v.gameObject:SetActive(this.m_IsShowNPC[i])
                    end
                end
            end
            _isNotInteractive = true
        end)
    end

    this.m_FoldNPCButtonIcon = {}
    for i = 1, table.Count(this.m_FoldNPCButton) do
        this.m_FoldNPCButtonIcon[i] = this.m_FoldNPCButton[i].gameObject.transform:Find( "Image_Icon" ):GetComponent(typeof(Image))
        local _IconString = MapNapTabsData.GetMapDataByIdx(i).m_GroupIcon
        SpriteMgr.Load(_IconString, this.m_FoldNPCButtonIcon[i])
    end

    this.m_TextTitle = this.m_ViewRef.m_Dic_TMPText:Get("&Text_AreaName")

    this.m_Group_Content_Npc = this.m_ViewRef.m_Dic_Trans:Get("&Content_NPC")
    --endregion

    --region 需要重複產生的物件
    --大地圖區域Icon
    this.m_WorldMapUnit = this.m_ViewRef.m_Dic_Trans:Get("&Image_WorldMapIcon").gameObject
    this.m_GObjPool_WorldMapUnit = Extension.CreatePrefabObjPool(this.m_WorldMapUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    --NPC Icon
    this.m_NPCUnit = this.m_ViewRef.m_Dic_Trans:Get("&Image_NPCIcon").gameObject
    this.m_GObjPool_NPCIconUnit = Extension.CreatePrefabObjPool(this.m_NPCUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    --任務目標Icon
    this.m_MissionUnit = this.m_ViewRef.m_Dic_Trans:Get("&Image_TargetIcon").gameObject
    this.m_GObjPool_MissionUnit = Extension.CreatePrefabObjPool(this.m_MissionUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    --礦脈Icon
    this.m_MineUnit = this.m_ViewRef.m_Dic_Trans:Get("&Button_Mine").gameObject
    this.m_GObjPool_MineUnit = Extension.CreatePrefabObjPool(this.m_MineUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    --區域按鈕
    this.m_AreaListUnit = this.m_ViewRef.m_Dic_Trans:Get("&Button_Area").gameObject
    this.m_GObjPool_AreaUnit = Extension.CreatePrefabObjPool(this.m_AreaListUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    --NPC按鈕
    this.m_NPCListUnit = this.m_ViewRef.m_Dic_Trans:Get("&Button_NPC").gameObject
    this.m_GObjPool_NPCUnit = Extension.CreatePrefabObjPool(this.m_NPCListUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    m_NpcHeight = this.m_NPCListUnit.transform.rect.height
    --endregion

    --取得 View ref
    this.m_Trans_Map = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Image_Map"))
    this.m_RectTrans_Map = this.m_Trans_Map.transform:GetComponent("RectTransform")
    this.m_Image_Map = this.m_Trans_Map.transform:GetComponent("RawImage")
    this.m_Animation_Map = this.m_Trans_Map.transform:GetComponent("UIAnimation")
    Map_Controller.SetAreaMapImg(SceneMgr.GetSceneID())
    
    --取地圖Rect
    this.m_RectTrans = this.m_RectTrans_Map.rect
    --Rect最大點位
    this.m_RectTransMax = this.m_RectTrans.max
    
    this.m_Trans_Map:AddListener(EventTriggerType.PointerClick, Map_Controller.Map_OnClick)
    
    Map_Controller.InitInteractiveUI()

    this.m_Image_PlayerIcon = this.m_ViewRef.m_Dic_Trans:Get("&Image_PlayerIcon")
    this.m_Image_PlayerIcon_Rect = this.m_Image_PlayerIcon.gameObject:GetComponent("RectTransform")
    this.m_Image_Click = this.m_ViewRef.m_Dic_Image:Get("&Image_Click")
    this.m_Image_Click_Rect = this.m_Image_Click.gameObject:GetComponent("RectTransform")
    this.m_Image_Click.gameObject:SetActive(false)

    WorldMapData.SortMapData()

    this.m_Image_MapRender = this.m_ViewRef.m_Dic_Trans:Get("&Image_Map")

    local _RawImage_MapRender = this.m_Image_MapRender:GetComponent("RawImage")
    if _RawImage_MapRender and _RawImage_MapRender.material then
        m_ClonedMaterial = Material.Instantiate(_RawImage_MapRender.material)
        _RawImage_MapRender.material = m_ClonedMaterial
    end
end

---開啟介面
function Map_Controller:Open()
    MapMgr.m_IsOpenMap = true
    -- 執行地圖效果前 先把HUD相機關了
    CameraMgr.SetHUDCameraActive(false)
    --更新MapMgr主要使用介面
    MapMgr.SetCurrentUI(Map_Controller.m_UIView)
    CameraMgr.TriggerScanEffect(true, RoleMgr.m_RC_Player.transform.position)

    -- 測試功能記得刪掉
    if MapMgr.m_IsOpenMap then
        --return false
    end

    Map_Controller.SetNoSignal(SceneMgr.GetSceneID())
    if this.m_Group_Resources then
        this.m_Group_Resources.m_Resources:OnUpdate()
    end

    this.m_Group_Interactive.gameObject:SetActive(false)
    this.m_Group_Interactive.gameObject.transform:SetParent(this.m_Image_Map.gameObject.transform.parent)
    Group_Right_Width = this.m_RectTrans_Right.rect.width
    Group_Left_Width = this.m_RectTrans_Left.rect.width
    CanInteractive = false

    Map_Controller.ShowWorldMap(false)
    Map_Controller.DoMapEffect()
    
    ScrollView.Update(this.m_ScrollView_Tabs)
    Map_Controller.SetTabsEnable(true)
    Map_Controller.SetFoldButtonName()
    
    local _worldGroup = Map_Controller.GetNowWorldGroup()
    if Map_Controller.CheckAreaType(SceneMgr.GetSceneID()) or _worldGroup == nil then
        Map_Controller.SetDungeonRowItem()
    else
        this.m_WorldGroupNum = _worldGroup
        this.m_LoveArea = ClientSaveMgr.GetDataValue(EClientSaveDataType.LoveArea, MapMgr.m_LoveAreaString)
        Map_Controller.SetWorldMapImg(this.m_WorldGroupNum)
        Map_Controller.SetAreaRowItem()
        Map_Controller.SetLoveAreaRowItem()
        Map_Controller.SetWorldAreaIcon()
    end
    Map_Controller.SetAreaActive()
    Map_Controller.CheckNowAreaClick()
    Map_Controller.SetAreaFoldState()
    
    Map_Controller.SetNPCItem(SceneMgr.GetSceneID())

    Map_Controller.UpdateAreaArrowDirection()
    Map_Controller.UpdateNPCArrowDirection()
    _isRefresh = true
    _isNotInteractive = true
    return true
end

---關閉介面
function Map_Controller:Close()
    MapMgr.m_IsOpenMap = false
    CameraMgr.SetHUDCameraActive(true)
    MapMgr.SetCurrentUI(MiniMap_Controller.m_UIView)
    CameraMgr.TriggerScanEffect(false, Vector3.zero)

    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayCloseMap, function()
        this.m_Image_Map.material = nil
    end)

    if _needSaveLove then
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.LoveArea, MapMgr.m_LoveAreaString, this.m_LoveArea)
        _needSaveLove = false
    end
    _isNotInteractive = true
    return true
end

function Map_Controller.OnDestroy()
    -- UI被刪除時 初始化所有 loacl table
    m_Table_WorldTabs = {}
    m_Table_WorldMapUnit = {}
    m_Table_AreaUnit = {}
    m_Table_LoveAreaUnit = {}
    m_Table_LocationUnit = {}
    m_Table_DungeonUnit = {}
    m_Table_NPCUnit = {}
    m_Table_MineUnit = {}
    m_Table_NPCIconUnit = {}
    m_Table_TargetUnit = {}
    m_Table_MineIconUnit = {}
    return true
end

local function CloseMark()
    this.m_Image_MarkIcon.gameObject:SetActive(false)
end

function Map_Controller:Update()
    Map_Controller.UpdatePlayerIcon()

    if _isRefresh then
        this.RefreshPanel()
        _isRefresh = false
    end

    if m_isClickLeft then
        if math.abs(this.m_Group_ContentArea.gameObject.transform.localPosition.y - m_NowClickLeftPosY) > 1 then
            _isNotInteractive = true
            m_isClickLeft = false
        end
    end

    if m_isClickRight then
        if math.abs(this.m_Group_Content_Npc.gameObject.transform.localPosition.y - m_NowClickRightPosY) > 1 then
            _isNotInteractive = true
            m_isClickRight = false
        end
    end

    if _isNotInteractive then
        this.m_Group_Interactive.gameObject:SetActive(false)
        this.m_Group_Interactive.gameObject.transform:SetParent(this.m_Image_Map.gameObject.transform.parent)
        Map_Controller.SetClickIcon(false)
        this.m_Group_Hint.gameObject:SetActive(false)
        this.m_Group_Condition.gameObject:SetActive(false)
        _isNotInteractive = false
    end
    
    if _isMark then
        HEMTimeMgr.CancelDoFunctionDelay(CloseMark)
        HEMTimeMgr.DoFunctionDelay(_MarkExistTime, CloseMark)
        _isMark = false
    end

    if this.m_IsPlayingClickIconTween ==false then
        Map_Controller.PlayClickIconTween()
    end
end

function Map_Controller.RefreshPanel()
    for i = 1, table.Count(this.m_Group_Area) do
        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Group_Area[i])
        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Content_Area[i].transform)
    end

    for i = 1, table.Count(this.m_Group_NPC) do
        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Group_NPC[i])
        LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Content_NPC[i].transform)
    end

    for i = 1, table.Count(this.m_Content_Area) do
        local _y = this.m_Content_Area[i].transform.rect.height - this.m_Content_Area[i].transform.anchoredPosition.y
        this.m_Group_Area[i].sizeDelta = Vector2(this.m_Group_Area[i].sizeDelta.x, _y + 20)
    end

    for i = 1, table.Count(this.m_Content_NPC) do
        local _y = this.m_Content_NPC[i].transform.rect.height - this.m_Content_NPC[i].transform.anchoredPosition.y
        this.m_Group_NPC[i].sizeDelta = Vector2(this.m_Group_NPC[i].sizeDelta.x, _y)
    end
end

function Map_Controller.CheckScreenRatio()
    local _ScreenRatio = SettingMgr.GetScreenResolution()
    _ScreenRatio = _ScreenRatio.x / _ScreenRatio.y
    
    local _Ratio219 = 21 / 9
    local _Ratio169 = 16 / 9
    local _Float = 0.01
    if math.abs(_ScreenRatio - _Ratio219) < _Float then
        m_IsRatio219 = true
    elseif math.abs(_ScreenRatio - _Ratio169) < _Float then
        m_IsRatio219 = false
    else
        m_IsRatio219 = false
    end
end

function Map_Controller.ResetGlitchValue(iData, iEnable)
    for k,v in pairs(iData) do
        local _GlitchType = nil
        if k == GlitchCtrl.GlitchType.CRT then
            _GlitchType = MapMgr.m_BigMapGlitchCtrl.m_CRTGlitch
        elseif k == GlitchCtrl.GlitchType.Block then
            _GlitchType = MapMgr.m_BigMapGlitchCtrl.m_BlockGlitch
        elseif k == GlitchCtrl.GlitchType.Jitter then
            _GlitchType = MapMgr.m_BigMapGlitchCtrl.m_JitterGlitch
        elseif k == GlitchCtrl.GlitchType.Digital then
            _GlitchType = MapMgr.m_BigMapGlitchCtrl.m_DigitalGlitch
        end

        if _GlitchType then
            for _ValKey,_setVal in pairs(v) do
                _GlitchType[_ValKey] = iEnable and _setVal or 0
            end
            MapMgr.m_BigMapGlitchCtrl:EnableGlitch(k, iEnable)
        end

    end
    
end

function Map_Controller.EnableGlitch()
    m_ClonedMaterial:SetFloat(Shader.PropertyToID("_Alpha"), EffectSetting.m_MiniMapNoSignalAlpha)
    m_IsEnableGlitch = true
    Map_Controller.ResetGlitchValue(m_UIGlitchEffectData.Common, true)
end

function Map_Controller.DisableGlitch()
    m_ClonedMaterial:SetFloat(Shader.PropertyToID("_Alpha"), 1)
    m_IsEnableGlitch = false
    Map_Controller.ResetGlitchValue(m_UIGlitchEffectData.Common, false)

end

function Map_Controller.OnClick_Back()
    if this.m_NowSceneID == SceneMgr.GetSceneID() then
        UIMgr.CloseToPreviousPage(Map_Controller)
    else
        Map_Controller.SetNPCItem(SceneMgr.GetSceneID())
    end
end

---光暈圖初始長度
local _HaloInitHeight = 333
---光暈圖修正值
local _HaloFixValue = 60
---需要修正的數量
local _NeedFixCount = 5
function Map_Controller.SetImageHalo(iType, iCount)
    if this.m_ImageFrame_Area[iType] ~= nil then
        local _Halo = this.m_ImageFrame_Area[iType].transform:Find("Image_Light")
        local _HaloSize = _Halo.gameObject.transform.sizeDelta
        _HaloSize.x = (iCount < _NeedFixCount) and _HaloInitHeight - ((_NeedFixCount - iCount) * _HaloFixValue) or _HaloInitHeight
        _Halo.gameObject.transform.sizeDelta = _HaloSize
    end
end

--region 動態表現
function Map_Controller.DoMapEffect()
    this.m_Image_Map.color = Color.Blue
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayMapChangeColor, function()
        LeanTween.color(this.m_Image_Map.rectTransform, Color.White, MapMgr.m_MapEffect.m_MapChangeColorTime);
    end)
    --等地圖變色跑完再讓互動介面可以互動
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayMapChangeColor + MapMgr.m_MapEffect.m_MapChangeColorTime, function()
        CanInteractive = true
    end)
    this.m_Animation_Map:DoActive(false)
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayMapDynamic, function()
        this.m_Animation_Map:DoActive(true)
        UIGlitchEffect.Reset(this.m_UIController, true)
        UIGlitchEffect.SetGlitchBegin(this.m_UIController, false, function()
            --UI雜訊結束後一起結束
            local _MapData = SceneAttributeData.Get(SceneMgr.GetSceneID())
            if _MapData and _MapData.m_MapNoise ~= 1 then
                MapMgr.DisableGlitch()
            end
         end)
        local _MapData = SceneAttributeData.Get(SceneMgr.GetSceneID())
        if _MapData and _MapData.m_MapNoise ~= 1 then
            Map_Controller.ResetGlitchValue(m_UIGlitchEffectData.Open, true)
            --MapMgr.EnableGlitch(true)    
        end
 
    end)
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayMapDynamic + _DelayMaterialTime, function()
        this.m_Image_Map.material = MapMgr.m_MapEffect.m_MapMaterial
    end)

    Map_Controller.DoGroupAnimation(false)
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayGroupDynamic, function()
        Map_Controller.DoGroupAnimation(true)
    end)
end

function Map_Controller.DoGroupEffect()
    Map_Controller.DoGroupAnimation(false)
    HEMTimeMgr.DoFunctionDelay(MapMgr.m_MapEffect.m_DelayGroupDynamic, function()
        Map_Controller.DoGroupAnimation(true)
    end)
end

function Map_Controller.DoGroupAnimation(iShow)
    this.m_Animation_Left:DoActive(iShow)
    this.m_Animation_Right:DoActive(iShow)
end
--endregion

local _AreaTitleNameString = {520306, 520301, 520307, 520311}
function Map_Controller.SetFoldButtonName()
    for i = 1, table.Count(this.m_FoldNPCButton) do
        local _NameStr = MapNapTabsData.GetMapDataByIdx(i).m_GroupString
        this.m_FoldNPCButton[i]:SetText(TextData.Get(_NameStr))
    end

    for i = 1, table.Count(this.m_ButtonTitle_Area) do
        local _NameStr = _AreaTitleNameString[i]
        this.m_ButtonTitle_Area[i]:SetText(TextData.Get(_NameStr))
    end
    
end

---判斷是不是副本
function Map_Controller.CheckAreaType(iSceneID)
    return SceneAttributeData.Get( iSceneID ).m_OutBtnEnable > 0 
end

---設定要顯示的區域
function Map_Controller.SetAreaActive()
    for i = 1, table.Count(this.m_ButtonTitle_Area) do
        --如果在副本地區就只顯示副本區塊
        if Map_Controller.CheckAreaType(SceneMgr.GetSceneID()) then
            this.m_ButtonTitle_Area[i].gameObject:SetActive(i == EAreaType.Dungeon)
            this.m_Group_Area[i].gameObject:SetActive(i == EAreaType.Dungeon)
        else
            this.m_ButtonTitle_Area[i].gameObject:SetActive(i ~= EAreaType.Dungeon)
            if i ~= EAreaType.Dungeon then
                local _activeObj = Map_Controller.CheckAreaContent(this.m_Content_Area[i])
                this.m_ButtonTitle_Area[i].gameObject:SetActive(_activeObj)
                this.m_ButtonTitle_Area[i]:SetSelect(_activeObj)
                this.m_Group_Area[i].gameObject:SetActive(_activeObj)
            else
                this.m_Group_Area[i].gameObject:SetActive(false)
            end
        end
    end
    this.m_ButtonTitle_Area[EAreaType.Location].gameObject:SetActive(false)
    this.m_Group_Area[EAreaType.Location].gameObject:SetActive(false)
    Map_Controller.UpdateAreaArrowDirection()
end

---確認區域部分是否有資料
function Map_Controller.CheckAreaContent(iContent)
    local _activeObj = false
    for i = 0, iContent.transform.childCount - 1 do
        if iContent.transform:GetChild(i).gameObject.activeSelf then
            _activeObj = true
            break
        end
    end
    return _activeObj
end

function Map_Controller.SetAreaFoldState(iIdx)
    if not Map_Controller.CheckAreaType(SceneMgr.GetSceneID()) then
        if iIdx == nil then
            for i = 1, table.Count(this.m_IsFoldArea) do
                local _activeObj = Map_Controller.CheckAreaContent(this.m_Content_Area[i])
                this.m_IsFoldArea[i] = _activeObj
                this.m_ButtonTitle_Area[i]:SetSelect(_activeObj)
                this.m_Group_Area[i].gameObject:SetActive(_activeObj)
            end
        else
            local _activeObj = Map_Controller.CheckAreaContent(this.m_Content_Area[iIdx])
            this.m_IsFoldArea[iIdx] = _activeObj
            this.m_ButtonTitle_Area[iIdx]:SetSelect(_activeObj)
            this.m_Group_Area[iIdx].gameObject:SetActive(_activeObj)
        end
    end
    Map_Controller.UpdateAreaArrowDirection()
end

---確認NPC部分是否有資料
function Map_Controller.CheckNpcContent(iIdx)
    if iIdx == 1 then
        local _MineData = EnergyMineData.GetMapMineData(this.m_NowSceneID)
        return table.Count(_MineData) > 0 and this.m_isUnlockMine
    else
        local _HaveNpc = false
        local _MapNPCData = EventMapNpc.GetEventMapNPC(this.m_NowSceneID)
        for k, _NPCData in pairs(_MapNPCData) do
            local _MapNpcClassData = _NPCData.m_NPCMode ~= 0 and MapNpcClassData.GetMapDataByIdx(_NPCData.m_NPCMode) or nil
            if _NPCData.m_NPCNum ~= 0 and _MapNpcClassData ~= nil and _MapNpcClassData.m_NPCGroup ~= 0 then
                if _MapNpcClassData.m_NPCGroup == iIdx then
                    _HaveNpc = true
                    break
                end
            end
        end
        return _HaveNpc
    end
end

function Map_Controller.SetNpcFoldState()
    for i = 1, table.Count(this.m_IsFoldNPC) do
        local _HaveNpc = Map_Controller.CheckNpcContent(i)
        this.m_IsFoldNPC[i] = _HaveNpc
        this.m_FoldNPCTrans[i].gameObject:SetActive(_HaveNpc)
        this.m_FoldNPCButton[i]:SetSelect(_HaveNpc)
        this.m_Group_NPC[i].gameObject:SetActive(_HaveNpc)
    end
    
    Map_Controller.UpdateNPCArrowDirection()
end

function Map_Controller.SetNpcIconActive()
    for i = 1, table.Count(this.m_IsShowNPC) do
        if i ~= 1 then
            for k, v in pairs(m_Table_NPCIconUnit) do
                if v.SceneID == this.m_NowSceneID and v.Group == i then
                    v.gameObject:SetActive(this.m_IsShowNPC[i])
                end
            end
        else
            for k, v in pairs(m_Table_MineIconUnit) do
                if v.SceneID == this.m_NowSceneID then
                    v.gameObject:SetActive(this.m_IsShowNPC[i] and this.m_isUnlockMine)
                end
            end
        end
    end
end

function Map_Controller.SetWorldNowSelect()
    this.m_Image_Select.gameObject:SetActive(false)
    local _WorldData = WorldMapData.GetMapDataByIdx(SceneMgr.GetSceneCID())
    if not _WorldData then
        local _SceneData = SceneAttributeData.Get(SceneMgr.GetSceneID())
        _WorldData = WorldMapData.GetMapDataByIdx(_SceneData.m_RelatedSceneID)
    end
    for k, v in pairs(m_Table_WorldMapUnit) do
        if v.SceneID == _WorldData.m_SceneID then
            this.m_Image_Select.gameObject:SetActive(_WorldData.m_WorldGroup == this.m_WorldGroupNum)
            this.m_Image_Select.gameObject.transform:SetParent(v.gameObject.transform)
            this.m_Image_Select.gameObject.transform.localPosition = Vector3.zero
            break
        end
    end
end

function Map_Controller.UpdatePlayerIcon()
    if RoleMgr.m_RC_Player ~= nil and this.m_NowSceneID == SceneMgr.GetSceneID() and not m_IsEnableGlitch then
        this.m_Image_PlayerIcon.gameObject:SetActive(true)
        --求出角色相對位置
        local _Position = Map_Controller.SetIconPosition(RoleMgr.m_RC_Player.transform.localPosition.x * 100,
                                                         RoleMgr.m_RC_Player.transform.localPosition.z * 100)
        this.m_Image_PlayerIcon.gameObject.transform.localPosition = _Position
        local _EulerAngles = RoleMgr.m_RC_Player:GetlocalEulerAngle()
        this.m_Image_PlayerIcon.gameObject.transform.localRotation = Quaternion.Euler(Vector3(0, 0, -_EulerAngles.y))
        if not this.m_Group_Interactive.gameObject.activeSelf then
            this.m_Image_PlayerIcon_Rect:SetAsLastSibling()
        end
    else
        this.m_Image_PlayerIcon.gameObject:SetActive(false)
    end
end

---刷新左側地圖類標籤是否展開的箭頭指向
function Map_Controller.UpdateAreaArrowDirection()
    for i = 1, table.Count(this.m_ButtonTitle_Area) do
        this.m_ButtonTitle_Area_Arrow[i].rectTransform.localEulerAngles = this.m_IsFoldArea[i] and _ArrowPointDown or _ArrowPointRight
    end
end

---刷新右側NPC類標籤是否展開的箭頭指向
function Map_Controller.UpdateNPCArrowDirection()
    for i = 1, table.Count(ENPCString) do
        this.m_FoldNPC_Arrow[i].rectTransform.localEulerAngles = this.m_IsFoldNPC[i] and _ArrowPointDown or _ArrowPointRight
    end
end

--region 世界地圖Icon

local function WorldMapClick(iWorldMapUnit)
    local _WorldData = iWorldMapUnit.m_WorldData

    Map_Controller.AreaBtn_OnClick(_WorldData.m_SceneID, iWorldMapUnit.m_ClickType, iWorldMapUnit)
    Map_Controller.SetBtnSelect(iWorldMapUnit.m_AreaType, iWorldMapUnit.m_WorldDataKey)

end

--顯示世界地圖上的區域Icon
function Map_Controller.SetWorldAreaIcon()
    local _WorldData = WorldMapData.GetWorldMapDataByGroupNum(this.m_WorldGroupNum)
    this.m_WorldData = WorldMapData.GetMatchWorldData(_WorldData)
    for k, _WorldData in pairs(this.m_WorldData) do
        local _Unit
        local _WorldMapUnit = {}
        if m_Table_WorldMapUnit[k] then
            _WorldMapUnit = m_Table_WorldMapUnit[k]
        else
            _Unit = this.m_GObjPool_WorldMapUnit:Get()
            _WorldMapUnit.gameObject = _Unit
            _WorldMapUnit.Icon = _WorldMapUnit.gameObject:GetComponent(typeof(Image))
            _WorldMapUnit.Name = _WorldMapUnit.gameObject.transform:Find( "Text_Name" ):GetComponent(typeof(TMPro.TextMeshProUGUI))
            _WorldMapUnit.MainButton = Button.New(_Unit)
            _WorldMapUnit.gameObject.transform:SetParent(this.m_Trans_WorldMap.transform)
            _WorldMapUnit.gameObject.transform.localScale = Vector3.one
        end
        
        _WorldMapUnit.SceneID = _WorldData.m_SceneID
        local _WorldRatio = 0.48
        local _Pos = Vector3(_WorldData.m_LocationX * _WorldRatio, _WorldData.m_LocationY * _WorldRatio, 0)
        _WorldMapUnit.gameObject.transform.localPosition = _Pos
        SpriteMgr.Load(_WorldData.m_IconName, _WorldMapUnit.Icon)
        _WorldMapUnit.Name.text = TextData.Get(_WorldData.m_SceneNameString)
        _WorldMapUnit.gameObject.name = "Area_".._WorldData.m_SceneID.."_"..TextData.Get(_WorldData.m_SceneNameString)
        _WorldMapUnit.m_WorldDataKey = k
        _WorldMapUnit.m_WorldData = _WorldData
        _WorldMapUnit.m_AreaType = EAreaType.All
        _WorldMapUnit.m_ClickType = EClickType.WorldMap

        Button.ClearListener(_WorldMapUnit.MainButton)
        Button.AddListener(_WorldMapUnit.MainButton, EventTriggerType.PointerClick, WorldMapClick, _WorldMapUnit)

        _WorldMapUnit.gameObject:SetActive(true)

        m_Table_WorldMapUnit[k] = _WorldMapUnit
    end
    MapMgr.ReleasePoolObj(table.Count(this.m_WorldData), m_Table_WorldMapUnit, this.m_GObjPool_WorldMapUnit)
end
--endregion

--region 設定左邊區域

local _LoveNormalColor = Extension.GetColor("#92B8CD")
local _LoveSelectColor = Extension.GetColor("#05FFFA")

local function CheckMapUnlock(iMapData)
    return PlayerData.GetLv() >= iMapData.m_UnlockLv and (iMapData.m_UnlockFlag == 0 or PlayerData.IsHaveStaticFlag(iMapData.m_UnlockFlag))
end

--region 設定全部區域內容

local function AreaUnitClick(iWorldMapUnit)
    WorldMapClick(iWorldMapUnit)
    Map_Controller.UpdateNPCArrowDirection()

end
function Map_Controller.SetAreaRowItem()
    local _WorldData = WorldMapData.GetWorldMapDataByGroupNum(this.m_WorldGroupNum)
    this.m_WorldData = WorldMapData.GetMatchWorldData(_WorldData)
    if this.m_WorldData == nil then return end
    
    for k, _WorldData in pairs(this.m_WorldData) do
        local _AreaUnit

        if m_Table_AreaUnit[k] then
            _AreaUnit = m_Table_AreaUnit[k]
        else
            local _Unit
            _AreaUnit = {}
            _Unit = this.m_GObjPool_AreaUnit:Get()
            _AreaUnit.gameObject = _Unit
            _AreaUnit.LoveButton = Button.New(_Unit.gameObject.transform:Find( "Button_Love" ))
            _AreaUnit.LoveButtonRender = _Unit.gameObject.transform:Find( "Button_Love" ):GetComponent(typeof( UIRenderChangeColor ))
            _AreaUnit.MainButton = Button.New(_Unit.gameObject.transform:Find( "Button_Area" ))
            _AreaUnit.BG = _Unit.gameObject.transform:Find( "Image_BG" )
            _AreaUnit.Name = _AreaUnit.MainButton.gameObject.transform:Find( "Text_Name" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
            _AreaUnit.Icon = _AreaUnit.MainButton.gameObject.transform:Find( "Image_Icon" )
            _AreaUnit.IconImage = _AreaUnit.Icon.gameObject.transform:GetComponent(typeof(Image))
            _AreaUnit.IconButton = Button.New(_AreaUnit.Icon.gameObject)

            _AreaUnit.gameObject.transform:SetParent(this.m_Content_Area[EAreaType.All].transform)
            _AreaUnit.gameObject.transform.localScale = Vector3.one
        end
        _AreaUnit.SceneID = _WorldData.m_SceneID
        _AreaUnit.gameObject.name = "Area_" .. _WorldData.m_SceneID

        local _HaveMine = MapMgr.CheckHaveMine(_WorldData.m_SceneID)
        local _HaveFlag = false
        _AreaUnit.Icon.gameObject:SetActive(_HaveMine)
        if _HaveMine then
            _HaveFlag = MapMgr.CheckMineFlag(_WorldData.m_SceneID)
            if _HaveFlag then
                _AreaUnit.IconImage.color = Extension.GetColor("#F6C73A")
            else
                _AreaUnit.IconImage.color = Extension.GetColor("#78919F")
            end
        end
        _AreaUnit.m_HaveFlag = _HaveFlag
        _AreaUnit.m_WorldData = _WorldData
        _AreaUnit.m_AreaType = EAreaType.All
        _AreaUnit.m_ClickType = EClickType.Area

        _AreaUnit.IconButton:ClearListener()
        _AreaUnit.IconButton:AddListener(EventTriggerType.PointerClick, Map_Controller.OnClick_AreaIcon, _AreaUnit)
        _AreaUnit.BG.gameObject:SetActive(false)

        local _AreaLowLv = _WorldData.m_LowLv
        local _AreaName = TextData.Get(_WorldData.m_SceneNameString)

        local _TextColor = CheckMapUnlock(_WorldData) and Color.White or Color.Grey

        local _lvStr = GString.Format(TextData.Get(530102), _AreaLowLv)
        _lvStr = GString.GetTextWithSize(_lvStr, 23)
        _lvStr = GString.GetTextWithColor(_lvStr, _TextColor)
        local _NameStr = GString.GetTextWithSize(_AreaName, 28)
        _NameStr = GString.GetTextWithColor(_NameStr,_TextColor)
        local _Button_Name = _lvStr .. "<pos=37%>" .. _NameStr
        _AreaUnit.Name.text = _Button_Name

        local _isLove = Map_Controller.CheckLoveAreaList(_WorldData.m_SceneID)
        _AreaUnit.LoveButton:SetSelect(_isLove)
        _AreaUnit.LoveButton:ClearListener(EventTriggerType.PointerClick)
        _AreaUnit.LoveButton:AddListener(EventTriggerType.PointerClick, Map_Controller.AddLoveAreaList, _AreaUnit)
        Button.ClearListener(_AreaUnit.MainButton)
        Button.AddListener(_AreaUnit.MainButton, EventTriggerType.PointerClick, AreaUnitClick, _AreaUnit)
        
        _AreaUnit.gameObject:SetActive(true)
        m_Table_AreaUnit[k] = _AreaUnit
    end
    _isRefresh = true
    MapMgr.ReleasePoolObj(table.Count(this.m_WorldData), m_Table_AreaUnit, this.m_GObjPool_AreaUnit)

    Map_Controller.SetImageHalo(EAreaType.All, table.Count(this.m_WorldData))
end

function Map_Controller.OnClick_AreaIcon(iAreaUnit)
    local _iHaveFlag = iAreaUnit.m_HaveFlag
    local _iMineData = iAreaUnit.m_WorldData

    local _MineData = EnergyMineData.GetMapMineData(_iMineData.m_SceneID)
    if _MineData == nil then return end

    this.m_CanTeleport = _iHaveFlag
    local _TargetInfo = {}
    _TargetInfo.HaveFlag = _iHaveFlag
    local _MinePos = nil
    if _iHaveFlag then
        for k, v in pairs(_MineData) do
            _MinePos = Vector3(v.m_PosX, v.m_PosY, 0)
            _TargetInfo.EventID = v.m_MineID
            break
        end
        _TargetInfo.Type = 2
        _TargetInfo.SceneID = _iMineData.m_SceneID
    else
        _TargetInfo.TotaleMineCount = table.Count(_MineData)
        local _MineCount = 0
        for k, v in pairs(_MineData) do
            if PlayerData.IsHaveStaticFlag(v.m_ActiveFlag) then
                _MineCount = _MineCount + 1
            end
        end
        _TargetInfo.NowMineCount = _MineCount
    end
    local _Pos = iAreaUnit.gameObject.transform.position
    Map_Controller.SetInteractive(EClickType.AreaIcon, _Pos, nil, _MinePos, _TargetInfo)
end
--endregion

--region 設定我的最愛區域內容

function Map_Controller.SetLoveAreaRowItem()
    local _LoveData = this.m_LoveArea ~= nil and this.m_LoveArea.m_WorldGroup[this.m_WorldGroupNum] or nil
    if _LoveData == nil then return end

    this.m_ButtonTitle_Area[EAreaType.Love].gameObject:SetActive(table.Count(_LoveData) > 0)
    for k, v in pairs(_LoveData) do
        local _AreaData = WorldMapData.GetMapDataByIdx(v)
        local _LoveAreaUnit

        if m_Table_LoveAreaUnit[k] then
            _LoveAreaUnit = m_Table_LoveAreaUnit[k]
        else
            local _Unit
            _LoveAreaUnit = {}    
            _Unit = this.m_GObjPool_AreaUnit:Get()
            _LoveAreaUnit.gameObject = _Unit
            _LoveAreaUnit.LoveButton = Button.New(_Unit.gameObject.transform:Find( "Button_Love" ))
            _LoveAreaUnit.LoveButtonRender = _Unit.gameObject.transform:Find( "Button_Love" ):GetComponent(typeof( UIRenderChangeColor ))
            _LoveAreaUnit.MainButton = Button.New(_Unit.gameObject.transform:Find( "Button_Area" ))
            _LoveAreaUnit.BG = _Unit.gameObject.transform:Find( "Image_BG" )
            _LoveAreaUnit.Icon = _LoveAreaUnit.MainButton.gameObject.transform:Find( "Image_Icon" )
            _LoveAreaUnit.IconImage = _LoveAreaUnit.Icon.gameObject.transform:GetComponent(typeof(Image))
            _LoveAreaUnit.IconButton = Button.New(_LoveAreaUnit.Icon.gameObject)
            _LoveAreaUnit.gameObject.transform:SetParent(this.m_Content_Area[EAreaType.Love].transform)
            _LoveAreaUnit.gameObject.transform.localScale = Vector3.one
        end
        _LoveAreaUnit.gameObject.name = "LoveArea_" .. _AreaData.m_SceneID

        local _HaveMine = MapMgr.CheckHaveMine(_AreaData.m_SceneID)
        local _HaveFlag = false
        _LoveAreaUnit.Icon.gameObject:SetActive(_HaveMine)

        _LoveAreaUnit.m_LoveDataKey = k
        _LoveAreaUnit.m_HaveFlag = _HaveFlag
        _LoveAreaUnit.m_WorldData = _AreaData
        _LoveAreaUnit.m_AreaType = EAreaType.Love
        _LoveAreaUnit.m_ClickType = EClickType.Area

        if _HaveMine then
            _HaveFlag = MapMgr.CheckMineFlag(_AreaData.m_SceneID)
            if _HaveFlag then
                _LoveAreaUnit.IconImage.color = Extension.GetColor("#F6C73A")
            else
                _LoveAreaUnit.IconImage.color = Extension.GetColor("#78919F")
            end

            _LoveAreaUnit.IconButton:ClearListener()
            _LoveAreaUnit.IconButton:AddListener(EventTriggerType.PointerClick, Map_Controller.OnClick_AreaIcon, _LoveAreaUnit)
        end

        _LoveAreaUnit.BG.gameObject:SetActive(false)

        local _AreaLowLv = _AreaData.m_LowLv
        local _AreaName = TextData.Get(_AreaData.m_SceneNameString)

        local _TextColor = CheckMapUnlock(_AreaData) and Color.White or Color.Grey

        local _lvStr = GString.Format(TextData.Get(530102), _AreaLowLv)
        _lvStr = GString.GetTextWithSize(_lvStr, 23)
        _lvStr = GString.GetTextWithColor(_lvStr, _TextColor)
        local _NameStr = GString.GetTextWithSize(_AreaName, 28)
        _NameStr = GString.GetTextWithColor(_NameStr,_TextColor)
        local _Button_Name = _lvStr .. "<pos=37%>" .. _NameStr

        _LoveAreaUnit.LoveButtonRender.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _LoveSelectColor)
        _LoveAreaUnit.LoveButton:SetSelect(true)
        _LoveAreaUnit.LoveButton:ClearListener(EventTriggerType.PointerClick)
        _LoveAreaUnit.LoveButton:AddListener(EventTriggerType.PointerClick, Map_Controller.AddLoveAreaList, _LoveAreaUnit)
        _LoveAreaUnit.MainButton:SetText(_Button_Name)
        _LoveAreaUnit.MainButton:ClearListener(EventTriggerType.PointerClick)
        _LoveAreaUnit.MainButton:AddListener(EventTriggerType.PointerClick, WorldMapClick, _LoveAreaUnit)
        
        _LoveAreaUnit.gameObject:SetActive(true)
        m_Table_LoveAreaUnit[k] = _LoveAreaUnit
    end
    _isRefresh = true
    MapMgr.ReleasePoolObj(table.Count(_LoveData), m_Table_LoveAreaUnit, this.m_GObjPool_AreaUnit)

    Map_Controller.SetImageHalo(EAreaType.Love, table.Count(_LoveData))
end

--檢查我的最愛區域
function Map_Controller.CheckLoveAreaList(iSceneID)
    local _find = false
    local _idx = 0
    if this.m_LoveArea then
        for k, v in pairs(this.m_LoveArea.m_WorldGroup[this.m_WorldGroupNum]) do
            if v == iSceneID then
                _find = true
                _idx = k
                break
            end
        end
    end
    return _find, _idx
end

function Map_Controller.SetLoveBtnState(iType, iIdx, iIsLove, iUnit)
    if iType == EAreaType.Love then
        for k, v in pairs(m_Table_AreaUnit) do
            if v.SceneID == iIdx then
                Map_Controller.SetLoveBtnRender(iIsLove, v)
                break
            end
        end
    else
        Map_Controller.SetLoveBtnRender(iIsLove, iUnit)
    end
end

function Map_Controller.SetLoveBtnRender(iLove, iUnit)
    local _Color = iLove and _LoveNormalColor or _LoveSelectColor
    iUnit.LoveButtonRender.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _Color)
    iUnit.LoveButton:SetSelect(false)
end

--加入或移除我的最愛
function Map_Controller.AddLoveAreaList(iUnit)
    local iType = iUnit.m_AreaType
    local iSceneID = iUnit.m_WorldData.m_SceneID
    
    local _isLove, _idx = Map_Controller.CheckLoveAreaList(iSceneID)
    if not _isLove then
        table.insert(this.m_LoveArea.m_WorldGroup[this.m_WorldGroupNum], iSceneID)
    else
        table.remove(this.m_LoveArea.m_WorldGroup[this.m_WorldGroupNum], _idx)
    end
    table.sort(this.m_LoveArea.m_WorldGroup[this.m_WorldGroupNum],function(a, b)
        return WorldMapData.GetMapDataByIdx(a).m_LowLv < WorldMapData.GetMapDataByIdx(b).m_LowLv
    end)
    Map_Controller.SetLoveAreaRowItem()
    Map_Controller.SetAreaFoldState(EAreaType.Love)
    Map_Controller.SetLoveBtnState(iType, iSceneID, _isLove, iUnit)
    _needSaveLove = true
    _isNotInteractive = true
end
--endregion

--region 設定當前副本內容
function Map_Controller.SetDungeonRowItem()
    Map_Controller.SetTabsEnable(false)
    local _AreaData = SceneAttributeData.Get(SceneMgr.GetSceneID())
    local _Unit
    local _AreaUnit = {}
    if m_Table_DungeonUnit[1] then
        _AreaUnit = m_Table_DungeonUnit[1]
    else
        _Unit = this.m_GObjPool_AreaUnit:Get()
        _AreaUnit.gameObject = _Unit
        _AreaUnit.LoveButton = Button.New(_Unit.gameObject.transform:Find( "Button_Love" ))
        _AreaUnit.MainButton = Button.New(_Unit.gameObject.transform:Find( "Button_Area" ))
        _AreaUnit.BG = _Unit.gameObject.transform:Find( "Image_BG" )
        _AreaUnit.Icon = _AreaUnit.MainButton.gameObject.transform:Find( "Image_Icon" )
        _AreaUnit.gameObject.transform:SetParent(this.m_Content_Area[EAreaType.Dungeon].transform)
        _AreaUnit.gameObject.transform.localScale = Vector3.one
    end
    _AreaUnit.LoveButton.gameObject:SetActive(false)
    _AreaUnit.Icon.gameObject:SetActive(false)
    _AreaUnit.gameObject.name = "Area_".._AreaData.m_SceneID
    local _AreaName = TextData.Get(_AreaData.m_SceneNameID)
    _AreaUnit.MainButton:SetText(_AreaName)
    _AreaUnit.gameObject:SetActive(true)
    m_Table_DungeonUnit[1] = _AreaUnit
    Map_Controller.SetImageHalo(EAreaType.Dungeon, 1)
end
--endregion

--endregion

function Map_Controller.CalMapRatio()
    local _MapData = SceneAttributeData.Get(this.m_NowSceneID)
    local _ScaleLevel = 0
    if _MapData.m_SmallMapScale < table.Count(MapMgr.MAP_SCALE_LEVEL) then
        _ScaleLevel = MapMgr.MAP_SCALE_LEVEL[_MapData.m_SmallMapScale +1]
    else
        _ScaleLevel = MapMgr.MAP_SCALE_LEVEL[MapMgr.MAP_SCALE_LEVEL.Count]
    end
    MapSize = this.m_RectTransMax.x * 2
    MapRatio = MapSize * 0.01 / _ScaleLevel
end

--設定NPC相關資料(中間Icon、右邊按鈕)
function Map_Controller.SetTableObjClose(iTable)
    for k, v in pairs(iTable) do
        v.gameObject:SetActive(false)
    end
end

function Map_Controller.SetNPCItem(iSceneID)
    this.m_NowSceneID = iSceneID
    Map_Controller.CalMapRatio()
    if not Map_Controller.SetNoSignal(this.m_NowSceneID) then
        Map_Controller.SetAreaMapImg(this.m_NowSceneID)
    end

    this.m_TextTitle.text = TextData.Get(SceneAttributeData.Get(this.m_NowSceneID).m_SceneNameID)

    Map_Controller.SetNpc()
    Map_Controller.SetEnergyMine(iSceneID)
    Map_Controller.SetMissionTargetItem()    

    Map_Controller.SetNpcFoldState()
    Map_Controller.SetNpcIconActive()

    this.m_Group_Content_Npc.transform.localPosition = Vector3.zero
    _isRefresh = true
end

function Map_Controller.SetNoSignal(iSceneID)
    local _isNoSignal = false
    local _MapData = SceneAttributeData.Get(iSceneID)
    if not _MapData or _MapData.m_MapNoise == 1 then
        MapMgr.EnableGlitch()
        _isNoSignal = true
    else
        MapMgr.DisableGlitch()
        _isNoSignal = false
    end
    return _isNoSignal
end

--region 設定右邊區域

---設定Icon位置
function Map_Controller.SetIconPosition(iPosX, iPosY)
    --算地圖原始大小
    local _Ori_MapSize = MapSize / MapRatio

    --算相對座標
    local _RelativePosX = iPosX - (_Ori_MapSize * 0.5)
    local _RelativePosY = iPosY - (_Ori_MapSize * 0.5)

    --對應到地圖上的座標
    local _PosX = _RelativePosX * (MapSize / _Ori_MapSize)
    local _PosY = _RelativePosY * (MapSize / _Ori_MapSize)
    return Vector3(_PosX, _PosY, 0)
end

function Map_Controller.SetNpc()
    this.m_AreaNPCData = EventMapNpc.GetEventMapNPC(this.m_NowSceneID)
    Map_Controller.SetNPCRowItem()
    Map_Controller.SetNPCIconItem()
end

---點到NPC的按鈕
local function NPCButtonOnClick(iNPCUnit)
    local _AreaNPCDataIdx = iNPCUnit.m_AreaNPCIdx
    local _AreaNPCData = iNPCUnit.m_AreaNPCData
    Map_Controller.SetBtnSelect(EAreaType.Npc, _AreaNPCDataIdx)
    local _MarkPos = Map_Controller.SetIconPosition(_AreaNPCData.m_PositionX, _AreaNPCData.m_PositionY)
    local _MovePos = Vector3(_AreaNPCData.m_PositionX * 0.01, _AreaNPCData.m_PositionY * 0.01, 0)
    local _TargetInfo = {}
    _TargetInfo.EventID = _AreaNPCData.m_EventID
    _TargetInfo.Type = 1
    local _pos = Vector3.GetTransformPosition(nil, iNPCUnit.gameObject.transform) 
    Map_Controller.SetInteractive(EClickType.NPC, _pos, _MarkPos, _MovePos, _TargetInfo)
end

---設定NPC(按鈕)
function Map_Controller.SetNPCRowItem()
    Map_Controller.SetTableObjClose(m_Table_NPCUnit)
    for k, v in pairs(this.m_AreaNPCData) do
        local _NPCUnit
        if m_Table_NPCUnit[k] then
            _NPCUnit = m_Table_NPCUnit[k]
        else
            local _Unit
            _NPCUnit = {}
            _Unit = this.m_GObjPool_NPCUnit:Get()
            _NPCUnit.gameObject = _Unit
            _NPCUnit.BG = _Unit.gameObject.transform:Find( "Image_BG" )
            _NPCUnit.MainButton = Button.New(_Unit)
            _NPCUnit.Icon = _NPCUnit.gameObject.transform:Find( "Image_Icon" ):GetComponent(typeof(Image))
        end
        _NPCUnit.BG.gameObject:SetActive(false)

        local _MapNpcClassData = v.m_NPCMode ~= 0 and MapNpcClassData.GetMapDataByIdx(v.m_NPCMode) or nil
        local _HaveNpcClassData = _MapNpcClassData ~= nil and _MapNpcClassData.m_NPCGroup ~= 0
        if _HaveNpcClassData then
            _NPCUnit.gameObject.transform:SetParent(this.m_Content_NPC[_MapNpcClassData.m_NPCGroup].transform)
        end
        _NPCUnit.gameObject.transform.localScale = Vector3.one
        local _NPCName = EventStrAll:GetText(v.m_NPCNum)
        if _NPCName == nil then
            _NPCName = TextData.Get(v.m_NPCNum)
        end
        _NPCUnit.m_AreaNPCIdx = k
        _NPCUnit.m_AreaNPCData = v

        _NPCUnit.gameObject.name = "NPC_" .. _NPCName
        _NPCUnit.MainButton:SetText(_NPCName)
        _NPCUnit.MainButton:ClearListener(EventTriggerType.PointerClick)
        _NPCUnit.MainButton:AddListener(EventTriggerType.PointerClick, NPCButtonOnClick, _NPCUnit)

        local _IsSuccess, _Data
        local _GetIconFromMapIconSetting, _IconType = MapMgr.CheckIconType(v.m_NPCMode, v.m_EventID)
        _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(_IconType, 0)
        --練功NPC不秀Icon
        if _GetIconFromMapIconSetting and _MapNpcClassData ~= nil and _MapNpcClassData.m_NPCGroup ~= 3 then
            _NPCUnit.Icon.sprite = _Data.m_Sprite
            _NPCUnit.Icon.gameObject:SetActive(true)
        else
            _NPCUnit.Icon.gameObject:SetActive(false)
        end

        _NPCUnit.gameObject:SetActive(_HaveNpcClassData)
        m_Table_NPCUnit[k] = _NPCUnit
    end
    MapMgr.ReleasePoolObj(table.Count(this.m_AreaNPCData), m_Table_NPCUnit, this.m_GObjPool_NPCUnit)
end
--endregion

--region 設定中間區域

local function NPCCenterButtonOnClick(iNPCIconUnit)
    local _AreaNPCDataIdx = iNPCIconUnit.m_AreaNPCIdx
    local _AreaNPCData = iNPCIconUnit.m_AreaNPCData
    local _Pos = Vector3.GetTransformPosition(nil, iNPCIconUnit.gameObject.transform)

    local _MovePos = Vector3(_AreaNPCData.m_PositionX * 0.01, _AreaNPCData.m_PositionY * 0.01, 0)
    local _TargetInfo = {}
    _TargetInfo.EventID = _AreaNPCData.m_EventID
    _TargetInfo.Type = 1
    Map_Controller.SetBtnSelect(EAreaType.Npc, _AreaNPCDataIdx)
    Map_Controller.MoveToSelectButton(iNPCIconUnit.Group, iNPCIconUnit.gameObject.name)
    local _ShowPos = iNPCIconUnit.gameObject.transform.position

    Map_Controller.SetInteractive(EClickType.NPCIcon, _ShowPos, _Pos, _MovePos, _TargetInfo)
end

---設定NPC Icon
function Map_Controller.SetNPCIconItem()
    Map_Controller.SetTableObjClose(m_Table_NPCIconUnit)
    for k, v in pairs(this.m_AreaNPCData) do
        local _IconUnit
        local _NPCIconUnit
        
        if m_Table_NPCIconUnit[k] then
            _NPCIconUnit = m_Table_NPCIconUnit[k]
        else
            _NPCIconUnit = {}
            _IconUnit = this.m_GObjPool_NPCIconUnit:Get()
            _NPCIconUnit.gameObject = _IconUnit
            _NPCIconUnit.MainButton = Button.New(_IconUnit)
            _NPCIconUnit.Icon = _NPCIconUnit.gameObject:GetComponent(typeof(Image))
            _NPCIconUnit.gameObject.transform:SetParent(this.m_Trans_Map.transform)
            _NPCIconUnit.gameObject.transform.localScale = Vector3.one
        end
        local _MapNpcClassData = v.m_NPCMode ~= 0 and MapNpcClassData.GetMapDataByIdx(v.m_NPCMode) or nil
        _NPCIconUnit.Group = _MapNpcClassData ~= nil and _MapNpcClassData.m_NPCGroup or 0
        
        local _NPCName = EventStrAll:GetText(v.m_NPCNum)
        if _NPCName == nil then
            _NPCName = TextData.Get(v.m_NPCNum)
        end
        _NPCIconUnit.m_AreaNPCIdx = k
        _NPCIconUnit.m_AreaNPCData = v

        _NPCIconUnit.gameObject.name = "NPC_" .. _NPCName
        --算Icon位置 原點從左下角開始
        local _Pos = Map_Controller.SetIconPosition(v.m_PositionX, v.m_PositionY)
        _NPCIconUnit.gameObject.transform.localPosition = _Pos
        _NPCIconUnit.gameObject:SetActive(false)

        _NPCIconUnit.MainButton:ClearListener()
        _NPCIconUnit.MainButton:AddListener(EventTriggerType.PointerClick, NPCCenterButtonOnClick, _NPCIconUnit)

        if not m_IsEnableGlitch then
            local _IsSuccess, _Data = {}
            local _GetIconFromMapIconSetting, _IconType = MapMgr.CheckIconType(v.m_NPCMode, v.m_EventID)
            _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(_IconType, _Data)
            --練功NPC、沒任務NPC不秀Icon
            if _GetIconFromMapIconSetting and _NPCIconUnit.Group ~= 3 and _IconType ~= 7 then
                _NPCIconUnit.Icon.sprite = _Data.m_Sprite
                _NPCIconUnit.gameObject:SetActive(true)
            else
                _NPCIconUnit.gameObject:SetActive(false)
            end
        else
            _NPCIconUnit.gameObject:SetActive(false)
        end

        _NPCIconUnit.SceneID = _NPCIconUnit.gameObject.activeSelf and v.m_SceneID or 0

        m_Table_NPCIconUnit[k] = _NPCIconUnit
    end
    MapMgr.ReleasePoolObj(table.Count(this.m_AreaNPCData), m_Table_NPCIconUnit, this.m_GObjPool_NPCIconUnit)
end

--任務目標Icon調整位置比例
local TARGETRATIO = 35
--設定任務目標Icon
function Map_Controller.SetMissionTargetItem()
    local _TargetData = MapMgr.CheckMissionTarget(this.m_NowSceneID)
    for k, _data in pairs(_TargetData) do
        local _Unit
        local _TargetUnit = {}

        if m_Table_TargetUnit[k] then
            _TargetUnit = m_Table_TargetUnit[k]
        else
            _Unit = this.m_GObjPool_MissionUnit:Get()
            _TargetUnit.gameObject = _Unit
            _TargetUnit.Icon = _TargetUnit.gameObject.transform:Find("Image_Target"):GetComponent(typeof(Image))
            _TargetUnit.MainButton = Button.New(_Unit)
            _TargetUnit.gameObject.transform:SetParent(this.m_Trans_Map.transform)
            _TargetUnit.gameObject.transform.localScale = Vector3.one
        end
        _TargetUnit.gameObject.name = "Target".._data.FlagID

        local _FlagData = PlayerData.GetMovingFlag(_data.FlagID)
        --todo 組織任務
        local _PlotData = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK, _data.FlagID)
        local _Step = (_PlotData.StepBegin + _FlagData.m_Status) - 1
        local _PlotStep = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DSTEP, _Step)
        if _PlotStep.NPCBegin > 0 then
            local _MisNPC = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DNPC, _PlotStep.NPCBegin).NPCData[1]
            local _Pos = Map_Controller.SetIconPosition(_MisNPC.POSX, (_MisNPC.POSY + TARGETRATIO))
            _TargetUnit.gameObject.transform.localPosition = _Pos
            local _NPCData = MapMgr.GetNPCByEventID(this.m_NowSceneID, _MisNPC.EventID)
            if _NPCData ~= nil then
                local _IsSuccess, _Data = {}
                --採集物
                if _NPCData.m_NPCMode == EventNPCMode.Collection then
                    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Collection, _Data)
                --擊殺目標
                elseif _NPCData.m_NPCMode == EventNPCMode.Fight or _NPCData.m_NPCMode == EventNPCMode.MasterPoint then
                    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Fight, _Data)
                --其他
                else
                    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Mission, _Data)
                end
                if _IsSuccess then
                    _TargetUnit.Icon.sprite = _Data.m_Sprite
                    _TargetUnit.gameObject:SetActive(true)
                else
                    _TargetUnit.gameObject:SetActive(false)
                end
            else
                _TargetUnit.gameObject:SetActive(false)
            end
            _TargetUnit.MainButton:ClearListener()
            _TargetUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
                local _MovePos = Vector3(_NPCData.m_PositionX * 0.01, _NPCData.m_PositionY * 0.01, 0)
                local _TargetInfo = {}
                _TargetInfo.TitleStrID = _PlotData.TitleStrID
                _TargetInfo.TipStrID = _PlotStep.TipStrID
                _TargetInfo.EventID = _data.EventID
                _TargetInfo.Type = 1
                Map_Controller.SetInteractive(EClickType.Target, _Pos, _Pos, _MovePos, _TargetInfo)
            end)
        end
        m_Table_TargetUnit[k] = _TargetUnit
    end
    MapMgr.ReleasePoolObj(table.Count(_TargetData), m_Table_TargetUnit, this.m_GObjPool_MissionUnit)
end
--endregion

--region 礦脈
--設定礦脈
function Map_Controller.SetEnergyMine(iSceneID)
    Map_Controller.SetTableObjClose(m_Table_MineUnit)
    Map_Controller.SetTableObjClose(m_Table_MineIconUnit)

    this.m_isUnlockMine = MapMgr.CheckTimeMachineEffectFlag(iSceneID)
    if this.m_isUnlockMine then
        Map_Controller.SetMineRowItem()
        Map_Controller.SetMineIconItem()
    end
end

function Map_Controller.SetMineRowItem()
    local _TempMineData = EnergyMineData.GetMapMineData(this.m_NowSceneID)
    if _TempMineData == nil then return end
    local _MineData = {}
    for k, v in pairs(_TempMineData) do
        table.insert(_MineData, v)
    end
    
    for k, v in pairs(_MineData) do
        local _Unit
        local _MineUnit = {}
        if m_Table_MineUnit[k] then
            _MineUnit = m_Table_MineUnit[k]
        else
            _Unit = this.m_GObjPool_NPCUnit:Get()
            _MineUnit.gameObject = _Unit
            _MineUnit.BG = _Unit.gameObject.transform:Find( "Image_BG" )
            _MineUnit.MainButton = Button.New(_Unit)
            _MineUnit.Icon = _MineUnit.gameObject.transform:Find( "Image_Icon" ):GetComponent(typeof(Image))
        end
        _MineUnit.gameObject.transform:SetParent(this.m_Content_NPC[1].transform) --礦脈預設第一個
        _MineUnit.gameObject.transform.localScale = Vector3.one
        _MineUnit.BG.gameObject:SetActive(false)
        _MineUnit.gameObject.name = "Mine_" .. v.m_MineID

        local _ActiveMine = EnergyMineMgr.CheckMineActive(v.m_MineID)
        local _NPCName = v.m_MineType == 0 and TextData.Get(20112010) or TextData.Get(20112010 + k)
        _NPCName = _ActiveMine and _NPCName .. TextData.Get(20111027) or _NPCName
        _MineUnit.MainButton:SetText(_NPCName)

        _MineUnit.MainButton:ClearListener()
        _MineUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
            Map_Controller.SetBtnSelect(EAreaType.Mine, k)
            local _MarkPos = Map_Controller.SetIconPosition(v.m_PosX, v.m_PosY)
            local _MovePos = Vector3(v.m_PosX * 0.01, v.m_PosY * 0.01, 0)
            local _TargetInfo = {}
            _TargetInfo.EventID = v.m_MineID
            _TargetInfo.Type = 2
            local _Pos = _MineUnit.gameObject.transform.position
            Map_Controller.SetInteractive(EClickType.Mine, _Pos, _MarkPos, _MovePos, _TargetInfo)
        end)
        
        --設定Icon
        local _IsSuccess, _Data = {}
        local _ActiveMine = EnergyMineMgr.CheckMineActive(v.m_MineID)
        if _ActiveMine then
            _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.MineCompleted, _Data)
        else
            _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Mine, _Data)
        end
        _MineUnit.Icon.gameObject:SetActive(_IsSuccess)
        _MineUnit.Icon.sprite = _Data.m_Sprite
        _MineUnit.gameObject:SetActive(true)
        m_Table_MineUnit[k] = _MineUnit
    end
    MapMgr.ReleasePoolObj(table.Count(_MineData), m_Table_MineUnit, this.m_GObjPool_NPCUnit)
end

---礦脈Icon高度修正值
local _FixValue = 20
--設定礦脈Icon
function Map_Controller.SetMineIconItem()
    local _TempMineData = EnergyMineData.GetMapMineData(this.m_NowSceneID)
    if _TempMineData == nil then return end
    local _MineData = {}
    for k, v in pairs(_TempMineData) do
        table.insert(_MineData, v)
    end
    
    for k, v in pairs(_MineData) do
        local _IconUnit
        local _MineIconUnit = {}
        
        if m_Table_MineIconUnit[k] then
            _MineIconUnit = m_Table_MineIconUnit[k]
        else
            _IconUnit = this.m_GObjPool_MineUnit:Get()
            _MineIconUnit.gameObject = _IconUnit
            _MineIconUnit.MainButton = Button.New(_IconUnit)
            _MineIconUnit.Icon = _MineIconUnit.gameObject.transform:Find("Image_Icon"):GetComponent(typeof(Image))
            _MineIconUnit.gameObject.transform:SetParent(this.m_Trans_Map.transform)
            _MineIconUnit.gameObject.transform.localScale = Vector3.one
        end
        _MineIconUnit.gameObject.name = "Mine_" .. v.m_MineID
        --算Icon位置 原點從左下角開始
        local _Pos = Map_Controller.SetIconPosition(v.m_PosX, v.m_PosY + _FixValue)
        _MineIconUnit.gameObject.transform.localPosition = _Pos

        local _IsSuccess, _Data = {}
        if not m_IsEnableGlitch then
            _MineIconUnit.gameObject:SetActive(true)
            local _ActiveMine = EnergyMineMgr.CheckMineActive(v.m_MineID)
            if _ActiveMine then
                _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.MineCompleted, _Data)
            else
                _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(EMapIconsType.Mine, _Data)
            end
            _MineIconUnit.Icon.sprite = _Data.m_Sprite
        else
            _MineIconUnit.gameObject:SetActive(false)
        end

        _MineIconUnit.MainButton:ClearListener()
        _MineIconUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
            this.m_Group_Content_Npc.transform.localPosition = Vector3.zero
            Map_Controller.SetBtnSelect(EAreaType.Mine, k)
            local _MarkPos = Map_Controller.SetIconPosition(v.m_PosX, v.m_PosY)
            local _MovePos = Vector3(v.m_PosX * 0.01, v.m_PosY * 0.01, 0)
            local _TargetInfo = {}
            _TargetInfo.EventID = v.m_MineID
            _TargetInfo.Type = 2
            local _Pos = _MineIconUnit.gameObject.transform.position
            Map_Controller.SetInteractive(EClickType.MineIcon, _Pos, _MarkPos, _MovePos, _TargetInfo)
        end)
    
        _MineIconUnit.SceneID = _MineIconUnit.gameObject.activeSelf and v.m_MineMap or 0
    
        m_Table_MineIconUnit[k] = _MineIconUnit
    end
    MapMgr.ReleasePoolObj(table.Count(_MineData), m_Table_MineIconUnit, this.m_GObjPool_MineUnit)
end
--endregion

--取傳送點外觀編號
function Map_Controller.GetTeleportPointLook(iSceneID, iEventID)
    local _look = 0
    local _data = EventBlockData:GetEventBlockDataByIdx(iSceneID)
    if _data ~= nil then
        for k, v in pairs(_data.m_EventMapTransferAy) do
            if v.m_EventID == iEventID then
                _look = v.m_Looks
                break
            end
        end
    end
    return _look
end

--region 世界地圖頁籤
function Map_Controller.GetTabsCount()
    this.m_TabsCount = MapTabsData.m_DataCount
    return this.m_TabsCount
end

function Map_Controller.AfterReuseItemInit(iItem, iRowIdx)
    if iItem ~= nil then
        iItem.Button = Button.New(iItem.m_GObj.transform)
        m_Table_WorldTabs[iRowIdx] = iItem
        iItem.Button:AddListener(EventTriggerType.PointerClick,function() Map_Controller.OnClick_WorldMapTabs(iRowIdx) end)
    end
end

function Map_Controller.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    if iItem ~= nil then
        local _MapTabsData = MapTabsData.GetMapDataByIdx(iRowIdx)
        if _MapTabsData ~= nil then
            iItem.Button:SetText(TextData.Get(_MapTabsData.m_GroupString))
            local _show = true
            if _MapTabsData.m_StaticFlag ~= 0 then
                _show = PlayerData.IsHaveStaticFlag(_MapTabsData.m_StaticFlag)
            else
                _show = true
            end
            iItem.m_GObj:SetActive(_show)            
        else
            iItem.m_GObj:SetActive(false)
        end
    end
end

function Map_Controller.SetTabsEnable(iEnable)
    for k, v in pairs(m_Table_WorldTabs) do
        v.Button.m_IsInteractable = iEnable
    end
end
--endregion

--region 世界地圖設定
function Map_Controller.GetNowWorldGroup()
    local _WorldData = WorldMapData.GetMapDataByIdx(SceneMgr.GetSceneCID())
    if not _WorldData then
        local _SceneData = SceneAttributeData.Get(SceneMgr.GetSceneID())
        _WorldData = WorldMapData.GetMapDataByIdx(_SceneData.m_RelatedSceneID)
    end
    return _WorldData.m_WorldGroup
end

function Map_Controller.OnClick_WorldMapTabs(iIdx)
    if Map_Controller.CheckAreaType(SceneMgr.GetSceneID()) then
        return
    end
    Map_Controller.SetBtnSelect(EAreaType.World, iIdx)
    Map_Controller.ShowWorldMap(true, iIdx)
    if this.m_WorldGroupNum ~= iIdx then
        this.m_WorldGroupNum = iIdx
        MapMgr.DisableGlitch()
        Map_Controller.SetAreaRowItem()
        Map_Controller.SetLoveAreaRowItem()
        Map_Controller.SetWorldAreaIcon()
    end
    Map_Controller.SetAreaFoldState()
    Map_Controller.SetNpcFoldState()

    Map_Controller.UpdateAreaArrowDirection()
    Map_Controller.UpdateNPCArrowDirection()
    Map_Controller.SetWorldNowSelect()
    _isRefresh = true
    _isNotInteractive = true
end

--設定世界地圖背景圖片
function Map_Controller.SetWorldMapImg(iIdx)
    local _NowWorld = MapTabsData.GetMapDataByIdx(iIdx)
    TextureMgr.Load(_NowWorld.m_WorldMapFile, false, function(iTex)
		this.m_Image_WorldMap.texture = iTex
	end)
end

function Map_Controller.ShowWorldMap(iShow, iIdx)
    if iShow and iIdx ~= nil then
        Map_Controller.SetWorldMapImg(iIdx)
    end
    this.m_Image_WorldMap_Mask.gameObject:SetActive(iShow)
    this.m_Animation_WorldMap:DoActive(iShow)
    this.m_Image_WorldMap_LeftLine.gameObject:SetActive(iShow)
    this.m_Image_WorldMap_BottomLine.gameObject:SetActive(iShow)
end
--endregion

local function SetSelect(iTable, iIdx, iSelect, iIsTabs)
    if iIdx ~= 0 and iTable[iIdx] ~= nil then
        local _button = {}
        if iIsTabs then
            _button = iTable[iIdx].Button
            _button:SetSelect(iSelect)
        else
            _button = iTable[iIdx].BG
            _button.gameObject:SetActive(iSelect)
        end
    end
end

function Map_Controller.CheckNowAreaClick()
    for k, v in pairs(m_Table_WorldTabs) do
        v.Button:ChangeStateTransitionGroup(0)
    end
    if m_Table_WorldTabs[this.m_WorldGroupNum] then
        m_Table_WorldTabs[this.m_WorldGroupNum].Button:ChangeStateTransitionGroup(1)
    end

    SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, false)
    for k, v in pairs(m_Table_AreaUnit) do
        if v.SceneID == SceneMgr.GetSceneID() then
            this.m_AreaSelectIdx = k
            SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, true)
            break
        end
    end
    
    SetSelect(m_Table_LoveAreaUnit, this.m_LoveSelectIdx, false)

    SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, false)

    SetSelect(m_Table_MineUnit, this.m_MineSelectIdx, false)
end

--region 設定按鈕點擊效果
---@param iType EAreaType 按鈕種類
function Map_Controller.SetBtnSelect(iType, iIdx)
    if iIdx == 0 then return end    

    if iType == EAreaType.All then

        SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, false)
        this.m_AreaSelectIdx = iIdx
        SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, true)

        SetSelect(m_Table_LoveAreaUnit, this.m_LoveSelectIdx, false)

    elseif iType == EAreaType.Npc then

        SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, false)
        this.m_NpcSelectIdx = iIdx
        SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, true)

        SetSelect(m_Table_MineUnit, this.m_MineSelectIdx, false)

    elseif iType == EAreaType.Love then

        SetSelect(m_Table_LoveAreaUnit, this.m_LoveSelectIdx, false)
        this.m_LoveSelectIdx = iIdx
        SetSelect(m_Table_LoveAreaUnit, this.m_LoveSelectIdx, true)

        SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, false)

    elseif iType == EAreaType.World then

        for k, v in pairs(m_Table_WorldTabs) do
            v.Button:ChangeStateTransitionGroup(0)
        end
        m_Table_WorldTabs[iIdx].Button:ChangeStateTransitionGroup(1)

        SetSelect(m_Table_AreaUnit, this.m_AreaSelectIdx, false)
        SetSelect(m_Table_LoveAreaUnit, this.m_LoveSelectIdx, false)
        SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, false)

    elseif iType == EAreaType.Mine then

        SetSelect(m_Table_MineUnit, this.m_MineSelectIdx, false)
        this.m_MineSelectIdx = iIdx
        SetSelect(m_Table_MineUnit, this.m_MineSelectIdx, true)

        SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, false)

    end
end

function Map_Controller.SetNpcBtnUnSelect()
    SetSelect(m_Table_NPCUnit, this.m_NpcSelectIdx, false)
    this.m_NpcSelectIdx = 0
    SetSelect(m_Table_MineUnit, this.m_MineSelectIdx, false)
    this.m_MineSelectIdx = 0
end

local NpcSpacing = 7
---點擊中間NPC Icon 右邊ScrollView移到該NPC位置
function Map_Controller.MoveToSelectButton(iType, iName)
    local _FoldCount = 0
    local _TempContent = {}
    local _Content = {}
    local _ChildCount = 0
    --找出目標NPC在哪個群組、計算在他之前有幾個打開的群組
    for k, v in pairs(this.m_Group_NPC) do
        if v.gameObject.activeSelf then
            _FoldCount = _FoldCount + 1
            table.insert(_TempContent, k)
        end
        if k == iType then
            if not v.gameObject.activeSelf then
                this.m_IsFoldNPC[iType] = true
                this.m_FoldNPCButton[iType]:SetSelect(true)
                v.gameObject:SetActive(true)
                Map_Controller.UpdateNPCArrowDirection()
            end
            _Content = this.m_Content_NPC[k]
            _ChildCount = _Content.transform.childCount - 1
            break
        end
    end
    --計算目標群組前面的群組高度
    local _PreContentHeight = 0
    for i = 1, _FoldCount - 1 do
        local _PreContent = this.m_Content_NPC[_TempContent[i]]
        local _ActiveChildCount = 0
        for j = 0, _PreContent.transform.childCount - 1 do
            local _Child = _PreContent.transform:GetChild(j)
            if _Child.gameObject.activeSelf then
                _ActiveChildCount = _ActiveChildCount + 1
            end
        end
        _PreContentHeight = _PreContentHeight + (_ActiveChildCount * m_NpcHeight) + NpcSpacing
    end
    --找到目標NPC並計算整體長度
    local _NpcCount = 0
    for i = 0, _ChildCount do
        local _ChildObj = _Content.transform:GetChild(i).gameObject
        if _ChildObj.activeSelf then
            _NpcCount = _NpcCount + 1
        end
        if _ChildObj.name == iName then
            local _MovePosY = ((m_FoldHeight + NpcSpacing) * _FoldCount) + ((_NpcCount - 1) * m_NpcHeight) + _PreContentHeight
            local _ContentNpcPos = this.m_Group_Content_Npc.transform.localPosition
            _ContentNpcPos.y = _MovePosY
            HEMTimeMgr.DoFunctionDelay(0.05, function() this.m_Group_Content_Npc.transform.localPosition = _ContentNpcPos end)
            break
        end
    end
end
--endregion

---區域按鈕點擊事件
function Map_Controller.AreaBtn_OnClick(iSceneID, iType, iUnit)
    local _WorldData = WorldMapData.GetMapDataByIdx(iSceneID)
    local _isLvMatch = PlayerData.GetLv() >= _WorldData.m_UnlockLv
    local _isFlagMatch = _WorldData.m_UnlockFlag == 0 or PlayerData.IsHaveStaticFlag(_WorldData.m_UnlockFlag)
    if _isLvMatch and _isFlagMatch then
        MapMgr.DisableGlitch()
        Map_Controller.ShowWorldMap(false)
        Map_Controller.SetAreaMapImg(iSceneID)
        Map_Controller.SetNPCItem(iSceneID)
        _isNotInteractive = true
    else
        local _TargetInfo = {}
        local _str = ""
        _TargetInfo.ShowMission = _isLvMatch and not _isFlagMatch
        if _isLvMatch and not _isFlagMatch then
            local _PlotData
            local _StaticMission = MissionMgr.GetMissionByStaticFlag()
            if(_StaticMission[_WorldData.m_UnlockFlag] ~= nil) then
                _PlotData = _StaticMission[_WorldData.m_UnlockFlag]
            else
                MessageMgr.AddCenterMsg(true, TextData.Get(20800003))
                return
            end
            _str = GString.Format(TextData.Get(20102001), EventStrAll:GetText(_PlotData.TitleStrID))
            this.m_Btn_Mission:ClearListener()
            this.m_Btn_Mission:AddListener(EventTriggerType.PointerClick, ExploreDairyMgr.OpenToMission, _PlotData)
        else
            _str = GString.Format(TextData.Get(20800004), _WorldData.m_LowLv)
        end
        _TargetInfo.ConditionText = _str
        local _Pos = iUnit.gameObject.transform.position
        Map_Controller.SetInteractive(iType, _Pos, nil, nil, _TargetInfo)
    end
end

--設定區域地圖背景圖片
function Map_Controller.SetAreaMapImg(iIdx)
    local _MapData = SceneAttributeData.Get(iIdx)
    if not _MapData or _MapData.m_MapNoise == 1 then
        return
    end
    MapMgr.ChangeBigMapTexture(_MapData.m_PrefabSName)
end

---調整地圖縮放大小
function Map_Controller.ConfigureMap(iSceneID)
    local _MapData = SceneAttributeData.Get(iSceneID)
    if _MapData.m_SmallMapScale < table.Count(MapMgr.MAP_SCALE_LEVEL) then
        this.m_ScaleLevel = MapMgr.MAP_SCALE_LEVEL[_MapData.m_SmallMapScale +1]
    else
        this.m_ScaleLevel = MapMgr.MAP_SCALE_LEVEL[MapMgr.MAP_SCALE_LEVEL.Count]
    end
    return MapMgr.MAP_SIZE / this.m_ScaleLevel
end

--介面互動按鈕清單點擊功能
local InteractiveEvent = {}

---點中地圖
function Map_Controller.Map_OnClick()
    local _Position = MapMgr.GetNavigateToClickPoint()

    if not _Position then
        return
    end

    --region 等比縮小符合地圖Mesh
    --取地圖Rect
    local _Rect = Map_Controller.m_RectTrans_Map.rect
    --Rect最大點位
    local _MaxPoint = _Rect.max
    --歸一化 * 地圖比例 = 實際向量
    local _FinalPosition = Vector2.New((_Position.x + _MaxPoint.x)/(_MaxPoint.x * 2) * MapMgr.MAP_SIZE,
                                       (_Position.y + _MaxPoint.y)/(_MaxPoint.y * 2) * MapMgr.MAP_SIZE)
    --endregion
    --region 打射線
    local _LayerMask = 2 ^ Layer.UIModel
    -- 算世界座標
    -- 取 Mesh 左下原點世界座標
    local _HitPos = MapMgr.m_BigMapMesh.m_MeshObj.transform.position
    -- 射線起始世界座標 = 起始座標 + 偏移向量
    local _WorldPos =  Vector3(_HitPos.x + _FinalPosition.x, _HitPos.y + _FinalPosition.y, -10)
    -- 射線方向
    local _Direction = Vector3(0, 0, 1) * 20
    -- 打射線
    local _isHit, _HitObj = Physics.Raycast(_WorldPos, _Direction, RaycastHit.out, MaxFloorRayCastLength, _LayerMask)

    if _isHit then
        Map_Controller.SetNpcBtnUnSelect()

        local _MoveRatio = Map_Controller.ConfigureMap(this.m_NowSceneID)
        local _TargetInfo = {}
        _TargetInfo.EventID = 0
        _TargetInfo.Type = 0
        Map_Controller.SetInteractive(EClickType.UI, _Position, _Position, _FinalPosition / _MoveRatio, _TargetInfo)
    else
        MapMgr.HideClickPoint()
    end

    -- 畫射線
    MapMgr.Debugger(nil, 4, _WorldPos, _Direction)
    --endregion
end

--region 互動介面
local ButtonGroupStr = {"Move", "Teleport", "Comming", "Waring", "MineRadar"}
local Group_Condition_Height, Group_Condition_Width = 0
---初始化互動介面相關元件
function Map_Controller.InitInteractiveUI()
    this.m_Group_Interactive = this.m_ViewRef.m_Dic_Trans:Get("&Group_Interactive")
    this.m_Group_Interactive_Rect = this.m_Group_Interactive.gameObject:GetComponent("RectTransform")
    this.m_Group_Interactive.gameObject:SetActive(false)

    this.m_Text_Pos = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Pos")

    this.m_Group_Caption = this.m_ViewRef.m_Dic_Trans:Get("&Group_Caption")
    this.m_Text_Caption_Name = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Caption_Name")
    this.m_Text_Caption_Describe = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Caption_Describe")
    this.m_Image_Interactive_Line = this.m_ViewRef.m_Dic_Trans:Get("&Image_Interactive_Line")
    this.m_Trans_InteractiveEvent = {}
    this.m_Image_DownLine = {}
    for k, v in pairs(ButtonGroupStr) do
        this.m_Trans_InteractiveEvent[k] = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_" .. v))
        this.m_Image_DownLine[k] = this.m_Trans_InteractiveEvent[k].gameObject.transform:Find("Image_Line")
    end

    for i = 1, table.Count(this.m_Trans_InteractiveEvent) do
        this.m_Trans_InteractiveEvent[i]:AddListener(EventTriggerType.PointerClick, function()
            InteractiveEvent[i]()
            _isNotInteractive = true
        end)
    end

    this.m_Group_Component = {this.m_Text_Pos, this.m_Group_Caption,
        this.m_Trans_InteractiveEvent[1], this.m_Trans_InteractiveEvent[2], this.m_Trans_InteractiveEvent[3], this.m_Trans_InteractiveEvent[4], this.m_Trans_InteractiveEvent[5]}

    this.m_Image_MarkIcon = this.m_ViewRef.m_Dic_Trans:Get("&Image_MarkIcon")
    this.m_Image_MarkIcon.gameObject:SetActive(false)
    this.m_Image_Mark = this.m_Image_MarkIcon.gameObject.transform:Find("Image_Mark"):GetComponent("Image")

    this.m_Group_Hint = this.m_ViewRef.m_Dic_Trans:Get("&Group_Hint")
    this.m_Text_Hint = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Hint")
    
    this.m_Group_Condition = this.m_ViewRef.m_Dic_Trans:Get("&Group_Condition")
    Group_Condition_Height = this.m_Group_Condition:GetComponent("RectTransform").rect.height
    Group_Condition_Width = this.m_Group_Condition:GetComponent("RectTransform").rect.width
    this.m_Btn_Mission = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Mission"))
    this.m_Trans_ConditionLv = this.m_ViewRef.m_Dic_Trans:Get("&Trans_ConditionLv")
    this.m_Text_ConditionLv = this.m_ViewRef.m_Dic_TMPText:Get("&Text_ConditionLv")
end

local Click_Fix_PosY = 15
---設定點擊位置顯示Icon
function Map_Controller.SetClickIcon(iShow, iPos)
    this.m_Image_Click.gameObject:SetActive(iShow)
    if iShow then

        this.m_Image_Click.gameObject.transform.localPosition = Vector2(iPos.x, iPos.y + Click_Fix_PosY)
        this.m_Image_Click_Rect:SetAsLastSibling()
        this.m_TweenBasePosition = Vector3(iPos.x, iPos.y + Click_Fix_PosY,0)
        Map_Controller.PlayClickIconTween()
    end
end

---圖釘tween 演出參數
---總時長
local m_HintTweenTime = 0.83
---上升階段佔演出的時長
local m_UpPeriod = 0.7
---下降階段佔演出的時長
local m_DownPeriod = 0.3
---垂直方向最大飄移距離
local m_HintTween_DeltaY_Max = 12
---Scale變化 max
local m_HintTween_Scale_Max = 1.2
---Scale變化 min
local m_HintTween_Scale_Min = 1.2
---透明度變化 max
local m_HintTween_Alpha_Max = 1.0
---透明度變化 min
local m_HintTween_Alpha_Min = 0.8
---透明度變化 delta
local m_HintTween_Alpha_delta = m_HintTween_Alpha_Max-m_HintTween_Alpha_Min


function Map_Controller.PlayClickIconTween()
    if this.m_ClickIconTweenID ~= nil then
        LeanTween.cancel(this.m_ClickIconTweenID)
    end
    local _ScaleValue
    if this.m_TweenBasePosition ==nil then
        this.m_TweenBasePosition = Vector3.zero
    end

    this.m_ClickIconTweenID = LeanTween.value( this.m_Image_Click.gameObject, System.Action_float(
            function(iValue)
                ---上升階段
                if iValue < m_UpPeriod then
                    if this.m_IsPlayingClickIconTween == false then
                        this.m_IsPlayingClickIconTween = true
                    end
                    _ScaleValue =  m_HintTween_Scale_Min + (m_HintTween_Scale_Max-m_HintTween_Scale_Min)*(iValue/m_UpPeriod)
                    this.m_Image_Click.gameObject.transform.localScale = Vector3(_ScaleValue,_ScaleValue,_ScaleValue)
                    this.m_Image_Click.color = Color(this.m_Image_Click.color.r ,this.m_Image_Click.color.g,this.m_Image_Click.color.b, m_HintTween_Alpha_Max - ( iValue/m_UpPeriod * m_HintTween_Alpha_delta))
                    this.m_Image_Click.gameObject.transform.localPosition = Vector3(0,m_HintTween_DeltaY_Max * iValue/m_UpPeriod,0) + this.m_TweenBasePosition
                else
                    ---下降階段
                    _ScaleValue = m_HintTween_Scale_Max - (m_HintTween_Scale_Max-m_HintTween_Scale_Min)*( (iValue-m_UpPeriod)/m_DownPeriod )
                    this.m_Image_Click.gameObject.transform.localScale = Vector3(_ScaleValue,_ScaleValue,_ScaleValue)
                    this.m_Image_Click.color = Color(this.m_Image_Click.color.r ,this.m_Image_Click.color.g,this.m_Image_Click.color.b, m_HintTween_Alpha_Min + ( (iValue-m_UpPeriod)/(m_DownPeriod) *m_HintTween_Alpha_delta ))

                    this.m_Image_Click.gameObject.transform.localPosition = Vector3(0, m_HintTween_DeltaY_Max*((1-iValue)/m_DownPeriod),0)+ this.m_TweenBasePosition
                end

                end), 0, 1, m_HintTweenTime)
                :setOnComplete(System.Action(function()
                    this.m_IsPlayingClickIconTween = false
                end )).id

end

---顯示互動介面
---@param iClickType number 點擊種類 1.點介面 2.點NPC按鈕 3.點任務目標 4.能量礦脈(右邊清單) 5.能量礦脈(中間地圖) 6.點區域 7.點區域Icon 8.點世界地圖
---@param iShowPos Vector3 UI顯示位置
---@param iMarkPos Vector3 標記顯示位置
---@param iMovePos Vector3 點到的座標
---@param iTargetInfo Data 點任務目標要顯示的資訊
function Map_Controller.SetInteractive(iClickType, iShowPos, iMarkPos, iMovePos, iTargetInfo)
    if not MapMgr.m_IsOpenMap or m_IsEnableGlitch then return end
    if CanInteractive == false then return end

    this.m_Group_Interactive.gameObject:SetActive(false)
    this.m_Group_Hint.gameObject:SetActive(false)
    this.m_Group_Condition.gameObject:SetActive(false)

    --判斷是點左邊還右邊並記錄點到當下content y的位置
    m_isClickLeft = iClickType == EClickType.Area or iClickType == EClickType.AreaIcon
    if m_isClickLeft then
        m_NowClickLeftPosY = this.m_Group_ContentArea.gameObject.transform.localPosition.y
    end
    m_isClickRight = iClickType == EClickType.NPC or iClickType == EClickType.Mine
    if m_isClickRight then
        m_NowClickRightPosY = this.m_Group_Content_Npc.gameObject.transform.localPosition.y
    end

    --傳送用事件編號
    this.m_EventID_Teleport = iTargetInfo.EventID or nil
    --傳送類型
    this.m_TeleportType = iTargetInfo.Type or nil
    --礦脈編號
    this.m_MineID = iTargetInfo.EventID or nil
    --點擊到的區域場景編號
    this.m_ClickSceneID = iTargetInfo.SceneID or nil
    Map_Controller.SetButtonState(iClickType)

    local _parent

    --先判斷是否點擊區域部分
    if iClickType == EClickType.Area or iClickType == EClickType.WorldMap then
        this.m_Group_Condition.gameObject:SetActive(true)
        this.m_Btn_Mission.gameObject:SetActive(iTargetInfo.ShowMission)
        this.m_Trans_ConditionLv.gameObject:SetActive(not iTargetInfo.ShowMission)
        if iTargetInfo.ShowMission then
            this.m_Btn_Mission:SetText(iTargetInfo.ConditionText)
        else
            this.m_Text_ConditionLv.text = iTargetInfo.ConditionText
        end

        _parent = iClickType == EClickType.Area and this.m_Group_Left.gameObject.transform or this.m_Main_Panel.gameObject.transform
        this.m_Group_Condition.gameObject.transform:SetParent(_parent)
        this.m_Group_Condition:SetAsLastSibling()
        this.m_Group_Condition.position = Map_Controller.FixInteractivePos(iShowPos, iClickType)
        return
    end
    
    if iClickType == EClickType.AreaIcon then
        this.m_Group_Interactive.gameObject:SetActive(iTargetInfo.HaveFlag)
        this.m_Group_Hint.gameObject:SetActive(not iTargetInfo.HaveFlag)
        if not iTargetInfo.HaveFlag then
            this.m_Text_Hint.text = GString.Format(TextData.Get(20800001), iTargetInfo.TotaleMineCount, iTargetInfo.NowMineCount)
            this.m_Group_Hint.gameObject.transform:SetParent(this.m_Group_Left.gameObject.transform)
            this.m_Group_Hint:SetAsLastSibling()
            this.m_Group_Hint.position = Map_Controller.FixInteractivePos(iShowPos, iClickType)
            return
        end
    end

    --不是區域部分再進行後續判斷
    this.m_Group_Interactive.gameObject:SetActive(true)
    Map_Controller.SetClickIcon(iMarkPos ~= nil, iMarkPos)
        
    if iClickType == EClickType.NPC or iClickType == EClickType.Mine then
        _parent = this.m_Group_Right.gameObject.transform
        this.m_Text_Pos.gameObject:SetActive(false)
        this.m_Image_Interactive_Line.gameObject:SetActive(false)

    elseif iClickType == EClickType.AreaIcon then
        _parent = this.m_Group_Left.gameObject.transform
        this.m_Text_Pos.gameObject:SetActive(false)
        this.m_Image_Interactive_Line.gameObject:SetActive(false)

    else
        _parent = this.m_Image_Map.gameObject.transform
        this.m_Text_Pos.gameObject:SetActive(true)
        this.m_Image_Interactive_Line.gameObject:SetActive(true)
        local _Pos = GString.Format("{0},{1}", math.floor(iMovePos.x * 100), math.floor(iMovePos.y * 100))
        this.m_Text_Pos.text = _Pos
    end
    this.m_Group_Interactive.gameObject.transform:SetParent(_parent)
    this.m_Group_Interactive_Rect:SetAsLastSibling()
    
    --點擊任務目標要顯示事件名字跟說明
    if iClickType == EClickType.Target then
        this.m_Text_Caption_Name.text = EventStrAll:GetText(iTargetInfo.TitleStrID)
        this.m_Text_Caption_Describe.text = EventStrAll:GetText(iTargetInfo.TipStrID)
    end
    this.m_Group_Caption.gameObject:SetActive(iClickType == EClickType.Target)
    
    this.m_InteractivePos.Move_x = iMovePos ~= nil and iMovePos.x or nil
    this.m_InteractivePos.Move_y = iMovePos ~= nil and iMovePos.y or nil
    this.m_InteractivePos.Mark_x = iMarkPos ~= nil and iMarkPos.x or nil
    this.m_InteractivePos.Mark_y = iMarkPos ~= nil and iMarkPos.y or nil

    local _Height = MapMgr.CalComponentHeight(iClickType, this.m_Group_Component)
    this.m_Group_Interactive.sizeDelta = Vector2(this.m_Group_Interactive.sizeDelta.x, _Height)
    if iClickType == EClickType.UI then
        this.m_Group_Interactive.localPosition = Map_Controller.FixInteractivePos(iShowPos, iClickType)
    else
        this.m_Group_Interactive.position = Map_Controller.FixInteractivePos(iShowPos, iClickType)
    end
end

--region 互動介面點擊事件

--設定按鈕狀態 1.自動尋路 2.傳送 3.集結 4.警告 5.礦脈雷達
function Map_Controller.SetButtonState(iType)
    for k, v in pairs(this.m_Trans_InteractiveEvent) do
        this.m_Trans_InteractiveEvent[k].gameObject:SetActive(false)
        v:SetDisable()
    end

    --尋路按鈕除了點擊左邊區域Icon以外都顯示
    if iType ~= EClickType.AreaIcon then
        this.m_Trans_InteractiveEvent[1].gameObject:SetActive(true)
        this.m_Trans_InteractiveEvent[1]:SetEnable()
    end

    -- 1.點介面 1開 2不開 3.4看情況
    if iType == EClickType.UI then
        local _SceneType = SceneAttributeData.Get( this.m_NowSceneID ).m_SceneMarkType
        --標示按鈕
        -- 沒隊伍時不顯示 有隊伍時是隊長就正常顯示 不是隊長就變灰顯示
        if _SceneType == EMapPointAuthorityType[0] or _SceneType == EMapPointAuthorityType[2] then
            this.m_Trans_InteractiveEvent[3].gameObject:SetActive(TeammateMgr.m_IsInTeam and this.m_NowSceneID == SceneMgr.GetSceneID())
            this.m_Trans_InteractiveEvent[4].gameObject:SetActive(TeammateMgr.m_IsInTeam and this.m_NowSceneID == SceneMgr.GetSceneID())
            if TeammateMgr.m_IsInTeam and TeammateMgr.m_IsLeader then
                this.m_Trans_InteractiveEvent[3]:SetEnable()
                this.m_Trans_InteractiveEvent[4]:SetEnable()
            end
        -- 有加入幫會才顯示 有幫會權限正常顯示 沒幫會權限變灰顯示
        elseif _SceneType == EMapPointAuthorityType[1] then
            --TODO 等幫會系統完成再處理
        end

    -- 2.點NPC按鈕 1開 2看情況 3.4不開
    elseif iType == EClickType.NPC or iType == EClickType.NPCIcon then
        this.m_Trans_InteractiveEvent[2]:SetEnable()
        local _HaveMine = MapMgr.CheckHaveMine(this.m_NowSceneID)
        if _HaveMine then
            this.m_Trans_InteractiveEvent[2].gameObject:SetActive(this.m_isUnlockMine)
            if this.m_isUnlockMine then
                this.m_CanTeleport = MapMgr.CheckHaveFlag(this.m_NowSceneID)
                Map_Controller.SetTeleportBtnState(this.m_CanTeleport)
            end
        else
            this.m_Trans_InteractiveEvent[2].gameObject:SetActive(true)
            this.m_CanTeleport = MapMgr.CheckHaveFlag(this.m_NowSceneID)
            Map_Controller.SetTeleportBtnState(this.m_CanTeleport)
        end

    -- 3.點任務目標 1開 2開 3.4不開
    elseif iType == EClickType.Target then
        this.m_Trans_InteractiveEvent[2].gameObject:SetActive(true)
        this.m_Trans_InteractiveEvent[2]:SetEnable()
        this.m_CanTeleport = MapMgr.CheckHaveFlag(this.m_NowSceneID)
        Map_Controller.SetTeleportBtnState(this.m_CanTeleport)

    -- 4、5.能量礦脈 1開 2看情況 3.4不開 5看情況
    elseif iType == EClickType.Mine or iType == EClickType.MineIcon then
        this.m_CanTeleport = EnergyMineMgr.CheckMineActive(this.m_MineID)
        Map_Controller.SetTeleportBtnState(this.m_CanTeleport)
        this.m_Trans_InteractiveEvent[2].gameObject:SetActive(this.m_CanTeleport)
        this.m_Trans_InteractiveEvent[5].gameObject:SetActive(not this.m_CanTeleport)
        this.m_Trans_InteractiveEvent[2]:SetEnable()
        this.m_Trans_InteractiveEvent[5]:SetEnable()

    -- 6.區域Icon 只開2
    elseif iType == EClickType.AreaIcon then
        this.m_Trans_InteractiveEvent[2].gameObject:SetActive(true)
        this.m_Trans_InteractiveEvent[2]:SetEnable()
        this.m_CanTeleport = MapMgr.CheckMineFlag(this.m_ClickSceneID)
        Map_Controller.SetTeleportBtnState(this.m_CanTeleport)
    end

    local _LastBtn = 0
    for k, v in pairs(this.m_Trans_InteractiveEvent) do
        if v.gameObject.activeSelf then
            _LastBtn = k
        end
    end
    for k, v in pairs(this.m_Image_DownLine) do
        this.m_Image_DownLine[k].gameObject:SetActive(k ~= _LastBtn)
    end
end

function Map_Controller.SetTeleportBtnState(iEnable)
    local _State = iEnable and 0 or 1
    this.m_Trans_InteractiveEvent[2]:ChangeStateTransitionGroup(_State)
end
--endregion

--互動介面X軸修正值
local FIXPOS_X = 20
---修正互動介面顯示位置
function Map_Controller.FixInteractivePos(iPos, iType)
    local _width = this.m_Group_Interactive_Rect.rect.width
    local _height = this.m_Group_Interactive_Rect.rect.height
    local _Pos = iPos
    if iType == EClickType.NPC or iType == EClickType.Mine then
        --減掉_width後會多往左位移一點 所以-20來修正
        _Pos.x = _Pos.x - (_width - FIXPOS_X) * 0.01
        return _Pos

    elseif iType == EClickType.Area or iType == EClickType.AreaIcon then
        return _Pos

    elseif iType == EClickType.WorldMap then
        _Pos.x = _Pos.x - Group_Condition_Width * 0.005
        _Pos.y = _Pos.y + Group_Condition_Height * 0.01
        return _Pos

    elseif iType == EClickType.NPCIcon or iType == EClickType.MineIcon then
        if _Pos.x * 100 + _width > this.m_RectTransMax.x then
            _Pos.x = _Pos.x - _width * 0.01
            if _Pos.y * 100 < - this.m_RectTransMax.y + _height then
                _Pos.y = _Pos.y + _height * 0.01
            end
        elseif _Pos.y * 100 < - this.m_RectTransMax.y + _height then
            _Pos.y = _Pos.y + _height * 0.01
        end
        return _Pos

    else
        if iPos.x > this.m_RectTransMax.x - _width then
            _Pos.x = iPos.x - _width
            if iPos.y < - this.m_RectTransMax.y + _height then
                _Pos.y = iPos.y + _height
            end
        elseif iPos.y < - this.m_RectTransMax.y + _height then
            _Pos.y = iPos.y + _height
        end
        return _Pos
    end
end

--自動移動
InteractiveEvent[1] = function()
    if this.m_NowSceneID == SceneMgr.GetSceneID() then
        MoveMgr.PlayerSelfPathFind(Vector3(this.m_InteractivePos.Move_x, 0, this.m_InteractivePos.Move_y))
    else
        SearchMgr.SearchNPCOnFoot(nil, this.m_NowSceneID, Vector3(this.m_InteractivePos.Move_x, 0, this.m_InteractivePos.Move_y ),false)
    end
    UIMgr.Close(Map_Controller)
end

--傳送
InteractiveEvent[2] = function()
    if this.m_CanTeleport then
        if this.m_EventID_Teleport == nil then
            MessageMgr.AddCenterMsg(false, TextData.Get(9220))
        else
            SendProtocol_004._025(this.m_TeleportType, this.m_EventID_Teleport)
            UIMgr.Close(Map_Controller)
        end
    else
        if MapMgr.CheckHaveMine(this.m_NowSceneID) then
            MessageMgr.AddCenterMsg(true, TextData.Get(20800002))
        else
            MessageMgr.AddCenterMsg(true, TextData.Get(20800003))
        end
    end
end

--集結標誌
InteractiveEvent[3] = function()
    this.m_NowPointType = EMapPointType.Comming
    MapMgr.SetMapPointer(this.m_InteractivePos, this.m_NowPointType)
end

--警示標誌
InteractiveEvent[4] = function()
    this.m_NowPointType = EMapPointType.Waring
    MapMgr.SetMapPointer(this.m_InteractivePos, this.m_NowPointType)
end

--礦脈雷達
InteractiveEvent[5] = function()
    UIMgr.Open(EnergyCore_Controller,EEnergyCorePageType.EnergyCore_MineRadar, this.m_MineID)
end

local Mark_Fix_PosY = 40
--地圖上顯示標誌
function Map_Controller.ShowMark(iData)
    this.m_Group_Interactive.gameObject:SetActive(false)
    this.m_Image_MarkIcon.gameObject:SetActive(true)
    local _IsSuccess, _Data = {}
    local _IconType = iData.m_Position.m_aKind == 1 and EMapIconsType.ComingPoint or EMapIconsType.WarningPoint
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(_IconType, _Data)
    this.m_Image_Mark.sprite = _Data.m_Sprite
    this.m_Image_MarkIcon.gameObject.transform.localPosition = Vector2(iData.m_Position.m_MarkPOSX, iData.m_Position.m_MarkPOSY + Mark_Fix_PosY)
    this.m_Image_MarkIcon:SetAsLastSibling()
    _isMark = true
end
--endregion
