---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright ? 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("UI/TimeLine/TimeLine_Model")

---場景動畫控制(跳過動畫與字幕)
---<AUTHOR>
---@version 1.0
---@since [ProjectBase] 0.1
---@date 2023.9.1
---@class TimeLine_Controller
TimeLine_Controller = {}
local this = TimeLine_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("TimeLine_View", "TimeLine_Controller", EUIOrderLayers.FullPage, true, "", true)

---等待下一個 事件來關閉
this.m_IsWaitNextEventClose = false

---下一個事件的 ID
this.m_NextEventID = 0

local NoEventClose = -1

---是否一鍵跳過視窗（金手指用）
this.m_IsSkipWindowOpen = false

--初始化全UI
local function InitialUI()
    this.m_Btn_Skip = this.m_ViewRef.m_Dic_Trans:Get("&Button_Skip")
	Button.AddListener(this.m_Btn_Skip, EventTriggerType.PointerClick, function()
        if not this.m_IsSkipWindowOpen then
            this.m_GObj_SkipWindow.gameObject:SetActive(true)
        else
            SceneMgr.SkipNowTimeLine()
        end

    end)
    ---遮蔽的黑圖
    this.m_GObj_Cover = this.m_ViewRef.m_Dic_Trans:Get("&Image_Cover").gameObject
    this.m_GObj_Cover:SetActive(false)
    -- 字幕組
    this.m_TextSubTitle = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TalkString")
    this.m_TextSubTitle.text = ""

    --region 跳過確認視窗
    ---視窗本人
    this.m_GObj_SkipWindow = this.m_ViewRef.m_Dic_Trans:Get("&GObj_SkipWindow")
    this.m_GObj_SkipWindow.gameObject:SetActive(false)
    ---視窗裡的確認按鈕
    this.m_Button_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm"))
    this.m_Button_Confirm:AddListener(EventTriggerType.PointerClick, function()
        -- 跳過本次動畫
        SceneMgr.SkipNowTimeLine()
    end)
    ---視窗裡的取消按鈕
    this.m_Button_Cancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Cancel"))
    this.m_Button_Cancel:AddListener(EventTriggerType.PointerClick, function()
        this.m_GObj_SkipWindow.gameObject:SetActive(false)
    end)
    --endregion
end

function TimeLine_Controller.Init()
    InitialUI()
end

---table參數 1.TalkID
---@param iTable_Param table
function TimeLine_Controller.Open(iParam)
    if not table.IsNullOrEmpty(iParam) then
        --設定 TimeLine ID 及 模式
        TimeLine_Model.m_TimeLine = table.remove(iParam, 1)
        TimeLine_Model.m_ESkipMode = table.remove(iParam, 1)
        this.m_NextEventID = table.remove(iParam, 1)

        if this.m_NextEventID == 0 then
            this.m_NextEventID = NoEventClose
        end

        --去掉三個之後還有東西的話 就是創角的 TimeLine
        local _IsNeedSendCloseToServer = iParam[3] == nil
        TimeLine_Model.m_IsNeedSendCloseToServer = _IsNeedSendCloseToServer and true or false
        if not table.IsNullOrEmpty(iParam) then
            --取得 ModelController
            iParam[1] = iParam[1] == nil and RoleMgr.m_RC_Player.m_ModelController or iParam[1]
        end
        ---已開啟的劇情動畫 UI
        UIMgr.m_OpenedPlotAnimationUI = this
        --走 TimeLine_Model
        TimeLine_Model.Play(unpack(iParam, 1, table.maxn(iParam)))
        return true
    end
    return false
end


function TimeLine_Controller.Close()
    if this.m_Name ~= nil and this.m_NextEventID ~= nil then
        D.Log(GString.GetTextWithColor("劇情動畫 UI["..this.m_Name.."] 已經由等待之事件ID ["..this.m_NextEventID.."] 關閉", Color.Gold))
    end
    if this.m_NextEventID == 0 then
        return
    end

    this.m_NextEventID = 0
    this.m_IsWaitNextEventClose = false
    coroutine.start(function()
        --延遲關閉 避免漏餡
        coroutine.wait(0.5)
        this.m_GObj_Cover:SetActive(false)
    end)
    UIMgr.m_OpenedPlotAnimationUI = nil
end

---設定遮蔽黑幕顯示
function TimeLine_Controller.SetCoverBlackActive(iIsActive)
    if UIMgr.GetUIStage(TimeLine_Controller) < EUIStage.Initialized then
        return
    end
    -- 是否需顯示關閉按鈕
    this.m_GObj_Cover:SetActive(iIsActive)
end

function TimeLine_Controller.SetButtonExitActive(iIsActive)
    -- 是否需顯示關閉按鈕
    this.m_Btn_Skip.gameObject:SetActive(iIsActive)
end

------關閉確認跳過視窗
function TimeLine_Controller.SetSkipWindowActive()
    if this.m_GObj_SkipWindow then
        this.m_GObj_SkipWindow.gameObject:SetActive(false)
    end
end

function TimeLine_Controller.OnDestroy()
    this.m_GObj_SkipWindow = nil
    return true
end