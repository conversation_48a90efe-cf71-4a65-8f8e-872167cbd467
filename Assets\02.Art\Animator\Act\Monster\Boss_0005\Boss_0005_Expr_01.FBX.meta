fileFormatVersion: 2
guid: 9d818454bc8c722479daaf1360ba2292
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Ankle_L
    100002: Ankle_R
    100004: //RootNode
    100006: Chest_M
    100008: Cup_L
    100010: Cup_R
    100012: DeformationSystem
    100014: Elbow_L
    100016: Elbow_R
    100018: ElbowPart1_L
    100020: ElbowPart1_R
    100022: Head_M
    100024: HeadEnd_M
    100026: Hip_L
    100028: Hip_R
    100030: IndexFinger1_L
    100032: IndexFinger1_R
    100034: IndexFinger2_L
    100036: IndexFinger2_R
    100038: IndexFinger3_L
    100040: IndexFinger3_R
    100042: Jaw_M
    100044: JawEnd_M
    100046: joint10_M
    100048: joint11_2_M
    100050: joint12_M
    100052: joint13_M
    100054: joint14_M
    100056: joint1_M
    100058: joint2_2_M
    100060: joint3_M
    100062: joint4_M
    100064: joint5_M
    100066: joint6_M
    100068: joint7_M
    100070: joint8_2_M
    100072: joint9_M
    100074: Knee_L
    100076: Knee_R
    100078: Main
    100080: MiddleFinger1_L
    100082: MiddleFinger1_R
    100084: MiddleFinger2_L
    100086: MiddleFinger2_R
    100088: MiddleFinger3_L
    100090: MiddleFinger3_R
    100092: Neck_M
    100094: PinkyFinger1_L
    100096: PinkyFinger1_R
    100098: PinkyFinger2_L
    100100: PinkyFinger2_R
    100102: PinkyFinger3_L
    100104: PinkyFinger3_R
    100106: RingFinger1_L
    100108: RingFinger1_R
    100110: RingFinger2_L
    100112: RingFinger2_R
    100114: RingFinger3_L
    100116: RingFinger3_R
    100118: Root_M
    100120: Scapula_L
    100122: Scapula_R
    100124: Shoulder_L
    100126: Shoulder_R
    100128: Spine1_M
    100130: ThumbFinger1_L
    100132: ThumbFinger1_R
    100134: ThumbFinger2_L
    100136: ThumbFinger2_R
    100138: ThumbFinger3_L
    100140: ThumbFinger3_R
    100142: Toes_L
    100144: Toes_R
    100146: Wrist_L
    100148: Wrist_R
    400000: Ankle_L
    400002: Ankle_R
    400004: //RootNode
    400006: Chest_M
    400008: Cup_L
    400010: Cup_R
    400012: DeformationSystem
    400014: Elbow_L
    400016: Elbow_R
    400018: ElbowPart1_L
    400020: ElbowPart1_R
    400022: Head_M
    400024: HeadEnd_M
    400026: Hip_L
    400028: Hip_R
    400030: IndexFinger1_L
    400032: IndexFinger1_R
    400034: IndexFinger2_L
    400036: IndexFinger2_R
    400038: IndexFinger3_L
    400040: IndexFinger3_R
    400042: Jaw_M
    400044: JawEnd_M
    400046: joint10_M
    400048: joint11_2_M
    400050: joint12_M
    400052: joint13_M
    400054: joint14_M
    400056: joint1_M
    400058: joint2_2_M
    400060: joint3_M
    400062: joint4_M
    400064: joint5_M
    400066: joint6_M
    400068: joint7_M
    400070: joint8_2_M
    400072: joint9_M
    400074: Knee_L
    400076: Knee_R
    400078: Main
    400080: MiddleFinger1_L
    400082: MiddleFinger1_R
    400084: MiddleFinger2_L
    400086: MiddleFinger2_R
    400088: MiddleFinger3_L
    400090: MiddleFinger3_R
    400092: Neck_M
    400094: PinkyFinger1_L
    400096: PinkyFinger1_R
    400098: PinkyFinger2_L
    400100: PinkyFinger2_R
    400102: PinkyFinger3_L
    400104: PinkyFinger3_R
    400106: RingFinger1_L
    400108: RingFinger1_R
    400110: RingFinger2_L
    400112: RingFinger2_R
    400114: RingFinger3_L
    400116: RingFinger3_R
    400118: Root_M
    400120: Scapula_L
    400122: Scapula_R
    400124: Shoulder_L
    400126: Shoulder_R
    400128: Spine1_M
    400130: ThumbFinger1_L
    400132: ThumbFinger1_R
    400134: ThumbFinger2_L
    400136: ThumbFinger2_R
    400138: ThumbFinger3_L
    400140: ThumbFinger3_R
    400142: Toes_L
    400144: Toes_R
    400146: Wrist_L
    400148: Wrist_R
    7400000: Boss_0005_Expr_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 91d1af04b6714d045b7bcd38d42ae31c,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
