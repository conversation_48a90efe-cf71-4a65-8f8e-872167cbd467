fileFormatVersion: 2
guid: 6be5bcf69f397d34d913b11e0c7e35fb
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001 Head
    100002: Bip001 Spine1
    100004: <PERSON>_Head_JawSHJnt
    100006: <PERSON>_Head_TopSHJnt
    100008: Deer_l_Clavicle_01_01SHJnt
    100010: Deer_l_Ear_01_01SHJnt
    100012: Deer_l_Ear_01_02SHJnt
    100014: Deer_l_FrontLeg_AnkleSHJnt
    100016: Deer_l_FrontLeg_BallSHJnt
    100018: Deer_l_FrontLeg_HipSHJnt
    100020: Deer_l_FrontLeg_Knee1SHJnt
    100022: Deer_l_FrontLeg_Knee2SHJnt
    100024: Deer_l_HindLeg_AnkleSHJnt
    100026: Deer_l_HindLeg_BallSHJnt
    100028: Deer_l_HindLeg_HipSHJnt
    100030: Deer_l_HindLeg_Knee1SHJnt
    100032: Deer_l_HindLeg_Knee2SHJnt
    100034: Deer_Neck_01SHJnt
    100036: <PERSON>_Neck_02SHJnt
    100038: Deer_r_Clavicle_01_01SHJnt
    100040: Deer_r_Ear_01_01SHJnt
    100042: Deer_r_Ear_01_02SHJnt
    100044: Deer_r_FrontLeg_AnkleSHJnt
    100046: Deer_r_FrontLeg_BallSHJnt
    100048: Deer_r_FrontLeg_HipSHJnt
    100050: Deer_r_FrontLeg_Knee1SHJnt
    100052: Deer_r_FrontLeg_Knee2SHJnt
    100054: Deer_r_HindLeg_AnkleSHJnt
    100056: Deer_r_HindLeg_BallSHJnt
    100058: Deer_r_HindLeg_HipSHJnt
    100060: Deer_r_HindLeg_Knee1SHJnt
    100062: Deer_r_HindLeg_Knee2SHJnt
    100064: Deer_ROOTSHJnt
    100066: Deer_Spine_01SHJnt
    100068: Deer_Spine_02SHJnt
    100070: Deer_Spine_03SHJnt
    100072: Deer_Spine_TopSHJnt
    100074: Deer_Tail_01_01SHJnt
    100076: Deer_Tail_01_02SHJnt
    100078: //RootNode
    400000: Bip001 Head
    400002: Bip001 Spine1
    400004: Deer_Head_JawSHJnt
    400006: Deer_Head_TopSHJnt
    400008: Deer_l_Clavicle_01_01SHJnt
    400010: Deer_l_Ear_01_01SHJnt
    400012: Deer_l_Ear_01_02SHJnt
    400014: Deer_l_FrontLeg_AnkleSHJnt
    400016: Deer_l_FrontLeg_BallSHJnt
    400018: Deer_l_FrontLeg_HipSHJnt
    400020: Deer_l_FrontLeg_Knee1SHJnt
    400022: Deer_l_FrontLeg_Knee2SHJnt
    400024: Deer_l_HindLeg_AnkleSHJnt
    400026: Deer_l_HindLeg_BallSHJnt
    400028: Deer_l_HindLeg_HipSHJnt
    400030: Deer_l_HindLeg_Knee1SHJnt
    400032: Deer_l_HindLeg_Knee2SHJnt
    400034: Deer_Neck_01SHJnt
    400036: Deer_Neck_02SHJnt
    400038: Deer_r_Clavicle_01_01SHJnt
    400040: Deer_r_Ear_01_01SHJnt
    400042: Deer_r_Ear_01_02SHJnt
    400044: Deer_r_FrontLeg_AnkleSHJnt
    400046: Deer_r_FrontLeg_BallSHJnt
    400048: Deer_r_FrontLeg_HipSHJnt
    400050: Deer_r_FrontLeg_Knee1SHJnt
    400052: Deer_r_FrontLeg_Knee2SHJnt
    400054: Deer_r_HindLeg_AnkleSHJnt
    400056: Deer_r_HindLeg_BallSHJnt
    400058: Deer_r_HindLeg_HipSHJnt
    400060: Deer_r_HindLeg_Knee1SHJnt
    400062: Deer_r_HindLeg_Knee2SHJnt
    400064: Deer_ROOTSHJnt
    400066: Deer_Spine_01SHJnt
    400068: Deer_Spine_02SHJnt
    400070: Deer_Spine_03SHJnt
    400072: Deer_Spine_TopSHJnt
    400074: Deer_Tail_01_01SHJnt
    400076: Deer_Tail_01_02SHJnt
    400078: //RootNode
    7400000: M_0701_Walk_00
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0701_Walk_00
      takeName: M_0701_Walk_00
      firstFrame: 0
      lastFrame: 28
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: a78e6dbf3a7232e4c91d4bb97a9b32d0,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
