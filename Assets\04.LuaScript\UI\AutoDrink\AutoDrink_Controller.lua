---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---自動喝水控制器
---@class AutoDrink_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 0.80
---date 2023.10.13
AutoDrink_Controller = {}
require("UI/AutoDrink/AutoDrink_Model")
local this = AutoDrink_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("AutoDrink_View", "AutoDrink_Controller", EUIOrderLayers.HalfPage_Right, true)

this.m_BagPotionData = {}

this.m_CurPotionType = EPotionType.HPPotion
this.m_ItemCountPerRow = 5

---自動喝水開啟字串idx
this.m_StartString = 1
---自動喝水關閉字串idx
this.m_CloseString = 2

---是否有改變喝水設定
this.m_isChangeValue = false
---暫存要存檔的資料
this.m_TempSaveData = {}

this.m_isInit = false
---Scroll View Y軸調整距離
local MovePos_Y = 85

---目前開啟的類別
this.m_OpenType = EPotionType.HPPotion

---Scrollview 想要顯示幾個橫排
this.m_ScrollviewRowCount = 3

---開關型按鍵等資料刷新時間
local _WaitDataTime = 0.01

---slider的文字說明字串ID 根據頁面不同有不同的說明文字
local m_SliderValueDescriptionTable ={
    [EPotionType.HPPotion] = 20203012,
    [EPotionType.MPPotion] = 20203022,
    [EPotionType.SPPotion] = 20203032,
}

---初始化
function AutoDrink_Controller.Init()
    AutoDrink_Model.Init()

    this.m_TMP_Title = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Title")
    this.m_TMP_Title.text = AutoDrink_Model.GetTitleStringByPotionType(EPotionType.HPPotion)

    this.m_Trans_AutoDrink = this.m_ViewRef.m_Dic_Trans:Get("&Trans_AutoDrink")
    this.m_Btn_CloseAD = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_CloseAutoDrink"))
    this.m_Btn_CloseAD:AddListener(EventTriggerType.PointerClick, function() UIMgr.Close(AutoDrink_Controller) end)
    
    this.m_Btn_Use = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Toggle_Use"))
    this.m_Btn_Use:AddListener(EventTriggerType.PointerClick,function()
        AutoDrink_Controller.OnClick_Use()
        AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Use,this.m_Btn_Use:IsSelect())
        
        HEMTimeMgr.DoFunctionDelay(_WaitDataTime, function()
            AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Use,this.m_Btn_Use:IsSelect())
        end )
        
    end)
    
    this.m_Icon_SelectItem = this.m_ViewRef.m_Dic_Trans:Get("&Icon_SelectItem")
    this.m_SelectItem = IconMgr.NewItemIcon(0, this.m_Icon_SelectItem, 110, nil, false)
    this.m_SelectItem:SetClickTwice(false)

    this.m_Slider_Percent = this.m_ViewRef.m_Dic_Slider:Get("&Slider_Percent")
    this.m_Slider_Percent.onValueChanged:AddListener(this.AutoDrinkValueChange)
    this.m_Text_Percent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Percent")
    this.m_Text_Percent.text = tostring(this.m_Slider_Percent.value).."%"
    ---Buff類藥水分頁專用text
    this.m_Text_BuffDescription = this.m_ViewRef.m_Dic_TMPText:Get("&Text_BuffDescription")

    this.m_Trans_PvP = this.m_ViewRef.m_Dic_Trans:Get("&Trans_PVP").gameObject
    this.m_Btn_Toggle_PvP = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Toggle_PVP"))
    this.m_Btn_Toggle_PvP:AddListener(EventTriggerType.PointerClick, function()
        AutoDrink_Controller.OnClick_SetIsPVP(not this.m_Btn_Toggle_PvP:IsSelect())

        HEMTimeMgr.DoFunctionDelay(_WaitDataTime, function()
            AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Toggle_PvP,this.m_Btn_Toggle_PvP:IsSelect())
        end )
    end)

    this.m_Btn_Toggle_AutoLow = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Toggle_AutoLow"))
    this.m_Btn_Toggle_AutoLow:AddListener(EventTriggerType.PointerClick, function()

        AutoDrink_Controller.OnClick_SetIsAutoLow(not this.m_Btn_Toggle_AutoLow:IsSelect())

        HEMTimeMgr.DoFunctionDelay(_WaitDataTime, function()
            AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Toggle_AutoLow,this.m_Btn_Toggle_AutoLow:IsSelect())
        end )

    end)

    this.m_Group_SetPotion = GroupButton.New(this.m_ViewRef.m_Dic_Trans:Get("&GroupTab_Potion"))
    for i = 1, this.m_Group_SetPotion.m_UIGroupBtnCtrl.ButtonCount do
		GroupButton.AddListenerByIndex(this.m_Group_SetPotion,i,EventTriggerType.PointerClick,function()
			AutoDrink_Controller.SwitchPotionPage(i)
		end)
	end

    ---有包含教學元件的Icon模板(用於複製使用) Modify by 凌傑RM#117450 2025.0103
    this.m_BagItemIcon = this.m_ViewRef.m_Dic_Trans:Get("&ItemIconAnimate")

    this.m_ScrollView_Potion = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Potion")
    this.m_ReuseItem = this.m_ViewRef.m_Dic_Trans:Get("&Item_Row").gameObject
    this.m_ScrollView = ScrollView.Init(this.m_ScrollView_Potion, true, this.m_ReuseItem,
        AutoDrink_Controller.GetBagPotionCount, AutoDrink_Controller.AfterReuseItemInit,
        AutoDrink_Controller.AfterReuseItemIndexUpdate, true)
    this.m_View_SizeDelta = Vector2(this.m_ScrollView_Potion.sizeDelta.x, this.m_ScrollView_Potion.sizeDelta.y)
    this.m_Adjust_View_SizeDelta = Vector2(this.m_ScrollView_Potion.sizeDelta.x, this.m_ScrollView_Potion.sizeDelta.y + MovePos_Y)
end

function AutoDrink_Controller.Open(iParam)
    local _Type = iParam[1] ~= nil and iParam[1] or EBaseValues.HP
    if AutoDrink_Controller.CheckType(_Type) then
        this.m_CurPotionType = _Type
        GStateObserverManager.Register(EStateObserver.BagDataRefresh, this)
    end

    if not this.m_isInit then
        this.m_View_Pos = this.m_ScrollView_Potion.gameObject.transform.localPosition
        this.m_Adjust_View_Pos = Vector3(this.m_View_Pos.x, this.m_View_Pos.y + MovePos_Y, 0)
        this.m_isInit = true
    end
    return true
end

function AutoDrink_Controller.ToDoAfterOpenUISucceed()
    GroupButton.OnPointerClickByIndex(this.m_Group_SetPotion, this.m_CurPotionType)
end

function AutoDrink_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.BagDataRefresh, this)
    if this.m_isChangeValue then
        AutoDrink_Model.SetSettingData(this.m_TempSaveData, true)
        this.m_isChangeValue = false
        this.m_TempSaveData = {}
    end
    for i = 1, table.Count(EPotionType) do
        AutoDrink_Model.AutoDrink(i)
    end
    IconMgr.CancelAllClick()
    UIMgr.OpenIconName(false)
    ---同步設定快捷區相關邏輯 UI顯示
    HotKey_Controller.CloseAutoDrinkCallBack()
    return true
end

function AutoDrink_Controller.OnDestroy()
    GStateObserverManager.UnRegister(EStateObserver.BagDataRefresh, this)
    return true
end

function AutoDrink_Controller.CheckType(iType)
    return iType >= EPotionType.HPPotion and iType <= EPotionType.DefPotion
end

---開啟或切換藥水頁面
function AutoDrink_Controller.SwitchPotionPage(iType)
    ---取得所有自動喝水設定
    local _AllAutoDrinkSetting = AutoDrink_Model.GetSettingData()
    ---取得當前頁面自動喝水設定
    local _AutoDrinkSetting = _AllAutoDrinkSetting.m_PortionGroup[_AllAutoDrinkSetting.m_CurIndex]
    this.m_CurPotionType = iType
    this.m_Btn_Use:SetSelect(_AutoDrinkSetting.m_IsOn[this.m_CurPotionType])
    this.m_Btn_Toggle_PvP:SetSelect(_AutoDrinkSetting.m_IsPVP)
    this.m_Btn_Toggle_AutoLow:SetSelect(_AutoDrinkSetting.m_IsAutoLow[this.m_CurPotionType])
    this.m_TMP_Title.text = AutoDrink_Model.GetTitleStringByPotionType(this.m_CurPotionType)

    AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Toggle_AutoLow,_AutoDrinkSetting.m_IsAutoLow[this.m_CurPotionType])
    AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Toggle_PvP,_AutoDrinkSetting.m_IsPVP)
    AutoDrink_Controller.ChangleUseIndexByBtnStatus(this.m_Btn_Use,_AutoDrinkSetting.m_IsOn[this.m_CurPotionType])

    ---根據是否為buff 類型的藥水來決定是否顯示百分比
    if this.m_CurPotionType == EPotionType.AtkPotion or this.m_CurPotionType == EPotionType.DefPotion then
        this.m_Slider_Percent.gameObject:SetActive(false)
        this.m_Text_Percent.gameObject:SetActive(false)
        this.m_Text_BuffDescription.gameObject:SetActive(true)

    else
        this.m_Slider_Percent.gameObject:SetActive(true)
        this.m_Text_Percent.gameObject:SetActive(true)
        this.m_Text_BuffDescription.gameObject:SetActive(false)

        this.m_Slider_Percent:SetValueWithoutNotify(tonumber(_AutoDrinkSetting.m_Value[this.m_CurPotionType]))
        AutoDrink_Controller.SetSliderText(iType)
    end

    this.m_Trans_PvP:SetActive(iType == EBaseValues.HP)

    this.m_BagPotionData = AutoDrink_Model.GetBagPotionData(this.m_CurPotionType,false)

    --塞假資料(Id=0)一直到有 排數*每排 到15(3*5)個為止
    local _LackAmount = this.m_ScrollviewRowCount*this.m_ItemCountPerRow - table.Count(this.m_BagPotionData)
    for i = 1, _LackAmount do
        table.insert(this.m_BagPotionData,0)
    end


    AutoDrink_Controller.AdjustViewPos(iType)
    local _ItemData = BagMgr.GetFirstSaveItemDataByItemIdx(_AutoDrinkSetting.m_ItemIdx[this.m_CurPotionType],{_AutoDrinkSetting.m_ItemState[this.m_CurPotionType]})
    if _ItemData ~= nil then
        AutoDrink_Controller.SetSelectItemIcon(this.m_CurPotionType, _ItemData)
    else
        this.m_SelectItem:RefreshIcon(0)
    end

    ScrollView.Update(this.m_ScrollView)

    IconMgr.CancelAllClick()
    UIMgr.OpenIconName(false)

    ---因為Icon刷新後 會將選擇提示關閉 所以必須幫他開啟
    HotKey_Controller.RenewPotionHotKeyOutLineHint(iType)
end

---調整ScrollView位置
function AutoDrink_Controller.AdjustViewPos(iType)
    ---新的UI規劃不需要調整scrollveiew位置 code 註解保留
    --[[
    if iType == EBaseValues.HP then
        this.m_ScrollView_Potion.transform.localPosition = this.m_View_Pos
        this.m_ScrollView_Potion.sizeDelta = this.m_View_SizeDelta
    else
        this.m_ScrollView_Potion.transform.localPosition = this.m_Adjust_View_Pos
        this.m_ScrollView_Potion.sizeDelta = this.m_Adjust_View_SizeDelta
    end
    ]]
end

---點擊開啟或關閉自動喝水
function AutoDrink_Controller.OnClick_Use()
    local _SelectPotion = AutoDrink_Model.GetSelectPotion(this.m_CurPotionType)
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_IsOn[this.m_CurPotionType] = not _AutoDrinkSetting.m_IsOn[this.m_CurPotionType]

    
    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_IsOn[this.m_CurPotionType] = not _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_IsOn[this.m_CurPotionType]
  
    AutoDrink_Model.SetSettingData(_AutoDrinkSetting, true)
    local _count = _SelectPotion ~= nil and _SelectPotion.m_Count or 0
    Main_SubCtrl_AutoDrink.SetPotionIcon(this.m_CurPotionType, _count)
    HotKey_Controller.RenewPotionHotKey()
    HotKey_Controller.RenewPotionHotKeyOutLineHint(this.m_CurPotionType)
end

---改變自動喝水判斷數值 只對HP MP SP類型的藥水有效
function AutoDrink_Controller.AutoDrinkValueChange()
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_Value[this.m_CurPotionType] = this.m_Slider_Percent.value
    AutoDrink_Controller.SetSliderText(this.m_CurPotionType)
    this.m_isChangeValue = true

    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_Value[this.m_CurPotionType] = this.m_Slider_Percent.value

    this.m_TempSaveData =_AutoDrinkSetting
end

---設定是否血量低於100%就自動喝水
function AutoDrink_Controller.OnClick_SetIsPVP(iIsOn)
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_IsPVP = iIsOn
    
    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_IsPVP = iIsOn

    AutoDrink_Model.SetSettingData(_AutoDrinkSetting, false)
end

---設定是否自動使用低階物品
function AutoDrink_Controller.OnClick_SetIsAutoLow(iIsOn)
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_IsAutoLow[this.m_CurPotionType] = iIsOn
    
    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_IsAutoLow[this.m_CurPotionType] = iIsOn
    AutoDrink_Model.SetSettingData(_AutoDrinkSetting, false)
end

function AutoDrink_Controller.GetBagPotionCount()
    this.m_BagPotionCount = table.Count(this.m_BagPotionData)
    local _MaxCountInScrollView = Mathf.Ceil(this.m_BagPotionCount / this.m_ItemCountPerRow)
    return _MaxCountInScrollView
end

function AutoDrink_Controller.AfterReuseItemInit(iItem, iRowIdx)
    if iItem ~= nil then
        local _TItemIcons = {}
        for i = 1, this.m_ItemCountPerRow do

            ---Icon物件改成複製有包含教學元件的Icon Modify by 凌傑RM#117450 2025.0103
            local _ItemIconAnimate = this.m_BagItemIcon:Instantiate(iItem.m_GObj.transform)

            ---設定有教學元件動態的父物件
            local _Parent = _ItemIconAnimate:Find("IconAnimFrame")

            local _DataIdx = (iRowIdx - 1) * this.m_ItemCountPerRow + i
            
            local function SetDrinkItem(iDataIndex)
                local _HasData, _SaveData = AutoDrink_Controller.GetItemData(_DataIdx)
                if _HasData then
                    AutoDrink_Controller.SetSelectItem(_SaveData)
                    AutoDrink_Model.AutoDrink(this.m_CurPotionType)
                    ---確保icon的hover功能可運作
                    iItem.m_Icons[i]:ShowSelect(false)
                end
            end
            _TItemIcons[i] = IconMgr.NewItemIcon(0, _Parent, 110, SetDrinkItem, _DataIdx)
            _TItemIcons[i].m_Parent = _Parent --設定有教學元件的父物件
            _ItemIconAnimate.gameObject:SetActive(true) --設定複製的物件開啟
        end
        iItem.m_Icons = _TItemIcons
    end
end

function AutoDrink_Controller.AfterReuseItemIndexUpdate(iItem, iRowIdx)
    if iItem ~= nil then
        local _ChildIconHasData = 0

        for i = 1, this.m_ItemCountPerRow do
            local _DataIdx = (iRowIdx - 1) * this.m_ItemCountPerRow + i
            local _HasData, _SaveData = AutoDrink_Controller.GetItemData(_DataIdx)
            if iRowIdx <= this.m_ScrollviewRowCount then
                iItem.m_Icons[i].gameObject:SetActive(true)
            else
                iItem.m_Icons[i].gameObject:SetActive(_HasData)
            end
            
            if _HasData then
                iItem.m_Icons[i]:RefreshIcon(_SaveData)
                iItem.m_Icons[i]:SetClickTwice(true, IconSetting.m_SelectTextId[IconSetting.ETwoStepTextId.Set])
                _ChildIconHasData = _ChildIconHasData + 1
            else
                iItem.m_Icons[i]:RefreshIcon(0)
            end
        end
        
        ---原本要顯示三排的做法 改成塞假資料 舊方法先駐解保留
        --[[
        --最少顯示三行
        if iRowIdx <= this.m_ScrollviewRowCount or _ChildIconHasData > 0 then
            iItem.m_GObj:SetActive(true)
        else
            iItem.m_GObj:SetActive(false)
        end
        ]]
    end
end

function AutoDrink_Controller.GetItemData(iDataIndex)
    return this.m_BagPotionData[iDataIndex] ~= nil, this.m_BagPotionData[iDataIndex]
end

---設定使用藥水物品
function AutoDrink_Controller.SetSelectItem(iItemData)
    AutoDrink_Controller.SetSelectItemIcon(this.m_CurPotionType, iItemData)
    local _SelectPotion = AutoDrink_Model.GetSelectPotion(this.m_CurPotionType)
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_ItemIdx[this.m_CurPotionType] = _SelectPotion.m_ItemIdx
    _AutoDrinkSetting.m_ItemState[this.m_CurPotionType] = _SelectPotion.m_ItemState

    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_ItemIdx[this.m_CurPotionType] = _SelectPotion.m_ItemIdx
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_ItemState[this.m_CurPotionType] = _SelectPotion.m_ItemState

    AutoDrink_Model.SetSettingData(_AutoDrinkSetting, true)

    HotKey_Controller.RenewPotionHotKey()
end

---設定使用藥水Icon
function AutoDrink_Controller.SetSelectItemIcon(iType, iItemData)
    local _ItemCount = 0
    AutoDrink_Model.SetSelectPotion(iType, iItemData)
    
    if iItemData ~= nil then
        local _ChkState = rawget(EItemStatus,iItemData.m_ItemState)
        _ItemCount = BagMgr.GetItemInBagAmount(iItemData.m_ItemIdx,{_ChkState})
    end

    if this.m_Trans_AutoDrink and this.m_Trans_AutoDrink.gameObject.activeSelf and this.m_CurPotionType == iType then
        this.m_SelectItem:RefreshIcon(iItemData)
        if iItemData ~= 0 then
            this.m_SelectItem:SetCount(_ItemCount)
        end
    end
    
    Main_SubCtrl_AutoDrink.SetPotionIcon(iType, _ItemCount)
end

---狀態改變通知
function AutoDrink_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.BagDataRefresh then
        this.m_BagPotionData = AutoDrink_Model.GetBagPotionData(this.m_CurPotionType,false)
        ScrollView.Update(this.m_ScrollView)
    elseif iState == EStateObserver.BaseValueRefresh then
        local _arg = {...}
        local _Type = _arg[1]
        AutoDrink_Model.AutoDrink(_Type)
    end
end

---依照基礎屬性類型 清空相對應的選擇物品欄位
---@param iType EBaseValues 基礎屬性類型
function AutoDrink_Controller.SetSelectItemEmpty(iType)
    Main_SubCtrl_AutoDrink.SetPotionIcon(iType, 0)
    AutoDrink_Controller.SetSelectItemIcon(iType, nil)
    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()
    _AutoDrinkSetting.m_ItemIdx[iType] = 0

    local _CurPageIndex = _AutoDrinkSetting.m_CurIndex
    _AutoDrinkSetting.m_PortionGroup[_CurPageIndex].m_ItemIdx[iType] = 0

    AutoDrink_Model.SetSettingData(_AutoDrinkSetting, true)
end

---需要Slider的那幾個頁面 需要設定slider說明文字
---@param iEPotionType EPotionType 藥水類別型態
function AutoDrink_Controller.SetSliderText(iEPotionType)

    local _SliderStringID = m_SliderValueDescriptionTable[iEPotionType] 
    if _SliderStringID ~= nil then
        this.m_Text_Percent.text = GString.Format(TextData.Get(_SliderStringID), this.m_Slider_Percent.value)
    end
end

---自動喝水介面 toggle(開關型按鍵) 根據是否為Select 決定使用群組
---@param iBtn Button 開關型按鍵(toggle)本體
---@param iUseSelectIndex bool 是否使用選擇選擇中index
function AutoDrink_Controller.ChangleUseIndexByBtnStatus(iBtn,iUseSelectIndex)
    --- 調整ButtonEx 的 m_UnitRenderInfoGroupIndex  讓對應物件以及其子物件的群駔index為 1或0 的設定
    --- 1 > 開關型按鍵設定為開啟 0 > 開關型按鍵設定為關閉
    local _UseIndex = iUseSelectIndex and 1 or 0
    iBtn:ChangeStateTransitionGroup(_UseIndex)
end

--region 新增自動喝水教學 Modify by 凌傑RM#117450 2025.0103
---教學開啟介面AutoDrink_Controller使用
---@param iItemID 物品ID
function AutoDrink_Controller.GetIconInAutoDrink(iItemID)

    ---取得在藥水第一筆在哪一頁哪一個位置
    ---@param iItemID 物品ID
    ---@return PotionType | 藥水類型, Index | 索引位置
    local function _GetFirstItemPotionTypeAndIndexInPotionData(iItemID)
        for key, value in pairs( this.m_BagPotionData ) do
            if value.m_ItemIdx == iItemID then
                return this.m_CurPotionType, key
            end
        end

        return this.m_CurPotionType, nil
    end

    ---取得頁數, 藥水的Index
    local _PageIndex, _IndexInSortBag = _GetFirstItemPotionTypeAndIndexInPotionData(iItemID)

    if _PageIndex ~= nil and _IndexInSortBag ~= nil then

        ---取得目標的行數
        local _CurrentTable = ScrollView.GetReuseItemByIndex(AutoDrink_Controller.m_ScrollView, math.ceil(_IndexInSortBag / AutoDrink_Controller.m_ItemCountPerRow)).m_Icons

        --檢查及回傳目標物件
        for key, value in pairs(_CurrentTable) do
            if value.m_Idx == iItemID then
                return value
            end
        end
    else
        return nil
    end
end
--endregion
