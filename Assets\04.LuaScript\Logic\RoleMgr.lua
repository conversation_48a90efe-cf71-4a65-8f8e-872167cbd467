---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("Controller/RoleController")
require("Logic/TeammateMgr")
require("Logic/Friend/FriendMgr")

---@class 角色生成與釋放
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.4.8
---class RoleMgr
RoleMgr = {}
local this = RoleMgr

---此玩家的 RoleController
---@type RoleController
this.m_RC_Player = nil

---@class Transform 角色父物件
this.m_RoleParent = nil

---@type ChineseGamer.Utilities.ObjectPool<GameObject> 角色物件池
this.m_GObjPool_Role = {}

---@type Vector3 玩家當前位置
this.m_currentVelocity = Vector3.zero

---場上活著的人 ( 勿直接取資料，請用 RoleMgr.Get(iRoleIdx) )
---@type table<string, RoleController> tostring(角色UID), 控制器
this.m_Table_ActiveRoles = {}

---存起來的登入角色( 只有玩家登入過的角色會存在這 )
this.m_Table_StoreSelfPlayer = {}

---存玩家們的頭像資料
---@type table<string, HeadData> tostring(角色UID), 頭像資料
this.m_PlayerHeadData = {}

---更新或新增玩家們的頭像資料
function RoleMgr.UpdateOrNewHeadData(iPlayerId, iHeadId, iHeadFrameId, iOnlyUpdateData)
	if this.m_PlayerHeadData[tostring(iPlayerId)] == nil then
		this.m_PlayerHeadData[tostring(iPlayerId)] = {}
	end

	this.m_PlayerHeadData[tostring(iPlayerId)].m_HeadId = iHeadId == nil and this.m_PlayerHeadData[tostring(iPlayerId)].m_HeadId or iHeadId
	this.m_PlayerHeadData[tostring(iPlayerId)].m_HeadFrameId = iHeadFrameId == nil and this.m_PlayerHeadData[tostring(iPlayerId)].m_HeadFrameId or iHeadFrameId

    if iOnlyUpdateData == nil or iOnlyUpdateData == false then
        IconMgr.UpdateHeadIcon(iPlayerId)
    end
end

local Role_ROOT_NAME = "Role_Root"
function RoleMgr.Init()
    this.m_RoleParent = GameObject.New(Role_ROOT_NAME).transform
    this.m_GObjPool_Role[EPlayerGender.Man] = Extension.GetGameObjPool(100,0,RoleMgr.RC_Reset,RoleMgr.RC_Init)
    this.m_GObjPool_Role[EPlayerGender.Woman] = Extension.GetGameObjPool(100,0,RoleMgr.RC_Reset,RoleMgr.RC_Init)
    --SelectMgr.SetOnTargetChanged(RoleController.SetSelectHUDActive)

    SelectMgr.SetOnTargetClicked(
            SelectMgr.ETargetType.Player,
            function(iGObj)
                RoleMgr.OnClick(iGObj)
            end
    )
end

---@param iRoleIdx number
function RoleMgr.CreatePlayer(iRoleIdx, iCreateRoleData)
    local _RC
    ---含狀態機已經完全產出完畢
    local _doAfterCreateDone = function(iRC)
        this.m_Table_ActiveRoles[tostring(iRoleIdx)] = iRC
        if this.m_RC_Player == nil and tostring(iRoleIdx) == tostring(PlayerData.GetRoleID()) then
            this.m_RC_Player = this.m_Table_ActiveRoles[tostring(iRoleIdx)]
        end
        if tostring(iRoleIdx) == tostring(PlayerData.GetRoleID()) then
            CameraMgr.InitCinemachine()
            CameraMgr.SetMainTarget(this.m_RC_Player.transform)
            --2 = ECMIndex.PlayerEffectCamera
            CameraMgr.SetParent(2, this.m_RC_Player.m_ModelObject.transform)

        end
    end

    --從pool取出來
    local _GameObj = this.m_GObjPool_Role[iCreateRoleData.m_AppearData.m_Gender]:Get()
    _GameObj.name = tostring(iRoleIdx)
    _GameObj.layer = Layer.Player
    _GameObj.tag = "Player"
    _GameObj:SetActive(true)

    --如果場景有自己的角色，把它砍了做一個新的
    if this.m_Table_ActiveRoles[tostring(iRoleIdx)] and tostring(iRoleIdx) == tostring(PlayerData.GetRoleID()) then
        RoleMgr.Release(PlayerData.GetRoleID())
        this.m_RC_Player = nil
    end

    ---RoleController
    _RC = RoleController.New(_GameObj, iCreateRoleData, _doAfterCreateDone)
    -- end

    --有寵物的話要生一隻
    if iCreateRoleData.m_PetID and iCreateRoleData.m_PetID ~= 0 then
        local _PetsData = {}
        _PetsData.m_PetID = iCreateRoleData.m_PetID
        _PetsData.m_EnvolveLv = iCreateRoleData.m_EnvolveLv
        _PetsData.m_PetName = iCreateRoleData.m_PetName
        _PetsData.m_SID = tostring(iRoleIdx)

        PetMgr.NewPet(_PetsData)
    end

    --_GameObj = nil
end

---@type table<number, RoleController>
RoleMgr.m_ActiveCullingTable = {}

function RoleMgr.Update()
    for k, v in pairs(this.m_Table_ActiveRoles) do
        v:Update()
    end

    BattleText_Controller.Update()
    AlertRangeMgr:Update()
    GhostMgr.Update()
end

function RoleMgr.LateUpdate()
    -- CullingGroupEvent 變動後更新
    for k, v in pairs(this.m_ActiveCullingTable) do
        if v:CullingUpdate() then
            this.m_ActiveCullingTable[k] = nil
        end
    end

    for k, v in pairs(this.m_Table_ActiveRoles) do
        v:LateUpdate()
    end
end

function RoleMgr.RC_Init(iGObj)
    if iGObj~=nil then
        local _Trans = iGObj:GetComponent("Transform")
        _Trans:SetParent(this.m_RoleParent)
        _Trans.localPosition = Vector3.zero
        _Trans.localScale = Vector3.one

        Extension.AddMissingComponent(iGObj, typeof(CharacterController))
    end
end

function RoleMgr.RC_Reset(iGObj)
    if iGObj ~= nil then
        iGObj:SetActive(false)
        iGObj.name = "Stored Role"
    end
end

function RoleMgr.SetRolePosition(iRoleIdx,iVec3)
    local _RoleData = RoleMgr.Get(iRoleIdx)
    if _RoleData~=nil then
        _RoleData:SetPosition(iVec3)
    else
        D.LogError("找不到RC iRoleIdx: " .. tostring(iRoleIdx))
    end
end

function RoleMgr.SetRoleRotation(iRoleIdx, iValue)
    local _RoleData = RoleMgr.Get(iRoleIdx)
    if _RoleData ~= nil then
        _RoleData:SetRotation(iValue)
    else
        D.LogError("找不到RC iRoleIdx: " .. tostring(iRoleIdx))
    end
end

---@param iRoleID number 是否開啟PK
---@param iPKSwitchStatus EPKSwichStatus PK開關狀態
function RoleMgr.SetPKInfo(iRoleID, iPKSwitchStatus)
    local _RC = RoleMgr.Get(iRoleID)

    if _RC then
        _RC.m_PKInfo.m_PKEnable = iPKSwitchStatus == EPKSwichStatus.Close and 0 or 1
        if iRoleID == PlayerData.GetCardID() then
            RoleMgr.m_PKEnable = _RC.m_PKInfo.m_PKEnable -- 更新
            Main_SubCtrl_RoleAttribute.UpdatePKInfo(iPKSwitchStatus, true)
            --自己的變動要刷所有人
            for k,v in pairs (this.m_Table_ActiveRoles) do
                v:RefreshRelationShip()
            end
        else
            _RC:RefreshRelationByType(ERelatType.Enemy)
            _RC:UpdateHUDRelationship()
        end
    end
end

--region 裝備物品相關
local function SetWeaponAndBody(iRC, iChangePos, iChangeItemData,  iChangeGrowTime, iChangeRarity)
    if iChangePos == 1 then

        local _WeaponType = EWeaponType.Fist
        if iChangeItemData then
            _WeaponType = iChangeItemData.m_EquipPosition
        end
        if iRC == RoleMgr.m_RC_Player then
            PlayerData.m_LoginPlayerData.m_WeaponType = _WeaponType
        end
        iRC:UpdateEnhance(_WeaponType, iChangeGrowTime, iChangeRarity)
        --暫時只有停止的功能
        iRC:UpdateWeapon(_WeaponType)
        AppearanceMgr.UpdateModelAppearance(iRC.m_PlayerID, nil, _WeaponType)

    elseif Equipment_Model.CheckIsBody(iChangePos) then
        --暫時只有停止的功能
        iRC:UpdateBody()
        AppearanceMgr.UpdateModelAppearance(iRC.m_PlayerID, nil)
    end

end

function RoleMgr.SetEquipData(iPlayerID, iChangePos, iChangeItemData, iChangeGrowTime)
    local _RC = RoleMgr.Get(iPlayerID)
    if _RC == nil then
        return
    end
    --在這裡分武器種類
    iChangePos = Equipment_Model.CheckIsWeapon(iChangePos) and 1 or iChangePos
    local _rarity = 0
    local _Cosmetics = _RC.AppearData.m_Cosmetics
    --_Cosmetics[ItemEquipPos.Fist] = 0
    if iChangeItemData then
        _Cosmetics[iChangePos] = iChangeItemData.m_ItemIdx and iChangeItemData.m_ItemIdx or iChangeItemData.m_Idx
    else
        _Cosmetics[iChangePos] = 0
    end

    if iChangeItemData then
        if iChangeItemData.m_ItemData then
            _rarity = iChangeItemData.m_ItemData.m_Rarity
        elseif iChangeItemData.m_Rarity then
            _rarity = iChangeItemData.m_Rarity
        end
    end
    --RoleMgr.m_RC_Player.m_IsModelUpdateComplete = false
    SetWeaponAndBody(_RC, iChangePos, iChangeItemData,
                iChangeGrowTime, _rarity)

end
--endregion

---其他玩家離開場景
function RoleMgr.LeaveScene(iRoleIdx, iLeaveType)
    local _RC = RoleMgr.Get(iRoleIdx)

    ---是否要清除選取 選取物件為離場玩家要清
    local _isClearTarget = SelectMgr.m_TargetController == _RC

    --選取物件滿足上條件，並交流選單顯示中、並交流中ID為離場玩家
    if _isClearTarget and
       UIMgr.IsVisible(Interact_Controller) and
       Interact_Model.m_OpenInteractPanelFrom == EOpenInteractPanelFrom.FromTargetInfo and
       tostring(Interact_Model.m_InteractRoleID) == tostring(iRoleIdx) then

        _isClearTarget = false
    end

    if _isClearTarget then
        SelectMgr.ClearSelect()
    end

    --離開方式為2，並且沒有被culling隱藏
    if _RC and iLeaveType == ETeleportInType.MapTeleport and _RC.m_CurrentCulling ~= nil and _RC.m_CurrentCulling < ECullingState.OUT_OF_RANGE then
        RoleMgr.TeleportLeave(iRoleIdx, function()
            --離場又進場?
            if _RC.m_IsFirstShowType == ETeleportInType.MapTeleport then
                --可能會沒有這個，傳送之後就登出了吧?
                if _RC.m_TeleportPos == nil then
                    _RC.m_IsFirstShowType = nil
                    _RC.m_TeleportPos = nil
                    this.Release(iRoleIdx)
                    return
                end
                RoleMgr.Teleport(iRoleIdx, _RC.m_TeleportPos.x, _RC.m_TeleportPos.y)
                _RC.m_IsFirstShowType = nil
                _RC.m_TeleportPos = nil
            else
                this.Release(iRoleIdx)
            end
        end)
    else
        this.Release(iRoleIdx)
    end

end

function RoleMgr.Release(iRoleIdx)
    local _RC = RoleMgr.Get(iRoleIdx)
    if _RC ~= nil then
        if not _RC.m_IsSelf then
            -- 釋放CullingSphere
            CullingGroupMgr.Inst:ReleaseBounding(_RC.m_BoundingSphereIdx)
            -- 釋放地圖Icon
            MapMgr.RemoveCharacter(iRoleIdx, MapMgr.ECharacterType.Player)

        end
        _RC:IsRelease()

        this.m_GObjPool_Role[_RC.AppearData.m_Gender]:Store(_RC.gameObject)

        this.m_Table_ActiveRoles[tostring(iRoleIdx)] = nil

        PetMgr.RemoveActivePet(iRoleIdx)
    end
end

function RoleMgr.Release_All(iNeedReleaseSelf)
    for k, v in pairs(this.m_Table_ActiveRoles) do
        if iNeedReleaseSelf then
            RoleMgr.Release(v.m_SID)
        else
            if v.m_SID ~= PlayerData.GetRoleID() then
                RoleMgr.Release(v.m_SID)
            end
        end
    end
end

function RoleMgr.OnClick(iGObj)
    local _RC = RoleMgr.GetPlayerControllerGObj(iGObj)
    if _RC then
        _RC:OnClick()
    end
end

function RoleMgr.Get(iRoleIdx)
    if this.m_Table_ActiveRoles[tostring(iRoleIdx)] then
        return this.m_Table_ActiveRoles[tostring(iRoleIdx)]
    else
        --D.LogError("Can't Get Player: " .. tostring(iRoleIdx) .. " " .. debug.traceback())
    end
end

---取得玩家 RC
---@return RoleController
function RoleMgr.GetPlayerRC()
    local _RC = this.m_Table_ActiveRoles[tostring(PlayerData.GetRoleID())]
    return _RC
end

---直接用場景物件找到玩家的RoleController
function RoleMgr.GetPlayerControllerGObj(iGObj)
    for key, value in pairs(this.m_Table_ActiveRoles) do
        if value.gameObject == iGObj then
            return value
        end
    end
end

---計算是否在可視範圍內, 並取得距離
---@return boolean, number 是否在範圍內, 距離多少
function RoleMgr.GetIsInVisibleRange(iVec3, iRange)
	local _RC = this.m_RC_Player
	if _RC ~= nil then
		local _PlayerPos = this.m_currentVelocity:GetTransformPosition(_RC.transform)
		if BattleMgr.m_NowSkillMove and _RC.m_StateController:GetStateType() == EStateType.Atk then
			_PlayerPos = BattleMgr.m_NowSkillMove
		end
		return GFunction.IsInRange_2D(_PlayerPos, iVec3, iRange)
	else
		return false
	end
end

function RoleMgr.StopMove()
    if this.m_RC_Player ~= nil then
        if this.m_RC_Player.m_StateController:GetStateType() == EStateType.Move then
            MoveMgr.PlayerStopFirst(true)
        end
    end
end

---玩家是否正在操作蘑菇頭
function RoleMgr.IsJoystickOnDrag()
    if UIMgr.IsVisible(Main_Controller) then
        return Main_SubCtrl_Joystick.m_IsOnDrag
    else
        return false
    end
end

---點蘑菇頭位移
function RoleMgr.JoystickDrag(iDragValue)
    if this.m_RC_Player == nil then
        return
    end


    --取得向前攝影機方向(旋轉矩陣去算)
    --local _Forward = Quaternion.LookRotation(Camera.main.transform.forward, Vector3.up)
    --取得後與拖曳方向向量相乘得到正確移動方向
    --local _Dir = Vector3.New(iDragValue.x,0,iDragValue.y):Mul(_Forward):SetNormalize()

    --用歐拉角和四元數計算
    local _tempVec = Vector3.New(iDragValue.x , 0 , iDragValue.y):SetNormalize()
    local _Dir= (Quaternion.Euler(0, Camera.main.transform.eulerAngles.y, 0)) *  _tempVec

    --兩個方法取出來的方向皆是正確的，之後可以再測試看哪個耗能比較小
    MoveMgr.MoveToDirection(_Dir)

end

function RoleMgr.GetSelfPlayerPos()
    if this.m_RC_Player == nil then
        return Vector3.zero
    end
    return this.m_RC_Player.transform.localPosition
end

--region 移動相關

--- 玩家移動 5-1 接進來
---@param iRoleID number 玩家ID
---@param iStartPos Vector2 起點
---@param iTargetPos Vector2 終點
---@param iSpeed number 移動速度
function RoleMgr.PlayerMoveToTarget(iRoleID, iStartPos, iTargetPos, iSpeed)
    local _Player = RoleMgr.Get(iRoleID)
    if _Player ~= nil then
        if iRoleID == PlayerData.GetRoleID() then -- 玩家自己
            this.m_RC_Player.m_MoveController:UpdateMoveSpeed(iSpeed)
            MoveMgr.ChangeMoveStart(iTargetPos, false)
        else

            --改成方向讓他超延伸
            _Player:OtherVectorMove(iTargetPos, iSpeed)

            -- 原本是戰鬥狀態，移動後要強制清除特效 & 音效
            if _Player.m_StateController:GetStateType() == EStateType.Atk then
                _Player:ReturnSkillEffect()
            end
        end
        PetMgr.SetPetMovement(iRoleID, iStartPos, iTargetPos, iSpeed)
    end

end

---玩家變動移動速度
function RoleMgr.PlayerUpdateSpeed(iRoleID, iSpeed)
    local _RC = RoleMgr.Get(iRoleID)
    if _RC then
        _RC:UpdateMoveSpeed(iSpeed)

        if _RC.m_IsSelf then
            SearchMgr.RefreshTable(ESearchKind.EnemyNPC)
            SearchMgr.RefreshTable(ESearchKind.EnemyPlayer)
        end
    end

    PetMgr.SyncPetSpeed(iRoleID, iSpeed)
end

--- 協定 5-2 通知玩家停止移動
---@param iEndPosition Vector2 最終位置
function RoleMgr.PlayerEndMove(iRoleID, iEndPosition, iReason)
    --玩家自身處理
    local _Player = RoleMgr.Get(iRoleID)

    if _Player == nil then
        return
    end

    if iRoleID == PlayerData.GetRoleID() then
        MoveMgr.PlayerSelfStop(iReason, iEndPosition)

        --this.m_RC_Player.m_MoveController:ChangeMoveStart(iEndPosition, true)
        --刷新攻擊清單
        SearchMgr.RefreshTable(ESearchKind.EnemyNPC)
        SearchMgr.RefreshTable(ESearchKind.EnemyPlayer)

    else
        --目前無作用
        --_Player:EndMove(iEndPosition, iReason)
	--跟NPC一樣快步到達指定座標
        _Player:MoveToTargetPos(nil,iEndPosition,42)

    end
    PetMgr.SendPetMovement(iRoleID, iEndPosition)
    --if( reason == ESEndMoveReason.Forcibly )
    -- {
        --     ReleaseMoveCtrl();
        -- }
end


---傳送功能兼表現
function RoleMgr.Teleport(iRoleID, iSPosX, iSPosZ, iPlayerFace, iCamFace, iModelController)
    local _Player = RoleMgr.Get(iRoleID)
    iPlayerFace = iPlayerFace and iPlayerFace or 0
    iCamFace = iCamFace and iCamFace or 0
    iModelController = iModelController and iModelController or _Player:GetModelController()

    if _Player ~= nil then
        local _Pos =  GFunction.ServerPosToScenePos( iSPosX, iSPosZ )
        if iRoleID == PlayerData.GetRoleID() then
            this.m_RC_Player.m_MoveController:StopMove(true)
        else
            _Player.m_MoveController:StopMove(true)
        end
        _Player:SetPosition(_Pos)

        if iPlayerFace ~= 0 then
            RoleMgr.SetRoleRotation(iRoleID, iPlayerFace, 0)
        end

        if iCamFace > 0 and iRoleID == PlayerData.GetRoleID() then
            CameraMgr.GetCMMgr():SetSmoothLook(iCamFace ,false)
        end

        --因為被拉回也會跳很怪，所以檔一下
        --其他人就讓他們傳吧
        if (SceneMgr.IsLoadAsTeleport() and _Player.m_IsSelf) or not _Player.m_IsSelf then

            --仍在進入場景流程，等一下再播特效
            if coroutine.running() and _Player.m_IsSelf then
                iModelController:ResetScan(false, false)
                --想等介面都開好再播特效
                coroutine.wait(Loading_Close_Wait_Time)
            end

            if _Player.m_IsSelf or (_Player.m_CurrentCulling and _Player.m_CurrentCulling < ECullingState.OUT_OF_RANGE) then
                _Player:TeleportEffect(true)

            end

            if _Player.m_IsSelf then

                --仍在進入場景流程，播完特效再把相機拉回
                if coroutine.running() then
			        --想晚點再播震動
                    HEMTimeMgr.DoFunctionDelay(EffectSetting.m_TeleportInShakeWait, function()
                        CameraMgr.OnShake(EffectSetting.m_TeleportShakeCamera.m_ID, EffectSetting.m_TeleportShakeCamera.m_Amplitude,
                            EffectSetting.m_TeleportShakeCamera.m_Frequency, EffectSetting.m_TeleportShakeCamera.m_Duration, EffectSetting.m_TeleportShakeCamera.m_EaseType)
                    end)

                end
                --傳送成功訊息
                if SceneMgr.GetTeleportMsg()>0 then
                    MessageMgr.AddCenterMsg(false, TextData.Get(SceneMgr.GetTeleportMsg()))
                end
                --傳過就必定取消
                SceneMgr.SetIsLoadAsTeleport(false, false)

            end

        end
    end
end

---傳送離場流程
function RoleMgr.TeleportLeave(iRoleID, iOnCompleteDelegate)
    local _Player = RoleMgr.Get(iRoleID)
    if _Player ~= nil then
        if _Player.m_IsSelf or (_Player.m_CurrentCulling and _Player.m_CurrentCulling < ECullingState.OUT_OF_RANGE) then
            _Player:TeleportEffect(false)
            --播到一半震動
            if _Player.m_IsSelf then
                HEMTimeMgr.DoFunctionDelay(EffectSetting.m_TeleportDuration * 0.5, function()
                    CameraMgr.OnShake(EffectSetting.m_TeleportShakeCamera.m_ID, EffectSetting.m_TeleportShakeCamera.m_Amplitude,
                        EffectSetting.m_TeleportShakeCamera.m_Frequency, EffectSetting.m_TeleportShakeCamera.m_Duration, EffectSetting.m_TeleportShakeCamera.m_EaseType)
                end)
            end

            if iOnCompleteDelegate then
                HEMTimeMgr.DoFunctionDelay(EffectSetting.m_TeleportDuration, iOnCompleteDelegate)
            end
        else
            if iOnCompleteDelegate then
                iOnCompleteDelegate()
            end
        end
    end
end
--endregion

--- 刷新 HUD 名稱
function RoleMgr.RefreshHUDName()
    for key, value in pairs(this.m_Table_ActiveRoles) do
        value:UpdateHUD(EHUD_UpdateChecker.Name)
    end
end

--- 刷新玩家敵對狀態
function RoleMgr.RefreshHostility(iRoleID, isEnemy)
    --預想狀況: 我方/周邊玩家開關PK、我方/周邊玩家切換PK設定
    local _Player = RoleMgr.Get(iRoleID)
    if _Player then
        _Player.m_IsEnemy = isEnemy
        --TODO: 要刷新select的圖、刷新SearchMgr的周圍目標
    end
end

---登出要做甚麼
function RoleMgr.LoggingOutToDo()
    if this.m_RC_Player ~= nil then
        this.m_RC_Player.gameObject:SetActive(false)
        -- this.m_RC_Player.m_HUDController:ReturnHUD()
        -- this.m_Table_StoreSelfPlayer[tostring(this.m_RC_Player.m_PlayerID)] = this.m_RC_Player
        -- this.m_Table_ActiveRoles[tostring(this.m_RC_Player.m_PlayerID)] = nil
        this.m_RC_Player = nil
    end
    for key, value in pairs(this.m_Table_ActiveRoles) do
        value:IsRelease(true)
        local _PlayerBasePoint = value.m_PlayerBasePoint
        if _PlayerBasePoint then
            if not Extension.IsUnityObjectNull(_PlayerBasePoint.transform)  then
                GameObject.Destroy(_PlayerBasePoint.transform.gameObject)
            end

            if not Extension.IsUnityObjectNull(_PlayerBasePoint.m_BattleTestObj)  then
                GameObject.Destroy(_PlayerBasePoint.m_BattleTestObj.gameObject)
            end

            _PlayerBasePoint.transform = nil
            _PlayerBasePoint.m_BattleTestObj = nil
            _PlayerBasePoint = nil
        end
        this.m_GObjPool_Role[value.AppearData.m_Gender]:Store(value.gameObject)
        this.m_Table_ActiveRoles[key] = nil
    end
end

function RoleMgr.OnUnrequire()
    if this.m_RoleParent.gameObject~=nil then
        GameObject.Destroy(this.m_RoleParent.gameObject)
        this.m_RoleParent = nil
    end
    RoleController.OnUnrequire()
    return true
end
return RoleMgr
