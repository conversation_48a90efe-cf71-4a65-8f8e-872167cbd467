---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---@class InnerMeridianSkillData
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.5.22
InnerMeridianSkillData = {}
InnerMeridianSkillData.__index = InnerMeridianSkillData

local this = InnerMeridianSkillData

--region 串檔 coroutine 專區

--- 需要等待的串檔
this.m_NeedWaitData = { }

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

--- 串檔總數
this.m_DataCount = 0

--- 新串了多少串檔
this.m_NewDataCount = 0

---Stream讀了多少次
this.m_StreamReadByteTimes = 0

--endregion 串檔 coroutine 專區
this.m_ASSET_NAME = "InnerMeridianSkill_C"
local m_Dic = {}

function InnerMeridianSkillData:New(iReader)
    ---@type InnerMeridianSkillData
    local _Data = {}
    setmetatable ( _Data, InnerMeridianSkillData )

    --- 流水號
    ---@type ushort
    _Data.m_ID = iReader:ReadUInt16()
    --- 靈脈編號
    ---@type byte
    _Data.m_Num = iReader:ReadByte()
    --- 用途定義
    ---@type byte
    _Data.m_Define = iReader:ReadByte()
    --- 內功武功編號
    ---@type ushort
    _Data.m_WugongID = iReader:ReadUInt16()
    --- 內功品階
    ---@type byte
    _Data.m_SkillGrade = iReader:ReadByte()
    --- 升級道具需求數
    ---@type byte
    _Data.m_NeedCount = iReader:ReadByte()
    --- 隨機機率
    ---@type ushort
    _Data.m_RandProb = iReader:ReadUInt16()

    return _Data.m_ID, _Data
end

---初始化
function InnerMeridianSkillData.Init()
    return DataReader.LoadFile(this.m_ASSET_NAME, InnerMeridianSkillData.OnLoadData)
end

---讀檔
function InnerMeridianSkillData.OnLoadData(iFile)
    local _Reader = DataReader.New(iFile)
    this.m_DataCount = _Reader:ReadUInt32()
    DataMgr.NewData(this, _Reader, nil, InnerMeridianSkillData.FunctionAddSingleDataByIndex)
end

---外掛串表讀檔
function InnerMeridianSkillData.OnLoadExTableData(iIdx , iData)
    return iData
end

---取得InnerMeridianSkillData
---@param m_ID InnerMeridianSkillDatam_ID
---@return InnerMeridianSkillData
function InnerMeridianSkillData.GetInnerMeridianSkillDataByIdx(iIdx)
    if iIdx > 0 and m_Dic[iIdx] ~= nil then
        return m_Dic[iIdx]
    else
        D.Log("Cant Find InnerMeridianSkillData m_ID: " .. iIdx)
        return nil
    end
end

---============分隔線以上為自動填入區段，功能請勿於此線以上撰寫================
---======================== I am Spliter! ===============================

function InnerMeridianSkillData.FunctionAddSingleDataByIndex(iCountIndex, iDataIndex, iData)
    if m_Dic[iData.m_Num] == nil then
        m_Dic[iData.m_Num] = {}
    end
    table.insert(m_Dic[iData.m_Num], iData)
end


function InnerMeridianSkillData.GetInnerMeridianSkillDataByID(iIdx, iID)
    local _Data = InnerMeridianSkillData.GetInnerMeridianSkillDataByIdx(iIdx)
    for k, v in pairs(_Data) do
        if v.m_WugongID == iID then
            return v
        end
    end
    return nil
end