---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---詢問視窗 大分類 4 物品icon + 機率展示型
---@class CommonQuery_Type4_Controller 
---author 鐘彥凱
---telephone #2881
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.10.9
CommonQuery_Type4_Controller = {}
local this = CommonQuery_Type4_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("CommonQuery_Type4_View", "CommonQuery_Type4_Controller", EUIOrderLayers.Peak)

---描述區域 顯示數字時使用字體
local TMPStyle_Number = "PO"
---描述區域 顯示文字時使用字體
local TMPStyle_Word = "W"

---初始化
function CommonQuery_Type4_Controller.Init()

    ---標題文字
    this.m_Text_TitleContent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_TitleContent")
    ---內文 目前規劃不需要 prefab區保留
    ---this.m_Text_Content = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content")

    ---按鍵列
    this.m_LayoutGroup_Button = this.m_ViewRef.m_Dic_Trans:Get("&LayoutGroup_Button").gameObject
    ---確定/取消/特別按鍵
    this.m_Btn_Cancel = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Cancel"))
    this.m_Btn_Confirm = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Confirm"))
    this.m_Btn_Special = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Special"))

    ---分隔線
    this.m_Img_SeperateLine = this.m_ViewRef.m_Dic_Trans:Get("&Img_SeperateLine").gameObject

    ---ScrollView 重複使用的元件
    this.m_ProbibilityUnit = this.m_ViewRef.m_Dic_Trans:Get("&IconUint").gameObject

    --ScrollView 項目掛載的 parent
    this.m_ContentIconsList = this.m_ViewRef.m_Dic_Trans:Get("&ContentIconsList")
    
    --UIScrollView 掛腳本的物件
    this.m_ScrollView_Icons = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Icons").gameObject

    ---換語言重登的初始化 不知為何 this.m_ScrollView 會有數值 所以強制清空 
    this.m_ScrollView = nil

    ---ScrollView 腳本 本身初始化
    this.m_ScrollView = ScrollView.Init(this.m_ScrollView_Icons,false,this.m_ProbibilityUnit, this.ReturnCount,this.AfterReuseItemInit,this.AfterReuseItemIndexUpdate,true)

end

---Update
function CommonQuery_Type4_Controller.Update()

end


function CommonQuery_Type4_Controller.Open()
    CommonQuery_Type4_Controller.Settinginformation()
    return true 
end

---設定中央詢問視窗 需要顯示的元件
function CommonQuery_Type4_Controller.Settinginformation()
    local _viewData = CommonQueryMgr.GetCurrentCommonQueryData()
    if not _viewData then
        D.LogError("中央詢問視窗沒資料!!")
        return
    end
    
    ---設定確認方法
    if _viewData.m_CallBack_Confirm ~= nil then
        CommonQuery_Type4_Controller.m_Type0_CallBack_Confirm = function()
            if(type(_viewData.m_CallBack_ConfirmArgs)=="table" and next(_viewData.m_CallBack_ConfirmArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Confirm,unpack(_viewData.m_CallBack_ConfirmArgs))
            else
                pcall(_viewData.m_CallBack_Confirm)
            end
            CommonQuery_Type4_Controller.DoConfirmBtn()
        end
    else
        CommonQuery_Type4_Controller.m_Type0_CallBack_Confirm = function()
            CommonQuery_Type4_Controller.DoConfirmBtn()
        end
    end

    ---設定取消方法
    if _viewData.m_CallBack_Cancel ~= nil then
        CommonQuery_Type4_Controller.m_Type0_CallBack_Cancel = function()
            if(type(_viewData.m_CallBack_CancelArgs)=="table" and next(_viewData.m_CallBack_CancelArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Cancel,unpack(_viewData.m_CallBack_CancelArgs))
            else
                pcall(_viewData.m_CallBack_Cancel)
            end
            CommonQuery_Type4_Controller.DoCancelBtn()
        end
    else
        CommonQuery_Type4_Controller.m_Type0_CallBack_Cancel = function()
            CommonQuery_Type4_Controller.DoCancelBtn()
        end
    end
    
    ---設定特殊方法
    if _viewData.m_CallBack_Special ~= nil then
        CommonQuery_Type4_Controller.m_Type0_CallBack_Special = function()
            if(type(_viewData.m_CallBack_SpecialArgs)=="table" and next(_viewData.m_CallBack_SpecialArgs) ~= nil) then
                pcall(_viewData.m_CallBack_Special,unpack(_viewData.m_CallBack_SpecialArgs))
            else
                pcall(_viewData.m_CallBack_Special)
            end
            CommonQuery_Type4_Controller.DoSpecialBtn()
        end
    else
        CommonQuery_Type4_Controller.m_Type0_CallBack_Special = function()
            CommonQuery_Type4_Controller.DoSpecialBtn()
        end
    end
     
    ---設定標題
    if _viewData.m_TitleContentID ~=0 then --有標題
        if table.IsNullOrEmpty(_viewData.m_TitleParma)then
            this.m_Text_TitleContent.text = TextData.Get(_viewData.m_TitleContentID)
        else
            this.m_Text_TitleContent.text = GString.Format(TextData.Get(_viewData.m_TitleContentID) , unpack(_viewData.m_TitleParma, 1, table.maxn(_viewData.m_TitleParma)))
        end
        
        this.m_Text_TitleContent.gameObject:SetActive(true)
    else
        this.m_Text_TitleContent.gameObject:SetActive(false)
    end
    
    if _viewData.m_Str_BTNCancel ~= 0 then --取消按鈕
        this.m_Btn_Cancel:SetText(TextData.Get(_viewData.m_Str_BTNCancel))
        Button.ClearListener(this.m_Btn_Cancel.gameObject)
        Button.AddListener(this.m_Btn_Cancel, EventTriggerType.PointerClick,CommonQuery_Type4_Controller.m_Type0_CallBack_Cancel)
        Button.SetAudioID(this.m_Btn_Cancel, _viewData.m_Sound_BTNCancel)
        this.m_Btn_Cancel.gameObject:SetActive(true)
    else
        this.m_Btn_Cancel.gameObject:SetActive(false)
    end
    
    if _viewData.m_Str_BTNConfirm ~= 0 then --確認按鈕
        this.m_Btn_Confirm:SetText(TextData.Get(_viewData.m_Str_BTNConfirm))
        Button.ClearListener(this.m_Btn_Confirm.gameObject)
        Button.AddListener(this.m_Btn_Confirm, EventTriggerType.PointerClick,CommonQuery_Type4_Controller.m_Type0_CallBack_Confirm)
        Button.SetAudioID(this.m_Btn_Confirm, _viewData.m_Sound_BTNConfirm)
        this.m_Btn_Confirm.gameObject:SetActive(true)
    else
        this.m_Btn_Confirm.gameObject:SetActive(false)
    end
    
    if _viewData.m_Str_BTNSpecial ~= 0 then --特殊按鈕
        this.m_Btn_Special:SetText(TextData.Get(_viewData.m_Str_BTNSpecial))
        Button.ClearListener(this.m_Btn_Special.gameObject)
        Button.AddListener(this.m_Btn_Special, EventTriggerType.PointerClick,CommonQuery_Type4_Controller.m_Type0_CallBack_Special)
        Button.SetAudioID(this.m_Btn_Special, _viewData.m_Sound_BTNSpecial)
        this.m_Btn_Special.gameObject:SetActive(true)
    else
        this.m_Btn_Special.gameObject:SetActive(false)
    end

    ---刷新ScrollView要顯示的內容

    if _viewData.m_CustomData ~= nil then
        this.m_CurrentIconType = _viewData.m_CustomData.m_IconType
        this.m_ItemIDTable = _viewData.m_CustomData.m_ItemTable
        this.m_DescriptionTable = _viewData.m_CustomData.m_DescriptionTable
        this.m_ItemCountTable = _viewData.m_CustomData.m_ItemCountTable

        for i = 1, this.m_ContentIconsList.childCount do
            this.m_ContentIconsList:GetChild(i-1).gameObject:SetActive(false)
        end

        ScrollView.UpdateToFirst(this.m_ScrollView)
    end
end

function CommonQuery_Type4_Controller.ReturnCount()
    return this.m_ItemIDTable == nil and 0 or table.Count(this.m_ItemIDTable)
end

function CommonQuery_Type4_Controller.AfterReuseItemInit(iItem,iRowIdx)
    if iItem == nil then
        return
    end
end

function CommonQuery_Type4_Controller.AfterReuseItemIndexUpdate(iItem,iRowIdx)

    if iItem == nil  then
        return
    elseif iRowIdx > this.ReturnCount() then
        iItem.m_GObj:SetActive(iRowIdx <= this.ReturnCount())
        return
    end

    CommonQuery_Type4_Controller.ProbabilitRowItem(iItem)
    iItem.m_GObj:SetActive(iRowIdx <= this.ReturnCount())
end

function CommonQuery_Type4_Controller.ProbabilitRowItem(iItem)

    if this.m_ItemIDTable == nil then
        return
    end

    local _UseItemID =  this.m_ItemIDTable[iItem.m_Index] 
    local _UseItemCount = this.m_ItemCountTable[iItem.m_Index] 
    local _UseItemDescription =  this.m_DescriptionTable[iItem.m_Index] 

    
    if _UseItemID ~= nil and _UseItemDescription ~= nil  then

        local _UISet = {}
        _UISet.gameObject = iItem.m_GObj
        _UISet.m_TMP_ItemName =  _UISet.gameObject.transform:Find("TMP_ItemName"):GetComponent(typeof(TMPro.TextMeshProUGUI))
        _UISet.m_TMP_Probability =  _UISet.gameObject.transform:Find("TMP_Prosibility"):GetComponent(typeof(TMPro.TextMeshProUGUI))

        

        local _ItemName
        if this.m_CurrentIconType == EIconType.Item then

            if iItem.m_ItemIcon == nil then
                iItem.m_ItemIcon = IconMgr.NewItemIcon(0, iItem.m_GObj.transform:Find("IconParent"), 70)
                iItem.m_ItemIcon.transform.localPosition = Vector3(0,-2,0)
                iItem.m_ItemIcon.m_TMP_Count.text = ""
                iItem.m_ItemIcon:SetClickTwice( false ) 
            end

            ---開啟物品icon  如果有其他類別icon開啟中要關閉
            iItem.m_ItemIcon.gameObject:SetActive(true)

            ---關閉其他類別Icon
            if iItem.m_SkillIcon ~= nil and iItem.m_SkillIcon.gameObject.activeSelf then
                iItem.m_SkillIcon.gameObject:SetActive(false)
            end

            ---關寵物icon
            if iItem.m_PetIcon ~= nil and iItem.m_PetIcon.gameObject.activeSelf then
                iItem.m_PetIcon.gameObject:SetActive(false)
            end

            iItem.m_ItemIcon:RefreshIcon(_UseItemID)
            iItem.m_ItemIcon:SetCount(_UseItemCount)

            _ItemName = ItemData.GetItemName(_UseItemID)
        elseif this.m_CurrentIconType == EIconType.Pet then

            --- 寵物 Icon
            if iItem.m_PetIcon == nil then
                iItem.m_PetIcon = IconMgr.NewPetIcon(0, iItem.m_GObj.transform:Find("IconParent"), 70)
                iItem.m_PetIcon.transform.localPosition = Vector3(0,-2,0)
                iItem.m_PetIcon.m_TMP_Count.text = ""
                iItem.m_PetIcon:SetClickTwice( false ) 
            end

            ---開啟物品icon  如果有其他類別icon開啟中要關閉
            iItem.m_PetIcon.gameObject:SetActive(true)

            ---關閉其他類別Icon
            if iItem.m_SkillIcon ~= nil and iItem.m_SkillIcon.gameObject.activeSelf then
                iItem.m_SkillIcon.gameObject:SetActive(false)
            end

            if iItem.m_ItemIcon ~= nil and iItem.m_ItemIcon.gameObject.activeSelf then
                iItem.m_ItemIcon.gameObject:SetActive(false)
            end


            iItem.m_PetIcon:RefreshIcon(_UseItemID)
            iItem.m_PetIcon:SetCount(_UseItemCount)

            _ItemName = PetData.GetPetName(_UseItemID)
            
        elseif  this.m_CurrentIconType == EIconType.Skill then

            if iItem.m_SkillIcon == nil then
                iItem.m_SkillIcon = IconMgr.NewSkillIcon(0, iItem.m_GObj.transform:Find("IconParent"), 70)
                iItem.m_SkillIcon.transform.localPosition = Vector3(0,-2,0)
                iItem.m_SkillIcon:SetClickTwice(false)
            end

            ---開啟技能icon  如果有其他類別icon開啟中要關閉
            iItem.m_SkillIcon.gameObject:SetActive(true)

            ---關閉其他類別Icon
            if iItem.m_ItemIcon ~= nil and iItem.m_ItemIcon.gameObject.activeSelf then
                iItem.m_ItemIcon.gameObject:SetActive(false)
            end

            ---關寵物icon
            if iItem.m_PetIcon ~= nil and iItem.m_PetIcon.gameObject.activeSelf then
                iItem.m_PetIcon.gameObject:SetActive(false)
            end

            iItem.m_SkillIcon:RefreshIcon(_UseItemID)

            local _SkillData = SkillData.GetSkillDataByIdx(_UseItemID)
            if _SkillData ~= nil then
                _ItemName = TextData.Get(_SkillData.m_NameId)
            end
        end

        _UISet.m_TMP_ItemName.text = _ItemName

        if type(_UseItemDescription) == "number" then
            _UseItemDescription = string.format("%.2f",_UseItemDescription)

            local _FinalString = GString.StringWithStyle( _UseItemDescription .. "%",TMPStyle_Number)
            _UISet.m_TMP_Probability.text =_FinalString
        else
            local _FinalString = GString.StringWithStyle( _UseItemDescription ,TMPStyle_Word)
            _UISet.m_TMP_Probability.text = _FinalString
        end
        

    end
end

---預設 點擊確定按鈕
function CommonQuery_Type4_Controller.DoConfirmBtn()
    D.Log("CommonQuery_Type4_Controller Confirm Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊取消按鈕
function CommonQuery_Type4_Controller.DoCancelBtn()
    D.Log("CommonQuery_Type4_Controller Cancel Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end

---預設 點擊特殊按鈕
function CommonQuery_Type4_Controller.DoSpecialBtn()
    D.Log("CommonQuery_Type4_Controller Special Btn Click",Color.White)
    CommonQueryMgr.ShowNextCommonQueryMgrData()
end


--[[
    if Input.GetKeyDown(KeyCode.M) then

		--- 測試用塞的假資料
		local _QueryData4 = 513
			
	   --- 測試用塞的假資料 類別4專屬 customData
	   local _Type4Data1 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.ShowProbability)

        local _IDTable = {10001,12001,14001,16001,18001,10002,12002,14002}
        local _DescriptionTable = {1.111,2.222,3.333,4.444,5.555,6.666,7.777,8.888}
        local _AmountTable = {10,20,30,40,50,60,70,80}

        _Type4Data1:Type4_BuildData(EIconType.Item,_IDTable, _DescriptionTable, _AmountTable)
	   
	   CommonQueryMgr.AddNewInform(_QueryData4,{},{}, function() print(" 點擊Type4測試 使用物品Icon") end,nil,
	   nil,nil,nil,nil,_Type4Data1)

	    --- 測試用塞的假資料 類別4專屬 customData
	   local _Type4Data2 = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.ShowProbability)

        local _IDTable2 = {2534,2532,2533,2541,2543,2542,63,71}
        local _DescriptionTable2 = {"技能1","技能2","技能3","技能4","技能5","技能6","技能7","技能8"}
        local _AmountTable2 = {10,20,30,40,50,60,70,80}

        _Type4Data2:Type4_BuildData(EIconType.Skill,_IDTable2, _DescriptionTable2, _AmountTable2)
	   
	   CommonQueryMgr.AddNewInform(_QueryData4,{},{}, function() print(" 點擊Type4測試 使用技能icon") end,nil,
	   nil,nil,nil,nil,_Type4Data2)
		
   end
]]
