%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Boss_0009_m
  m_Shader: {fileID: 4800000, guid: fa29df416bd74f24f83f61f76b0fc51c, type: 3}
  m_ValidKeywords:
  - _Emi_ON
  - _Ocu_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 7107c60ef5cbd1b45b819e47c0bfedd8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveTex:
        m_Texture: {fileID: 2800000, guid: a897146633472df4884daf9edba42b6f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 7fe0815293e3a8a44a0a5895500a4cc9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 91eccdbd983c46c40985c1829524f8ac, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap:
        m_Texture: {fileID: 2800000, guid: a2baabb7b18e66e40a2ec2583a3a8f60, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DeformationIntensity: 0
    - _DetailNormalMapScale: 1
    - _DissolveEdge: 0
    - _DissolveThreshold: 0
    - _DstBlend: 0
    - _Emi: 1
    - _EmissiveInten: 2
    - _FadeThreshold: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.25
    - _GlossyReflections: 1
    - _GlowRimIntensity: 1.5
    - _GlowRimPow: 3
    - _HitRimIntensity: 0
    - _HitRimPow: 4
    - _Metallic: 1
    - _Mode: 0
    - _OccBlend: 0
    - _OccInten: 1
    - _OccPow: 1
    - _OcclusionStrength: 1
    - _Ocu: 1
    - _Parallax: 0.02
    - _PassOps: 7
    - _ScanIntensity: 1
    - _ScanMaxScale: 0.2
    - _ScanObjectFoot: 0
    - _ScanObjectHeight: 1
    - _ScanScale: 0.28
    - _ScanScaleStart: 0.8
    - _ScanScaleStrength: 0.4
    - _ScanValue: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _ClothColor1: {r: 1, g: 1, b: 1, a: 1}
    - _ClothColor2: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 0.94117653, g: 0.94117653, b: 0.94117653, a: 1}
    - _DissolveColor: {r: 0, g: 5.6, b: 7.34, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _EmissiveColor: {r: 1, g: 1, b: 1, a: 1}
    - _GlowColor: {r: 0, g: 0, b: 0, a: 1}
    - _HairColor: {r: 0.38431373, g: 0.79607844, b: 1, a: 1}
    - _HitColor: {r: 0, g: 0, b: 0, a: 1}
    - _ScanColor: {r: 0.81307805, g: 0.7783019, b: 1, a: 1}
    - _SkinColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
