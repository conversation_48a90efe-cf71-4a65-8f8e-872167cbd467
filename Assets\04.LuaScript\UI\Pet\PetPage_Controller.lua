---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---寵物介面
---寵物介面本體，其它頁面要掛在他下面
---@class PetPage_Controller
---author <PERSON> Wei
---telephone #2892
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.12.19
PetPage_Controller = {}

require("UI/Pet/Pet_Model")
require("UI/Pet/PetList_Controller")
require("UI/Pet/PetAttribute_Controller")
require("UI/Pet/PetFuse_Controller")
require("UI/Pet/PetUpgrade_Controller")
require("UI/Pet/PetMission_Controller")

---@class EPetPage 寵物頁面
EPetPage = {
    --- 無
    None = 0,
    --- 基本頁面
	Basic = 1,
	--- 合成製作
	Fuse = 2,
	--- 突破
	Upgrade = 3,
	--- 派遣
	Mission = 4,
	--- 修復艙室
	Repair = 5,
}

PetPage_Controller[EPetPage.Basic] = PetAttribute_Controller
PetPage_Controller[EPetPage.Fuse] = PetFuse_Controller
PetPage_Controller[EPetPage.Upgrade] = PetUpgrade_Controller
PetPage_Controller[EPetPage.Mission] = PetMission_Controller
PetPage_Controller[EPetPage.Repair] = {}

local this = PetPage_Controller

setmetatable( this, { __index = UIControllerBase } )
this:New("Pet_View", "PetPage_Controller", EUIOrderLayers.FullPage, false, "bg_005")

--- 寵物列表
this.m_PetList = PetList_Controller

--- 頁面GObj
this.m_PageGameObject = {}

--- 目前頁面
local m_CurrentPage = EPetPage.Basic

--- 左邊點擊初始化過了沒
local m_IsLeftGroupInit = false

--- 打開頁面可以帶入的參數
local m_OpenParam = {}

--- 取得頁面對應字串
local function GetPageTitle(iIndex)

	if(iIndex == EPetPage.Basic) then
		return GString.Format(TextData.Get(20323003), TextData.Get(20323001))
	elseif(iIndex == EPetPage.Fuse) then
		return GString.Format(TextData.Get(20323004), TextData.Get(20323001))
	else
		return GString.Format(TextData.Get(20323005), TextData.Get(20323001))
	end

end

---初始化
function PetPage_Controller.Init()

	Pet_Model.Init()

    -- 初始化寵物列表
	this.m_PetListGObject = this.m_ViewRef.m_Dic_Trans:Get("&PetList_View")
    this.m_PetList.Init(this)

    -- 第一頁 寵物資料
	this.m_PageGameObject[EPetPage.Basic] = this.m_ViewRef.m_Dic_Trans:Get("&PetAttribute_View").gameObject
    -- 第二頁 寵物融合
    this.m_PageGameObject[EPetPage.Fuse] = this.m_ViewRef.m_Dic_Trans:Get("&PetFuse_View").gameObject
	-- 第三葉 寵物突破
	this.m_PageGameObject[EPetPage.Upgrade] = this.m_ViewRef.m_Dic_Trans:Get("&PetUpgrade_View").gameObject
	-- 第四頁 寵物派遣
    this.m_PageGameObject[EPetPage.Mission] = this.m_ViewRef.m_Dic_Trans:Get("&PetMission_View").gameObject

    -- 看看哪個先開啟
	for i = 1, table.Count(this.m_PageGameObject) do

		-- 初始化各分頁
		if PetPage_Controller[i] ~= nil then
			PetPage_Controller[i].Init(this)
		end

	end

	-- 主要 Panel
	this.m_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&MainPanel")

	--- 資源列表
	local _CurrentResourceTable =
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }

	--要產生的位置(父物件) 新版上方條
	this.m_FullPageTitleBar = FullPageTitleBar.New(this, this.m_MainPanel, 0, "DefaultTitleName", _CurrentResourceTable)

    -- 分頁表
	local _IconTable = {"Common_pet_but001D", "Common_pet_but002D", "Common_pet_but003D", "Common_pet_but004D", "Common_pet_but005D"}
	local function _ClickPage(iIndex)
		if(m_IsLeftGroupInit) then
			m_CurrentPage = iIndex
			PetPage_Controller.OnPageButtonClick(m_CurrentPage)
		else
			m_IsLeftGroupInit = true
		end
	end
    this.m_GObjPageGroupButton = LeftGroupTab.New(this.m_MainPanel, 2, _IconTable, function(iIndex)
		_ClickPage(iIndex)
	end)


end

function PetPage_Controller.ToDoAfterOpenUISucceed()
	this.m_GObjPageGroupButton:OnPointerClickByIndex(m_CurrentPage)
end

---Update
function PetPage_Controller.Update()

	-- 更新各母分頁 如果空的 不更新 如果是修復艙室 也不更新
	if(PetPage_Controller[m_CurrentPage] ~= nil and m_CurrentPage ~= EPetPage.Repair) then

		PetPage_Controller[m_CurrentPage].Update()

	end

	-- 要看有沒有派遣任務 有的話要做秒數的倒數
	local _PetMissionData = PetMgr.GetPetMissionData()
	for _k, _v in pairs(_PetMissionData) do

		-- 是在派遣中
		if(_v.m_ServerData.m_State == EMissionMissionState.Working) then
			_v.m_ServerData.m_TimeLeft = _v.m_ServerData.m_TimeLeft - Time.deltaTime * 1

			-- 倒數結束沒 結束就可收吧
			if(_v.m_ServerData.m_TimeLeft <= 0) then
				_v.m_ServerData.m_State = EMissionMissionState.CollectAble
				PetMission_Controller.ChangeState(_k, EMissionMissionState.CollectAble)
			end

		end

	end

	this.m_PetList.Update()

end

--- 開啟介面
---@param number iParams[1] 開哪個頁面
---@param table iParam[2] 任務資料
function PetPage_Controller.Open(iParams)

	-- 暫時
	m_CurrentPage = EPetPage.Basic

	if not table.IsNullOrEmpty(iParams) then

		if(iParams[1] ~= nil and iParams[1] ~= 0) then
			m_CurrentPage = iParams[1]
		end

	end

	-- 開頁面有沒有要帶入的參數
	m_OpenParam = {}
	if(iParams[2]) then
		table.insert(m_OpenParam, iParams[2])
	end

	--PetPage_Controller.OpenPage(m_CurrentPage)

	--- 登記觀察者 貨幣變化
	GStateObserverManager.Register(EStateObserver.UpdateDiamond, PetPage_Controller)
	GStateObserverManager.Register(EStateObserver.UpdateCoin, PetPage_Controller)
	GStateObserverManager.Register(EStateObserver.UpdateHEMCoin, PetPage_Controller)

	SendProtocol_020._021_01()

	return true

end

function PetPage_Controller.Close()

    -- 看看哪個先開啟
	for i = 1, table.Count(this.m_PageGameObject) do

		-- 初始化各分頁
		if PetPage_Controller[i] ~= nil then
			PetPage_Controller[i].Close()
		end

	end

	-- 如果是修復艙室
	if(m_CurrentPage == EPetPage.Repair) then

		-- 關閉修復艙室
		UIMgr.Close(RoomRepair_Controller)

	end
	
	-- 關掉 PhysicalCamera 設定
	AppearanceMgr.TurnOffPhysicalCameraAndSetToFixValue()

end

function PetPage_Controller.OpenPage(iIndex)

	--開啟各母分頁
	if(PetPage_Controller[iIndex] ~= nil and m_CurrentPage ~= EPetPage.Repair) then

		PetPage_Controller[iIndex].Open(m_OpenParam)

	end

	-- 如果是修復艙室
	if(m_CurrentPage == EPetPage.Repair) then

		-- 開啟修復艙室
		UIMgr.Open(RoomRepair_Controller, TimeMachineMgr.ERoom.PetRoom)

	end

	this.m_FullPageTitleBar:SetTitleText(GetPageTitle(iIndex))

	LeftGroupTab.OnPointerClickByIndex(this.m_GObjPageGroupButton, iIndex)

end

function PetPage_Controller.OnPageButtonClick(iPageIndex)

	--Log
	--D.LogWarning(GString.Format("開啟母分頁：{0}", Bag_Controller.GetTitleText(iPageIndex)))

	-- 選擇列表清光光
	PetList_Controller.ClearAllSelectedItem()

	-- 關掉 PhysicalCamera 設定
	AppearanceMgr.TurnOffPhysicalCameraAndSetToFixValue()

	--開啟相對應母分頁物件
	for i = 1, table.Count(this.m_PageGameObject) do

		if this.m_PageGameObject[i] ~= nil then
			this.m_PageGameObject[i]:SetActive(i == iPageIndex)
		end
	end

	-- 開啟各母分頁
	if (PetPage_Controller[iPageIndex] ~= nil and m_CurrentPage ~= EPetPage.Repair) then

		PetPage_Controller[iPageIndex].Open(m_OpenParam)

	end

	-- 如果是修復艙室
	if(m_CurrentPage == EPetPage.Repair) then

		-- 開啟修復艙室
		UIMgr.Open(RoomRepair_Controller, TimeMachineMgr.ERoom.PetRoom)

	else
		
		UIMgr.Close(RoomRepair_Controller)

	end

	-- 484派遣頁面 還要看是不是維修艙室 是的話列表要關掉
	if(m_CurrentPage == EPetPage.Mission or m_CurrentPage == EPetPage.Repair) then

		this.m_PetListGObject.gameObject:SetActive(false)

	else

		this.m_PetListGObject.gameObject:SetActive(true)

	end

	--更換相對應 TitleText
	this.m_FullPageTitleBar:SetTitleText(GetPageTitle(iPageIndex))

	-- 寵物列表移到頂
	PetList_Controller.UpdateToFirst()

	-- 關閉寵物列表遮罩
	PetList_Controller.SetPetListCover(false)

end

--- 選擇方案 1
function PetPage_Controller.OnOption1Click(iNumber)

	PetPage_Controller[m_CurrentPage].Option1Click(iNumber)

end

--- 選擇方案 2 中的按鈕被點擊
---@type number iNumber 那顆按鈕被點擊
function PetPage_Controller.OnOption2Click(iNumber)

	-- 有 Option2Click 的才呼叫
	if(PetPage_Controller[m_CurrentPage].Option2Click ~= nil) then
    	PetPage_Controller[m_CurrentPage].Option2Click(iNumber)
	end


end

--- 寵物列表被點選 要傳給對應的頁面資料 每個 controller 如果要接到 PetList 來的點擊資料需要有一個 PetListClick function
---@type table iData 會給設給 List 的資料
function PetPage_Controller.OnPetListClick(iData)

    PetPage_Controller[m_CurrentPage].PetListClick(iData)

end

--- 收到協定後有沒有要刷新的
---@param table iParam 額..看想塞什麼 現在會塞區分哪些協定種類
function PetPage_Controller.UpdateAfterProtocol(iParam)

	PetPage_Controller[m_CurrentPage].RefreshAfterProtocol(iParam)

end

--- 清空
function PetPage_Controller.OnDestroy()
	local _IsCanDestroy = true
    -- 重製到第一頁
    m_CurrentPage = EPetPage.Basic

	-- Init 關起來
	m_IsLeftGroupInit = false

	-- 打開頁面的參數設空
	m_OpenParam = {}

    this.m_PetList.OnDestroy()

	for i = 1, table.Count(this.m_PageGameObject) do

		-- 各分頁
		if PetPage_Controller[i] ~= nil then
			_IsCanDestroy = PetPage_Controller[i].OnDestroy()
		end

	end

	--- 關閉觀察者 貨幣變化
	GStateObserverManager.UnRegister(EStateObserver.UpdateDiamond, PetPage_Controller)
	GStateObserverManager.UnRegister(EStateObserver.UpdateCoin, PetPage_Controller)
	GStateObserverManager.UnRegister(EStateObserver.UpdateHEMCoin, PetPage_Controller)

	return _IsCanDestroy
end

--- 打開選擇清單
function PetPage_Controller.OpenSelectList(ibool)

	this.m_PetListGObject.gameObject:SetActive(ibool)

end

--- 背包物品有更新會呼叫
function PetPage_Controller:OnStateChanged(iState)

    if iState == EStateObserver.UpdateDiamond or
    iState == EStateObserver.UpdateCoin or
    iState == EStateObserver.UpdateHEMCoin then
        -- 更新錢包
        this.m_Group_Resources.m_Resources:OnUpdate()
    end

end
