fileFormatVersion: 2
guid: a02825fc135a68d499347e5c5fdf920c
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Head
    100004: Bip001 Spine1
    100006: Bone001
    100008: Bone002
    100010: Bone003
    100012: Bone004
    100014: Bone005
    100016: Bone006
    100018: Bone007
    100020: Bone008
    100022: Bone009
    100024: //RootNode
    100026: L anal fin01
    100028: L anal fin02
    100030: L anal fin03
    100032: L anal fin04
    100034: L Dorsal Fin01
    100036: L Dorsal Fin02
    100038: L Dorsal Fin03
    100040: L Dorsal Fin04
    100042: L Dorsal Fin05
    100044: L Dorsal Fin06
    100046: L Dorsal Fin07
    100048: L Dorsal Fin08
    100050: L Dorsal Fin09
    100052: L Dorsal Fin10
    100054: L Dorsal Fin11
    100056: L Dorsal Fin12
    100058: L Dorsal Fin13
    100060: L Dorsal Fin14
    100062: L Dorsal Fin15
    100064: L Dorsal Fin16
    100066: L Dorsal Fin17
    100068: Lhand01
    100070: Lhand02
    100072: Lhand03
    100074: Lhand04
    100076: Lhand05
    100078: mouth
    100080: R anal fin01
    100082: R anal fin02
    100084: R anal fin03
    100086: R anal fin04
    100088: R Dorsal Fin01
    100090: R Dorsal Fin02
    100092: R Dorsal Fin03
    100094: R Dorsal Fin04
    100096: R Dorsal Fin05
    100098: R Dorsal Fin06
    100100: R Dorsal Fin07
    100102: R Dorsal Fin08
    100104: R Dorsal Fin09
    100106: R Dorsal Fin10
    100108: R Dorsal Fin11
    100110: R Dorsal Fin12
    100112: R Dorsal Fin13
    100114: R Dorsal Fin14
    100116: R Dorsal Fin15
    100118: R Dorsal Fin16
    100120: R Dorsal Fin17
    100122: Rhand01
    100124: Rhand02
    100126: Rhand03
    100128: Rhand04
    100130: Rhand05
    100132: Spine02
    100134: Spine03
    100136: Spine04
    100138: Skin
    400000: Bip001
    400002: Bip001 Head
    400004: Bip001 Spine1
    400006: Bone001
    400008: Bone002
    400010: Bone003
    400012: Bone004
    400014: Bone005
    400016: Bone006
    400018: Bone007
    400020: Bone008
    400022: Bone009
    400024: //RootNode
    400026: L anal fin01
    400028: L anal fin02
    400030: L anal fin03
    400032: L anal fin04
    400034: L Dorsal Fin01
    400036: L Dorsal Fin02
    400038: L Dorsal Fin03
    400040: L Dorsal Fin04
    400042: L Dorsal Fin05
    400044: L Dorsal Fin06
    400046: L Dorsal Fin07
    400048: L Dorsal Fin08
    400050: L Dorsal Fin09
    400052: L Dorsal Fin10
    400054: L Dorsal Fin11
    400056: L Dorsal Fin12
    400058: L Dorsal Fin13
    400060: L Dorsal Fin14
    400062: L Dorsal Fin15
    400064: L Dorsal Fin16
    400066: L Dorsal Fin17
    400068: Lhand01
    400070: Lhand02
    400072: Lhand03
    400074: Lhand04
    400076: Lhand05
    400078: mouth
    400080: R anal fin01
    400082: R anal fin02
    400084: R anal fin03
    400086: R anal fin04
    400088: R Dorsal Fin01
    400090: R Dorsal Fin02
    400092: R Dorsal Fin03
    400094: R Dorsal Fin04
    400096: R Dorsal Fin05
    400098: R Dorsal Fin06
    400100: R Dorsal Fin07
    400102: R Dorsal Fin08
    400104: R Dorsal Fin09
    400106: R Dorsal Fin10
    400108: R Dorsal Fin11
    400110: R Dorsal Fin12
    400112: R Dorsal Fin13
    400114: R Dorsal Fin14
    400116: R Dorsal Fin15
    400118: R Dorsal Fin16
    400120: R Dorsal Fin17
    400122: Rhand01
    400124: Rhand02
    400126: Rhand03
    400128: Rhand04
    400130: Rhand05
    400132: Spine02
    400134: Spine03
    400136: Spine04
    400138: Skin
    4300000: Skin
    7400000: Boss_0004_Expr_02
    9500000: //RootNode
    13700000: Skin
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: d1d6bbc3ddcf1644891b41543bee060c,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
