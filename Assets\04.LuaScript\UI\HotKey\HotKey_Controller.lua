---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("UI/HotKey/HotKeyBtn")

---@class HotKey_Controller
---主要 Controller，用來處理通用的功能
---其他分支功能到各Controller下處理
---author 詩惠
---version 2.0
---since HEM 2.0
---date 2022.07.12
HotKey_Controller = {}
local this = HotKey_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("HotKey_View", "HotKey_Controller", EUIOrderLayers.HalfPage_Right)

---戰鬥UI 顯示的時間
local SHOW_FIGHT_UI_TIME = 3

--region 拓展的其他 Controllers

--region 戰鬥快捷列 BattleAtk_Controller
--- 戰鬥快捷列
---@class BattleAtk_Controller
local BattleAtk_Controller = {}

function BattleAtk_Controller:Init()

    local _AnchorPrefix = "&Anchor_"
    local _ViewRef = HotKey_Controller.m_ViewRef

    ---戰鬥快捷鍵
    self.m_Table_BattleBtn = {}
    for k, v in pairs(EMainHotKeyNum) do
        local _Tmp_Anchor = _ViewRef.m_Dic_Trans:Get(string.Concat(_AnchorPrefix, k))

        local _Btn = HotKeyBtn:New(
                _Tmp_Anchor, 
                EHotkeyArea.BattleAtkArea, 
                PlayerData.GetWeaponType(), 
                v, 
                _Tmp_Anchor.rect.width, 
                self.OnTriggerFightUI)
        
        self.m_Table_BattleBtn[v] = _Btn
        --- 右下戰鬥快捷暫定無須 UI 預設音效
        if self.m_Table_BattleBtn[v].m_HotKeyIcon ~= nil then
            Button.SetAudioID(self.m_Table_BattleBtn[v].m_HotKeyIcon.m_UIEvent,-1)
        end
    end

    ---一般狀態群組
    self.m_Trans_Group_Normal = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Group_Normal")
    self.m_Trans_Group_Normal.gameObject:SetActive(true)
    ---戰鬥狀態群組
    self.m_Trans_Group_Fight = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Group_Fight")
    self.m_Trans_Group_Fight.gameObject:SetActive(true)

    ---迴避
    self.m_Btn_Dash = _ViewRef.m_Dic_Trans:Get("&Btn_Dash")
    Button.AddListener(self.m_Btn_Dash, EventTriggerType.PointerClick, self.OnClick_Dash)
end

function BattleAtk_Controller:Update()
    for i = EMainHotKeyNum.NormalAtk, EMainHotKeyNum.Move do
        if self.m_Table_BattleBtn[ i ] and self.m_Table_BattleBtn[ i ].m_HotKeyIcon then
            self.m_Table_BattleBtn[ i ].m_HotKeyIcon:UpdateCD()
            --self.m_Table_BattleBtn[ i ]:UpdateKeyCode()
        end
    end
end

function BattleAtk_Controller:Refresh()
    Debug_Model.AddNormalText("BattleAtk_Controller:Refresh 武器頁面設定: " .. HotKeyMgr.GetWeaponNum())
    D.Log("BattleAtk_Controller:Refresh 武器頁面設定: " .. HotKeyMgr.GetWeaponNum() .. "\n" .. debug.traceback())
    for _key, _value in pairs(self.m_Table_BattleBtn) do
        _value.m_Row = HotKeyMgr.GetWeaponNum()
        _value:Refresh()
        HotKeyMgr.SetBtnClick(_value)
    end

end

function BattleAtk_Controller:SetFightUI(iBool)
    self.m_Trans_Group_Fight.gameObject:SetActive(iBool)
    self.m_Trans_Group_Normal.gameObject:SetActive(iBool)
end

---迴避/翻滾/衝刺
function BattleAtk_Controller.OnClick_Dash()
    BattleMgr.UseSkill(BattleSetting.DASH_SKILL_ID)
end

---顯示戰鬥按鈕UI
function BattleAtk_Controller.OnTriggerFightUI()
    --D.Log("OnTriggerFightUI")
    HotKey_Controller.m_Timer_ShowFightUI = SHOW_FIGHT_UI_TIME
    BattleAtk_Controller:SetFightUI(true)
end
--endregion

--region 中下區塊快捷列
---中下區塊快捷列
---@class BottomHotKey_Controller
local BottomHotKey_Controller = {}
---當一次顯示兩排式 背景的滑動面板要localScale要變成這個數值
local _LocalScaleOfTwoRowTypeDragPanel = Vector3(1,1.8,1) 
---BottomHotKey_Controller  m_Trans_SecHotKey LeanTween相關定位點
local _RightPos = Vector3(-286,29.6,0)
local _LeftPos = Vector3(-346,29.6,0)
--- m_Trans_SecHotKey LeanTween 淡入 / 淡出時間
local _FLOATING_TWEEN_PERIOD = 0.1

--- 取得目前選擇的行數
---@param iIsSecond boolean 是否選第二行
function HotKey_Controller.GetButtonExpandArea(iIsSecond)
    -- 如果沒有展開兩行，就只給他案第一行
    if iIsSecond and BottomHotKey_Controller.m_ExpandArea >= 2 then
        return BottomHotKey_Controller.m_Table_BottomRowBtn[2][1].m_Row + HotKey_Controller.GetBottomHotkeyCurrentRow() -1
    else
        ---只有一排時 m_CurrentRow 就是當前快捷鍵對應到的Row
        return  HotKey_Controller.GetBottomHotkeyCurrentRow() 
    end
end

function BottomHotKey_Controller:Init()
    local _Prefix_SecHotKey = "&Second_"
    local _Prefix_Btn = "&Btn_"
    local _Dash = "_"
    
    --Sec Area GameObject 名稱為 區域編號_第幾個按鈕 ex: Btn_1_6
    ---中下區域快捷列
    -- 顯示中的 HotKeyBtn
    self.m_Table_BottomRowBtn = {}
    -- 列物件群組
    self.m_Table_HotKeyGroup = {}

    --紀錄排數 展開排數初始化
    self.m_ExpandArea = 0
    --- m_CurrentRow 紀錄當前對應到HotKeyMgr 哪個Row的資料 (如果快捷鍵是兩排模式 紀錄的是下面那一排對應 到 HotKeyMgr哪個Row的資料)
    self.m_CurrentRow = 1

    -- 快捷鍵初始化
    for i = EBottomHotKeyRows.Second_1, HOTKEY_MAX_ROW do
        self.m_Table_HotKeyGroup[i] = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get(string.Concat(_Prefix_SecHotKey, i))
        self.m_Table_BottomRowBtn[i] = {}
        
        -- 一列六格
        for k, v in pairs(EBottomHotKeyIdx) do
            local _Tmp_Btn = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get(string.Concat(_Prefix_Btn, i, _Dash, v))
            self.m_Table_BottomRowBtn[i][v] = HotKeyBtn:New(_Tmp_Btn, EHotkeyArea.BottomRowArea, i, v, 86)
        end
        
        -- 預設全關，用存檔再開
        self.m_Table_HotKeyGroup[i].gameObject:SetActive(false)
    end

    ---切換換分頁時 顯示用的燈號
    local _NotShowRowIndexString = "&RowDot_"
    self.m_NotShowDotCollection ={}

    for i = 1, EBottomHotKeyRows.Second_8 do
        local _NotShowString = _NotShowRowIndexString .. i
        self.m_NotShowDotCollection[i] = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get(_NotShowString):GetComponent(typeof(GameTools.UIExtension.UIImageChange))

    end

    ---建立透明背景底板 給拖曳換頁使用
    self.m_DragPoint = {}
    self.m_BackDragPanel = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&DragPanel")

    Button.AddListener( self.m_BackDragPanel,EventTriggerType.PointerDown, HotKey_Controller.OnClickDragZone)
    Button.AddListener( self.m_BackDragPanel,EventTriggerType.PointerUp, HotKey_Controller.OnRelease_Joystick)

    ---選單切換
    self.m_Btn_ExpandSecArea = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_ExpandSecArea")
    Button.AddListener(self.m_Btn_ExpandSecArea, EventTriggerType.PointerClick, BottomHotKey_Controller.OnClick_ExpandRow)
    self.OnClick_ExpandRow()

    ---快捷鍵母物件 換頁時反饋的滑動使用 
    self.m_Trans_SecHotKey = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&SecHotKey")
    

    self.SetBackSlidePanel(self.m_ExpandArea)

end

function BottomHotKey_Controller:Update()
    -- 從當前列 到 展開列
    for i = 1,  self.m_ExpandArea  do
        for j = EBottomHotKeyIdx.One, HOTKEY_MAX_IDX do

            local _HotKeyBtn = self.m_Table_BottomRowBtn[ i ][ j ]
            if _HotKeyBtn and _HotKeyBtn.m_HotKeyIcon and _HotKeyBtn.m_HotKeyIcon.m_Idx ~= 0 then
                _HotKeyBtn.m_HotKeyIcon:UpdateCD()
                --_HotKeyBtn:UpdateKeyCode()

                -- 已經結束 CD
                if not CDMgr.IsCD(_HotKeyBtn.m_HotKeyIcon.m_Type, _HotKeyBtn.m_HotKeyIcon.m_Idx) then
                    -- 如果有啟動自動使用
                    if _HotKeyBtn.m_HotKeyIcon.m_isAutoUse then
                        -- 使用熱鍵
                    end
                end
            end
        end
    end

end

function BottomHotKey_Controller.OnClick_ExpandRow()
    local _mySelf = BottomHotKey_Controller
    
    -- 到達最達高列數，回到一列
    if _mySelf.m_ExpandArea == HOTKEY_MAX_ROW then
        _mySelf.m_ExpandArea = 1

        for i = EBottomHotKeyRows.Second_2, HOTKEY_MAX_ROW do
            _mySelf.m_Table_HotKeyGroup[i].gameObject:SetActive(false)
        end
    else
        --一排變兩排 可能需要重新判斷 m_CurrentRow
        local _CurrentRow = _mySelf.m_CurrentRow
        
        -- 第一排的 m_CurrentRow = 1 ,2 必須變回  m_CurrentRow = 1  
        --除2後先計算Index
        _mySelf.m_CurrentRow = (math.floor ((_CurrentRow-1)/2) )*2+1 

        _mySelf.m_ExpandArea = _mySelf.m_ExpandArea + 1
        
        for i = EBottomHotKeyRows.Second_1, _mySelf.m_ExpandArea do
            _mySelf.m_Table_HotKeyGroup[i].gameObject:SetActive(true)

            for j = 1, EBottomHotKeyIdx.Six do
                _mySelf.m_Table_BottomRowBtn[i][j]:Refresh()
            end
        end
    end
    BottomHotKey_Controller.SetBackSlidePanel(_mySelf.m_ExpandArea)
    BottomHotKey_Controller.ChangeShowRowDot()

    --[[
    if Main_SubCtrl_SimpleChat.m_IsInitChatPosition then
        Main_SubCtrl_SimpleChat.SetChatPos(_mySelf.m_ExpandArea)
    end
    ]]
end

---背景的拖曳面板大小調整
---@param iRow number 當前要顯示幾排快捷鍵
function BottomHotKey_Controller.SetBackSlidePanel(iRow)
    local _mySelf = BottomHotKey_Controller

    if iRow ==1 then
        _mySelf.m_BackDragPanel.localScale = Vector3.one
    elseif iRow == HOTKEY_MAX_ROW then
        _mySelf.m_BackDragPanel.localScale = _LocalScaleOfTwoRowTypeDragPanel
    end
end

------底部快捷鍵換頁 修改m_CurrentRow
---@param iRightSlide bool  向右滑動 index往上加 反之往下減
function BottomHotKey_Controller.OnDrag_SwtichToNextRowMethod2(iRightSlide)
    
    local _mySelf = BottomHotKey_Controller
    local _SlideDirection = (iRightSlide) and 1 or -1
    if _mySelf.m_ExpandArea == HOTKEY_MAX_ROW then

        _mySelf.m_CurrentRow = _mySelf.m_CurrentRow + _SlideDirection*HOTKEY_MAX_ROW 

        if _mySelf.m_CurrentRow > EBottomHotKeyRows.Second_8 then
            _mySelf.m_CurrentRow = EBottomHotKeyRows.Second_1
        elseif _mySelf.m_CurrentRow < EBottomHotKeyRows.Second_1 then
            _mySelf.m_CurrentRow = EBottomHotKeyRows.Second_7
        end

    else
        _mySelf.m_CurrentRow = _mySelf.m_CurrentRow + _SlideDirection

        if _mySelf.m_CurrentRow > EBottomHotKeyRows.Second_8 then
            _mySelf.m_CurrentRow = EBottomHotKeyRows.Second_1
        elseif _mySelf.m_CurrentRow < EBottomHotKeyRows.Second_1 then
            _mySelf.m_CurrentRow = EBottomHotKeyRows.Second_8
        end
    end
end


---切換目前顯示的是第幾個 Row 記錄的資料
function BottomHotKey_Controller.ChangeShowRowDot()
   
    local _DataRow = BottomHotKey_Controller.m_CurrentRow

    for i = 1, EBottomHotKeyRows.Second_8 do

        if BottomHotKey_Controller.m_ExpandArea == HOTKEY_MAX_ROW then

            if i == _DataRow or i ==_DataRow+1  then
                BottomHotKey_Controller.m_NotShowDotCollection[i]:Trigger(ESelectionState.Selected)
            else
                BottomHotKey_Controller.m_NotShowDotCollection[i]:Trigger(ESelectionState.Normal)
            end

        else

            if i == _DataRow   then
                BottomHotKey_Controller.m_NotShowDotCollection[i]:Trigger(ESelectionState.Selected)
            else
                BottomHotKey_Controller.m_NotShowDotCollection[i]:Trigger(ESelectionState.Normal)
            end
        end
    end
end

---藉由 Idx 刷新熱鍵
function BottomHotKey_Controller:Refresh( iIdx, iNeedRefreshIdx0 )
    ---判斷有沒有被設定進快捷鍵
    if HotKeyMgr.m_Allow_Bottom_Area_Repeat_Set == false then
        local _HotKeyUnit = HotKeyMgr.GetBottomAreaHotKeyByIdx( iIdx )
        if not _HotKeyUnit then
            -- 可能是物品用完，直接更新所有的Hotkey顯示
            if iIdx == 0 and iNeedRefreshIdx0 then

                ---如果帳號 原本有將一個ID設定到多個快捷建 再切換成單一道具無法設定到多個快捷建時 
                ---會在 初始化的 HotKeyMgr.AnalysisProtocol 設定第2個相同的ID時發生錯誤 因為此時 尚未設定 self.m_ExpandArea  (此時 self.m_ExpandArea = nil)
                ---理論上 只會在內部測試的時候遇到
                if self.m_ExpandArea ~=nil then
                    for i = 1,  self.m_ExpandArea  do
                        for j = 1, HOTKEY_MAX_IDX do
                            self.m_Table_BottomRowBtn[i][j]:Refresh()
                        end
                    end
                end 
            end
            return
        end

        -- _HotKeyUnit資料內容基本上不會變動，因此不需要刷新，更換內容物行為在更換的時候執行
        -- 校正為顯示列 並須先判斷目前的顯示列隊應到那些 LinkRow 再取出data
        local _ViewRow = _HotKeyUnit.m_Row - self.m_CurrentRow +1
        -- 如果在顯示範圍內就刷新
        if _ViewRow > 0 and _ViewRow <= self.m_ExpandArea then
            self.m_Table_BottomRowBtn[ _ViewRow ][ _HotKeyUnit.m_Num ]:Refresh()
        end
----------------------------------------------------------------------------------------------
    else
        local _HotKeyUnitSets = HotKeyMgr.GetBottomAreaHotKeySetsByIdx( iIdx )

        ---取到空值表示 沒有設定在快捷鍵 不需要執行後續的刷新判斷
        if _HotKeyUnitSets == nil then
            return
        end

        for k , _TestHotKeyUnit in pairs(_HotKeyUnitSets) do
            if not _TestHotKeyUnit then
                -- 可能是物品用完，直接更新所有的Hotkey顯示
                if iIdx == 0 and iNeedRefreshIdx0 then
                    for i = self.m_CurrentRow, self.m_CurrentRow + self.m_ExpandArea -1 do
                        for j = 1, HOTKEY_MAX_IDX do
                            self.m_Table_BottomRowBtn[i][j]:Refresh()
                        end
                    end
                end
                return
            end

            --- _HotKeyUnit資料內容基本上不會變動，因此不需要刷新，更換內容物行為在更換的時候執行
            ---反推目前 _TestHotKeyUnit 的 m_Row 是否再用快捷鍵的可視範圍
            local _ViewRow = _TestHotKeyUnit.m_Row - self.m_CurrentRow +1

            -- 如果在顯示範圍內就刷新
            if _ViewRow > 0 and _ViewRow <= self.m_ExpandArea then
               
                self.m_Table_BottomRowBtn[ _ViewRow ][ _TestHotKeyUnit.m_Num ]:Refresh()
            end
        end
    end

end

---藉由位置刷新熱鍵
function BottomHotKey_Controller:RefreshByPos(iRow, iPos)
    -- 校正為顯示列
    local _ViewRow = iRow 
    -- 如果在顯示範圍內就刷新
    if _ViewRow > 0 and _ViewRow <= self.m_ExpandArea then
        self.m_Table_BottomRowBtn[ _ViewRow ][ iPos ]:Refresh( )
    end
end

---取出目前HotKeyMGr紀錄的DataRow 對應到Hotkey_Controller 的哪一個Row
---@param iDataRow number 要檢查的DataRow
---@return number 回傳當前對應到的Hotkey_Controller的哪一個Row
function BottomHotKey_Controller.GetRowIndexByLinkRowIndex(iDataRow)
    local _mySelf = BottomHotKey_Controller
    if _mySelf.m_ExpandArea == HOTKEY_MAX_ROW then

        for i = 1, HOTKEY_MAX_ROW do

            if _mySelf.m_Table_BottomRowBtn[i][1].m_Row + _mySelf.m_CurrentRow-1 == iDataRow then
                return i
            end
        end
    else
        if _mySelf.m_Table_BottomRowBtn[1][1].m_Row + _mySelf.m_CurrentRow-1 == iDataRow then
            return 1
        end
        
    end
    -- 如果是dummyicon 可能取不到 link row
    return -1
end

--endregion

--region 通用快捷按鍵
local CommonHotKey_Controller = {}
function CommonHotKey_Controller:Init()
    ---坐騎
    self.m_Btn_Mount = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_Mount")
    Button.AddListener(self.m_Btn_Mount, EventTriggerType.PointerClick, self.OnClick_Mount)
    InputMgr.RegistKey(HotKey_Controller, BattleSetting.m_CommonHotKey[ECommonHotkeyPos.SwitchTarget], CommonHotKey_Controller)

    ---切換目標
    self.m_Btn_SearchTarget = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_Target")
    Button.AddListener(self.m_Btn_SearchTarget, EventTriggerType.PointerClick, self.OnClick_SearchTarget)

    ---切換寵物
    self.m_Btn_PetFunc = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_PetFunc")
    Button.AddListener(self.m_Btn_PetFunc, EventTriggerType.PointerClick, self.OnClick_PetFunction)


end

---搜尋目標事件
function CommonHotKey_Controller.OnClick_SearchTarget()
    D.Log("OnClick_SearchTarget")
    local _RC_Target = SearchMgr.GetHostilityByTab()
    if _RC_Target ~= nil then
        SelectMgr.ChangeTarget(_RC_Target.gameObject)
    end
end

---寵物的相關快捷功能
function CommonHotKey_Controller.OnClick_PetFunction()
    D.Log("寵物功能")
end

---騎乘坐騎事件
function CommonHotKey_Controller.OnClick_Mount()
    D.Log("上下坐騎")
end

function CommonHotKey_Controller:UpdateKeyCode(iKey, iKeyState)
    if iKey == BattleSetting.m_CommonHotKey[ECommonHotkeyPos.SwitchTarget] then
        if iKeyState == EKeyCodeState.KeyUp then
            CommonHotKey_Controller.OnClick_SearchTarget()
        end
    end
end
--endregion


---藥水快捷鍵
-------------------------------------------------------------
local PotionHotKey_Controller = {}

local _RightPos_Potion = Vector3(-286,29.6,0)
local _LeftPos_Potion = Vector3(-346,29.6,0)
local _Sprite_Gear_AutoDringPageOn = "MainIcon_077_A"
local _Sprite_Gear_AutoDringPageOff = "MainIcon_077_D"


function PotionHotKey_Controller:Init()
    ---藥水快捷鍵 頁面提示點物件名稱前綴
    local _Prefix_PotionHotKey = "&RowDot_Portion_"
    ---藥水快捷鍵 按鍵物件名稱前綴
    local _Prefix_PotionBtn = "&Btn_Portion_"
    
    -- 顯示中的 PotionHotKeyBtn
    self.m_Table_PotionBtn = {}
    -- 顯示現在再第幾頁的圈圈
    self.m_Table_PotionPageHint = {}

    ---當前模式
    self.m_Mode = EPotionHotKeyParameters.EOperateMode.Normal

    ---總共有幾頁
    for i = 1, EPotionHotKeyParameters.m_PageAmount do
        self.m_Table_PotionPageHint[i] = {}
        local _Trans_PotionPageHint = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get(_Prefix_PotionHotKey..i)
        self.m_Table_PotionPageHint[i].m_UIImageChange = _Trans_PotionPageHint:GetComponent(typeof(GameTools.UIExtension.UIImageChange))
        self.m_Table_PotionPageHint[i].m_BtnEx = Button.New(_Trans_PotionPageHint.gameObject)
        Button.AddListener(self.m_Table_PotionPageHint[i].m_BtnEx, EventTriggerType.PointerClick, 
        function() 
            --如果是編輯模式則不執行
            if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
                return
            end
            
            ---需要動態 所以要先檢查 要左滑還是右滑
            local _IsRight = false

            if  PotionHotKey_Controller.m_CurrentPotionIndex == i then
                return
            end
            
            if PotionHotKey_Controller.m_CurrentPotionIndex == 1 then
                
                _IsRight = i == 2 and true or false
            elseif PotionHotKey_Controller.m_CurrentPotionIndex == 2 then
                _IsRight = i == 3 and true or false
            elseif PotionHotKey_Controller.m_CurrentPotionIndex == 3 then
                _IsRight = i == 1 and true or false
            end
            PotionHotKey_Controller.SlideToChangePage(_IsRight) 

        end)
    end

    ---RowDot 左右兩遍的用來切到上/下一頁的按鍵
    ---上一頁
    self.m_Btn_LastPage = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_LastPage")
    Button.AddListener( self.m_Btn_LastPage,EventTriggerType.PointerClick,
    function() 
        --如果是編輯模式則不執行
        if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
            return
        end
        PotionHotKey_Controller.SlideToChangePage(false) 
    end)
    ---下一頁
    self.m_Btn_NextPage = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_NextPage")
    Button.AddListener( self.m_Btn_NextPage,EventTriggerType.PointerClick,
    function() 
        --如果是編輯模式則不執行
        if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
            return
        end
        PotionHotKey_Controller.SlideToChangePage(true) 
    end)

    --- 跟切換頁面 有關的按鍵及提示圓圈 全部放在此物件下
    self.m_RowDotLayer = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&RowDotLayer").gameObject

    ---快捷鍵點擊方法
    local _PotionAreaHotKeyClick = function(iIndex)

        local _Result, _Reason
        ---取得現在使用的道具
        local _AutoDrinkSet = ClientSaveMgr.GetDataValue(EClientSaveDataType.AutoDrink,AutoDrink_Model.m_ClientSaveKey)
        
        local _PotiionID = _AutoDrinkSet.m_PortionGroup[self.m_CurrentPotionIndex].m_ItemIdx[iIndex]

        ---編輯模式時 切換自動喝水設定頁面
        ---一般模式時 根據是否有填入道具 使用道具或者開頁面
        if self.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
            GroupButton.OnPointerClickByIndex(AutoDrink_Controller.m_Group_SetPotion, iIndex)
            PotionHotKey_Controller.SetAllIconSelectStatus(false,iIndex)
            
        elseif self.m_Mode == EPotionHotKeyParameters.EOperateMode.Normal then
            
            if  _PotiionID == 0 then
                UIMgr.Open(AutoDrink_Controller,iIndex) 
                self.m_Mode = EPotionHotKeyParameters.EOperateMode.Edit
                PotionHotKey_Controller.SetAllIconSelectStatus(false,iIndex)
                PotionHotKey_Controller.ShowRawDotLayer(false)
                PotionHotKey_Controller.SetGearButtonImageAutoDrinkPage(true)
            else
                if not BagMgr.UseItembyItemIdx( _PotiionID, 1 ) then 
                    _Reason = "[HotKey] 快捷鍵使用道具失敗。ItemIdx: " .. _PotiionID
                    D.Log(_Reason)
                end
            end
        end             
    end
    -- 快捷鍵初始化
    -- 一頁五格
    for i = 1, EPotionHotKeyParameters.m_BtnAmount do
        self.m_Table_PotionBtn[i] = {}
        self.m_Table_PotionBtn[i].m_BtnParent = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get(string.Concat(_Prefix_PotionBtn, i))
        self.m_Table_PotionBtn[i].m_Obj_NoPotionHint = self.m_Table_PotionBtn[i].m_BtnParent:Find("Img_NoPotionHint").gameObject
        self.m_Table_PotionBtn[i].m_ItemIcon = IconMgr.NewItemIcon(0,self.m_Table_PotionBtn[i].m_BtnParent, 83.2,_PotionAreaHotKeyClick,i)
        self.m_Table_PotionBtn[i].m_ItemIcon.gameObject.transform:SetSiblingIndex(0)
        self.m_Table_PotionBtn[i].m_ItemIcon.m_AllowEmptyDrag = true
        self.m_Table_PotionBtn[i].m_ItemIcon:SetClickTwice(false)
        :AddEvent(BasicIcon.OnBasicDrag, EventTriggerType.Drag)
        :AddEvent(function(iIconSelf, iSender) -- 設定拖曳事件
                if not iIconSelf.m_DragType then
                    return
                end
                --如果是編輯模式則不執行
                if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
                    return
                end

                if iIconSelf.m_DragType == EDragType.Left then
                    PotionHotKey_Controller.SlideToChangePage(true)
                elseif iIconSelf.m_DragType == EDragType.Right then
                    PotionHotKey_Controller.SlideToChangePage(false)
                end
        
            end, EventTriggerType.EndDrag)
        ---提示這個按鍵正被選擇中的特效物件
        self.m_Table_PotionBtn[i].m_Obj_SelectOutLine = self.m_Table_PotionBtn[i].m_BtnParent:Find("Img_SelectOutLine").gameObject

        ---登記數字鍵盤快捷
        InputMgr.RegistKey(HotKey_Controller, EHotkeyArea.PotionArea .. "_" ..i, PotionHotKey_Controller)
        
    end

    ---建立透明背景底板 給拖曳換頁使用
    self.m_DragPoint = {}
    self.m_BackDragPanel = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&DragPanel_Portion")


    Button.AddListener( self.m_BackDragPanel,EventTriggerType.PointerDown,PotionHotKey_Controller.OnClickDragZone)
    Button.AddListener( self.m_BackDragPanel,EventTriggerType.PointerUp,PotionHotKey_Controller.OnRelease_EndDrag)


    ---開啟自動藥水頁面
    self.m_Btn_OpenAutoDrink = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&Btn_OpenAutoDrink")
    Button.AddListener(self.m_Btn_OpenAutoDrink, EventTriggerType.PointerClick, 
function() 
                    if UIMgr.IsVisible(AutoDrink_Controller) then
                        UIMgr.Close(AutoDrink_Controller)
                        self.m_Mode = EPotionHotKeyParameters.EOperateMode.Normal
                    else
                        UIMgr.Open(AutoDrink_Controller,EPotionType.HPPotion)
                        PotionHotKey_Controller.SetGearButtonImageAutoDrinkPage(true)
                        PotionHotKey_Controller.ShowRawDotLayer(false)
                        self.m_Mode = EPotionHotKeyParameters.EOperateMode.Edit
                    end 

                end)
    ---開啟自動喝水的按鈕圖片
    self.m_Img_OpenAutoDrink = HotKey_Controller.m_ViewRef.m_Dic_Image:Get("&Btn_OpenAutoDrink")

    ---快捷鍵母物件 換頁時反饋的滑動使用 
    self.m_Trans_PotionHotKey = HotKey_Controller.m_ViewRef.m_Dic_Trans:Get("&PotionHotKey")

    ---目前使用哪一頁的藥水快捷
    self.m_CurrentPotionIndex = 1
    local _AutoDrinkSet = ClientSaveMgr.GetDataValue(EClientSaveDataType.AutoDrink,AutoDrink_Model.m_ClientSaveKey)
    
    if _AutoDrinkSet then
        self.m_CurrentPotionIndex = _AutoDrinkSet.m_CurIndex
    end

    PotionHotKey_Controller.UpdateUICondition()
end

---自動喝水頁面被開/關時 藥水快捷鍵區的 齒輪按鍵要切換圖片
---@param iIsOpen boolean 自動會水介面是否開啟
function PotionHotKey_Controller.SetGearButtonImageAutoDrinkPage(iIsOpen)
    local _UseSpriteName = (iIsOpen) and _Sprite_Gear_AutoDringPageOn or _Sprite_Gear_AutoDringPageOff
    SpriteMgr.Load(_UseSpriteName, PotionHotKey_Controller.m_Img_OpenAutoDrink)
end

---根據再哪個藥水分頁 切換頁面提示藍點
function PotionHotKey_Controller.ChangeShowRowDot()
   
    for i = 1, EPotionHotKeyParameters.m_PageAmount do
        if i == PotionHotKey_Controller.m_CurrentPotionIndex   then
            PotionHotKey_Controller.m_Table_PotionPageHint[i].m_UIImageChange:Trigger(ESelectionState.Selected)
        else
            PotionHotKey_Controller.m_Table_PotionPageHint[i].m_UIImageChange:Trigger(ESelectionState.Normal)
        end
    end
    
end

---點擊開始拖曳 紀錄初始點位置
---@param iEventData 被點擊UI
function PotionHotKey_Controller.OnClickDragZone(iEventData)

    if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
        return
    end

    local _TableCount = table.Count(BottomHotKey_Controller.m_DragPoint)
    PotionHotKey_Controller.m_DragPoint[_TableCount+1] = iEventData.position
end

---放開拖曳 如果超過兩個點被登記在m_DragPoint 不執行換頁並清空
---@param iEventData 被點擊的UI
function PotionHotKey_Controller.OnRelease_EndDrag(iEventData)
    if PotionHotKey_Controller.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
        return
    end

    if table.Count(PotionHotKey_Controller.m_DragPoint) == 1 then
        local _IsSlideToRight = (iEventData.position.x < PotionHotKey_Controller.m_DragPoint[1].x)
        local _DeltaX = math.abs(iEventData.position.x - PotionHotKey_Controller.m_DragPoint[1].x)

        if _DeltaX < 0.1 then
            return
        end
        PotionHotKey_Controller.SlideToChangePage( _IsSlideToRight)
    end
    
    table.Clear(PotionHotKey_Controller.m_DragPoint)
end

---底部快捷鍵換頁 反饋LeanTween
---@param iRightSlide bool  向右滑動 index往下減 反之往上加
function  PotionHotKey_Controller.SlideToChangePage( iRightSlide)
    local _OrigiPos = PotionHotKey_Controller.m_Trans_PotionHotKey.localPosition
    local FLOATING_END_POS = (iRightSlide) and _LeftPos_Potion or  _RightPos_Potion
    local _CanvasGroup = PotionHotKey_Controller.m_Trans_PotionHotKey.gameObject:GetComponent( "CanvasGroup")
    
    local _Seq = LeanTween.sequence()
    _Seq:append(
    LeanTween.move(PotionHotKey_Controller.m_Trans_PotionHotKey,FLOATING_END_POS,_FLOATING_TWEEN_PERIOD)
    :setOnUpdate( 
        System.Action_float(
        function(iValue)
            ---滑動的前期不做淡出處理 讓滑動的效果變明顯 因此前1/3週期不做變淡處理
            local _AlphaValue = iValue<0.33 and 1  or (1-iValue)/0.67
            _CanvasGroup.alpha = (_AlphaValue)
         end
        ))
    :setOnComplete(System.Action(
    function() 
        PotionHotKey_Controller.m_Trans_PotionHotKey.localPosition = _OrigiPos
        PotionHotKey_Controller.ChangePotionIndex(iRightSlide)
        PotionHotKey_Controller.UpdateUICondition()

        AutoDrink_Model.SetGroupPotionIcon(false)

    end)
    ))
    
    _Seq:append(LeanTween.alphaCanvas(_CanvasGroup, 1,_FLOATING_TWEEN_PERIOD):setEase(LeanTweenType.easeOutQuint)) 

end

---切換目前藥水的分頁Index 可指定特定index 或者左/右滑動 判斷下個index 或者直接設定 iSpecificPage 時則忽視 iRightSlide
---@param iRightSlide bool  向右滑動 index往上加 反之往下減
---@param iSpecificPage number 設定到特定頁面
function  PotionHotKey_Controller.ChangePotionIndex(iRightSlide,iSpecificPage)

    if iSpecificPage then
        PotionHotKey_Controller.m_CurrentPotionIndex =iSpecificPage
    else
        local _AddIndex = iRightSlide and 1 or -1
        PotionHotKey_Controller.m_CurrentPotionIndex = PotionHotKey_Controller.m_CurrentPotionIndex + _AddIndex
    end

    if PotionHotKey_Controller.m_CurrentPotionIndex > EPotionHotKeyParameters.m_PageAmount then
        PotionHotKey_Controller.m_CurrentPotionIndex = 1
    end

    if PotionHotKey_Controller.m_CurrentPotionIndex < 1 then
        PotionHotKey_Controller.m_CurrentPotionIndex = EPotionHotKeyParameters.m_PageAmount
    end

    local _AutoDrinkSetting = AutoDrink_Model.GetSettingData()

    if _AutoDrinkSetting.m_CurIndex ~= PotionHotKey_Controller.m_CurrentPotionIndex then
        _AutoDrinkSetting.m_CurIndex = PotionHotKey_Controller.m_CurrentPotionIndex
        AutoDrink_Model.SetSettingData(_AutoDrinkSetting, false)
    end
    
end

---刷新按鍵內容 目前在第幾頁的頁面提示
function  PotionHotKey_Controller.UpdateUICondition()

    ---確保介面紀錄的index 跟 ClientSave 紀錄的要相同 不同的話使用ClientSave的
    local _AutoDrinkSet = ClientSaveMgr.GetDataValue(EClientSaveDataType.AutoDrink,AutoDrink_Model.m_ClientSaveKey)
    if PotionHotKey_Controller.m_CurrentPotionIndex ~= _AutoDrinkSet.m_CurIndex then
        PotionHotKey_Controller.m_CurrentPotionIndex = _AutoDrinkSet.m_CurIndex
    end

    PotionHotKey_Controller.RenewBtnData()
    PotionHotKey_Controller.ChangeShowRowDot()
end

---刷新按鍵內容
function  PotionHotKey_Controller.RenewBtnData()
    
    local _AutoDrinkSet = ClientSaveMgr.GetDataValue(EClientSaveDataType.AutoDrink,AutoDrink_Model.m_ClientSaveKey)
    local _PotionIDTable = {}

    local _NeedReSetAutoDrinkData = false

    if _AutoDrinkSet then
        for i = 1, table.Count(_AutoDrinkSet.m_PortionGroup) do
            for j = 1, table.Count(_AutoDrinkSet.m_PortionGroup[i].m_ItemIdx) do
                if i == PotionHotKey_Controller.m_CurrentPotionIndex then
                    local _AutoDrinkData = {}
                    local _CheckState = _AutoDrinkSet.m_PortionGroup[i].m_ItemState[j] == "Normal" and EItemStatus.Normal or EItemStatus.Binding
                    local _PlayerItemAmount = BagMgr.GetItemInBagAmount(_AutoDrinkSet.m_PortionGroup[i].m_ItemIdx[j],{_CheckState})

                    ---如果道具數量為0 代表玩家已經將道具使用完畢
                    if _PlayerItemAmount == 0  then
                        ---只取將編號設定成0  不修改是否自動使用狀態
                        _AutoDrinkSet.m_PortionGroup[i].m_ItemIdx[j] = 0
                        _NeedReSetAutoDrinkData = true
                    end

                    _AutoDrinkData.m_ItemIdx = _AutoDrinkSet.m_PortionGroup[i].m_ItemIdx[j]
                    _AutoDrinkData.m_IsOn = _AutoDrinkSet.m_PortionGroup[i].m_IsOn[j]
                    _AutoDrinkData.m_ItemState = _AutoDrinkSet.m_PortionGroup[i].m_ItemState[j]
                    
                    table.insert(_PotionIDTable,_AutoDrinkData)
                end
            end
        end
    end

    if _NeedReSetAutoDrinkData then
        ClientSaveMgr.ChangeDataValue(EClientSaveDataType.AutoDrink, AutoDrink_Model.m_ClientSaveKey,_AutoDrinkSet)
    end

    for i = 1, EPotionHotKeyParameters.m_BtnAmount do
        
        local _CheckState = _PotionIDTable[i].m_ItemState == "Normal" and EItemStatus.Normal or EItemStatus.Binding
        local _ItemCount = BagMgr.GetItemInBagAmount(_PotionIDTable[i].m_ItemIdx,{_CheckState})

        PotionHotKey_Controller.m_Table_PotionBtn[i].m_ItemIcon:RefreshIcon( _PotionIDTable[i].m_ItemIdx)
        PotionHotKey_Controller.m_Table_PotionBtn[i].m_ItemIcon:SetCount(_ItemCount)
        PotionHotKey_Controller.m_Table_PotionBtn[i].m_Obj_NoPotionHint:SetActive(_PotionIDTable[i].m_ItemIdx == 0)
        local _IsAutoUse = _PotionIDTable[i].m_IsOn and _PotionIDTable[i].m_ItemIdx ~= 0
        if _IsAutoUse == true then
            PotionHotKey_Controller.m_Table_PotionBtn[i].m_ItemIcon:SetOtherFrameImage(EOtherItemIconFrameType.AutoUse)
        else
            PotionHotKey_Controller.m_Table_PotionBtn[i].m_ItemIcon:SetOtherFrameImage(EOtherItemIconFrameType.None)
        end
    end
end

---快捷鍵外框顯示設定
---@param iCloseAll bool 是否強制關閉所有
---@param iIndex number 如果不是關閉所有 要讓哪一個的外框閃爍(其他都關閉)
function PotionHotKey_Controller.SetAllIconSelectStatus(iCloseAll,iIndex)
    --self.m_Table_PotionBtn[i].m_Obj_SelectOutLine
    if iCloseAll then
        for i = 1, EPotionHotKeyParameters.m_BtnAmount do
            PotionHotKey_Controller.m_Table_PotionBtn[i].m_Obj_SelectOutLine:SetActive(false)
        end
    else
        for i = 1, EPotionHotKeyParameters.m_BtnAmount do
            PotionHotKey_Controller.m_Table_PotionBtn[i].m_Obj_SelectOutLine:SetActive(i == iIndex)
        end
    end

end


---開啟/關閉 RowDotLayer
---@param iShowLayer bool 開啟/關閉 RowDotLayer
function PotionHotKey_Controller.ShowRawDotLayer(iShowLayer)

    PotionHotKey_Controller.m_RowDotLayer:SetActive(iShowLayer)

end

---藥水區快捷鍵 也需要提示CD 使用中
function PotionHotKey_Controller:Update()
    -- 從當前列 到 展開列
    for i = 1, EPotionHotKeyParameters.m_BtnAmount do
        PotionHotKey_Controller.m_Table_PotionBtn[i].m_ItemIcon:UpdateCD()
    end

end


---設定方法 讓數字鍵可以觸發快捷鍵點擊功能
function PotionHotKey_Controller:UpdateKeyCode(iKey, iKeyState)
        
    if iKeyState ~= EKeyCodeState.KeyDown then
        --檢查用 平時註解掉
        --D.Log("不是按下 全部檔掉")
        return
    end
    local _Result, _Reason
    ---取得現在使用的道具
    local _AutoDrinkSet = ClientSaveMgr.GetDataValue(EClientSaveDataType.AutoDrink,AutoDrink_Model.m_ClientSaveKey)
    
    local _num = tonumber(iKey:match("_(%d+)$"))
    local _PotiionID = _AutoDrinkSet.m_PortionGroup[self.m_CurrentPotionIndex].m_ItemIdx[_num]

    ---編輯模式時 不允許使用快捷鍵修改
    ---一般模式時 根據是否有填入道具 使用道具 或者 不做任何操作
    if self.m_Mode == EPotionHotKeyParameters.EOperateMode.Edit then
        --檢查用 平時註解掉
        --D.Log("編輯模式不允許使用快捷鍵")
        return
    elseif self.m_Mode == EPotionHotKeyParameters.EOperateMode.Normal then
        
        if  _PotiionID == 0 then
            --檢查用 平時註解掉
            --D.Log("藥水ID = 0 無法使用")
            return
        else
            if not BagMgr.UseItembyItemIdx( _PotiionID, 1 ) then 
                _Reason = "[HotKey] 快捷鍵使用道具失敗。ItemIdx: " .. _PotiionID
                D.Log(_Reason)
            end
        end
    end             
end

--endregion

this.m_Btn_SearchTarget = nil

---次區域熱鍵切換狀態, 預設開一排初始值為零讓他跑成1, 後續需要撈玩家設定檔
--- 0:全關
--- 1:開一排
--- 2:開兩排
--- 4:開四排
--- 5:開八排
--this.m_ExpandSecAreaType = 0
this.m_Btn_ExpandSecArea = nil
this.m_Table_SecHotKeyGroup = {}
---顯示戰鬥 UI 的計時器
this.m_Timer_ShowFightUI = 0

---初始化
function HotKey_Controller.Init()
    -- 損毀技能圖要先讀取才能初始化 BattleAtk_Controller
    this.m_Obj_Broken = this.m_ViewRef.m_Dic_Trans:Get("&Obj_Broken")
    this.m_Obj_Broken.gameObject:SetActive(false)

    ---取得UI ref
    --region Get hotkey UIRef
    -- 戰鬥快捷按鈕初始化
    BattleAtk_Controller:Init()
    -- 底部快捷按鈕初始化
    BottomHotKey_Controller:Init()
    -- 通用快捷按鈕功能初始化
    CommonHotKey_Controller:Init()
    --- 藥水快捷鍵區 初始化
    PotionHotKey_Controller:Init()
    --endregion

    ---Hover 物件
    if Extension.IsUnityObjectNull(this.m_Trans_HoverObj) then
        this.m_Trans_HoverObj = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Hover")
        local _TempObj = this.m_ViewRef.m_Dic_Trans:Get("&Image_BG_Hover")
        if _TempObj ~= nil then
            this.m_IconAreaSize = _TempObj:GetComponent( typeof( RectTransform ) ).rect.width / 2
        end
    end
    this.m_Trans_HoverObj.gameObject:SetActive(false)

    if Extension.IsUnityObjectNull(this.m_Trans_Hover) then
        this.m_Trans_Hover = this.m_ViewRef.m_Dic_Image:Get("&Image_Hover")
    end

    if Extension.IsUnityObjectNull(this.m_Image_Hover_Cancel) then
        this.m_Image_Hover_Cancel = this.m_ViewRef.m_Dic_Image:Get("&Image_Hover_Cancel")
        this.m_UIEvent_Hover_Cancel = Button.New(Extension.AddMissingComponent( this.m_Image_Hover_Cancel.gameObject, typeof( ButtonEx ) ))
        this.m_UIEvent_Hover_Cancel:AddListener(EventTriggerType.PointerEnter, function() this:OnEnterCancel() end)
        this.m_UIEvent_Hover_Cancel:AddListener(EventTriggerType.PointerExit, function() this:OnExitCancel() end)
        this.m_Image_Hover_Cancel.gameObject:SetActive(false)
    end
    
    this.m_SecArea = this.m_ViewRef.m_Dic_Trans:Get("&SecArea").gameObject

    ---掛機按鈕
    this.m_Btn_AutoBattle = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Btn_AutoBattle"))
    Button.AddListener(this.m_Btn_AutoBattle, EventTriggerType.PointerClick, HotKey_Controller.OnClick_AutoBattle)
    this.m_Btn_AutoBattle:SetSelect(not (AutoBattleMgr.m_AutoBattleState == AutoBattleMgr.EAutoBattleState.Stop))
    this.m_Tmp_AutoBattleState = this.m_ViewRef.m_Dic_TMPText:Get("&AutoBattleState")
    if ProjectMgr.IsRelease() then
        this.m_Tmp_AutoBattleState.gameObject:SetActive(false)
    else
        this.m_Tmp_AutoBattleState.gameObject:SetActive(true)
        this.m_Tmp_AutoBattleState.text = table.GetKey(AutoBattleMgr.EAutoBattleState, AutoBattleMgr.m_AutoBattleState)
    end
    
    this.m_IsInited = true

    ---該HotKey右半版介面
    this.m_Obj_Main = this.m_ViewRef.m_Dic_Trans:Get("&Main").gameObject

    BottomHotKey_Controller.ChangeShowRowDot()
end

function HotKey_Controller.Update()
    ---戰鬥區塊自動收回
    -- if this.m_Timer_ShowFightUI > 0 then
    --     this.m_Timer_ShowFightUI = this.m_Timer_ShowFightUI - HEMTimeMgr.m_DeltaTime
    --     if this.m_Timer_ShowFightUI <= 0 then
    --         this.m_Timer_ShowFightUI = 0
    --         this.m_Trans_Group_Fight.gameObject:SetActive(true)
    --         this.m_Trans_Group_Normal.gameObject:SetActive(true)
    --     end
    -- end

    ---快捷CD刷新
    BattleAtk_Controller:Update()
    
    BottomHotKey_Controller:Update()
    
    PotionHotKey_Controller:Update()
end

function HotKey_Controller.SetPhotoMode(iIsActive)
    this.m_SecArea:SetActive(iIsActive)
end

function HotKey_Controller.IsShowRightPage(iIsSwitch)
    if this.m_Obj_Main ~= nil then
        this.m_Obj_Main:SetActive(iIsSwitch)
    end
end

function HotKey_Controller.Open()
    HotKey_Controller.RefreshBattleAtkHotKey()
    PotionHotKey_Controller.UpdateUICondition()
    return true
end

function HotKey_Controller.Close()
    return true
end

--- 高亮快捷鍵區塊
function HotKey_Controller.LightUpBattleAtk(iIcon, _isLightUpBattleAtk)
    if not iIcon then
        --todo: 關掉高亮效果
        return
    end
end

--- HotKeyController的Icon模式
---@class HotKey_Controller.EHotKeyMode
HotKey_Controller.EHotKeyMode = {
    CommonMode = 1,
    EditMode = 2
}

function HotKey_Controller.SwitchMode(iMode)
    for i = EBottomHotKeyRows.Second_1, HOTKEY_MAX_ROW do
        -- 一列六格
        for k, v in pairs(EBottomHotKeyIdx) do
            BottomHotKey_Controller.m_Table_BottomRowBtn[i][v]:SwitchMode(iMode)
        end
    end
 
    -- 快捷介面當前模式
    HotKey_Controller.m_CurrentMode = iMode
end

--region 刷新熱鍵

--- 刷新戰鬥熱鍵
function HotKey_Controller.RefreshBattleAtkHotKey()
    BattleAtk_Controller:Refresh()
end

---底部快捷鍵換頁 反饋LeanTween
---@param iRightSlide bool  向右滑動 index往下減 反之往上加
function  HotKey_Controller.SlideToChangePage( iRightSlide)
    local _OrigiPos = BottomHotKey_Controller.m_Trans_SecHotKey.localPosition
    local FLOATING_END_POS = (iRightSlide) and _LeftPos or  _RightPos
    local _CanvasGroup = BottomHotKey_Controller.m_Trans_SecHotKey.gameObject:GetComponent( "CanvasGroup")
    local _Seq = LeanTween.sequence()
    _Seq:append(
    LeanTween.move(BottomHotKey_Controller.m_Trans_SecHotKey,FLOATING_END_POS,_FLOATING_TWEEN_PERIOD)
    :setOnUpdate( 
        System.Action_float(
        function(iValue)
            ---滑動的前期不做淡出處理 讓滑動的效果變明顯 因此前1/3週期不做變淡處理
            local _AlphaValue = iValue<0.33 and 1  or (1-iValue)/0.67
            _CanvasGroup.alpha = (_AlphaValue)
         end
        ))
    :setOnComplete(System.Action(
    function() 
        BottomHotKey_Controller.m_Trans_SecHotKey.localPosition = _OrigiPos
        HotKey_Controller.RenewDataDueToSlidePage( iRightSlide)
    end)
    ))
    
    -- 有 m_HotKeyUnit 資料的就是選快捷列的，就把 Click 清空，讓他可以點下一個快捷列 icon 來交換
    if HotKeyMgr.m_SelectIconAy ~= nil and HotKeyMgr.m_SelectIconAy[1].m_HotKeyUnit then
        IconMgr.CancelAllClick()
        UIMgr.OpenIconName(false)
    end

    _Seq:append(LeanTween.alphaCanvas(_CanvasGroup, 1,_FLOATING_TWEEN_PERIOD):setEase(LeanTweenType.easeOutQuint)) 
end

---底部快捷鍵換頁 訊息切換 資料刷新
---@param iRightSlide bool  向右滑動 index往下減 反之往上加
function  HotKey_Controller.RenewDataDueToSlidePage( iRightSlide)    
    ---刷新 m_CurrentRow
    BottomHotKey_Controller.OnDrag_SwtichToNextRowMethod2(iRightSlide)

    ---當前顯示幾排
    local _RowAmount = BottomHotKey_Controller.m_ExpandArea
    
    --- Icon刷新
    for i = 1, _RowAmount do
        for j = 1, EBottomHotKeyIdx.Six  do
            BottomHotKey_Controller.m_Table_BottomRowBtn[i][j]:Refresh()
        end
    end

    BottomHotKey_Controller.ChangeShowRowDot()
end


--- 刷新底部快捷鍵
---@param iIdx number ItemID|WugongID
---@param iNeedRefreshIdx0 boolean idx為0時是否要刷新，因為使用最後一個道具的時候，S端會傳idx0進來，如果不刷新會導致物品顯示殘留
function HotKey_Controller.RefreshBottomHotKey( iIdx, iNeedRefreshIdx0 )
    BottomHotKey_Controller:Refresh(iIdx, iNeedRefreshIdx0)
end

--- 刷新底部快捷鍵
---@param iIdx number ItemID|WugongID
---@param iNeedRefreshIdx0 boolean idx為0時是否要刷新，因為使用最後一個道具的時候，S端會傳idx0進來，如果不刷新會導致物品顯示殘留
function HotKey_Controller.RefreshAllBottomHotKey()
    for i = 1, BottomHotKey_Controller.m_ExpandArea  do
        for j = EBottomHotKeyIdx.One, HOTKEY_MAX_IDX do
            BottomHotKey_Controller.m_Table_BottomRowBtn[ i ][ j ]:Refresh()
        end
    end
end

function HotKey_Controller.RefreshBottomHotKey_ByPos(iRow, iPos)
    BottomHotKey_Controller:RefreshByPos(iRow, iPos)
end

---刷新底部快捷鍵 高亮顯示狀態
function HotKey_Controller.SetAllHotKeyBtnHighLightStatus( iISNeedHightLight)
    for i = 1, BottomHotKey_Controller.m_ExpandArea  do
        for j = EBottomHotKeyIdx.One, HOTKEY_MAX_IDX do
            local _HotKeyBtn = BottomHotKey_Controller.m_Table_BottomRowBtn[ i ][ j ]
            _HotKeyBtn:ChangeHotKeyBtnHighLightStatus(iISNeedHightLight)
        end
    end

end

---關閉所有選擇框
function HotKey_Controller.CloseAllSelectHint()
    for i = 1, BottomHotKey_Controller.m_ExpandArea do
        for j = EBottomHotKeyIdx.One, HOTKEY_MAX_IDX do
            BottomHotKey_Controller.m_Table_BottomRowBtn[i][j]:ShowSelectIconByUnit(false)
        end
    end
end

--endregion

--region Hover
function HotKey_Controller.OnPointerDown(iHoverPos, iEventData)
    this.OnExitCancel()
    -- this.m_Trans_HoverObj.gameObject:SetActive(true)
    this.m_Trans_HoverObj.transform.position = iHoverPos
    -- this.m_Image_Hover_Cancel.gameObject:SetActive(true)
    
    -- 點下去的坐標
    this.m_PointerDownPos = iEventData
end

function HotKey_Controller.OnPointerUp(iEventData)
    this.m_Trans_HoverObj.gameObject:SetActive(false)
    this.m_Image_Hover_Cancel.gameObject:SetActive(false)
    this.m_PointerDownPos = nil
end

function HotKey_Controller.OnEnterCancel()
    this.m_Image_Hover_Cancel.color = Color.Red
    HotKeyMgr.SetCancelSkill(true)
end

function HotKey_Controller.OnExitCancel()
    this.m_Image_Hover_Cancel.color = Color.White
    HotKeyMgr.SetCancelSkill(false)
end

function HotKey_Controller.OnUpdateHover(iPos)
    -- 要滑動超過一定距離才會出現蘑菇頭
    if Vector2.Distance(iPos, this.m_PointerDownPos) > BattleSetting.m_DirectionDistance then
        this.m_Trans_HoverObj.gameObject:SetActive(true)
        this.m_Image_Hover_Cancel.gameObject:SetActive(true)
    end

    -- 戰鬥按紐搖桿
    if(iPos ~= nil) then
        this.m_Trans_Hover.rectTransform.anchoredPosition = Vector2.ClampMagnitude(iPos, this.m_IconAreaSize)
    end
end
--endregion Hover

--- 點擊自動戰鬥
function HotKey_Controller.OnClick_AutoBattle()
    AutoBattleMgr.SetAutoBattle(this.m_Btn_AutoBattle:IsSelect() and AutoBattleMgr.EAutoBattleState.Stop or AutoBattleMgr.EAutoBattleState.Play)
end

---中間底部快捷鍵區域 透明背景板拖曳相關功能

---點擊開始拖曳 紀錄初始點位置
---@param iEventData 被點擊UI
function HotKey_Controller.OnClickDragZone(iEventData)
    local _TableCount = table.Count(BottomHotKey_Controller.m_DragPoint)
    BottomHotKey_Controller.m_DragPoint[_TableCount+1] = iEventData.position
end

---放開拖曳 如果超過兩個點被登記在m_DragPoint 不執行換頁並清空
---@param iEventData 被點擊的UI
function HotKey_Controller.OnRelease_Joystick(iEventData)
    if table.Count(BottomHotKey_Controller.m_DragPoint) == 1 then
        local _IsSlideToRight = (iEventData.position.x < BottomHotKey_Controller.m_DragPoint[1].x)
        HotKey_Controller.SlideToChangePage(_IsSlideToRight)
    end
    
    table.Clear(BottomHotKey_Controller.m_DragPoint)
end

---取得目前的 m_CurrentRow 讓資料可以填入
function HotKey_Controller.GetBottomHotkeyCurrentRow()
    return  BottomHotKey_Controller.m_CurrentRow
end

---任務追蹤用的自動戰鬥
function HotKey_Controller.QuestAutoFight(iState)
    if iState == true then
        this.m_Btn_AutoBattle:SetSelect(true)
        AutoBattleMgr.SetAutoBattle(AutoBattleMgr.EAutoBattleState.Play)
    else
        this.m_Btn_AutoBattle:SetSelect(false)
        AutoBattleMgr.SetAutoBattle(AutoBattleMgr.EAutoBattleState.Stop)
    end
end


---region 藥水快捷相關設定

---設定藥水區快捷鍵操作模式
---@param iMode EPotionHotKeyParameters.EOperateMode 藥水快捷的操作mode
function HotKey_Controller.ChangePotionHotKeyPageMode(iMode)
    PotionHotKey_Controller.m_Mode = iMode
end

---關閉自動喝水設定介面後 快捷鍵這邊需要同步處理的事情
function HotKey_Controller.CloseAutoDrinkCallBack()
    PotionHotKey_Controller.SetAllIconSelectStatus(true,1)
    HotKey_Controller.ChangePotionHotKeyPageMode(EPotionHotKeyParameters.EOperateMode.Normal)
    PotionHotKey_Controller.ShowRawDotLayer(true)
    PotionHotKey_Controller.SetGearButtonImageAutoDrinkPage(false)
end

---提拱給外部使用 刷新藥水區快捷鍵
function HotKey_Controller.RenewPotionHotKey()
    PotionHotKey_Controller.RenewBtnData()
end

---提拱給外部使用 刷新藥水區選擇提示框
---@param iPotionType number 藥水類型
function HotKey_Controller.RenewPotionHotKeyOutLineHint(iPotionType)
    PotionHotKey_Controller.SetAllIconSelectStatus(false,iPotionType)
end
---end region
