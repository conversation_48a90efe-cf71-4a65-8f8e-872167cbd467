fileFormatVersion: 2
guid: b7c4eb45c7a627b4aae457ef4c8be678
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Bip001 Head
  - first:
      1: 100002
    second: Bip001 Spine1
  - first:
      1: 100004
    second: //RootNode
  - first:
      1: 100006
    second: Viper_Back_01_01SHJnt
  - first:
      1: 100008
    second: Viper_Back_01_02SHJnt
  - first:
      1: 100010
    second: Viper_Back_01_03SHJnt
  - first:
      1: 100012
    second: <PERSON>_Back_01_04SHJnt
  - first:
      1: 100014
    second: Viper_Back_01_05SHJnt
  - first:
      1: 100016
    second: Viper_Back_01_06SHJnt
  - first:
      1: 100018
    second: Viper_Back_01_07SHJnt
  - first:
      1: 100020
    second: Viper_Back_01_08SHJnt
  - first:
      1: 100022
    second: Viper_Back_01_09SHJnt
  - first:
      1: 100024
    second: Viper_Back_01_10SHJnt
  - first:
      1: 100026
    second: Viper_Front_01_01SHJnt
  - first:
      1: 100028
    second: Viper_Front_01_02SHJnt
  - first:
      1: 100030
    second: Viper_Front_01_03SHJnt
  - first:
      1: 100032
    second: Viper_Front_01_04SHJnt
  - first:
      1: 100034
    second: Viper_Front_01_05SHJnt
  - first:
      1: 100036
    second: Viper_Front_01_07SHJnt
  - first:
      1: 100038
    second: Viper_Front_01_08SHJnt
  - first:
      1: 100040
    second: Viper_Front_01_09SHJnt
  - first:
      1: 100042
    second: Viper_Front_01_10SHJnt
  - first:
      1: 100044
    second: Viper_Front_01_11SHJnt
  - first:
      1: 100046
    second: Viper_Head_JawEndSHJnt
  - first:
      1: 100048
    second: Viper_Head_JawSHJnt
  - first:
      1: 100050
    second: Viper_Head_TopSHJnt
  - first:
      1: 100052
    second: Viper_l_Fang_01_01SHJnt
  - first:
      1: 100054
    second: Viper_MAINSHJnt
  - first:
      1: 100056
    second: Viper_Neck_01SHJnt
  - first:
      1: 100058
    second: Viper_Neck_02SHJnt
  - first:
      1: 100060
    second: Viper_r_Fang_01_01SHJnt
  - first:
      1: 100062
    second: Viper_ROOTSHJnt
  - first:
      1: 100064
    second: Viper_Tongue_01_01SHJnt
  - first:
      1: 100066
    second: Viper_Tongue_01_02SHJnt
  - first:
      1: 100068
    second: Viper_Tongue_01_03SHJnt
  - first:
      1: 100070
    second: Viper_Tongue_01_04SHJnt
  - first:
      4: 400000
    second: Bip001 Head
  - first:
      4: 400002
    second: Bip001 Spine1
  - first:
      4: 400004
    second: //RootNode
  - first:
      4: 400006
    second: Viper_Back_01_01SHJnt
  - first:
      4: 400008
    second: Viper_Back_01_02SHJnt
  - first:
      4: 400010
    second: Viper_Back_01_03SHJnt
  - first:
      4: 400012
    second: Viper_Back_01_04SHJnt
  - first:
      4: 400014
    second: Viper_Back_01_05SHJnt
  - first:
      4: 400016
    second: Viper_Back_01_06SHJnt
  - first:
      4: 400018
    second: Viper_Back_01_07SHJnt
  - first:
      4: 400020
    second: Viper_Back_01_08SHJnt
  - first:
      4: 400022
    second: Viper_Back_01_09SHJnt
  - first:
      4: 400024
    second: Viper_Back_01_10SHJnt
  - first:
      4: 400026
    second: Viper_Front_01_01SHJnt
  - first:
      4: 400028
    second: Viper_Front_01_02SHJnt
  - first:
      4: 400030
    second: Viper_Front_01_03SHJnt
  - first:
      4: 400032
    second: Viper_Front_01_04SHJnt
  - first:
      4: 400034
    second: Viper_Front_01_05SHJnt
  - first:
      4: 400036
    second: Viper_Front_01_07SHJnt
  - first:
      4: 400038
    second: Viper_Front_01_08SHJnt
  - first:
      4: 400040
    second: Viper_Front_01_09SHJnt
  - first:
      4: 400042
    second: Viper_Front_01_10SHJnt
  - first:
      4: 400044
    second: Viper_Front_01_11SHJnt
  - first:
      4: 400046
    second: Viper_Head_JawEndSHJnt
  - first:
      4: 400048
    second: Viper_Head_JawSHJnt
  - first:
      4: 400050
    second: Viper_Head_TopSHJnt
  - first:
      4: 400052
    second: Viper_l_Fang_01_01SHJnt
  - first:
      4: 400054
    second: Viper_MAINSHJnt
  - first:
      4: 400056
    second: Viper_Neck_01SHJnt
  - first:
      4: 400058
    second: Viper_Neck_02SHJnt
  - first:
      4: 400060
    second: Viper_r_Fang_01_01SHJnt
  - first:
      4: 400062
    second: Viper_ROOTSHJnt
  - first:
      4: 400064
    second: Viper_Tongue_01_01SHJnt
  - first:
      4: 400066
    second: Viper_Tongue_01_02SHJnt
  - first:
      4: 400068
    second: Viper_Tongue_01_03SHJnt
  - first:
      4: 400070
    second: Viper_Tongue_01_04SHJnt
  - first:
      74: 7400000
    second: M_0801_Attack_01
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0801_Attack_01
      takeName: M_0801_Attack_01
      internalID: 0
      firstFrame: 1
      lastFrame: 50
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 2bdecec6365c3b0438b0024c62c5587e,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
