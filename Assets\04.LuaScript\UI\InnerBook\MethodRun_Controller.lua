﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功系統-心法運行頁面
---@class MethodRun_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.21
MethodRun_Controller = {}
local this = MethodRun_Controller

---正在運行的心法
this.m_RunningMethod = nil

---心法運行
local _MethodRunStr = 20112025
---取消運行
local _MethodStopStr = 21002006
---領悟心法
local _UnderstandMethodStr = 21002007
---心法升級
local _MethodUpgradeStr = 21002008
---心法進階
local _MethodAdvanceStr = 21002009
---達最高階級
local _MethodMaxLvStr = 20102038

---心法最高等級
local _MethodMaxLv = 100

---目前心法串檔
local _InnerBookData = {}

---當前選到的心法
local _NowSelectMethod = {}

---是否是心法升級
local _IsUpgrade = false

---心法升級點擊次數
local _ClickCount = 0

---心法升級等級Table
local _UpgradeLvTable = {}

---心法物件Data
local m_Table_Method = {}

---各階段Style
local _CaptionNameStyle = {"W", "G_01", "B_01", "P_01", "R_01", "Y_02", "DYY", "PP"}

---初始化
function MethodRun_Controller.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --內功心法列表
    local _ScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Skill")
    local _ReuseItem = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Skill").gameObject
    this.m_ScrollView_Method = ScrollView.Init(_ScrollView, false, _ReuseItem,
        MethodRun_Controller.GetCount,
        MethodRun_Controller.AfterReuseItemInit,
        MethodRun_Controller.AfterReuseItemIndexUpdate, true, true)

    --心法效果
    this.m_Image_GetSpiritValueBG = this.m_ViewRef.m_Dic_Trans:Get("&Image_GetSpiritValueBG"):GetComponent(typeof(UIRenderChangeColor))
    this.m_Text_SpiritValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_GetSpiritValue")
    this.m_Image_EffectValueBG = this.m_ViewRef.m_Dic_Trans:Get("&Image_EffectValueBG"):GetComponent(typeof(UIRenderChangeColor))
    this.m_Text_Effect = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Effect")
    this.m_Text_EffectValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_EffectValue")
    local _Image_Skill = this.m_ViewRef.m_Dic_Trans:Get("&Image_Skill")
    this.m_SkillIcon_Effect = IconMgr.NewInnerSkillIcon(0, _Image_Skill, 110)
    this.m_Text_SkillName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SkillName")
    this.m_Text_SkillLv = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SkillLv")
    this.m_Text_SkillDescribe = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SkillDescribe")
    this.m_Btn_Left = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Left"))
    this.m_Btn_Left:AddListener(EventTriggerType.PointerClick, function() MethodRun_Controller.OnClick_Left() end)
    this.m_Btn_Right = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Right"))
    this.m_Btn_Right:AddListener(EventTriggerType.PointerClick, function() MethodRun_Controller.OnClick_Right() end)

    --心法主視覺
    this.m_Image_MethodIcon = this.m_ViewRef.m_Dic_Trans:Get("&Image_MethodIcon"):GetComponent(typeof(Image))
    this.m_Text_MethodName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_MethodName")

    this.m_Group_MainSkill = this.m_ViewRef.m_Dic_Trans:Get("&Group_MainSkill")
    this.m_Text_MainSpiritValue = this.m_ViewRef.m_Dic_TMPText:Get("&Text_MainSpiritValue")
    local _Image_MainSkill = this.m_ViewRef.m_Dic_Trans:Get("&Image_MainSkill")
    this.m_SkillIcon_Main = IconMgr.NewInnerSkillIcon(0, _Image_MainSkill, 110)
    this.m_Text_MainSkill = this.m_ViewRef.m_Dic_TMPText:Get("&Text_MainSkill")
end

function MethodRun_Controller.Update()
    --心法升級時 要根據升到幾等同步更新心法效果的數值(之後會改用註冊的)
    if _IsUpgrade then
        if _ClickCount ~= CommonQuery_Type2_Controller.GetCurrentTimes() then
            _ClickCount = CommonQuery_Type2_Controller.GetCurrentTimes()
            local _Lv = _UpgradeLvTable[_ClickCount]
            MethodRun_Controller.SetMethodEffect(_InnerBookData, true, false, _Lv)
        end
    end
end

function MethodRun_Controller.Open()
    if table.Count(InnerBook_Model.m_MethodData) == 0 then
        InnerBook_Model.SetInitMethodData()
    end
    ScrollView.Update(this.m_ScrollView_Method)
    MethodRun_Controller.OnClick_Method(1)
    MethodRun_Controller.SetRunningMethod()
    
    _IsUpgrade = false
    
    GStateObserverManager.Register(EStateObserver.UpdateMethodData, this)
    GStateObserverManager.Register(EStateObserver.UpdateCurMethod, this)
    GStateObserverManager.Register(EStateObserver.UpdateSpiritValue, this)
end

function MethodRun_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.UpdateMethodData, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateCurMethod, this)
    GStateObserverManager.UnRegister(EStateObserver.UpdateSpiritValue, this)
end

function MethodRun_Controller.OnDestroy()
    m_Table_Method = {}
end

---設定運行中心法
function MethodRun_Controller.SetRunningMethod()
    if InnerBook_Model.m_RunningMethod ~= nil then
        local _IconTextureName = WugongData.GetWugongDataByIdx(InnerBook_Model.m_RunningMethod.m_WugongID):GetIconTextureName()
        SpriteMgr.Load(_IconTextureName, function(iSprite)
            this.m_Image_MethodIcon.sprite = iSprite
        end)
        this.m_Image_MethodIcon.gameObject:SetActive(true)
        this.m_Text_MethodName.text = TextData.Get(InnerBook_Model.m_RunningMethod.m_MethodString)
        local _WugongData = WugongData.GetWugongDataByIdx(InnerBook_Model.m_RunningMethod.m_WugongID)
        this.m_SkillIcon_Main:RefreshIcon(_WugongData.m_Idx)
        this.m_Text_MainSkill.text = _WugongData.m_Name
        local _SpiritAccStr = GString.Format(TextData.Get(10101011), InnerBook_Model.m_SpiritValue, InnerBook_Model.m_MaxSpiritValue)
        this.m_Text_MainSpiritValue.text = _SpiritAccStr
        ScrollView.Update(this.m_ScrollView_Method)
    else
        this.m_Image_MethodIcon.gameObject:SetActive(false)
        this.m_Text_MethodName.text = TextData.Get(21002001)
        this.m_SkillIcon_Main:RefreshIcon(0)
    end

    this.m_Group_MainSkill.gameObject:SetActive(InnerBook_Model.m_RunningMethod ~= nil)
end

--region 心法ScrollView
function MethodRun_Controller.GetCount()
    return InnerBookData.GetDataCount()
end

function MethodRun_Controller.AfterReuseItemInit(iItem, iIdx)
    if iItem ~= nil then
        local _Image_Icon = iItem.m_GObj.transform:Find("Image_Icon")
        iItem.m_Icon = IconMgr.NewInnerSkillIcon(0, _Image_Icon, 95)
        iItem.m_Name = iItem.m_GObj.transform:Find("Text_Name"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        iItem.m_ChapterName = iItem.m_GObj.transform:Find("Text_ChapterName"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        iItem.m_Lv = iItem.m_GObj.transform:Find("Text_Lv"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        iItem.m_AttrIcon = {}
        for i = 1, 2 do
            iItem.m_AttrIcon[i] = {}
            iItem.m_AttrIcon[i].Image = iItem.m_GObj.transform:Find("Image_Attribute_" .. i):GetComponent(typeof( Image ))
            iItem.m_AttrIcon[i].Render = iItem.m_GObj.transform:Find("Image_Attribute_" .. i):GetComponent(typeof( UIImageChange ))
        end
        iItem.m_Btn = Button.New(iItem.m_GObj)
        iItem.m_Btn:AddListener(EventTriggerType.PointerClick, function() MethodRun_Controller.OnClick_Method(iIdx) end)
        table.insert(m_Table_Method, iItem)
    end
end

function MethodRun_Controller.AfterReuseItemIndexUpdate(iItem, iIdx)
    if iItem ~= nil then
        local _data = InnerBookData.GetInnerBookDataByIdx(iIdx)
        if _data ~= nil then
            if InnerBook_Model.m_MethodData[iIdx] == nil then return end
            
            local _Step = InnerBook_Model.m_MethodData[iIdx].m_Step
            _data = _Step > 0 and _data[_Step] or _data[1]
            local _IconTextureName = WugongData.GetWugongDataByIdx(_data.m_WugongID):GetIconTextureName()
            iItem.m_Icon:RefreshIcon_Method(_IconTextureName, _data.m_Step)
            local _Style = (InnerBook_Model.m_RunningMethod ~= nil and InnerBook_Model.m_RunningMethod.m_MethodID == iIdx) and "PO" or "W"
            iItem.m_Name.text = GString.StringWithStyle(TextData.Get(_data.m_MethodString), _Style)
            iItem.m_ChapterName.text = GString.StringWithStyle(TextData.Get(_data.m_ChapterString), _CaptionNameStyle[_data.m_Step])
            if _Step == 0 then
                iItem.m_Lv.text = TextData.Get(21001013)
            else
                local _Lv = InnerBook_Model.m_MethodData[iIdx].m_Lv
                iItem.m_Lv.text = GString.Format(TextData.Get(20111037), _Lv, _MethodMaxLv)
            end

            for i = 1, 2 do
                local _SpriteName = (InnerBook_Model.m_RunningMethod ~= nil and InnerBook_Model.m_RunningMethod.m_MethodID == iIdx) and 
                    InnerBook_Model.m_AttMark[ESelectionState.Selected][i][_data.m_Attribute[i].Value] or
                    InnerBook_Model.m_AttMark[ESelectionState.Normal][i][_data.m_Attribute[i].Value]
                SpriteMgr.Load( _SpriteName, function(iSprite)
                    iItem.m_AttrIcon[i].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, iSprite)
                end)
                SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Highlighted][i][_data.m_Attribute[i].Value], function(iSprite)
                    iItem.m_AttrIcon[i].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Highlighted, iSprite)
                end)
                SpriteMgr.Load( InnerBook_Model.m_AttMark[ESelectionState.Selected][i][_data.m_Attribute[i].Value], function(iSprite)
                    iItem.m_AttrIcon[i].Render.m_GroupRenderInfo:SetRenderValue(ESelectionState.Selected, iSprite)
                end)
                iItem.m_AttrIcon[i].Image.sprite = iItem.m_AttrIcon[i].Render.m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal)
            end
        end
        iItem.m_GObj:SetActive(_data ~= nil)
    end
end
--endregion

---設定心法顯示效果
function MethodRun_Controller.SetMethodRender(iIdx)
    for k, v in pairs(m_Table_Method) do
        local _State = k == iIdx and 1 or 0
        v.m_Btn:ChangeStateTransitionGroup(_State)
    end
end

---設定按鈕狀態
function MethodRun_Controller.SetMethodBtnState()
    --心法未領悟
    if _NowSelectMethod.m_Step == 0 then
        this.m_Btn_Left:SetText(TextData.Get(_MethodRunStr))
        this.m_Btn_Left:SetDisable(false)
        this.m_Btn_Right:SetText(TextData.Get(_UnderstandMethodStr))
        this.m_Btn_Right:SetEnable()

    --已領悟
    else
        if InnerBook_Model.m_RunningMethod ~= nil and _NowSelectMethod.m_MethodID == InnerBook_Model.m_RunningMethod.m_MethodID then
            this.m_Btn_Left:SetText(TextData.Get(_MethodStopStr))
            this.m_Btn_Left:SetEnable()
        else
            this.m_Btn_Left:SetText(TextData.Get(_MethodRunStr))
            this.m_Btn_Left:SetEnable()
        end
        
        if _NowSelectMethod.m_MethodData.m_Lv < _MethodMaxLv then
            this.m_Btn_Right:SetText(TextData.Get(_MethodUpgradeStr))
            this.m_Btn_Right:SetEnable()
        else
            if _NowSelectMethod.m_Step < InnerBook_Model.m_MethodMaxStep then
                this.m_Btn_Right:SetText(TextData.Get(_MethodAdvanceStr))
                this.m_Btn_Right:SetEnable()
            else
                this.m_Btn_Right:SetText(TextData.Get(_MethodMaxLvStr))
                this.m_Btn_Right:SetDisable(false)
            end
        end
    end
end

--region 心法效果
---取得技能資訊
local function GetSkillInfo(iFixValue, iFormulaID, iPassiveLv)
    local _StatusData = StatusNameData.GetStatusNameDataByIdx(iFixValue)
    local _Name = TextData.Get(_StatusData.m_TextIdx)
    local _Value = FormulaData.GetValue(iFormulaID, iPassiveLv)
    return _Name, _Value
end

---設定心法效果
function MethodRun_Controller.SetMethodEffect(iData, iUpgrade, iAdvance, iLv)
    --如果心法還沒領悟就顯示1階段的效果
    local _TempMethod = _NowSelectMethod.m_Step == 0 and iData[_NowSelectMethod.m_Step + 1] or _NowSelectMethod
    local _WugongData = WugongData.GetWugongDataByIdx(_TempMethod.m_WugongID)

    local _SpiritValue, _NextValue, _Describe
    local _Lv = _NowSelectMethod.m_MethodData.m_Lv
    local _EffectName, _EffectValue = GetSkillInfo(_TempMethod.m_FixValue, _TempMethod.m_FormulaID, _Lv)
    local _GetSpiritBGState = (InnerBook_Model.m_RunningMethod ~= nil and _NowSelectMethod.m_MethodID == InnerBook_Model.m_RunningMethod.m_MethodID) and
        ESelectionState.Selected or ESelectionState.Normal
    --一般狀態
    if not iAdvance and not iUpgrade then
        this.m_Image_GetSpiritValueBG:Trigger(_GetSpiritBGState)
        local _State = _NowSelectMethod.m_MethodData.m_Step > 0 and ESelectionState.Selected or ESelectionState.Normal
        this.m_Image_EffectValueBG:Trigger(_State)
        _SpiritValue = _TempMethod.m_SpiritPerFiveMin
        _EffectValue = _EffectValue
        _Describe = GString.StyleChangeBatch(GFunction.GetSkillUpLevelFormatString(_WugongData.m_Idx, _Lv), InnerBook_Model.m_NormalStyle)

    --升級狀態
    elseif iUpgrade then
        this.m_Image_GetSpiritValueBG:Trigger(_GetSpiritBGState)
        this.m_Image_EffectValueBG:Trigger(ESelectionState.Selected)
        local _EffectName, _UpgradeEffectValue = GetSkillInfo(_TempMethod.m_FixValue, _TempMethod.m_FormulaID, iLv)
        _SpiritValue = _TempMethod.m_SpiritPerFiveMin
        _EffectValue = _EffectValue .. " -> " .. GString.StringWithStyle(_UpgradeEffectValue, InnerBook_Model.m_EmphasizeStyle)
        _Describe = GString.StyleChangeBatch(GFunction.GetSkillUpLevelFormatString(_WugongData.m_Idx, _Lv), InnerBook_Model.m_EmphasizeStyle)

    --進階狀態
    elseif iAdvance then
        this.m_Image_GetSpiritValueBG:Trigger(_GetSpiritBGState)
        this.m_Image_EffectValueBG:Trigger(ESelectionState.Selected)
        if _NowSelectMethod.m_Step == 0 then
            _SpiritValue = "0 -> " .. GString.StringWithStyle(_TempMethod.m_SpiritPerFiveMin, InnerBook_Model.m_EmphasizeStyle)
            _EffectValue = "0 -> " .. GString.StringWithStyle(_EffectValue, InnerBook_Model.m_EmphasizeStyle)
        else
            local _NextData = iData[_NowSelectMethod.m_Step + 1]
            if _NextData ~= nil then
                _EffectName, _NextValue = GetSkillInfo(_NextData.m_FixValue, _NextData.m_FormulaID, 1)
                _SpiritValue = _TempMethod.m_SpiritPerFiveMin .. " -> " .. GString.StringWithStyle(_NextData.m_SpiritPerFiveMin, InnerBook_Model.m_EmphasizeStyle)
                _EffectValue = _EffectValue .. " -> " .. GString.StringWithStyle(_NextValue, InnerBook_Model.m_EmphasizeStyle)
            else
                _SpiritValue = _TempMethod.m_SpiritPerFiveMin
                _EffectValue = _EffectValue
            end
        end
        _Describe = GString.StyleChangeBatch(GFunction.GetSkillUpLevelFormatString(_WugongData.m_Idx, 1), InnerBook_Model.m_EmphasizeStyle)
    end

    local _Style = _NowSelectMethod.m_Step == 0 and InnerBook_Model.m_EmphasizeStyle or InnerBook_Model.m_NormalStyle
    this.m_Text_SpiritValue.text = GString.StringWithStyle(_SpiritValue, _Style)
    this.m_Text_Effect.text = _EffectName
    this.m_Text_EffectValue.text = GString.StringWithStyle(_EffectValue, _Style)
    this.m_SkillIcon_Effect:RefreshIcon(_WugongData.m_Idx)
    this.m_Text_SkillName.text = _WugongData.m_Name
    this.m_Text_SkillLv.text = GString.Format(TextData.Get(530102), _Lv)
    this.m_Text_SkillDescribe.text = _Describe
end
--endregion

---設定心法升級
function MethodRun_Controller.UpdateMethodData(iIdx)
    MethodRun_Controller.OnClick_Method(iIdx)
    ScrollView.Update(this.m_ScrollView_Method)

    InnerBook_Controller.SetMaskActive(false)
end

--region 點擊專區
---點擊心法
function MethodRun_Controller.OnClick_Method(iIdx)
    _InnerBookData = InnerBookData.GetInnerBookDataByIdx(iIdx)
    local _Step = InnerBook_Model.m_MethodData[iIdx].m_Step
    --Step = 0 等於心法還沒領悟
    _NowSelectMethod = _InnerBookData[_Step]
    _NowSelectMethod.m_MethodData = InnerBook_Model.m_MethodData[iIdx]

    MethodRun_Controller.SetMethodEffect(_InnerBookData, false, false)
    MethodRun_Controller.SetMethodRender(iIdx)
    MethodRun_Controller.SetMethodBtnState()
end

---點擊左邊按鈕(心法運行、取消運行)
function MethodRun_Controller.OnClick_Left()
    if InnerBook_Model.m_RunningMethod == nil or
      (InnerBook_Model.m_RunningMethod ~= nil and
       _NowSelectMethod.m_MethodID ~= InnerBook_Model.m_RunningMethod.m_MethodID) then
        SendProtocol_003._017(3, _NowSelectMethod)
    else
        SendProtocol_003._017(3, {m_MethodID = 0})
    end
end

---設定心法升級道具需求數量
local function SetUpgradeCount()
    local _NeedCount = _NowSelectMethod.m_NeedCount
    local _Lv = _NowSelectMethod.m_MethodData.m_Lv
    local _CountTable = {}
    for i = _Lv, 100 do
        for j = 1, _NeedCount do
            table.insert(_CountTable, i)
        end
    end
    table.remove(_CountTable, 1)

    local _TotalCount = table.Count(_CountTable)
    local _BagItemCount = BagMgr.GetItemInBagAmount(55001)
    local _MaxCount = _TotalCount > _BagItemCount and _BagItemCount or _TotalCount
    return _CountTable, _MaxCount
end

---點擊右邊按鈕(領悟心法、心法升級、心法進階、達最高階級)
function MethodRun_Controller.OnClick_Right()
    --領悟心法
    if _NowSelectMethod.m_Step == 0 then
        local _ItemCount = BagMgr.GetItemInBagAmount(_NowSelectMethod.m_ItemID)
        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
        _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon, {_NowSelectMethod.m_ItemID}, {_ItemCount}, {true}, false)
        local _Name = TextData.Get(_NowSelectMethod.m_MethodString) .. " - " .. TextData.Get(_NowSelectMethod.m_ChapterString)
        _Type2Data:BuildDescriptionBoxTable({TextData.Get(21002013)}, {_Name})
        _Type2Data:BuildConfirmBtnStatus(_ItemCount > 0)
        CommonQueryMgr.AddNewInform(560, {TextData.Get(21002011)}, {TextData.Get(21002012)},
            function()
                --領悟心法
                local _Packet = {}
                _Packet.m_MethodID = _NowSelectMethod.m_MethodID
                _Packet.m_Step = _NowSelectMethod.m_MethodData.m_Step + 1
                SendProtocol_003._017(1, _Packet)
                InnerBook_Controller.SetMaskActive(false)
            end,
            nil,
            function() InnerBook_Controller.SetMaskActive(false) end, nil,
            nil, nil, _Type2Data)

        InnerBook_Controller.SetMaskActive(true, false)

    --心法升級
    elseif _NowSelectMethod.m_Step > 0 and _NowSelectMethod.m_MethodData.m_Lv < _MethodMaxLv then
        local _ItemCount = BagMgr.GetItemInBagAmount(55001)
        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
	    _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon, {55001}, {(_ItemCount)}, {true}, false)
        local _Name = TextData.Get(_NowSelectMethod.m_MethodString) .. " - " .. TextData.Get(_NowSelectMethod.m_ChapterString)
        local _PreLv = _NowSelectMethod.m_MethodData.m_Lv
	    _Type2Data:BuildDescriptionBoxTable({_Name}, {_PreLv}, {_PreLv}, {false}, {true})
        local _CountTable, _MaxCount = SetUpgradeCount()
        _UpgradeLvTable = _CountTable
	    _Type2Data:BuildTimeTable(_MaxCount, 1, {0}, {_CountTable}, {}, true)
        _Type2Data:BuildConfirmBtnStatus(_ItemCount > 0)
	    CommonQueryMgr.AddNewInform(561, {TextData.Get(21002016)}, {TextData.Get(21002017)},
            function()
                --心法升級
                local _Packet = {}
                _Packet.m_MethodID = _NowSelectMethod.m_MethodID
                _Packet.m_Lv = _CountTable[CommonQuery_Type2_Controller.GetCurrentTimes()]
                SendProtocol_003._017(2, _Packet)
                InnerBook_Controller.SetMaskActive(false)
                _IsUpgrade = false
                _ClickCount = 0
            end,
            nil,
            function()
                InnerBook_Controller.SetMaskActive(false)
                MethodRun_Controller.SetMethodEffect(_InnerBookData, false, false)
                _IsUpgrade = false
                _ClickCount = 0
            end, nil,
            nil, nil, _Type2Data)

        InnerBook_Controller.SetMaskActive(true)
        MethodRun_Controller.SetMethodEffect(_InnerBookData, true, false, _NowSelectMethod.m_MethodData.m_Lv)

        _IsUpgrade = true

    --心法進階
    else
        local _ItemCount = BagMgr.GetItemInBagAmount(_NowSelectMethod.m_ItemID)
        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
        _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon, {_NowSelectMethod.m_ItemID}, {_ItemCount}, {true}, false)
        local _Name = TextData.Get(_NowSelectMethod.m_MethodString) .. " - " .. TextData.Get(_NowSelectMethod.m_ChapterString)
        _Type2Data:BuildDescriptionBoxTable({TextData.Get(21002018)}, {_Name})
        _Type2Data:BuildConfirmBtnStatus(_ItemCount > 0)
        CommonQueryMgr.AddNewInform(560, {TextData.Get(21002011)}, {TextData.Get(21002012)}, 
            function() 
                --心法進階
                local _Packet = {}
                _Packet.m_MethodID = _NowSelectMethod.m_MethodID
                _Packet.m_Step = _NowSelectMethod.m_MethodData.m_Step + 1
                SendProtocol_003._017(1, _Packet)
                InnerBook_Controller.SetMaskActive(false)
            end,
            nil,
            function()
                InnerBook_Controller.SetMaskActive(false)
                MethodRun_Controller.SetMethodEffect(_InnerBookData, false, false)
            end,
            nil, nil, nil, _Type2Data)

        InnerBook_Controller.SetMaskActive(true)
        MethodRun_Controller.SetMethodEffect(_InnerBookData, false, true)
    end
end
--endregion

---狀態改變通知
function MethodRun_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.UpdateMethodData then
        local _Param = {...}
        local _ID = _Param[1]
        MethodRun_Controller.UpdateMethodData(_ID)
    elseif iState == EStateObserver.UpdateCurMethod or iState == EStateObserver.UpdateSpiritValue then
        MethodRun_Controller.SetRunningMethod()
        MethodRun_Controller.SetMethodBtnState()
    end
end