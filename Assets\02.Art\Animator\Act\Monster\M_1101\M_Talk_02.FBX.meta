fileFormatVersion: 2
guid: 9a3aa067e62c45c4fa80ee04cc12fb7c
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: Bip001
    100004: Bip001 Footsteps
    100006: Bip001 Head
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger1
    100018: Bip001 L Finger11
    100020: Bip001 L Finger2
    100022: Bip001 L Finger21
    100024: Bip001 L Finger3
    100026: Bip001 L Finger31
    100028: Bip001 L Finger4
    100030: Bip001 L Finger41
    100032: Bip001 L Foot
    100034: Bip001 L Forearm
    100036: Bip001 L Hand
    100038: Bip001 L Thigh
    100040: Bip001 L UpperArm
    100042: Bip001 Neck
    100044: Bip001 Pelvis
    100046: Bip001 R Calf
    100048: Bip001 R Clavicle
    100050: Bip001 R Finger0
    100052: Bip001 R Finger01
    100054: Bip001 R Finger1
    100056: Bip001 R Finger11
    100058: Bip001 R Finger2
    100060: Bip001 R Finger21
    100062: Bip001 R Finger3
    100064: Bip001 R Finger31
    100066: Bip001 R Finger4
    100068: Bip001 R Finger41
    100070: Bip001 R Foot
    100072: Bip001 R Forearm
    100074: Bip001 R Hand
    100076: Bip001 R Thigh
    100078: Bip001 R UpperArm
    100080: Bip001 Spine
    100082: Bip001 Spine1
    100084: bones_Eyebrow
    100086: bones_F_F_Skirt_001
    100088: bones_F_F_Skirt_002
    100090: bones_F_R_Skirt_001
    100092: bones_F_R_Skirt_002
    100094: bones_F_Skirt_001
    100096: bones_F_Skirt_002
    100098: bones_F_Skirt_003
    100100: bones_F_Skirt_004
    100102: bones_Head_001
    100104: bones_L_Ear_001
    100106: bones_L_Ear_002
    100108: bones_L_Eye_001
    100110: bones_L_Eye_Down_001
    100112: bones_L_Eye_Up_001
    100114: bones_L_Hair_001
    100116: bones_L_Mouth_001
    100118: bones_L_Skirt_001
    100120: bones_L_Skirt_002
    100122: bones_L_Sleeve_001
    100124: bones_L_Sleeve_002
    100126: bones_Mouth_Down_001
    100128: bones_Mouth_UP_001
    100130: bones_R_Ear_001
    100132: bones_R_Ear_002
    100134: bones_R_Eye_001
    100136: bones_R_Eye_Down_001
    100138: bones_R_Eye_Up_001
    100140: bones_R_Hair_001
    100142: bones_R_Mouth_001
    100144: bones_R_Skirt_001
    100146: bones_R_Skirt_002
    100148: bones_R_Sleeve_001
    100150: bones_R_Sleeve_002
    100152: bones_Tongue_001
    100154: bones_Tongue_002
    100156: Dummy_ L_Foot_001
    100158: Dummy_ R_Foot_001
    100160: Dummy_F_Hair_001
    100162: Dummy_L_Calf_001
    100164: Dummy_L_F_Skirt_001
    100166: Dummy_L_F_Skirt_002
    100168: Dummy_L_F_Skirt_003
    100170: Dummy_L_F_Skirt_004
    100172: Dummy_L_Hair_001
    100174: Dummy_L_UpperArm_001
    100176: Dummy_L_UpperArm_002
    100178: Dummy_R_Calf_001
    100180: Dummy_R_F_Skirt_001
    100182: Dummy_R_F_Skirt_002
    100184: Dummy_R_F_Skirt_003
    100186: Dummy_R_F_Skirt_004
    100188: Dummy_R_Hair_001
    100190: Dummy_R_UpperArm_001
    100192: Dummy_R_UpperArm_002
    100194: Dummy_Tongue_000
    100196: Particle View 001
    400000: //RootNode
    400002: Bip001
    400004: Bip001 Footsteps
    400006: Bip001 Head
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger1
    400018: Bip001 L Finger11
    400020: Bip001 L Finger2
    400022: Bip001 L Finger21
    400024: Bip001 L Finger3
    400026: Bip001 L Finger31
    400028: Bip001 L Finger4
    400030: Bip001 L Finger41
    400032: Bip001 L Foot
    400034: Bip001 L Forearm
    400036: Bip001 L Hand
    400038: Bip001 L Thigh
    400040: Bip001 L UpperArm
    400042: Bip001 Neck
    400044: Bip001 Pelvis
    400046: Bip001 R Calf
    400048: Bip001 R Clavicle
    400050: Bip001 R Finger0
    400052: Bip001 R Finger01
    400054: Bip001 R Finger1
    400056: Bip001 R Finger11
    400058: Bip001 R Finger2
    400060: Bip001 R Finger21
    400062: Bip001 R Finger3
    400064: Bip001 R Finger31
    400066: Bip001 R Finger4
    400068: Bip001 R Finger41
    400070: Bip001 R Foot
    400072: Bip001 R Forearm
    400074: Bip001 R Hand
    400076: Bip001 R Thigh
    400078: Bip001 R UpperArm
    400080: Bip001 Spine
    400082: Bip001 Spine1
    400084: bones_Eyebrow
    400086: bones_F_F_Skirt_001
    400088: bones_F_F_Skirt_002
    400090: bones_F_R_Skirt_001
    400092: bones_F_R_Skirt_002
    400094: bones_F_Skirt_001
    400096: bones_F_Skirt_002
    400098: bones_F_Skirt_003
    400100: bones_F_Skirt_004
    400102: bones_Head_001
    400104: bones_L_Ear_001
    400106: bones_L_Ear_002
    400108: bones_L_Eye_001
    400110: bones_L_Eye_Down_001
    400112: bones_L_Eye_Up_001
    400114: bones_L_Hair_001
    400116: bones_L_Mouth_001
    400118: bones_L_Skirt_001
    400120: bones_L_Skirt_002
    400122: bones_L_Sleeve_001
    400124: bones_L_Sleeve_002
    400126: bones_Mouth_Down_001
    400128: bones_Mouth_UP_001
    400130: bones_R_Ear_001
    400132: bones_R_Ear_002
    400134: bones_R_Eye_001
    400136: bones_R_Eye_Down_001
    400138: bones_R_Eye_Up_001
    400140: bones_R_Hair_001
    400142: bones_R_Mouth_001
    400144: bones_R_Skirt_001
    400146: bones_R_Skirt_002
    400148: bones_R_Sleeve_001
    400150: bones_R_Sleeve_002
    400152: bones_Tongue_001
    400154: bones_Tongue_002
    400156: Dummy_ L_Foot_001
    400158: Dummy_ R_Foot_001
    400160: Dummy_F_Hair_001
    400162: Dummy_L_Calf_001
    400164: Dummy_L_F_Skirt_001
    400166: Dummy_L_F_Skirt_002
    400168: Dummy_L_F_Skirt_003
    400170: Dummy_L_F_Skirt_004
    400172: Dummy_L_Hair_001
    400174: Dummy_L_UpperArm_001
    400176: Dummy_L_UpperArm_002
    400178: Dummy_R_Calf_001
    400180: Dummy_R_F_Skirt_001
    400182: Dummy_R_F_Skirt_002
    400184: Dummy_R_F_Skirt_003
    400186: Dummy_R_F_Skirt_004
    400188: Dummy_R_Hair_001
    400190: Dummy_R_UpperArm_001
    400192: Dummy_R_UpperArm_002
    400194: Dummy_Tongue_000
    400196: Particle View 001
    7400000: M_Talk_02
    9500000: //RootNode
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: dad121a675cc76343b5f3e45a7dd72ce,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
