﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---協定3-X (玩家資料相關)
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.5.26

ProtocolMgr[3] = {}

-- [3-1]即時更新玩家資料 +kind(1)
ProtocolMgr[3][1] = function(iPacket)
    local _Kind = iPacket:ReadByte()
    if _Kind == 1 then
        PlayerData.UpdatePlayerDiamond(iPacket)
    elseif _Kind == 2 then
        PlayerData.UpdatePlayerCoin(iPacket)
    elseif _Kind == 3 then
        PlayerData.UpdatePlayerSocialStatus(iPacket)
    elseif _Kind == 4 then
        PlayerData.UpdatePlayerLvAndExp(iPacket)
    elseif _Kind == 5 then
        local _nowHp = iPacket:ReadUInt32()
        local _maxHp = iPacket:ReadUInt32()

        PlayerData.UpdatePlayerHp(_nowHp, _maxHp)
    elseif _Kind == 6 then
        PlayerData.UpdatePlayerMp(iPacket)
    elseif _Kind == 7 then
        PlayerData.UpdatePlayerSp(iPacket)
    elseif _Kind == 8 then
        --玩家永久標記索引(2)+永久標記數值(1)
        local _StaticFlag = StaticFlag:New(iPacket)
        PlayerData.GetFlags(EFlags.Static).Set(_StaticFlag)
    elseif _Kind == 9 then
        --動態事件標記+動態事件索引(1)+編號(2)+步驟狀態(1)+時間(1)
        local _Index, _MoveFlag = MovingFlag:New(iPacket)
        PlayerData.GetFlags(EFlags.Move).Set(_MoveFlag)
    elseif _Kind == 10 then
        -- 計數永標索引編號(2)+數量(4)
        local _StaticNumFlag = StaticNumFlag:New(iPacket)
        BagMgr.FlagItemUpdate(_StaticNumFlag)

    elseif _Kind == 11 then
        -- 聲譽(4)
        local _Reputation = iPacket:ReadUInt32()

        PlayerData.m_LoginPlayerData.m_Reputation = _Reputation

        if  PlayerData_Title ~=nil then
            PlayerData_Title.RecheckCheckTitleAvailable(ETitleChckType.Reputation)
        end
        GStateObserverManager.Notify(EStateObserver.UpdateReputation)
    elseif _Kind == 12 then
        --武功點數(4)
        PlayerData.UpdatePlayerWogonPoint(iPacket)
        GStateObserverManager.Notify(EStateObserver.UpdateWugongPoint)
    elseif _Kind == 16 then
        --回傳投點 剩下的可投點數(4)
        local _LeftPoint = iPacket:ReadUInt32()
        PlayerData.Get(EPalyerData.Attributes).SetNotUsePoint(_LeftPoint)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 17 then
        --回傳投點力量後 力量數值(1)
        local _Str = iPacket:ReadByte()
        PlayerData.Get(EPalyerData.Attributes)[EBaseAttributes.STR].Point(_Str)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 18 then
        --回傳投點敏捷後 敏捷數值(1)
        local _Agi = iPacket:ReadByte()
        PlayerData.Get(EPalyerData.Attributes)[EBaseAttributes.AGI].Point(_Agi)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 19 then
        --回傳投點體魄後 體魄數值(1)
        local _Con = iPacket:ReadByte()
        PlayerData.Get(EPalyerData.Attributes)[EBaseAttributes.CON].Point(_Con)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 20 then
        --回傳投點真元後 真元數值(1)
        local _Int = iPacket:ReadByte()
        PlayerData.Get(EPalyerData.Attributes)[EBaseAttributes.INT].Point(_Int)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 21 then
        --回傳投精神後 精神數值(1)
        local _Men = iPacket:ReadByte()
        PlayerData.Get(EPalyerData.Attributes)[EBaseAttributes.MEN].Point(_Men)
        GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
    elseif _Kind == 23 then
        PlayerData.UpdatePlayerDP(iPacket)
    elseif _Kind == 24 then
        --福緣(2)
        GStateObserverManager.Notify(EStateObserver.UpdateFortune)
    elseif _Kind == 27 then
        PlayerData.UpdatePlayerHEMCoin(iPacket)
    elseif _Kind == 30 then
        --陣營(1) [0.無1.A隊 2.B隊] ( 現在無分AB隊 )
        local _Alliance = iPacket:ReadByte()
        if not RoleMgr.m_RC_Player then
            return
        end

        RoleMgr.m_RC_Player.m_Troops = _Alliance
    elseif _Kind == 31 then
        --生活技能編號(1)[1.挖礦 2.採集 3.農耕 4.採藥 5.經商 6.工匠技能 7.琴 8.棋 9.書 10.畫 11.詩 12.酒 13.花 14.茶]+等級(1)+經驗值(4)
        local _SkillType = iPacket:ReadByte()
        local _Level = iPacket:ReadByte()
        local _exp = iPacket:ReadByte()

        if PlayerData_Attributes ~=nil then
            PlayerData_Attributes.SetLiveSkillLevel(_SkillType,_Level,_exp)
        end

        GStateObserverManager.Notify(EStateObserver.UpdateLLivingSkill)
    elseif _Kind == 32 then -- PK開關(1)[1開 0關] -- 現在改由 8-9 收，有進階設定
        local _PKEnable = iPacket:ReadByte()
        --[[RoleMgr.m_RC_Player.m_PKInfo.m_PKEnable = _PKEnable
        RoleMgr.m_RC_Player:RefreshRelationByType(ERelatType.Enemy)
        RoleMgr.m_RC_Player:UpdateHUDRelationship()]]
    elseif _Kind == 33 then
        --威望值(善惡值)(2)[0-60000]
        local _Prestige = iPacket:ReadUInt16()
        if not RoleMgr then
            return
        end

        -- 不存入RC，會先一步收到協定
        RoleMgr.m_Prestige = _Prestige
        RoleMgr.m_PKState = _Prestige >= PRESTIGE_BOUNDARY and EPKState.Blue or EPKState.Red

        if RoleMgr.m_RC_Player then
            RoleMgr.m_RC_Player:RefreshRelationByType(ERelatType.Enemy)
            RoleMgr.m_RC_Player:UpdateHUDRelationship()
            --自己的變動要刷所有人
            for k,v in pairs (RoleMgr.m_Table_ActiveRoles) do
                v:RefreshRelationByType(ERelatType.Enemy)
                v:UpdateHUDRelationship()
            end
        end

        if  PlayerData_Title ~=nil then
            PlayerData_Title.RecheckCheckTitleAvailable(ETitleChckType.Prestige)
        end
        GStateObserverManager.Notify(EStateObserver.UpdatePrestige)
    elseif _Kind == 34 then
        --現行犯剩餘時間(1)秒
        local _PK = iPacket:ReadByte()
        local _Switch = false
        if _PK > 0 then
            _Switch = RoleMgr.m_PKState ~= EPKState.Purple
            RoleMgr.m_PKState = EPKState.Purple
        else
            _Switch = RoleMgr.m_PKState ~= (RoleMgr.m_Prestige >= PRESTIGE_BOUNDARY and EPKState.Blue or EPKState.Red)
            RoleMgr.m_PKState = RoleMgr.m_Prestige >= PRESTIGE_BOUNDARY and EPKState.Blue or EPKState.Red
        end

        if _Switch then
            RoleMgr.m_RC_Player:RefreshRelationByType(ERelatType.Enemy)
            RoleMgr.m_RC_Player:UpdateHUDRelationship()
            --自己的變動要刷所有人
            for k,v in pairs (RoleMgr.m_Table_ActiveRoles) do
                v:RefreshRelationByType(ERelatType.Enemy)
                v:UpdateHUDRelationship()
            end
        end
    elseif _Kind == 37 then
        local _PetsData = {}
        _PetsData.m_PetID = iPacket:ReadInt16()
        _PetsData.m_EnvolveLv = iPacket:ReadByte()
        _PetsData.m_PetName = iPacket:ReadString()
        _PetsData.m_SID = tostring(PlayerData.GetCardID())
        if _PetsData.m_PetID > 0 then
            PetMgr.NewPet(_PetsData)
        else
            PetMgr.RemoveActivePet(_PetsData.m_SID)
            PetMgr.SetSummonedPetPlot(0)
        end
    elseif _Kind == 40 then
        --量子源訊息(1)
        local _QuantumSourceValue = iPacket:ReadByte()
        PlayerData.UpdatePlayerQuantumSourceValue(iPacket)
        GStateObserverManager.Notify(EStateObserver.UpdateQuantumSource)
    elseif _Kind == 41 then
        --稱號編號(2)
        local _TitleID = iPacket:ReadUInt16()

        --- 給titleiD = 0 表示要變成沒有任何稱號 如果原本有稱號 需觸發移除稱號通知
        if _TitleID == 0 and PlayerData.GetTitleID() ~= 0 then

            MessageMgr.AddCenterMsg(false, TextData.Get(9722))

            PlayerData_Title.SetTitleIsEquiped(_TitleID)
            PlayerData.SetTitleID(_TitleID)

            if UIMgr.IsVisible(RoleAttribute_Controller) and RoleAttribute_Page_Title.m_Page_GameObject.activeSelf then
                RoleAttribute_Page_Title.RenewDetailInfo_Single()
                ScrollView.Update(RoleAttribute_Page_Title.m_ScrollView_TitleOverview)
            end


        end
    elseif _Kind == 42 then
        --裝備快捷列更新 + 位置(1) + 物品ID(4) + 流水號(4)
        local _Pos = iPacket:ReadByte()
        local _ID = iPacket:ReadUInt32()
        local _SerialNumber = iPacket:ReadUInt32()
        Equipment_Model.UpdateEquipSet(_Pos, _ID, _SerialNumber)
    elseif _Kind == 44 then
        --快捷組別設定+種類(1)[1.裝備 2.外觀]+設定(1)
        local _SetKind = iPacket:ReadByte()
        local _Select = iPacket:ReadByte()
        if _SetKind == 1 then --裝備
            Equipment_Model.SetSelectEquipSetNum(_Select)
            if Equipment_Model.m_IsSendEquipProtocol then
                Equipment_Model.m_IsSendEquipProtocol = false
                Equipment_Model.ChangeEquipSet(_Select)
            end
        elseif _SetKind == 2 then --外觀
        end
    elseif _Kind == 45 then
        --45.外功快捷組別+設定(1)[1~10]+刀(1)+劍(1)+拳(1)+槍(1)+棍(1)[0.第一組 1.第二組]
        local _Setting = iPacket:ReadByte()
        local _Table = {}

        for i = 1, 5 do
            _Table[i] = iPacket:ReadByte()
        end

        Debug_Model.AddNormalText("3-1-45 外功快捷組別: " .. _Setting)
        SkillBook_Model.SetSkillPage(_Table)

    elseif _Kind == 46 then
        -- 46.艙室效果記數永標+編號(1)+數值(2)
        local _TimeMachineEffect = TimeMachineEffectFlag:New(iPacket)
        PlayerData.GetFlags(EFlags.TimeMachineEffect).Set(_TimeMachineEffect)
    elseif _Kind == 47 then
        -- 47.記數永標索引編號(2)+數量(2)
        local _StaticNumFlag = ExtraStaticNumFlag:New(iPacket,_Kind)
        BagMgr.FlagItemUpdate(_StaticNumFlag)
    elseif _Kind == 48 then
        -- 48.記數永標索引編號(2)+數量(1)
        local _StaticNumFlag = ExtraStaticNumFlag:New(iPacket,_Kind)
        BagMgr.FlagItemUpdate(_StaticNumFlag)
        Community_Friend_Controller.SayHello_TimesUpdate()
    elseif _Kind == 49 then
        local _EnergyKeep = iPacket:ReadUInt64()
        EnergyMineMgr.SetEnergyKeep(_EnergyKeep)
        GStateObserverManager.Notify(EStateObserver.UpdateEnergy, _EnergyKeep)
    elseif _Kind == 50 then
        EnergyMineMgr.SetEnergyEffSum(iPacket:ReadUInt32())
        EnergyMineMgr.SetEnergyMax(iPacket:ReadUInt64())
        GStateObserverManager.Notify(EStateObserver.UpdateEnergy, EnergyMineMgr.GetEnergyKeep())
    end
end

--- [3-2]玩家物品資料
ProtocolMgr[3][2] = function(iPacket)
    local _Kind = iPacket:ReadByte()
    if _Kind == 1 then
        -- 1.玩家登入時收到物品資料+count(2)+<<物品欄位索引(2)+物品編號(4)+目前保存度(1)+強化次數(1)+最大保存度(1)+鑲嵌效果1(2)+鑲嵌效果2(2)+鑲嵌效果3(2)+物品狀態(1)+數量(2)+流水號(4)>>
        local _Count = iPacket:ReadInt16()
        local _TBag = {}
        ---2022.09.08 Add by KK, 以 ItemIdx 為 Key 當搜尋
        local _TBag_SearchByItemIdx = {}
        BagMgr.SetBagTable( _TBag, EBagType.Equipment, _TBag_SearchByItemIdx )

        for i = 1, _Count do
            local _SID, _Data = SaveItemData:New(iPacket)
            _TBag[_SID] = _Data

            -- 更新SearchTable
            _TBag_SearchByItemIdx = BagMgr.SetSearchTable(_Data,EBagType.Equipment)
		end
		GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
	elseif _Kind == 2 then
		-- 2.新增或更新某欄位物品+count(2)+<<物品欄位索引(2)+物品編號(4)+目前保存度(1)+強化次數(1)+突破次數(1)+鑲嵌效果1(2)+鑲嵌效果2(2)+鑲嵌效果3(2)+物品狀態(1)+數量(2)+流水號(4)>>+來源種類(1) +參數(4)
		-- +來源種類(1) +參數(4) [來源:17.NPC掉落 參數:NPCID]
		local _Count = iPacket:ReadInt16()

        -- 獲得物品資料列表
        local _ItemGetQueue = Queue:New()

        -- 獲得物品數量資訊
        local _GetItemNunInfo = {}
        for i = 1, _Count do
            local _SID, _SaveItemData = SaveItemData:New(iPacket)

            --舊的物品資料
            local _CItemData = BagMgr.GetBagItemBySID(_SID,nil,EBagType.Equipment)

            --更新背包各種table
            local _IsItemCountModify = BagMgr.UpdateBagTable(_SID, _SaveItemData,EBagType.Equipment)

            -- 儲存獲得物品到列表
            _ItemGetQueue:Enqueue(_SaveItemData)

            ---判斷是否更新自動喝水
            --if AutoDrink_Model ~= nil then
            --    AutoDrink_Model.CheckAutoDrink(_SaveItemData.m_Count > 0, _SaveItemData.m_Count > 0 and _SaveItemData or _CItemData)
            --end

            -- 獲得物品數量計算
            local _GetItemNum = _SaveItemData.m_Count
            if _CItemData ~= nil then
                _GetItemNum = _SaveItemData.m_Count - _CItemData.m_Count
            end
            -- 紀錄獲得物品數量
            -- 同一次協定相同物品要合併計算(分堆的話 會拿到兩個SID)
            if _GetItemNunInfo[_SaveItemData.m_ItemIdx] == nil then
                -- 道具被刪除物品編號會拿到 0 要特別處理
                if _SaveItemData.m_ItemIdx ~= 0 then
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx] = {}
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum = _GetItemNum
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemName = _SaveItemData.m_ItemData:ItemName()
                end
            else
                _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum = _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum + _GetItemNum   
            end
            -- 通知特定物品刷新
            GStateObserverManager.Notify(EStateObserver.UpdateSpecificItem, _SaveItemData.m_ItemIdx)

            if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                HotKey_Controller.RenewPotionHotKey()
            end
        end
        -- 顯示物品獲得訊息
        for key, value in pairs(_GetItemNunInfo) do
            -- 減少不顯示
            if value.m_GetItemNum > 0 then
                local _GetItemMsg = GString.Format(TextData.Get(733),value.m_GetItemName,value.m_GetItemNum)
                ChatMgr.Add_SystemChannels(_GetItemMsg)
                MessageMgr.AddItemGetMsg(_GetItemMsg) -- 玩家使用兌換卡, 同時顯示獲得物品訊息 Add by 凌傑RM#115832 2024.12.09
            end
        end
        GStateObserverManager.Notify(EStateObserver.BagDataRefresh)

        -- 物品是NPC掉落物
        local _From = iPacket:ReadByte()
        local _NPCDrop = 17
        local _FuDia = 29 -- 福袋
        local _EventGive = 13 --事件給予
        -- 6好像是用巨神兵加的
        if _From == _NPCDrop or _From == _FuDia or _From == _EventGive or _From == 6 then
            for i = 1, _Count do
                if not _ItemGetQueue:IsEmpty() then
                    local _GetItemData = _ItemGetQueue:Dequeue()
                    -- 將道具加到新增列表中
                    if _GetItemData.m_ItemIdx == 0 then
                        break
                    end
                    BagMgr.AddNewItemList(_GetItemData.m_ItemIdx, _GetItemData.m_SID,EBagType.Equipment)
                    PopItemChange_Mgr.AddItem(_GetItemData.m_ItemIdx, _GetItemData.m_SID)

                    if _From == _NPCDrop then
                        local _NPCID = iPacket:ReadUInt32()
                        if _NPCID ~= nil then
                            ---將來自某怪的所有掉落物暫存到DropMgr對應table
                            DropMgr.FillItemIDData(_NPCID,_GetItemData.m_ItemIdx)
                        end
                    end
                end
            end
        end
    elseif _Kind == 3 then
        -- 3.玩家登入時收到裝備物品資料+count(1)+<<裝備位置(1)+物品編號(4)+目前保存度(1)+強化次數(1)+突破次數(1)+鑲嵌效果1(2)+鑲嵌效果2(2)+鑲嵌效果3(2)+物品狀態(1)+數量(2)>>
        local _Count = iPacket:ReadByte()
        local _TEquip = {}
        for i = 1, _Count do
            local _SID, _Save = SaveItemData:New(iPacket)
            --如果裝備位置是1(武器)或6-14(帽子到披風)才做處理
            if _SID == ItemEquipPos.Knife or (_SID >= ItemEquipPos.Hat and _SID <= ItemEquipPos.Cloak) then
                local _ItemData = ItemData.GetItemDataByIdx(_Save.m_ItemIdx) ---@type ItemData
                if _ItemData ~= nil then
                    local _EquipPos = Equipment_Model.ChangeEquipPos(_ItemData.m_EquipPosition)
                    local _Pos = Equipment_Model.CheckEquipPos(_EquipPos)
                    _TEquip[_Pos] = _Save
                end
            end
        end
        Equipment_Model.SetEquip(_TEquip)
    elseif _Kind == 5 then
        -- 5.變更物品欄某物品屬性 + ObjIndex(2) + Kind(1)
        -- kind=2 2.變更物品耐久度 + 現在耐久度(1)
        -- kind=3 3.變更物品強化等級(1)
        local _ObjIndex = iPacket:ReadInt16()
        local _InformationKind = iPacket:ReadByte()

        if _InformationKind == 1 then
            local _ItemDurability = iPacket:ReadByte()
        elseif _InformationKind == 2 then
            local _ItemGrowTimes = iPacket:ReadByte()
        end


    elseif _Kind == 6 then
        -- 6.變更某裝備欄某裝備屬性 +裝備位置(1) +kind(1)
        -- kind=1 1.變更裝備狀態 + 新裝備狀態(1)
        -- kind=2 2.變更物品耐久度 + 現在耐久度(1)
        -- kind=3 3.變更物品強化等級(1)

        local _EquipPos = iPacket:ReadByte()
        local _InformationKind = iPacket:ReadByte()

        if _InformationKind == 1 then
            local _EquipState = iPacket:ReadByte()
            Equipment_Model.RenewEquipInformation(_EquipPos, ERenewEquipmentInformationType.EquipState,_EquipState)
        elseif _InformationKind == 2 then
            local _EquipDurability = iPacket:ReadByte()
            Equipment_Model.RenewEquipInformation(_EquipPos, ERenewEquipmentInformationType.Durability,_EquipDurability)
        elseif _InformationKind == 3 then
            local _EquipGrowTimes = iPacket:ReadByte()
            Equipment_Model.RenewEquipInformation(_EquipPos, ERenewEquipmentInformationType.GrowTimes,_EquipGrowTimes)
        end
    elseif _Kind == 10 then
        -- 10.玩家登入時收到物品資料+背包(1)[=2 page2,=3 page3]+count(2)+<<物品欄位索引(2)+物品編號(4)+數量(2)+物品狀態(1)>>
        local _BagType = iPacket:ReadByte()
        local _Count = iPacket:ReadInt16()
        local _TBag = {}
        -----2022.09.08 Add by KK, 以 ItemIdx 為 Key 當搜尋
        local _TBag_SearchByItemIdx = {}
        BagMgr.SetBagTable( _TBag ,_BagType)

        for i = 1, _Count do
            local _SID, _Data = ConsumablesItemData:New(iPacket)
            _TBag[_SID] = _Data

            -- 更新SearchTable
            _TBag_SearchByItemIdx = BagMgr.SetSearchTable(_Data,_BagType)
		end
        Main_SubCtrl_AutoDrink.SetGroupPotionIcon()
		GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
    elseif _Kind == 11 then
        -- 11.新增或更新某欄位物品+背包(1)[=2 page2,=3 page3]+count(2)+<<物品欄位索引(2)+物品編號(4)+數量(2)+物品狀態(1)>>+來源種類(1) 
        -- +參數(4)[來源:17.NPC掉落 參數:NPCID]
        local _BagType = iPacket:ReadByte()
        local _Count = iPacket:ReadInt16()

        -- 獲得物品資料列表
        local _ItemGetQueue = Queue:New()
        -- 獲得物品資料列表_數量為正的
        local _PositiveItemTable = {}
        -- 獲得物品數量資訊
        local _GetItemNunInfo = {}
        for i = 1, _Count do
            local _SID, _SaveItemData = ConsumablesItemData:New(iPacket)

            --舊的物品資料
            local _CItemData = BagMgr.GetBagItemBySID(_SID,nil,_BagType)

            --更新背包各種table
            local _IsItemCountModify = BagMgr.UpdateBagTable(_SID, _SaveItemData,_BagType)

            -- 儲存獲得物品到列表
            _ItemGetQueue:Enqueue(_SaveItemData)

            ---判斷是否更新自動喝水
            if _BagType == EBagType.Potion then
                if AutoDrink_Model ~= nil then
                    AutoDrink_Model.CheckAutoDrink(_SaveItemData.m_Count > 0, _SaveItemData.m_Count > 0 and _SaveItemData or _CItemData)
                end

                if HotKey_Controller and UIMgr.IsVisible(HotKey_Controller) then
                    HotKey_Controller.RenewPotionHotKey()
                end

                if AutoDrink_Controller ~= nil and UIMgr.IsVisible(AutoDrink_Controller) then
                    GroupButton.OnPointerClickByIndex(AutoDrink_Controller.m_Group_SetPotion, AutoDrink_Controller.m_CurPotionType)
                end
            end
            
            -- 獲得物品數量計算
            local _GetItemNum = _SaveItemData.m_Count
            if _CItemData ~= nil then
                _GetItemNum = _SaveItemData.m_Count - _CItemData.m_Count
            end
            -- 紀錄獲得物品數量
            -- 同一次協定相同物品要合併計算(分堆的話 會拿到兩個SID)
            if _GetItemNunInfo[_SaveItemData.m_ItemIdx] == nil then
                -- 道具被刪除物品編號會拿到 0 要特別處理
                if _SaveItemData.m_ItemIdx ~= 0 then
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx] = {}
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum = _GetItemNum
                    _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemName = _SaveItemData.m_ItemData:ItemName()
                end
            else
                _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum = _GetItemNunInfo[_SaveItemData.m_ItemIdx].m_GetItemNum + _GetItemNum   
            end
            -- 通知特定物品刷新
            GStateObserverManager.Notify(EStateObserver.UpdateSpecificItem, _SaveItemData.m_ItemIdx)
            
        end

        -- 顯示物品獲得訊息
        for key, value in pairs(_GetItemNunInfo) do
            -- 減少不顯示
            if value.m_GetItemNum > 0 then
                local _GetItemMsg = GString.Format(TextData.Get(733),value.m_GetItemName,value.m_GetItemNum)
                ChatMgr.Add_SystemChannels(_GetItemMsg)
                MessageMgr.AddItemGetMsg(_GetItemMsg) -- 玩家使用兌換卡, 同時顯示獲得物品訊息 Add by 凌傑RM#115832 2024.12.09
                table.insert(_PositiveItemTable,key)
            end
        end
        GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
        TimeMachineMgr.NotifyControl(table.Count(_PositiveItemTable),_PositiveItemTable)
        -- 物品是NPC掉落物
        local _From = iPacket:ReadByte()
        local _NPCDrop = 17
        local _FuDia = 29 -- 福袋
        local _EventGive = 13 --事件給予
        -- 6好像是用巨神兵加的
        if _From == _NPCDrop or _From == _FuDia or _From == _EventGive or _From == 6 then
            for i = 1, _Count do
                if not _ItemGetQueue:IsEmpty() then
                    local _GetItemData = _ItemGetQueue:Dequeue()
                    -- 將道具加到新增列表中
                    if _GetItemData.m_ItemIdx == 0 then
                        break
                    end
                    
                    if _GetItemNunInfo[_GetItemData.m_ItemIdx].m_GetItemNum > 0 then
                        BagMgr.AddNewItemList(_GetItemData.m_ItemIdx, _GetItemData.m_SID,_BagType)
                    end
                    PopItemChange_Mgr.AddItem(_GetItemData.m_ItemIdx, _GetItemData.m_SID)

                    if _From == _NPCDrop then
                        local _NPCID = iPacket:ReadUInt32()
                        if _NPCID ~= nil then
                            ---將來自某怪的所有掉落物暫存到DropMgr對應table
                            DropMgr.FillItemIDData(_NPCID,_GetItemData.m_ItemIdx)
                        end
                    end
                end
            end
        end
    end
end

-- [3-3]玩家登入時收到的標記資料 +kind(1)
ProtocolMgr[3][3] = function(iPacket)
    local _Kind = iPacket:ReadByte()
    if _Kind == 1 then
        -- 永標資料+count(2)+<<永標索引(2)+永標數值(1)>>
        local _Count = iPacket:ReadInt16()
        local _Table = {}
        for i = 1, _Count do
            local _StaticFlag = StaticFlag:New(iPacket)
            _Table[_StaticFlag.m_ID] = _StaticFlag.m_Value
        end
        PlayerData.GetFlags(EFlags.Static).Init(_Table)
    elseif _Kind == 2 then
        --動標資料+count(1)+<<索引(1)+編號(2)+步驟(2)+時間(1)>>
        local _Count = iPacket:ReadByte()
        local _Table = {}
        for i = 1, _Count do
            local _Index, _MoveFlag = MovingFlag:New(iPacket)
            if _Index > 0 then
                _Table[_Index] = _MoveFlag
            end
        end
        PlayerData.GetFlags(EFlags.Move).Init(_Table)
    elseif _Kind == 3 then
        -- 計數永標資料+count(2)+<<永標索引編號(2)+數量(4)>>
        local _Count = iPacket:ReadInt16()
        local _NeedRefreshBag = false
        --local _Table = {}
        for i = 1, _Count do
            local _StaticNumFlag = StaticNumFlag:New(iPacket)
            if _StaticNumFlag.m_ID > 0 then
                --_Table[_StaticNumFlag.m_ID] = _StaticNumFlag
                PlayerData.GetFlags(EFlags.StaticNum).Init(_StaticNumFlag.m_ID,_StaticNumFlag)
            end

            local _FlagItemIdx = ItemData.GetFlagItemData(_StaticNumFlag.m_ID)
            if _FlagItemIdx ~= nil then
                _NeedRefreshBag = true
                local _TBag = BagMgr.m_TBag[EBagType.Component]
                ---2022.09.08 Add by KK, 以 ItemIdx 為 Key 當搜尋
                local _TBag_SearchByItemIdx = {}
                BagMgr.SetBagTable( _TBag, EBagType.Component, _TBag_SearchByItemIdx )


                local _SID, _Data = ComponentItemData:New(_StaticNumFlag.m_ID , _FlagItemIdx , _StaticNumFlag.m_Value)
                _TBag[_SID] = _Data

                -- 更新SearchTable
                _TBag_SearchByItemIdx = BagMgr.SetSearchTable(_Data,EBagType.Component)

            end
        end

        if _NeedRefreshBag then
            GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
        end
    elseif _Kind == 4 then
        --- 4.玩家登入時+count(1)+<<生活技能編號(1)[1.挖礦 2.採集 3.農耕 4.採藥 5.經商 6.工匠技能7.琴8.棋 9.書10.畫11.詩12.酒13.花14.茶]+等級(1)+經驗值(4)>>
        local _Count = iPacket:ReadByte()
        for i = 1, _Count do
            local _LiveSkillNo = iPacket:ReadByte()
            local _LiveSkillLevel = iPacket:ReadByte()
            local _LiveSkillExp = iPacket:ReadInt32()
            PlayerData_Attributes.SetLiveSkillLevel(_LiveSkillNo,_LiveSkillLevel,_LiveSkillExp)
        end
    elseif _Kind == 6 then
        --- 6.玩家登入時收到艙室效果記數永標+count(1)+<<編號(1)+數值(2)>>
        local _Count = iPacket:ReadByte()
        local _Table = {}
        for i = 1, _Count do
            local _StaticNumFlag = TimeMachineEffectFlag:New(iPacket,_Kind)
            if _StaticNumFlag.m_ID > 0 then
                _Table[_StaticNumFlag.m_ID] = _StaticNumFlag
            end
        end
        PlayerData.GetFlags(EFlags.TimeMachineEffect).Init(_Table)
    elseif _Kind == 7  or _Kind == 8 then
        --- 7.玩家登入時收到計數永標word的資料+count(2)+<<索引編號(2)+數量(2)>>
        --- 8.玩家登入時收到計數永標byte的資料+count(2)+<<索引編號(2)+數量(1)>>
        local _Count = iPacket:ReadUInt16()
        local _NeedRefreshBag = false
        --local _Table = {}
        for i = 1, _Count do
            local _StaticNumFlag = ExtraStaticNumFlag:New(iPacket,_Kind)
            if _StaticNumFlag.m_ID > 0 then
                --_Table[_StaticNumFlag.m_ID] = _StaticNumFlag
                PlayerData.GetFlags(EFlags.StaticNum).Init(_StaticNumFlag.m_ID,_StaticNumFlag)
            end
            local _FlagItemData = ItemData.GetFlagItemData(_StaticNumFlag.m_ID)
            if _FlagItemData ~= nil then
                local _TBag = BagMgr.m_TBag[EBagType.Component]
                local _TBag_SearchByItemIdx = {}
                BagMgr.SetBagTable( _TBag, EBagType.Component, _TBag_SearchByItemIdx )

                local _SID, _Data = ComponentItemData:New(_StaticNumFlag.m_ID , _FlagItemData , _StaticNumFlag.m_Value)
                _TBag[_SID] = _Data

                -- 更新SearchTable
                _TBag_SearchByItemIdx = BagMgr.SetSearchTable(_Data,EBagType.Component)
            end
        end
        if _NeedRefreshBag then
            GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
        end
    end
end

--- [3-4] 每過晚上12點重置標記 +kind(1)
--- 1.清永標+起始編號(2)+終止編號(2)
--- 2.清動標+起始編號(2)+終止編號(2)
--- 3.清記數永標+起始編號(2)+終止編號(2)
ProtocolMgr[3][4] = function(iPacket)
    local _Kind = iPacket:ReadByte()
    local _Start = iPacket:ReadUInt16() - 1
    local _End = iPacket:ReadUInt16() - 1

    --防呆
    if _Start > _End then
        return
    end

    if _Kind == 1 then
        -- 1.清永標
        for i = _Start, _End do
            local _StaticFlag = PlayerData.GetFlags(EFlags.Static).GetNewWhenStaticFlagRemove(i)
            if _StaticFlag ~= nil then
                PlayerData.GetFlags(EFlags.Static).Set(_StaticFlag)
            end
        end
    elseif _Kind == 2 then
        -- 2.清動標
        -- FixMe: 批次設定導致卡頓
        for i = _Start, _End do
            local _Index, _MoveFlag = MovingFlag:New(i)
            if _Index > 0 then
                PlayerData.GetFlags(EFlags.Move).Set(_MoveFlag)
            end
        end
    elseif _Kind == 3 then
        -- 3.清記數永標
        for i = _Start, _End do
            local _StaticNumFlag = StaticNumFlag:New(i)
            if _StaticNumFlag.m_ID > 0 then
                PlayerData.GetFlags(EFlags.StaticNum).Set(_StaticNumFlag)
            end
        end
    end
end

--- [3-5] 玩家武功資料
ProtocolMgr[3][5] = function(iPacket)
    local _Kind = iPacket:ReadByte()

    if _Kind == 1 then
        -- 1.玩家登入武功技能+count(2)+<<武功索引(2)+武功編號(2)+武功等級(1)+武功升級百分比(1)>> ( 武功升級百分比 已移除 #123787)
        local _Count = iPacket:ReadInt16()
        local _WugongData = {}
        for i = 1, _Count do
            local _TempWugongData = {}
            _TempWugongData.m_Idx = iPacket:ReadInt16()
            _TempWugongData.m_WugongId = iPacket:ReadInt16()
            _TempWugongData.m_Lv = iPacket:ReadByte()
            _TempWugongData.m_Percent = iPacket:ReadByte()
            _WugongData[_TempWugongData.m_WugongId] = _TempWugongData
        end
        PlayerData_Wugong.InitWugong(_WugongData)
        SkillBook_Model.Init()
    elseif _Kind == 2 then
        -- 2.新增或更新+武功索引(2)+武功編號(2)+武功等級(1)+武功升級百分比(1) ( 武功升級百分比 已移除 #123787)
        local _TempWugongData = {}
        _TempWugongData.m_Idx = iPacket:ReadInt16()
        _TempWugongData.m_WugongId = iPacket:ReadInt16()
        _TempWugongData.m_Lv = iPacket:ReadByte()
        _TempWugongData.m_Percent = iPacket:ReadByte()
        PlayerData_Wugong.AddWugong(_TempWugongData)

        if(SkillBook_Controller ~= nil) then
            GStateObserverManager.Notify(EStateObserver.UpdateSkillBook)
        end
    elseif _Kind == 3 then
        -- 3.習武結果(1)[0.成功 1.玩家等級不足 2.老師編號錯誤3.習武場景不對 4.老師沒教此武功編號
        -- 5.前置武功等級不足 6.學習屬性或偏性不符 7.武功索引錯誤 8.身上武功數量已達上限 9.使用條件不符]
        local _Result = iPacket:ReadByte()
        if _Result == 0 then
            --MessageMgr.AddCenterMsg(TextData.Get(9561))

            if(SkillBook_Controller ~= nil) then
                GStateObserverManager.Notify(EStateObserver.UpdateSkillBook)
            end
		else
			if ProjectMgr.IsShowDebug() then
				MessageMgr.AddCenterMsg(GString.Format("3-5-3 習武結果: {0} [0.成功 1.玩家等級不足 2.老師編號錯誤3.習武場景不對 4.老師沒教此武功編號 "..
					"5.前置武功等級不足 6.學習屬性或偏性不符 7.武功索引錯誤 8.身上武功數量已達上限 9.使用條件不符]", _Result))
			end
        end
    end
end

-- [3-6]虛寶(永丹)屬性
ProtocolMgr[3][6] = function(iPacket)
	local _Kind = iPacket:ReadByte()
	local _Count = 0
	local _Index = 0
	local _Value = 0
    --- 3-6-1 玩家登入時 server回傳的對應虛寶永丹的使用次數
	if _Kind == 1 then
		_Count = iPacket:ReadByte()
		for i = 1, _Count do
            --永丹的索引編號
			_Index = iPacket:ReadByte()
            --對應永丹吃過幾顆
			_Value = iPacket:ReadByte()
			if _Index <= MaxStaticPropKind then
                local _Index_Hem2 = TakemedicineData.TranslateIndexToYuDanID(_Index)
                PlayerData.Get(EPalyerData.Attributes).SetYuDanUseCodition(_Index_Hem2,_Value)
			else
				D.Log( GString.GetTextWithHex(GString.Format( "ProtocolMgr[3][6] 虛寶(永丹)屬性 {0} 超出範圍 ", _Index ),Color.Red));
			end
		end
        PlayerData_Attributes.CalculateYudanEffectData()

    --- 3-6-2 登入後 如果永丹使用次數有變化 更新對應虛寶永丹的使用次數
	elseif _Kind == 2 then
		_Index = iPacket:ReadByte()
		_Value = iPacket:ReadByte()
		if _Index <= MaxStaticPropKind then
            local _Index_Hem2 = TakemedicineData.TranslateIndexToYuDanID(_Index)
            PlayerData.Get(EPalyerData.Attributes).SetYuDanUseCodition(_Index_Hem2,_Value)
            PlayerData_Attributes.CalculateYudanEffectData()
            --永丹次數變更後後 可能造成主屬性 或者副屬性的更新
            GStateObserverManager.Notify(EStateObserver.UpdateMainAttribute)
            GStateObserverManager.Notify(EStateObserver.UpdateSubAttribute)
            --通知使用永丹的次數已經刷新
            GStateObserverManager.Notify(EStateObserver.UpdateYuDan)

		else
			D.Log( GString.GetTextWithHex(GString.Format( "ProtocolMgr[3][6] 虛寶(永丹)屬性 {0} 超出範圍 ", _Index ),Color.Red));
		end

		---TODO
		---刷新背包及負重
        GStateObserverManager.Notify(EStateObserver.BagDataRefresh)
	end
end

-- [3-9] 跨天 Client自己處理資料 [如租用物品] +Month(1)+Week(1)
-- 警告:會先收到3-9跨日後 才收到1-7時間
-- 所已不可在此跟 GData.Inst.ServerDateTime 比對時間
ProtocolMgr[3][9] = function(iPacket)
	local _CleanMonth = iPacket:ReadBoolean()
	local _CleanWeek = iPacket:ReadBoolean()
    HEMTimeMgr.m_IsDateChangeAndWaitFirstServerTimeReceived = true
end

--[3-10] 10.玩家資訊+RoleID(8)+Len(4)+Name(?)+組織編號(4)+等級(2)+魅力(4)
ProtocolMgr[3][10] = function(iPacket)
	local _RoleID = iPacket:ReadUInt64()
    local _PlayerName = iPacket:ReadString()
    local _OrqanizationNumber = iPacket:ReadInt32()
    local _Level = iPacket:ReadInt16()
    local _Charm = iPacket:ReadInt32()

    if PlayerData ~= nil then
        ---玩家擁有魅力值
        PlayerData.m_LoginPlayerData.m_Charm = _Charm
    end

    if  PlayerData_Title ~=nil then
        PlayerData_Title.RecheckCheckTitleAvailable(ETitleChckType.Charm)
    end

end

-- [3-12] 稱號相關
-- 12.稱號相關 + kind(1)
-- 1.獲得新稱號+編號(2)
-- 2.裝備某稱號結果(1)[0.成功 1.尚未獲得此稱號 ]+稱號編號(2)
ProtocolMgr[3][12] = function(iPacket)
	local _Kind = iPacket:ReadByte()
    if _Kind == 1 then
        local _NewTitleID = iPacket:ReadInt16()

        ---比較新稱號跟目前用的稱號權重 新稱號權重較大 則觸發更換詢問
        if PlayerData_Title.CompareOrder(_NewTitleID) then
            local _TitleData = TitleData.Get(_NewTitleID)
            if _TitleData ~=nil then

                local _TitleName = TextData.Get(_TitleData.m_TextID_Name)

                FastClickHint_Controller.ToOpenQueue(FastClickHint_Model.EWhichType_Page.Title, _TitleName, "", 
                    function()
                        UIMgr.Open(RoleAttribute_Controller,false,ERoleControllerSubPageType.Title,_NewTitleID)
                        end
                    )
            end
        end

    elseif  _Kind == 2 then
        local _Result = iPacket:ReadByte()
        local _TitleID = iPacket:ReadInt16()

        ---稱號裝備成功時 需要替換本地資料 並刷新介面(如果有開) 以及跳中央訊息
        if _Result == 0 then
            PlayerData_Title.SetTitleIsEquiped(_TitleID)
            PlayerData.SetTitleID(_TitleID)

            if UIMgr.IsVisible(RoleAttribute_Controller) and RoleAttribute_Page_Title.m_Page_GameObject.activeSelf then
                RoleAttribute_Page_Title.RenewDetailInfo_Single()
                ScrollView.Update(RoleAttribute_Page_Title.m_ScrollView_TitleOverview)
            end

            local _TitleData = TitleData.Get(_TitleID)

            if _TitleData ~= nil then
                local _TitleName = TextData.Get(_TitleData.m_TextID_Name)
                local _StringCenterMseeage = GString.Format(TextData.Get(9721), _TitleName)
                MessageMgr.AddCenterMsg(false, _StringCenterMseeage)
            end
        end
    end
end


--- [3-13] 設定頭像結果通知
--- 13.頭圖+kind(1)
--- 1.設定頭像結果(1)[0.成功 1.條件不足]+頭像編號(2)
--- 2.設定頭框結果(1)[0.成功 1.條件不足]+頭框編號(1)
ProtocolMgr[3][13] = function(iPacket)
	local _Kind = iPacket:ReadByte()
	local _Result = iPacket:ReadByte()

    ---Todo 接收到 設定成功 失敗時 要如何反饋
    if _Kind == 1 then
        local _HeadNo = iPacket:ReadInt16()
        local _SpriteName = HeadIconData.GetHeadIconDataByIdx(_HeadNo).m_2DImageName

        if _Result == 0 then
            --修改本地紀錄檔案名稱
            Main_SubCtrl_RoleAttribute.ChangeLocalAvatarSetting(_Kind,_HeadNo,_SpriteName)
            --修改屬性 頭像 說明面板的按鍵顯示 如果設定成功 一定有權限而且使用中
            RoleAttribute_Page_Avatar.SetInfoPanelButtonStatus(true,true)

            ---刷新目前使用中的頭像紀錄
            RoleAttribute_Page_Avatar.SetCurrentHeadFrameNumber(_HeadNo,nil)
        elseif _Result == 1 then
            --Todo 換失敗如何通知本地
        end

    elseif _Kind == 2 then
        local _FrameNo = iPacket:ReadByte()
        local _SpriteName = HeadFrameData.GetHeadFrameDataByIdx(_FrameNo).m_2DImageName

        if _Result == 0 then
            D.Log("設定頭框成功 頭框編號 = ".. _FrameNo .. " Sprite Name = " .. _SpriteName)
            --修改本地紀錄檔案名稱
            Main_SubCtrl_RoleAttribute.ChangeLocalAvatarSetting(_Kind,_FrameNo,_SpriteName)
            --修改屬性 頭像 說明面板的按鍵顯示 如果設定成功 一定有權限而且使用中
            RoleAttribute_Page_Avatar.SetInfoPanelButtonStatus(true,true)
            ---刷新目前使用中的頭框紀錄
            RoleAttribute_Page_Avatar.SetCurrentHeadFrameNumber(nil,_FrameNo)
        elseif _Result == 1 then
             --Todo 換失敗如何通知本地
        end
    end

end

--- [3-14] 我的探索積分領獎結果
--- 14.我的探索積分領獎結果(1)[0.成功 1.積分不足 2.已領過獎 3.背包空間不足] (如果成功才會送) +物品編號(4)+數量(2)
ProtocolMgr[3][14] = function(iPacket)
	local _Result = iPacket:ReadByte()
   
    --ExploreDairy_Controller.UpdateProgressBar(0)
    
    -- 如果成功
    if(_Result == 0) then

        local _ItemId = iPacket:ReadInt32()
        local _Num = iPacket:ReadInt16()

        local _Data = {}
        _Data = {}
        _Data.ID = _ItemId
        _Data.Number = _Num

        ExploreDairyMgr.UpdateCollectResult(_Data)
        --ExploreDairy_Controller.ShowReward(_Data)

    end

end

--- [3-15] 任務章節領獎結果
--- 15.任務章節領獎結果(1)[0.成功 1.條件不足2.已領過獎3.背包空間不足4.已達金錢上限5.名譽已達上限]+章節(1)
ProtocolMgr[3][15] = function(iPacket)
	local _Result = iPacket:ReadByte()
    
    -- 如果成功
    if(_Result == 0) then

        local _Chapter = iPacket:ReadByte()
        local _AllChapters = MissionMgr.GetAllChapter()
        local _AfterSorting = ExploreDairy_Model.SortShowReward(_AllChapters[_Chapter].m_UpgradeEffect)
        local _Data = {}
        local _Point = 0

        if(_AfterSorting) then
            for _key, _value in pairs(_AfterSorting) do
                _Data[_key] = {}
                _Data[_key].ID = _value.m_Idx
                _Data[_key].Number = _value.m_Value
            end

            -- 收到這個協定時 點數已經更新
            _Point =  _AllChapters[_Chapter].m_ReceivePoints
            
        end
        
        ExploreDairy_Controller.ShowReward(_Data, _Point)
        MissionBook_Controller.DoAfterGettingChapterRewards(_Chapter)
        MissionBook_Controller.ChapterClick(_Chapter)

    end

end

--- [3-16] 再造增幅系統
--- 16.再造增幅系統+功能(1)
--- 1.改造結果(1)[0.成功 1.改造中 2.進度已滿 3.每日次數已滿 4.條件不足]
--- 2.調校資料<<數值(1)>>*6
--- 3.增幅階段(1)[1-15]
--- 4.各部位改造進度<<進度(1)>>*6
--- 5.改造中資訊+部位(1)+開始年(2)+月(1)+日(1)+時(1)+分(1)
--- 6.神經覺醒資料+Count(1)+<<位置(1)+覺醒編號(2)+覺醒強度(1)>>
--- 7.部位改造完成+部位(1)+進度(1)
--- 8.神經覺醒結果+位置(1)+覺醒編號(2)+覺醒強度(1)(待確認)
--- 9.改造加速目標剩餘時間+部位(1)+時間分(4)
ProtocolMgr[3][16] = function(iPacket)
	local _Kind = iPacket:ReadByte()
    
    if _Kind == 1 then
        -- 點擊武裝增幅_改造按鈕
        local _Result = iPacket:ReadByte()
        if _Result > 0 then
            local _016_001_TextID = 10200014

            MessageMgr.AddCenterMsg(false, TextData.Get(_016_001_TextID))

        else
            if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
                ReengineeringGrowth_ArmedIncrease_Controller.IncreaseStateUpdate(nil, EReengineering_IncreaseType.Increasing)
            end
        end

    elseif _Kind == 2 then
        -- 玩家武裝調校資料(登入時傳送)
        for i = 1, ReengineeringGrowth_Model.SIX_PART do
            ReengineeringGrowth_Model.m_PartState[i] = iPacket:ReadByte()
        end

    elseif _Kind == 3 then
        -- 玩家目前的武裝增幅階段(登入時傳送)
        ReengineeringGrowth_Model.m_NowIncreaseState = iPacket:ReadByte()

    elseif _Kind == 4 then
        -- 玩家目前武裝增幅各部位的改造進度(登入時傳送)
        for i = 1, ReengineeringGrowth_Model.SIX_PART do
            ReengineeringGrowth_Model.m_NowIncreaseSchedule[i] = iPacket:ReadByte()
            ReengineeringGrowth_Model.m_NowIncreasePart[i] = false
        end
        table.Clear(ReengineeringGrowth_Model.m_NowIncreaseTime)

    elseif _Kind == 5 then
        local _Part = iPacket:ReadByte()
        local _Year = iPacket:ReadUInt16()
        local _Month = iPacket:ReadByte() 
        local _Day = iPacket:ReadByte()
        local _Hour = iPacket:ReadByte()
        local _Minute = iPacket:ReadByte()
        
        local _StartTime = os.time({year = _Year, month = _Month, day = _Day,
        hour = _Hour, min = _Minute, sec = 0})
        
        ReengineeringGrowth_ArmedIncrease_Controller.SetTime(_Part, _StartTime)
        
        if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
            ReengineeringGrowth_ArmedIncrease_Controller.UpdateSelf()
        end

    elseif _Kind == 6 then
        local _Count = iPacket:ReadByte()
        if _Count > 0 then
            for i = 1, _Count do
                local _Part = iPacket:ReadByte()
                local _Idx = iPacket:ReadUInt16()
                local _Strength = iPacket:ReadByte()
                
                local _Data = { m_Idx = _Idx, m_Strength = _Strength }
                ReengineeringGrowth_Model.m_NervousAwakeningData[_Part] = _Data
            end

            if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
                --預計寫: 顯示神經覺醒資料的反饋介面
                ReengineeringGrowth_NervousAwakening_Controller.UpdateSelf()
            end
        else
            table.Clear(ReengineeringGrowth_Model.m_NervousAwakeningData)
        end

    elseif _Kind == 7 then
        local _Part = iPacket:ReadByte()
        local _State = iPacket:ReadByte()

        ReengineeringGrowth_Model.m_NowIncreaseSchedule[_Part] = _State
        table.insert(ReengineeringGrowth_Model.m_OpenRewardViewPartIdxTable, _Part)
        table.Clear(ReengineeringGrowth_Model.m_NowIncreaseTime[_Part])
        ReengineeringGrowth_ArmedIncrease_Controller.CancelCountDown(_Part)

        if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
            local _Type = EReengineering_IncreaseType.Idle

            -- 如果當前增幅進度達到或超過十階段，則設置類型為完成
            if ReengineeringGrowth_Model.m_NowIncreaseSchedule[_Part] >= ReengineeringGrowth_ArmedIncrease_Controller._TEN_STATE then
                _Type = EReengineering_IncreaseType.Complete
            end
            ReengineeringGrowth_ArmedIncrease_Controller.IncreaseStateUpdate(_Part, _Type)
        end

    elseif _Kind == 8 then
        local _Part = iPacket:ReadByte()
        local _Idx = iPacket:ReadUInt16()
        local _Strength = iPacket:ReadByte()

        local _Data = { m_Idx = _Idx, m_Strength = _Strength }
        ReengineeringGrowth_Model.m_AwakeningTemporaryData[_Part] = _Data
        
        if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
            ReengineeringGrowth_NervousAwakening_Controller.AskAwakeChoose(_Part)
        end

    elseif _Kind == 9 then
        local _Part = iPacket:ReadByte()
        local _Min = iPacket:ReadUInt32()

        ReengineeringGrowth_ArmedIncrease_Controller.m_NowIncreaseMin[_Part] = _Min

        if UIMgr.IsVisible(ReengineeringGrowth_Controller) then
            ReengineeringGrowth_ArmedIncrease_Controller.UpdateTime(_Part)

            if ReengineeringGrowth_ArmedIncrease_Controller.m_IsAskMoney then
                ReengineeringGrowth_ArmedIncrease_Controller.OpenEstimatedCostQuery(_Part)
                ReengineeringGrowth_ArmedIncrease_Controller.m_IsAskMoney = false
            end
        end
    end
end

--- [3-17] 內功系統
ProtocolMgr[3][17] = function(iPacket)
    local _Kind = iPacket:ReadByte()

    --1.內功心法資料+數量(1)<<心法編號(1)+心法階段(1)+心法等級(1)>>
    if _Kind == 1 then
        local _Data = {}
        local _Count = iPacket:ReadByte()
        for i = 1, _Count do
            local _ID = iPacket:ReadByte()
            local _Step = iPacket:ReadByte()
            local _Lv = iPacket:ReadByte()
            _Data[_ID] = {m_Step = _Step, m_Lv = _Lv}
        end
        InnerBook_Model.SetMethodData(_Data)

    --2.更新心法資料+心法編號(1)+心法階段(1)+心法等級(1)
    elseif _Kind == 2 then
        local _Data = {}
        _Data.m_ID = iPacket:ReadByte()
        _Data.m_Step = iPacket:ReadByte()
        _Data.m_Lv = iPacket:ReadByte()
        InnerBook_Model.UpdateMethodData(_Data)

    --3.目前運行心法+心法編號(1)
    elseif _Kind == 3 then
        local _ID = iPacket:ReadByte()
        InnerBook_Model.SetCurrentMethod(_ID)

    --4.目前靈氣值(4)
    elseif _Kind == 4 then
        local _Value = iPacket:ReadInt32()
        --InnerBook_Model.SetSpiritValue(_Value)

    --5.靈脈資料+數量(1) <<目前靈脈穴位數(1)+已推演靈功數量(1)[[靈功所屬階段(1)+靈功ID(2)+靈功等級(1)]]>>*6
    elseif _Kind == 5 then
        local _Data = {}
        for i = 1, InnerBook_Model.m_MeridianCount do
            local _Point = iPacket:ReadByte()
            local _Step = math.floor(_Point * 0.1) + 1
            local _Point = _Point % 10
            local _WugongCount = iPacket:ReadByte()
            ---六脈靈功資料
            local _WugongData = {}
            for j = 1, _WugongCount do
                local _Step = iPacket:ReadByte()
                local _ID = iPacket:ReadUInt16()
                local _Lv = iPacket:ReadByte()
                _WugongData[_Step] = {m_ID = _ID, m_Lv = _Lv}
            end
            table.insert(_Data, {m_Step = _Step, m_Point = _Point, m_WugongData = _WugongData})
        end
        InnerBook_Model.SetAllSpiritData(_Data)

    --6.靈功推演資料+靈脈位置(1)+靈功所屬階段(1)+靈功ID(2)
    elseif _Kind == 6 then
        local _Data = {}
        _Data.m_MeridianID = iPacket:ReadByte()
        _Data.m_Step = iPacket:ReadByte()
        _Data.m_WugongID = iPacket:ReadUInt16()
        SixSpirit_Controller.CheckSpiritWugongData(_Data)

    --7.目前裝備靈功+靈功_ID(2)*6
    elseif _Kind == 7 then
        local _Data = {}
        for i = 1, InnerBook_Model.m_MeridianCount do
            local _WugongID = iPacket:ReadUInt16()
            table.insert(_Data, _WugongID)
        end
        InnerBook_Model.SetCurrentWugong(_Data)

    --8.訊息(1)[1.心法升級失敗 2.心法進階失敗 3.靈脈貫通失敗 4.靈脈進階失敗 5.靈功推演失敗 6.心法運行失敗 7.裝備靈功失敗 8.靈功升級失敗]
    elseif _Kind == 8 then
        local _Result = iPacket:ReadByte()
        if _Result == 1 then
            D.Log("心法升級失敗")
        elseif _Result == 2 then
            D.Log("心法進階失敗")
        elseif _Result == 3 then
            D.Log("靈脈貫通失敗")
        elseif _Result == 4 then
            D.Log("靈脈進階失敗")
        elseif _Result == 5 then
            D.Log("靈功推演失敗")
        elseif _Result == 6 then
            D.Log("心法運行失敗")
        elseif _Result == 7 then
            D.Log("裝備靈功失敗")
        elseif _Result == 8 then
            D.Log("靈功升級失敗")
        end

    --9.靈脈資料更新
    elseif _Kind == 9 then
        local _Type = iPacket:ReadByte()
        local _Data = {}
        --1.靈脈穴位+靈脈位置(1)+穴位數(1) 
        if _Type == 1 then
            _Data.m_MeridianID = iPacket:ReadByte()
            local _Point = iPacket:ReadByte()
            _Data.m_Step = math.floor(_Point * 0.1) + 1
            _Data.m_Point = _Point % 10
            _Data.m_WugongData = InnerBook_Model.m_MeridianData[_Data.m_MeridianID].m_WugongData
            InnerBook_Model.SetPerSpiritData(_Data, InnerBook_Model.m_SpiritUpdateType.Spirit)

        --2.靈功資料+靈脈位置(1)+靈功所屬階段(1)+靈功ID(2)+靈功等級(1)
        elseif _Type == 2 then
            _Data.m_MeridianID = iPacket:ReadByte()
            _Data.m_Step = InnerBook_Model.m_MeridianData[_Data.m_MeridianID].m_Step
            _Data.m_Point = InnerBook_Model.m_MeridianData[_Data.m_MeridianID].m_Point
            _Data.m_WugongStep = iPacket:ReadByte()
            _Data.m_WugongID = iPacket:ReadUInt16()
            local _Lv = iPacket:ReadByte()
            local _WugongData = InnerBook_Model.m_MeridianData[_Data.m_MeridianID].m_WugongData
            _WugongData[_Data.m_WugongStep] = {m_ID = _Data.m_WugongID, m_Lv = _Lv}
            _Data.m_WugongData = _WugongData
            InnerBook_Model.SetPerSpiritData(_Data, InnerBook_Model.m_SpiritUpdateType.Wugong)
        end

    --10.靈氣解放
    elseif _Kind == 10 then
        --心法編號(1)
        local _Method = iPacket:ReadByte()
        InnerBook_Model.SetSpiritLiberation(_Method)
    end
end
