---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---活動介面Controller
---@class Activity_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2024.7.30
Activity_Controller = {}
local this = Activity_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Activity_View", "Activity_Controller", EUIOrderLayers.FullPage, true, "bg_005")

require( "UI/Activity/Activity_Model")

local m_ImageString = "Campaign_"
local m_Image_I_String = "Campaign_I_"
local m_MainMoveDis = 447
local m_Table_MainUnit = {}
local m_FullPageMenuLeft = {}
local m_Table_DungeonUnit = {}
local Content_Interval_Size = 10
local BaseRewardID = {95007, 95005, 95010}

local _StarKind = {Normal = 1, Pass = 2, No = 3, Achieve = 4}
local _StarColor = {
    Extension.GetColor("#113B62"),
    Extension.GetColor("#622411"),
    Extension.GetColor("#5D5D5D"),
    Extension.GetColor("#FFC600")
}

local _StateKind = {Normal = 1, Highlighted = 2, Press = 3, Select = 4, Disable = 5}

local _GroupTabColor_Frame = {}
local _GroupTabColor_BG = {}
local _GroupTabText_Style = {}

---圖片載入完成的計數器 
local m_LoadCount = 0
---要顯示的活動 Table
local m_Table_ShowMain = {}
---活動顯示批次處理
local m_MainVisibleBatch
---副本顯示批次處理
local m_DungeonVisibleBatch
---活動動態效果移動秒數
local MAIN_EFFECT_MOVE_TIME = 0.8
---動態效果間隔秒數
local EFFECT_PER_TIME = 0.1
---副本列表動態效果初始位置
local DUNGEON_EFFECT_INIT_POSY = 435
---副本資訊動態效果初始位置
local TRANS_EFFECT_INIT_POSX = 120
---副本資訊動態效果目標位置
local TRANS_EFFECT_TARGET_POSX = 1.29
---副本資訊動態效果秒數
local TRANS_EFFECT_TIME = 1

---移到指定副本延遲間隔秒數
local ShowSpecifyDungeon_DelayPerTime = 0.12
---副本物件高度修正值
local DungeonObj_Fix_Height = 9

---副本類型
this.m_SelectType = 0
---副本難度分類
this.m_ModelType = 1
---目前選到的副本編號
this.m_SelectDungeonIdx = 0
---紀錄該類型第一個副本編號
this.m_FirstDungeon = 0
---主畫面點擊的類型Idx
this.m_ClickIdx = 0
---寶箱星級物品資料
this.m_TreasureData = {}
---掃蕩副本獲得物品資料
this.m_GetItemData = {}
---獎勵物品資料
this.m_ContentRewardData = {}

local _IconTable = {"Common_time_but005D", "Common_time_but005D", "Common_time_but005D", "Common_time_but005D", "Common_time_but005D", "Common_time_but005D"}

---教學使用 要配合企劃的物件命名前綴
local m_ButtonMain_Prefix = "&Button_Main_kind_"

---初始化
function Activity_Controller.Init()
    Activity_Model.Init()
    --region Group_Top
    this.m_Btn_Back = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Back"))
    this.m_Btn_Back:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.OnClick_Back() end)

    this.m_Btn_Close = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Close"))
    this.m_Btn_Close:AddListener(EventTriggerType.PointerClick, function() UIMgr.Close(Activity_Controller) end)
    ---資源列
	this.m_Group_Resources = {}
	this.m_Group_Resources.transform = this.m_ViewRef.m_Dic_Trans:Get("&Group_Resources")
    local _CurrentResourceTable = 
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
	this.m_Group_Resources.m_Resources = ResourceBar:InitResourceBar(this.m_Group_Resources.transform, _CurrentResourceTable)
    --endregion

    --region Group_Main
    this.m_Group_Main = this.m_ViewRef.m_Dic_Trans:Get("&Group_Main")
    this.m_ScrollView_Main = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Main"):GetComponent("UIScrollView")
    this.m_ScrollView_Main:SetReverseScroll(true)

    this.m_Button_Main = this.m_ViewRef.m_Dic_Trans:Get("&Button_Main").gameObject
    this.m_GObj_Main = Extension.CreatePrefabObjPool(this.m_Button_Main, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    this.m_Content_Main = this.m_ViewRef.m_Dic_Trans:Get("&Content_Main")
    this.m_Content_Main_Rect = this.m_Content_Main.gameObject:GetComponent("RectTransform")

    this.m_Btn_LeftArrow = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_LeftArrow"))
    this.m_Btn_LeftArrow:AddListener(EventTriggerType.PointerClick, Activity_Controller.OnClick_LeftArrow)
    this.m_Btn_RightArrow = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_RightArrow"))
    this.m_Btn_RightArrow:AddListener(EventTriggerType.PointerClick, Activity_Controller.OnClick_RightArrow)
    --endregion

    --region Group_Dungeon
    this.m_Group_Dungeon = this.m_ViewRef.m_Dic_Trans:Get("&Group_Dungeon")

    this.m_Btn_Schedule = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Schedule"))
    this.m_Btn_Schedule:AddListener(EventTriggerType.PointerClick, Activity_Controller.OnClick_Schedule)
    
    this.m_GroupTab_Type = this.m_ViewRef.m_Dic_Trans:Get("&GroupTab_Type")
    this.m_Btn_ModelType = {}
    this.m_Image_ModelType_Frame = {}
    this.m_Image_ModelType_BG = {}
    this.m_Text_ModelType = {}
    for i = 1, 3 do
        this.m_Btn_ModelType[i] = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Type_" .. i))
        this.m_Btn_ModelType[i]:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.OnClick_ModelType(i) end)
        this.m_Image_ModelType_Frame[i] = this.m_Btn_ModelType[i].transform:GetComponent("UIRenderChangeColor")
        this.m_Image_ModelType_BG[i] = this.m_Btn_ModelType[i].transform:Find("Image_BG"):GetComponent("UIRenderChangeColor")
        this.m_Text_ModelType[i] = this.m_Btn_ModelType[i].transform:Find("Text_Info"):GetComponent("UIRenderTMPTextChangeStyle")
    end
    _GroupTabColor_Frame[_StateKind.Normal] = this.m_Image_ModelType_Frame[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal)
    _GroupTabColor_Frame[_StateKind.Select] = this.m_Image_ModelType_Frame[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Selected)
    _GroupTabColor_BG[_StateKind.Normal] = this.m_Image_ModelType_BG[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal)
    _GroupTabColor_BG[_StateKind.Select] = this.m_Image_ModelType_BG[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Selected)
    _GroupTabText_Style[_StateKind.Normal] = this.m_Text_ModelType[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal)
    _GroupTabText_Style[_StateKind.Select] = this.m_Text_ModelType[1].m_GroupRenderInfo:GetRenderValue(ESelectionState.Selected)

    this.m_Transform_MainPanel = this.m_ViewRef.m_Dic_Trans:Get("&Group_Dungeon")
    m_FullPageMenuLeft = LeftGroupTab.New(this.m_Transform_MainPanel, 1, _IconTable, function(iIndex)
        if m_FullPageMenuLeft.m_Table_PageButtons[iIndex].m_Data then
            Activity_Controller.OnClick_Type(m_FullPageMenuLeft.m_Table_PageButtons[iIndex].m_Data[1].m_ActivityType)
        end
    end)

    this.m_Button_Dungeon = this.m_ViewRef.m_Dic_Trans:Get("&Button_Dungeon").gameObject
    this.m_Button_Dungeon_Height = this.m_Button_Dungeon:GetComponent("RectTransform").rect.height
    this.m_GObj_Dungeon = Extension.CreatePrefabObjPool(this.m_Button_Dungeon, Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))
    this.m_Content_Dungeon = this.m_ViewRef.m_Dic_Trans:Get("&Content_Dungeon")

    this.m_Trans_Content_Rect = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Content"):GetComponent("RectTransform")
    this.m_CanvasGroup_Trans = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Content"):GetComponent("CanvasGroup")
    --endregion

    Activity_Controller.InitContent()
    Activity_Controller.InitWindow()
end

function Activity_Controller.Open(iParam)
    this.m_Group_Resources.m_Resources:OnUpdate()
    this.m_ModelType = 0
    Activity_Controller.SetWindowActive(false)
    local _Idx = iParam[1]
    this.m_ShowSpecifyDungeon = not (_Idx == 0 or _Idx == nil)
    if _Idx == 0 or _Idx == nil then
        this.m_ModelType = 1
        local _isOpenMain = not RecruitMgr.m_isPreviousActivity
        Activity_Controller.ShowGroupMain(_isOpenMain)
        Activity_Controller.ShowGroupDungeon(not _isOpenMain)
        RecruitMgr.m_isPreviousActivity = false
    else
        local _data = CampaignData.GetCampaignDataBym_Idx(_Idx)
        this.m_SelectType = _data.m_ActivityType
        for k, v in pairs(Activity_Model.m_CampaignSortData) do
            if v[1].m_ActivityType == this.m_SelectType then
                this.m_ClickIdx = k
                break
            end
        end
        this.m_ModelType = _data.m_ModelType
        this.m_FirstDungeon = _Idx
        Activity_Controller.ShowGroupMain(false)
        Activity_Controller.ShowGroupDungeon(true)
    end

    GStateObserverManager.Register(EStateObserver.UISellRefresh, this)
    return true
end

function Activity_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.UISellRefresh, this)
    return true
end

function Activity_Controller.OnDestroy()
    m_Table_MainUnit = {}
    m_FullPageMenuLeft = {}
    m_Table_DungeonUnit = {}
    return true
end

function Activity_Controller.SetWindowActive(iShow)
    this.m_Trans_Window.gameObject:SetActive(iShow)
    this.m_Trans_Window_BG.gameObject:SetActive(iShow)
end

function Activity_Controller.OnClick_Back()
    if this.m_Group_Main.gameObject.activeSelf then
        UIMgr.CloseToPreviousPage(Activity_Controller)
    else
        Activity_Controller.ShowGroupMain(true)
        Activity_Controller.ShowGroupDungeon(false)
    end
end

--region 初始化
function Activity_Controller.InitContent()
    this.m_Btn_Reset = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Reset"))
    this.m_Btn_Reset:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.OnClick_Reset() end)
    this.m_Text_RemainingCount = this.m_ViewRef.m_Dic_TMPText:Get("&Text_RemainingCount")
    this.m_Image_ActivityUp = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Image_ActivtyUp"))
    this.m_Image_ActivityUp:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.SetWindowInfo(EWindowClickType.ActivityUp) end)
    
    this.m_Image_Content_Pic_Mask = this.m_ViewRef.m_Dic_Trans:Get("&Image_Content_Pic_Mask")
    this.m_Image_Content_Pic = this.m_ViewRef.m_Dic_RawImage:Get("&Image_Content_Pic")
    this.m_Btn_Content_Question = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Question_Content"))
    this.m_Btn_Content_Question:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.SetWindowInfo(EWindowClickType.StarCondition) end)
    this.m_Image_Content_Star = {}
    for i = 1, 3 do
        this.m_Image_Content_Star[i] = this.m_ViewRef.m_Dic_Trans:Get("&Image_Content_Star_" .. i).gameObject:GetComponent("Image")
    end
    this.m_Text_Content_Lv = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Lv")
    this.m_Text_Content_Name = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Name")
    this.m_Text_Content_StarCaption = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_StarCaption")
    this.m_Trans_Star = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Star")
    this.m_Text_Content_Open = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Open")
    this.m_Text_Content_Limit = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Limit")
    this.m_Text_Content_Caption = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Caption")
    this.m_Rect_Content_Open = this.m_Text_Content_Open.gameObject:GetComponent("RectTransform")
    this.m_Rect_Content_Limit = this.m_Text_Content_Limit.gameObject:GetComponent("RectTransform")
    this.m_Rect_Content_Caption = this.m_Text_Content_Caption.gameObject:GetComponent("RectTransform")
    this.m_Trans_Content_Scorll = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Content_Scroll")
    this.m_Group_Content_Caption = this.m_ViewRef.m_Dic_Trans:Get("&Group_Content_Caption"):GetComponent("RectTransform")
    this.m_Text_Content_Reward = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Content_Reward")
    this.m_Trans_Reward_Type0 = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Reward_Type0")
    this.m_Trans_Reward_Type1 = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Reward_Type1")

    local _ScrollView = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_Reward_Type0")
    local _ReuseItme = this.m_ViewRef.m_Dic_Trans:Get("&Item_Reward").gameObject
    this.m_ScrollView_Reward_Type0 = ScrollView.Init(_ScrollView, true, _ReuseItme,
        Activity_Controller.GetCount_ContentReward,
        Activity_Controller.AfterReuseItemInit_ContentReward,
        Activity_Controller.AfterReuseItemIndexUpdate_ContentReward,
        true)

    this.m_Btn_Content_Reward = {}
    for i = 1, 3 do
        this.m_Btn_Content_Reward[i] = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Reward_" .. i))
        this.m_Btn_Content_Reward[i]:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.OnClick_Reward(i) end)
    end

    this.m_Btn_Content = {}
    for i = 1, 3 do
        this.m_Btn_Content[i] = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Content_" .. i))
        this.m_Btn_Content[i]:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.OnClick_ContentBtn(i) end)
    end

    this.m_Text_PassLimit = this.m_ViewRef.m_Dic_TMPText:Get("&Text_PassLimit")
end

function Activity_Controller.InitWindow()
    this.m_Trans_Window_BG = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Trans_Window_BG"))
    this.m_Trans_Window_BG:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.SetWindowActive(false) end)
    this.m_Trans_Window = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Window")
    this.m_Btn_Window_Close = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Window_Close"))
    this.m_Btn_Window_Close:AddListener(EventTriggerType.PointerClick, function() Activity_Controller.SetWindowActive(false) end)
    this.m_Text_Window_Title = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Window_Title")
    this.m_Text_Window_Caption = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Window_Caption")
    this.m_Group_StarCaption = this.m_ViewRef.m_Dic_Trans:Get("&Group_StarCaption")
    this.m_Trans_StarCaption = {}
    this.m_Data_StarCaption = {}
    for i = 1, 3 do
        this.m_Trans_StarCaption[i] = this.m_ViewRef.m_Dic_Trans:Get("&Trans_StarCaption_" .. i)
        this.m_Data_StarCaption[i] = {}
        this.m_Data_StarCaption[i].Image_Star = this.m_Trans_StarCaption[i].gameObject.transform:Find("Image_Star"):GetComponent("Image")
        this.m_Data_StarCaption[i].Text_Caption = this.m_Trans_StarCaption[i].gameObject.transform:Find("Text_Caption"):GetComponent(typeof(TMPro.TextMeshProUGUI))
    end
end
--endregion

--region 主介面
function Activity_Controller.OnClick_LeftArrow()
    local _pos = this.m_Content_Main_Rect.anchoredPosition
    local _moveDic = _pos.x > 0 and m_MainMoveDis or -m_MainMoveDis
    local _count = math.abs(math.ceil(_pos.x / _moveDic))
    if _pos.x < 0 then
        _pos.x = (_count - 1) * -m_MainMoveDis
        this.m_Content_Main_Rect.anchoredPosition = _pos
    end
end

function Activity_Controller.OnClick_RightArrow()
    local _pos = this.m_Content_Main_Rect.anchoredPosition
    local _moveDic = _pos.x > 0 and m_MainMoveDis or -m_MainMoveDis
    local _count = math.abs(math.floor(_pos.x / _moveDic))
    _pos.x = -((_count + 1) * m_MainMoveDis)
    if _pos.x >= -((table.Count(Activity_Model.m_CampaignSortData) - 4) * m_MainMoveDis) then
        this.m_Content_Main_Rect.anchoredPosition = _pos
    end
end

function Activity_Controller.ShowGroupMain(iBool)
    this.m_Group_Main.gameObject:SetActive(iBool)
    if iBool then
        ---把 ScrollView 關起來 特效演完才能滑
        this.m_ScrollView_Main.enabled = false
        Activity_Controller.SetScrollView_Main()
    end
end

function Activity_Controller.ShowGroupDungeon(iBool)
    this.m_Group_Dungeon.gameObject:SetActive(iBool)
    if iBool then
        Activity_Controller.SetWindowActive(false)
        Activity_Controller.ShowGroupTab_Type()
        Activity_Controller.SetRemainingCount()
        Activity_Controller.SetScrollView_Type()
        Activity_Controller.SetTypeBtnState(this.m_ClickIdx)
        Activity_Controller.SetScrollView_Dungeon()
        Activity_Controller.SetContentInfo(this.m_FirstDungeon)
    end
end

function Activity_Controller.SetScrollView_Main()
    for k, v in pairs(m_Table_MainUnit) do
        v.gameObject:SetActive(false)
    end
    for k, _data in pairs(Activity_Model.m_CampaignSortData) do
        local _Unit
        local _MainUnit = {}

        if m_Table_MainUnit[k] then
            _MainUnit = m_Table_MainUnit[k]
        else
            _Unit = this.m_GObj_Main:Get()
            _MainUnit.gameObject = _Unit
            _MainUnit.Image_BG = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_BG")
            _MainUnit.Image_TypePic = _MainUnit.Image_BG.transform:Find("Image_TypePic"):GetComponent("RawImage")
            _MainUnit.Image_Lock = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_Lock")
            _MainUnit.Image_Up = _Unit.gameObject.transform:Find("EffectFrame_Main/Image_Up")

            _MainUnit.Text_Title = _Unit.gameObject.transform:Find("EffectFrame_Main/Text_Title"):GetComponent(typeof(TMPro.TextMeshProUGUI))
            _MainUnit.Text_Describe = _Unit.gameObject.transform:Find("EffectFrame_Main/Text_Describe"):GetComponent(typeof(TMPro.TextMeshProUGUI))
            _MainUnit.Text_Factor = _Unit.gameObject.transform:Find("EffectFrame_Main/Text_Factor"):GetComponent(typeof(TMPro.TextMeshProUGUI))

            _MainUnit.Lock = false
            _MainUnit.MainButton = Button.New(_Unit.gameObject)
            _MainUnit.MainButton:AddListener(EventTriggerType.PointerClick, function()
                this.m_ClickIdx = k
                Activity_Controller.OnClick_Main(_data[1].m_ActivityType, _MainUnit.Lock)
            end)
            
            _MainUnit.gameObject.transform:SetParent(this.m_Content_Main.transform)
            _MainUnit.gameObject.transform.localScale = Vector3.one

            _MainUnit.ChangeState = function(iKind)
                local _State = iKind == _StateKind.Normal and 0 or 1
                _MainUnit.MainButton:ChangeStateTransitionGroup(_State)
                local _Color = iKind == _StateKind.Normal and Color.White or Extension.GetColor("#5B5B5B")
                _MainUnit.Image_TypePic.color = _Color
            end
        end

        _MainUnit.gameObject.name = m_ButtonMain_Prefix .. tostring(_data[1].m_ActivityType) 

        local _ImageString = m_ImageString .. GFunction.Zero_stuffing(_data[1].m_ActivityTypeImage, 4)
        TextureMgr.Load(_ImageString, true, function(iTex)
            if iTex ~= nil then
                _MainUnit.Image_TypePic.texture = iTex
                _MainUnit.Image_BG.gameObject:SetActive(true)
                ---圖都 Load 完才跑動態效果
                Activity_Controller.MainContentEffect()
            else
                _MainUnit.Image_BG.gameObject:SetActive(false)
            end
        end)
    
        _MainUnit.Image_Lock.gameObject:SetActive(false)
        _MainUnit.Image_Up.gameObject:SetActive(false)
        _MainUnit.Text_Title.text = TextData.Get(_data[1].m_TitleString)
        _MainUnit.Text_Describe.text = TextData.Get(_data[1].m_DescribeString)
    
        local _InTime = Activity_Model.GetIsInTime(_data)
        local _isOpenTime = Activity_Model.GetIsTimePeriod(_data)
        local _LowerLv = Activity_Model.GetLowerLv(_data)
        if PlayerData.GetLv() >= _LowerLv then
            if _data[1].m_LimitedEvent == 0 then
                if _InTime and _isOpenTime then
                    _MainUnit.Text_Factor.text = TextData.Get(20500004)--"每日開放"
                    _MainUnit.Lock = false
                    _MainUnit.ChangeState(_StateKind.Normal)
                else
                    _MainUnit.Text_Factor.text = TextData.Get(20500005)--"非活動時間"
                    _MainUnit.Image_Lock.gameObject:SetActive(true)
                    _MainUnit.Lock = true
                    _MainUnit.ChangeState(_StateKind.Disable)
                end
            else
                local _formatStr = TextData.Get(20500002)

                ---跟據企劃需求 取得最長活動期間的那個活動來顯示日期
                local _LongestPerod = 0
                local _LongestIndex = 1
                for i = 1, table.Count(_data) do
                    
                    if Activity_Model.CheckInTime(_data[i].m_Start.m_Month, _data[i].m_Start.m_Day, _data[i].m_End.m_Month, _data[i].m_End.m_Day) then
                        local _ActivityPeriod = CampaignData.GetCampaignOpenTimeBym_Idx(_data[i].m_Idx)

                        if _ActivityPeriod > _LongestPerod  then
                            _LongestPerod = _ActivityPeriod
                            _LongestIndex = i
                        end
                    end
                end

                _MainUnit.Text_Factor.text = GString.Format(_formatStr,
                    _data[_LongestIndex].m_Start.m_Month, _data[_LongestIndex].m_Start.m_Day, _data[_LongestIndex].m_End.m_Month, _data[_LongestIndex].m_End.m_Day)

            end
        else
            local _formatStr = TextData.Get(20500003)
            _MainUnit.Text_Factor.text = GString.Format(_formatStr, _data[1].m_LimitLv_Low)
            _MainUnit.Image_Lock.gameObject:SetActive(true)
            _MainUnit.Lock = true
            _MainUnit.ChangeState(_StateKind.Disable)
        end

        local _IsActivityUp = RecruitMgr.GetIsActivityUp(_data[1].m_ActivityType)
        _MainUnit.Image_Up.gameObject:SetActive(_IsActivityUp)

        ---紀錄要顯示的活動
        if _data[1].m_LimitedEvent == 0 or _InTime then
            m_Table_ShowMain[k] = _MainUnit
        end

        --_MainUnit.gameObject:SetActive(_data[1].m_LimitedEvent == 0 or _InTime)
        m_Table_MainUnit[k] = _MainUnit
    end
end

function Activity_Controller.OnClick_Main(iIdx, iLock)
    if not iLock then
        this.m_SelectType = iIdx
        Activity_Controller.ShowGroupMain(false)
        Activity_Controller.ShowGroupDungeon(true)
    end
end
--endregion

--副本
--region 副本種類頁籤
function Activity_Controller.ShowGroupTab_Type()
    local _CampaignData = RecruitMgr.GetCampaignTypeData(this.m_SelectType)
    this.m_GroupTab_Type.gameObject:SetActive(_CampaignData[1].m_ModelType ~= 0)
    for i = 1, 3 do
        this.m_Btn_ModelType[i].gameObject:SetActive(false)
        for k, v in pairs(_CampaignData) do
            if v.m_ModelType == i then
                this.m_Btn_ModelType[i]:SetText(TextData.Get(v.m_BookmarkString))
                this.m_Btn_ModelType[i].gameObject:SetActive(true)
                local _TypeDungeonData = Activity_Model.GetTypeDungeonData(i, this.m_SelectType)
                local _SortData = RecruitMgr.SortDungeon(_TypeDungeonData)
                if table.Count(_SortData) == 0 then
                    this.m_Btn_ModelType[i]:SetDisable()
                else
                    this.m_Btn_ModelType[i]:SetEnable()
                end
                break
            end
        end
        if this.m_ModelType == i then
            Activity_Controller.SetModelTypeRender(i, _StateKind.Select)
        else
            Activity_Controller.SetModelTypeRender(i, _StateKind.Normal)
        end
        this.m_Btn_ModelType[i]:SetSelect(this.m_ModelType == i)
    end
end

function Activity_Controller.OnClick_ModelType(iIdx)
    Activity_Controller.SetModelTypeClick(iIdx)
    this.m_ModelType = iIdx
    this.m_ShowSpecifyDungeon = false
    Activity_Controller.SetScrollView_Dungeon()
    Activity_Controller.SetContentInfo(this.m_FirstDungeon)
end

function Activity_Controller.SetModelTypeClick(iIdx)
    Activity_Controller.SetModelTypeRender(this.m_ModelType, _StateKind.Normal)
    this.m_Btn_ModelType[this.m_ModelType]:SetSelect(false)
    Activity_Controller.SetModelTypeRender(iIdx, _StateKind.Select)
    this.m_Btn_ModelType[iIdx]:SetSelect(true)
end

function Activity_Controller.SetModelTypeRender(iIdx, iType)
    this.m_Image_ModelType_Frame[iIdx].m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _GroupTabColor_Frame[iType])
    this.m_Image_ModelType_BG[iIdx].m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _GroupTabColor_BG[iType])
    this.m_Text_ModelType[iIdx].m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _GroupTabText_Style[iType])
end
--endregion

--region 剩餘次數
function Activity_Controller.SetRemainingCount()
    local _RemainingCount = RecruitMgr.SetRemainingCount(this.m_SelectType)
    this.m_Text_RemainingCount.text = _RemainingCount
end

function Activity_Controller.OnClick_Reset()
    local _CampaignPassData = CampaignPassData.GetCampaignPassDataByIdx(this.m_SelectType)
    local _passCount = PlayerData.GetFlags(EFlags.StaticNum).Get(_CampaignPassData.m_EnterCountStaticFlag)
    if _passCount == 0 then
        MessageMgr.AddCenterMsg(true, TextData.Get(20510002))
    else
        local _SellData = SellData.GetSellDataByIdx(_CampaignPassData.m_LimitResetItemSellID)
        local _SellStr = {}
        local _ItemData = ItemData.GetItemDataByIdx(_SellData.m_ItemOne)
        _SellStr[1] = TextData.Get(_ItemData.m_Idx_ItemNameText)
        SellMgr.OpenUISell(525, _SellStr, SellMgr.SendProtocol_012_001, {EUISellProtocolType.UseSellWithOption, this.m_SelectType})
    end
end
--endregion

--region 副本類型
function Activity_Controller.SetScrollView_Type()
    for k, v in pairs(m_FullPageMenuLeft.m_Table_PageButtons) do
        v.gameObject:SetActive(false)
    end
    for k, _data in pairs(Activity_Model.m_CampaignSortData) do
        local _TypeUnit = m_FullPageMenuLeft:GetButtonTableByIndex(k)

        if _TypeUnit.Lock == nil then
            _TypeUnit.Lock = false
        end
        _TypeUnit.m_Data = _data

        _TypeUnit.m_Image_Label.gameObject:SetActive(false)
        _TypeUnit.m_TMP_Label.text = TextData.Get(_TypeUnit.m_Data[1].m_TitleString)
        
        local _InTime = Activity_Model.GetIsInTime(_TypeUnit.m_Data)
        local _isOpenTime = Activity_Model.GetIsTimePeriod(_TypeUnit.m_Data)
        local _LowerLv = Activity_Model.GetLowerLv(_TypeUnit.m_Data)
        if PlayerData.GetLv() >= _LowerLv then
            if _TypeUnit.m_Data[1].m_LimitedEvent == 0 then
                if _InTime and _isOpenTime then
                    _TypeUnit.Lock = false
                else
                    _TypeUnit.Lock = true
                end
            else
                _TypeUnit.Lock = false
            end
        else
            _TypeUnit.Lock = true
        end

        if _TypeUnit.Lock then
            _TypeUnit.m_Button:SetDisable()
        else
            _TypeUnit.m_Button:SetEnable()
        end

        local _IsActivityUp = RecruitMgr.GetIsActivityUp(_TypeUnit.m_Data[1].m_ActivityType)
        _TypeUnit.m_Image_Label.gameObject:SetActive(_IsActivityUp)

        _TypeUnit.gameObject:SetActive(_TypeUnit.m_Data[1].m_LimitedEvent == 0 or _InTime)
    end
end

function Activity_Controller.SetTypeBtnState(iIdx)
    for k, v in pairs(m_FullPageMenuLeft.m_Table_PageButtons) do
        v.m_Button:SetSelect(v.m_Button.m_ButtonEx.m_IndexInGroup + 1 == iIdx)
    end
end

function Activity_Controller.OnClick_Type(iIdx, iLock)
    this.m_ModelType = 1
    this.m_ShowSpecifyDungeon = false
    if iLock then
        MessageMgr.AddCenterMsg(false, TextData.Get(20500111))
    else
        this.m_SelectType = iIdx
        Activity_Controller.ResetDungeonEffect()
        Activity_Controller.ShowGroupTab_Type()
        Activity_Controller.SetRemainingCount()
        Activity_Controller.SetScrollView_Dungeon()
        Activity_Controller.SetContentInfo(this.m_FirstDungeon)
    end
end
--endregion

--region 副本列表
function Activity_Controller.SetScrollView_Dungeon()
    for k, v in pairs(m_Table_DungeonUnit) do
        v.gameObject:SetActive(false)
    end
    local _TypeDungeonData = Activity_Model.GetTypeDungeonData(this.m_ModelType, this.m_SelectType)
    local _SortData = RecruitMgr.SortDungeon(_TypeDungeonData)
    if not this.m_ShowSpecifyDungeon then
        this.m_FirstDungeon = table.Count(_SortData) ~= 0 and _SortData[1].m_Idx or 0
    end
    for k, _data in pairs(_SortData) do
        local _Unit
        local _DungeonUnit = {}

        if m_Table_DungeonUnit[k] then
            _DungeonUnit = m_Table_DungeonUnit[k]
        else
            _Unit = this.m_GObj_Dungeon:Get()
            
            _DungeonUnit.gameObject = _Unit
            _DungeonUnit.m_MainBtn = Button.New(_Unit.gameObject)
            _DungeonUnit.m_Image_BG = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Image_BG")
            _DungeonUnit.m_MainImg = _DungeonUnit.m_Image_BG.transform:Find( "RawImage" ):GetComponent("RawImage")
            _DungeonUnit.m_Text_Name = _Unit.gameObject.transform:Find( "EffectFrame_Dungeon/Text_Name" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
            _DungeonUnit.m_SelectedBG = _Unit.gameObject.transform:Find( "EffectFrame_Dungeon/Image_BG_Light" ).gameObject
            _DungeonUnit.m_Image_Lv = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Image_Lv"):GetComponent(typeof(Image))
            _DungeonUnit.m_Text_Lv = _DungeonUnit.m_Image_Lv.transform:Find( "Text_Lv"):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
            _DungeonUnit.m_Image_Frame = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Image_Frame")
            _DungeonUnit.m_Image_State = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Image_State")
            _DungeonUnit.m_Trans_State = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Trans_State")
            _DungeonUnit.m_Trans_Star = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Trans_Star")
            _DungeonUnit.m_Image_Star = {}
            for i = 1, 3 do
                _DungeonUnit.m_Image_Star[i] = _DungeonUnit.m_Trans_Star.transform:Find("Image_Star_" .. i).gameObject:GetComponent("Image")
            end
            _DungeonUnit.m_Image_Icon = {}
            _DungeonUnit.m_ItemIcon = {}
            for i = 1, 3 do
                _DungeonUnit.m_Image_Icon[i] = _Unit.gameObject.transform:Find("EffectFrame_Dungeon/Trans_ItemIcon/Image_ItemIcon_" .. i)
                _DungeonUnit.m_ItemIcon[i] = IconMgr.NewItemIcon(0, _DungeonUnit.m_Image_Icon[i], 100)
            end            
            
            _DungeonUnit.gameObject.transform:SetParent(this.m_Content_Dungeon.transform)
            _DungeonUnit.gameObject.transform.localScale = Vector3.one

            _DungeonUnit.EffectFrame_Rect  = _Unit.gameObject.transform:Find("EffectFrame_Dungeon")

            _DungeonUnit.m_State_Text_State = _DungeonUnit.m_Trans_State.transform:Find("Text_State"):GetComponent(typeof(TMPro.TextMeshProUGUI))
            _DungeonUnit.OnChangeState = function(iKind)
                _DungeonUnit.m_MainBtn:ChangeStateTransitionGroup(iKind)
                local _StateStr = ""
                _StateStr = iKind == 1 and TextData.Get(20500007) or TextData.Get(20500111)
                _DungeonUnit.m_State_Text_State.text = _StateStr
            end
            _DungeonUnit.OnStateActivity = function(iShow)
                _DungeonUnit.m_Image_State.gameObject:SetActive(iShow)
                _DungeonUnit.m_Trans_State.gameObject:SetActive(iShow)
            end
        end

        ---教學演出需求 需要把物件名稱換成招募ID
        _DungeonUnit.gameObject.name = _data.m_RecruitID
        _DungeonUnit.m_MainBtn:ClearListener()
        _DungeonUnit.m_MainBtn:AddListener(EventTriggerType.PointerClick, function()
            for k, v in pairs(m_Table_DungeonUnit) do
                v.m_SelectedBG.gameObject:SetActive(false)
            end
            m_Table_DungeonUnit[k].m_SelectedBG.gameObject:SetActive(true)
            Activity_Controller.SetContentInfo(_data.m_Idx)
        end)
        
        _DungeonUnit.m_SelectedBG.gameObject:SetActive(this.m_FirstDungeon == _data.m_Idx)

        --控制ScrollView滑到指定副本位置
        if this.m_ShowSpecifyDungeon then
            if this.m_FirstDungeon == _data.m_Idx then
                --等動畫跑完才調整位置
                HEMTimeMgr.DoFunctionDelay((table.Count(_SortData) * ShowSpecifyDungeon_DelayPerTime), function()
                    local _ContentPos = this.m_Content_Dungeon.gameObject.transform.localPosition
                    _ContentPos = Vector3(_ContentPos.x, (this.m_Button_Dungeon_Height - DungeonObj_Fix_Height) * (k - 1), _ContentPos.z)
                    this.m_Content_Dungeon.gameObject.transform.localPosition = _ContentPos
                end)
            end
        end
            
        local _ImageString = m_ImageString .. GFunction.Zero_stuffing(_data.m_DungeonImage, 4)
        TextureMgr.Load(_ImageString, true, function(iTex)
            if iTex ~= nil then
                _DungeonUnit.m_MainImg.texture = iTex
                _DungeonUnit.m_Image_BG.gameObject:SetActive(true)
                ---圖都 Load 完才跑動態效果
                Activity_Controller.DungeonEffect(table.Count(_SortData))
            else
                _DungeonUnit.m_Image_BG.gameObject:SetActive(false)
            end
        end)

        local _formatStr = TextData.Get(20500008)
        _DungeonUnit.m_Text_Lv.text = GString.Format(_formatStr, _data.m_LimitLv_Low)
        _DungeonUnit.m_Text_Name.text = TextData.Get(_data.m_DungeonTitleString)

        --通關狀態變色處理
        local _PassFlag = PlayerData.IsHaveStaticFlag(_data.m_PassStaticFlag)
        if PlayerData.GetLv() >= _data.m_LimitLv_Low then
            _DungeonUnit.OnStateActivity(_PassFlag)
            --local _Kind = _PassFlag and _StateKind.Select or _StateKind.Normal
            local _Kind = _PassFlag and 1 or 0
            _DungeonUnit.OnChangeState(_Kind)
        else
            _DungeonUnit.OnStateActivity(true)
            _DungeonUnit.OnChangeState(2)
        end

        --星星狀態處理
        if _data.m_ActivityType == 2 or _data.m_ActivityType == 3 then
            _DungeonUnit.m_Trans_Star.gameObject:SetActive(true)
            local _StarFlag = PlayerData.GetFlags(EFlags.StaticNum).Get(_data.m_HighestStarStaticFlag)
            for i = 1, 3 do
                if bit.band(_StarFlag, bit.lshift(1, i - 1)) ~= 0 then
                    _DungeonUnit.m_Image_Star[i].color = _StarColor[_StarKind.Achieve]
                else
                    if _PassFlag then
                        _DungeonUnit.m_Image_Star[i].color = _StarColor[_StarKind.Pass]
                    else
                        _DungeonUnit.m_Image_Star[i].color = _StarColor[_StarKind.Normal]
                    end
                end
                if PlayerData.GetLv() < _data.m_LimitLv_Low then
                    _DungeonUnit.m_Image_Star[i].color = _StarColor[_StarKind.No]
                end
            end
        else
            _DungeonUnit.m_Trans_Star.gameObject:SetActive(false)
        end

        --獎勵預覽
        local _RewardData = Activity_Model.SetCanGetReward(_data.m_StarInfo[1].m_TreasureID)
        local _SortData = Activity_Model.SortByRarity(_RewardData)
        for i = 1, 3 do
            if _SortData[i] ~= nil then
                _DungeonUnit.m_ItemIcon[i]:RefreshIcon(_SortData[i].m_ItemID)
                _DungeonUnit.m_ItemIcon[i]:SetClickTwice(false)
            else
                _DungeonUnit.m_ItemIcon[i]:RefreshIcon(0)
            end
        end
        
        m_Table_DungeonUnit[k] = _DungeonUnit
    end
end
--endregion

--region 副本資訊
function Activity_Controller.SetContentInfo(iIdx)
    ---重置一下動畫
    LeanTween.cancelAll();
    this.m_CanvasGroup_Trans.alpha = 0
    local _DungeonData = Activity_Model.GetDungeonData(this.m_SelectType, iIdx)
    if _DungeonData == nil then return end

    this.m_SelectDungeonIdx = iIdx
    
    local _IsActivityUp = RecruitMgr.GetIsActivityUp(this.m_SelectType)
    this.m_Image_ActivityUp.gameObject:SetActive(_IsActivityUp)

    this.m_Btn_Content_Question.gameObject:SetActive(this.m_SelectType == 2 or this.m_SelectType == 3)

    local _ImageString = m_ImageString .. GFunction.Zero_stuffing(_DungeonData.m_DungeonImage, 4)
    TextureMgr.Load(_ImageString, true, function(iTex)
        if iTex ~= nil then
            this.m_Image_Content_Pic.texture = iTex
            this.m_Image_Content_Pic_Mask.gameObject:SetActive(true)
            ---圖 Load 完才跑動態效果
            Activity_Controller.TransContentEffect()
        else
            this.m_Image_Content_Pic_Mask.gameObject:SetActive(false)
        end
    end)
    
    local _formatStr = TextData.Get(20500008)
    this.m_Text_Content_Lv.text = GString.Format(_formatStr, _DungeonData.m_LimitLv_Low)
    this.m_Text_Content_Name.text = TextData.Get(_DungeonData.m_DungeonTitleString)
    this.m_Text_Content_StarCaption.text = TextData.Get(_DungeonData.m_StarRatingString)
    if _DungeonData.m_ActivityType == 2 or _DungeonData.m_ActivityType == 3 then
        this.m_Trans_Star.gameObject:SetActive(true)
        local _StarFlag = PlayerData.GetFlags(EFlags.StaticNum).Get(_DungeonData.m_HighestStarStaticFlag)
        for i = 1, 3 do
            if bit.band(_StarFlag, bit.lshift(1, i - 1)) ~= 0 then
                this.m_Image_Content_Star[i].color = _StarColor[_StarKind.Achieve]
            else
                this.m_Image_Content_Star[i].color = _StarColor[_StarKind.Normal]
            end
        end
    else
        this.m_Trans_Star.gameObject:SetActive(false)
    end
    
    RecruitMgr.m_SelectDungeonIdx = _DungeonData.m_ActivityType == 3 and iIdx or 0

    local _DataInfo = ""

    ---類別1 為節慶活動 節慶活動要加入活動期間
    if _DungeonData.m_ActivityType == EActivityType.Festival then
        local _formatStr = TextData.Get(20500002)

        _DataInfo = GString.Format(_formatStr,
            _DungeonData.m_Start.m_Month, _DungeonData.m_Start.m_Day, _DungeonData.m_End.m_Month, _DungeonData.m_End.m_Day)
    end

    this.m_Text_Content_Open.text = _DataInfo.. Activity_Model.CheckOpenTime(_DungeonData.m_OpenTime, _DungeonData.m_Start, _DungeonData.m_End)

    local _agree, _limitStr = Activity_Model.CheckLimit(_DungeonData.m_SpecialLimit, _DungeonData.m_SpecialLimit_Value)
    this.m_CanEnterDungeon = _agree
    this.m_Text_Content_Limit.text = _limitStr
    if not _agree then
        this.m_Text_Content_Limit.color = _DungeonData.m_SpecialLimit ~= 2 and Color.Red or Color.White
    else
        this.m_Text_Content_Limit.color = Color.White
    end

    local _PlotData = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK, _DungeonData.m_PlotDynamicFlag)
    this.m_Text_Content_Caption.text = _PlotData ~= nil and EventStrAll:GetText(_PlotData.OutLineStrID) or ""
    LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Text_Content_Caption.transform)

    local _Text_Open_Height = this.m_Rect_Content_Open.rect.height
    local _Text_Limit_Height = this.m_Rect_Content_Limit.rect.height
    local _Text_Caption_Height = this.m_Rect_Content_Caption.rect.height
    local _CaptionHeight = Content_Interval_Size * 4 + _Text_Open_Height + _Text_Limit_Height + _Text_Caption_Height
    local _LimitHigh = this.m_Group_Content_Caption.rect.height * 3
    _CaptionHeight = _CaptionHeight < _LimitHigh and _CaptionHeight or this.m_Group_Content_Caption.rect.height
    local _Size = Vector2(this.m_Trans_Content_Scorll.sizeDelta.x, _CaptionHeight)
    this.m_Trans_Content_Scorll.sizeDelta = _Size

    this.m_Text_Content_Reward.text = _DungeonData.m_RewardTypeSetting == 1 and TextData.Get(20511004) or TextData.Get(20511015)
    this.m_Trans_Reward_Type0.gameObject:SetActive(_DungeonData.m_RewardTypeSetting == 0 or _DungeonData.m_RewardTypeSetting == 2)
    this.m_Trans_Reward_Type1.gameObject:SetActive(_DungeonData.m_RewardTypeSetting == 1)

    --顯示一星掉寶
    if _DungeonData.m_RewardTypeSetting == 0 then
        local _RewardData = Activity_Model.SetCanGetReward(_DungeonData.m_StarInfo[1].m_TreasureID)
        this.m_ContentRewardData = Activity_Model.SortByRarity(_RewardData)
        ScrollView.Update(this.m_ScrollView_Reward_Type0)

    --顯示經驗戰技武點
    elseif _DungeonData.m_RewardTypeSetting == 2 then
        for k, v in pairs(_DungeonData.m_FixedReward) do
            if v ~= 0 then
                local _RewardData = {}
                _RewardData.m_ItemID = BaseRewardID[k]
                _RewardData.m_Count = v
                table.insert(this.m_ContentRewardData, _RewardData)
            end
        end
        ScrollView.Update(this.m_ScrollView_Reward_Type0)
    end

    Activity_Controller.SetContentButton(_agree)
end

--region 按鈕狀態
local function SetButtonStatus(iIdx, iShow, iText)
    if iShow then
        this.m_Btn_Content[iIdx]:SetEnable()
    else
        this.m_Btn_Content[iIdx]:SetDisable(false)
    end
    if iText ~= nil then
        Button.SetText(this.m_Btn_Content[iIdx], TextData.Get(iText))
    end
end

function Activity_Controller.SetContentButton(iArgee)
    this.m_Btn_Content[2].gameObject:SetActive(this.m_SelectType ~= 2)
    local _Type = Activity_Controller.SetButtonType()
    --個人副本
    if this.m_SelectType == 2 then
        if _Type == 0 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(3, false, 20500103)
        elseif _Type == 1 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(3, true,  20500103)
        elseif _Type == 2 then
            SetButtonStatus(1, true,  20500101)
            SetButtonStatus(3, true,  20500103)
        elseif _Type == 3 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(3, true,  20500102)
        elseif _Type == 4 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(3, false, 20500102)
        elseif _Type == 5 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(3, false, 20500103)
        end
        if not iArgee then
            SetButtonStatus(1, false)
            SetButtonStatus(3, false)
        end

    --時空討伐(組隊)
    elseif this.m_SelectType == 3 then
        if _Type == 0 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(2, false, 20500104)
            SetButtonStatus(3, false, 20500105)
        elseif _Type == 1 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(2, true,  20500104)
            SetButtonStatus(3, true,  20500105)
        elseif _Type == 2 then
            SetButtonStatus(1, true,  20500101)
            SetButtonStatus(2, true,  20500104)
            SetButtonStatus(3, true,  20500105)
        elseif _Type == 3 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(2, false, 20500104)
            SetButtonStatus(3, true,  20500102)
        elseif _Type == 4 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(2, false, 20500104)
            SetButtonStatus(3, false, 20500102)
        elseif _Type == 5 then
            SetButtonStatus(1, false, 20500101)
            SetButtonStatus(2, false, 20500104)
            SetButtonStatus(3, false, 20500105)
        end
        if not iArgee then
            SetButtonStatus(1, false)
            SetButtonStatus(2, false)
            SetButtonStatus(3, false)
        end

    --其他
    else
        SetButtonStatus(1, true, 20500106)
        SetButtonStatus(2, true, 20500107)
        SetButtonStatus(3, _Type == 1, 20500124)
    end
    this.m_Text_PassLimit.gameObject:SetActive(_Type == 4)
end

---設定副本按鈕是否開啟和文字
function Activity_Controller.SetButtonType()
    local _CampaignData = Activity_Model.GetDungeonData(this.m_SelectType, this.m_SelectDungeonIdx)
    if _CampaignData == nil then return end
    
    local _Type = 0
    --通關永標
    local _IsPass = PlayerData.IsHaveStaticFlag(_CampaignData.m_PassStaticFlag)
    --通關動標
    local _PassDynFlag = PlayerData.GetFlags(EFlags.Move).Get(_CampaignData.m_PassDynamicFlag).m_Status
    --星級條件動標
    local _StarDynFlag = PlayerData.GetFlags(EFlags.Move).Get(_CampaignData.m_CurrentStarDynamicFlag).m_Status
    --通關動標(上限數值)
    local _limitCount = _CampaignData.m_PassDynamicFlag_Limit

    local _CampaignPassData = CampaignPassData.GetCampaignPassDataByIdx(this.m_SelectType)
    --今日入場次數日刪計數永標
    local _EnterCountStaticFlag = PlayerData.GetFlags(EFlags.StaticNum).Get(_CampaignPassData.m_EnterCountStaticFlag)
    --基礎今日入場上限次數
    local _EnterCountLimit = RecruitMgr.GetRemainingCount(this.m_SelectType)

    --限制等級
    local _LimitLv = _CampaignData.m_LimitLv_Low
    if PlayerData.GetLv() < _LimitLv then
        _Type = 0
        return _Type
    end

    --個人副本 or 時空討伐(組隊)
    if this.m_SelectType == 2 or this.m_SelectType == 3 then
        if _EnterCountStaticFlag >= _EnterCountLimit then
            _Type = 5
            return _Type
        end
        if not _IsPass then
            _Type = _StarDynFlag == 0 and 1 or 2
            return _Type
        else
            _Type = _PassDynFlag < _limitCount and 3 or 4
            return _Type
        end

    --其他
    else
        if (not _IsPass and _PassDynFlag == 0 and _limitCount == 1) or _limitCount == 0 then
            _Type = 1
            return _Type
        end
        if (_IsPass and _limitCount == 1) or (_PassDynFlag >= _limitCount) then
            _Type = 2
            return _Type
        end
    end
end
--endregion

--region 按鈕效果
function Activity_Controller.OnClick_ContentBtn(iIdx)
    local _CampaignData = Activity_Model.GetDungeonData(this.m_SelectType, this.m_SelectDungeonIdx)
    if _CampaignData == nil then return end

    --個人副本
    if this.m_SelectType == 2 then
        --掃蕩
        if iIdx == 1 then
            local function _SendProtocol_011_003()
                local _data = {}
                local _SellData = SellMgr.m_ConsumeData
                _data.m_Idx = _CampaignData.m_Idx
                _data.m_ItemID = _SellData.m_ConsumeID
                _data.m_SID = _SellData.m_ConsumeID ~= 0 and _SellData.m_ConsumeData[1].m_SID or 0
                SendProtocol_011._003(15, _data)
            end
            local function GetSellStr(iID)
                local _SellData = SellData.GetSellDataByIdx(iID)
                local _SellStr = {}
                local _ItemData = ItemData.GetItemDataByIdx(_SellData.m_ItemOne)
                _SellStr[1] = TextData.Get(_ItemData.m_Idx_ItemNameText)
                return _SellStr
            end
            local _SellStr = GetSellStr(_CampaignData.m_SweepItemSellID)
            SellMgr.m_ConsumeData = SellMgr.GetPrize(_CampaignData.m_SweepItemSellID)
            CommonQueryMgr.AddNewInform(520, {},_SellStr, _SendProtocol_011_003)

        --進入或重置關卡
        elseif iIdx == 3 then
            local function _SendProtocol_011_002()
                SendProtocol_011._002(1, _CampaignData)
            end
            if not PlayerData.IsHaveStaticFlag(_CampaignData.m_PassStaticFlag) then
                if _CampaignData.m_SpecialLimit == 3 or _CampaignData.m_SpecialLimit == 4 or _CampaignData.m_SpecialLimit == 5 then
                    --入場費共用詢問視窗編號從522開始
                    local _CommonQueryIdx = 519 + _CampaignData.m_SpecialLimit
                    CommonQueryMgr.AddNewInform(_CommonQueryIdx, {},{_CampaignData.m_SpecialLimit_Value}, _SendProtocol_011_002)
                else
                    _SendProtocol_011_002()
                end
            else
                local function GetSellStr(iID)
                    local _SellData = SellData.GetSellDataByIdx(iID)
                    local _SellStr = {}
                    local _ItemData = ItemData.GetItemDataByIdx(_SellData.m_ItemOne)
                    _SellStr[1] = TextData.Get(_ItemData.m_Idx_ItemNameText)
                    local _PassDynFlag = PlayerData.GetFlags(EFlags.Move).Get(_CampaignData.m_PassDynamicFlag).m_Status
                    _SellStr[2] = tostring(_PassDynFlag)
                    _SellStr[3] = _CampaignData.m_PassDynamicFlag_Limit
                    return _SellStr
                end
                local _SellStr = GetSellStr(_CampaignData.m_ResetItemSellID)
                SellMgr.OpenUISell(521, _SellStr, SellMgr.SendProtocol_012_001, {EUISellProtocolType.UseSell})
            end
        end

    --時空討伐(組隊)
    elseif this.m_SelectType == 3 then
        --掃蕩
        if iIdx == 1 then
            local function _SendProtocol_011_003()
                local _data = {}
                local _SellData = SellMgr.GetPrize(_CampaignData.m_SweepItemSellID)
                _data.m_Idx = _CampaignData.m_Idx
                _data.m_ItemID = _SellData.m_ConsumeID
                _data.m_SID = _SellData.m_ConsumeID ~= 0 and _SellData.m_ConsumeData[1].m_SID or 0
                SendProtocol_011._003(15, _data)
            end
            local function GetSellStr(iID)
                local _SellData = SellData.GetSellDataByIdx(iID)
                local _SellStr = {}
                local _ItemData = ItemData.GetItemDataByIdx(_SellData.m_ItemOne)
                _SellStr[1] = TextData.Get(_ItemData.m_Idx_ItemNameText)
                local _FlagCount = PlayerData.GetFlags(EFlags.StaticNum).Get(_SellData.m_ContNumFlag)
                local _remainingCount = _SellData.m_BuyMaxCount - _FlagCount
                _remainingCount = _remainingCount > 0 and _remainingCount or 0
                _SellStr[2] = tostring(_remainingCount)
                _SellStr[3] = tostring(_SellData.m_BuyMaxCount)
                return _SellStr
            end
            local _SellStr = GetSellStr(_CampaignData.m_SweepItemSellID)
            CommonQueryMgr.AddNewInform(520, {},_SellStr, _SendProtocol_011_003)

        --招募
        elseif iIdx == 2 then
            if RecruitMgr.m_MyTeam == nil then
                UIMgr.Open(Recruit_Controller, ERecruitType.Recruit)
            else
                UIMgr.Open(Recruit_Controller)
            end

        --組隊或重置
        elseif iIdx == 3 then
            if not PlayerData.IsHaveStaticFlag(_CampaignData.m_PassStaticFlag) then
                UIMgr.Open(Recruit_Controller, ERecruitType.Quick)
            else
                local function GetSellStr(iID)
                    local _SellData = SellData.GetSellDataByIdx(iID)
                    local _SellStr = {}
                    local _ItemData = ItemData.GetItemDataByIdx(_SellData.m_ItemOne)
                    _SellStr[1] = TextData.Get(_ItemData.m_Idx_ItemNameText)
                    local _PassDynFlag = PlayerData.GetFlags(EFlags.Move).Get(_CampaignData.m_PassDynamicFlag).m_Status
                    _SellStr[2] = tostring(_PassDynFlag)
                    _SellStr[3] = _CampaignData.m_PassDynamicFlag_Limit
                    return _SellStr
                end
                local _SellStr = GetSellStr(_CampaignData.m_ResetItemSellID)
                SellMgr.OpenUISell(521, _SellStr, SellMgr.SendProtocol_012_001, {EUISellProtocolType.UseSell})
            end
        end

    --其他
    else
        if iIdx == 1 then
        elseif iIdx == 2 then
        elseif iIdx == 3 then
        end
        MessageMgr.AddCenterMsg(false, "待規劃")
    end
end
--endregion

function Activity_Controller.OnClick_Reward(iIdx)
    Activity_Controller.SetWindowInfo(EWindowClickType.StarReward, iIdx)
end

--region window
---@param iType number 類型 1.星級條件 2.星級獎勵 3.加乘活動
---@param iIdx number 星級 1 2 3星
function Activity_Controller.SetWindowInfo(iType, iIdx)
    Activity_Controller.SetWindowActive(iType == EWindowClickType.StarCondition)
    local _DungeonData = Activity_Model.GetDungeonData(this.m_SelectType, this.m_SelectDungeonIdx)
    if _DungeonData == nil then return end

    local _StarFlag = PlayerData.GetFlags(EFlags.StaticNum).Get(_DungeonData.m_HighestStarStaticFlag)
    if iType == EWindowClickType.StarCondition then
        this.m_Group_StarCaption.gameObject:SetActive(true)
        this.m_Text_Window_Title.text = TextData.Get(20511001)
        this.m_Text_Window_Caption.text = TextData.Get(20511002)
        for i = 1, 3 do
            if bit.band(_StarFlag, bit.lshift(1, i - 1)) ~= 0 then
                this.m_Data_StarCaption[i].Image_Star.color = _StarColor[_StarKind.Achieve]
            else
                this.m_Data_StarCaption[i].Image_Star.color = _StarColor[_StarKind.Normal]
            end
            this.m_Data_StarCaption[i].Text_Caption.text = TextData.Get(_DungeonData.m_StarInfo[i].m_String)
        end

    elseif iType == EWindowClickType.StarReward then
        table.Clear(this.m_TreasureData)
        --如果還沒三星而且開三星獎勵箱
        if _StarFlag ~= 7 and iIdx == 3 then
            local _data = {}
            _data.m_ItemID = _DungeonData.m_FirstThreeStar
            _data.m_Count = TextData.Get(20511011)
            table.insert(this.m_TreasureData, _data)
        end
        local _TreasureID = _DungeonData.m_StarInfo[iIdx].m_TreasureID
        local _RewardData = Activity_Model.SetCanGetReward(_TreasureID)
        local _SortRewardData = Activity_Model.SortByRarity(_RewardData)
        for k, v in pairs(_SortRewardData) do
            table.insert(this.m_TreasureData, v)
        end
        for k, v in pairs(_DungeonData.m_FixedReward) do
            if v ~= 0 then
                local _data = {}
                _data.m_ItemID = BaseRewardID[k]
                _data.m_Count = v
                table.insert(this.m_TreasureData, _data)
            end
        end
        local _CommonQueryIdx = 204 + iIdx
        local _Type5Data = CustomCommonQueryData_Type5.NewData(this.m_TreasureData)
        CommonQueryMgr.AddNewInform(_CommonQueryIdx, {},{},{},{},{},{},{},{}, _Type5Data)

    elseif iType == EWindowClickType.ActivityUp then
        local _CampaignPassData = CampaignPassData.GetCampaignPassDataByIdx(this.m_SelectType)
        local _Content = {}
        _Content[1] = TextData.Get(_DungeonData.m_TitleString)
        _Content[2] = _CampaignPassData.m_LimitAdd
        _Content[3] = Activity_Model.ChangeTimeString(_CampaignPassData.m_Start.m_Hour)
        _Content[4] = Activity_Model.ChangeTimeString(_CampaignPassData.m_Start.m_Min)
        _Content[5] = Activity_Model.ChangeTimeString(_CampaignPassData.m_End.m_Hour)
        _Content[6] = Activity_Model.ChangeTimeString(_CampaignPassData.m_End.m_Min)
        CommonQueryMgr.AddNewInform(204, {}, _Content)
    end
end

--時程表
function Activity_Controller.OnClick_Schedule()
    local _Schedule = CampaignData.GetCampaignScheduleData()
    UIMgr.Open(Schedule_Controller, _Schedule)
end

--掃蕩副本
function Activity_Controller.SetSweepGetItem(iData)
    local _Data = Activity_Model.SortByRarity(iData.m_ItemData)
    this.m_GetItemData = _Data
    local _data = {}
    local _BaseReward = {iData.m_ExpPoint, iData.m_SkillPoint, iData.m_Money}
    for i = 1, 3 do
        if _BaseReward[i] ~= 0 then
            _data[i] = {}
            _data[i].m_ItemID = BaseRewardID[i]
            _data[i].m_Count = _BaseReward[i]
            table.insert(this.m_GetItemData, _data[i])
        end
    end

    local _RewardData = {}
    _RewardData.m_RewardTitle = 20500122
    _RewardData.m_RewardSubDatas = {}

    _RewardData.m_RewardSubDatas[1] = {}
    for i = 1, table.Count(this.m_GetItemData) do
        table.insert(_RewardData.m_RewardSubDatas[1], this.m_GetItemData[i])
    end
    
    CommonRewardMgr.AddNewReward(CommonRewardType.MultiItem, _RewardData )

    Activity_Controller.RefreshInfo()
end
--endregion

--region 刷新
function Activity_Controller.RefreshInfo()
    --上面資源
    this.m_Group_Resources.m_Resources:OnUpdate()
    --共用次數
    Activity_Controller.SetRemainingCount()
    --副本資訊
    Activity_Controller.SetContentInfo(this.m_SelectDungeonIdx)
end

function Activity_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.UISellRefresh then
        Activity_Controller.RefreshInfo()
    end
end
--endregion

--region Content獎勵
function Activity_Controller.GetCount_ContentReward()
    if this.m_ContentRewardData == nil then return 0 end
    local _ItemCount = table.Count(this.m_ContentRewardData)
    return _ItemCount
end

function Activity_Controller.AfterReuseItemInit_ContentReward(iItem, iRowIdx)
    if iItem ~= nil then
        local _Parent = iItem.m_GObj.transform
        iItem.m_Icons = IconMgr.NewItemIcon(0, _Parent, 110)
        iItem.m_Icons:SetClickTwice(false)
    end
end

function Activity_Controller.AfterReuseItemIndexUpdate_ContentReward(iItem, iRowIdx)
    if iItem ~= nil then
        local _ChildIconHasData = 0

        local _Data = this.m_ContentRewardData ~= nil and this.m_ContentRewardData[iRowIdx] or nil
        if _Data ~= nil then
            iItem.m_Icons:RefreshIcon(_Data.m_ItemID)
            if type(_Data.m_Count) == "string" and string.match(_Data.m_Count, "%d") then
                iItem.m_Icons:SetCount(_Data.m_Count, "WO_01")
            else
                iItem.m_Icons:SetCount(_Data.m_Count)
            end
            iItem.m_Icons.gameObject:SetActive(true)
            _ChildIconHasData = _ChildIconHasData + 1
        else
            iItem.m_Icons.gameObject:SetActive(false)
        end
        iItem.m_GObj:SetActive(_ChildIconHasData > 0)
    end
end
--endregion

---活動列表動態效果
function Activity_Controller.MainContentEffect()
    m_LoadCount = m_LoadCount + 1
    ---如果圖都 Load 完才跑動態效果
    if m_LoadCount == #Activity_Model.m_CampaignSortData then
        m_MainVisibleBatch = UIVisibleEffect.AddUIVisibleBatch(m_Table_ShowMain, EFFECT_PER_TIME)
        m_MainVisibleBatch:Start(0)
        m_LoadCount = 0
        local _Frequency = MAIN_EFFECT_MOVE_TIME + EFFECT_PER_TIME * #Activity_Model.m_CampaignSortData
        HEMTimeMgr.DoFunctionDelay(_Frequency, function()
            this.m_ScrollView_Main.enabled = true
        end)
    end
end

---副本列表動態效果
---@param iCount number 副本數量
function Activity_Controller.DungeonEffect(iCount)
    m_LoadCount = m_LoadCount + 1
    if m_LoadCount == iCount then
        local _ShowDungeon = {}
        for i = 1, iCount do
            _ShowDungeon[i] = m_Table_DungeonUnit[i]
        end
        m_LoadCount = 0
        m_DungeonVisibleBatch = UIVisibleEffect.AddUIVisibleBatch(_ShowDungeon, EFFECT_PER_TIME)
        m_DungeonVisibleBatch:Start(0)
    end
end

--region 動態效果

---重置副本列表動態效果
function Activity_Controller.ResetDungeonEffect()
    ---停止動態效果
    m_DungeonVisibleBatch:Stop()
    ---重設位置（把第一個先拉到畫面外面就好）
    local _Pos = m_Table_DungeonUnit[1].EffectFrame_Rect.anchoredPosition
    _Pos.y = DUNGEON_EFFECT_INIT_POSY
    m_Table_DungeonUnit[1].EffectFrame_Rect.anchoredPosition = _Pos
end

---副本資訊動態效果
function Activity_Controller.TransContentEffect()
    ---淡入效果
    LeanTween.value(0, 1, TRANS_EFFECT_TIME)
        :setEase(LeanTweenType.linear)
        :setOnUpdate(System.Action_float(
            function(value)
                this.m_CanvasGroup_Trans.alpha = value
            end))
    ---從右邊飄進來
    local _Pos = this.m_Trans_Content_Rect.anchoredPosition
    _Pos.x = TRANS_EFFECT_INIT_POSX
    this.m_Trans_Content_Rect.anchoredPosition = _Pos
    LeanTween.value(_Pos.x, TRANS_EFFECT_TARGET_POSX, TRANS_EFFECT_TIME)
        :setEase(LeanTweenType.easeOutQuad)
        :setOnUpdate(System.Action_float(
            function(value)
                _Pos.x = value
                this.m_Trans_Content_Rect.anchoredPosition = _Pos
            end))
end

--endregion
