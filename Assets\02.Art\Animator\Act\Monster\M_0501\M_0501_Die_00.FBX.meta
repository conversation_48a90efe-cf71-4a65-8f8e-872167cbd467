fileFormatVersion: 2
guid: 2c268e790fbd9e0438570b4e71ae9ccd
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: EagleBone001
  - first:
      1: 100002
    second: EagleBone001 1
  - first:
      1: 100004
    second: EagleBone002
  - first:
      1: 100006
    second: EagleBone002 1
  - first:
      1: 100008
    second: EagleHead
  - first:
      1: 100010
    second: EagleHeadBone001
  - first:
      1: 100012
    second: EagleLArm1
  - first:
      1: 100014
    second: EagleLArm2
  - first:
      1: 100016
    second: EagleLArmDigit01
  - first:
      1: 100018
    second: EagleLArmDigit11
  - first:
      1: 100020
    second: EagleLArmPalm
  - first:
      1: 100022
    second: EagleLLeg1
  - first:
      1: 100024
    second: EagleLLeg2
  - first:
      1: 100026
    second: <PERSON><PERSON>egAnkle
  - first:
      1: 100028
    second: EagleLLegDigit11
  - first:
      1: 100030
    second: EagleLLegDigit12
  - first:
      1: 100032
    second: EagleLLegDigit21
  - first:
      1: 100034
    second: EagleLLegDigit22
  - first:
      1: 100036
    second: EagleLLegDigit31
  - first:
      1: 100038
    second: EagleLLegDigit32
  - first:
      1: 100040
    second: EagleLLegDigit41
  - first:
      1: 100042
    second: EagleLLegDigit42
  - first:
      1: 100044
    second: EagleLLegPlatform
  - first:
      1: 100046
    second: EaglePelvis
  - first:
      1: 100048
    second: EagleRArm1
  - first:
      1: 100050
    second: EagleRArm2
  - first:
      1: 100052
    second: EagleRArmDigit01
  - first:
      1: 100054
    second: EagleRArmDigit11
  - first:
      1: 100056
    second: EagleRArmPalm
  - first:
      1: 100058
    second: EagleRibcage
  - first:
      1: 100060
    second: EagleRLeg1
  - first:
      1: 100062
    second: EagleRLeg2
  - first:
      1: 100064
    second: EagleRLegAnkle
  - first:
      1: 100066
    second: EagleRLegDigit11
  - first:
      1: 100068
    second: EagleRLegDigit12
  - first:
      1: 100070
    second: EagleRLegDigit21
  - first:
      1: 100072
    second: EagleRLegDigit22
  - first:
      1: 100074
    second: EagleRLegDigit31
  - first:
      1: 100076
    second: EagleRLegDigit32
  - first:
      1: 100078
    second: EagleRLegDigit41
  - first:
      1: 100080
    second: EagleRLegDigit42
  - first:
      1: 100082
    second: EagleRLegPlatform
  - first:
      1: 100084
    second: EagleSpine1
  - first:
      1: 100086
    second: EagleSpine1 1
  - first:
      1: 100088
    second: EagleSpine2
  - first:
      1: 100090
    second: EagleSpine2 1
  - first:
      1: 100092
    second: EagleTail1
  - first:
      1: 100094
    second: EagleTail2
  - first:
      1: 100096
    second: //RootNode
  - first:
      1: 100098
    second: EagleLBone001
  - first:
      1: 100100
    second: EagleLBone002
  - first:
      1: 100102
    second: EagleRBone001
  - first:
      1: 100104
    second: EagleRBone002
  - first:
      4: 400000
    second: EagleBone001
  - first:
      4: 400002
    second: EagleBone001 1
  - first:
      4: 400004
    second: EagleBone002
  - first:
      4: 400006
    second: EagleBone002 1
  - first:
      4: 400008
    second: EagleHead
  - first:
      4: 400010
    second: EagleHeadBone001
  - first:
      4: 400012
    second: EagleLArm1
  - first:
      4: 400014
    second: EagleLArm2
  - first:
      4: 400016
    second: EagleLArmDigit01
  - first:
      4: 400018
    second: EagleLArmDigit11
  - first:
      4: 400020
    second: EagleLArmPalm
  - first:
      4: 400022
    second: EagleLLeg1
  - first:
      4: 400024
    second: EagleLLeg2
  - first:
      4: 400026
    second: EagleLLegAnkle
  - first:
      4: 400028
    second: EagleLLegDigit11
  - first:
      4: 400030
    second: EagleLLegDigit12
  - first:
      4: 400032
    second: EagleLLegDigit21
  - first:
      4: 400034
    second: EagleLLegDigit22
  - first:
      4: 400036
    second: EagleLLegDigit31
  - first:
      4: 400038
    second: EagleLLegDigit32
  - first:
      4: 400040
    second: EagleLLegDigit41
  - first:
      4: 400042
    second: EagleLLegDigit42
  - first:
      4: 400044
    second: EagleLLegPlatform
  - first:
      4: 400046
    second: EaglePelvis
  - first:
      4: 400048
    second: EagleRArm1
  - first:
      4: 400050
    second: EagleRArm2
  - first:
      4: 400052
    second: EagleRArmDigit01
  - first:
      4: 400054
    second: EagleRArmDigit11
  - first:
      4: 400056
    second: EagleRArmPalm
  - first:
      4: 400058
    second: EagleRibcage
  - first:
      4: 400060
    second: EagleRLeg1
  - first:
      4: 400062
    second: EagleRLeg2
  - first:
      4: 400064
    second: EagleRLegAnkle
  - first:
      4: 400066
    second: EagleRLegDigit11
  - first:
      4: 400068
    second: EagleRLegDigit12
  - first:
      4: 400070
    second: EagleRLegDigit21
  - first:
      4: 400072
    second: EagleRLegDigit22
  - first:
      4: 400074
    second: EagleRLegDigit31
  - first:
      4: 400076
    second: EagleRLegDigit32
  - first:
      4: 400078
    second: EagleRLegDigit41
  - first:
      4: 400080
    second: EagleRLegDigit42
  - first:
      4: 400082
    second: EagleRLegPlatform
  - first:
      4: 400084
    second: EagleSpine1
  - first:
      4: 400086
    second: EagleSpine1 1
  - first:
      4: 400088
    second: EagleSpine2
  - first:
      4: 400090
    second: EagleSpine2 1
  - first:
      4: 400092
    second: EagleTail1
  - first:
      4: 400094
    second: EagleTail2
  - first:
      4: 400096
    second: //RootNode
  - first:
      4: 400098
    second: EagleLBone001
  - first:
      4: 400100
    second: EagleLBone002
  - first:
      4: 400102
    second: EagleRBone001
  - first:
      4: 400104
    second: EagleRBone002
  - first:
      74: 7400000
    second: M_0501_Die_00
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0501_Die_00
      takeName: M_0501_Die_00
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 411fe58c08863d74598fdebd7e92668f,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
