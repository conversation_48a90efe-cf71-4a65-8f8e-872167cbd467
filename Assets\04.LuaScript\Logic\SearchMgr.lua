---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

require("Logic/SystemSettingData/SearchSetting")


---@class SearchMgr 搜尋管理器,主要處理
----搜尋敵人
---尋找NPC ( 對話 )
---尋找其他玩家 ( 尚未實裝,目前沒玩家資料可以撈 2022.07.28 )
---<AUTHOR>
---@version 1.0
---@since [黃易群俠傳M 2.0]
---@date 2022.7.28
SearchMgr = {}
local this = SearchMgr

---是否初始化
this.m_IsInitialized = false

---@type number|RoleController
---搜尋範圍內的 NPC 資料 <自定義流水號, RoleController>
this.m_Table_AttackableNPC = {}
---現在 m_Table_AttackableNPC 的索引
local m_Idx_CurSelect = 1

---@type number|RoleController
---搜尋範圍內的 友善NPC 資料 <自定義流水號, RoleController>
this.m_Table_FriendlyNPC = {}

this.m_Table_AttackablePlayer = {}
this.m_Table_FriendlyPlayer = {}

---@type SId|table
---用SId當key攻擊我的物件(RC、剩餘時間、權重、距離)
local m_Table_AttackMeObj = {}

---@type number|table
---照權重排序後攻擊我的物件
this.m_Table_AttackMeList = {}

---@type function
---抵達場景後執行的程式
this.m_SearchNPCFunc = nil

---是否自動搜尋最近的敵人
this.m_AutoSearch = true

---@class ESearchKind 搜索目標方式
ESearchKind = 
{
    EnemyNPC = "EnemyNPC",
    FriendlyNPC = "FriendlyNPC",
    EnemyPlayer = "EnemyPlayer",
    FriendlyPlayer = "FriendlyPlayer",
    AttackMeRole = "AttackMeRole"
}

ETeleportKind =
{
    Mission = 1,
    Map = 2,
}

---初始化
function SearchMgr.Init()
    D.Log( "SearchMgr Init" )

    HEMTimeMgr.AddListener( SearchMgr[ESearchKind.FriendlyNPC].Refresh, 0.75, true )
    HEMTimeMgr.AddListener( SearchMgr.RefreshAttackMeListOnSecond, 1, true )
    HEMTimeMgr.AddListener( SearchMgr.AutoSearchTarget, 1, true )

    this.m_CullingSetting = CameraMgr.GetCullingGroupSetting().RangeSetting
    this.m_IsInitialized = true
end

---Update
function SearchMgr.Update()

    ---先使用 IsLogin擋
    if Login_Model.m_IsLogin == false then
        return
    end

    if this.m_IsInitialized == false then
        this.Init()
    end

    --無選取對象時自動選取最近敵人
    if this.m_AutoSearch and Extension.IsUnityObjectNull(SelectMgr.m_TargetGObj) then
        local _RC = this.GetHostility_Nearest()
        if _RC ~= nil then
            SelectMgr.ChangeTarget(_RC.gameObject)
            ---暫代 太遠取消自動普攻
            local _Dis = Vector3.Distance(Vector3.GetTransformPosition(nil, _RC.gameObject.transform) , RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.GetPlayerRC().transform) )
            if _Dis > BattleSetting.m_CancelAutoBattleDistance then
                BattleMgr.ShowLog("太遠了不想打: " .. _Dis, "#F864E8")
                BattleMgr.SetAutoNormalBattle(false)
            end
        else
            -- 找不到目標就取消自動普攻
            BattleMgr.SetAutoNormalBattle(false)
            Main_SubCtrl_TargetInfo.SetTargetInfoVisiable(false)
        end
        this.m_AutoSearchTargrt = _RC
    end


end

---依照條件刷新 Table
---@param iFilterAPI function 要篩選的API
local function Refresh( iFilterAPI, iRange, iTargetType )
    local _Reslut = {}
    if RoleMgr.m_RC_Player and iFilterAPI then
        --D.Log( "重新搜尋 NPC" )
        ---先計算距離
        local _TmpTable = {}

        local _searchTable
        if iTargetType == SelectMgr.ETargetType.NPC then
            _searchTable = NPCMgr.m_Table_ActiveNPC
        elseif iTargetType == SelectMgr.ETargetType.Player then
            _searchTable = RoleMgr.m_Table_ActiveRoles
        else
            return
        end
        
        --判斷用哪個CullingRange來先做一次剔除
        local _CullingRange = ECullingState.OUT_OF_RANGE
        for k, v in pairs(this.m_CullingSetting) do
            if v >= iRange then
                _CullingRange = k-1
                math.min(_CullingRange, 0)
                break
            end 
        end
        local _Pos = {}

        for k, v in pairs( _searchTable ) do
            if v:IsInRange(_CullingRange, false) then
                Vector3.GetTransformPosition(_Pos, v.transform)
                local _IsInvisible, _Dis = RoleMgr.GetIsInVisibleRange( _Pos, iRange )
                if _IsInvisible and
                   v.m_StateController and v.m_StateController:GetStateType() ~= EStateType.Dead and
                   not v.m_BeSealed then

                    --處理那些可以當成目標 ex: NPC, 玩家...等等
                    if iFilterAPI( v.m_Relation[ERelatType.Enemy], v ) then
                        ---用距離當 Key
                        if _TmpTable[ _Dis ] then
                            _Dis = _Dis + 0.001
                        end
                        v.m_SearchDist = _Dis
                        _TmpTable[ _Dis ] = v
                    end
                end
            end
        end

        --另外再查機關
        if iTargetType == SelectMgr.ETargetType.NPC then
            _searchTable = GearMgr.m_Table_GearController
            for k,v in pairs(_searchTable) do
                if not Extension.IsUnityObjectNull(v.transform) then
                    Vector3.GetTransformPosition(_Pos, v.transform)
                    local _IsInvisible, _Dis = RoleMgr.GetIsInVisibleRange(_Pos, iRange )
                    if _IsInvisible  then
                        if iFilterAPI( false, v ) then
                            if _TmpTable[ _Dis ] then
                                _Dis = _Dis + 0.001
                            end
                            v.m_SearchDist = _Dis
                            _TmpTable[ _Dis ] = v
                        end
                    end    
                end
            end    
        end

        --升順
        for k,v in pairs(_TmpTable) do
            _Reslut[k] = v
            if table.Count(_Reslut) >= SearchSetting.ENEMY_LIST_LIMIT then
                break
            end
        end

    end
    
    return _Reslut
end

---檢查是不是可攻擊
local function IsAttackable( isEnemy )
    return isEnemy
end

---檢查是不是 友善
local function IsFriendly( isEnemy )
    return not isEnemy
end

---友善且可互動
local function IsFriendlyAndInteractable(isEnemy, iController)
    if not isEnemy then
        if iController.m_NPCEventController and iController.m_NPCEventController.m_EventID ~= 0 and not iController.m_BeSealed then
            ---取得 EventData
            local _EventData = EventData:GetEventDataByIdx( iController.m_NPCEventController.m_EventID )
            if _EventData then
                local _EventTriData = EventTriData:GetEventTriDataByIdx( _EventData.m_TriBegin )
                if this.IsCollectableNPC(iController) then
                    if _EventTriData and _EventTriData.m_TriID ~= 0 then
                        return true
                    end    
                end
            end
        elseif GearMgr.IsGearInteractable(iController) and not iController.m_Disappear then
            return true
        end
    end

    return false
end

---@param iController RoleController|HUDData 可能會是RC或是HUDData
function SearchMgr.IsCollectableNPC(iController)

    if iController.m_NPCEventController or (iController.m_NPCMode and iController.m_EventID) then
        local _EventID = iController.m_NPCEventController and iController.m_NPCEventController.m_EventID or iController.m_EventID
        if iController.m_NPCMode and iController.m_NPCMode == EventNPCMode.Collection then
            local _finish = EventMgr:GetMissionProgressByEventID(_EventID)
            return _finish > 1
        end
    end

    return true
end

---設定清單刷新時同時刷新的函式
---@param iSearchKind ESearchKind 搜索目標方式
---@param iCallback function 註冊的函數
---@return number 註冊函數時的ID
function SearchMgr.SetListRefreshListener(iSearchKind, iCallback)
    if SearchMgr[iSearchKind].CallBack == nil then
        SearchMgr[iSearchKind].CallBack = {}
    end

    return table.insert(SearchMgr[iSearchKind].CallBack, iCallback)
end

---移除清單刷新時函式
---@param iSearchKind ESearchKind 搜索目標方式
---@param iIdx number 註冊函數時的ID
function SearchMgr.RemoveListRefreshListener(iSearchKind, iIdx)
    if SearchMgr[iSearchKind].CallBack and SearchMgr[iSearchKind].CallBack[iIdx] then
        table.remove(SearchMgr[iSearchKind].CallBack, iIdx)
    end
end

---刷新資料整合於此
---@param iSearchKind ESearchKind 搜索目標方式
function SearchMgr.RefreshTable(iSearchKind)
    SearchMgr[iSearchKind].Refresh()
end

---取得資料整合於此
---@param iSearchKind ESearchKind 搜索目標方式
function SearchMgr.GetSearchList(iSearchKind)
    return SearchMgr[iSearchKind].GetList()
end

--region 各別搜尋類型

SearchMgr[ESearchKind.EnemyNPC] = {}
SearchMgr[ESearchKind.EnemyNPC].Refresh = function()
    this.m_Table_AttackableNPC = Refresh( IsAttackable, SearchSetting.SEARCH_RANGE_ATTACKABLE_NPC, SelectMgr.ETargetType.NPC)
    if SearchMgr[ESearchKind.EnemyNPC].CallBack then
        for k,v in pairs(SearchMgr[ESearchKind.EnemyNPC].CallBack) do
            v(ESearchKind.EnemyNPC, this.m_Table_AttackableNPC)
        end
    end
end 
SearchMgr[ESearchKind.EnemyNPC].GetList = function()
    return this.m_Table_AttackableNPC
end

SearchMgr[ESearchKind.FriendlyNPC] = {}
SearchMgr[ESearchKind.FriendlyNPC].Refresh = function()
    this.m_Table_FriendlyNPC = Refresh( IsFriendlyAndInteractable, SearchSetting.SEARCH_RANGE_FRIENDLY_NPC, SelectMgr.ETargetType.NPC )
    local _NewTable = {}
    ---依照距離 sort, 得到近到遠的NPC排序
    for k, v in table.pairsByKeys(  this.m_Table_FriendlyNPC ) do
        table.insert(_NewTable, v)
    end
    this.m_Table_FriendlyNPC = _NewTable
    if SearchMgr[ESearchKind.FriendlyNPC].CallBack then
        for k,v in pairs(SearchMgr[ESearchKind.FriendlyNPC].CallBack) do
            v(ESearchKind.FriendlyNPC, this.m_Table_FriendlyNPC)
        end
    end
end 
SearchMgr[ESearchKind.FriendlyNPC].GetList = function()
    return this.m_Table_FriendlyNPC
end

SearchMgr[ESearchKind.EnemyPlayer] = {}
SearchMgr[ESearchKind.EnemyPlayer].Refresh = function()
    this.m_Table_AttackablePlayer = Refresh( IsAttackable, SearchSetting.SEARCH_RANGE_ATTACKABLE_PLAYER, SelectMgr.ETargetType.Player )
    if SearchMgr[ESearchKind.EnemyPlayer].CallBack then
        for k,v in pairs(SearchMgr[ESearchKind.EnemyPlayer].CallBack) do
            v(ESearchKind.EnemyPlayer, this.m_Table_AttackablePlayer)
        end
    end
end 
SearchMgr[ESearchKind.EnemyPlayer].GetList = function()
    return this.m_Table_AttackablePlayer
end

SearchMgr[ESearchKind.FriendlyPlayer] = {}
SearchMgr[ESearchKind.FriendlyPlayer].Refresh = function()
    this.m_Table_FriendlyPlayer = Refresh( IsFriendly, SearchSetting.SEARCH_RANGE_FRIENDLY_PLAYER, SelectMgr.ETargetType.Player )
    if SearchMgr[ESearchKind.FriendlyPlayer].CallBack then
        for k,v in pairs(SearchMgr[ESearchKind.FriendlyPlayer].CallBack) do
            v(ESearchKind.FriendlyPlayer, this.m_Table_FriendlyPlayer)
        end
    end
end 
SearchMgr[ESearchKind.FriendlyPlayer].GetList = function()
    return this.m_Table_FriendlyPlayer
end

SearchMgr[ESearchKind.AttackMeRole] = {}
SearchMgr[ESearchKind.AttackMeRole].Refresh = function()
    table.Clear( this.m_Table_AttackMeList)
    ---有人正在打我，先瞄正再打我的人
    if table.Count(m_Table_AttackMeObj) > 0 then
        for k,v in pairs(m_Table_AttackMeObj)do
            table.insert(this.m_Table_AttackMeList, v)
        end
        table.sort(this.m_Table_AttackMeList,function(k1, k2) 
            return (k1.m_Priority - k1.m_Distance) > (k2.m_Priority - k2.m_Distance) 
        end)
    end

    if SearchMgr[ESearchKind.AttackMeRole].CallBack then
        for k,v in pairs(SearchMgr[ESearchKind.AttackMeRole].CallBack) do
            v(ESearchKind.AttackMeRole, this.m_Table_AttackMeList)
        end
    end
end
SearchMgr[ESearchKind.AttackMeRole].GetList = function()
    for k,v in pairs(this.m_Table_AttackMeList) do
        if v.m_RC.m_StateController:IsDead() then
            this.m_Table_AttackMeList[k] = nil
        end
    end

    return this.m_Table_AttackMeList
end

--endregion

---塞入攻擊我的對象
function SearchMgr.InsertAttackMeList(iRoleController)
    local _IsInvisible, _Dis = RoleMgr.GetIsInVisibleRange( Vector3.GetTransformPosition(nil, iRoleController.transform) , SearchSetting.SEARCH_RANGE_ATTACKABLE_NPC )
    if not _IsInvisible then
        return
    end

    local _id
    local _priority
    if iRoleController.m_NPCID and NPCData:Get(iRoleController.m_NPCID) then
        _priority = NPCData:Get(iRoleController.m_NPCID).m_Priority
    elseif iRoleController.m_PlayerID then
        _priority = 500
    end
    _id = iRoleController.m_SID

    if m_Table_AttackMeObj[_id] == nil then
        m_Table_AttackMeObj[_id] = {}
        m_Table_AttackMeObj[_id].m_RC = iRoleController
    end
    m_Table_AttackMeObj[_id].m_Time = 5
    m_Table_AttackMeObj[_id].m_Priority = _priority
    m_Table_AttackMeObj[_id].m_Distance = _Dis       

    this.RefreshTable(ESearchKind.AttackMeRole)

    if not this.m_AutoSearch and SelectMgr.m_TargetController == nil then
        local _RC = this.GetHostility_Nearest()
        if _RC then
            SelectMgr.ChangeTarget(_RC.gameObject)
        end
    end

end

---每秒執行，若對象5秒內沒有攻擊玩家則消除
function SearchMgr.RefreshAttackMeListOnSecond()
    local _count = table.Count(m_Table_AttackMeObj)
    local _pos = {}
    for k,v in pairs(m_Table_AttackMeObj) do
        v.m_Time = v.m_Time - 1
        if v.m_Time <= 0 or v.m_RC.m_StateController:IsDead() then
            table.Clear(m_Table_AttackMeObj[k])
            m_Table_AttackMeObj[k] = nil
            m_Idx_CurSelect = 1
        else
            Vector3.GetTransformPosition(_pos, v.m_RC.transform)
            local _IsInvisible, _Dis = RoleMgr.GetIsInVisibleRange( _pos , SearchSetting.SEARCH_RANGE_ATTACKABLE_NPC )
            if not _IsInvisible then
                table.Clear(m_Table_AttackMeObj[k])
                m_Table_AttackMeObj[k] = nil
            else
                v.m_Distance = _Dis
            end
        end
    end

    if _count ~= table.Count(m_Table_AttackMeObj) then
        ---清單有變動
        this.RefreshTable(ESearchKind.AttackMeRole)

    end
end

function SearchMgr.IsAttackMe(iSID)
    return m_Table_AttackMeObj[iSID] ~= nil
end

---重置tab的順序
function SearchMgr.ResetTabIndex()
    m_Idx_CurSelect = 1
end

---Tab切換最近的攻擊目標
---@return RoleController
function SearchMgr.GetHostilityByTab()

    this.RefreshTable(ESearchKind.EnemyNPC)
    this.RefreshTable(ESearchKind.EnemyPlayer)

    local _TargetRC
    local _AttackMeList = SearchMgr.GetSearchList(ESearchKind.AttackMeRole)
    local _EnemyList = SearchMgr.GetSearchList(ESearchKind.EnemyNPC)
    local _EnemyPlayer = SearchMgr.GetSearchList(ESearchKind.EnemyPlayer)

    local _newList = {}
    for k,v in pairs(_AttackMeList) do
        table.insert(_newList, v.m_RC)
    end

    local _TmpTable = {}
    
    table.AddRange(_TmpTable, _EnemyList)
    table.AddRange(_TmpTable, _EnemyPlayer)

    table.sort(_TmpTable, function(k1, k2) return k1.m_SearchDist < k2.m_SearchDist end)

    for k,v in pairs(_TmpTable) do
        if v.m_StateController and (not v.m_StateController:IsDead()) then
            local _Toadd = true
            for _, _attackme in pairs(_AttackMeList) do
                if v == _attackme.m_RC then
                    _Toadd = false
                    break
                end
            end

            if _Toadd then
                table.insert(_newList, v)
            end
        end
    end
    _TmpTable = nil

    if m_Idx_CurSelect >= #_newList then
        m_Idx_CurSelect = 1
    end

    for i = m_Idx_CurSelect , #_newList do
        if _newList[i] ~= SelectMgr.m_TargetController and
        _newList[i].m_StateController ~= EStateType.Dead  then
            m_Idx_CurSelect = i
            _TargetRC = _newList[i]
            D.Log( "切換目標 CurIdx: " .. _TargetRC.m_DebugIdentifyName )
            return _TargetRC
        end
    end

    _newList = nil
    return _TargetRC
end

---取得最近的敵人 NPC 資料, 給戰鬥挑選用
---@param iSpecifyNPCID number 指定NPCID
---@return RoleController
function SearchMgr.GetHostility_Nearest( iSpecifyNPCID )
    if iSpecifyNPCID ~= nil then
        D.Log( "搜尋目標指定NPC ID: " .. iSpecifyNPCID )
    end

    local _TargetRC = nil
    local _searchList = SearchMgr.GetSearchList(ESearchKind.AttackMeRole)
    if table.Count(_searchList) > 0 and iSpecifyNPCID == nil then

        for k, v in pairs(_searchList) do
            if v.m_RC.m_StateController 
               and (not v.m_RC.m_StateController:IsDead()) then
                --權種最高且正在打我的人
                _TargetRC = v.m_RC
                break
            end
        end
    end
    
    if _TargetRC == nil then
        --- 沒人打我，找最近的人
        this.RefreshTable(ESearchKind.EnemyNPC)
        this.RefreshTable(ESearchKind.EnemyPlayer)

        local _TmpTable = {}
        local _EnemyList = SearchMgr.GetSearchList(ESearchKind.EnemyNPC)
        local _EnemyPlayer = SearchMgr.GetSearchList(ESearchKind.EnemyPlayer)    
        
        table.AddRange(_TmpTable, _EnemyPlayer)
        table.AddRange(_TmpTable, _EnemyList)

        table.sort(_TmpTable, function(k1, k2) return k1.m_SearchDist < k2.m_SearchDist  end)
        for k,v in pairs(_TmpTable) do
            if _TargetRC == nil and v.m_StateController and (not v.m_StateController:IsDead()) then
                _TargetRC = v
                break
            end
        end

        _TmpTable = nil
    end

    return _TargetRC
end

---取得最近的友善 NPC 資料
---@param iIdx number 有填是第幾位
---@return RoleController
function SearchMgr.GetFriendlyNPC(iIdx)
    ---不用刷新 table, 已經註冊 HEMTimer 處理
    ---近的優先
    local _searchList = SearchMgr.GetSearchList(ESearchKind.FriendlyNPC)

    return _searchList[iIdx]
end

---與最近的友善NPC互動
---@param iIdx number 取前面第幾個NPC(未填寫澤第一位)
function SearchMgr.InterActFrendlyNPC(iIdx)
    iIdx = iIdx == nil and 1 or iIdx
    
    SearchMgr.RefreshTable(ESearchKind.FriendlyNPC)
    
    local _RC_FriendlyNPC = this.GetFriendlyNPC(iIdx)
    if _RC_FriendlyNPC ~= nil then
        SelectMgr.ChangeAndInterAct(_RC_FriendlyNPC)
    end  
end

---清除搜尋資料
function SearchMgr.Clear()
    this.m_Table_AttackableNPC = {}
    this.m_Table_FriendlyNPC = {}
    this.m_Table_AttackablePlayer = {}
    this.m_Table_FriendlyPlayer = {}
end

---銷毀
function SearchMgr.Destroy()
    HEMTimeMgr.RemoveListener( SearchMgr[ESearchKind.FriendlyNPC].Refresh )
end

local m_SearchData = nil

--- 一鍵傳送 11號公車
---@param iEventID ushort 用事件找特定NPC
---@param iSceneID ushort 在哪個場景
---@param iPos Vector3 座標位置
---@param iFight bool 是否為戰鬥(否則與最近NPC對話)
---@param iAutoSearch bool 是否是由自己呼叫的
function SearchMgr.SearchNPCOnFoot(iEventID, iSceneID, iPos, iFight, iAutoSearch)
    local _SceneID = SceneMgr.GetSceneID()

    if not iAutoSearch then
        m_SearchData = {}
        m_SearchData.EventID = iEventID
        m_SearchData.SceneID = iSceneID
        m_SearchData.Pos = iPos
        m_SearchData.isFight = iFight
    end

    if m_SearchData == nil then
        return
    end
    ---換地圖接續用
    this.m_SearchNPCFunc = function() this.SearchNPCOnFoot(m_SearchData.EventID, m_SearchData.SceneID, m_SearchData.Pos ,m_SearchData.isFight, true) end

    if RoleMgr.m_RC_Player ~= nil then
        if _SceneID == m_SearchData.SceneID then
            local _searching = false
            m_SearchData.Pos.y = RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.m_RC_Player.transform).y
            if m_SearchData.EventID then
                local _NPC = NPCMgr.GetNPCControllerEventID(m_SearchData.EventID)
                if #_NPC > 0 and not Extension.IsUnityObjectNull(_NPC[1].gameObject) then
                    SelectMgr.ChangeAndInterAct(_NPC[1])
                    _searching = true
                end
            end
            if not _searching then
                if Vector3.Distance( m_SearchData.Pos, RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.m_RC_Player.transform)) <= RoleMgr.m_RC_Player.m_Radius then
                    this.m_SearchNPCFunc = nil
                    --抵達位置後看要自動戰鬥還是安怎
                    if m_SearchData.isFight then
                        MoveMgr.PlayerSelfPathFind(m_SearchData.Pos)
                        --設定自動戰鬥 
                        HotKey_Controller.QuestAutoFight(true)
                    else
                        --fix 23.12.25 已在距離內就不再次尋路，直接進行接續步驟
                        --MoveMgr.PlayerSelfPathFind(m_SearchData.Pos, this.InterActFrendlyNPC )
                        this.InterActFrendlyNPC()
                    end
                else
                    --路徑卡住的話要用這個接續
                    MoveMgr.PlayerSelfPathFind(m_SearchData.Pos, this.m_SearchNPCFunc )
                end    
            end
        elseif _SceneID ~= m_SearchData.SceneID then
    
            --從MapLink找到下一個傳送點
            local _transferPos = {}
            _transferPos = SceneMgr.GetMapLinkCoordinate(m_SearchData.SceneID)
            if _transferPos then
                --高度為一致再去確認距離
                _transferPos.y = RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.m_RC_Player.transform).y
                --如果很近代表傳點是NPC
                if Vector3.Distance(_transferPos, RoleMgr.m_currentVelocity:GetTransformPosition(RoleMgr.m_RC_Player.transform)) <= RoleMgr.m_RC_Player.m_Radius then
                    --fix 23.12.25 已在距離內就不再次尋路，直接進行接續步驟
                    --MoveMgr.PlayerSelfPathFind(_transferPos, this.InterActFrendlyNPC )
                    this.InterActFrendlyNPC()
                else
                    MoveMgr.PlayerSelfPathFind(_transferPos, this.m_SearchNPCFunc )
                    if AutoBattleMgr.m_AutoBattleState == AutoBattleMgr.EAutoBattleState.Play then
                        -- 暫停自動戰鬥
                        HotKey_Controller.QuestAutoFight(false)
                    end
                end
            else
                --無法傳送至場景
            end
        end
    end
end

---如果正在尋找目標
function SearchMgr.CrossSceneSearch()
    if m_SearchData then 
        SearchMgr.SearchNPCOnFoot(nil,nil,nil,nil,true)
    end
end

---清除自動尋找目標
function SearchMgr.ClearAutoSearch()
    this.m_AutoSearchTargrt = nil
end

---每一秒自動找尋最近目標
function SearchMgr.AutoSearchTarget()
    if this.m_AutoSearchTargrt and this.m_AutoSearch then
        local _RC = this.GetHostility_Nearest()
        if _RC ~= this.m_AutoSearchTargrt then
            this.m_AutoSearchTargrt = _RC
            if this.m_AutoSearchTargrt then
                SelectMgr.ChangeTarget(this.m_AutoSearchTargrt.gameObject)
            else
                SelectMgr.ClearSelect()
            end
        end
    end
end

---清空尋找目標
function SearchMgr.ClearSearchTarget()
    this.m_SearchNPCFunc = nil
    m_SearchData = nil

end

--- 一鍵傳送 花錢飛過去
function SearchMgr.SearchNPCWithMoney(iEventData)
end

function SearchMgr.OnClickMissionData(iMissionData)
    
    D.Log("戳到的任務".. EventStrAll:GetText(iMissionData.TitleStrID))
    local _Event = EventData:GetEventDataByIdx(iMissionData.EventID)
    if iMissionData.Finish == EventTipType.Undone and (iMissionData.POSX > 0 or iMissionData.POSY > 0) then
        local _eventID
        local _CanAutoFight = false
        if iMissionData.m_Step then
            _eventID = iMissionData.m_Step.EventID
            _CanAutoFight = iMissionData.m_Step.Auto == 1
        end
        this.SearchNPCOnFoot(_eventID, iMissionData.SceneID, Vector3(iMissionData.POSX * 0.01, 0, iMissionData.POSY * 0.01 ),_CanAutoFight)
    elseif _Event then
        if _Event.m_X1 > 0 or _Event.m_Y1 > 0 then
            --Event表拉出來的座標需要除以100
            this.SearchNPCOnFoot(iMissionData.EventID, _Event.m_SceneID, Vector3(_Event.m_X1 * 0.01, 0, _Event.m_Y1 * 0.01 ),false)
        else
            D.LogError("任務傳送位置為 0,0 ， 事件ID: "..iMissionData.EventID)
        end
    else
        D.LogError("任務傳送位置為 0,0 ，或取不到任務步驟事件， 事件ID: "..iMissionData.EventID)
    end

end

function SearchMgr.OnClickTeleport(iData)

    if(iData.EventID ~= nil) then

        local _Event = EventData:GetEventDataByIdx(iData.EventID)
        if  (iData.POSX > 0 or iData.POSY > 0) then -- iData.Finish == EventTipType.Undone and
            local _eventID
            if iData.m_Step then
                _eventID = iData.m_Step.EventID
            end
            SendProtocol_004._026(iData.SceneID, iData.POSX, iData.POSY)
            --this.SearchNPCOnFoot(_eventID, iData.SceneID, Vector3(iData.POSX * 0.01, 0, iData.POSY * 0.01 ),false)
        --elseif _Event then

        --    SendProtocol_004._025(1, iData.EventID)

        end

        --SendProtocol_004._025(1, iData.EventID)


    end

end

return SearchMgr
