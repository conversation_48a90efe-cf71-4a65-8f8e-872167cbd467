---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---資源列
---@class ResourceBar
---author 鼎翰
---version 1.0
---since [HEM 2.0]
---date 2022.01.13
ResourceBar = {}

---元件上顯示圖片的元件名稱
local _TheImageObjectName = "Image_ResourceIcon"
---資源 Prefab 的前綴
local _ResourcePrefabNameFront = "Group_Resource"

---@class EResourceGroupType 資源種類
EResourceGroupType = {
    --- 無
	None = 0,
    --- 基礎貨幣 [ 時空幣(鑽石), 黃易幣, 遊戲幣 ]
    BaseCurrency = 1,
	--- 武點
	WuPoint = 2,
}

---取得物品或標記數量
---@param iID number 物品或標記的 ID（貨幣通常會有假的物品，也會經由物品 ID 取）
---@return number Count 數量
function ResourceBar:GetAmount(iID)
	local _Amount = 0
    ---如果是貨幣類 找玩家資料
    if table.ContainsKey(EItemIDResourceTable, iID) then
        _Amount = PlayerData.GetCurrency(EItemIDResourceTable[iID])
    ---如果是物品 找背包
    else
		_Amount = BagMgr.GetItemInBagAmount(iID)
	end
    ---回傳數量
	return _Amount
end

---取得道具類用的 Sprite
---@param iItemID uint 道具ID
---@return string Sprite 名稱
local function GetItemSpriteName(iItemID)
	local _SpriteName =""
	local _ItemData = ItemData.GetItemDataByIdx(iItemID)
	if _ItemData then
		_SpriteName = _ItemData:GetItemTextureName()
	end
	return _SpriteName
end

---從 EResourceGroupType 對應資源的 itemID Table [新增貨幣群組時要新增於此]
---@param iEResourceGroupType EResourceGroupType 貨幣種類
---@return table 與資源類型相對應的物品 ID 們
local function GetItemIDTableFromEResourceGroupType(iEResourceGroupType)

	local _ItemIDTable = {}
	---資源類-貨幣 回傳 錢/黃易幣/時空幣
	
	if iEResourceGroupType == EResourceGroupType.BaseCurrency then
		table.insert(_ItemIDTable, table.GetKey(EItemIDResourceTable, ECurrencyType.Diamond))
		table.insert(_ItemIDTable, table.GetKey(EItemIDResourceTable, ECurrencyType.HEMCoin))
		table.insert(_ItemIDTable, table.GetKey(EItemIDResourceTable, ECurrencyType.Money))
	---資源類-武點 回傳 武點
	elseif iEResourceGroupType == EResourceGroupType.WuPoint then
		table.insert(_ItemIDTable, table.GetKey(EItemIDResourceTable, ECurrencyType.WugongPoint))
	end
	
	return _ItemIDTable
end

--region 資源列

local function ButtonIconOnClick(iItem)
	if iItem.m_ItemID ~= 0 then
		HintMgr_Controller.OpenHint(EHintType.ResourceBar, iItem.m_ItemID)
	else
		D.LogError("沒有設定物品ID")
	end

end

local function ButtonAddOnClick(iItem)
	if iItem.m_ItemID ~= 0 then
		D.Log("等待後續添加購買方法 道具ID = " .. iItem.m_ItemID)
	else
		D.LogError("沒有設定物品ID")
	end

end

---產生新的資源物件
---@param iItemID number 物品編號
---@param iTrans_Res_Parent Transform 物品的 Transform
---@param iPrefabName string Prefab 名稱
local function GetNewItem(iTrans_Res_Parent, iPrefabName)
	--設定單個資源/道具
	---資源種類
	local _Item = {}
	_Item.transform = iTrans_Res_Parent
	_Item.transform.name = iPrefabName
	---Icon
	_Item.m_Image_ResourceIcon = _Item.transform:Find(_TheImageObjectName):GetComponent( typeof( Image ) )
	_Item.m_Image_ResourceIcon.color = Color.White
	---Button_顯示資源的圖案 按下去要跳ItemHint
	_Item.m_Button_Icon = Button.New(_Item.m_Image_ResourceIcon.gameObject)
	---檢查ButtonEx是否存在 ItemID 是否不為0
	if _Item.m_Button_Icon ~= nil then
		_Item.m_Button_Icon:AddListener(EventTriggerType.PointerClick, ButtonIconOnClick, _Item)
	end
	---資源數量
	_Item.m_TMP_Text = _Item.transform:Find("TMP_Count"):GetComponent("TMPro.TextMeshProUGUI")
	---Button_Add 按鈕
	_Item.m_Table_Button = Button.New(_Item.transform:Find("&Button_Add").gameObject)
	---按鍵 添加資源/道具方法
	_Item.m_Table_Button:AddListener(EventTriggerType.PointerClick, ButtonAddOnClick, _Item)

	return _Item
end

---取得想設定的資源 Table
---@param iEResourceGroupType EResourceType 資源類型 [ 新增貨幣群組類型時，請搜尋 [新增貨幣群組時要新增於此] ]
---@param iItemTable table 另外要顯示的物品資源 [顯示在最前方]
function ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(iEResourceGroupType, iItemTable)
	---產生的資源們
	local _Table_ResourceUnit = GetItemIDTableFromEResourceGroupType(iEResourceGroupType)

	---如果沒有道具 就直接用資源類
	if iItemTable == nil  then
		iItemTable = _Table_ResourceUnit
	else
		---貨幣類塞在道具後面
		for i = 1, table.Count(_Table_ResourceUnit) do
			table.insert(iItemTable, _Table_ResourceUnit[i])
		end
	end

	return iItemTable
end

local function CreatResourceUnit(iUnit_Trans)
	local _ResourceUnit = iUnit_Trans:Instantiate(iUnit_Trans.parent)
	--_ResourceUnit:SetParent(iUnit_Trans.parent)
	_ResourceUnit.localScale = Vector3.one
	_ResourceUnit.localPosition = Vector3.zero
	return _ResourceUnit
end

---更新所有 ResourceUnit 資料
local function UpdateAllResourceUnitInfo(iResourceUnitCount, iResourceUnitTable, iCurrentItemTable)
	for i = 1, iResourceUnitCount do
		local _DataCount = table.Count(iCurrentItemTable)
		if i <= _DataCount then
			if iCurrentItemTable[i] ~= nil then
				---設定物品ID
				iResourceUnitTable[i].m_ItemID =  iCurrentItemTable[i]
				---設定圖片
				SpriteMgr.Load( GetItemSpriteName(iResourceUnitTable[i].m_ItemID), iResourceUnitTable[i].m_Image_ResourceIcon)
				---設定數量
				local _ResourceCount = ResourceBar:GetAmount(iResourceUnitTable[i].m_ItemID)
				iResourceUnitTable[i].m_TMP_Text.text = GValue.ValueToString(_ResourceCount, EValueShowType.Full)
				--檢查有沒有顯示
				if not iResourceUnitTable[i].transform.gameObject.activeSelf then
					iResourceUnitTable[i].transform.gameObject:SetActive(true)
				end
			end
		else
			--沒用到的就收起來
			iResourceUnitTable[i].transform.gameObject:SetActive(false)
		end
	end
end

---設定資源列
---@param iTrans_Resources Transform 資源列本人
---@param iItemTable table 已經處理好的資源 Table
---@param iIndex number 初始的 Index [= nil 預設為 1]
function ResourceBar:InitResourceBar(iTrans_Resources, iItemTables, iIndex)
	---初始的 Index
	local _Index = 1

	if iIndex ~= nil then
		_Index = iIndex
	end

	---ResourceBar 本人
	local _ResourceBar = {}
	setmetatable( _ResourceBar, { __index = ResourceBar } )
	
	---傳進來需要顯示的 table
	_ResourceBar.m_Table_ItemID = iItemTables
	---目前顯示 m_Table_ItemID 的 Index
	_ResourceBar.m_CurrentShowDataIndex = _Index
	---Resource 元件結構的最大總數
	local _MaxResourceUnitCount = 1
	--計算最大所需的數量
	for i = 1, table.Count(iItemTables) do
		if table.Count(iItemTables[i]) > _MaxResourceUnitCount then
			_MaxResourceUnitCount = table.Count(iItemTables[i])
		end
	end
	---Resource 元件結構的最大總數
	_ResourceBar.m_MaxResourceUnitCount = _MaxResourceUnitCount

	---Resource 元件結構的 Table
	local _Table_ResourceUnit = {}
	---資源的 RectTransform
	local _Table_Resource_Trans = {}
	_Table_Resource_Trans[1] = iTrans_Resources:Find(_ResourcePrefabNameFront)
	for i = 1, _MaxResourceUnitCount do
		---產生單個UI
		if i ~= 1  then
			_Table_Resource_Trans[i] = CreatResourceUnit(_Table_Resource_Trans[1])
		end
		--建立 Resource 元件結構
		_Table_ResourceUnit[i] = GetNewItem(_Table_Resource_Trans[i], _ResourcePrefabNameFront .. i)
	end

	---存最大數量的 Resource 元件結構
	_ResourceBar.m_Table_ResourceUnit = _Table_ResourceUnit

	--產生物件完成

	--開始輸入資料
	if iItemTables[_Index] ~= nil then
		UpdateAllResourceUnitInfo(_ResourceBar.m_MaxResourceUnitCount, _Table_ResourceUnit, iItemTables[_Index])
	end

	return _ResourceBar
end

---給其他介面用的即時刷新 資源/道具方法
---@param iChangeIndex number 要更換到資料的哪個 Index [= nil 預設為更新原本的]
function ResourceBar:OnUpdate(iChangeIndex)
	--更新原本的資源
	if iChangeIndex == nil or self.m_CurrentShowDataIndex == iChangeIndex then
		if table.Count(self.m_Table_ResourceUnit) > 0 then
			for i = 1, table.Count(self.m_Table_ResourceUnit) do
				---刷新數量
				if self.m_Table_ResourceUnit[i].m_ItemID ~= nil then
					local _ResourceCount = ResourceBar:GetAmount(self.m_Table_ResourceUnit[i].m_ItemID)
					self.m_Table_ResourceUnit[i].m_TMP_Text.text = GValue.ValueToString(_ResourceCount, EValueShowType.Full)
				end
			end
		end
	else
		--Index 設定不正確比資料結構還大
		if iChangeIndex > table.Count(self.m_Table_ItemID) then
			D.LogError("ResourceBar 更新錯誤，Index 設定比資料結構還大。 Index：".. iChangeIndex)
			return
		end
		--更新物品
		UpdateAllResourceUnitInfo(self.m_MaxResourceUnitCount, self.m_Table_ResourceUnit, self.m_Table_ItemID[iChangeIndex])
		--物品更新完之後 Index 要更新
		self.m_CurrentShowDataIndex = iChangeIndex
	end
end

--endregion
