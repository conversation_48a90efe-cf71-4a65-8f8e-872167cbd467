---=====================================================================
---             CHINESE GAMER PROPRIETARY INFORMATION
---
---This software is supplied under the terms of a license agreement or
---nondisclosure agreement with CHINESE GAMER and may not
---be copied or disclosed except in accordance with the terms of that
---agreement.
---
---                Copyright © 2021 by CHINESE GAMER.
---                     All Rights Reserved.
---
---   -------------------------------------------------------------
---
---=====================================================================
--region require Lua
json = require 'cjson'
--endregion

--region require Lua script
require("Common/string")
require("Common/Stack")
require("Common/Queue")
require("Common/AttributesFormula")
require("Common/AnimationDefine")
require("Common/UIUnit/Button")
require("Common/UIUnit/GroupButton")
require("Common/UIUnit/EventTrigger")
require("Common/UIUnit/ResourceBar")
require("Common/UIUnit/ScrollView")
require("Common/UIUnit/UIVisibleEffect")
require("Common/UIUnit/Dropdown")
require("Common/UIUnit/TMP_Hyperlink")
require("Common/UIUnit/LeftGroupTab")
require("Common/UIUnit/FullPageTitleBar")
require("Logic/JsonMgr")
--endregion



--region UnityEngine Object Reference
Vector3 = UnityEngine.Vector3
Vector4 = UnityEngine.Vector4
Quaternion = UnityEngine.Quaternion
Application = UnityEngine.Application
NetworkReachability = UnityEngine.NetworkReachability
SystemInfo = UnityEngine.SystemInfo
UnityWebRequest = UnityEngine.Networking.UnityWebRequest
Transform = UnityEngine.Transform
GameObject = UnityEngine.GameObject
Resources = UnityEngine.Resources
Texture2D = UnityEngine.Texture2D
FilterMode = UnityEngine.FilterMode
RenderMode = UnityEngine.RenderMode
WrapMode = UnityEngine.WrapMode
EventTriggerType = UnityEngine.EventSystems.EventTriggerType
Sprite = UnityEngine.Sprite
Rect = UnityEngine.Rect
RectTransformUtility = UnityEngine.RectTransformUtility
Input = UnityEngine.Input
Canvas = UnityEngine.Canvas
Screen = UnityEngine.Screen
AudioClip = UnityEngine.AudioClip
AudioSource = UnityEngine.AudioSource
KeyCode = UnityEngine.KeyCode
TextAnchor = UnityEngine.TextAnchor
RectTransform = UnityEngine.RectTransform
Edge = UnityEngine.RectTransform.Edge
Axis = UnityEngine.RectTransform.Axis
Texture = UnityEngine.Texture
RawImage = UnityEngine.UI.RawImage
Image = UnityEngine.UI.Image
Text = UnityEngine.UI.Text
Toggle = UnityEngine.UI.Toggle
Slider = UnityEngine.UI.Slider
RenderTexture = UnityEngine.RenderTexture
GridLayoutGroup = UnityEngine.UI.GridLayoutGroup
VerticalLayoutGroup = UnityEngine.UI.VerticalLayoutGroup
HorizontalLayoutGroup = UnityEngine.UI.HorizontalLayoutGroup
---@class InputField
InputField = UnityEngine.UI.InputField
Outline = UnityEngine.UI.Outline
LayoutRebuilder = UnityEngine.UI.LayoutRebuilder
CharacterController = UnityEngine.CharacterController
Material = UnityEngine.Material
MaterialPropertyBlock = UnityEngine.MaterialPropertyBlock
Shader = UnityEngine.Shader
Physics = UnityEngine.Physics
BoxCollider = UnityEngine.BoxCollider
LoadSceneMode = UnityEngine.SceneManagement.LoadSceneMode
Animator = UnityEngine.Animator
Profiler = UnityEngine.Profiling.Profiler
Random = UnityEngine.Random
Camera = UnityEngine.Camera
CollisionFlags = UnityEngine.CollisionFlags
AudioSourceCurveType = UnityEngine.AudioSourceCurveType
AudioRolloffMode = UnityEngine.AudioRolloffMode
ColorUtility = UnityEngine.ColorUtility
LODGroup = UnityEngine.LODGroup
ParticleSystem = UnityEngine.ParticleSystem
SkinnedMeshRenderer = UnityEngine.SkinnedMeshRenderer
LayoutRebuilder = UnityEngine.UI.LayoutRebuilder
SpriteMgr = GameTools.Sprite.SpriteMgr
FTPFileDownloadMgr = GameTools.FTP.FTPFileDownloadMgr
TMP_Dropdown = TMPro.TMP_Dropdown
OptionData = TMPro.TMP_Dropdown.OptionData
OptionDataList = TMPro.TMP_Dropdown.OptionDataList
ContentType =TMPro.TMP_InputField.ContentType
TMP_Settings = TMPro.TMP_Settings
TMP_TextParsingUtilities = TMPro.TMP_TextParsingUtilities
RectMask2D = UnityEngine.UI.RectMask2D
ProjectMgr = GameTools.Project.ProjectMgr
ScreenOrientation = UnityEngine.ScreenOrientation
D = GameTools.Log.D
ExAPI = GameTools.ExAPI
LeanTweenVisual = DentedPixel.LTEditor.LeanTweenVisual
TMP_TextUtilities = TMPro.TMP_TextUtilities
TMP_LinkInfo = TMPro.TMP_LinkInfo
TMP_StyleSheet = TMPro.TMP_StyleSheet
TMP_Style = TMPro.TMP_Style
GUIUtility = UnityEngine.GUIUtility
VideoPlayer = UnityEngine.Video.VideoPlayer
VideoRenderMode = UnityEngine.Video.VideoRenderMode
VideoClip = UnityEngine.Video.VideoClip
MeshRenderer = UnityEngine.MeshRenderer
SoftMask = SoftMasking.SoftMask
BaseEventData = UnityEngine.EventSystems.BaseEventData
UnityAction_BaseEventData = UnityEngine.Events.UnityAction_UnityEngine_EventSystems_BaseEventData
Entry = UnityEngine.EventSystems.EventTrigger.Entry

ButtonEx = GameTools.UIExtension.ButtonEx
ButtonDelegateMgr = GameTools.UIExtension.ButtonDelegateMgr
UIGroupButtonCtrl = GameTools.UIExtension.UIGroupButtonCtrl
UIRenderCtrl = GameTools.UIExtension.UIRenderCtrl
ESelectionState = GameTools.UIExtension.ESelectionState

UIRenderActive = GameTools.UIExtension.UIRenderActive
UIImageChange = GameTools.UIExtension.UIImageChange
UIRenderMove = GameTools.UIExtension.UIRenderMove
UIRenderScale = GameTools.UIExtension.UIRenderScale
UIRenderChangeColor = GameTools.UIExtension.UIRenderChangeColor
UIRenderTMPTextChangeStyle = GameTools.UIExtension.UIRenderTMPTextChangeStyle


--endregion

--region System Object Reference
DateTime = System.DateTime
--endregion

--region AStar
FunnelModifier = Pathfinding.FunnelModifier;
Seeker  = Pathfinding.Seeker;
--endregion

--region UI 顯示永標設定
---UI 顯示永標 最小值
UIVISIBLE_STATIC_FLAGID_MIN = 2801
---UI 顯示永標 最大值
UIVISIBLE_STATIC_FLAGID_MAX = 2900
---UI 顯示永標 例外
UIVisible_StaticFlag_Exception = {3701, 9999}
--endregion

---每開啟一個 UI 時要增加的階層數值
UI_LAYER_ORDER_VALUE_ADD = 10

---DataMgr 每次讀串檔內的多少位元組資料
MAX_DATA_LOAD_ONCE_BYTES = 10240

---@class EConnectTarget 連線目標
EConnectTarget = {
    --- 0. 遊戲伺服器
    GameServer = 0,
    --- 1. 燈號機
    SeverLight = 1,
}

--region Encoding

---@class System.Text.Encoding  System.Text.Encoding 的 字碼頁
---用於 相關需要 System.Text.Encoding.CodePages 的參數 (Extension.BytesToString(Encoding iEncoding, byte[] iBytes))
---要用可以自己加 參考網址 https://learn.microsoft.com/zh-tw/dotnet/api/system.text.encoding?view=net-7.0
EncodingCodepage = {
    ---950 繁體中文-big5
    Default = 950,
    ---936 簡體中文
    GB2312 = 936,
    ---950 繁體中文
    --Big5 = 950,
    ---Unicode (UTF-8)
    UTF8 = 65001,
    ---Unicode (UTF-7)
    UTF7 = 65000,
    ---Unicode (UTF-32)
    UTF32 = 12000,
    ---Unicode (UTF-16)
    Unicode = 1200,
    ---US-ASCII
    ASCII = 20127,
}
--endregion

--region Screen

---螢幕比例列舉
---@class EResolutionStyle
EResolutionStyle =
{
    ---螢幕比例預設(16:9)
    Default = 0,
    ---螢幕比例寬版(21:9)
    Wide = 1,
    ---螢幕比例窄版(4:3)
    Narrow = 2,
    ---螢幕比例特殊寬(2:1)
    Special = 3,
    ---其他
    Other = 4
}

---螢幕比例列舉名字
---@class EResolutionStyleName
EResolutionStyleName =
{
    [EResolutionStyle.Default] = 438011,
    [EResolutionStyle.Wide] = "寬版(缺字)",
}

---螢幕比例列舉
---@class EResolutionStyleValue
EResolutionStyleValue =
{
    ---螢幕比例預設(16:9)
    [EResolutionStyle.Default] = 16/9,
    ---螢幕比例寬版(21:9)
    [EResolutionStyle.Wide] = 21/9,
    ---螢幕比例窄版(4:3)
    [EResolutionStyle.Narrow] = 4/3,
    ---螢幕比例特殊寬(2:1)
    [EResolutionStyle.Special] = 2/1,

}

--endregion

---沒有永標的時候 物件要怎麼顯示
---@class EShowTypeWithNoFlag
EShowTypeWithNoFlag = {
    --- 0 = 預設 不顯示
    Default = 0,
    --- 1 = 反灰
    Disable = 1,
    --- 2 = 自訂 function(iGObjComponent, iStaticFlagID, iIsActive)
    Function = 2,
}

--region UI

--region UI Boundary
---最大內縮距離
MAX_INDENTATION_DISTANCE = 200
--endregion

--region UIOrder 類型

---UI 介面分層
---@class EUIOrderLayers UI 介面分層
EUIOrderLayers = {
    ---左邊半版介面層
    HalfPage_Left = 1,
    ---右邊半版介面層
    HalfPage_Right = 2,
    ---中間半版介面層
    HalfPage_Center = 3,
    ---全版介面層
    FullPage = 4,
    ---頂層
    Peak = 5,
    ---Debug 層
    Debug = 6
}

--endregion

---UI 的階段
---@class EUIStage UI 目前的階段
EUIStage = {
    ---無
    None = 0,
    ---讀取 Asset 中
    LoadingFromResourceMgr = 1,
    ---已讀取物件
    UIObjLoaded = 2,
    ---初始化中
    Initializing = 3,
    ---初始化 已完成
    Initialized = 4,
    ---BG準備完成
    LoadBGFinish = 5,
    ---開啟中
    Opening = 6,
    ---開啟效果動作中
    DoingOpenEffect = 7,
    ---已開啟
    Opened = 8,
    ---關閉中
    Closing = 9,
    ---關閉效果動作中
    DoingCloseEffect = 10,
    ---已關閉
    Closed = 11,
}

---介面開關類型
---@class EUIOpenCloseType
EUIOpenCloseType = {
    ---0. 預設(與 ChangeActive 同)
    Defult = 0,
    ---1. 改變顯示
    ChangeActive = 1,
    ---2. 移出相機範圍
    MoveOutOfCameraRange = 2,
}

---UI Drag type
---@class EDragType
EDragType = {
    None = 0,
    Up = 1,
    Down = 2,
    Left = 3,
    Right = 4
}

--endregion

---取用字串Icon
ICON_STR = "Icon"

---@class EPlotAnimationPlayType 劇情動畫播放類型
EPlotAnimationPlayType =
{
    ---0. None
    None = 0,
    ---1. 場景動畫
    TimeLine = 1,
    ---2. CG 動畫
    CGPlayer = 2,
}

---@class ETimeLineSkipMode TimeLine 跳過類型
ETimeLineSkipMode =
{
    --- 0 不可按ESC
    None = 0,
    --- 1 不可按ESC
    CannotSkip = 1,
    --- 2.個人可按ESC
    CanSkip = 2,
    --- 3.團體中隊長按ESC
    LeaderCanSkip = 3
}

---教學類型
---@class ETeachType
ETeachType = {
    ---圖片
    RawImage = 1,
    ---打字機
    TypingMeching = 2,
    ---遮罩重點式步驟教學
    FocusStep = 3,
    ---事件
    Event = 4,
}

---教學元件類型
---@class ETeachComponentType
ETeachComponentType = {
    ---預設[點選沒有功能，直接進行下一步]
    Default = 0,
    ---按鈕
    Button = 1,
    ---拉霸
    Slider = 2,
    ---勾選
    Toggle = 3,
    ---動態生成Icon
    DynamicIcon = 4,
}

---教學的鎖定遮罩樣式
---@class ETeachFocusMaskImageType
ETeachFocusMaskImageType = {
    ---無
    None = 0,
    ---依照按鈕範圍遮罩
    Default = 1,
    ---依照按鈕範圍遮罩 旁邊加右箭頭
    DefaultAndRightArrow = 2,
    ---依照按鈕範圍遮罩 旁邊加左箭頭
    DefaultAndLeftArrow = 3,
    ---按鈕為中心框出方形光圈
    SquareAperture = 4,
    ---按鈕為中心框出圓形光圈
    CircularAperture = 5,
}

---畫布的九宮格位置
---@class ENinePlacesOnCanvas
ENinePlacesOnCanvas = {
    ---左上 = 1
    UpperLeft = 1,
    ---左中 = 2
    MiddleLeft = 2,
    ---左下 = 3
    LowerLeft = 3,
    ---中上 = 4
    UpperMiddle = 4,
    ---置中 = 5
    Center = 5,
    ---中下 = 6
    LowerMiddle = 6,
    ---右上 = 7
    UpperRight = 7,
    ---右中 = 8
    MiddleRight = 8,
    ---右下 = 9
    LowerRight = 9,
}

---Anchors 相對應於畫布的九宮格的位置
---@class EAnchorsNinePlacesOnCanvas
EAnchorsNinePlacesOnCanvas = {
    ---左上
    [ENinePlacesOnCanvas.UpperLeft] = { anchorMin = Vector2(0, 1), anchorMax = Vector2(0, 1) },
    ---左中
    [ENinePlacesOnCanvas.MiddleLeft] = { anchorMin = Vector2(0, 0.5), anchorMax = Vector2(0, 0.5) },
    ---左下
    [ENinePlacesOnCanvas.LowerLeft] = { anchorMin = Vector2(0, 0), anchorMax = Vector2(0, 0) },
    ---中上
    [ENinePlacesOnCanvas.UpperMiddle] = { anchorMin = Vector2(0.5, 1), anchorMax = Vector2(0.5, 1) },
    ---置中
    [ENinePlacesOnCanvas.Center] = { anchorMin = Vector2(0.5, 0.5), anchorMax = Vector2(0.5, 0.5) },
    ---中下
    [ENinePlacesOnCanvas.LowerMiddle] = { anchorMin = Vector2(0.5, 0), anchorMax = Vector2(0.5, 0) },
    ---右上
    [ENinePlacesOnCanvas.UpperRight] = { anchorMin = Vector2(1, 1), anchorMax = Vector2(1, 1) },
    ---右中
    [ENinePlacesOnCanvas.MiddleRight] = { anchorMin = Vector2(1, 0.5), anchorMax = Vector2(1, 0.5) },
    ---右下
    [ENinePlacesOnCanvas.LowerRight] = { anchorMin = Vector2(1, 0), anchorMax = Vector2(1, 0) },
}

---AnchorPosition 的 Normalize
---@class ENormalizeAnchorPosition
ENormalizeAnchorPosition = {
    ---左上
    [ENinePlacesOnCanvas.UpperLeft] = Vector3(1, -1, 1),
    ---左中
    [ENinePlacesOnCanvas.MiddleLeft] = Vector3(1, 0, 0),
    ---左下
    [ENinePlacesOnCanvas.LowerLeft] = Vector3(1, 1, 0),
    ---中上
    [ENinePlacesOnCanvas.UpperMiddle] = Vector3(0, -1, 0),
    ---置中
    [ENinePlacesOnCanvas.Center] = Vector3(0, 0, 0),
    ---中下
    [ENinePlacesOnCanvas.LowerMiddle] = Vector3(0, 1, 0),
    ---右上
    [ENinePlacesOnCanvas.UpperRight] = Vector3(-1, -1, 0),
    ---右中
    [ENinePlacesOnCanvas.MiddleRight] = Vector3(-1, 0, 0),
    ---右下
    [ENinePlacesOnCanvas.LowerRight] = Vector3(-1, 1, 0),
}

---教學說明底板樣式
---@class ETeachInfoFrameType
ETeachInfoFrameType = {
    ---無
    None = 0,
    ---有頭像的方形
    SquareWithHeadIcon = 1,
    ---方形
    Square = 2,
    ---圓形
    Circle = 3,
}

--region Enum
---@class EServerFeature 伺服器功能
EServerFeature = {
    --- 虛寶卡
    VerifyCode = 1,
    --- 儲值雙倍
    DaubleGould = 2,
    --- PK 伺服器
    PKServer = 3,
    --- 時空幣市集(個人商店用)
    DiamondMall = 4
}

---@class ERank 品階
ERank = {
    --- 無
    None = 0,
    --- 白
    White = 1,
    --- 綠
    Green = 2,
    --- 藍
    Blue = 3,
    --- 紫
    Purple = 4,
    --- 紅
    Red = 5,
    --- 金
    Gold = 6,
    --- 暗金色
    DarkGold = 7,
    --- 彩色
    Rainbow = 8,
}

---@class EPlayerGender 性別
EPlayerGender = { Man = 0, Woman = 1 }

---@class EPlayerModelStatus 模型種類
ModelStatus = {
    --- 不處理 NPC(包含採集物), 變身玩家(採礦機), 隊友玩家
    Non = 0,
    --- 完全外觀 計算預算
    FullModel = 1,
    --- 通用模型 不計算預算
    BudgetModel = 2,
    --- 制服 計算預算
    Uniform = 3,
}

---對應企劃編輯特效掛點編號
---@class EBoneIdx
EBoneIdx = {
    Non = 0,
    Foot = 1,
    L_W0 = 2,
    L_W1 = 3,
    L_W2 = 4,
    L_W3 = 5,
    L_W4 = 6,
    R_W0 = 7,
    R_W1 = 8,
    R_W2 = 9,
    R_W3 = 10,
    R_W4 = 11,
    Chest = 12,
    Head = 13,
    L_Hand = 14,
    R_Hand = 15,
    Pelvis = 16,
    BothHand = 100,
    TargetGround = 101
}

---對應骨架物件名稱
---@class EBoneName
EBoneName = {
    Non = "Empty",
    Foot = "Foot",
    --- 2: 刀 左手心
    L_W0 = "WA_L0",
    --- 3: 劍 左手 (劍鞘中心)
    L_W1 = "WA_L1",
    --- 4: 拳 左手心
    L_W2 = "Bip001 L Forearm",
    --- 5: 槍 左手心
    L_W3 = "WA_L3",
    --- 6: 棍 左手心
    L_W4 = "WA_L4",
    --- 7: 刀 右手 (刀中心)
    R_W0 = "WA_R0",
    --- 8: 劍 右手 (劍中心)
    R_W1 = "WA_R1",
    --- 9: 拳 右手肘
    R_W2 = "Bip001 R Forearm",
    ---10: 槍、棍 (槍、棍中心)
    R_W3 = "WA_R3",
    ---11: 槍、棍 右手心
    R_W4 = "WA_R4",
    ---12: 胸口 (身體中心)
    Chest = "Bip001 Spine1",
    ---13: 頭的中央
    Head = "Bip001 Head",
    ---14: 左手腕
    L_Hand = "Bip001 L Hand",
    ---15: 右手腕
    R_Hand = "Bip001 R Hand",
    ---16: 身體正中央
    Pelvis = "Bip001 Pelvis",
    ---100: 雙手
    BothHand = "",
    --- 101: 地板
    TargetGround = ""
}

---判斷是否是武器的骨架位置
---@class EWeaponBone
EWeaponBone = {EBoneIdx.R_W0, EBoneIdx.R_W1, EBoneIdx.L_W1, EBoneIdx.R_W2, EBoneIdx.L_W2, EBoneIdx.R_W3, EBoneIdx.BothHand}

---@class Gang 心性
Gang = {
    --- 防呆用
    None = 0,
    --- 佛
    Buddhism = 1,
    --- 道
    Taoism = 2,
    --- 佛、道
    Buddhism_Taoism = 3,
    --- 魔
    Demonic = 4,
    --- 佛、魔
    Buddhism_Demonic = 5,
    --- 道、魔
    Taoism_Demonic = 6,
    --- 佛、道、魔
    Buddhism_Taoism_Demonic = 7,
}

---@class EWeaponType
EWeaponType = {
    --- 無(空手是算拳，這是檢查用的，請注意)
    Non = 0,
    --- 刀
    Knife = 1,
    --- 劍
    Sword = 2,
    --- 拳
    Fist = 3,
    --- 槍
    Pike = 4,
    --- 棍
    Stick = 5,
}

---@class EWugongType
EWugongType = {
    Non = 0,
    --- 刀
    Knife = 1,
    --- 劍
    Sword = 2,
    --- 拳
    Fist = 3,
    --- 槍
    pike = 4,
    --- 棍
    Stick = 5,
    --- 內功
    Skill = 6,
    --- 心法
    Hart = 7,
    --- 輕功
    Fly = 8,
    --- 特殊
    Special = 9,
    --- 動物
    Animal = 10,
    --- 陣法
    Formation = 11,
    --- 情侶技
    Lovers = 12,
    --- 戒指
    Ring = 13,
}

---@class ItemEquipPos 物品表裝備位置
ItemEquipPos = {
    None = 0,
    --- 刀
    Knife = 1,
    --- 劍
    Sword = 2,
    --- 拳
    Fist = 3,
    --- 槍
    Pike = 4,
    --- 棍
    Stick = 5,
    --- 帽
    Hat = 6,
    --- 衣服
    Cloth = 7,
    --- 護腕
    Hand = 8,
    --- 襯衣
    Shirt = 9,
    --- 鞋子
    Shoe = 10,
    --- 面具
    Mask = 11,
    --- 項鍊
    Necklace = 12,
    --- 戒指
    Ring = 13,
    --- 披風
    Cloak = 14,
	--- 時裝刀
	Fashion_Knife = 21,
	--- 時裝劍
	Fashion_Sword = 22,
	--- 時裝拳
	Fashion_Fist = 23,
	--- 時裝槍
	Fashion_Pike = 24,
	--- 時裝棍
	Fashion_Stick = 25,
	--- 時裝帽
	Fashion_Hat = 26,
	--- 時裝衣
	Fashion_Cloth = 27,
	--- 時裝面具
	Fashion_Mask = 28,
	--- 時裝背飾
    Fashion_Back = 29,
}

--region 物品表的物品種類
---@class EItemType 物品種類
EItemType = {}
---無
EItemType.None = 0
---刀
EItemType.Weapon_Knife = 10
---劍
EItemType.Weapon_Sword = 11
---拳
EItemType.Weapon_Fist = 12
---槍
EItemType.Weapon_Pike = 13
---棍
EItemType.Weapon_Stick = 14
---頭部
EItemType.Hat = 19
---身體
EItemType.Cloth = 20
---襯衣
EItemType.Shirt = 21
---手部
EItemType.Hand = 22
---腳部
EItemType.Shoe = 23
---戒指
EItemType.Ring = 24
---面具 ( 以前是耳環 )
EItemType.Mask = 25
---項鍊
EItemType.Necklace = 26
---掛飾 ( 披風 )
EItemType.Cloak = 27
---藥水
EItemType.Medicine = 29
---大罐藥水
EItemType.BigMedicine = 30
---銀票
EItemType.Check = 31
---存款單
EItemType.Deposit = 32
---經驗道具
EItemType.Experience = 33
---攻城道具
EItemType.Siege = 34
---寵物藥品
EItemType.Pet = 35
---煙火
EItemType.Fireworks = 36
---永丹
EItemType.YongDan = 37
---屬性重置
EItemType.AttributesReset = 38
---飛行器
EItemType.Aircraft = 39
---修理道具
EItemType.FixTool = 40
---副本重置道具
EItemType.DungeonReset = 41
---組織道具
EItemType.Organizing = 42
---秘笈
EItemType.Secret = 43
---虛寶
EItemType.VTreasure = 44
---轉換器
EItemType.ChangeTool = 45
---遊戲內福袋
EItemType.LuckyBag = 46
---寶箱
EItemType.TreasureBox = 47
---資源探測工具
EItemType.Tools = 48
---放置型資源探測工具
EItemType.ToolsPlace = 49
---靈獸武器 - 鑰匙
EItemType.Key = 50
---靈獸糧食 - 上鎖的寶箱
EItemType.LockBox = 51
---靈獸精元體
EItemType.SpiritEssence = 52
---戰場藥
EItemType.WarMedicine = 53
---放丹道具
EItemType.FunDan = 54
---兌換券
EItemType.ExchangeCard = 55
---情緣道具
EItemType.AffectionKind = 56
---傳入道具
EItemType.TransportIn = 57
---傳出道具
EItemType.TransportOut = 58
---染劑
EItemType.Dye = 59
---強化卷軸
EItemType.EnhancedScroll = 60
---強化機率丹
EItemType.EnhancedProb = 61
---提煉石
EItemType.RefinedStone = 62
---洗煉道具
EItemType.RePropItem = 63
---神秘寶石
EItemType.ScrectGem = 65
---打洞道具
EItemType.RemoveMosaicItem = 66
---消除道具
EItemType.PunchHoleItem = 67
---取出寶石
EItemType.TakeOutGem = 68
---採集的合成材料
EItemType.ProbeItem = 70
---增加武器合成成功機率
EItemType.WeaponSynthesis = 71
---增加防具合成成功機率
EItemType.ArmorSynthesis = 72
---寵物
EItemType.PetItem = 75
---靈獸
EItemType.SpiritPet = 76
---單人座騎
EItemType.SingleRide = 77
---雙人座騎
EItemType.DoubleRide = 78
---多載座騎
EItemType.MultiRide = 79
---戰鬥坐騎
EItemType.FightRide = 80
---兌錢道具
EItemType.ExchangeMoney = 91
---特殊/任務/副本
EItemType.Mission = 92
---雜物( 活動 )
EItemType.Activity = 93
---返億物品
EItemType.DungeonItem = 94
---光束手槍
EItemType.Weapon_LightGun = 96
---時裝頭
EItemType.Fashion_Head = 105
---時裝衣服
EItemType.Fashion_Cloth = 106
---時裝面具
EItemType.Fashion_Mask = 108
---時裝披風
EItemType.Fashion_Cloak = 109
---時裝刀
EItemType.FashionWeapon_Knife = 118
---時裝劍
EItemType.FashionWeapon_Sword = 119
---時裝拳
EItemType.FashionWeapon_Fist = 120
---時裝槍
EItemType.FashionWeapon_Pike = 121
---時裝棍
EItemType.FashionWeapon_Stick = 122
---持續藥水
EItemType.SustainingMedicine = 123
--endregion

--region 物品狀態
---@class EItemStatus 物品狀態
EItemStatus = {
    ---正常閒置中
    Normal = 0,
    ---安全交易中
    SafeTransaction = 1,
    ---拍賣中
    Auction = 2,
    ---騎乘中
    Riding = 3,
    ---採集中
    Probe = 4,
    ---上託鏢中
    Express = 5,
    ---販售中
    Sell = 7,
    ---放置探測中
    ProbePlace = 8,
    ---非法物
    StealItem = 10,
    ---交易上鎖
    TransactionLock = 12,
    ---騎乘上鎖中
    RideLock = 13,
    ---綁定
    Binding = 14,
    ---著裝中
    DressUp = 15,
    ---試穿中
    TryOn = 16,
    ---非法物 騎乘中
    StealItemLock = 17
}

---@class ECullingState
ECullingState = {
    --- 近範圍內可視
    IN_NEARBY_RANGE = 0,
    --- 中範圍內可視
    IN_MEDIUM_RANGE = 1,
    --- 過渡範圍(主要用來處理淡入淡出等中遠範圍的過渡效果)
    IN_TRANSITION_RANGE = 2,
    --- 遠範圍
    IN_FARAWAY_RANGE = 3,
    --- 範圍外
    OUT_OF_RANGE = 4
}

---模型類型
EModelKind = {
    None = 0,
    --- 玩家
    Player = 1,
    --- 英雄(HM、HW)
    Hero = 2,
    --- 一般NPC(NM、NW)
    Normal = 3,
    --- 怪物(M)
    Monster = 4,
    --- 採集物(CO)
    COllection = 5,
}

---@class ELoginFunc
ELoginFunc = {
    --- 登入
    NormalLogin = 0,
    --- 註冊
    Register = 1,
}

---@class AccountType 帳號類型
EAccountType = {
    None = -1,
    ChineseGamer = 0,
    FB = 1,
    Twitter = 2,
    Google = 3,
    Apple = 4,
    Playpark = 5,
}

---@class ELanguage 語言相關
ELanguage = {
    TW = 0,
    CN = 1,
    TH = 2,
}

SERVER_FTP_CODE = 1
--endregion

--region Const Value
---名字最大長度
LIMIT_NAME = 7

--region Attributes Min & Max
MinStr = 1
MaxStr = 512
MinAgi = 1
MaxAgi = 512
MinCon = 1
MaxCon = 512
MinInt = 1
MaxInt = 512
MinMen = 1
MaxMen = 512
BaseLoad = 625
MinLoad = 1
MaxLoad = 50000
MinHp = 50
MaxHp = ********
MinMp = 50
MaxMp = ********
MinSp = 10
MaxSp = ********
MinDp = 50
MaxDp = ********
MinDmg = 0
MaxDmg = 65535
MinMDmg = 0
MaxMDmg = 65535
MinDef = 0
MaxDef = 65535
MinMDef = 0
MaxMDef = 65535
MinHit = 0
MaxHit = 1000
MinDge = 0
MaxDge = 1000
MinCri = 0
MaxCri = 95
MinCriDmg = -10000
MaxCriDmg = 10000
MinMSpd = 1
MaxMSpdW = 24
MaxMSpdR = 40
MinASpd = 300
MaxASpd = 3000
MinMnuDmg = 0
MaxMnuDmg = 150
MinOffDmg = 0
MaxOffDmg = 75
MinMaxHurt = 0
MaxMaxHurt = 135000
MinBladeDmg = 0
MaxBladeDmg = 10000
MinSwordDmg = 0
MaxSwordDmg = 10000
MinFightDmg = 0
MaxFightDmg = 10000
MinSpearDmg = 0
MaxSpearDmg = 10000
MinStickDmg = 0
MaxStickDmg = 10000
MinColdTime = 0
MaxColdTime = 10000
MinMagColdT = 0
MaxMagColdT = 10000
MinHurtRate = -10000
MaxHurtRate = 10000
MinMnuHurtR = -10000
MaxMnuHurtR = 10000
MinHPRec = -1000000
MaxHPRec = 1000000
MinHPRestRec = 0
MaxHPRestRec = 1000000
MinMPRec = -1000000
MaxMPRec = 1000000
MinMPRestRec = 0
MaxMPRestRec = 1000000
MinSPRec = 0
MaxSPRec = 1000000
MinSPRestRec = 0
MaxSPRestRec = 1000000
MinDPRec = 0
MaxDPRec = 1000000
MinDPRestRec = 0
MaxDPRestRec = 1000000
MinHPPer = -10000
MaxHPPer = 10000
MinMPPer = 0
MaxMPPer = 10000
MinDefPer = -10000
MaxDefPer = 10000
MinMDefPer = -10000
MaxMDefPer = 10000
MinMHurt = -10000
MaxMHurt = 10000
MinAHurt = -10000
MaxAHurt = 10000
MinSkPointUse = -500
--endregion

---Unity Layer
Layer = {
    default = 0,
    TransparentFX = 1,
    IgnoreRaycast = 2,
    Water = 4,
    UI = 5,
    Boundry = 8,
    Unit = 11,
    HUD = 12,
    UIModel = 13,
    UIModelLight = 14,
    Boss = 16,
    FX = 17,
    PlayerSelf = 18,
    Player = 19,
    NPC = 20,
    RTMModel = 21,
    RTMInvisible = 22,
    PostProcessing = 23,
    SceneGround = 24,
    SceneObstacle = 25,
    ObstacleSticker = 26,
    ObstacleStckerD = 27,
    SceneReflection = 28,
    SceneSmallObjects = 29,
    FloorTrigger = 30,
    SceneObstacle_IgnoreCameraCollider = 31,
}

UniformIdentifier = 1995
MaleUniformItemId = 67004 ---男制服物品編號
FemaleUniformItemId = 67504 ---女制服物品編號

--region 制服預設顏色
Uniform_DefaultIndex_ClothColorA = 17
Uniform_DefaultIndex_ClothColorB = 54
Uniform_DefaultIndex_ClothColorC = 115
Uniform_DefaultIndex_ClothColorD = 136
Uniform_DefaultIndex_Weapon = 194

UniformAttacker_DefaultIndex_ClothColorA = 18
UniformAttacker_DefaultIndex_ClothColorB = 55
UniformAttacker_DefaultIndex_ClothColorC = 120
UniformAttacker_DefaultIndex_ClothColorD = 124
UniformAttacker_DefaultIndex_Weapon = 161

UniformDefender_DefaultIndex_ClothColorA = 18
UniformDefender_DefaultIndex_ClothColorB = 55
UniformDefender_DefaultIndex_ClothColorC = 120
UniformDefender_DefaultIndex_ClothColorD = 156
UniformDefender_DefaultIndex_Weapon = 193
--endregion

LODLevel = 4

--region 數值類物品ID
    BuddhismID  = 95001--佛點數
    TaoistID = 95002--道點數
    DvilID = 95003--魔點數
    MoneyID = 95004--金錢
    SkillPointsID = 95005--武點
    ReputationID = 95006--聲譽
    ExpID = 95007--經驗值
    HEMCoinID = 95010--黃易幣
    DiamondID = 95011--時空幣
	MaxStaticPropKind = 250--永丹種類上限數量
	BStaticMedicine = 67001--永丹起始ID
--endregion

--region 活動相關
TITLE_STR_FIRST_NUM = 550151
--endregion

---@class ELoadingOpenType Loading 開啟類型
ELoadingOpenType = {
    ---無
    None = 0,
    ---預設
    Defult = 1,
    ---打字機
    CaptionTheater = 2,
    ---特效
    Effect = 3,
    ---CG
    UseCG = 4
}

---@class ELoadingDefultType Loading 預設類型
ELoadingDefultType = {
    ---共用
    Common = 0,
    ---下載
    Download = 1,
    ---換場
    ChangeScene = 2,
}

---偵測地板高度射線長
MaxFloorRayCastLength = 100

---@class MoveSpeedFromServer ---角色移動速度定義表 單位:公分/0.5sec ※Server給的
MoveSpeedFromServer = {
    [0] = 0,
    [1] = 25,
    [2] = 50,
    [3] = 75,
    [4] = 100,
    [5] = 125,
    [6] = 150,
    [7] = 175,
    [8] = 200,
    [9] = 225,
    [10] = 250,
    [11] = 275,
    [12] = 300,
    [13] = 325,
    [14] = 350,
    [15] = 375,
    [16] = 400,
    [17] = 425,
    [18] = 450,
    [19] = 475,
    [20] = 500,
    [21] = 525,
    [22] = 550,
    [23] = 575,
    [24] = 600,
    [25] = 625,
    [26] = 650,
    [27] = 675,
    [28] = 700,
    [29] = 725,
    [30] = 750,
    [31] = 775,
    [32] = 800,
    [33] = 825,
    [34] = 850,
    [35] = 875,
    [36] = 900,
    [37] = 925,
    [38] = 950,
    [39] = 975,
    [40] = 1000, --對是七千沒錯 --白癡特規不需要了吧
    [41] = 1025,
    [42] = 1050,
    [43] = 1075,
    [44] = 1100,
    [45] = 1125,
    [46] = 1150,
    [47] = 1175,
    [48] = 1200,
    [49] = 1225,
    [50] = 1250,
    [51] = 1275,
    [52] = 1300,
    [53] = 1325,
    [54] = 1350,
    [55] = 1375,
    [56] = 1400,
    [57] = 1425,
    [58] = 1450,
    [59] = 1475,
    [60] = 1500,
    [61] = 1525,
    [62] = 1550,
    [63] = 1575,
    [64] = 1600,
    [65] = 1625,
    [66] = 1650,
    [67] = 1675,
    [68] = 1700,
    [69] = 1725
}

---@class ECurrencyType 貨幣種類
ECurrencyType = {
    --- 無
    None = 0,
    --- 遊戲幣
    Money = 1,
    --- 黃易幣
    HEMCoin = 2,
    --- 時空幣(鑽石)
    Diamond = 3,
    --- 福緣
    Fortune = 4,
    --- 武點
    WugongPoint = 5,
    --- 威望值(善惡值)
    Prestige = 6,
    --- 聲譽
    Reputation = 7,
    --- 量子源
    QuantumSource = 8,
    --- 物品
    Item = 9,
    --- 記數永標
    CountFlag = 10,
    --- 負重
    Load = 11,
}

---ItemID 與貨幣種類的對照 [新增貨幣群組時要新增於此]
EItemIDResourceTable =
{
    ---物品表定義 用來顯示金錢的道具ID 95004
    [95004] = ECurrencyType.Money,
    ---物品表定義 用來顯示黃易幣的道具ID 95010
    [95010] = ECurrencyType.HEMCoin,
    ---物品表定義 用來顯示時空幣(鑽石)的道具ID 95011
    [95011] = ECurrencyType.Diamond,
    ---物品表定義 用來顯示福緣的道具ID 95038
    [95038] = ECurrencyType.Fortune,
    ---物品表定義 用來顯示武點的道具ID 95005
    [95005] = ECurrencyType.WugongPoint,
    ---物品表定義 用來顯示威望值(善惡值)的道具ID 95047
    [95047] = ECurrencyType.Prestige,
    ---物品表定義 用來顯示聲譽的道具ID 95006
    [95006] = ECurrencyType.Reputation,
    ---物品表定義 用來顯示量子源的道具ID 95052
    [95052] = ECurrencyType.QuantumSource,
    ---物品表定義 用來顯示記數永標的道具ID 95054
    [95054] = ECurrencyType.CountFlag,
}


---@class ECurrencyGroupType 貨幣群組
ECurrencyGroupType = {
    --- 無( 不使用的項目 如負重)
    None = 0,
    --- 貨幣群組
    CurrencyGroup = 1,
    --- 資源群組
    ResourceGroup = 2,
    --- 活動群組
    ActivityGroup = 3,
    --- 其他群組
    OtherGroup = 4,
    ---Max 給for迴圈使用
    Max = 5,
}

--region Buff

---@class 特殊Buff
ESpecialBuffID = {
    ---清溪流泉 BUFF
    FreeTalking = 4572,
    ---新手保護
    RookieProtect = 4585,
    ---PK保護
    PKProtect = 4586,
}


---@class EProtocolBuffEffectType 協定10-15 10-16 10-17 造成的效果種類 [0.狀態 1.扣血 2.補血 3.扣內 4.補內 5.扣真 6.補真 7.扣罡 8.補罡  ]
EProtocolBuffEffectType =
{
    State = 0,
    ReduceHP = 1,
    AddHP = 2,
    ReducceMP = 3,
    AddMP = 4,
    ReduceSP = 5,
    AddSP = 6,
    ReduceDP = 7,
    AddDP = 8,
}

---Buff被動種類 5:核心技累積buff 6:護盾累積buff
EPassiveType = 
{
    Magnet = 5,
    Shield = 6,

}
--endregion

--endregion

--region 畫質

---@class EDeviceLevel 裝置等級 為配合系統設置從0開始
EDeviceLevel =
{
    Low = 0,
    Medium = 1,
    High = 2,
}

---@class ESimpleSetting 快速設定
ESimpleSetting =
{
    FuncFirst = 0,
    Balance = 1,
    Quality = 2,
    User = 3
}

---@class EQuality 畫質設定品階 為配合系統設置從0開始
EQuality =
{
    Medium = 0,
    High = 1,
    VeryHigh = 2,
    Ultra = 3
}

---@class EQualityName 畫質設定名稱
EQualityName =
{
    [EQuality.Medium] = 56,
    [EQuality.High] = 57,
    [EQuality.VeryHigh] = 58,
    [EQuality.Ultra] = "極致"
}

---@class EShadowDistanceName 陰影距離設定名稱
EShadowDistanceName =
{
    [EQuality.Medium] = 46, ---近
    [EQuality.High] = 47, ---一般
    [EQuality.VeryHigh] = 48, ---遠
}

---@class EShadowDistancePC 陰影距離PC
EShadowDistancePC =
{
    [EQuality.Medium] = 32,
    [EQuality.High] = 48,
    [EQuality.VeryHigh] = 64,
}

---@class EShadowDistanceDevice 陰影距離裝置
EShadowDistanceDevice =
{
    [EQuality.Medium] = 15,
    [EQuality.High] = 20,
    [EQuality.VeryHigh] = 25,
}

---@class EVsync 垂直同步
EVsync =
{
    --Fps20 = 20,
    [EQuality.Medium] = 30,
    [EQuality.High] = 60,
    [EQuality.VeryHigh] = 120
}

---@class EResolutionName 螢幕解析度名稱
EResolutionName = 
{
    [EQuality.Medium] = 56,
    [EQuality.High] = 57,
    [EQuality.VeryHigh] = 58,
    [EQuality.Ultra] = 67
}

---@class EResolutionValue 螢幕解析度高(現僅用來計算縮放比)
EResolutionValue = 
{
    [EQuality.Medium] = 540,
    [EQuality.High] = 720, -- 4/3
    [EQuality.VeryHigh] = 900, --5/4
    [EQuality.Ultra] = 1080 --6/5
}

--endregion

--region 戰鬥文字相關

---Tick爆擊增加索引
BATTLETEXT_TICK_CRIT_ADD = 50

---@class EBattleTextType HUD特效文字類型
EBattleTextType = {
    Default = 0, --預設
    Hurt = 1,        --受傷
    MakeHP = 2,        --補血
    State = 3,        --狀態


    Normal = 4,             --一般
    NotMP = 5,              --沒魔
    Critical = 6,           --爆擊

    MakeMP = 7,             --補魔
    MakeSP = 8,             --補真

    Assimilate = 9,         --吸收傷害
    Miss = 10,               --未命中
    Dodge = 11,              --迴避
    ResistNegative = 12,     --抵抗負面
    MakeDP = 13,             --補罡
    PetNormal = 14,          --寵物一般傷害
    PetCritical = 15,        --寵物爆擊傷害
    BloodShield = 16,        --血盾
    EnegyShield = 17,        --罡盾
    Dot = 18,                --DOT 流血, 中毒 都用持續傷害來做
    MagAtk = 19,             --內功傷害
    MagCritical = 20,        --內功爆擊

    HeroChoose = 101,    --選邊站頭圖
    --2022.04.29 Add by KK, 加入敵方的特殊顯示
    Enemy_Assimilate = 109,         --吸收傷害
    Enemy_Miss = 110,               --未命中
    Enemy_Dodge = 111,              --迴避
    Enemy_ResistNegative = 112,     --抵抗負面
    Enemy_BloodShield = 116,        --血盾
    Enemy_EnegyShield = 117,        --罡盾
    ChangeWugong = 200,             --換外功
    UseWugong = 201,                --使用武功
    DebuffWord = 202,               --Debuff狀態剩餘時間
    BuffBigWord = 203,              --獲得buff大字

    --Tick用戰鬥文字
    TickWord_1 = 301,
    TickWord_2 = 302,
    TickWord_3 = 303,
    TickWord_4 = 304,
    TickWord_5 = 305,
    TickWord_6 = 306,
    TickWord_7 = 307,
    TickWord_8 = 308,
    TickWord_9 = 309,
    TickWord_10 = 310,

    --Tick用戰鬥文字 爆擊
    TickWord_Crit_1 = 351,
    TickWord_Crit_2 = 352,
    TickWord_Crit_3 = 353,
    TickWord_Crit_4 = 354,
    TickWord_Crit_5 = 355,
    TickWord_Crit_6 = 356,
    TickWord_Crit_7 = 357,
    TickWord_Crit_8 = 358,
    TickWord_Crit_9 = 359,
    TickWord_Crit_10 = 360,

    --Tick用戰鬥文字內功
    TickMagWord_1 = 401,
    TickMagWord_2 = 402,
    TickMagWord_3 = 403,
    TickMagWord_4 = 404,
    TickMagWord_5 = 405,
    TickMagWord_6 = 406,
    TickMagWord_7 = 407,
    TickMagWord_8 = 408,
    TickMagWord_9 = 409,
    TickMagWord_10 = 410,

    --Tick用戰鬥文字內功 爆擊
    TickMagWord_Crit_1 = 451,
    TickMagWord_Crit_2 = 452,
    TickMagWord_Crit_3 = 453,
    TickMagWord_Crit_4 = 454,
    TickMagWord_Crit_5 = 455,
    TickMagWord_Crit_6 = 456,
    TickMagWord_Crit_7 = 457,
    TickMagWord_Crit_8 = 458,
    TickMagWord_Crit_9 = 459,
    TickMagWord_Crit_10 = 460,

}

EBattleTextChineseName = {
    [EBattleTextType.Default] = "預設",
    [EBattleTextType.Hurt] = "受傷",
    [EBattleTextType.MakeHP] = "補血",
    [EBattleTextType.State] = "狀態",


    [EBattleTextType.Normal] = "一般",
    [EBattleTextType.NotMP] = "沒魔",
    [EBattleTextType.Critical] = "爆擊"  ,

    [EBattleTextType.MakeMP] = "補魔",
    [EBattleTextType.MakeSP] = "補真",

    [EBattleTextType.Assimilate] = "吸收傷害",
    [EBattleTextType.Miss] = "未命中",
    [EBattleTextType.Dodge] = "迴避",
    [EBattleTextType.ResistNegative] = "抵抗負面",
    [EBattleTextType.MakeDP] = "補罡",
    [EBattleTextType.PetNormal] = "寵物一般傷害",
    [EBattleTextType.PetCritical] = "寵物爆擊傷害",
    [EBattleTextType.BloodShield] = "血盾",
    [EBattleTextType.EnegyShield] = "罡盾",
    [EBattleTextType.Dot] = "DOT 流血, 中毒",-- 都用持續傷害來做
    [EBattleTextType.MagAtk] = "內功傷害",
    [EBattleTextType.MagCritical] = "內功爆擊",

    [EBattleTextType.HeroChoose] = "選邊站頭圖",
    --2022.04.29 Add by KK, 加入敵方的特殊顯示
    [EBattleTextType.Enemy_Assimilate] = "吸收傷害(敵)",
    [EBattleTextType.Enemy_Miss] = "未命中(敵)",
    [EBattleTextType.Enemy_Dodge] = "迴避(敵)",
    [EBattleTextType.Enemy_ResistNegative] = "抵抗負面(敵)",
    [EBattleTextType.Enemy_BloodShield] = "血盾(敵)",
    [EBattleTextType.Enemy_EnegyShield] = "罡盾(敵)",
    [EBattleTextType.ChangeWugong] = "更換外功",
    [EBattleTextType.UseWugong] = "使用絕學",
    [EBattleTextType.DebuffWord] = "Debuff的字",
    [EBattleTextType.BuffBigWord] = "Buff的大字",

    --Tick用戰鬥文字
    [EBattleTextType.TickWord_1] = "Tick戰鬥字 1",
    [EBattleTextType.TickWord_2] = "Tick戰鬥字 2",
    [EBattleTextType.TickWord_3] = "Tick戰鬥字 3",
    [EBattleTextType.TickWord_4] = "Tick戰鬥字 4",
    [EBattleTextType.TickWord_5] = "Tick戰鬥字 5",
    --Tick用戰鬥文字 爆擊
    [EBattleTextType.TickWord_Crit_1] = "Tick戰鬥字爆擊 1",
    [EBattleTextType.TickWord_Crit_2] = "Tick戰鬥字爆擊 2",
    [EBattleTextType.TickWord_Crit_3] = "Tick戰鬥字爆擊 3",
    [EBattleTextType.TickWord_Crit_4] = "Tick戰鬥字爆擊 4",
    [EBattleTextType.TickWord_Crit_5] = "Tick戰鬥字爆擊 5",

    --Tick用內功戰鬥文字
    [EBattleTextType.TickMagWord_1] = "Tick內功戰鬥字 1",
    [EBattleTextType.TickMagWord_2] = "Tick內功戰鬥字 2",
    [EBattleTextType.TickMagWord_3] = "Tick內功戰鬥字 3",
    [EBattleTextType.TickMagWord_4] = "Tick內功戰鬥字 4",
    [EBattleTextType.TickMagWord_5] = "Tick內功戰鬥字 5",
    --Tick用內功戰鬥文字 爆擊
    [EBattleTextType.TickMagWord_Crit_1] = "Tick內功戰鬥字爆擊 1",
    [EBattleTextType.TickMagWord_Crit_2] = "Tick內功戰鬥字爆擊 2",
    [EBattleTextType.TickMagWord_Crit_3] = "Tick內功戰鬥字爆擊 3",
    [EBattleTextType.TickMagWord_Crit_4] = "Tick內功戰鬥字爆擊 4",
    [EBattleTextType.TickMagWord_Crit_5] = "Tick內功戰鬥字爆擊 5",

}

---受擊種類(一般來說由S端傳來)
---@class EValueKind
EValueKind =
{
    Miss = 0,
    ---扣血
    LessHp = 1,
    ---加血
    AddHp = 2,
    ---給狀態
    State = 3,
    ---爆擊扣血
    CriLessHp = 4,
    ---迴避/沒命中
    Dodge = 5,


    --只好寫在C端了
    ---內功一般傷害
    MagNormal = 101,
    ---內功爆擊
    MagCrit = 102,
    ---吸收數值(C端自訂)
    Assimilate = 103,
    ---抵擋(C端自訂)
    ResistNegative = 104,
    ---血盾(C端自訂)
    BloodShield = 105,
    ---罡盾(C端自訂)
    EnegyShield = 106,
}

---協定Tick效果種類
---效果種類 1.回復hp 2.回復mp 3.回復sp 4.回罡氣 11減少hp 12減少mp 13 減少sp 14.扣罡氣  21.吸HP 22.吸MP 31.引爆HP 32.引爆MP 33.反彈傷害HP
---@class ETickValueKind
ETickValueKind =
{
    [1] = EBattleTextType.MakeHP,
    [2] = EBattleTextType.MakeMP,
    [3] = EBattleTextType.MakeSP,
    [4] = EBattleTextType.MakeDP,
    [11] = EBattleTextType.Hurt,
    [21] = EBattleTextType.MakeHP,
    [22] = EBattleTextType.MakeMP,
}

---協定Tick效果種類
--效果種類 1.回復hp 2.回復mp 3.回復sp 4.回罡氣 11減少hp 12減少mp 13 減少sp 14.扣罡氣 15爆擊傷害 16.MISS  21.吸HP 22.吸MP 31.引爆HP 32.引爆MP 33.反彈傷害HP
EPetValueKind =
{
    [1] = EBattleTextType.MakeHP,
    [2] = EBattleTextType.MakeMP,
    [3] = EBattleTextType.MakeSP,
    [4] = EBattleTextType.MakeDP,
    [11] = EBattleTextType.PetNormal,
    [15] = EBattleTextType.PetCritical,
    [16] = EBattleTextType.Miss,
    [21] = EBattleTextType.MakeHP,
    [22] = EBattleTextType.MakeMP,
}


--endregion

--region 相機狀態
ECameraType = CinemachineMgr.ECameraType
ECameraTypeChange = {
    --有必要再用這個，提供字串存檔轉C#Enum
    [tostring(ECameraType.Free)] = ECameraType.Free,
    [tostring(ECameraType.Fix)] = ECameraType.Fix,
}
--endregion

---按鈕按下預設的音效 ID
OnCilckDefultAudioID = 31001

--region RectTransform
---RectTransform 對應的錨點
---@class EAnchorPresets
EAnchorPresets =
{
    TopLeft = { Min = Vector2( 0, 1 ), Max = Vector2( 0, 1 ) },
    TopCenter = { Min = Vector2( 0.5, 1 ), Max = Vector2( 0.5, 1 ) },
    TopRight = { Min = Vector2( 1, 1 ), Max = Vector2( 1, 1 ) },

    MiddleLeft = { Min = Vector2( 0, 0.5 ), Max = Vector2( 0, 0.5 ) },
    MiddleCenter = { Min = Vector2( 0.5, 0.5 ), Max = Vector2( 0.5, 0.5 ) },
    MiddleRight = { Min = Vector2( 1, 0.5 ), Max = Vector2( 1, 0.5 ) },

    BottomLeft = { Min = Vector2( 0, 0 ), Max = Vector2( 0, 0 ) },
    BottonCenter = { Min = Vector2( 0.5, 0 ), Max = Vector2( 0.5, 0 ) },
    BottomRight = { Min = Vector2( 1, 0 ), Max = Vector2( 1, 0 ) },

    VertStretchLeft = { Min = Vector2( 0, 0 ), Max = Vector2( 0, 1 ) },
    VertStretchCenter = { Min = Vector2( 0.5, 0 ), Max = Vector2( 0.5, 1 ) },
    VertStretchRight = { Min = Vector2( 1, 0 ), Max = Vector2( 1, 1 ) },

    HorStretchTop = { Min = Vector2( 0, 1 ), Max = Vector2( 1, 1 ) },
    HorStretchMiddle = { Min = Vector2( 0, 0.5 ), Max = Vector2( 1, 0.5 ) },
    HorStretchBottom = { Min = Vector2( 0, 0 ), Max = Vector2( 1, 0 ) },

    StretchAll = { Min = Vector2( 0, 0 ), Max = Vector2( 1, 1 ) }
}

---@class PivotPresets
EPivotPresets =
{
    TopLeft = Vector2( 0, 1 ),
    TopCenter = Vector2( 0.5, 1 ),
    TopRight = Vector2( 1, 1 ),

    MiddleLeft = Vector2( 0, 0.5 ),
    MiddleCenter = Vector2( 0.5, 0.5 ),
    MiddleRight = Vector2( 1, 0.5 ),

    BottomLeft = Vector2( 0, 0 ),
    BottomCenter = Vector2( 0.5, 0 ),
    BottomRight = Vector2( 1, 0 )
}
--endregion

--region ShadowCastingMode
EShadowCastingMode = UnityEngine.Rendering.ShadowCastingMode
--endregion

--region Hint相關
---Hint種類
EHintType =
{
    --- 物品Hint
    ItemHint = 1,
    --- 技能Hint
    SkillHint = 2,
    --- 增益Hint
    BuffHint = 3,
    --- 寵物Hint
    PetHint = 4,
    --- 資源列
    ResourceBar = 5,
    --- 通用Hint
    CommonHint = 6
}

---單一 Hint區塊 種類
EHintBoxType =
{
    --- 數值名 + 數值
    KindAndValue = 1,
    --- 純文字說明
    NormalText = 2,
    --- 圖片 + 圖片名
    IconAndValue = 3,
    --- 圖片 + 圖片名 + 數值(可隱藏)
    IconAndRate = 4,
    --- 區塊標題旁的問號按鈕
    ShowRate = 5,
    --- 區塊標題旁的額外圖示
    TitleIcon = 6
}

---Hint區塊中的內容類型
EHintBoxContentType = {
    --- 圖片資料
    IconData = 1,
    --- Hint全部內文資料
    HintContentData = 2
}
--endregion


---@class EPalyerData 玩家資料Table
EPalyerData =
{
	---玩家的基礎數值 (1：氣血( HP )，2：內力( MP )，3：真氣( SP )，4：罡氣( DP ))，5：磁能( MagEner ))
	BaseValues = 1,
	---玩家的角色屬性 (1：力量( STR )，2：體魄( CON )，3：精神( MEN )，4：敏捷( AGI )，5：真元( INT ))
	Attributes = 2,
    ---玩家標記 (1：永標，2：動標，3：計數永標)
    Flags = 3,
    --- 武功資料
    Wugong = 4,
    ---玩家的副屬性 (除了五大屬性以外的數值 攻擊屬性 攻擊增益 防禦屬性 其他屬性 其他增益)
    SubAttribute = 5,
    ---稱號資料
    Title = 6,
}

---@class EBaseValues 角色的基礎數值(1：氣血( HP )，2：內力( MP )，3：真氣( SP )，4：罡氣( DP ))，5：磁能( MagEner ))
EBaseValues =
{
    ---氣血
    HP = 1,
    ---內力
    MP = 2,
    ---真氣
    SP = 3,
    ---罡氣
    DP = 4,
    ---磁能
    MagEner = 11,
    ---Max
    MAX = 6,
}

---@class EBaseAttributes 角色的基礎屬性(1：力量( STR )，2：體魄( CON )，3：精神( MEN )，4：敏捷( AGI )，5：真元( INT ))
EBaseAttributes =
{
    ---力量
    STR = 1,
    ---體魄
    CON = 2,
    ---精神
    MEN = 3,
    ---敏捷
    AGI = 4,
    ---真元
    INT = 5,
}

--- 基礎屬性的 TextID (1：力量( STR )，2：體魄( CON )，3：精神( MEN )，4：敏捷( AGI )，5：真元( INT ))
TableBaseAttributesTextID =
{
    ---力量
    [EBaseAttributes.STR] = 70001,
    ---體魄
    [EBaseAttributes.CON] = 70003,
    ---精神
    [EBaseAttributes.MEN] = 70005,
    ---敏捷
    [EBaseAttributes.AGI] = 70002,
    ---真元
    [EBaseAttributes.INT] = 70004,
}

---@class ESubAttributesType 角色非基礎屬性 以外的屬性的分類
ESubAttributesType =
{
    ---攻擊屬性
    ATK_ATTRIBUTE = 1,
    ---攻擊增益
    ATK_GAIN = 2,
    ---防禦屬性
    DEF_ATRIBUTE = 3,
    ---其他屬性
    OTHER_ATTRIBUTE = 4,
    ---其他增益
    OTHER_GAIN = 5,
    ---最大項目
    MAX =6
}

---@class ERoleSubAttribute 角色非基礎屬性 以外的屬性
ERoleSubAttribute =
{
    --攻擊屬性類
    ---外功攻擊 enum Index =1
    ATK =1,
    ---內功攻擊 enum Index =2
    SKILLATK = 2,
    ---爆擊率 enum Index =3
    CRITICALRATE = 3,
    ---爆擊傷害 enum Index =4
    CRITICAL = 4,
    ---攻擊速度 enum Index =5
    ATKSPEED = 5,
    ---命中 enum Index =6
    HIT= 6,
    ---最大傷害 enum Index =7
    MAXHURT=7,
    ---真武傷害 enum Index =8
    DIRECTDAMAGE = 8,
    ---經脈攻擊 enum Index =9
    SPECIALDAMAGE = 9,
    ---擊氣冷卻 enum Index =10
    COLLECT_COOLDOWN = 10,
    ---內功冷卻 enum Index =11
    SKILL_COOLDOWN = 11,

    --攻擊增益類
    ---外功攻擊倍率 enum Index =12
    ATKRATE =12,
    ---外功傷害倍率 enum Index =13
    ATK_DAMAGE_RATE = 13,
    ---內功攻擊倍率 enum Index =14
    MAGICATKRATE =14,
    ---內功傷害倍率 enum Index =15
    MAGIC_ATK_DAMAGE_RATE = 15,
    ---刀法傷害增加常數 enum Index =16
    BLADE_DAMAGE = 16,
    ---劍法傷害增加常數 enum Index =17
    SWORD_DAMAGE = 17,
    ---拳法傷害增加常數 enum Index =18
    FIGHT_DAMAGE = 18,
    ---槍法傷害增加常數 enum Index =19
    SPEAR_DAMAGE = 19,
    ---刀法傷害增加常數 enum Index =20
    STICK_DAMAGE = 20,
    ---刀法傷害倍率 enum Index =21
    BLADE = 21,
    ---劍法傷害倍率 enum Index =22
    SWORD = 22,
    ---拳法傷害倍率 enum Index =23
    FIGHT = 23,
    ---槍法傷害倍率 enum Index =24
    SPEAR = 24,
    ---棍法傷害倍率 enum Index =25
    STICK = 25,
    ---總傷害倍率 enum Index = 26
    ALL_DAMAGE_RATE =26,

    --防禦屬性類
    ---外功防禦 enum Index = 27
    DEF =27,
    ---外功防禦倍率加乘 enum Index = 28
    DEF_PROPORTION_ADD = 28,
    ---外功化傷 enum Index = 29
    ATK_HURT_REDUCE = 29,
    ---外功減傷 enum Index = 30
    ATKDEC = 30,
    ---內功防禦 enum Index = 31
    SKILLDEF =31,
    ---內功防禦倍率加乘 enum Index = 32
    SKILLDEF_PROPORTION_ADD = 32,
    ---內功化傷 enum Index = 33
    SKILL_ATK_HURT_REDUCE = 33,
    ---內功減傷 enum Index = 34
    MAGIC_ATK_DEC = 34,
    ---抗暴率 enum Index =35
    ANTI_CRITICALRATE = 35,
    ---抗暴傷 enum Index =36
    ANTI_CRITICAL = 36,
    ---總減傷 enum Index = 37
    ALL_DANAGE_REDUCE_RATE = 37,
    ---化勁 enum Index = 38
    DEPELECTION = 38,
    ---卸勁率 enum Index = 39
    Unloading = 39,
    ---迴避 enum Index = 40
    DODGE = 40,
    ---經脈防禦 enum Index = 41
    SPECIALDEFENCE = 41,
    ---PK減傷 enum Index = 42
    PK_DEC_DAMAGE = 42,

    --其他屬性
    ---自動回血 enum Index = 43
    RETURN_HP = 43,
    ---打坐回血 enum Index = 44
    MEDITATE_HP = 44,
    ---自動回內 enum Index = 45
    RETURN_MP =45,
    ---打坐回內 enum Index = 46
    MEDITATE_MP = 46,
    ---自動回真 enum Index = 47
    RETURN_SP =47,
    ---打坐回真 enum Index = 48
    MEDITATE_SP = 48,
    ---自動回血倍率 enum Index = 49
    HP_REC_RATE = 49,
    ---打坐回血倍率 enum Index = 50
    HP_REC_MEDITATE_RATE = 50,
    ---自動回內倍率 enum Index = 51
    MP_REC_RATE = 51,
    ---打坐回內倍率 enum Index = 52
    MP_REC_MEDITATE_RATE = 52,

    --其他增益
    ---氣血倍率 enum Index = 53
    HPRATE = 53,
    ---內力倍率 enum Index = 54
    MPRATE = 54,
    ---真氣倍率 enum Index = 55
    SPRATE = 55,
    ---行走速度 enum Index = 56
    WALKSPEED = 56,
    ---騎乘速度  enum Index = 57
    RIDESPEED = 57,
    ---移動速度 enum Index = 58
    MOVESPEED = 58,
    ---經驗獲得 enum Index = 59
    EXPGAIN = 59,
    ---武點獲得 enum Index = 60
    SKILLPOINTGAIN = 60,
    ---掉落率 enum Index = 61
    ITEMDROPRATE =61,
    ---物品售價 enum Index = 62
    SELLPRICE = 62,
    ---武點消耗 enum Index = 63
    USE_SKILL_POINT_RATE = 63,
    ---Max
    MAX = 64,
}

---@class EFlags 標記種類(1：永標( Static )，2：動標( Move )，3：計數永標( StaticNum )、4:時光機艙室效果記數永標(TimeMachineEffect))
EFlags =
{
    ---永標
    Static = 1,
    ---動標
    Move = 2,
    ---計數永標
    StaticNum = 3,
    ---時光機艙室效果記數永標
    TimeMachineEffect = 4,
}

---屬性最大點數
MaxAttributePoint = 100

---預設最大武點上限 ( 後續會因為丹藥...等改變 )
DEFAULT_MAX_SKILL_POINT = 4200000000

---威望值(善惡值)分界
PRESTIGE_BOUNDARY = 30000

--region 屬性效果類型
---@class EAttributeType 屬性效果類型 ( 原 enum EAttributes )
EAttributeType = {}
---加-力量
EAttributeType.AddSTR = 1
---減-力量
EAttributeType.SubSTR = 2
---加-敏捷
EAttributeType.AddAGI = 3
---減-敏捷
EAttributeType.SubAGI = 4
---加-體魄
EAttributeType.AddCON = 5
---減-體魄
EAttributeType.SubCON = 6
---加-真元
EAttributeType.AddINT = 7
---減-真元
EAttributeType.SubINT = 8
---加-精神
EAttributeType.AddMEN = 9
---減-精神
EAttributeType.SubMEN = 10
---加-負重 (2.0不使用 維持 避免排序錯誤)
EAttributeType.AddLoad = 11
---減-負重 (2.0不使用 維持 避免排序錯誤)
EAttributeType.SubLoad = 12
---加-外功攻擊
EAttributeType.AddATK = 13
---減-外功攻擊
EAttributeType.SubATK = 14
---加-外功防禦
EAttributeType.AddDEF = 15
---減-外功防禦
EAttributeType.SubDEF = 16
---加-內功攻擊
EAttributeType.AddSkillATK = 17
---減-內功攻擊
EAttributeType.SubSkillATK = 18
---加-內功防禦
EAttributeType.AddSkillDEF = 19
---減-內功防禦
EAttributeType.SubSkillDEF = 20
---加-最大傷害
EAttributeType.AddMaxHurt = 21
---減-最大傷害
EAttributeType.SubMaxHurt = 22
---加-爆擊率
EAttributeType.AddCriticalRate = 23
---減-爆擊率
EAttributeType.SubCriticalRate = 24
---加-爆擊傷害
EAttributeType.AddCritical = 25
---減-爆擊傷害
EAttributeType.SubCritical = 26
---加-卸勁率
EAttributeType.AddUnloading = 27
---減-卸勁率
EAttributeType.SubUnloading = 28
---加-命中
EAttributeType.AddHit = 29
---減-命中
EAttributeType.SubHit = 30
---加-迴避
EAttributeType.AddDodge = 31
---減-迴避
EAttributeType.SubDodge = 32
---加-氣血上限
EAttributeType.AddHP = 33
---減-氣血上限
EAttributeType.SubHP = 34
---加-自動回血
EAttributeType.AddReturnHP = 35
---減-自動回血
EAttributeType.SubReturnHP = 36
---加-打坐回血
EAttributeType.AddMeditateHP = 37
---減-打坐回血
EAttributeType.SubMeditateHP = 38
---加-氣血藥效果 (血藥效果)
EAttributeType.AddHPcure = 39
---減-氣血藥效果
EAttributeType.SubHPcure = 40
---加-內力上限 (內力)
EAttributeType.AddMP = 41
---減-內力上限
EAttributeType.SubMP = 42
---加-自動回內
EAttributeType.AddReturnMP = 43
---減-自動回內
EAttributeType.SubReturnMP = 44
---加-打坐回內
EAttributeType.AddMeditateMP = 45
---減-打坐回內
EAttributeType.SubMeditateMP = 46
---加-內力藥效果 (內藥效果)
EAttributeType.AddMPcure = 47
---減-內力藥效果
EAttributeType.SubMPcure = 48
---加-真氣上限 (真氣)
EAttributeType.AddSP = 49
---減-真氣上限
EAttributeType.SubSP = 50
---加-回真倍率 (自動回真)
EAttributeType.AddReturnSP = 51
---減-回真倍率
EAttributeType.SubReturnSP = 52
---加-打坐回真倍率 (打坐回真)
EAttributeType.AddMeditateSP = 53
---減-打坐回真倍率
EAttributeType.SubMeditateSP = 54
---加-罡氣上限
EAttributeType.AddDP = 55
---減-罡氣上限
EAttributeType.SubDP = 56
---加-罡氣回復
---EAttributeType.AddReturnDP                 = 57
---減-自動回罡
---EAttributeType.SubReturnDP                 = 58
---加-PK減傷
EAttributeType.AddPKDecDamage = 57
---減-PK減傷
EAttributeType.SubPKDecDamage = 58
---加-打坐回罡
---EAttributeType.AddMeditateDP               = 59
---減-打坐回罡
---EAttributeType.SubMeditateDP               = 60
---加-NPC減傷
EAttributeType.AddNPCDecDamage = 59
---減-NPC減傷
EAttributeType.SubNPCDecDamage = 60
---加-攻擊速度
EAttributeType.AddATKSpeed = 61
---減-攻擊速度
EAttributeType.SubATKSpeed = 62
---加-移動速度 (行走速度)
EAttributeType.AddMoveSpeed = 63
---減-移動速度
EAttributeType.SubMoveSpeed = 64
---加-回復速度 (打坐回血倍率)
EAttributeType.AddReturnSpeed = 65
---減-回復速度 (打坐回血倍率)
EAttributeType.SubReturnSpeed = 66
---加-傷害倍率
EAttributeType.AddHurtMagnification = 67
---減-傷害倍率
EAttributeType.SubHurtMagnification = 68
---加-減傷倍率 (總減傷)
EAttributeType.AddDecDamageMagnification = 69
---減-減傷倍率
EAttributeType.SubDecDamageMagnification = 70
---加-座騎速度 (騎乘速度)
EAttributeType.AddRideSpeed = 71
---減-座騎速度
EAttributeType.SubRideSpeed = 72
---加-經驗增加率 (經驗獲得)
EAttributeType.AddEXPRate = 73
---減-經驗增加率
EAttributeType.SubEXPRate = 74
---加-武點增加率 (武點獲得)
EAttributeType.AddSkillPointRate = 75
---減-武點增加率
EAttributeType.SubSkillPointRate = 76
---加-化勁
EAttributeType.AddDepletion = 77
---減-化勁
EAttributeType.SubDepletion = 78
---加-攻擊範圍 (串表 無效果 保留位置)
EAttributeType.AddATKRange = 79
---減-攻擊範圍 (串表 無效果 保留位置)
EAttributeType.SubATKRange = 80
---加-受到的治療量
EAttributeType.AddBeCure = 81
---減-受到的治療量
EAttributeType.SubBeCure = 82
---加-氣血上限倍率 (氣血)
EAttributeType.AddHPRate = 83
---減-氣血上限倍率
EAttributeType.SubHPRate = 84
---加-內力上限倍率 (內力)
EAttributeType.AddMPRate = 85
---減-內力上限倍率
EAttributeType.SubMPRate = 86
---加-自動回血倍率
EAttributeType.AddHPRecRate = 87
---減-自動回血倍率
EAttributeType.SubHPRecRate = 88
---加-自動回內倍率
EAttributeType.AddMPRecRate = 89
---減-自動回內倍率
EAttributeType.SubMPRecRate = 90
---加-刀法傷害倍率
EAttributeType.AddBlade = 91
---減-刀法傷害倍率
EAttributeType.SubBlade = 92
---加-劍法傷害倍率
EAttributeType.AddSword = 93
---減-劍法傷害倍率
EAttributeType.SubSword = 94
---加-拳法傷害倍率
EAttributeType.AddFight = 95
---減-拳法傷害倍率
EAttributeType.SubFight = 96
---加-槍法傷害倍率
EAttributeType.AddSpear = 97
---減-槍法傷害倍率
EAttributeType.SubSpear = 98
---加-棍法傷害倍率
EAttributeType.AddStick = 99
---減-棍法傷害倍率
EAttributeType.SubStick = 100
---加-外功攻擊倍率
EAttributeType.AddATKRate = 101
---減-外功攻擊倍率
EAttributeType.SubATKRate = 102
---加-外功防禦倍率
EAttributeType.AddDEFRate = 103
---減-外功防禦倍率
EAttributeType.SubDEFRate = 104
---加-內功防禦倍率
EAttributeType.AddSkillDEFRate = 105
---減-內功防禦倍率
EAttributeType.SubSkillDEFRate = 106
---加-冷卻時間倍率
EAttributeType.AddColdDownRate = 107
---減-冷卻時間倍率
EAttributeType.SubColdDownRate = 108
---加-氣血消耗倍率
EAttributeType.AddHPDepletionRate = 109
---減-氣血消耗倍率
EAttributeType.SubHPDepletionRate = 110
---加-內力消耗倍率
EAttributeType.AddMPDepletionRate = 111
---減-內力消耗倍率
EAttributeType.SubMPDepletionRate = 112
---加-真氣消耗倍率
EAttributeType.AddSPDepletionRate = 113
---減-真氣消耗倍率
EAttributeType.SubSPDepletionRate = 114
---加-打坐回內倍率
EAttributeType.AddMeditateMPRate = 115
---減-打坐回內倍率
EAttributeType.SubMeditateMPRate = 116
---加-內功攻擊倍率
EAttributeType.AddMagAtkRate = 117
---減-內功攻擊倍率
EAttributeType.SubMagAtkRate = 118
---加-集氣時間倍率 (2.0 廢棄此參數)
EAttributeType.AddAccumulateRate = 119
---減-集氣時間倍率 (2.0 廢棄此參數)
EAttributeType.SubAccumulateRate = 120
---加-內功冷卻時間倍率
EAttributeType.AddMagColdRate = 121
---減-內功冷卻時間倍率
EAttributeType.SubMagColdRate = 122
---加-真氣上限倍率
EAttributeType.AddSPRate = 123
---減-真氣上限倍率
EAttributeType.SubSPRate = 124
---加-攻擊速度倍率
EAttributeType.AddASpdRate = 125
---減-攻擊速度倍率
EAttributeType.SubASpdRate = 126
---加-移動速度倍率 (行走速度)
EAttributeType.AddMSpdRate = 127
---減-移動速度倍率
EAttributeType.SubMSpdRate = 128
---加-坐騎速度倍率
EAttributeType.AddRSpdRate = 129
---減-坐騎速度倍率
EAttributeType.SubRSpdRate = 130
---加-耐久度倍率 (武器維護)
EAttributeType.AddEnduranceRate = 131
---減-耐久度倍率
EAttributeType.SubEnduranceRate = 132
---加-物品延遲 (物品冷卻)
EAttributeType.AddItemUseDelay = 133
---減-物品延遲
EAttributeType.SubItemUseDelay = 134
---加-打坐武點倍率
EAttributeType.AddSitSkPointRate = 135
---減-打坐武點倍率
EAttributeType.SubSitSkPointRate = 136
---加-武點消耗倍率
EAttributeType.AddUseSkPointRate = 137
---減-武點消耗倍率
EAttributeType.SubUseSkPointRate = 138
---加-外攻增傷倍率 (外功傷害倍率)
EAttributeType.AddAtkIncHurtRate = 139
---減-外攻增傷倍率
EAttributeType.SubAtkIncHurtRate = 140
---加-內攻增傷倍率 (內功傷害)
EAttributeType.AddMagIncHurtRate = 141
---減-內攻增傷倍率
EAttributeType.SubMagIncHurtRate = 142
---加-外攻減傷倍率
EAttributeType.AddAtkDecHurtRate = 143
---減-外攻減傷倍率
EAttributeType.SubAtkDecHurtRate = 144
---加-內攻減傷倍率
EAttributeType.AddMagDecHurtRate = 145
---減-內攻減傷倍率
EAttributeType.SubMagDecHurtRate = 146
---加-真武傷害
EAttributeType.AddDirectDamage = 147
---減-真武傷害
EAttributeType.SubDirectDamage = 148
---加-掉落率
EAttributeType.AddItemDropRate = 149
---減-掉落率
EAttributeType.SubItemDropRate = 150
---加-物品賣價
EAttributeType.AddSellPrice = 151
---減-物品賣價
EAttributeType.SubSellPrice = 152
---加-外攻化傷
EAttributeType.AddAtkHurtDepletion = 153
---減-外攻化傷
EAttributeType.SubAtkHurtDepletion = 154
---加-內攻化傷
EAttributeType.AddMagHurtDepletion = 155
---減-內攻化傷
EAttributeType.SubMagHurtDepletion = 156
---加-武器合成機率
EAttributeType.AddWpSuccRate = 157
---減-武器合成機率
EAttributeType.SubWpSuccRate = 158
---加-防具合成機率
EAttributeType.AddAmSuccRate = 159
---減-防具合成機率
EAttributeType.SubAmSuccRate = 160
---加-合成物品速度倍率
EAttributeType.AddFusionSPDRate = 161
---減-合成物品速度倍率
EAttributeType.SubFusionSPDRate = 162
---加-內功增益狀態時間
EAttributeType.AddBuffTimeRate = 163
---減-內功增益狀態時間
EAttributeType.SubBuffTimeRate = 164
---加-內功增益狀態時間
EAttributeType.AddBuffTime = 165
---減-內功增益狀態時間
EAttributeType.SubBuffTime = 166
---加-外功消耗內力
EAttributeType.AddSkillUseEnergy = 167
---減-外功消耗內力
EAttributeType.SubSkillUseEnergy = 168
---加-外功消耗內力倍率
EAttributeType.AddSkillUseEnergyRate = 169
---減-外功消耗內力倍率
EAttributeType.SubSkillUseEnergyRate = 170
---加-內功消耗內力
EAttributeType.AddBuffUseEnergy = 171
---減-內功消耗內力
EAttributeType.SubBuffUseEnergy = 172
---加-內功消耗內力倍率
EAttributeType.AddBuffUseEnergyRate = 173
---減-內功消耗內力倍率
EAttributeType.SubBuffUseEnergyRate = 174
---加-經脈攻擊
EAttributeType.AddSpecialDamage = 175
---減-經脈攻擊
EAttributeType.SubSpecialDamage = 176
---加-打坐武點倍率
EAttributeType.AddRestSkillPointRate = 177
---減-打坐武點倍率
EAttributeType.SubRestSkillPointRate = 178
---加-經脈防禦
EAttributeType.AddSpecialDefend = 179
---減-經脈防禦
EAttributeType.SubSpecialDefend = 180
---加-裝備維護
EAttributeType.AddEquipProtect = 181
---減-裝備維護
EAttributeType.SubEquipProtect = 182
---加-合成機率
EAttributeType.AddFusionRate = 191
---加-增加單人補血武功有效距離 (只有加沒有減)
EAttributeType.AddSingleBloodDistance = 194
--- 增加 刀外功加成 (整數)
EAttributeType.AddKnifeWugong = 200
--- 增加 劍外功加成 (整數)
EAttributeType.AddSwordWugong = 201
--- 增加 拳外功加成 (整數)
EAttributeType.AddFistWugong = 202
--- 增加 槍外功加成 (整數)
EAttributeType.AddPikeWugong = 203
--- 增加 棍外功加成 (整數)
EAttributeType.AddStickWugong = 204
--- 增加 抗爆率(爆擊抵抗)(千分比)
EAttributeType.AddCriticalResistanceRate = 205
--- 減少 抗爆率(爆擊抵抗)(千分比)
EAttributeType.ReduceCriticalResistanceRate = 206
--- 增加 抗爆傷(爆傷抵抗)(千分比)
EAttributeType.AddCriticalResistance = 207
--- 減少 抗爆傷(爆傷抵抗)(千分比)
EAttributeType.ReduceCriticalResistance = 208
--- 磁能儲存上限
EAttributeType.IncreaseMagneticLimit = 209

--- 280 寵物用

--- 寵物攻擊倍率(增加)
EAttributeType.IncreasePetAttack = 280
--- 寵物攻擊倍率(減少)
EAttributeType.DecreasePetAttack = 281
--- 寵物攻擊速度(增加)
EAttributeType.IncreasePetAttSpeed = 282
--- 寵物攻擊速度(減少)
EAttributeType.DecreasePetAttSpeed = 283
--- 寵物爆擊率(增加)
EAttributeType.IncreasePetCritChance = 284
--- 寵物爆擊率(減少)
EAttributeType.DecreasePetCritChance = 285
--- 寵物爆擊傷害(增加)
EAttributeType.IncreasePetCritDmg = 286
--- 寵物爆擊傷害(減少)
EAttributeType.DecreasePetCritDmg = 287


---Dot類型屬性從1000開始

---持續回復 氣血 氣血(數值)
EAttributeType.ContinueRecoverHP = 1000
---持續造成 經脈傷害 氣血(數值)
EAttributeType.ContinueCauseSpecialDamage = 1001
---持續回復內力 內力(數值)
EAttributeType.ContinueRecoverMP = 1002
---持續減少內力 內力(數值)
EAttributeType.ContinueReduceMP = 1003
---持續回復 罡氣 罡氣(數值)
EAttributeType.ContinueRecoverDP = 1004
---持續減少罡氣 罡氣(數值)
EAttributeType.ContinueReduceDP = 1005
---持續回復 真氣 真氣(數值)
EAttributeType.ContinueReoverSP = 1006
---持續減少 真氣 真氣(數值)
EAttributeType.ContinueReduceSP = 1007
---持續回復 氣血 氣血(%)
EAttributeType.ContinueRecoverHP_HundredRatio = 1008
---持續造成 經脈傷害 氣血(%)
EAttributeType.ContinueCauseSpecialDamage_HundredRatio = 1009
---持續回復內力 內力(%)
EAttributeType.ContinueRecoverMP_HundredRatio = 1010
---持續減少內力 內力(%)
EAttributeType.ContinueReduceMP_HundredRatio = 1011
---持續回復 罡氣 罡氣(%)
EAttributeType.ContinueRecoverDP_HundredRatio = 1012
---持續減少罡氣 罡氣(%)
EAttributeType.ContinueReduceDP_HundredRatio = 1013
---持續回復 真氣 真氣(%)
EAttributeType.ContinueReoverSP_HundredRatio = 1014
---持續減少 真氣 真氣(%)
EAttributeType.ContinueReduceSP_HundredRatio = 1015
---增加 Buff狀態 "數值為空 Buff填在Buff表的 特殊效果指定Buff編號"
EAttributeType.AddBuffStatus = 1016
---減少 Buff狀態 "數值為空 Buff填在Buff表的 特殊效果指定Buff編號"
EAttributeType.SubBuffStatus = 1017
---持續增加經驗值
EAttributeType.CoutinueAddExperience = 1018
---持續增加經驗值
EAttributeType.CoutinueAddWogonPoint = 1019

---持續 受吸取內力 扣中狀態的玩家或NPC的內力值給施放者
EAttributeType.CoutinueLoseMPByAbsorb = 1020
---持續 受吸取氣血 扣中狀態的玩家或NPC的內力值給施放者
EAttributeType.CoutinueLoseMPByAbsorb = 1021
--endregion

--region 虛寶(永丹)屬性 VirtualProperty 物品表 AX 欄 -> 基本1 屬性種類

---虛寶(永丹)屬性效果類型
---@class EVirtualPropertyKind ( 原 enum EStaticPropKind )
EVirtualPropertyKind = {}

EVirtualPropertyKind.Non             = 0
EVirtualPropertyKind.MaxHP_A         = 1
EVirtualPropertyKind.MaxHP_B         = 2
EVirtualPropertyKind.MaxMP_A         = 3
EVirtualPropertyKind.MaxMP_B         = 4
EVirtualPropertyKind.MaxSP_A         = 5
EVirtualPropertyKind.MaxSP_B         = 6
EVirtualPropertyKind.MaxLoad_A       = 7
EVirtualPropertyKind.MaxLoad_B       = 8
EVirtualPropertyKind.BuffTime        = 9
EVirtualPropertyKind.HPRef           = 10
---增加力量
EVirtualPropertyKind.STR             = 11
---增加敏捷
EVirtualPropertyKind.AGI             = 12
---增加體魄
EVirtualPropertyKind.CON             = 13
---增加真元
EVirtualPropertyKind.INT             = 14
---增加精神
EVirtualPropertyKind.MEN             = 15
EVirtualPropertyKind.ALL             = 16
EVirtualPropertyKind.MaxLUK          = 17
EVirtualPropertyKind.MaxSkillPoint   = 18
---背包擴充1
EVirtualPropertyKind.AddBag_1        = 19
---背包擴充2
EVirtualPropertyKind.AddBag_2        = 20
---錢莊擴充1
EVirtualPropertyKind.AddDepot_1      = 21
---錢莊擴充2
EVirtualPropertyKind.AddDepot_2      = 22
EVirtualPropertyKind.SuperChip_1     = 23
EVirtualPropertyKind.SuperChip_2     = 24
EVirtualPropertyKind.uperChip_3      = 25
EVirtualPropertyKind.EquipFace       = 26
EVirtualPropertyKind.EquipHead       = 27
EVirtualPropertyKind.EquipCloak      = 28
EVirtualPropertyKind.EquipWeapon     = 29
EVirtualPropertyKind.EquipBody       = 30
EVirtualPropertyKind.EquipHand       = 31
EVirtualPropertyKind.EquipRing       = 32
EVirtualPropertyKind.EquipFoot       = 33
EVirtualPropertyKind.EquipNecklace   = 34
EVirtualPropertyKind.EquipShirt      = 35
---背包擴充3
EVirtualPropertyKind.AddBag_3        = 36
---背包擴充4
EVirtualPropertyKind.AddBag_4        = 37
---背包擴充5
EVirtualPropertyKind.AddBag_5        = 38
---背包擴充6
EVirtualPropertyKind.AddBag_6        = 39
---錢莊擴充3
EVirtualPropertyKind.AddDepot_3      = 40
---錢莊擴充4
EVirtualPropertyKind.AddDepot_4      = 41
---錢莊擴充5
EVirtualPropertyKind.AddDepot_5      = 42
---錢莊擴充6
EVirtualPropertyKind.AddDepot_6      = 43
---習武空間擴充
EVirtualPropertyKind.WugongSpace_1 	 = 44
---經驗值獲得率
EVirtualPropertyKind.Exp_A           = 45
---經驗值獲得率
EVirtualPropertyKind.Exp_B           = 46
---武點獲得率
EVirtualPropertyKind.SkillPoint_A    = 47
---武點獲得率
EVirtualPropertyKind.SkillPoint_B    = 48
---習武空間擴充
EVirtualPropertyKind.WugongSpace_2   = 49
---習武空間擴充
EVirtualPropertyKind.WugongSpace_3   = 50
---SERVER使用
EVirtualPropertyKind.TmpSpace1       = 51
---SERVER使用
EVirtualPropertyKind.TmpSpace2       = 52
---SERVER使用
EVirtualPropertyKind.TmpSpace3       = 53
---SERVER使用
EVirtualPropertyKind.TmpSpace4       = 54
---SERVER使用
EVirtualPropertyKind.TmpSpace5       = 55
---外功攻擊增加
EVirtualPropertyKind.Atk_1           = 56
---內功攻擊增加
EVirtualPropertyKind.MagAtk_1        = 57
---外功防禦增加
EVirtualPropertyKind.Def_1           = 58
---內功防禦增加
EVirtualPropertyKind.MagDef_1        = 59
---外功化傷增加
EVirtualPropertyKind.AtkHurtReduce_1 = 60
---內功化傷增加
EVirtualPropertyKind.MagHurtReduce_1 = 61
---外功攻擊增加
EVirtualPropertyKind.Atk_2           = 62
---內功攻擊增加
EVirtualPropertyKind.MagAtk_2        = 63
---外功防禦增加
EVirtualPropertyKind.Def_2           = 64
---內功防禦增加
EVirtualPropertyKind.MagDef_2        = 65
---習武空間擴充
EVirtualPropertyKind.WugongSpace_4   = 66
---習武空間擴充
EVirtualPropertyKind.WugongSpace_5   = 67
---爆擊傷害倍率增加
EVirtualPropertyKind.Critical_Hurt_1 = 68
---爆擊傷害倍率增加
EVirtualPropertyKind.Critical_Hurt_2 = 69
---命中
EVirtualPropertyKind.Hit_1           = 70
---命中
EVirtualPropertyKind.Hit_2           = 71
---爆擊率增加
EVirtualPropertyKind.Critical_Rate_1 = 72
---爆擊率增加
EVirtualPropertyKind.Critical_Rate_2 = 73
---攻擊速度增加
EVirtualPropertyKind.AtkSpd_1        = 74
---攻擊速度增加
EVirtualPropertyKind.AtkSpd_2        = 75
---最大傷害增加
EVirtualPropertyKind.MaxDamage_1     = 76
---最大傷害增加
EVirtualPropertyKind.MaxDamage_2     = 77
---真武傷害增加
EVirtualPropertyKind.DirDmg_1        = 78
---真武傷害增加
EVirtualPropertyKind.DirDmg_2        = 79
---經脈攻擊增加
EVirtualPropertyKind.SpecialDmg_1    = 80
---經脈攻擊增加
EVirtualPropertyKind.SpecialDmg_2    = 81
---刀法傷害倍率增加
EVirtualPropertyKind.Blade_1         = 82
---刀法傷害倍率增加
EVirtualPropertyKind.Blade_2         = 83
---劍法傷害倍率增加
EVirtualPropertyKind.Sword_1         = 84
---劍法傷害倍率增加
EVirtualPropertyKind.Sword_2         = 85
---拳法傷害倍率增加
EVirtualPropertyKind.Fight_1         = 86
---拳法傷害倍率增加
EVirtualPropertyKind.Fight_2         = 87
---槍法傷害倍率增加
EVirtualPropertyKind.Spear_1         = 88
---槍法傷害倍率增加
EVirtualPropertyKind.Spear_2         = 89
---棍法傷害倍率增加
EVirtualPropertyKind.Stick_1         = 90
---棍法傷害倍率增加
EVirtualPropertyKind.Stick_2         = 91
---化勁倍率增加
EVirtualPropertyKind.Depletion_1     = 92
---化勁倍率增加
EVirtualPropertyKind.Depletion_2     = 93
---卸勁倍率增加
EVirtualPropertyKind.Unloading_1     = 94
---卸勁倍率增加
EVirtualPropertyKind.Unloading_2     = 95
---經脈防禦增加
EVirtualPropertyKind.SpecialDef_1    = 96
---經脈防禦增加
EVirtualPropertyKind.SpecialDef_2    = 97
---迴避增加
EVirtualPropertyKind.Dodge_1         = 98
---迴避增加
EVirtualPropertyKind.Dodge_2         = 99
---外功減傷倍率增加
EVirtualPropertyKind.AtkDec_1        = 100
---外功減傷倍率增加
EVirtualPropertyKind.AtkDec_2        = 101
---內功減傷倍率增加
EVirtualPropertyKind.MagAtkDec_1     = 102
---內功減傷倍率增加
EVirtualPropertyKind.MagAtkDec_2     = 103
EVirtualPropertyKind.MaxHP_C         = 104
EVirtualPropertyKind.MaxHP_D         = 105
EVirtualPropertyKind.MaxMP_C         = 106
EVirtualPropertyKind.MaxMP_D         = 107
EVirtualPropertyKind.MaxSP_C         = 108
EVirtualPropertyKind.MaxSP_D         = 109
EVirtualPropertyKind.MaxLoad_C       = 110
EVirtualPropertyKind.MaxLoad_D       = 111
---經驗值獲得率
EVirtualPropertyKind.Exp_C           = 112
---經驗值獲得率
EVirtualPropertyKind.Exp_D           = 113
---武點獲得率
EVirtualPropertyKind.SkillPoint_C    = 114
---武點獲得率
EVirtualPropertyKind.SkillPoint_D    = 115
---增加氣血上限E
EVirtualPropertyKind.MaxHP_E         = 116
---增加氣血上限F
EVirtualPropertyKind.MaxHP_F         = 117
---增加內力上限E
EVirtualPropertyKind.MaxMP_E         = 118
---增加內力上限F
EVirtualPropertyKind.MaxMP_F         = 119
---增加真氣上限E
EVirtualPropertyKind.MaxSP_E         = 120
---增加真氣上限F
EVirtualPropertyKind.MaxSP_F         = 121
---增加負重上限E
EVirtualPropertyKind.MaxLoad_E       = 122
---增加負重上限F
EVirtualPropertyKind.MaxLoad_F       = 123
---最大傷害增加
EVirtualPropertyKind.MaxDamage_3     = 124
---最大傷害增加
EVirtualPropertyKind.MaxDamage_4     = 125
---真武傷害增加
EVirtualPropertyKind.DirDmg_3        = 126
---真武傷害增加
EVirtualPropertyKind.DirDmg_4        = 127
---命中增加
EVirtualPropertyKind.Hit_3           = 128
---命中增加
EVirtualPropertyKind.Hit_4           = 129
---爆擊率增加
EVirtualPropertyKind.Critical_Rate_3 = 130
---爆擊率增加
EVirtualPropertyKind.Critical_Rate_4 = 131
---外攻防禦增加
EVirtualPropertyKind.Def_3           = 132
---外攻防禦增加
EVirtualPropertyKind.Def_4           = 133
---內攻防禦增加
EVirtualPropertyKind.MagDef_3        = 134
---內攻防禦增加
EVirtualPropertyKind.MagDef_4        = 135
---增加力量
EVirtualPropertyKind.STR_2           = 136
---增加敏捷
EVirtualPropertyKind.AGI_2           = 137
---增加體魄
EVirtualPropertyKind.CON_2           = 138
---增加真元
EVirtualPropertyKind.INT_2           = 139
---增加精神
EVirtualPropertyKind.MEN_2           = 140
---外功攻擊增加
EVirtualPropertyKind.Atk_3           = 141
---外功攻擊增加
EVirtualPropertyKind.Atk_4           = 142
---內功攻擊增加
EVirtualPropertyKind.MagAtk_3        = 143
---內功攻擊增加
EVirtualPropertyKind.MagAtk_4        = 144
---外功化傷增加
EVirtualPropertyKind.AtkHurtReduce_3 = 145
---外功化傷增加
EVirtualPropertyKind.AtkHurtReduce_4 = 146
---內功化傷增加
EVirtualPropertyKind.MagHurtReduce_3 = 147
---內功化傷增加
EVirtualPropertyKind.MagHurtReduce_4 = 148
---爆擊傷害倍率增加
EVirtualPropertyKind.Critical_Hurt_3 = 149
---爆擊傷害倍率增加
EVirtualPropertyKind.Critical_Hurt_4 = 150
---攻擊速度增加
EVirtualPropertyKind.AtkSpd_3        = 151
---攻擊速度增加
EVirtualPropertyKind.AtkSpd_4        = 152
---經脈攻擊增加
EVirtualPropertyKind.SpecialDmg_3    = 153
---經脈攻擊增加
EVirtualPropertyKind.SpecialDmg_4    = 154
---化勁倍率增加
EVirtualPropertyKind.Depletion_3     = 155
---化勁倍率增加
EVirtualPropertyKind.Depletion_4     = 156
---卸勁倍率增加
EVirtualPropertyKind.Unloading_3     = 157
---卸勁倍率增加
EVirtualPropertyKind.Unloading_4     = 158
---經脈防禦增加
EVirtualPropertyKind.SpecialDef_3    = 159
---經脈防禦增加
EVirtualPropertyKind.SpecialDef_4    = 160
---迴避增加
EVirtualPropertyKind.Dodge_3         = 161
---迴避增加
EVirtualPropertyKind.Dodge_4         = 162
---外功減傷倍率增加
EVirtualPropertyKind.AtkDec_3        = 163
---外功減傷倍率增加
EVirtualPropertyKind.AtkDec_4        = 164
---內功減傷倍率增加
EVirtualPropertyKind.MagAtkDec_3     = 165
---內功減傷倍率增加
EVirtualPropertyKind.MagAtkDec_4     = 166
---增加力量
EVirtualPropertyKind.STR_3           = 167
---增加敏捷
EVirtualPropertyKind.AGI_3           = 168
---增加體魄
EVirtualPropertyKind.CON_3           = 169
---增加真元
EVirtualPropertyKind.INT_3           = 170
---增加精神
EVirtualPropertyKind.MEN_3           = 171
---刀法傷害倍率增加
EVirtualPropertyKind.Blade_3         = 172
---劍法傷害倍率增加
EVirtualPropertyKind.Sword_3         = 173
---拳法傷害倍率增加
EVirtualPropertyKind.Fight_3         = 174
---槍法傷害倍率增加
EVirtualPropertyKind.Spear_3         = 175
---棍法傷害倍率增加
EVirtualPropertyKind.Stick_3         = 176
---PK減傷倍率增加
EVirtualPropertyKind.PKDec_1         = 177
---PK減傷倍率增加
EVirtualPropertyKind.PKDec_2         = 178
---合成成功率增加
EVirtualPropertyKind.FusionRate_1    = 179
---合成成功率增加
EVirtualPropertyKind.FusionRate_2    = 180
---PK刀法減傷(數值)
EVirtualPropertyKind.PKBladeDecHurt_1 = 181
---PK劍法減傷(數值)
EVirtualPropertyKind.PKSwordDecHurt_1 = 182
---PK拳法減傷(數值)
EVirtualPropertyKind.PKFightDecHurt_1 = 183
---PK槍法減傷(數值)
EVirtualPropertyKind.PKSpearDecHurt_1 = 184
---PK棍法減傷(數值)
EVirtualPropertyKind.PKStickDecHurt_1 = 185
---PK刀法減傷(數值)
EVirtualPropertyKind.PKBladeDecHurt_2 = 186
---PK劍法減傷(數值)
EVirtualPropertyKind.PKSwordDecHurt_2 = 187
---PK拳法減傷(數值)
EVirtualPropertyKind.PKFightDecHurt_2 = 188
---PK槍法減傷(數值)
EVirtualPropertyKind.PKSpearDecHurt_2 = 189
---PK棍法減傷(數值)
EVirtualPropertyKind.PKStickDecHurt_2 = 190
---PK刀法減傷(數值)
EVirtualPropertyKind.PKBladeDecHurt_3 = 191
---PK劍法減傷(數值)
EVirtualPropertyKind.PKSwordDecHurt_3 = 192
---PK拳法減傷(數值)
EVirtualPropertyKind.PKFightDecHurt_3 = 193
---PK槍法減傷(數值)
EVirtualPropertyKind.PKSpearDecHurt_3 = 194
---PK棍法減傷(數值)
EVirtualPropertyKind.PKStickDecHurt_3 = 195
---PK刀法減傷(數值)
EVirtualPropertyKind.PKBladeDecHurt_4 = 196
---PK劍法減傷(數值)
EVirtualPropertyKind.PKSwordDecHurt_4 = 197
---PK拳法減傷(數值)
EVirtualPropertyKind.PKFightDecHurt_4 = 198
---PK槍法減傷(數值)
EVirtualPropertyKind.PKSpearDecHurt_4 = 199
---PK棍法減傷(數值)
EVirtualPropertyKind.PKStickDecHurt_4 = 200
---PK刀法減傷(數值)
EVirtualPropertyKind.PKBladeDecHurt_5 = 201
---PK劍法減傷(數值)
EVirtualPropertyKind.PKSwordDecHurt_5 = 202
---PK拳法減傷(數值)
EVirtualPropertyKind.PKFightDecHurt_5 = 203
---PK槍法減傷(數值)
EVirtualPropertyKind.PKSpearDecHurt_5 = 204
---PK棍法減傷(數值)
EVirtualPropertyKind.PKStickDecHurt_5 = 205
---PK刀法增傷(數值)
EVirtualPropertyKind.PKBladeIncHurt_1 = 206
---PK劍法增傷(數值)
EVirtualPropertyKind.PKSwordIncHurt_1 = 207
---PK拳法增傷(數值)
EVirtualPropertyKind.PKFightIncHurt_1 = 208
---PK槍法增傷(數值)
EVirtualPropertyKind.PKSpearIncHurt_1 = 209
---PK棍法增傷(數值)
EVirtualPropertyKind.PKStickIncHurt_1 = 210
---PK刀法增傷(數值)
EVirtualPropertyKind.PKBladeIncHurt_2 = 211
---PK劍法增傷(數值)
EVirtualPropertyKind.PKSwordIncHurt_2 = 212
---PK拳法增傷(數值)
EVirtualPropertyKind.PKFightIncHurt_2 = 213
---PK槍法增傷(數值)
EVirtualPropertyKind.PKSpearIncHurt_2 = 214
---PK棍法增傷(數值)
EVirtualPropertyKind.PKStickIncHurt_2 = 215
---PK刀法增傷(數值)
EVirtualPropertyKind.PKBladeIncHurt_3 = 216
---PK劍法增傷(數值)
EVirtualPropertyKind.PKSwordIncHurt_3 = 217
---PK拳法增傷(數值)
EVirtualPropertyKind.PKFightIncHurt_3 = 218
---PK槍法增傷(數值)
EVirtualPropertyKind.PKSpearIncHurt_3 = 219
---PK棍法增傷(數值)
EVirtualPropertyKind.PKStickIncHurt_3 = 220
---PK刀法增傷(數值)
EVirtualPropertyKind.PKBladeIncHurt_4 = 221
---PK劍法增傷(數值)
EVirtualPropertyKind.PKSwordIncHurt_4 = 222
---PK拳法增傷(數值)
EVirtualPropertyKind.PKFightIncHurt_4 = 223
---PK槍法增傷(數值)
EVirtualPropertyKind.PKSpearIncHurt_4 = 224
---PK棍法增傷(數值)
EVirtualPropertyKind.PKStickIncHurt_4 = 225
---PK刀法增傷(數值)
EVirtualPropertyKind.PKBladeIncHurt_5 = 226
---PK劍法增傷(數值)
EVirtualPropertyKind.PKSwordIncHurt_5 = 227
---PK拳法增傷(數值)
EVirtualPropertyKind.PKFightIncHurt_5 = 228
---PK槍法增傷(數值)
EVirtualPropertyKind.PKSpearIncHurt_5 = 229
---PK棍法增傷(數值)
EVirtualPropertyKind.PKStickIncHurt_5 = 230

--endregion

---@class EExpItemKind 經驗道具分類 ( enum EExperienceKind )
EExpItemKind = {}
---無
EExpItemKind.Non = 0
---經驗值
EExpItemKind.Exp = 1
---武點
EExpItemKind.SkillPoint = 2
---心性_佛
EExpItemKind.Buddhism = 3
---心性_道
EExpItemKind.Taoist = 4
---心性_魔
EExpItemKind.Dvil = 5
---經商
EExpItemKind.Business = 6

---@class EMedicineKind 藥水基本屬性再分類 ( enum EMedicineKind )
EMedicineKind = {}
---無
EMedicineKind.Non = 0
---氣血
EMedicineKind.HP = 1
---內力
EMedicineKind.MP = 2
---真氣
EMedicineKind.SP = 3
---罡氣
EMedicineKind.DP = 4
---增益效果
EMedicineKind.Buff = 5
---新手buff
EMedicineKind.GuideBuff = 6
---PK保護
EMedicineKind.Stop_PK = 7

---@class EGameIdxType 遊戲使用的索引類型
EGameIdxType = {}
---空
EGameIdxType.None = 0
---武功區間 1~2000
EGameIdxType.Skill = 1
---裝備技能區間 2001~2500
EGameIdxType.Equip_Skill = 2001
---預留區間 2501~9000
EGameIdxType.Reserved = 2501
---特殊按鈕區間 9001~10000 *組合招區間9007~9011(企劃不填表要直接寫死)
EGameIdxType.Special_Btn = 9001
---物品區間 10001~
EGameIdxType.Item = 10001


--region SelectMgr 選取系統
---場景物件消失距離
DISAPPEAR_DISTANCE = 15
--endregion

ESendMoveReason ={
    Non = 0,
    ---S 先到
    Server = 1,
     --- C 到
    Client = 2,
    --- 強制停止
    Forcibly = 3,
    --- 改變速度前停止
    ChangeSpeed = 4,
    --- 罡氣0受擊
    OnHit = 5
}

EPlayerStuckedReason = {
    --- 撞到障礙點
    HitObstacle = 0,
    --- 因為尋路時間過久而卡住
    PathMoveTimeTooLong = 1,
    --- 移動距離過短
    MoveDistanceTooShort = 2,
    --- 5-4 強制修正位置
    PositionWasCorrectedByServer = 3,
    --- 5-2 強制停止
    ForceStoppedByServer = 4,
    --- 被技能中斷
    BreakBySkill = 5,
    --- 路徑上有障礙物
    PathHaveObstacle = 6,
    --- 玩家整個人都在障礙點(卡死，只能透過卡點逃脫)
    PositionIsObstacle = 7,
    ---  沒卡住(內部運作用，不會收到這則通知)
    NotStucked = 8,
    --- 尋路找到的目標無法跟一開始的目標吻合
    PathTargetError = 9
}

ESendMoveResult = {
    Success = 0,
    MoveDistanceTooShort = 1,
    Hit = 2,
    TooMuchPoints = 3
}

mDetourOffset = {
    Vector2.New(1,0),
    Vector2.New(-1,0),
    Vector2.New(0,1),
    Vector2.New(0,-1),
    Vector2.New(1,1),
    Vector2.New(-1,1),
    Vector2.New(-1,1), -- 原本就長這樣 去問原作者
    Vector2.New(-1,-1)
}

--任務資料分類
EMissionType =
{
    ---主線/劇情
    Main        = 1,

    ---俠客
    Branch      = 2,

    ---特殊
    Special     = 3,

    ---修行 預留
    Sharpen     = 4,

    ---活動
    Activity    = 5,

    ---幫會
    Guild       = 6,

    ---副本
    Raid        = 7,

    ---關卡 預留
    Stage       = 8,

    ---組隊 預留
    Team        = 9,

    ---補增
    More        = 10,

    Non         = 255,
}

--臉部特徵調整索引
EFacialAdjustList =
{
    [1]=	"EyePosX",
    [2]=	"EyePosY",
    [3]=	"EyeRot",
    [4]=	"EyeSize",
    [5]=	"ForeheadOffset",
    [6]=	"EyebrowPosY",
    [7]=	"EyebrowPosX",
    [8]=	"EyebrowPosZ",
    [9]=	"EyebrowRot",
    [10]=	"EyebrowSize",
    [11]=	"NoseBridgeOffset",
    [12]=	"NoseBridgeSize",
    [13]=	"NoseOffset",
    [14]=	"NosePos",
    [15]=	"NoseRot",
    [16]=	"NoseSize",
    [17]=	"MouthPos",
    [18]=	"MouthRot",
    [19]=	"MouthSize",
    [20]=	"MouthThickness",
    [21]=	"TibiaOffset",
    [22]=	"CheekOffset",
    [23]=	"ChinEPos",
    [24]=	"ChinEOffset",
    [25]=	"ChinSPos",
    [26]=	"ChinSOffset",
    [27]=	"ChinSSize",
}

---AnimationEvent 名稱
---@class EAnimationEvent
EAnimationEvent =
{
    OnParticle = "OnParticle",
    OnSound = "OnSound",
    OnCamera = "OnCamera",
    OnPostProcess = "OnPostProcess",
    OnDelegate = "OnDelegate",
    OnEnd = "OnEnd"
}

--region 對話相關
ChatItemMaxNum = 6

---@class EChannelPage 頻道(這個陣列是從0開始的 取的時候要注意)
EChannelPage = {
    ---不分類頻道
    AllChannels 		= 0,
    ---公頻
    PublicChannels 		= 1,
    ---輕語頻道
    AreaChannels 		= 2,
    ---密語頻道
    PrivateChannels 	= 3,
    ---好友頻道
    FriendChannels   	= 4,
    ---隊伍頻道
    TeamChannels 		= 5,
    ---幫會頻道
    GangChannels 		= 6,
    ---系統頻道
    SystemChannels 		= 7,
    ---陣營
    CampsChannels       = 8,
    ---大聲公
    PublicChannels2     = 9,
}

---@class EChannelStr 頻道字串
EChannelStr = {
    ---不分類頻道(全部聊天)
    [EChannelPage.AllChannels] = 20204000,
    ---公頻(世界聊天)
    [EChannelPage.PublicChannels] = 20204002,
    ---輕語頻道(區域聊天)
    [EChannelPage.AreaChannels] = 20204001,
    ---密語頻道(私訊聊天)
    [EChannelPage.PrivateChannels] = 20204004,
    ---好友頻道
    [EChannelPage.FriendChannels] = 0,
    ---隊伍頻道(隊伍聊天)
    [EChannelPage.TeamChannels] = 20204003,
    ---幫會頻道(兵團聊天)
    [EChannelPage.GangChannels] = 20204005,
    ---系統頻道(系統)
    [EChannelPage.SystemChannels] = 20204008,
    ---陣營(陣營聊天)
    [EChannelPage.CampsChannels] = 20204006,
    ---大聲公(大聲公)
    [EChannelPage.PublicChannels2] = 20204009,
}

---@class EChannelTMPSTyle 頻道使用的TMPStyle
EChannelTMPSTyle = {
    ---不分類頻道(全部聊天)
    [EChannelPage.AllChannels] = "GO_03",
    ---公頻(世界聊天)
    [EChannelPage.PublicChannels] = "OO",
    ---輕語頻道(區域聊天)
    [EChannelPage.AreaChannels] = "GO",
    ---密語頻道(私訊聊天)
    [EChannelPage.PrivateChannels] = "GO_02",
    ---好友頻道
    [EChannelPage.FriendChannels] = 0,
    ---隊伍頻道(隊伍聊天)
    [EChannelPage.TeamChannels] = "BO",
    ---幫會頻道(兵團聊天)
    [EChannelPage.GangChannels] = "RO",
    ---系統頻道(系統)
    [EChannelPage.SystemChannels] = "GO_03",
    ---陣營(陣營聊天)
    [EChannelPage.CampsChannels] = "PO_01",
    ---大聲公(大聲公)
    [EChannelPage.PublicChannels2] = "GO_03",
}

EChannelIMG = {
    "MainIcon_066_L",
    "MainIcon_0101_L",
    "MainIcon_0102_L",
    "MainIcon_0103_L",
    "MainIcon_0104_L",
    "MainIcon_0104_L",
    "MainIcon_0105_L",
    "MainIcon_0106_L",
    "MainIcon_0107_L",
    "MainIcon_100",
}

EchannelTab = {
    EChannelPage.AllChannels,
    EChannelPage.AreaChannels,
    EChannelPage.PublicChannels,
    EChannelPage.TeamChannels,
    EChannelPage.FriendChannels,
    EChannelPage.PrivateChannels,
    EChannelPage.GangChannels,
    EChannelPage.CampsChannels,
    EChannelPage.SystemChannels,
    EChannelPage.PublicChannels2
}

EControlChannel = {
    [EChannelPage.AreaChannels] = 1,
    [EChannelPage.PublicChannels] = 2,
    [EChannelPage.TeamChannels] = 3,
    [EChannelPage.PrivateChannels] = 4,
    --EChannelPage.FriendChannels,
    [EChannelPage.GangChannels] = 5,
    [EChannelPage.CampsChannels] = 6,
    [EChannelPage.PublicChannels2] = 7,
}

ESpeakChannel = {
    EChannelPage.PublicChannels,
    EChannelPage.AreaChannels,
    EChannelPage.TeamChannels,
    EChannelPage.PrivateChannels,
    EChannelPage.GangChannels,
    EChannelPage.CampsChannels
}

---@class EChannelEmoji 頻道字串圖樣
EChannelEmoji = {
    ---不分類頻道 沒圖暫代
    [EChannelPage.AllChannels] = "Symbol_13",
    ---公頻
    [EChannelPage.PublicChannels] = "Symbol_12",
    ---輕語頻道
    [EChannelPage.AreaChannels] = "Symbol_15",
    ---密語頻道
    [EChannelPage.PrivateChannels] = "Symbol_18",
    ---好友頻道
    [EChannelPage.FriendChannels] = "Symbol_13",
    ---隊伍頻道
    [EChannelPage.TeamChannels] = "Symbol_17",
    ---幫會頻道
    [EChannelPage.GangChannels] = "Symbol_16",
    ---系統頻道
    [EChannelPage.SystemChannels] = "Symbol_13",
    ---陣營 沒圖暫代
    [EChannelPage.CampsChannels] = "Symbol_14",
    ---大聲公 沒圖暫代
    [EChannelPage.PublicChannels2] = "Symbol_13",
}

------@class EChannelStyle 頻道字串Style
EChannelStyle = {
    ---不分類頻道
    [EChannelPage.AllChannels] = "GO_03",
    ---公頻
    [EChannelPage.PublicChannels] = "OO",
    ---輕語頻道
    [EChannelPage.AreaChannels] = "GO",
    ---密語頻道
    [EChannelPage.PrivateChannels] = "GO_02",
    ---好友頻道
    [EChannelPage.FriendChannels] = "GO_03",
    ---隊伍頻道
    [EChannelPage.TeamChannels] = "BO",
    ---幫會頻道
    [EChannelPage.GangChannels] = "RO",
    ---系統頻道
    [EChannelPage.SystemChannels] = "GO_03",
    ---陣營
    [EChannelPage.CampsChannels] = "PO_01",
    ---大聲公
    [EChannelPage.PublicChannels2] = "GO_03",
}

---@class EBrowPage 發話內容
EBrowPage = {
    Emoticons   = 0,
    YanText     = 1,
    Bag         = 2
}
--endregion

---@class ENPCPriority NPC權重
ENPCPriority = {
    ---小怪
    Monster     = 0,
    ---菁英
    Elite       = 1,
    ---Boss
    Boss        = 2,
    ---世界 Boss
    WorldBoss   = 3,
    ---預留1
    Blank1      = 4,
    ---預留2
    Blank2      = 5,
}
--region Observer
---@class EStateObserver 註冊的state
EStateObserver = {
    ---背包資料更新
    BagDataRefresh = 1,
    ---HP、MP、SP數值更新
    BaseValueRefresh = 2,
    ---等級數值更新
    PlayerLevelRefresh = 3,
    ---經驗值武點刷新
    PlayerExpWogonRefresh = 4,
    ---任何Buff的刷新
    BuffRefresh = 5,
    --- 更新 HP
    UpdateHp = 6,
    --- 更新 MP
    UpdateMp = 7,
    --- 更新 SP
    UpdateSp = 8,
    -- --- 更新 DP (  )
    -- UpdateDp = 9,
    --- 更新 鑽石
    UpdateDiamond = 10,
    --- 更新 銀幣
    UpdateCoin = 11,
    --- 更新 黃易幣
    UpdateHEMCoin = 12,
    --- 更新 狀態
    ChangeState = 13,
    --- 更新屬性 未投點點數( 力量 體魄 精神 敏捷 真元 未投點的點數 )
    UpdateMainAttribute = 14,
    --- 更新副屬性
    UpdateSubAttribute = 15,
    --- 更新永丹使用狀況
    UpdateYuDan = 16,
    --- 更新生活技能
    UpdateLLivingSkill = 17,
    --- 更新我的最愛道具(增加或者減少)
    UpdateMyFavoriteItem = 18,
    --- 更新物品狀態(使用限制) EItemStatus 這個參數變化時使用
    UpdateItemState = 19,
    --- 量子回音
    UpdateSkillBook = 20,
    --- 更新永標
    UpdateStaticFlag = 21,
    --- 更新能量效率
    UpdateEnergy = 22,
    --- 介消成功刷新
    UISellRefresh = 23,
    --- 更新武功
    UpdateWugong = 24,
    --- 更新鍵盤按鈕
    UpdateInputKey = 25,

    ---整點
    UpdateOnHourTime = 26,
    
    --- 更新特定物品
    UpdateSpecificItem = 27,
    --- 更新福緣
    UpdateFortune = 28,
    --- 更新武點
    UpdateWugongPoint = 29,
    --- 更新威望
    UpdatePrestige = 30,
    --- 更新聲譽
    UpdateReputation = 31,
    --- 更新量子源
    UpdateQuantumSource = 32,

    ---更新心法資料
    UpdateMethodData = 33,
    ---更新運行心法
    UpdateCurMethod = 34,
    ---更新靈氣值
    UpdateSpiritValue = 35,
    ---更新靈脈貫通
    UpdateSpiritVeins = 36,
    ---更新六脈靈功
    UpdateSixSpirit = 37,
    ---更新靈氣解放
    UpdateSpiritLiberation = 38,
}
--endregion

--region AVGType
---@class EAVGType AVG模式
EAVGType = {
    Order = 1,
    Random = 2
}
--endregion

---預設AVG發話者的頭圖
DEFAULT_AVG_HEAD = "RoleImg_0"
---預設男性頭圖
DEFAULT_MAN_HEAD = "RoleImg_CM_0012"
---預設女性頭圖
DEFAULT_WOMAN_HEAD = "RoleImg_CW_0012"

---預設頭像外框圖
DEFAULT_HEAD_FRAME = "Avatar_Frame_003"

EGenderHead =
{
    DEFAULT_MAN_HEAD,
    DEFAULT_WOMAN_HEAD
}

---預設男性全身圖
DEFAULT_MAN_DIALOGUE = "CM_0012_A"
---預設女性全身圖
DEFAULT_WOMAN_DIALOGUE = "CW_0012_A"


---@class EPKSwichStatus PK開關狀態
EPKSwichStatus =
{
    Close = 0,
    Open = 1,
    Discount = 2,
    CancelDiscount = 3
}

---@class EBubbleBoxWhoTalk 發話者
EBubbleBoxWhoTalk = {
    PLAYER = 0,
    ASK = 1,
    NPC1 = 2,
    NPC2 = 3,
    NPC3 = 4,
    NPC4 = 5,
    NPC5 = 6,
    NPC6 = 7,
    None = 8
}

---@class EBubbleBoxPriority 發話權重
EBubbleBoxPriority = {
    Routine = 1,
    Fight = 2,
    Event = 3,
}

---@class EBubbleBoxMode 對話模式(跟S端給的不一樣，不確定為啥)
EBubbleBoxMode = {
    Order = 3,
    Random = 2,
    Blood1 = 1,
    PetSpecial = -1,

}

---@class E_ExpWogonPointType 顯示經驗獲得跟武點獲得的TMP 會使用到的enum
E_ExpWogonPointType = {
    ExpType = 1,
    WogonPointType = 2
}
--endregion

--輔助功能群
---@class EAideGroup 輔助功能相關群組
EAideGroup = {
    Chat = 1,
    Afk = 2,
    ---快速聊天 表情符號
    QuickChat = 3,
    -- Chip = 4,
    -- PK = 5
}

--提示功能群相關
---提醒數量最大上限
TipMaxNum = 9
---超過上限要顯示的字串
TipMoreMaxNumStr = TipMaxNum .. "+"

---@class ETipGroup 提示功能相關群組
ETipGroup = {
    Event = 1,
    Promotion = 2,
    Repair = 3,
    Teach = 4,
    Recruit = 5,
}

---@class ELiveSkill 生活技能相關
ELiveSKill = {
    --挖礦
    DigMine = 1,
    --採集
    CollectMaterial = 2,
    --農耕
    Farming = 3,
    --採藥
    CollectMedicine = 4,
    --工匠
    Artisan = 5,
    --琴藝
    Guqin =6,
    --棋藝
    Chess =7,
    --書藝
    Writing = 8,
    --畫藝
    Drawing = 9,
    --詩藝
    Poem = 10,
    --酒藝
    Wine = 11,
    --花藝
    Flower = 12,
    --茶藝
    Tea = 13,
    --緞刀
    BladeMaking = 14,
    --鑄劍
    SwordMaking = 15,
    --紡織
    Textile = 16,
    --鞋匠
    ShowMaking = 17,
    --冶槍
    SpearMaking = 18,
    --鑲嵌
    Mosaic = 19,
    --縫紉
    Sewing = 20,
    --練爪
    ClawMaking = 21,
    --製棍
    StickMaking = 22,
    --巧匠
    SkillfulCraftsman = 23,
    --裁縫
    Tailor = 24,
    ---Max
    Max = 25,

}

---@class EAvatarQulifyType 頭像/頭框 取得條件類型
EAvatarQualityType =
{
    ---無前置需求
    NO_REQUEST =1,
    ---永標
    Static =2,
    ---計數永標
    StaticNum =3,
}

---地圖標記類型
EMapPointType = {
    --集結
    Comming = 1,
    --警示
    Waring = 2
}

---@class EResurrectionType 復活方式
EResurrectionType =
{
    ---光子傳送 回傳到緊急傳送點且 有 懲罰
    PhotonTransfer = 1,
    ---奈米光傳送 回到緊急傳送點且 沒有 懲罰
    NanoLightTransfer = 2,
    ---奈米注射 原地復活
    NanoShot = 3,

    ---假死後的復活
    PostFakeDead = 4,
    ---其他玩家放季能幫你復活
    ResurrectionByOtherPlayer = 5,
    ---用協定8-13直接復活 (基本上是開發者在使用)
    ResurrectionByOtherPlayer_Dev = 6,
}

---@class ELeaveDungeonType 離開副本的種類
ELeaveDungeonType =
{
    ---一般副本
    Normal = 1,
    ---大俠選邊站
    SelectSide = 2,
    ---PVP量子幻境
    PVPQuantum = 3,
    ---結婚場景
    MarriageMap = 4,
    ---破碎戰場
    BrokenBattle = 5,
    ---攻城戰
    Siege = 6,
}


---@class ETitlePageGroup 稱號顯示頁籤(類型)
ETitlePageGroup =
{
    ---一般
    Normal = 1,
    ---活動
    Activity = 2,
    ---特殊
    Special = 3,
    ---其他
    Other = 4,
}

---稱號是否獲得的的檢查條件
ETitleChckType =
{
    ---檢查 聲譽
    Reputation = 1,
    ---檢查魅力
    Charm = 2,
    ---檢查 威望
    Prestige = 3,
    ---檢查一般永標
    Static = 4,
    ---檢查動標
    MovingFlag = 5,
    ---檢查計數永標
    StaticNum = 6,
    ---檢查生活技能
    LivingSkill = 7,
    ---檢查永丹
    YuDan = 8,
}


---屬性 永丹/晶片篩選 晶片類型
EChipFilterChipType =
{
    ---全部
    AllChip = 1,
    ---攻擊
    AttackChip = 2,
    ---防禦
    DefenceChip = 3,
    ---其他
    OtheChip= 4,
}

---屬性 永丹/晶片篩選 晶片完成度
EChipFilterChipComplete =
{
    ---全部
    AllChip = 1,
    ---已完成晶片
    CompletedChip = 2,
    ---未完成晶片
    NotCompletedChip= 3,
}

--- 一次購買背包的數量
BAG_BUY_TIMES = 5;

--- 背包種類Enum
EBagType =
{
    --- 頁簽1 裝備
    Equipment = 1,
    --- 頁簽2 藥水
    Potion = 2,
    --- 頁簽3 消耗品 ( 藥水以外 )
    Consumables = 3,
    --- 頁簽4 材料/素材 (額外記數永標區間 1501~3500)
    Component= 4,
}

--- 背包擴充永丹流水號
--BAG_BAG_1 = 46
--BAG_BAG_2 = 47
--BAG_BAG_3 = 48
--BAG_BAG_4 = 49
--BAG_BAG_5 = 50
--- 背包擴充永丹流水號
BAG_EXPANSION_FLAG =
{
    [EBagType.Equipment] = 46,
    [EBagType.Potion] = 47,
    [EBagType.Consumables] = 48,
    [EBagType.Component] = 49,
}

--- 背包共用視窗開啟號
BAG_COMMONQUERY_IDX =
{
    [EBagType.Equipment] = 533,
    [EBagType.Potion] = 534,
    [EBagType.Consumables] = 535,
    [EBagType.Component] = 536,

}

--- 背包介消開啟號(協定種類)
---@type EUISellProtocolType
BAG_SELL_IDX = 5

---隊伍
---物品分配模式
ETeamItemDistributionType =
{
    ---各自拾取 玩家指會獲得自己有時取權的東西
    PickSelf = 1,
    ---隊伍隨機 隊友有拾取權的物品 由系統隨機分配
    TeamRandom = 2,
}

---隊伍邀請方式
ETeamInviteType =
{
    ---只有隊長能邀請
    OnlyLeaderInvite = 1,
    ---所有人都可以邀請
    AllInvite = 2,
}

---隊伍隊員修改訊息
ETeamRenewInfoType =
{
    ---更新HP 訊息
    TeamateHP = 1,
    ---更新buff狀態
    TeamateBuff = 2,
    ---更新隊員等級(理論上只會升級)
    TeamateLevel = 3,
    ---更新隊員場景ID
    TeammateSceneID = 4,
    --- 更新在線狀況
    TeammateOnline = 5,
    ---更新心性
    TeammateHeart = 6,
    ---更新MP
    TeammateMP = 7,

    --招募組隊
    ---準備
    RecruitReady = 8,
}

ERecruitType =
{
    ---招募隊伍
    Recruit = 1,
    ---快速組對
    Quick = 2
}
---探索日誌獎勵
EExploreReward = 
{
    ---1.時空晶鑽
    Money = 1,
    --- 2.物品
    Item = 2,

}

--- 任務狀態
EMissionStatus = 
{
    Non = 0,
    CanAccept = 1,
    Undone = 2,
    CanComplete = 3,
    NoLevel = 8,
    NoCondition = 9,
    NeedToFinish = 10,
    Done = 11,
}

--- 任務獎勵種類ID
EMissionPrizeKind = 
{
    None            = 0,    --- 沒東西
    BuddhismPoint   = 1,    --- 佛點數
    TaoistPoint     = 2,    --- 道點數
    DevilPoint      = 3,    --- 魔點數
    Money           = 4,    --- 金錢
    Item            = 5,    --- 物品
    BattlePoint     = 6,    --- 戰技點
    FamePoint       = 7,    --- 名譽
    Exp             = 8,    --- 經驗值
    StaticFlag      = 9,    --- 永標
    MoveFlag        = 10,   --- 動標狀態
    ExplorePoint    = 11,   --- 探索積分
    SelfPickItem    = 12    --- 自選物品
}

---0.一般進場 1.取消隱身進場 2:地圖傳送進場
---@class ETeleportInType
ETeleportInType = 
{
    None = 0,
    ShowUpFromSneak = 1,
    MapTeleport = 2,
}

--- 自發光外框
---@class EGlowType
EGlowType =
{
    --- 無狀態
    NONE = 0,
    --- 假玩家發光
    FAKEPLAYER = 1,
    ---九尾特效發光
    PLAYER_BUFF_EFFECT = 2,
}

--- 寵物種類
---@class EPetType
EPetType =
{
    --- 擬態生物(怪物或野獸外型)
    MONSTER = 1,

    --- 仿生人類(人形)
    HUMAN = 2,

    --- 神秘(很難分類的放這邊)
    MYSTIC = 3,
}
--- 寵物能力標題
---@class EPetAttributeType
EPetAttributeType =
{
    --- 自律者屬性
    BasicAbility = 1,
    --- 隨機主動技一覽
    RandomActiveSkill = 2,
    --- 隨機被動技一覽
    RandomPassiveSkill = 3,
    --- 四星常駐效果
    FourStarBuff = 4,
    --- 五星收藏效果
    FiveStarAward = 5,
    --- 自律者技能
    RealPetSkill = 6,
}

--- 寵物最大星數
PetMaxStar = 5

--- 寵物派遣可送最大寵物數量
PetSendMissionMax = 3

--- 寵物星等的色碼
PetStarColor =
{
    --- 星亮的色碼
    STAR_LIGHT = "#05fffb",
    --- 星黑的色碼
    STAR_DARK = "#17425e"
}

--- 寵物列表中分別種類
---@class EPetDataType
EPetDataType = 
{
    --- 道具
    ITEM = 1,

    --- 寵物
    PET = 2,
}

---特別聚焦玩家的鏡頭分類
ECMPlayerEffect =
{
    Teleport = 1,
    LearnWugongBack = 2,
    LearnWugongTeleport = 3,
}

EGameDataVersion =
{
    AnyVersion = 0,

    IOS = 1,
    Android = 2,
    Mycard = 3,
    SkyDragon = 4,
    Other = 5,

    IOS_REVIEW = 6,
    Nine_Card = 35,
    Playmall = 80,
    Steam = 90,
}
---儲值類別
EPurchaseKind =
{
    --- 一般儲值
    NormalPurchase = 0,

    --- 限時儲值
    LimitedPurchase = 1,

    --- 單次儲值
    OncePurchase = 2,

    --- 永久VIP
    PermanentVIP = 3,

    --- 限時VIP(月卡)
    MonthCard = 4,

    --- 一般禮包
    NormalGiftpack = 5,

    --- 現時禮包
    LimitedGiftpack = 6,

    --- 單次禮包
    OnceGiftpack = 7
}

---商品類別
---@class EProductKind
EProductKind =
{
    ---限時
    Timing      = 1,
    ---禮包
    Backage     = 2,
    ---時裝
    Fashion     = 3,
    ---異獸
    Brute       = 4,
    ---物資
    Tackle      = 5,
    ---裝備
    Equip       = 6,
    ---儲值
    Deposit     = 7,
    ---超值包
    Treasure    = 8,
    ---
    Card        = 9
}
---優惠類型
EOnSellType = {
    ---折扣
    Discount = 0,
    ---增值
    Extra = 1
}
--- 次數限制 標記類型 [ 0=無 1=個人計數永標(日刪) 2=個人計數永標(周刪) 3=個人永標(限單次購買) 4=個人計數永標(月刪) ]
---@class EStoreLimitType
EStoreLimitType = {
    None        = 0,
    EveryDay    = 1,
    EveryWeek   = 2,
    OnlyOnce    = 3,
    EveryMonth  = 4,
}
---商城切換
EStoreSwitch = {
    ---商店
    Store    = 121,
    ---金流
    CashFlow = 126,
}
---商城區塊
---@class EStorePnl
EStorePnl = {
    ---商城單項
    Pnl1 = 1,
    ---禮包類型
    Pnl2 = 2,
    ---金流單項
    Pnl3 = 3,
}
---商城交易幣別
---@class EStoreCurrency
EStoreCurrency = {
    None    = 0,
    --- 時空幣(鑽石)
    Diamond = 1,
    --- 黃易幣
    HEMCoin = 2,
    --- 遊戲幣
    Money   = 3,
    --- 免費
    Free    = 9
}
---幣值圖
ECurrencyIcon = {
    [EStoreCurrency.None] = "",             --None
    [EStoreCurrency.Diamond] = "Icon111002",   --Diamond
    [EStoreCurrency.HEMCoin] = "Icon111001",   --HEMCoin
    [EStoreCurrency.Money] = "Icon111000",   --Money
    [EStoreCurrency.Free] = "",
}
---福袋獎勵類型
EFuDaiRewardType = {
    Item        = 1,
    Furniture   = 2,
    Pet         = 3,
}

EPetStrengthen = 
{
    ATTACK = 1,
    ATKSPEED = 2,
    CRITICAL = 3,
    SKILL1 = 4,
    SKILL2 = 5,
}

---鍵盤按鈕狀態
EKeyCodeState =
{
    KeyDown = "KeyDown",
    KeyPress = "KeyPress",
    KeyUp = "KeyUp",
}

---這裡定義使用者可以用的鍵盤按鈕
---TODO 整段須改為TextData
EAvailableKeyCode = 
{
    -- 未指定（永遠不會作為按鍵結果返回）
    None = "未指定",

    -- Tab 鍵
    Tab = "Tab",

    -- Clear 鍵
    Clear = "清除鍵",

    -- Return 鍵
    Return = "返回鍵",

    -- 暫停鍵 (PC機)
    Pause = "暫停鍵",

    -- 空白鍵
    Space = "空白鍵",

    -- 驚嘆號 '!' 鍵
    Exclaim = "!",

    -- 井字 '#' 鍵
    Hash = "#",

    -- 美元符號 '$' 鍵
    Dollar = "$",

    -- 百分比 '%' 鍵
    Percent = "%",

    -- AND 符號 '&' 鍵
    Ampersand = "&",

    -- 單引號 '\'' 鍵
    Quote = "'",

    -- 左括號 '(' 鍵
    LeftParen = "(", 

    -- 右括號 ')' 鍵
    RightParen = ")",

    -- 星號 '*' 鍵
    Asterisk = "*",

    -- 加號 '+' 鍵
    Plus = "+",

    -- 逗號 ',' 鍵
    Comma = ",",

    -- 減號 '-' 鍵
    Minus = "-",

    -- 句點 '.' 鍵
    Period = ".",

    -- 斜線 '/' 鍵
    Slash = "/",

    -- 數字 0 鍵
    Alpha0 = "數字 0",

    -- 數字 1 鍵
    Alpha1 = "數字 1",

    -- 數字 2 鍵
    Alpha2 = "數字 2",

    -- 數字 3 鍵
    Alpha3 = "數字 3",

    -- 數字 4 鍵
    Alpha4 = "數字 4",

    -- 數字 5 鍵
    Alpha5 = "數字 5",

    -- 數字 6 鍵
    Alpha6 = "數字 6",

    -- 數字 7 鍵
    Alpha7 = "數字 7",

    -- 數字 8 鍵
    Alpha8 = "數字 8",

    -- 數字 9 鍵
    Alpha9 = "數字 9",

    -- 冒號 ':' 鍵
    Colon = ":",

    -- 分號 ';' 鍵
    Semicolon = ";",

    -- 小於號 '<' 鍵
    Less = "<",

    -- 等號 '=' 鍵
    Equals = "=",

    -- 大於號 '>' 鍵
    Greater = ">",

    -- 問號 '?' 鍵
    Question = "?",

    -- at 符號 '@' 鍵
    At = "@",

    -- 左中括號 '[' 鍵
    LeftBracket = "[",

    -- 右中括號 ']' 鍵
    RightBracket = "]",

    -- 脫字符號 '^' 鍵
    Caret = "^",

    -- 底線 '_' 鍵
    Underscore = "_",

    -- 反引號 '`' 鍵
    BackQuote = "`",

    -- 'a' 鍵  
    A = "按鍵 A",

    -- 'b' 鍵  
    B = "按鍵 B",

    -- 'c' 鍵  
    C = "按鍵 C",

    -- 'd' 鍵  
    D = "按鍵 D",

    -- 'e' 鍵  
    E = "按鍵 E",

    -- 'f' 鍵  
    F = "按鍵 F",

    -- 'g' 鍵  
    G = "按鍵 G",

    -- 'h' 鍵  
    H = "按鍵 H",

    -- 'i' 鍵  
    I = "按鍵 I",

    -- 'j' 鍵  
    J = "按鍵 J",

    -- 'k' 鍵  
    K = "按鍵 K",

    -- 'l' 鍵  
    L = "按鍵 L",

    -- 'm' 鍵  
    M = "按鍵 M",

    -- 'n' 鍵  
    N = "按鍵 N",

    -- 'o' 鍵  
    O = "按鍵 O",

    -- 'p' 鍵  
    P = "按鍵 P",

    -- 'q' 鍵  
    Q = "按鍵 Q",

    -- 'r' 鍵  
    R = "按鍵 R",

    -- 's' 鍵  
    S = "按鍵 S",

    -- 't' 鍵  
    T = "按鍵 T",

    -- 'u' 鍵  
    U = "按鍵 U",

    -- 'v' 鍵  
    V = "按鍵 V",

    -- 'w' 鍵  
    W = "按鍵 W",

    -- 'x' 鍵  
    X = "按鍵 X",

    -- 'y' 鍵  
    Y = "按鍵 Y",

    -- 'z' 鍵  
    Z = "按鍵 Z",

    -- 左大括號 '{' 鍵
    LeftCurlyBracket = "{",

    -- 管線符號 '|' 鍵
    Pipe = "|",

    -- 右大括號 '}' 鍵
    RightCurlyBracket = "}",

    -- 波浪號 '~' 鍵
    Tilde = "~",

    -- 刪除鍵 (forward delete)
    Delete = "刪除鍵",

    -- 數字鍵盤 0  
    Keypad0 = "數字鍵盤 0",

    -- 數字鍵盤 1  
    Keypad1 = "數字鍵盤 1",

    -- 數字鍵盤 2  
    Keypad2 = "數字鍵盤 2",

    -- 數字鍵盤 3  
    Keypad3 = "數字鍵盤 3",

    -- 數字鍵盤 4  
    Keypad4 = "數字鍵盤 4",

    -- 數字鍵盤 5  
    Keypad5 = "數字鍵盤 5",

    -- 數字鍵盤 6  
    Keypad6 = "數字鍵盤 6",

    -- 數字鍵盤 7  
    Keypad7 = "數字鍵盤 7",

    -- 數字鍵盤 8  
    Keypad8 = "數字鍵盤 8",

    -- 數字鍵盤 9  
    Keypad9 = "數字鍵盤 9",

    -- 數字鍵盤 '.'  
    KeypadPeriod = "數字鍵盤 .",

    -- 數字鍵盤 '/'  
    KeypadDivide = "數字鍵盤 /",

    -- 數字鍵盤 '*'  
    KeypadMultiply = "數字鍵盤 *",

    -- 數字鍵盤 '-'  
    KeypadMinus = "數字鍵盤 -",

    -- 數字鍵盤 '+'  
    KeypadPlus = "數字鍵盤 +",

    -- 數字鍵盤 Enter  
    KeypadEnter = "數字鍵盤 Enter",

    -- 數字鍵盤 '='  
    KeypadEquals = "數字鍵盤 =",

    -- 上箭頭鍵
    UpArrow = "上箭頭",

    -- 下箭頭鍵
    DownArrow = "下箭頭",

    -- 右箭頭鍵
    RightArrow = "右箭頭",

    -- 左箭頭鍵
    LeftArrow = "左箭頭",

    -- 插入鍵
    Insert = "插入鍵",

    -- Home 鍵
    Home = "Home 鍵",

    -- End 鍵
    End = "End 鍵",

    -- Page Up 鍵
    PageUp = "上一頁鍵",

    -- Page Down 鍵
    PageDown = "下一頁鍵",

    -- F1  
    F1 = "F1",

    -- F2  
    F2 = "F2",

    -- F3  
    F3 = "F3",

    -- F4  
    F4 = "F4",

    -- F5  
    F5 = "F5",

    -- F6  
    F6 = "F6",

    -- F7  
    F7 = "F7",

    -- F8  
    F8 = "F8",

    -- F9  
    F9 = "F9",

    -- F10  
    F10 = "F10",

    -- F11  
    F11 = "F11",

    -- F12  
    F12 = "F12",

    -- F13  
    F13 = "F13",

    -- F14  
    F14 = "F14",

    -- F15  
    F15 = "F15",

    -- 右 Shift 鍵
    RightShift = "右 Shift 鍵",

    -- 左 Shift 鍵
    LeftShift = "左 Shift 鍵",

    -- 右 Control 鍵
    RightControl = "右 Control 鍵",

    -- 左 Control 鍵
    LeftControl = "左 Control 鍵",

    -- 右 Alt 鍵
    RightAlt = "右 Alt 鍵",

    -- 左 Alt 鍵
    LeftAlt = "左 Alt 鍵",


}

--- 寵物派遣狀態
---[0.未派遣 1.派遣中 2.派遣完成]
EMissionMissionState = {
    Free = 0,
    Working = 1,
    CollectAble = 2,
}

--- 交換物的獲得種類
EExchangeCardDataTYPE = 
{
    None 	= 0,
    Number 	= 1,
    Upgrade = 2,
}

---按鈕觸發型態字尾
EButtonTriggerTypeSuffix =
{
    Normal = "_D",
    Highlighted = "_H",
    Pressed = "_S",
    Disabled = "_L",
    Selected = "_A",

}

--- 其他類型物品外框
EOtherItemIconFrameType =
{
    --- 無
    None = 0,
    --- 自動使用
    AutoUse = 1,
}