﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---@class InnerMeridianData
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.5.22
InnerMeridianData = {}
InnerMeridianData.__index = InnerMeridianData

local this = InnerMeridianData

--region 串檔 coroutine 專區

--- 需要等待的串檔
this.m_NeedWaitData = { }

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

--- 串檔總數
this.m_DataCount = 0

--- 新串了多少串檔
this.m_NewDataCount = 0

---Stream讀了多少次
this.m_StreamReadByteTimes = 0

--endregion 串檔 coroutine 專區
this.m_ASSET_NAME = "InnerMeridian_C"
local m_Dic = {}

function InnerMeridianData:New(iReader)
    ---@type InnerMeridianData
    local _Data = {}
    setmetatable ( _Data, InnerMeridianData )

    --- 流水號
    ---@type byte
    _Data.m_ID = iReader:ReadByte()
    --- 靈脈編號
    ---@type byte
    _Data.m_Num = iReader:ReadByte()
    --- 靈脈階段
    ---@type byte
    _Data.m_Step = iReader:ReadByte()
    --- 貫通靈氣消耗基礎值
    ---@type ushort
    _Data.m_AuraCost = iReader:ReadUInt16()
    --- 貫通靈氣消耗穴位加乘值
    ---@type ushort
    _Data.m_AuraCostAdd = iReader:ReadUInt16()
    --- 靈脈進階需求靈氣值
    ---@type ushort
    _Data.m_NeedCount = iReader:ReadUInt16()
    --- 穴位效果
    ---@type Effect
    _Data.m_Effect = {}
    for i = 1, 3 do
        _Data.m_Effect[i] = {}
        ---穴位效果屬性修正值
        ---@type ushort
        _Data.m_Effect[i].m_AttrFix = iReader:ReadUInt16()
        ---穴位效果數值
        ---@type ushort
        _Data.m_Effect[i].m_AttrValue = iReader:ReadUInt16()
    end
    --- 心法屬性加成屬性修正值
    ---@type ushort
    _Data.m_MethodAttrFix = iReader:ReadUInt16()
    --- 心法屬性加成數值
    ---@type ushort
    _Data.m_MethodAttrValue = iReader:ReadUInt16()

    return _Data.m_Num, _Data
end

---初始化
function InnerMeridianData.Init()
    return DataReader.LoadFile(this.m_ASSET_NAME, InnerMeridianData.OnLoadData)
end

---讀檔
function InnerMeridianData.OnLoadData(iFile)
    local _Reader = DataReader.New(iFile)
    this.m_DataCount = _Reader:ReadUInt32()
    DataMgr.NewData(this, _Reader, nil, InnerMeridianData.FunctionAddSingleDataByIndex, InnerMeridianData.DoAfterDictionaryInitialized)
end

---外掛串表讀檔
function InnerMeridianData.OnLoadExTableData(iIdx , iData)
    return iData
end

---取得InnerMeridianData
---@param m_ID InnerMeridianDatam_ID
---@return InnerMeridianData
function InnerMeridianData.GetInnerMeridianDataByIdx(iIdx)
    if iIdx > 0 and m_Dic[iIdx] ~= nil then
        return m_Dic[iIdx]
    else
        D.Log("Cant Find InnerMeridianData m_ID: " .. iIdx)
        return nil
    end
end

---============分隔線以上為自動填入區段，功能請勿於此線以上撰寫================
---======================== I am Spliter! ===============================

function InnerMeridianData.FunctionAddSingleDataByIndex(iCountIndex, iDataIndex, iData)
    if m_Dic[iData.m_Num] == nil then
        m_Dic[iData.m_Num] = {}
    end
    table.insert(m_Dic[iData.m_Num], iData)
end

---靈脈穴位原點及方向設定 方向 1 = 上、2 = 右、3 = 下、4 = 左
this.m_AcuSetting = {
    [1] = {m_StartPoint = Vector3(-127, 488, 0), m_Direction = {2, 3, 3, 4, 3, 2, 3, 3}},
    [2] = {m_StartPoint = Vector3( 156, 488, 0), m_Direction = {4, 3, 3, 2, 3, 4, 3, 3}},
    [3] = {m_StartPoint = Vector3(-127,  86, 0), m_Direction = {4, 1, 4, 3, 3, 3, 4, 3}},
    [4] = {m_StartPoint = Vector3( 158,  86, 0), m_Direction = {2, 1, 2, 3, 3, 3, 2, 3}},
    [5] = {m_StartPoint = Vector3(-26, -114, 0), m_Direction = {4, 3, 3, 3, 4, 1, 4, 3}},
    [6] = {m_StartPoint = Vector3( 53, -114, 0), m_Direction = {2, 3, 3, 3, 2, 1, 2, 3}},
}

---靈脈穴位座標
this.m_AcuCoordinateData = {}

--- 初始化後處理
function InnerMeridianData.DoAfterDictionaryInitialized()
    local _Spacing = 103
    --設定穴位座標
    for k, v in pairs(this.m_AcuSetting) do
        this.m_AcuCoordinateData[k] = {m_Point = {}, m_Line = {}, m_Rota = {}}
        table.insert(this.m_AcuCoordinateData[k].m_Point, v.m_StartPoint)
        local _Coordinate = v.m_StartPoint
        for _k, _v in pairs(v.m_Direction) do
            if _v == 1 then
                _Coordinate = _Coordinate + Vector3(0, _Spacing, 0)
            elseif _v == 2 then
                _Coordinate = _Coordinate + Vector3(_Spacing, 0, 0)
            elseif _v == 3 then
                _Coordinate = _Coordinate + Vector3(0, -_Spacing, 0)
            elseif _v == 4 then
                _Coordinate = _Coordinate + Vector3(-_Spacing, 0, 0)
            end
            table.insert(this.m_AcuCoordinateData[k].m_Point, _Coordinate)
        end
    end

    for k, v in pairs(this.m_AcuCoordinateData) do
        for i = 1, table.Count(v.m_Point) do
            if i <= 8 then
                --設定連接線座標
                local _Point1 = v.m_Point[i]
                local _Point2 = v.m_Point[i + 1]
                local _MidX = (_Point1.x + _Point2.x) * 0.5
                local _MidY = (_Point1.y + _Point2.y) * 0.5
                table.insert(v.m_Line, Vector3(_MidX, _MidY, 0))

                --設定連接線旋轉
                local _RotationZ = 0
                if _Point1.x == _Point2.x then
                    _RotationZ = 90
                elseif _Point1.y == _Point2.y then
                    _RotationZ = 0
                end
                table.insert(v.m_Rota, Vector3(0, 0, _RotationZ))
            end
        end
    end
end