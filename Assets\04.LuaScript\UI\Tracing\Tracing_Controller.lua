---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2023 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("UI/Tracing/Tracing_Model")

---處理戰鬥文字控制
---@class Tracing_Controller
---author WereHsu
---version 1.0
---since [黃易群俠傳M] 0.90
---date 2023.09.25
Tracing_Controller = {}
local this = Tracing_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Tracing_View", "Tracing_Controller", EUIOrderLayers.HalfPage_Center)

---隊員最多顯示6個buff
this.MAX_TEAMMATE_SHOW_BUFF_COUNT = 6
---BuffIcon 的物件使用名稱
this.ICON_OBJ_NAME = "Icon_"

---隊伍設定介面 目前Type 底圖顏色
local TeamSetting_UseColor = Extension.GetColor("#AA6F3D")
---隊伍設定介面 非目前Type 底圖顏色
local TeamSetting_NonUseColor = Extension.GetColor("#496378")
---隊伍設定介面 目前Type TMP風格
local TeamSetting_UseStyle = "YWS"
---隊伍設定介面 非目前Type TMP風格
local TeamSetting_NonUseStyle = "W"

---組隊邀請權限使用字串ID
---只有隊長
local ONLY_LEADER_INVITE_StrID = 520902
---任何人
local ALL_INVITE_StrID = 520903

---隊伍物品分配方式字串ID
---各自
local ITEM_DISTRIBUTION_PICKSELF_StrID = 520905
---隨機分配
local ITEM_DISTRIBUTION_RANDOM_StrID = 520906
---自己按離開組隊時 使用的CommonQuery編號
local LEAVETEAM_COMMONQUERYID = 351

--- 任務可完成時出現在 tmp 的小圖大小
local FITICONSIZE = 45

--- 任務可完成 Icon 會歪掉 調整高低
local TEXTOFFSET = -0.4

--- 星星黃色
local StarGoldColor = Extension.GetColor("#FFC600")

--- 星星灰色
local StarGrayColor = Extension.GetColor("#0D3B62")

local m_NowRaid = {}

---前端抓到的UI放這裡
local function InitialUI()
    this.m_GObjPageGroupButton = GroupButton.New(this.m_ViewRef.m_Dic_Trans:Get("&Group_PageButtons").gameObject)
    for i = 1, GroupButton.GetCount(this.m_GObjPageGroupButton) do
        GroupButton.AddListenerByIndex(this.m_GObjPageGroupButton, i, EventTriggerType.PointerClick, function()
            this.OnPageButtonClick(i)
        end)
    end

    this.m_GroupListPanel = this.m_ViewRef.m_Dic_UIAnimation:Get("&Group_ListPanels")
    this.m_FoldButton = Button.New(this.m_ViewRef.m_Dic_ButtonEx:Get("&Button_Fold"))
    Button.AddListener(this.m_FoldButton, EventTriggerType.PointerClick, function()
        this.m_GroupListPanel:DoActive(Tracing_Model.m_isFoldPanel)
        Tracing_Model.m_isFoldPanel = not Tracing_Model.m_isFoldPanel
    end)

    --region 任務相關UI

    this.m_MissionListUnit = this.m_ViewRef.m_Dic_Trans:Get("&Unit_MissionList").gameObject
    --- 群組 追蹤中的任務
    this.m_Group_TracingMission = this.m_ViewRef.m_Dic_Trans:Get("&Group_Tracing_Mission")
    --- 群組 可以接的任務
    this.m_Group_CanGetMission = this.m_ViewRef.m_Dic_Trans:Get("&Group_CanGet_Mission")
    --- 群組內的內容 實際任務物件丟這裡面(動畫用所以包在群組內)
    this.m_Content_CanGetMission = this.m_ViewRef.m_Dic_UIAnimation:Get("&Content_CanGet_Mission")
    --- 包含上述群組的物件 (因為Size Fitter有時有問題 要在update做一次重刷)
    this.m_Group_ContentMissionList = this.m_ViewRef.m_Dic_Trans:Get("&Content_MissionList")

    ---因為Size Fitter有時有問題 要用ContentFitterImmediate做一次重刷
    this.m_ContentFitterImmediate_ContentMissionList = this.m_Group_ContentMissionList:GetComponent(typeof(ContentFitterImmediate))


    this.m_MissionListUnit.gameObject:SetActive(false)
    this.m_GObj_MissionList = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_MissionList")

    --- 摺疊可接任務按鈕
    this.m_FoldCanGetButton = this.m_ViewRef.m_Dic_ButtonEx:Get("&ButtonTitle_CanGetMission")
    Button.AddListener(this.m_FoldCanGetButton, EventTriggerType.PointerClick, function()
        this.m_Content_CanGetMission:DoActive(Tracing_Model.m_IsFoldCanGetMission)
        Tracing_Model.m_IsFoldCanGetMission = not Tracing_Model.m_IsFoldCanGetMission
    end)

    --- 任務物件的物件池
    this.m_GObjPool_MissionUnit = Extension.CreatePrefabObjPool(this.m_MissionListUnit,Vector3.New(0, 0, 0), Quaternion.Euler(0, 0, 0))

    --- 副本任務整組
    this.m_GroupRaidMissionUnit = this.m_ViewRef.m_Dic_Trans:Get("&Group_RaidMissionStars").gameObject

    --- 副本任務訊息
    this.m_Unit_RaidMission = this.m_ViewRef.m_Dic_Trans:Get("&Unit_RaidMission")

    --- 副本任務標題
    this.m_RaidTitle = this.m_Unit_RaidMission.transform:Find("img_listTitle").transform:Find("TMP_RaidName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))

    --- 副本任務內容
    this.m_RaidDetail = this.m_Unit_RaidMission.transform:Find("Text_RaidDetail").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

    --- 展開地址
    this.m_Group_Stars = this.m_ViewRef.m_Dic_Trans:Get("&Group_Star_Group")

    --- 裝 content
    this.m_Content_Star = this.m_Group_Stars.transform:Find("&Content_Star_Group")

    --- ContentAnimation
    this.m_Content_Animation = this.m_ViewRef.m_Dic_UIAnimation:Get("&Content_Star_Group")

    --- 副本下展按鈕
    this.m_RaidFoldButton = this.m_ViewRef.m_Dic_Trans:Get("&ButtonTitle_RaidStars")
    Button.AddListener(this.m_RaidFoldButton, EventTriggerType.PointerClick, function()
        this.m_Content_Animation:DoActive(Tracing_Model.m_IsFoldRaid)
        Tracing_Model.m_IsFoldRaid = not Tracing_Model.m_IsFoldRaid
    end)

    --- 副本星星
    this.m_Star = {}
    for i = 1, 3 do

        local _StarStr = "RaidStar".. i
        this.m_Star[i] = {}
        this.m_Star[i].Image = this.m_Content_Star.transform:Find(_StarStr).transform:Find("Img_Star").gameObject:GetComponent(typeof(Image))
        this.m_Star[i].Detail = this.m_Content_Star.transform:Find(_StarStr).transform:Find("Tmp_StarCondition").gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))

    end

    --endregion

    --region 原本要做篩選列表
    -- this.m_GObjSearchGroupButton = this.m_ViewRef.m_Dic_Trans:Get("&Group_SearchType").gameObject
    -- for i = 1, GroupButton.GetCount(this.m_GObjSearchGroupButton) do
    --     GroupButton.AddListenerByIndex(this.m_GObjSearchGroupButton,i,EventTriggerType.PointerClick,function()
    --         this.OnSearchTypeClick(i)
    --     end)
    -- end
    --endregion


    --- 隊伍相關
    this.m_GObj_TeamPage = this.m_ViewRef.m_Dic_Trans:Get("&TeamPage")

    ---物件 - 隊伍成頁的分頁
    this.m_GObj_TeamMemberPage = this.m_ViewRef.m_Dic_Trans:Get("&GObj_TeamMemberPage")

    ---物件 - 單一隊伍成員資訊物件
    this.m_GObj_PlayerINfoUnit = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_Player")

    ---物件 - 隊伍設定的分頁
    this.m_GObj_TeamSettingPage = this.m_ViewRef.m_Dic_Trans:Get("&GObj_TeamSettingPage").gameObject

    ---按鍵 - 關閉隊伍設定分頁
    this.m_Btn_CloseSettingPanel = this.m_ViewRef.m_Dic_Trans:Get("&Btn_CloseSettingPanel")
    Button.AddListener(this.m_Btn_CloseSettingPanel.gameObject, EventTriggerType.PointerClick,function()
        Tracing_Controller.RenewTeamMemberData()
        Tracing_Controller.ShowTeamSettingPanel(false)
    end)

    ---按鍵 - 邀請權限設為 僅隊長
    this.m_Btn_InviteCondition_LeaderOnly = this.m_ViewRef.m_Dic_Trans:Get("&Btn_InviteCondition_LeaderOnly")
    Button.AddListener(this.m_Btn_InviteCondition_LeaderOnly.gameObject, EventTriggerType.PointerClick,function()
        Tracing_Controller.SetInviteQualify(ETeamInviteType.OnlyLeaderInvite)
    end)
    ---按鍵 - 邀請權限設為 所有成員
    this.m_Btn_InviteCondition_AnyOne = this.m_ViewRef.m_Dic_Trans:Get("&Btn_InviteCondition_AnyOne")
    Button.AddListener(this.m_Btn_InviteCondition_AnyOne.gameObject, EventTriggerType.PointerClick,function()
        Tracing_Controller.SetInviteQualify(ETeamInviteType.AllInvite)
    end)


     ---按鍵 - 物品分配為 只能撿自己
     this.m_Btn_Item_SelfOnly = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Item_SelfOnly")
     Button.AddListener(this.m_Btn_Item_SelfOnly.gameObject, EventTriggerType.PointerClick,function()
        Tracing_Controller.SetItemDistributionType(ETeamItemDistributionType.PickSelf)
    end)
     ---按鍵 - 物品分配為 隨機分配
     this.m_Btn_Item_Random = this.m_ViewRef.m_Dic_Trans:Get("&Btn_Item_Random")
     Button.AddListener(this.m_Btn_Item_Random.gameObject, EventTriggerType.PointerClick,function()
        Tracing_Controller.SetItemDistributionType(ETeamItemDistributionType.TeamRandom)
    end)


    ---按鍵 - 離開隊伍
    this.m_Btn_LeaveTeam = this.m_ViewRef.m_Dic_Trans:Get("&Btn_LeaveTeam")
    Button.AddListener(this.m_Btn_LeaveTeam.gameObject, EventTriggerType.PointerClick,function()

            CommonQueryMgr.AddNewInform(LEAVETEAM_COMMONQUERYID,{},{},
            function()
                local _Packet = {}
                SendProtocol_011._001(6,_Packet)
            end,{})
        end)

    ---按鍵 - 開啟隊伍設定分頁
    this.m_Btn_TeamSetting = this.m_ViewRef.m_Dic_Trans:Get("&Btn_TeamSetting")
    Button.AddListener(this.m_Btn_TeamSetting.gameObject, EventTriggerType.PointerClick,function()
            Tracing_Controller.RenewTeamSettingData()
            Tracing_Controller.ShowTeamSettingPanel(true)
        end)

    ---按鍵 - 開啟招募介面
    this.m_Btn_InvitePanel = this.m_ViewRef.m_Dic_Trans:Get("&Btn_InvitePanel")
    Button.AddListener(this.m_Btn_InvitePanel.gameObject, EventTriggerType.PointerClick,function()
            if SceneAttributeData.Get( SceneMgr.GetSceneID() ).m_OutBtnEnable > 0 then
                MessageMgr.AddCenterMsg(true, TextData.Get(9169))
            else
                UIMgr.Open(Recruit_Controller)
            end
        end)

    ---Transform 從隊伍介面點開互動介面時 互動按鍵面板放的位置 &InteractPanelPos_Team
    this.m_GObj_InteractPanelPos_Team = this.m_ViewRef.m_Dic_Trans:Get("&InteractPanelPos_Team")


end

---@type FlagID|MissionItem
local m_Table_MissionUnit = {}

local _isRefersh = false

---初始化左邊資訊列表
local function InitialScrollBar()
    this.m_SearchListUnit = this.m_ViewRef.m_Dic_Trans:Get("&Unit_SearchList")
    this.m_GObj_SearchList = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_SearchList")
    ---@type ScrollView
    this.m_ScrollView_SearchList =
        ScrollView.Init( this.m_GObj_SearchList.gameObject, false, this.m_SearchListUnit.gameObject, function() return 10  end, this.AfterReuseSearchInit , this.AfterReuseSearchIndexUpdate, false)
    this.m_GObj_SearchList.gameObject:SetActive(false)

end

local InitialPlayerDataPanel = function()

    ---隊伍 隊員資訊的介面UI table
    this.m_PlayerPanelTable = {}

    for i = 1, TEAMMATE_MAX_COUNT do

        local _ItemObject = nil
        -- 第一個 用本人
        if i == 1 then
            _ItemObject =  this.m_GObj_PlayerINfoUnit
        -- 其他 產生新的物件
        else
            _ItemObject =  this.m_GObj_PlayerINfoUnit:Instantiate( this.m_GObj_PlayerINfoUnit)
            _ItemObject:SetParent(this.m_GObj_TeamMemberPage)
        end
        _ItemObject:SetSiblingIndex(i-1)

        local _PlayerInfoUISet = {}
        ---玩家資訊面板物件本身
        _PlayerInfoUISet.m_gameObject = _ItemObject.gameObject

        ---這個UISet使用哪一個RoleID的玩家
        _PlayerInfoUISet.m_RoleID = nil
        ---有玩家時要開啟物件掛的parent
        _PlayerInfoUISet.m_Obj_WithMember = _ItemObject:Find("WithMemberSet")
        ---TMP 玩家名稱
        _PlayerInfoUISet.m_TMP_PlayerName = _PlayerInfoUISet.m_Obj_WithMember:Find("TMP_PlayerName").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI))
        --- Slider 玩家HP
        _PlayerInfoUISet.m_SliderBar_HP = _PlayerInfoUISet.m_Obj_WithMember:Find("SliderBar_HP").gameObject:GetComponent(typeof(Slider))
        --- Slider 玩家MP
        _PlayerInfoUISet.m_SliderBar_MP = _PlayerInfoUISet.m_Obj_WithMember:Find("SliderBar_MP").gameObject:GetComponent(typeof(Slider))
        --- 物件 掛bufficon 的位置
        _PlayerInfoUISet.m_Trans_BuffSet = _PlayerInfoUISet.m_Obj_WithMember:Find("BuffSet")
        --- TMP buff太多時顯示用的提示
        _PlayerInfoUISet.m_TMP_BuffCount =  _PlayerInfoUISet.m_Trans_BuffSet:Find("TMP_BuffCount").gameObject
        --- TMP 玩家等級
        _PlayerInfoUISet.m_TMP_PlayerLevel = _PlayerInfoUISet.m_Obj_WithMember:Find("TMP_PlayerLevel").gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI))

        _PlayerInfoUISet.m_BuffIcon = {}
        --- 物件 玩家已經按下準備
        _PlayerInfoUISet.m_Img_AlreadyPrepared =_PlayerInfoUISet.m_Obj_WithMember:Find("Img_AlreadyPrepared").gameObject

        --- 物件 此玩家為隊長
        _PlayerInfoUISet.m_Img_LeaderHint =_PlayerInfoUISet.m_Obj_WithMember:Find("Img_LeaderHint").gameObject

        ---沒玩家時要開啟的物件們掛的parent
        _PlayerInfoUISet.m_Obj_NoMemberSet = _ItemObject:Find("NoMemberSet")

        _PlayerInfoUISet.m_Btn_Recruit = Button.New(_PlayerInfoUISet.m_Obj_NoMemberSet:Find("Btn_Recruit").gameObject)
        Button.AddListener(_PlayerInfoUISet.m_Btn_Recruit, EventTriggerType.PointerDown, UIMgr.Open, Community_Controller)

        table.insert(this.m_PlayerPanelTable,_PlayerInfoUISet)
    end

    ---生成 bufff icon 跟 head icon
    for i = 1, TEAMMATE_MAX_COUNT do

        ---buff icon 最多塞6個
        for j = 1, this.MAX_TEAMMATE_SHOW_BUFF_COUNT do
            local _Icon = Extension.AddMissingComponent(GameObject.New(this.ICON_OBJ_NAME .. j),
                typeof(UnityEngine.RectTransform))
            local _GridLayoutGroup = this.m_PlayerPanelTable[i].m_Trans_BuffSet:GetComponent(typeof(GridLayoutGroup))
            this.m_PlayerPanelTable[i].m_BuffIcon[j] = IconMgr.NewBuffIcon(0, _Icon, _GridLayoutGroup.cellSize.x, nil)
            this.m_PlayerPanelTable[i].m_BuffIcon[j]:SetCountScale( IconSetting.m_BuffIconCountSize )
            _Icon:SetParent(this.m_PlayerPanelTable[i].m_Trans_BuffSet)
            _Icon.transform.localScale = Vector3.one
            this.m_PlayerPanelTable[i].m_BuffIcon[j].gameObject:SetActive(false)

            if j == this.MAX_TEAMMATE_SHOW_BUFF_COUNT then
                this.m_PlayerPanelTable[i].m_TMP_BuffCount.transform:SetSiblingIndex(j)
            end
        end

        --- 物件 掛頭像 的位置
        this.m_PlayerPanelTable[i].m_HeadParant = this.m_PlayerPanelTable[i].m_Obj_WithMember:Find("HeadParant")
        --- 物件 頭像icon
        this.m_PlayerPanelTable[i].m_HeadIcon = IconMgr.NewHeadIcon(0, this.m_PlayerPanelTable[i].m_HeadParant,92,
    function()
                    if this.m_PlayerPanelTable[i].m_RoleID  then
                        if Interact_Model.RelationCheck(this.m_PlayerPanelTable[i].m_RoleID,EOpenInteractPanelFrom.FromTeamUI) then
                            UIMgr.Open(Interact_Controller)
                        end
                    end
                end,{})
    end

end

--region ScrollView 任務相關列表
--- 重新設定所有任務物件 (過圖、追蹤變動、標記更新)
function Tracing_Controller.SetAllMission()
    local _count = Tracing_Model.GetMissionCount()

    for i = 1, _count do
        Tracing_Controller.SetMissionListRowItem(i)
    end

    ---超過的部分要回收
    if #m_Table_MissionUnit > _count then
        for i = _count + 1, #m_Table_MissionUnit do
            this.m_GObjPool_MissionUnit:Store(m_Table_MissionUnit[i].gameObject)
            m_Table_MissionUnit[i] = nil
        end
    end

    ---強迫重新設定追蹤中任務 的物件高度
    LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Group_TracingMission)

    ---非初始化要重刷框框大小
    _isRefersh = true

end

--- 副本任務
---@param number iId 副本ID
function Tracing_Controller.SetRaidMissionItem(iId)

    -- 這 Id 能不能用
    local _IsIdUseable = iId ~= 0 and iId ~= nil

    -- 開還是關
    this.m_GroupRaidMissionUnit:SetActive(_IsIdUseable)

    -- 這東西不可用
    if(not _IsIdUseable) then
        return
    end

    -- 取副本資料
    local _RaidData = {}
    _RaidData = CampaignData.GetCampaignDataBym_Idx(iId)
    local _PlotData = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DBOOK, _RaidData.m_PlotDynamicFlag)

    --- 設定副本任務標題
    this.m_RaidTitle.text = _PlotData ~= nil and EventStrAll:GetText(_PlotData.TitleStrID) or ""

    --- 設定副本任務細節
    m_NowRaid = _PlotData
    Tracing_Controller.UpdateRaidDetail(_RaidData.m_PlotDynamicFlag)

    local _FlagsNum = PlayerData_Flags[EFlags.StaticNum].Get(_RaidData.m_HighestStarStaticFlag)

    --- 設定星星條件
    for i = 1, 3 do

        local _Str = TextData.Get(_RaidData.m_StarInfo[i].m_String)
        this.m_Star[i].Detail.text = _Str

        if bit.band(_FlagsNum, bit.lshift(1, i - 1)) ~= 0 then
            this.m_Star[i].Image.color = StarGoldColor
        else
            this.m_Star[i].Image.color = StarGrayColor
        end

    end

    --- 展開地址
    this.m_Group_Stars = this.m_ViewRef.m_Dic_Trans:Get("&Group_Star_Group")

    ---非初始化要重刷框框大小
    _isRefersh = true

end

---@param number iId 動標ID
function Tracing_Controller.UpdateRaidDetail(iId)

        --檢查當前主動標的狀態
        local _Status = PlayerData.GetMovingFlag(iId).m_Status

        --取得當前步驟ID
        local _Index = (m_NowRaid.StepBegin + _Status) - 1
        local _Step = DPlotData.GetData(DPlotData.ASSET_NAME.ASSET_NAME_DSTEP,_Index)

        --- 設定副本任務細節
        this.m_RaidDetail.text = _Step ~= nil and EventStrAll:GetText(_Step.TipStrID) or ""

        local _RaidTitleBtn = Button.New(this.m_Unit_RaidMission)

        Button.ClearListener(_RaidTitleBtn)
        Button.AddListener(_RaidTitleBtn, EventTriggerType.PointerClick, function()
            SearchMgr.OnClickMissionData(MissionMgr.GetMissionByFlagId(iId))
        end)

end

---設定任務列表資訊
function Tracing_Controller.SetMissionListRowItem(iIdx)

    local _MissionData = Tracing_Model.m_MissionList[iIdx]

    if _MissionData == nil then
        return
    end

    if(_MissionData.m_bTrack ~= true and _MissionData.Finish ~= EventTipType.CanAccept) then
        return
    end

    local _Unit
    local _MissionUnit = {}

    if m_Table_MissionUnit[iIdx] then
        _MissionUnit = m_Table_MissionUnit[iIdx]
    else
        _Unit = this.m_GObjPool_MissionUnit:Get()
        _MissionUnit.gameObject = _Unit
        _MissionUnit.MainButton = Button.New(_Unit)
        _MissionUnit.Title = Button.New(_Unit.transform:Find( "img_listTitle"))
        _MissionUnit.ClassityIcon = _MissionUnit.Title.transform:Find( "Icon_Classity" ):GetComponent(typeof(Image))
        _MissionUnit.m_MissionDetails = _Unit.transform:Find( "Text_MissionDetail" )
        if _MissionUnit.m_MissionDetails then
            _MissionUnit.m_MissionDetails = _MissionUnit.m_MissionDetails.gameObject:GetComponent(typeof( TMPro.TextMeshProUGUI ))
        end
    end

    --設定物件名稱
    _MissionUnit.gameObject.name = "Mission_".._MissionData.FlagID

    --設定任務種類Icon
    if MissionMgr.MissionClassityIcon[_MissionData.Classity] then
        SpriteMgr.Load(MissionMgr.MissionClassityIcon[_MissionData.Classity], _MissionUnit.ClassityIcon)
    --else
    --    D.LogError("Mission Classity Has No Icon, Classity = ".._MissionData.Classity)
    end


    --設定清單任務名稱
    if(_MissionData.Finish == EventTipType.CanComplete) then
        local _EmojiString = "Symbol_42"
        _EmojiString = GString.GetTMPEmoji(_EmojiString)
        _EmojiString = GString.GetTextWithSize(_EmojiString, FITICONSIZE)
        _EmojiString = GString.GetTextWithVoffset(_EmojiString, TEXTOFFSET)
        _MissionUnit.Title:SetText(EventStrAll:GetText(_MissionData.TitleStrID).._EmojiString)
    else
        _MissionUnit.Title:SetText(EventStrAll:GetText(_MissionData.TitleStrID))
    end
    Button.ClearListener(_MissionUnit.MainButton)
    Button.AddListener(_MissionUnit.MainButton, EventTriggerType.PointerClick, SearchMgr.OnClickMissionData, _MissionData)
    Button.ClearListener(_MissionUnit.Title)
    Button.AddListener(_MissionUnit.Title, EventTriggerType.PointerClick, SearchMgr.OnClickMissionData, _MissionData)

    _MissionUnit.m_MissionDetails.gameObject:SetActive(false)
    --- 設定物件細項內容
    if _MissionData.Finish == EventTipType.CanAccept then
        _MissionUnit.gameObject.transform:SetParent(this.m_Content_CanGetMission.transform)
    else
        _MissionUnit.m_MissionDetails.gameObject:SetActive(true)
        _MissionUnit.m_MissionDetails.text = EventStrAll:GetText(_MissionData.TipStrID)
        _MissionUnit.gameObject.transform:SetParent(this.m_Group_TracingMission)
        LayoutRebuilder.ForceRebuildLayoutImmediate(_MissionUnit.gameObject.transform)
    end
    _MissionUnit.gameObject.transform.localScale = Vector3.one
    _MissionUnit.gameObject:SetActive(true)

    m_Table_MissionUnit[iIdx] = _MissionUnit

end

--endregion

--region ScrollView 搜尋附近物件相關


function Tracing_Controller.AfterReuseSearchInit(iItem, iRowIdx)
    if iItem == nil then
        return
    end
    this.SetSearchListRowItem(iItem)
end

function Tracing_Controller.AfterReuseSearchIndexUpdate(iItem, iRowIdx)
    if iItem == nil then
        return
    end
    this.SetSearchListRowItem(iItem)
    iItem.m_GObj:SetActive(iRowIdx <= this.GetMaxCountInSearch())
end

local function OnClickSearchList(iSearchData)
    if SelectMgr.m_TargetController == iSearchData then
        HotKeyMgr.MobileAtkEvent( EHotkeyArea.BattleAtkArea, EMainHotKeyPos.Main_1, EMainHotKeyNum.NormalAtk, EventTriggerType.PointerClick )
        --SelectMgr.ChangeAndInterAct(SelectMgr.ETargetType.Npc, _SearchData.gameObject)
        ScrollView.UpdateToFirst(this.m_ScrollView_SearchList)
        _isRefersh = true
    else
        SelectMgr.ChangeTarget(iSearchData.gameObject, true)
    end

end


function Tracing_Controller.SetSearchListRowItem(iUnit)
    if Tracing_Model.SearchList[Tracing_Model.m_SearchTab] == nil then
        return
    end
    ---@type RoleController
    local _SearchData = Tracing_Model.SearchList[Tracing_Model.m_SearchTab][iUnit.m_Index]

    if _SearchData == nil then
        return
    end

    iUnit.m_Unit = {}
    local _SearchUnit = {}
    _SearchUnit.MainButton = Button.New(iUnit.m_GObj.transform:Find( "img_listBG" ))
    _SearchUnit.m_Img_Selected = _SearchUnit.MainButton.transform:Find( "img_listBG_Selected" )
    _SearchUnit.m_Img_Attacking = _SearchUnit.MainButton.transform:Find( "img_listBG_Attacking" )

    _SearchUnit.m_Text_Level = _SearchUnit.MainButton.transform:Find( "TMP_TargetLevel" ):GetComponent(typeof( TMPro.TextMeshProUGUI ))
    _SearchUnit.m_Img_Icon = _SearchUnit.MainButton.transform:Find( "Img_Icon" ):GetComponent("Image")

    _SearchUnit.m_Img_Selected.gameObject:SetActive(SelectMgr.m_TargetController == _SearchData)

    _SearchUnit.m_Img_Attacking.gameObject:SetActive(SearchMgr.IsAttackMe(_SearchData.m_SID))

    --設定名稱
    _SearchUnit.MainButton:SetText(_SearchData:GetName())
    if _SearchData.m_Lv then
        _SearchUnit.m_Text_Level.text = GString.Format(TextData.Get(700), string.format("%03d", _SearchData.m_Lv))
    else
        _SearchUnit.m_Text_Level.text = GString.Format(TextData.Get(700), string.format("%03d", 0))
    end

    local _IconName
    if _SearchData.m_NPCMode then
        local _NPCData = NPCData:Get(_SearchData.m_NPCID)

        if _SearchData.m_NPCMode == EventNPCMode.Fight or _SearchData.m_NPCMode == EventNPCMode.FightCannotDie then
            _IconName = ETargetIconType[_NPCData.m_Priority]
        else
            _IconName = ETargetIconType[_SearchData.m_NPCMode]
        end
    else
        _IconName = OTHER_PLAYER_ICON
    end

    SpriteMgr.Load(_IconName, _SearchUnit.m_Img_Icon)

    Button.ClearListener(_SearchUnit.MainButton)
    Button.AddListener(_SearchUnit.MainButton, EventTriggerType.PointerClick, OnClickSearchList, _SearchData)

    iUnit.m_Unit = _SearchUnit
end

---取得目前搜尋清單數量
function Tracing_Controller.GetMaxCountInSearch()
    return Tracing_Model.GetSearchCount()
end

--endregion

local function OnTargetChange()
    this.m_ScrollView_SearchList.AfterReuseItemInit()
end

function Tracing_Controller.Init()
    InitialUI()
    InitialScrollBar()
    InitialPlayerDataPanel()

    this.m_PageGroup = {}
    this.m_PageGroup[1] = this.m_GObj_MissionList
    this.m_PageGroup[2] = this.m_GObj_TeamPage
    this.m_PageGroup[3] = this.m_GObj_SearchList

    ---這個View左半版的顯示介面
    this.m_GObj_LeftPanel = this.m_ViewRef.m_Dic_Trans:Get("&Left_Panel").gameObject
    PlayerData.Get(EPalyerData.Flags).SetNotify("MovingFlag_OnChange", Tracing_Controller.SetMissionListDirty)
    PlayerData.Get(EPalyerData.Flags).SetNotify("StaticNumFlag_OnChange", Tracing_Controller.SetMissionListDirty)
    GStateObserverManager.Register(EStateObserver.PlayerLevelRefresh, this)

    Tracing_Model.RegistListener()
    SelectMgr.SetOnTargetChanged(OnTargetChange)

end

function Tracing_Controller.OnDestroy()
    table.Clear(m_Table_MissionUnit)
    SelectMgr.UnregistOnTargetChanged(OnTargetChange)
    GStateObserverManager.UnRegister(EStateObserver.PlayerLevelRefresh, this)
    this.m_ScrollView_SearchList = nil
    return true
end

function Tracing_Controller.IsShowLeftPage(iIsSwitch)
    this.m_GObj_LeftPanel:SetActive(iIsSwitch)
end

function Tracing_Controller.Open(iParam)
    Tracing_Model.Init()
    Tracing_Controller.SetAllMission()
    this.m_ScrollView_SearchList.AfterReuseItemInit()
    this.m_GroupListPanel:DoActive(not Tracing_Model.m_isFoldPanel)

    return true
end

function Tracing_Controller.ToDoAfterOpenUISucceed()
    this.m_GObjPageGroupButton:OnEventTriggerByIndex(1, EventTriggerType.PointerClick)
end

function Tracing_Controller.Close(iParam)
    return true
end

function Tracing_Controller.Update()

    -- 重刷Size Fitter必須要顯示中才能重刷
    -- 改在Update執行
    if _isRefersh then
        this.RefreshPanel()
        _isRefersh = false
    end
    local _y = this.m_Content_CanGetMission.transform.rect.height - this.m_Content_CanGetMission.transform.anchoredPosition.y
    this.m_Group_CanGetMission.sizeDelta = Vector2(this.m_Group_CanGetMission.sizeDelta.x ,_y)

    --- 副本任務的也需要這樣控
    local _Star_y = this.m_Content_Animation.transform.rect.height - this.m_Content_Animation.transform.anchoredPosition.y
    this.m_Group_Stars.sizeDelta = Vector2(this.m_Group_Stars.sizeDelta.x , _Star_y)

    ---隊伍頁面開啟中時 才需要刷新buff icon
    if Tracing_Controller.IsShowTeamPage() then
        for key1 , _TeammateUI in pairs (this.m_PlayerPanelTable) do
            --- v.m_RoleID 表示這個UI有紀錄隊友  m_BuffIcon
            if _TeammateUI.m_RoleID ~= nil then
                for key2 , _BuffIcon in pairs (_TeammateUI.m_BuffIcon) do
                    _BuffIcon:UpdateTime()
                end
            end
         end
    end
end

function Tracing_Controller.RefreshPanel()
    --LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Group_TracingMission )
    --LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Group_ContentMissionList )
    this.m_ContentFitterImmediate_ContentMissionList:ForceUpdateContentSize()
    --LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_Content_CanGetMission.transform )
    --LayoutRebuilder.ForceRebuildLayoutImmediate(this.m_GObj_SearchList )

end

---動標、記數永標變化時把現有的任務弄髒
function Tracing_Controller.SetMissionListDirty(iFlagID)
    if(iFlagID ~= nil and iFlagID.m_FlagID ~= nil and iFlagID.m_FlagID ~= 0 and not MissionMgr.CheckTrackMission(iFlagID.m_FlagID)) then
        MissionMgr.UpdateAndSaveTrackMission(iFlagID.m_FlagID)
    end

    this.RefreshMissionList()

    if not table.IsNullOrEmpty(m_NowRaid) then
        -- 只在目前在的副本時更新
        local _Flag = PlayerData.GetMovingFlag(m_NowRaid.FlagID)
        if _Flag.m_FlagID == 0 then
            this.m_GroupRaidMissionUnit:SetActive(false)
            m_NowRaid = {}

        elseif iFlagID.m_FlagID ~= nil and m_NowRaid.FlagID == iFlagID.m_FlagID then
            this.UpdateRaidDetail(iFlagID.m_FlagID)
        end
    end
end

--- 要通知任務列表
function Tracing_Controller.RefreshMissionList()
    --用Coroutine刷這個
    Tracing_Model.RefreshMissionData(false, this.SetAllMission)

end

--- 附近物件有所變動要通知搜尋系統
function Tracing_Controller.OnSearchListChanged()
    if this.m_ScrollView_SearchList and this.m_GObj_SearchList.gameObject.activeSelf then
        this.m_ScrollView_SearchList.AfterReuseItemInit()
        --ScrollView.UpdateToFirst(this.m_ScrollView_SearchList)
    end

end

--- 頁籤選擇時的動作
function Tracing_Controller.OnPageButtonClick(iPage)

    -- if iPage == GroupButton.GetCount(this.m_GObjPageGroupButton) then
    --     this.m_GroupListPanel:DoActive(Tracing_Model.m_isFoldPanel)
    --     Tracing_Model.m_isFoldPanel = not Tracing_Model.m_isFoldPanel
    --     if not Tracing_Model.m_isFoldPanel then
    --         GroupButton.OnPointerClickByIndex(this.m_GObjPageGroupButton,Tracing_Model.m_CurrentPage)
    --     end
    --     return
    -- end

    if Tracing_Model.m_CurrentPage == iPage and not Tracing_Model.m_isFoldPanel then
        return
    else
        --把面板打開
        this.m_FoldButton:SetSelect(false)
        Tracing_Model.m_isFoldPanel = false
        this.m_GroupListPanel:DoActive(not Tracing_Model.m_isFoldPanel)
    end
    Tracing_Model.m_CurrentPage = iPage

    for k,v in pairs(this.m_PageGroup) do
        v.gameObject:SetActive(k == Tracing_Model.m_CurrentPage)
    end

    ---開隊伍分頁時 需要刷新對應資訊
    if Tracing_Model.m_CurrentPage == 2 then
        Tracing_Controller.OpenTeamPage()
    end
end

---角色基礎屬性有更新會呼叫
---@param iEStateObserver EStateObserver 觀察者模式
---@param iEBaseValues EBaseValues 角色基礎數值類型
function Tracing_Controller:OnStateChanged(iEStateObserver)
    if iEStateObserver == EStateObserver.PlayerLevelRefresh then
        this.SetMissionListDirty()
    end
end

--- 要做附近物件篩選的 (現在無用)
-- function Tracing_Controller.OnSearchTypeClick(iPage)
--     if Tracing_Model.m_SearchTab == iPage then
--         return
--     end

--     Tracing_Model.m_SearchTab = iPage
--     this.OnSearchListChanged()

--     ScrollView.UpdateToFirst(this.m_ScrollView_SearchList)

-- end

---隊伍相關

---開啟隊伍分頁
function Tracing_Controller.OpenTeamPage()
    Tracing_Controller.RenewTeamMemberData()
end


---刷新隊伍成員資訊
function Tracing_Controller.RenewTeamMemberData()


    for k , v in pairs (this.m_PlayerPanelTable) do
        v.m_RoleID = nil
    end

    ---如果沒有組隊 只顯示最下面的UI 並且設置成沒有隊員的狀態
    if table.Count(TeammateMgr.m_TeammateList) == 0 then
        for i = 1, TEAMMATE_MAX_COUNT do
            this.m_PlayerPanelTable[i].m_Obj_WithMember.gameObject:SetActive(false)
            this.m_PlayerPanelTable[i].m_Obj_NoMemberSet.gameObject:SetActive(i==TEAMMATE_MAX_COUNT)
        end
        return
    end

    ---有隊員根據隊員數量顯示
    ---要填到第幾個玩家顯示面板
    local _FillIndex = 1

    for k , v in pairs (TeammateMgr.m_TeammateList) do
        local _Kind = v[ETeamKind.Team] ~= nil and ETeamKind.Team or ETeamKind.Recruit
        if PlayerData.GetRoleID() ~= v[_Kind].m_TeammateData.m_RoleID then
            this.m_PlayerPanelTable[_FillIndex].m_RoleID = v[_Kind].m_TeammateData.m_RoleID
            Tracing_Controller.RenewSingleTeamMemberData(v[_Kind].m_TeammateData.m_RoleID)
            _FillIndex = _FillIndex +1
        end
    end

    for i = _FillIndex, TEAMMATE_MAX_COUNT do
        this.m_PlayerPanelTable[i].m_Obj_WithMember.gameObject:SetActive(false)
        this.m_PlayerPanelTable[i].m_Obj_NoMemberSet.gameObject:SetActive(true)
    end
end

---拿到尚未使用的隊員顯示UI
---@param iRoleID RoleID 腳色ID
function Tracing_Controller.GetIdleTeammateUI(iRoleID)

    local _RefUI
    for k , v in pairs (this.m_PlayerPanelTable) do
       if v.m_RoleID == nil then
            v.m_RoleID = iRoleID
       end
    end
    return _RefUI
end


---清除隊員UI的RoleID
---@param iRoleID RoleID 腳色ID
function Tracing_Controller.ClearTeammateUI()

end

---刷新隊伍成員資訊
---@param iRoleID RoleID 腳色ID
---@param iAddMember bool 在已有隊伍狀態下增加新隊員
function Tracing_Controller.RenewSingleTeamMemberData(iRoleID,iAddMember)

    local _TeammateData = TeammateMgr.Get(ETeamKind.Team, iRoleID)

    local _TeammateUI   =  Tracing_Controller.GetTeammateUI(iRoleID) --( iAddMember ) and  Tracing_Controller.GetIdleTeammateUI(iRoleID) or Tracing_Controller.GetTeammateUI(iRoleID)

    if _TeammateData == nil  then
        D.Log("nil _TeammateData")
    end

    if _TeammateUI == nil  then
        D.Log("nil _TeammateUI")
    end

    if _TeammateData ~= nil and _TeammateUI ~= nil  then

        _TeammateUI.m_Obj_WithMember.gameObject:SetActive(true)
        _TeammateUI.m_Obj_NoMemberSet.gameObject:SetActive(false)
        ---填入名稱
        _TeammateUI.m_TMP_PlayerName.text = _TeammateData.m_TeammateData.m_Name

        ---填入HP Slider
        _TeammateUI.m_SliderBar_HP.value = _TeammateData.m_TeammateData.m_CurHp/_TeammateData.m_TeammateData.m_MaxHp

        ---Todo 填入MP slider
        _TeammateUI.m_SliderBar_MP.value = _TeammateData.m_TeammateData.m_CurMp/_TeammateData.m_TeammateData.m_MaxMp

        ---填入玩家等級
        _TeammateUI.m_TMP_PlayerLevel.text = _TeammateData.m_TeammateData.m_LV


        ---刷新頭像訊息 要等真的組隊才能測試
        _TeammateUI.m_HeadIcon:RefreshIcon( _TeammateData.m_TeammateData.m_RoleID)

        ---是不是隊長
        _TeammateUI.m_Img_LeaderHint:SetActive(_TeammateData.m_TeammateData.m_IsLeader)

        ---是不是準備中
        _TeammateUI.m_Img_AlreadyPrepared:SetActive(RecruitMgr.GetSpecificMemberReady(iRoleID))
        ---Buff
        ---是否有buff
        local _WithBuff = (table.Count(_TeammateData.m_TeammateData.m_BuffList) > 0)
        local _ShowTooMuchBuffHint  = (_WithBuff and table.Count(_TeammateData.m_TeammateData.m_BuffList) >  this.MAX_TEAMMATE_SHOW_BUFF_COUNT)

        _TeammateUI.m_Trans_BuffSet.gameObject:SetActive(_WithBuff)

        _TeammateUI.m_TMP_BuffCount:SetActive(_ShowTooMuchBuffHint)

        if _WithBuff then
            for i = 1, this.MAX_TEAMMATE_SHOW_BUFF_COUNT do
                if _TeammateData.m_TeammateData.m_BuffList[i] and _TeammateData.m_TeammateData.m_BuffList[i] then
                    _TeammateUI.m_BuffIcon[i]:RefreshIcon(_TeammateData.m_TeammateData.m_BuffList[i])
                    _TeammateUI.m_BuffIcon[i].gameObject:SetActive(true)
                else
                    _TeammateUI.m_BuffIcon[i].gameObject:SetActive(false)
                end
            end
        end
    end

end

---取得對應RoleID 使用的隊員UI
function Tracing_Controller.GetTeammateUI(iRoleID)

    for  k, v in pairs (this.m_PlayerPanelTable) do
       if v.m_RoleID == iRoleID then
           return v
       end
    end

    return nil
end

---有隊員被移除時 對應的UI要清除資料並切換顯示
---@param iRoleID string 用string 表示的RoleID
function Tracing_Controller.TurnOffTeamUI(iRoleID)

    for  k, v in pairs (this.m_PlayerPanelTable) do
        if tostring(v.m_RoleID) == iRoleID then
            v.m_Obj_WithMember.gameObject:SetActive(false)
            v.m_Obj_NoMemberSet.gameObject:SetActive(true)
        end
    end

end

---刷新隊伍設定資訊
function Tracing_Controller.RenewTeamSettingData()

    Tracing_Controller.TeamSettingBtnDisplayType(this.m_Btn_InviteCondition_LeaderOnly,TeammateMgr.m_TeamInviteType == ETeamInviteType.OnlyLeaderInvite,ONLY_LEADER_INVITE_StrID)
    Tracing_Controller.TeamSettingBtnDisplayType(this.m_Btn_InviteCondition_AnyOne,TeammateMgr.m_TeamInviteType == ETeamInviteType.AllInvite,ALL_INVITE_StrID)
    Tracing_Controller.TeamSettingBtnDisplayType(this.m_Btn_Item_SelfOnly,TeammateMgr.m_TeamItemDistributionType == ETeamItemDistributionType.PickSelf,ITEM_DISTRIBUTION_PICKSELF_StrID)
    Tracing_Controller.TeamSettingBtnDisplayType(this.m_Btn_Item_Random,TeammateMgr.m_TeamItemDistributionType == ETeamItemDistributionType.TeamRandom,ITEM_DISTRIBUTION_RANDOM_StrID)

end

---隊伍設定 設定按件的底色跟TMP文字Style
---@param iButton Transform 按鍵物件的transform
---@param iIsCurrentType bool 是不是目前在TeammateMgr設定的 類別
---@param iBtnWordID number 按鍵要使用甚麼文字的字串ID
function Tracing_Controller.TeamSettingBtnDisplayType(iButton, iIsCurrentType , iBtnWordID)
    ---按鍵的底圖顏色
    local _BtnImage = iButton.gameObject:GetComponent(typeof(Image))
    ---按鍵文字內容
    local _BtnString = TextData.Get(iBtnWordID)
    ---按鍵文字TMP Style
    local _StyleString = iIsCurrentType and TeamSetting_UseStyle or TeamSetting_NonUseStyle
    ---組合TMP Style 跟文字內容後的結果
    local _StyleContent = GString.StringWithStyle(_BtnString,_StyleString)
    Button.SetText(iButton,_StyleContent)

    _BtnImage.color = (iIsCurrentType) and TeamSetting_UseColor or TeamSetting_NonUseColor
end

---設定邀請權限
---@param iETeamInviteType ETeamInviteType 邀請權限
function Tracing_Controller.SetInviteQualify(iETeamInviteType)

    ---不再隊伍中 無法設定
    if TeammateMgr.m_IsInTeam == false then

    ---不是隊長 提示你不是隊長
    elseif TeammateMgr.m_IsLeader == false then
       MessageMgr.AddCenterMsg(false, TextData.Get(10400101))
    else
       local _Packet = {}
        _Packet.m_TeamInviteType = (iETeamInviteType-1)
       SendProtocol_011._001(16,_Packet)
    end
end

---設定物品分配方式
---@param iETeamItemDistributionType ETeamItemDistributionType 物品分配模式
function Tracing_Controller.SetItemDistributionType(iETeamItemDistributionType)

    ---不再隊伍中 無法設定
    if TeammateMgr.m_IsInTeam == false then

    ---不是隊長 提示你不是隊長
    elseif TeammateMgr.m_IsLeader == false then
        MessageMgr.AddCenterMsg(false, TextData.Get(10400101))
    else
        local _Packet = {}
        _Packet.m_TeamItemDistributionType = iETeamItemDistributionType-1
        SendProtocol_011._001(18,_Packet)
    end
end

---開啟隊伍設定分頁
---@param iShowPanel bool 開啟隊伍設定介面
function Tracing_Controller.ShowTeamSettingPanel(iShowPanel)
    this.m_GObj_TeamSettingPage:SetActive(iShowPanel)

    ---需要開關對應的tracing_Controller按鍵顯示
    this.m_GObjPageGroupButton.gameObject:SetActive(not iShowPanel)
    this.m_FoldButton.gameObject:SetActive(not iShowPanel)
end


---檢查隊伍頁面是否正被顯示中
---@return bool 頁面是否正被開啟
function Tracing_Controller.IsShowTeamPage()
    return (Tracing_Controller.m_GObj_TeamPage ~= nil and Tracing_Controller.m_GObj_TeamPage.gameObject.activeSelf)
end

---刷新某隊員 是否按下準備中
---@param iRoleID 需要檢查的腳色ID
function Tracing_Controller.RecruitMemberIsReady(iRoleID)

    local _TeammateData = TeammateMgr.Get(ETeamKind.Team, iRoleID)
    local _TeammateUI   =  Tracing_Controller.GetTeammateUI(iRoleID)

    if _TeammateData~=nil and _TeammateUI~=nil then
        _TeammateUI.m_Img_AlreadyPrepared:SetActive(RecruitMgr.GetSpecificMemberReady(iRoleID))
    end
end
