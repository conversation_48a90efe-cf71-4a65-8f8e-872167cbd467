fileFormatVersion: 2
guid: f9c5645531a4e3e4699eeadba2c6abda
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: Bip001
  - first:
      1: 100004
    second: Bip001 Head
  - first:
      1: 100006
    second: Bip001 L foot
  - first:
      1: 100008
    second: Bip001 L hand
  - first:
      1: 100010
    second: Bip001 R foot
  - first:
      1: 100012
    second: Bip001 R hand
  - first:
      1: 100014
    second: Bone002
  - first:
      1: 100016
    second: Bone003
  - first:
      1: 100018
    second: Bone005
  - first:
      1: 100020
    second: Bone006
  - first:
      1: 100022
    second: Bone007
  - first:
      1: 100024
    second: Bone008
  - first:
      1: 100026
    second: Bone009
  - first:
      1: 100028
    second: Bone013
  - first:
      1: 100030
    second: Bone014
  - first:
      1: 100032
    second: Bone015
  - first:
      1: 100034
    second: Bone017
  - first:
      1: 100036
    second: Bone018
  - first:
      1: 100038
    second: Bone019
  - first:
      1: 100040
    second: CP_024
  - first:
      1: 100042
    second: Dummy001
  - first:
      1: 100044
    second: weapon_point
  - first:
      1: 100046
    second: Floor
  - first:
      1: 100048
    second: P_0014
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: Bip001
  - first:
      4: 400004
    second: Bip001 Head
  - first:
      4: 400006
    second: Bip001 L foot
  - first:
      4: 400008
    second: Bip001 L hand
  - first:
      4: 400010
    second: Bip001 R foot
  - first:
      4: 400012
    second: Bip001 R hand
  - first:
      4: 400014
    second: Bone002
  - first:
      4: 400016
    second: Bone003
  - first:
      4: 400018
    second: Bone005
  - first:
      4: 400020
    second: Bone006
  - first:
      4: 400022
    second: Bone007
  - first:
      4: 400024
    second: Bone008
  - first:
      4: 400026
    second: Bone009
  - first:
      4: 400028
    second: Bone013
  - first:
      4: 400030
    second: Bone014
  - first:
      4: 400032
    second: Bone015
  - first:
      4: 400034
    second: Bone017
  - first:
      4: 400036
    second: Bone018
  - first:
      4: 400038
    second: Bone019
  - first:
      4: 400040
    second: CP_024
  - first:
      4: 400042
    second: Dummy001
  - first:
      4: 400044
    second: weapon_point
  - first:
      4: 400046
    second: Floor
  - first:
      4: 400048
    second: P_0014
  - first:
      21: 2100000
    second: Material #28
  - first:
      21: 2100002
    second: No Name
  - first:
      23: 2300000
    second: Bip001
  - first:
      23: 2300002
    second: Bip001 Head
  - first:
      23: 2300004
    second: weapon_point
  - first:
      33: 3300000
    second: Bip001
  - first:
      33: 3300002
    second: Bip001 Head
  - first:
      33: 3300004
    second: weapon_point
  - first:
      43: 4300000
    second: CP_024
  - first:
      43: 4300002
    second: Bip001
  - first:
      43: 4300004
    second: Bip001 Head
  - first:
      43: 4300006
    second: weapon_point
  - first:
      43: 4300008
    second: P_0014
  - first:
      74: 7400000
    second: idle
  - first:
      74: 7400002
    second: run
  - first:
      74: 7400004
    second: ready
  - first:
      74: 7400006
    second: hit
  - first:
      74: 7400008
    second: weak
  - first:
      74: 7400010
    second: atk1
  - first:
      74: 7400012
    second: win
  - first:
      74: 7400014
    second: die
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: CP_024
  - first:
      137: 13700002
    second: P_0014
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: run
      takeName: Take 001
      internalID: 0
      firstFrame: 30
      lastFrame: 42
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ready
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: hit
      takeName: Take 001
      internalID: 0
      firstFrame: 50
      lastFrame: 72
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: weak
      takeName: Take 001
      internalID: 0
      firstFrame: 80
      lastFrame: 120
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: atk1
      takeName: Take 001
      internalID: 0
      firstFrame: 130
      lastFrame: 170
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: win
      takeName: Take 001
      internalID: 0
      firstFrame: 178
      lastFrame: 210
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: die
      takeName: Take 001
      internalID: 0
      firstFrame: 218
      lastFrame: 250
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 0
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 893773fc0b38a9f41a52a35e9e8c7c1b,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
