fileFormatVersion: 2
guid: 81b9a0018d0a3f9449631f442a519167
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Back01
    100002: Back02
    100004: Back03
    100006: Bip001 Spine1
    100008: Bone001
    100010: Bone002
    100012: Bone003
    100014: Bone004
    100016: Bone006
    100018: Eye_L_Down
    100020: Eye_L_Up
    100022: Eye_R_Down
    100024: Eye_R_Up
    100026: Floor
    100028: Foot 001
    100030: Foot 002
    100032: Foot 003
    100034: Foot 004
    100036: Foot 005
    100038: Foot 006
    100040: //RootNode
    400000: Back01
    400002: Back02
    400004: Back03
    400006: Bip001 Spine1
    400008: Bone001
    400010: Bone002
    400012: Bone003
    400014: Bone004
    400016: Bone006
    400018: Eye_L_Down
    400020: Eye_L_Up
    400022: Eye_R_Down
    400024: Eye_R_Up
    400026: Floor
    400028: Foot 001
    400030: Foot 002
    400032: Foot 003
    400034: Foot 004
    400036: Foot 005
    400038: Foot 006
    400040: //RootNode
    2100000: No Name
    2300000: Floor
    3300000: Floor
    4300000: Floor
    7400000: M_0004_Expr_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 7f0b1fa5c4ef7dc4791cb6286f420daf,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
