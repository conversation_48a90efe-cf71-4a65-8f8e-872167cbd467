%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c63d4b50d9a147c47a56961848c10619, type: 3}
  m_Name: ResText
  m_EditorClassIdentifier: 
  m_List:
  - m_Type: 0
    m_List_TextContent:
    - m_Key: DLSize
      m_Text: "\u5DF2\u4E0B\u8F09: {0} / {1}"
    - m_Key: GameInit
      m_Text: "\u904A\u6232\u521D\u59CB\u5316\u4E2D"
    - m_Key: ReStart
      m_Text: "\u8ACB\u91CD\u65B0\u958B\u555F\u904A\u6232"
    - m_Key: Confirm
      m_Text: "\u78BA\u8A8D"
    - m_Key: Cancel
      m_Text: "\u53D6\u6D88"
    - m_Key: ChechNetwork
      m_Text: "\u672A\u5075\u6E2C\u5230\u7DB2\u8DEF<br>\u8ACB\u6AA2\u67E5\u7DB2\u8DEF\u8A2D\u5B9A"
    - m_Key: CheckGameVersion
      m_Text: "\u904A\u6232\u7248\u672C\u4E0D\u7B26 Ver {0}"
    - m_Key: CheckDownload
      m_Text: "\u9700\u8981\u4E0B\u8F09\u8CC7\u6E90 : {0} {1}<br>\u78BA\u5B9A\u4E0B\u8F09\u8CC7\u6E90?"
    - m_Key: CheckAssets
      m_Text: "\u904A\u6232\u8CC7\u6E90\u932F\u8AA4"
    - m_Key: DLAssetsError
      m_Text: "\u4E0B\u8F09\u904A\u6232\u8CC7\u6E90\u7570\u5E38"
    - m_Key: InitAssetsError
      m_Text: "\u904A\u6232\u8CC7\u6E90\u521D\u59CB\u5316\u7570\u5E38"
    - m_Key: HaveNewVersion
      m_Text: "\u6AA2\u67E5\u5230\u65B0\u7684\u7248\u672C {0}<br>\u662F\u5426\u524D\u5F80\u4E0B\u8F09?"
    - m_Key: ExeUpdateInfoError
      m_Text: "\u53D6\u5F97\u57F7\u884C\u6A94\u8CC7\u8A0A\u7570\u5E38"
    - m_Key: SelectLanguage
      m_Text: "\u8ACB\u9078\u64C7\u8A9E\u8A00"
    - m_Key: Version
      m_Text: "\u7248\u672C[ \u57F7\u884C\u6A94:{0}.  \u8CC7\u6E90:{1}.]"
    - m_Key: FileCheckSize
      m_Text: "\u5DF2\u9A57\u8B49\u6A94\u6848 {0}  MB"
    - m_Key: FileCheckCorrect
      m_Text: "\u8CC7\u6599\u6AA2\u67E5\u7121\u7570\u5E38\uFF0C\u53EF\u958B\u59CB\u9032\u884C\u904A\u6232"
  - m_Type: 2
    m_List_TextContent:
    - m_Key: DLSize
      m_Text: 'Download: {0} / {1}'
    - m_Key: GameInit
      m_Text: Initializing game.
    - m_Key: ReStart
      m_Text: "\u8ACB\u91CD\u65B0\u958B\u555F\u904A\u6232"
    - m_Key: Confirm
      m_Text: Confirm
    - m_Key: Cancel
      m_Text: Cancel
    - m_Key: ChechNetwork
      m_Text: No network detected.<br>Please check your network settings.
    - m_Key: CheckGameVersion
      m_Text: Game version mismatch. Ver {0}
    - m_Key: CheckDownload
      m_Text: 'Required to download resources: {0} {1}<br>Do you want to proceed
        with the download?  '
    - m_Key: CheckAssets
      m_Text: Game resource error
    - m_Key: DLAssetsError
      m_Text: Error occurred while downloading game resources
    - m_Key: InitAssetsError
      m_Text: Error occurred during game resource initialization
    - m_Key: HaveNewVersion
      m_Text: A new version {0} is available<br>Would you like to download it?
    - m_Key: ExeUpdateInfoError
      m_Text: Failed to retrieve executable information
    - m_Key: SelectLanguage
      m_Text: Select Language
    - m_Key: Version
      m_Text: 'Version[ Execution : {0}.  Resources : {1}.]'
    - m_Key: FileCheckSize
      m_Text: Check File {0}  MB
    - m_Key: FileCheckCorrect
      m_Text: The data check shows no issues. You may now begin the game.
  - m_Type: 1
    m_List_TextContent:
    - m_Key: DLSize
      m_Text: "\u5DF2\u4E0B\u8F7D: {0} / {1}"
    - m_Key: GameInit
      m_Text: "\u6E38\u620F\u521D\u59CB\u5316\u4E2D"
    - m_Key: ReStart
      m_Text: "\u8BF7\u91CD\u65B0\u5F00\u542F\u6E38\u620F"
    - m_Key: Confirm
      m_Text: "\u786E\u8BA4"
    - m_Key: Cancel
      m_Text: "\u53D6\u6D88"
    - m_Key: ChechNetwork
      m_Text: "\u672A\u4FA6\u6D4B\u5230\u7F51\u8DEF<br>\u8BF7\u68C0\u67E5\u7F51\u8DEF\u8BBE\u5B9A"
    - m_Key: CheckGameVersion
      m_Text: "\u6E38\u620F\u7248\u672C\u4E0D\u7B26 Ver {0}"
    - m_Key: CheckDownload
      m_Text: "\u9700\u8981\u4E0B\u8F7D\u8D44\u6E90 : {0} {1}<br>\u786E\u5B9A\u4E0B\u8F7D\u8D44\u6E90?"
    - m_Key: CheckAssets
      m_Text: "\u6E38\u620F\u8D44\u6E90\u9519\u8BEF"
    - m_Key: DLAssetsError
      m_Text: "\u4E0B\u8F7D\u6E38\u620F\u8D44\u6E90\u5F02\u5E38"
    - m_Key: InitAssetsError
      m_Text: "\u6E38\u620F\u8D44\u6E90\u521D\u59CB\u5316\u5F02\u5E38"
    - m_Key: HaveNewVersion
      m_Text: "\u68C0\u67E5\u5230\u65B0\u7684\u7248\u672C {0}<br>\u662F\u5426\u524D\u5F80\u4E0B\u8F7D?"
    - m_Key: ExeUpdateInfoError
      m_Text: "\u53D6\u5F97\u6267\u884C\u6863\u8D44\u8BAF\u5F02\u5E38"
    - m_Key: SelectLanguage
      m_Text: "\u8BF7\u9009\u62E9\u8BED\u8A00"
    - m_Key: Version
      m_Text: "\u7248\u672C[ \u6267\u884C\u6863:{0}.  \u8D44\u6E90:{1}.]"
    - m_Key: FileCheckSize
      m_Text: "\u5DF2\u9A8C\u8BC1\u6587\u4EF6 {0} MB"
    - m_Key: FileCheckCorrect
      m_Text: "\u8D44\u6599\u68C0\u67E5\u65E0\u5F02\u5E38\uFF0C\u53EF\u4EE5\u5F00\u59CB\u8FDB\u884C\u6E38\u620F\u3002"
