---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
require("Logic/SystemSettingData/HUDSetting")
require("UI/BattleText/BattleText_Controller")
require("Logic/BubbleMgr")
require("Logic/HUD/HUD_CoreSkillMgr")
require("Logic/HUD/HUDFactory")

---HUD控制
---@class HUDController
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.6.6
HUDController = {}
HUDController.__index = HUDController

HUDCanvas = GameObject.Find("HUDCanvas").transform
HUDCanvas_Rect = HUDCanvas:GetComponent(typeof(RectTransform))

---@class EHUD_UpdateChecker
EHUD_UpdateChecker = {
    Enable = 1,
    HP = 2,
    Name = 3,
    State = 4,
    Guild = 5,
    Title = 6,
    Color = 7,
    Icon = 8,
    CoreSkill = 9,
}

---自己 HUD 血條的漸層色
local _Gradient_Self_ColorKeyAy = {
    UnityEngine.GradientColorKey.New(Extension.GetColor("#217C4A"), 0),
    UnityEngine.GradientColorKey.New(Extension.GetColor("#31A95E"), 1)
}

---其他 HUD 血條的漸層色
local _Gradient_Other_ColorKeyAy = {
    UnityEngine.GradientColorKey.New(Extension.GetColor("#A22B22"), 0),
    UnityEngine.GradientColorKey.New(Extension.GetColor("#C6391F"), 1)
}

---HUD 基礎高度
local _DefultHight = 2

--region String
local m_Str_View = "_View"
--endregion

local m_HUDDebug = false
local m_HUDCollector = {}

---@type HUDSetting.m_TextStyle
local m_TextStyle = HUDSetting.m_TextStyle
---@type HUDSetting.m_LVGap
local m_LVGap = HUDSetting.m_LVGap

--region HUD 淡入
--- 平方較好計算距離
local m_ActiveRange = HUDSetting.m_ActiveRange * HUDSetting.m_ActiveRange
local m_CullingFade = HUDSetting.m_NearbyCulling * HUDSetting.m_NearbyCulling - m_ActiveRange
--endregion

--region 泡泡框
---泡泡框狀態
---@class BubbleState
local BubbleState = {}

--- BattleText 物件
local m_GObj_BattleText = nil

function BubbleState.New()
    local _bs = {}
    setmetatable(_bs, {__index = BubbleState})

    _bs:Init()

    return _bs
end

function BubbleState:Init()
    self.m_IsBubbling = false
    self.m_Step = 0
    self.m_TextStep = 0
    self.m_SeqID = 0
    self.m_CurMsg = ""
    self.m_StoreMsg = ""
    self.m_FullMsg = ""
    self.m_NeedScroll = false
    self.m_CanvasAlpha = 0
    self.m_ContentAlpha = 1
    self.m_DelayTime = 0
end

function BubbleState:GetMsgContent(iMsg)
    self.m_CurMsg, self.m_StoreMsg, self.m_NeedScroll = BubbleMgr.StringFormat(iMsg)
    return self.m_CurMsg
end
--endregion

---region Emoji
---Emoji狀態
---@class EmojiState
local EmojiState = {}
function EmojiState.New()
    local _EmojiState = {}
    setmetatable(_EmojiState, {__index = EmojiState})

    _EmojiState:Init()

    return _EmojiState
end

function EmojiState:Init()
    ---播放中
    self.m_IsShowing = false
    ---emoji名稱
    self.m_EmojiName = ""
    ---Emoji的hashcode
    self.m_HashCode = nil
    ---被生出來的emoji 物件
    self.m_EmojiObj = nil
    ---播放一段時間消失 用 tween的 delayed
    self.m_TweenID = 0

end

---掛在HUD的emoji 播放幾次後要回收
local m_EmojiPlayTime = 10
---
---endregion

function HUDController.Init()
    HUDFactory.Init()
    SelectMgr.SetOnTargetChanged(HUDController.OnTargetChange)
    BubbleMgr.Init()
    HUDController.OpenBattleText()
end

function HUDController.SetHUDDebug(iIsOn)
    m_HUDDebug = iIsOn
    HUDController.RefreshAllHUD(EHUD_UpdateChecker.Enable)
end
function HUDController.GetHUDDebug()
    return m_HUDDebug
end

---@param iTrans Transform 目標Transform
---@param iHUDData HUDData
---@param iRC RoleController 用來調用HUD生成後行為
function HUDController.New(iTrans, iHUDData, iRC)
    ---@type HUDController
    local _HC = setmetatable({  }, {__index = HUDController})

    _HC.m_HUDData = iHUDData
    _HC.m_HUDHeight = 0
    _HC.m_RoleTransform = iTrans
    _HC.m_RoleType = iRC.m_RoleType
    _HC.m_BubbleState = BubbleState.New()

    ---現階段 只有其他玩家 或者玩家本人才需要 emoji的顯示
    if _HC.m_RoleType == ERoleType.OtherPlayer or _HC.m_RoleType == ERoleType.Self then
        _HC.m_EmojiState = EmojiState.New()
    end

    --假玩家的HUD分類是玩家
    if _HC.m_RoleType == ERoleType.NPC and iRC.m_PlayerID then
        _HC.m_RoleType = ERoleType.OtherPlayer
    end

    HUDFactory.GetHUD(_HC, true)

    _HC.m_ForceUpdate = {}
    _HC.m_UpdateList = {}

    if not m_HUDCollector[_HC.m_HUDData.m_SID] then
        m_HUDCollector[_HC.m_HUDData.m_SID] = _HC
    end

    _HC.m_IsInited = true

    return _HC
end

function HUDController:SetStateController(iStateController)
    self.m_StateController = iStateController
    self:SetHUDDirty(EHUD_UpdateChecker.Name)
end

--- 設定模型實際高度與縮放，用來計算 HUD 位置
function HUDController:SetHeightAndScale(iHeight, iScale, isTween)
    self.m_Height = iHeight

    -- 目前沒用到
    --self.m_Scale = iScale
    --self.m_HUDScale = _DefultHight * self.m_Scale

    if isTween then
        self.m_TweenHeight = self.m_Height
        self.m_timeElapsed = 0
    else
        self.m_TweenHeight = nil
        self.m_timeElapsed = nil
        -- HUD 高度
        self.m_HUDHeight = self.m_Height
    end
end

--- Update
function HUDController:Update()
    --縮放中
    if self.m_TweenHeight then
        self.m_HUDHeight = Mathf.Lerp(self.m_HUDHeight, self.m_TweenHeight, self.m_timeElapsed / 0.5)
        self.m_timeElapsed = self.m_timeElapsed + Time.deltaTime
    end

    if self.m_timeElapsed and self.m_timeElapsed > 0.5 then
        self.m_HUDHeight = self.m_TweenHeight
        self.m_TweenHeight = nil
        self.m_timeElapsed = nil
    end

    if table.Count(self.m_ForceUpdate) > 0 then
        for k, v in pairs(self.m_ForceUpdate) do
            v()
            self.m_ForceUpdate[k] = nil
        end
    end
end

function HUDController:LateUpdate()
    if table.Count(self.m_UpdateList) > 0 then
        -- 被遮擋而不顯示，就只做物件顯示更新(主要更新物件有沒有被使用)
        if not self.m_HUDActive then
            if self.m_UpdateList[EHUD_UpdateChecker.Enable] then
                -- 更新顯示
                if self:CheckEnable() then
                    self.m_UpdateList[EHUD_UpdateChecker.Enable] = nil
                end
            end
            return
        end

        for k, v in pairs(self.m_UpdateList) do
            local _IsSuccess = false

            if k == EHUD_UpdateChecker.Enable then
                -- 更新顯示
                _IsSuccess = self:CheckEnable()
            elseif k == EHUD_UpdateChecker.HP then
                -- 更新血量
                _IsSuccess = self:UpdateHpBar()
            elseif k == EHUD_UpdateChecker.Name then
                -- 更新名字
                _IsSuccess = self:RefreshName()
            elseif k == EHUD_UpdateChecker.Title then
                -- 更新稱號
                _IsSuccess = self:RefreshTitle()
            elseif k == EHUD_UpdateChecker.Guild then
                -- 更新公會
            elseif k == EHUD_UpdateChecker.Color then
                -- 更新變色
                _IsSuccess = self:RefreshNameColor()
                _IsSuccess = _IsSuccess and self:RefreshHpBarColor()
            elseif k == EHUD_UpdateChecker.Icon then
                -- 更新Icon
                _IsSuccess = self:RefreshIcon()
            elseif k == EHUD_UpdateChecker.CoreSkill then
                -- 更新核心技
                _IsSuccess = self:RefreshCoreSkill()
            end

            -- 更新成功清掉索引
            if _IsSuccess then
                self.m_UpdateList[k] = nil
            end
        end
    end

    if self.m_HUDActive then
        -- 更新位置
        self:SetPosition()

        if self.m_RoleType ~= ERoleType.Self then
            --計算距離
            local _Dis = HUDMgr.Inst:GetDistance(self.m_RoleTransform)
            -- 更新透明度
            self:SetAlpha(_Dis)
            -- 更新Order
            self:SetOrder(_Dis)
        end
    end
end

function HUDController:ReturnHUD()
    if not self.m_UpdateList then
        return
    end

    if m_HUDCollector[self.m_HUDData.m_SID] then
        m_HUDCollector[self.m_HUDData.m_SID] = nil
    end

    if self.m_UpdateList then
        table.Clear(self.m_UpdateList)
    end

    if self.m_ForceUpdate then
        table.Clear(self.m_ForceUpdate)
    end

    if self.m_RoleType == ERoleType.NPC then
        self:ReleaseBubble()
    end

    --清掉 HUDData
    if self.m_HUDData then
        self.m_HUDData = nil
    end

    --清掉敵我關係
    if self.m_Relation then
        self.m_Relation = nil
    end

    HUDFactory.ReturnHUD(self, true)

    self.m_Unload = false
    self.m_IsInited = false
end

--
function HUDController:SetHUD(iIsActive)
    if not self.m_CanvasGroup or Extension.IsUnityObjectNull(self.m_CanvasGroup) then
        return
    end

    --將整個 HUD 隱藏/顯示
    self.m_CanvasGroup.alpha = iIsActive and 1 or 0
    self.m_HUDActive = iIsActive

    -- 組成HUD
    HUDFactory.GetHUD(self, false)
end

function HUDController:SetPosition()
    if not self.m_RootTrans then
        return
    end

    HUDMgr.Inst:SetPosition(self.m_RootTrans, self.m_RoleTransform, self.m_HUDHeight)
end

function HUDController:SetAlpha(iDistance)
    self.m_CanvasGroup.alpha = Mathf.Lerp(1, 0, (iDistance - m_ActiveRange)/m_CullingFade)
end

function HUDController:SetOrder(iDistance)
    iDistance = Mathf.Round(iDistance)
    self.m_Canvas.sortingOrder = HUDSetting.m_MaxOrder - iDistance
end

--- 排序 HUD
function HUDController:Sort()
    -- 沒顯示就不排序，重新啟用時會排
    if not self.m_HUDActive then
        return
    end

    local _YPos = 0
    local _Space = 4

    _YPos = HUDHPPool.m_SizeY + _Space

    local SizeY
    local SizeX_Guild, SizeX_Name
    local _Module = self.m_Module[EHUDModule.Name]
    --盟徽與名稱
    if not string.IsNullOrEmpty(self.m_HUDData.m_GuildName) then
        -- Y
        SizeY = _YPos + (HUDNamePool.m_GuildY / 2)
        -- X
        local _GuidX = HUDNamePool.m_GuildX
        local _NameX = HUDNamePool.m_NameX
        local _Center = (_GuidX + _NameX) / 2
        SizeX_Guild = -_Center + (_GuidX / 2)
        SizeX_Name = _Center - (_NameX / 2)
        -- 更新下個起點
        _YPos = _YPos + HUDNamePool.m_GuildY + _Space
    else
        -- Y
        -- 暫時一律採用盟徽高度，如果未來有要縮排再換回來
        --SizeY = _YPos + (HUDNamePool.m_NameY / 2)
        SizeY = _YPos + (HUDNamePool.m_GuildY / 2)
        --X
        SizeX_Guild = 0
        SizeX_Name = 0
        -- 更新下個起點
        _YPos = _YPos + HUDNamePool.m_GuildY + _Space
    end
    -- 有物件就更新
    if _Module.m_hasComponent then
        _Module.m_ObjRect.localPosition = Vector3(0, SizeY, 0)
        _Module.m_GuildRect.localPosition = Vector3(SizeX_Guild, 0, 0)
        _Module.m_Name_Rect.localPosition = Vector3(SizeX_Name, 0, 0)
    end

    -- 稱號
    _Module = self.m_Module[EHUDModule.Title]
    if not string.IsNullOrEmpty(self.m_HUDData.m_TitleName) then
        -- Y
        SizeY = _YPos + (HUDTitlePool.m_SizeY / 2)
        -- 更新下個起點
        _YPos = _YPos + HUDTitlePool.m_SizeY + _Space
    end
    -- 有物件就更新
    if _Module.m_hasComponent then
        _Module.m_ObjRect.localPosition = Vector3(0, SizeY, 0)
    end

    -- Icon
    _Module = self.m_Module[EHUDModule.Icon]
    -- 有物件就更新
    if _Module.m_hasComponent then
        -- 有顯示
        if _Module.m_Obj.activeSelf then
            -- Y
            SizeY = _YPos + (HUDIconPool.m_SizeY / 2)
            -- 更新下個起點
            _YPos = _YPos + HUDIconPool.m_SizeY + _Space
            _Module.m_ObjRect.localPosition = Vector3(0, SizeY, 0)
        else
            _Module.m_ObjRect.localPosition = Vector3(0, 130, 0) -- 不加入排序，固定高度
        end
    end

    -- 泡泡框
    _Module = self.m_Module[EHUDModule.BubbleBox]
    if _Module.m_hasComponent then
        SizeY = _YPos + (HUDBubblePool.m_SizeY / 2)
        _Module.m_ObjRect.localPosition = Vector3(0, SizeY, 0)
        _YPos = _YPos + HUDBubblePool.m_SizeY + _Space
    end

    -- 表情符號
    _Module = self.m_Module[EHUDModule.Emoji]
    if _Module.m_hasComponent then
        SizeY = _YPos + (HUDEmojiPool.m_SizeY / 2)
        _Module.m_ObjRect.localPosition = Vector3(0, SizeY, 0)
        _YPos = _YPos + HUDEmojiPool.m_SizeY + _Space
    end
end

--- 檢查各 HUD 元件是否顯示
function HUDController:CheckEnable()
    local _IsActive = false

    if not self.m_Relation then
        return false
    end

    --region 血條
    -- NPC 規則：
    -- 1. 預設不顯示 HP
    -- 2. 選擇中的敵人才顯示 HP

    -- Player 規則：
    -- 1. 預設不顯示
    -- 2. 敵人顯示 HP

    -- NPC
    if self.m_RoleType == ERoleType.NPC then
        -- 預設不顯示
        _IsActive = false

        -- 被選擇中的敵人會顯示 HP
        if self.m_isSelected and self.m_Relation[ERelatType.Enemy] then
            _IsActive = true
        end
    else
        -- 敵人會顯示 HP
        if self.m_Relation[ERelatType.Enemy] then
            _IsActive = true
        else
            _IsActive = false
        end

        -- 被選擇 或 玩家本人會顯示血條
        if self.m_isSelected or self.m_RoleType == ERoleType.Self then
            _IsActive = true
        end
    end

    if m_HUDDebug then
        _IsActive = true
    end

    -- 從關閉顯示 -> 開啟顯示，才更新數值
    if not self.m_Module[EHUDModule.HP].m_hasComponent and _IsActive then
        self:SetHUDDirty(EHUD_UpdateChecker.HP)
    end

    self.m_Module[EHUDModule.HP].m_hasComponent = _IsActive
    --endregion

    --region 名稱
    _IsActive = false

    -- NPC 規則：
    -- 1. NPC Mode 6、77 選擇時才顯示名字(敵方)
    -- 2. NPC Mode 14 採集物有任務推演時才顯示名字
    -- 3. 其餘 NPC Mode 皆常駐顯示名字

    -- Player 規則：
    -- 1. 路人 HUD 系統設置開關

    if self.m_RoleType == ERoleType.NPC then
        if self.m_Relation[ERelatType.Enemy] then
            _IsActive = false
        elseif self.m_HUDData.m_NPCMode == EventNPCMode.Collection then
            _IsActive = SearchMgr.IsCollectableNPC(self.m_HUDData)
        else
            _IsActive = true
        end
    else
        _IsActive = true
    end

    if self.m_isSelected or m_HUDDebug then
        _IsActive = true
    end

    -- 從關閉顯示 -> 開啟顯示，才更新數值
    if not self.m_Module[EHUDModule.Name].m_hasComponent and _IsActive then
        self:SetHUDDirty(EHUD_UpdateChecker.Name)
    end

    self.m_Module[EHUDModule.Name].m_hasComponent = _IsActive
    --endregion

    --region 幫徽
    --[[if not self.m_GuildBG_Img or Extension.IsUnityObjectNull(self.m_GuildBG_Img) then
        return false
    end
    self.m_GuildBG_Img.gameObject:SetActive(false) -- 先都關掉，之後有功能再看怎麼實作]]
    --endregion

    --region 稱號
    _IsActive = false
    if not string.IsNullOrEmpty(self.m_HUDData.m_TitleName) then
        _IsActive = true
    end

    -- 從關閉顯示 -> 開啟顯示，才更新數值
    if not self.m_Module[EHUDModule.Title].m_hasComponent and _IsActive then
        self:SetHUDDirty(EHUD_UpdateChecker.Title)
    end

    self.m_Module[EHUDModule.Title].m_hasComponent = _IsActive
    --endregion

    --region Icon
    _IsActive = true
    -- 目前只有 NPC 頭上會有Icon
    if self.m_RoleType ~= ERoleType.NPC then
        _IsActive = false
    elseif self.m_HUDData.m_NPCMode then
        -- 暫時使用MapIconSetting取圖，之後出現歧異則要分割出HUD使用
        -- 取到的IconType沒對應圖
        local _IconType = MapIconSetting.NpcModeToMapIcon[self.m_HUDData.m_NPCMode]
        if not _IconType or _IconType == EMapIconsType.MasterPoint then
            _IsActive = false
        else
            self.m_Module[EHUDModule.Icon].m_IconType = _IconType
        end
    end

    -- 從關閉顯示 -> 開啟顯示，才更新數值
    if not self.m_Module[EHUDModule.Icon].m_hasComponent and _IsActive then
        self:SetHUDDirty(EHUD_UpdateChecker.Icon)
    end

    self.m_Module[EHUDModule.Icon].m_hasComponent = _IsActive
    --endregion

    --region BubbleBox
    -- 是否顯示BubbleBox交由
    -- HUController:StartBubbleBox (開)
    -- 與 HUDController:ShowBubbleBox (關) 控制
    --endregion

    --region 核心技量條
    --- 只有玩家本人才顯示、且要有核心技 Buff 才顯示
    _IsActive = self.m_RoleType == ERoleType.Self and (self.m_Module[EHUDModule.CoreSkill].m_PassiveType and self.m_Module[EHUDModule.CoreSkill].m_PassiveType > 0)

    if not self.m_Module[EHUDModule.CoreSkill].m_hasComponent and _IsActive then
        self:SetHUDDirty(EHUD_UpdateChecker.CoreSkill)
    end

    self.m_Module[EHUDModule.CoreSkill].m_hasComponent = _IsActive
    --endregion

    -- 重新取得當前 HUD 構成
    HUDFactory.GetHUD(self, false)
    return true
end

---刷新血條 HUD
function HUDController:UpdateHpBar()
    local _Module = self.m_Module[EHUDModule.HP]
    if not _Module.m_hasComponent then
        return false
    end

    local _MaxHp = math.max(self.m_HUDData.m_CurShield + self.m_HUDData.m_CurHp, self.m_HUDData.m_MaxHp)

    local _HpPercentage = self.m_HUDData.m_CurHp / _MaxHp
    _Module.m_HPBar.fillAmount = _HpPercentage
    local _ShieldPercentage = self.m_HUDData.m_CurShield / _MaxHp
    _Module.m_ShieldBar.fillAmount = _ShieldPercentage
    _Module.m_ShieldBar.transform.anchoredPosition = Vector2(
            _Module.m_ShieldBar.transform.sizeDelta.x * _HpPercentage,
            _Module.m_ShieldBar.transform.anchoredPosition.y)

    --泡泡框
    if self.m_HPObserveInit then
        BubbleMgr.TriggerHPBubble(self.m_HUDData.m_SID, _HpPercentage, self.m_HPBubbleStage)
    end

    return true
end

---刷新血條顏色
function HUDController:RefreshHpBarColor()
    if not self.m_Relation then
        return false
    end

    local _Module = self.m_Module[EHUDModule.HP]
    if not _Module.m_hasComponent then
        return false
    end

    if not RoleMgr.m_PKState or not RoleMgr.m_PKEnable then
        return false
    end

    if self.m_RoleType == ERoleType.Self then
        _Module.m_Gradient_HPBar.Gradient.colorKeys = _Gradient_Self_ColorKeyAy
        _Module.m_Gradient_HPBar:Refresh()
        return true
    end

    if self.m_HUDData.m_NPCMode then -- NPC顯示紅色
        _Module.m_Gradient_HPBar.Gradient.colorKeys = _Gradient_Other_ColorKeyAy
    else
        if self.m_Relation[ERelatType.Enemy] then -- 敵方玩家
            if RoleMgr.m_PKState >= EPKState.Red and self.m_HUDData.m_PKEnable == 0 then
                -- 玩家本人是惡人/現行犯 且該玩家沒開 PK 顯示原顏色
                _Module.m_Gradient_HPBar.Gradient.colorKeys = _Gradient_Self_ColorKeyAy
            else
                _Module.m_Gradient_HPBar.Gradient.colorKeys = _Gradient_Other_ColorKeyAy
            end
        else
            _Module.m_Gradient_HPBar.Gradient.colorKeys = _Gradient_Self_ColorKeyAy
        end
    end

    _Module.m_Gradient_HPBar:Refresh()
    return true
end

local _Dash = "_"
---刷新名稱 HUD
function HUDController:RefreshName()
    local _Module = self.m_Module[EHUDModule.Name]
    if not _Module.m_hasComponent then
        return false
    end

    local _Name = ""

    function AddDash()
        if _Name ~= "" then
            _Name = string.Concat(_Name, _Dash)
        end
    end

    if BattleMgr.m_IS_BATTLE_DEBUG then
        AddDash()
        _Name = string.Concat(_Name, tostring(self.m_HUDData.m_SID))
    end

    if NPCMgr.m_IS_SHOWNPCID and self.m_HUDData.m_ID then
        AddDash()
        _Name = string.Concat(_Name, tostring(self.m_HUDData.m_ID))
    end

    if NPCMgr.m_IS_SHOWNPCEVENTID and self.m_HUDData.m_EventID then
        AddDash()
        _Name = string.Concat(_Name, tostring(self.m_HUDData.m_EventID))
    end

    AddDash()
    _Name = string.Concat(_Name, self.m_HUDData.m_Name)

    if self.m_StateController and BattleMgr.m_IS_BATTLE_DEBUG then
        _Name = _Name .. "\n<color=#23FFD4>" .. table.GetKey(EStateType, self.m_StateController:GetStateType())
        if self.m_RoleType == ERoleType.Self and AutoBattleMgr.m_AutoBattleState == AutoBattleMgr.EAutoBattleState.Play then
            _Name = _Name .. "_<color=#23FFD4>" .. table.GetKey(EAutoBattleStateType, AutoBattleMgr.GetStateType())
        end
    end

    _Module.m_Name_TMP.text = _Name

    return true
end

---刷新名稱顏色
function HUDController:RefreshNameColor()
    if not self.m_Relation then
        return false
    end

    local _Module = self.m_Module[EHUDModule.Name]
    if not _Module.m_hasComponent then
        return false
    end

    -- 還沒收到本人 PK 相關數值，先不更新
    if not RoleMgr.m_PKState or not RoleMgr.m_PKEnable then
        return false
    end

    if self.m_RoleType == ERoleType.Self then
        if RoleMgr.m_PKState >= EPKState.Red then -- 變成惡人/現行犯
            _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_PO_01)
        else
            _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_OO)
        end

        return true
    end

    -- 有 NPCMode = NPC
    if self.m_HUDData.m_NPCMode then
        if self.m_Relation[ERelatType.Enemy] then
            -- 使用等級區間分色
            local _LVGaps = self.m_HUDData.m_Lv - PlayerData.GetLv()

            if _LVGaps <= m_LVGap.Lower_2 then
                -- 灰色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_GO_03)
            elseif _LVGaps <= m_LVGap.Lower_1 then
                -- 淺綠
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_GO_02)
            elseif _LVGaps <= m_LVGap.Equal then
                -- 黃色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_OO)
            elseif _LVGaps <= m_LVGap.Higher_1 then
                -- 橘色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_OO_01)
            elseif _LVGaps <= m_LVGap.Higher_2 then
                -- 紅色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_RO_01)
            elseif _LVGaps > m_LVGap.Higher_2 then
                -- 紫色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_PO_01)
            end
        else
            -- 不是敵人顯示黃色
            _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_OO)
        end
    else
        -- 不是 NPC 的判斷
        -- 是不是敵人
        if self.m_Relation[ERelatType.Enemy] then
            if self.m_HUDData.m_PKState >= EPKState.Red then
                -- 該玩家是現行犯/惡人
                -- 紫色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_PO_01)
            else
                -- 普通敵人
                -- 紅色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_RO_01)
            end
        else
            -- 是不是隊友
            if self.m_Relation[ERelatType.Teammate] then
                -- 是不是隊友
                -- 藍色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_BO_01)
            elseif self.m_Relation[ERelatType.Guild] then
                -- 是不是同幫
                -- 綠色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_GO_01)
            else
                -- 普通人
                -- 黃色
                _Module.m_Name_TMP.textStyle = TMP_Settings.defaultStyleSheet:GetStyle(m_TextStyle.TextStyle_OO)
            end
        end
    end

    return true
end

---刷新稱號 HUD
function HUDController:RefreshTitle()
    local _Module = self.m_Module[EHUDModule.Title]
    if not _Module.m_hasComponent then
        return false
    end

    if not self.m_HUDData.m_TitleName then
        return false
    end

    _Module.m_Title_TMP.text = self.m_HUDData.m_TitleName
    return true
end

---刷新 Icon
function HUDController:RefreshIcon()
    if not self.m_RoleType then
        return false
    end

    local _Module = self.m_Module[EHUDModule.Icon]
    if not _Module.m_hasComponent then
        return true
    end

    local _FinalType = EMapIconsType.Non
    local _EventTipType = EventMgr:TryGetMissionID(self.m_HUDData.m_EventID, self.m_HUDData.m_NPCMode)
    if _EventTipType then
        _FinalType = MapIconSetting.MissionTypeToMapIcon:GetCase(_EventTipType)
    end

    -- 決定要取什麼Icon
    if _Module.m_IconType == EMapIconsType.Mission and _FinalType == EMapIconsType.Non then
        -- 是任務NPC但沒取到任務Icon
        _Module.m_Obj:SetActive(false) -- 關掉Icon顯示
        return true
    elseif _FinalType == EMapIconsType.Non then
        -- 不是任務NPC，也沒取到任務Icon
        _FinalType = _Module.m_IconType -- 使用原本的Icon
    end

    local _IsSuccess, _Data = {}
    _IsSuccess, _Data = MapIconSetting.m_Dict_MapIcon:TryGetValue(_FinalType, _Data)
    if _IsSuccess then
        _Module.m_NPCIcon.sprite = _Data.m_Sprite
    end

    if _Module.m_Obj.activeSelf ~= _IsSuccess then
        _Module.m_Obj:SetActive(_IsSuccess)
        -- Icon 切換顯示狀態要重新排序
        self:Sort()
    end

    return true
end

---刷新核心技
function HUDController:RefreshCoreSkill()
    local _Module = self.m_Module[EHUDModule.CoreSkill]
    if not _Module.m_hasComponent then
        return true
    end

    -- 還沒載好 Obj
    if not _Module.m_CoreSkillHUD or not _Module.m_CoreSkillHUD.m_Inited then
        return false
    end

    _Module.m_CoreSkillHUD:UpdateValue(_Module.m_Value)

    -- 不使用 Sort，核心技的排序規則讀取後自定義
    return true
end

---標記要更新的 HUD 資訊
---@param iHUDChecker EHUD_UpdateChecker
function HUDController:SetHUDDirty(iHUDChecker)
    if not self.m_UpdateList then
        return
    end

    if self.m_UpdateList[iHUDChecker] then
        return
    end

    self.m_UpdateList[iHUDChecker] = true
end

--- 更新關係資訊
--- 更新後會影響 HUD 的變色與顯示
---@param iState RelationshipState
function HUDController:RefreshRelationship(iState)
    self.m_Relation = iState
    self:SetHUDDirty(EHUD_UpdateChecker.Enable)
    self:SetHUDDirty(EHUD_UpdateChecker.Color)
end

--- 被選擇通知
---@param iRC RoleController
---@param iLastSelectRC RoleController
function HUDController.OnTargetChange(iRC, iLastSelectRC)

    if iRC and iRC.m_HUDController then
        iRC.m_HUDController.m_isSelected = true
        iRC.m_HUDController:CheckEnable()
    end

    if iLastSelectRC and iLastSelectRC.m_HUDController then
        iLastSelectRC.m_HUDController.m_isSelected = false
        iLastSelectRC.m_HUDController:CheckEnable()
    end

end

---@param iEHUD_Checker EHUD_UpdateChecker
function HUDController.RefreshAllHUD(iEHUD_Checker)
    for k, v in pairs(m_HUDCollector) do
        table.insert(
            v.m_ForceUpdate,
            function()
                v:SetHUDDirty(iEHUD_Checker)
            end
        )
    end
end

--region 戰鬥跳字
---定義戰鬥文字的UI
function HUDController.OpenBattleText()
    local _inSceneName = BattleText_Controller.m_UIView:gsub(m_Str_View, "")
    local _UITransform = HUDCanvas:Find(_inSceneName)
    if _UITransform ~= nil then
        --this.InitUI(iUI, _UITransform.gameObject, iIsOpen, iIsForceUseArgs, iArgs)
    else ---讀取資源
        ResourceMgr.Load(
            BattleText_Controller.m_UIView,
            function(iAsset)
                local _UIObj = iAsset
                m_GObj_BattleText = _UIObj
                if Extension.IsUnityObjectNull(_UIObj) then
                    D.LogError(string.Concat(BattleText_Controller._UIView, ": can`t found asset"))
                    return
                end

                _UIObj.transform:SetParent(HUDCanvas)
                _UIObj.transform.localPosition = Vector3.zero
                _UIObj.transform.localScale = Vector3.one
                _UIObj.transform.localRotation = Quaternion.identity

                local _Canvas = Extension.AddMissingComponent(_UIObj, typeof( UnityEngine.Canvas ))
                _Canvas.overrideSorting = true
                _Canvas.sortingOrder = HUDSetting.m_BattleTextOrder

                local _RectTrans = _UIObj:GetComponent(typeof(RectTransform))
                _RectTrans.anchorMin = Vector2.zero
                _RectTrans.anchorMax = Vector2.one
                _RectTrans.anchoredPosition = Vector2.zero
                _RectTrans.sizeDelta = Vector2.zero

                local _ViewRef = _UIObj:GetComponent("ViewRef")
                if _ViewRef == nil then
                    D.logError(string.Concat(BattleText_Controller.m_UIController, "UIRef Not Found !!"))
                    return
                end
                BattleText_Controller.m_ViewRef = _ViewRef
                BattleText_Controller.m_MainPanel = {}
                if BattleText_Controller.m_MainPanel == nil or BattleText_Controller.m_MainPanel.rectTransform == nil then
                    if BattleText_Controller.m_UIBoundaryTarget ~= nil then
                        BattleText_Controller.m_MainPanel.rectTransform =
                            BattleText_Controller.m_UIBoundaryTarget.RectTransform
                    end
                end
                ---UI 去掉 _View 當名字
                _UIObj.name = _inSceneName

                _ViewRef:Init()
                BattleText_Controller.Init()
            end
        )
    end
end
--endregion

--region 泡泡框
--- 開始執行泡泡框
---@param iData BubbleDialogData
function HUDController:StartBubbleBox(iData)
    local _Module = self.m_Module[EHUDModule.BubbleBox]
    if iData.m_NPCSID ~= self.m_HUDData.m_SID then
        D.LogWarning("[HUD] SID 不同!!" .. iData.m_NPCSID .. ", " .. self.m_HUDData.m_SID)
        return false
    end

    self:KillBubbleBox()

    local _BubbleState = self.m_BubbleState
    _BubbleState.m_IsBubbling = true
    _BubbleState.m_FullMsg = iData.m_Msg
    _BubbleState.m_CanvasAlpha = 0
    _BubbleState.m_DelayTime = iData.m_DelayTime

    if not _Module.m_hasComponent then
        _Module.m_hasComponent = true
        HUDFactory.GetHUD(self, false)
    end

    self:ShowBubbleBox()

    if BubbleMgr.isDebug() then
        BubbleMgr.DebugLog("StartBubbleBox NPCID: " .. iData.m_NPCSID .. " Traceback: " .. debug.traceback())
    end

    return true
end

---Step1的泡泡框tween時的function
---@param iTable table<number,HUDController>
local function BubbleStateOne(iParam)--iHUDController, iNeedScroll
    ---@type HUDController
    local _HUDController = iParam[1]

    local _NeedScroll = iParam[2]
    
    if _NeedScroll then
        _HUDController:DoBubbleTextMotion()
        return
    end

    _HUDController:ShowBubbleBox()
end

local m_BubbleStateTable = {}

local function BubbleFadeIn(iHUDController)

    iHUDController.m_BubbleState.m_TextStep = 0
    iHUDController.m_BubbleState.m_Step = 1
    iHUDController:ShowBubbleBox()
end

---顯示泡泡框流程
---@param iData BubbleDialogData
function HUDController:ShowBubbleBox()
    local _BubbleState = self.m_BubbleState
    local _Module = self.m_Module[EHUDModule.BubbleBox]

    if BubbleMgr.isDebug() then
        BubbleMgr.DebugLog(
            "Trigger Show Bubble, ID: " ..
                self.m_HUDData.m_SID .. "Name: " .. self.m_HUDData.m_Name .. "Step: " .. _BubbleState.m_Step,
            "gray"
        )
    end

    -- 結束泡泡框顯示效果
    if _BubbleState.m_Step == 4 then
        HEMTimeMgr.DoFunctionDelay(_BubbleState.m_DelayTime / 1000, BubbleMgr.QueryNextDialog, self.m_HUDData.m_SID)

        _BubbleState:Init()
        _Module.m_hasComponent = false
        HUDFactory.GetHUD(self, false)
        return
    end

    if _BubbleState.m_SeqID ~= 0 then
        LeanTween.cancel(_BubbleState.m_SeqID)
    end

    if _BubbleState.m_Step == 0 then
        -- 淡入
        _Module.m_TMP_BubbleContent.text = _BubbleState:GetMsgContent(_BubbleState.m_FullMsg)
        _BubbleState.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup, 1, BubbleMgr.FadeTime / 1000):setOnComplete(
                System.Action_object(HUDController.ShowBubbleBox, self)
            ).id
    elseif _BubbleState.m_Step == 1 then
        if m_BubbleStateTable[self] == nil then
            m_BubbleStateTable[self] = {self, _BubbleState.m_NeedScroll}
        end

        m_BubbleStateTable[self][2] = _BubbleState.m_NeedScroll
        
        -- 持續顯示
        _BubbleState.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup, 1, BubbleMgr.DisPlayTime / 1000):setOnComplete(
                System.Action_object(BubbleStateOne, m_BubbleStateTable[self])
            ).id
    elseif _BubbleState.m_Step == 2 then
        -- 淡出
        _BubbleState.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup, 0, BubbleMgr.FadeTime / 1000):setOnComplete(
                System.Action_object(HUDController.ShowBubbleBox, self)
            ).id
    elseif _BubbleState.m_Step == 3 then
        -- 隱藏
        _BubbleState.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup, 0, BubbleMgr.WaitTime / 1000):setOnComplete(
            System.Action_object(HUDController.ShowBubbleBox, self)
        ).id
    end

    _BubbleState.m_Step = _BubbleState.m_Step + 1
end

---泡泡框內容分段顯示流程
function HUDController:DoBubbleTextMotion()
    local _Module = self.m_Module[EHUDModule.BubbleBox]
    local _State = self.m_BubbleState
    _State.m_TextStep = _State.m_TextStep + 1

    if _State.m_TextStep == 1 then
        -- 內容字淡出
        _State.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup_BubbleContent, 0, 0.5):setOnComplete(
                System.Action_object(HUDController.DoBubbleTextMotion, self)
            ).id
    elseif _State.m_TextStep == 2 then
        -- 內容字淡入
        _Module.m_TMP_BubbleContent.text = _State:GetMsgContent(_State.m_StoreMsg)
        _State.m_SeqID =
            LeanTween.alphaCanvas(_Module.m_CanvasGroup_BubbleContent, 1, 0.5):setOnComplete(
                System.Action_object(BubbleFadeIn, self)
            ).id
    end
end

--- 清除正在運行的泡泡框效果
function HUDController:KillBubbleBox()
    if not self.m_BubbleState then
        return
    end

    local _SeqID = self.m_BubbleState.m_SeqID
    if LeanTween.isTweening(_SeqID) then
        LeanTween.cancel(_SeqID)
        if BubbleMgr.isDebug() then
            BubbleMgr.DebugLog("Is Tweening", "yellow")
        end
    end
end

--- 停止泡泡框
function HUDController:StopBubbleBox()
    local _State = self.m_BubbleState
    if not _State then
        return
    end

    if _State.m_IsBubbling then
        _State.m_IsBubbling = false
        self:KillBubbleBox()
        local _Module = self.m_Module[EHUDModule.BubbleBox]
        _Module.m_hasComponent = false
        HUDFactory.GetHUD(self, false)
    end
end

---註冊血量變動發話
function HUDController:RegisterHPObserve(iBool)
    if not self.m_HPObserveInit then
        self:SetHPCurrentOrder(-1)
    end

    self.m_HPObserveInit = iBool
end

function HUDController:SetHPCurrentOrder(iStage)
    local _StageChange = false
    if self.m_HPBubbleStage ~= iStage then
        self.m_HPBubbleStage = iStage
        _StageChange = true
    end
    return _StageChange
end

function HUDController:ReleaseBubble()
    if not self.m_BubbleState then
        return
    end

    self:StopBubbleBox()
    self.m_BubbleState:Init()
    self:RegisterHPObserve(false)
    self:SetHPCurrentOrder(nil)

    BubbleMgr.RemoveDialog(self.m_HUDData.m_SID)
end
--endregion

--region 核心技量條 ( 目前只有玩家自己才看得到 )
function HUDController:SetCoreSkillInfo(iPassiveType, iValue)
    local _Module = self.m_Module[EHUDModule.CoreSkill]
    if not _Module then
        D.LogWarning("太早了同學")
        return
    end

    if _Module.m_PassiveType ~= iPassiveType then
        _Module.m_PassiveType = iPassiveType
        self:SetHUDDirty(EHUD_UpdateChecker.Enable)
    end

    _Module.m_Value = iValue
    self:SetHUDDirty(EHUD_UpdateChecker.CoreSkill)
end
--[[
--- 設定集氣
function HUDController:SetPowerSkill(iTime, iMaxLight, iNowLight)
    if self.m_Slider_PowerSkill then
        self.m_Slider_PowerSkill.gameObject:SetActive(true)
        self.m_Slider_PowerSkill.maxValue = iTime

        if self.m_PowerSkillTweenId then
            LeanTween.cancel(self.m_PowerSkillTweenId)
        end

        self.m_Slider_PowerSkill.value = 0
        self.m_PowerSkillTweenId =
            LeanTween.value(
            self.m_Slider_PowerSkill.gameObject,
            System.Action_float(
                function(iValue)
                    self.m_Slider_PowerSkill.value = iValue
                end), 0, iTime, iTime).id

        for i = 1, HUDSetting.m_LightMaxCount do
            if i < iMaxLight then
                self.m_PowerLight.m_Trans_Light[i].gameObject:SetActive(true)
            else
                self.m_PowerLight.m_Trans_Light[i].gameObject:SetActive(false)
            end

            if (iMaxLight - i) < iNowLight then
                self.m_PowerLight.m_Image_Light[i].gameObject:SetActive(true)
            else
                self.m_PowerLight.m_Image_Light[i].gameObject:SetActive(false)
            end
        end
    end
end

--- 取消集氣
function HUDController:CancelPowerSkill()
    if self.m_Slider_PowerSkill then
        self.m_Slider_PowerSkill.gameObject:SetActive(false)
        if self.m_PowerSkillTweenId then
            LeanTween.cancel(self.m_PowerSkillTweenId)
            self.m_PowerSkillTweenId = nil
        end

        for i = 1, HUDSetting.m_LightMaxCount do
            self.m_PowerLight.m_Trans_Light[i].gameObject:SetActive(false)
            self.m_PowerLight.m_Image_Light[i].gameObject:SetActive(false)
        end
    end
end]]
--endregion 核心技量條 ( 只有玩家自己才看得到 )


---region 表情符號

---取得特定RoleID的 HUDController
---@param iRoleID 腳色roleID
function HUDController.GetpecficHUDFromRoleID(iRoleID)

    if m_HUDCollector[tostring(iRoleID)] ~= nil then
       return m_HUDCollector[tostring(iRoleID)]
    end
end

---開始演出對應Emoji
---@param iEmojiString string 要演出的Emoji prefab檔案
function HUDController:StartEmoji(iEmojiString)
    local _Module = self.m_Module[EHUDModule.Emoji]

    ---設定成演出中 設定演出prefab name
    local _EmojiState = self.m_EmojiState
    _EmojiState.m_IsShowing = true
    _EmojiState.m_EmojiName  = iEmojiString

    if not _Module.m_hasComponent then
        _Module.m_hasComponent = true
        HUDFactory.GetHUD(self, false)
    end

    self:EndEmoji()
    self:ShowEmoji()
end

---載入emoji 並且 開始播放
function HUDController:ShowEmoji()
    local _EmojiState = self.m_EmojiState
    local _Module = self.m_Module[EHUDModule.Emoji]

    ---不在播放中 播放新的
    --if _EmojiState.m_IsShowing== false then
    EmojiMgr.GetEmojiByName(_EmojiState.m_EmojiName,function(iHashCode,iObj)
        _EmojiState.m_HashCode = iHashCode
       _EmojiState.m_EmojiObj = iObj

        iObj.transform:SetParent(_Module.m_Obj.transform)
        iObj.transform.localPosition = Vector3(0,0,0)
        iObj.transform.localScale = Vector3(0.7,0.7,0.7)
        iObj:SetActive(true)

        local _AllTrans = iObj:GetComponentsInChildren(typeof(UnityEngine.Transform)):ToTable()

        for key,value in pairs(_AllTrans) do
            value.gameObject.layer = Layer.HUD
        end

        local _AnimationTime
        local _Animator = iObj:GetComponent("Animator")
        local _stateInfo = _Animator:GetCurrentAnimatorStateInfo(0);
        _AnimationTime = _stateInfo.length

        if _EmojiState.m_TweenID ~= 0 and LeanTween.isTweening(_EmojiState.m_TweenID) then
                LeanTween.cancel(_EmojiState.m_TweenID)
        end

        _EmojiState.m_TweenID =
        LeanTween.delayedCall(_AnimationTime * m_EmojiPlayTime, System.Action(
            function()
                self:EndEmoji(true)
            end)).id
    end)

end

---關閉目前演出中的emoji並歸還物件
---@param iNeedChangeLayer bool 是否要切換layer 回收時默認將所有layer都改成UI 理論上只有生給HUD使用的emoji需要改回去其他會默認Default
function HUDController:EndEmoji(iNeedChangeLayer)

    if not self.m_EmojiState then
        return
    end

    if self.m_EmojiState.m_HashCode ~= nil then
        EmojiMgr.ReturnEmojiByNameAndHashCode(self.m_EmojiState.m_HashCode,iNeedChangeLayer)
        self.m_EmojiState.m_HashCode = nil
    end

end

---ndregion 表情符號
function HUDController:OnUnrequire()
    if m_GObj_BattleText~=nil then
        m_GObj_BattleText:Destroy()
    end
    HUDFactory.OnUnrequire()
    return true
end