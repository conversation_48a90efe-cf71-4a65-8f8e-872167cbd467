---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---CommonIcon 繼承自 BasicIcon
---@class CommonIcon
---author 侑薰
---telephone #2872
---version 1.0
---since [黃易群俠傳M] 9.9
---date 2025.4.15
CommonIcon = setmetatable( {}, { __index = BasicIcon } )

---從 Asset 讀取的暫存
---@type GameObject
local m_Tmp = nil

---初始化, 進行 Prefab 讀取
function CommonIcon.Init( iParent )
    CommonIcon.LoadResources( "CommonIcon", iParent,
            function( iAsset )
                m_Tmp = iAsset
            end )
end

function CommonIcon:New( iData, iParent, iWidth, iOnClick, iOnClickParam )
    local function OnIconClick()
        if iOnClick then
            iOnClick(iOnClickParam)
        end
    end

    local _Icon = BasicIcon:New( m_Tmp, EIconType.Common, 0, iParent, iWidth, OnIconClick, nil, nil, nil )
    if not _Icon then
        D.LogError("Create New Icon failed.")
        return nil
    end

    setmetatable( _Icon, { __index = self } )

    _Icon:SetNeedOpenHint(true)
         :SetNeedLongPress(true)
        --  :SetLongPressCallback(BasicIcon.OpenBasicHint)
         :SetClickTwice(false)

    -- 選擇框
    if _Icon.m_Trans_Select == nil then
        _Icon.m_Trans_Select = _Icon.transform:Find( "Image_Select" ):GetComponent( typeof( Image ) )
        _Icon.m_Tween_Select = _Icon.m_Trans_Select:GetComponent( typeof( LeanTweenVisual ) )
        _Icon.m_Tween_Select.enabled = false

        _Icon:SetObjectActive(_Icon.m_Trans_Select, false)
    end

    if iParent then
        _Icon.transform:SetParent( iParent )
    end

    ---設定數量
    if _Icon.m_TMP_Count == nil then
        _Icon.m_TMP_Count = _Icon.transform:Find( "TMP_Count" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
    end

    ---特效背景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectBack == nil then
        _Icon.m_Trans_EffectBack = _Icon.transform:Find( "Trans_EffectBack" )
    end

    ---特效前景 (因為特效要擺在 icon 後面，所以才有這個)
    if _Icon.m_Trans_EffectFront == nil then
        _Icon.m_Trans_EffectFront = _Icon.transform:Find( "Trans_EffectFront" )
    end

    ---設定遮罩
    if _Icon.m_Img_Mask == nil then
        _Icon.m_Img_Mask = _Icon.transform:Find( "Image_Mask" )
    end

    ---不被IconMgr IconMgr.CancelAllClick() 清除選擇，體現在開Hint時不會被統一清除
    _Icon.m_NeedClearSelect = true

    _Icon:RefreshIcon(iData)

    return _Icon
end

--- 設定稀有度外框
function CommonIcon:SetRank( iRank )
    --- 新增物品稀有度到 8 Modify by hui 2025.03.31
    if iRank > ERank.Rainbow then
        iRank = ERank.Rainbow
    end
    self.m_Rank = iRank
    local _self, _IsNewFrame = self:SetFrameType(iRank)
    if _IsNewFrame == true then
        self.m_Trans_EffectBack.transform:SetAsFirstSibling()
    end

    return self
end

--- 設定外框圖
function CommonIcon:SetFrameImage(iRank)
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            -- 存下所有可變色區塊
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end

    for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
        self.m_FrameImage[key1].sprite = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].sprite
        self.m_FrameImage[key1].material = IconMgr.m_FrameIcon.m_ItemFrameIcon[iRank][key1].material
    end

    return self
end

---設定物品數量
---@param iCount number 數量(number)
---@param iCount string 要顯示的數量字串(string)
---@param iStyle string 請給 style (優先度高於原始設定)
---@param iWithoutSizeController boolean 不受尺寸控制
function CommonIcon:SetCount( iCount, iStyle, iWithoutSizeController )
    if iCount ~= nil then
        if type(iCount) == "number" then
            self.m_TMP_Count.text = tostring( iCount > 0 and GValue.CountToString(iCount, EValueShowType.Short) or "" )

            if iCount > IconSetting.m_ITEMMAXCOUNT then
                self.m_TMP_Count.text = GString.GetTextWithSize(self.m_TMP_Count.text, IconSetting.IconTextSizeSmall)
            end

            -- 改用是否有idx決定要不要顯示數量
            self:SetObjectActive(self.m_TMP_Count, iCount > 0)

            self.m_Count = iCount

            self.m_TMP_Count.alignment = TMPro.TextAlignmentOptions.BottomRight;
        elseif type(iCount) == "string" then
            self.m_TMP_Count.text = iCount

            self:SetObjectActive(self.m_TMP_Count, not string.IsNullOrEmpty(iCount))

            self.m_TMP_Count.alignment = TMPro.TextAlignmentOptions.Bottom;

            if iStyle == nil then
                self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, "WO_01")
            end
        end
    else
        self:SetObjectActive(self.m_TMP_Count, false)
    end

    if iStyle then
        self.m_TMP_Count.text = GString.StringWithStyle( self.m_TMP_Count.text, iStyle)
    end

    if iWithoutSizeController then
        ---計算 Scale, 採用縮放的方式才不會影響子物件定位點導致顯示異常
        local _Scale = 1 / self.transform.localScale.x
        self.m_TMP_Count.transform.localScale = Vector3( _Scale, _Scale, _Scale )
    else
        self.m_TMP_Count.transform.localScale = Vector3.one
    end

    return self
end

--- 設定選擇
function CommonIcon:ShowSelect( iIsSelect )
    self:SetObjectActive(self.m_Trans_Select, iIsSelect)

    if iIsSelect == true then
        -- 點第一下解除新物品標記
        if self.m_SaveData ~= nil then
            self.m_SaveData.m_IsNew = false
        end
        self.m_Trans_Select.sprite = IconMgr.m_IconSelectImage
        self.m_Trans_Select.color = Color.White
        self.m_ClickOnce = true
    end
    self.m_Tween_Select.enabled = false
end

function CommonIcon:ShowSettingSelect( iIsSettingSelect )
    if iIsSettingSelect == true then
        self.m_Trans_Select.sprite = IconMgr.m_IconSettingImage
    end
    self.m_ClickOnce = nil
    self:SetObjectActive(self.m_Trans_Select, iIsSettingSelect)
    self.m_Tween_Select.enabled = iIsSettingSelect
    if iIsSettingSelect == true then
        self.m_Tween_Select:buildAllTweensAgain()
    end
end

--- 取消選擇
---@param iDoSelectAction boolean 是否執行選擇函式
function CommonIcon:CancelSelect(iDoSelectAction)
    UIMgr.OpenIconName(false)
    self:ShowSelect(false)
    self.m_ClickOnce = nil

    -- 給 icon 底層因為判斷不選取，不再次觸發 action 用
    if iDoSelectAction ~= false then
        self:DoSelectAction(false)
    end
end

--- 註冊 CommonIcon 資訊
---@param iIdx number Debug 模式下圖片左上角的 Idx
---@param iIconID number 圖片編號(圖片檔名會是"Icon"..iIconID)
---@param iIconName string 圖片名(目前只在 CommonHint 會用到(Hint圖片旁的標題))
---@param iIconRank ERank 稀有度(與物品稀有度相同)(若不填，預設為白(1))
---@param iIconCount number 數量(可不填)
---@return CommonIcon
function CommonIcon:SetIconData( iIdx, iIconID, iIconName, iIconRank, iIconCount )
    self.m_Idx = iIdx
    self.m_IconStr = ICON_STR .. GValue.Zero_stuffing(iIconID, 6)
    self.m_Name = iIconName
    self.m_Rank = iIconRank
    self.m_Count = iIconCount
    return self
end

---重設 Icon 資訊
---@param iData number Icon ID(number)
---@param iData table CommonIcon 資料(table): [1]:IconID, [2]:圖檔名稱, [3]:Icon名, [4]:稀有度, [5]:數量
---@param iIconType EIconType 讀表種類(無表可讀請用EIconType.Common)
function CommonIcon:RefreshIcon( iData, iIconType )
    local _IconId
    local _IconStr
    local _IconName
    local _IconRank
    local _IconCount
    if type(iData) == "number" then
        _IconId = iData
    elseif type(iData) == "table" then
        _IconId = iData.m_Idx
        _IconStr = iData.m_IconStr
        _IconName = iData.m_Name
        _IconRank = iData.m_Rank
        _IconCount = iData.m_Count
    else
        do return end
    end

    local function ResetIcon()
        self:RefreshBasicIcon(0)
        self:SetName()
            :SetRank(ERank.None)
            :SetCount(0)
    end

    if _IconId == 0 then
        ResetIcon()
        do return end
    end

    if iIconType == EIconType.Item then
        local _ItemData = ItemData.GetItemDataByIdx( _IconId )
        if _ItemData == nil then
            ResetIcon()
            do return end
        end
        self.m_ItemData = _ItemData

        self:RefreshBasicIcon(_IconId, self.m_ItemData:GetItemTextureName())
        self:SetName(self.m_ItemData:ItemName())
            :SetRank(self.m_ItemData.m_Rarity)
    elseif iIconType == EIconType.Common then
        self:RefreshBasicIcon(_IconId, _IconStr)

        if _IconName ~= nil then
            self:SetName(_IconName)
        else
            self:SetName("")
        end

        if _IconRank ~= nil then
            self:SetRank(_IconRank)
        else
            self:SetRank(ERank.White)
        end

        if _IconCount ~= nil then
            self:SetCount(_IconCount)
        else
            self:SetObjectActive(self.m_TMP_Count, false)
        end
    elseif iIconType == EIconType.Pet then
        
        local _PetData = PetData.GetPetDataByIdx( _IconId )
        if _PetData == nil then
            ResetIcon()
            do return end
        end
        ---取得對應物品 判斷稀有度 
        self:SetRank(_PetData.m_PetRank)

        self:RefreshBasicIcon(_IconId, _PetData:GetPetTextureName())
    end

    self:ShowSelect( false )
end

--region 開關 Hint
function CommonIcon:OpenHint()
    if not self.m_NeedOpenHint or self.m_Idx == 0 then
        return
    end

    IconMgr.CancelAllClick()
    HintMgr_Controller.OpenHint(EHintType.CommonHint, self)
end

function CommonIcon:CloseHint()
    -- UIMgr.Close( CommonHint_Controller )
end
--endregion 開關 Hint

--- 設定 UI Controller
---@param iUIController UIControllerBase UIController
function CommonIcon:SetIconUIController(iUIController)
    if iUIController ~= nil then
        self.m_UIController = iUIController
    end
end


---顯示/關閉遮罩
---@param iShowMask bool 是否顯示遮罩
function CommonIcon:ShowMask(iShowMask)
    self.m_Img_Mask.gameObject:SetActive(iShowMask)
end
