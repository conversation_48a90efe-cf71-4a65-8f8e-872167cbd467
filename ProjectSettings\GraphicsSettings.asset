%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!30 &1
GraphicsSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 14
  m_Deferred:
    m_Mode: 0
    m_Shader: {fileID: 0}
  m_DeferredReflections:
    m_Mode: 0
    m_Shader: {fileID: 0}
  m_ScreenSpaceShadows:
    m_Mode: 1
    m_Shader: {fileID: 64, guid: 0000000000000000f000000000000000, type: 0}
  m_LegacyDeferred:
    m_Mode: 0
    m_Shader: {fileID: 0}
  m_DepthNormals:
    m_Mode: 1
    m_Shader: {fileID: 62, guid: 0000000000000000f000000000000000, type: 0}
  m_MotionVectors:
    m_Mode: 1
    m_Shader: {fileID: 75, guid: 0000000000000000f000000000000000, type: 0}
  m_LightHalo:
    m_Mode: 1
    m_Shader: {fileID: 105, guid: 0000000000000000f000000000000000, type: 0}
  m_LensFlare:
    m_Mode: 1
    m_Shader: {fileID: 102, guid: 0000000000000000f000000000000000, type: 0}
  m_VideoShadersIncludeMode: 2
  m_AlwaysIncludedShaders:
  - {fileID: 7, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 10753, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 10770, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 10783, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 4800000, guid: 0d89a194f8ef0cb4a9bb9a793c4cc75a, type: 3}
  - {fileID: 4800000, guid: 17c474647a8c4e849a9f49d4bbda541b, type: 3}
  - {fileID: 4800000, guid: 9839189d918374a318d397a86e90aa73, type: 3}
  - {fileID: 4800000, guid: 2933b413a51fc4ff3a83c7ef4177ae84, type: 3}
  - {fileID: 4800000, guid: 33e06cd3c50064f30a691b600fc62f3f, type: 3}
  - {fileID: 4800000, guid: 9f5c4f1f1cdcc9348bfa00cc892c10b7, type: 3}
  - {fileID: 4800000, guid: 935b7be1c88464d2eb87204fdfab5a38, type: 3}
  - {fileID: 4800000, guid: e65241fa80a374114b3f55ed746c04d9, type: 3}
  - {fileID: 4800000, guid: 98210fc82c55b44748b9a8679828a56a, type: 3}
  - {fileID: 4800000, guid: 9f04b08d6b8294ad58254b2d246d730a, type: 3}
  m_PreloadedShaders:
  - {fileID: 20000000, guid: 0362d4aee6701334991046872d8295f3, type: 2}
  m_PreloadShadersBatchTimeLimit: -1
  m_SpritesDefaultMaterial: {fileID: 10754, guid: 0000000000000000f000000000000000,
    type: 0}
  m_CustomRenderPipeline: {fileID: 0}
  m_TransparencySortMode: 0
  m_TransparencySortAxis: {x: 0, y: 0, z: 1}
  m_DefaultRenderingPath: 1
  m_DefaultMobileRenderingPath: 1
  m_TierSettings:
  - serializedVersion: 5
    m_BuildTarget: 7
    m_Tier: 2
    m_Settings:
      standardShaderQuality: 1
      renderingPath: 1
      hdrMode: 2
      realtimeGICPUUsage: 25
      useReflectionProbeBoxProjection: 0
      useReflectionProbeBlending: 0
      useHDR: 1
      useDetailNormalMap: 0
      useCascadedShadowMaps: 1
      prefer32BitShadowMaps: 0
      enableLPPV: 0
      useDitherMaskForAlphaBlendedShadows: 0
    m_Automatic: 0
  - serializedVersion: 5
    m_BuildTarget: 4
    m_Tier: 2
    m_Settings:
      standardShaderQuality: 1
      renderingPath: 1
      hdrMode: 2
      realtimeGICPUUsage: 25
      useReflectionProbeBoxProjection: 0
      useReflectionProbeBlending: 0
      useHDR: 1
      useDetailNormalMap: 0
      useCascadedShadowMaps: 1
      prefer32BitShadowMaps: 0
      enableLPPV: 0
      useDitherMaskForAlphaBlendedShadows: 0
    m_Automatic: 0
  m_LightmapStripping: 1
  m_FogStripping: 1
  m_InstancingStripping: 0
  m_LightmapKeepPlain: 0
  m_LightmapKeepDirCombined: 1
  m_LightmapKeepDynamicPlain: 0
  m_LightmapKeepDynamicDirCombined: 0
  m_LightmapKeepShadowMask: 1
  m_LightmapKeepSubtractive: 0
  m_FogKeepLinear: 1
  m_FogKeepExp: 0
  m_FogKeepExp2: 0
  m_AlbedoSwatchInfos: []
  m_LightsUseLinearIntensity: 0
  m_LightsUseColorTemperature: 0
  m_DefaultRenderingLayerMask: 1
  m_LogWhenShaderIsCompiled: 0
  m_SRPDefaultSettings: {}
  m_CameraRelativeLightCulling: 0
  m_CameraRelativeShadowCulling: 0
