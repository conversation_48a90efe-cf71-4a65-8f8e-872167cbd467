---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---Icon 繼承底層
---"不要"在這裡分開處理衍伸類
---有需要對延伸Icon處理，到所屬腳本調整
---@class BasicIcon
---author KK
---version 2.0
---since [HEM 2.0]
---date 2024.02.15
BasicIcon = {}

function BasicIcon.LoadResources( iResourcesName, iParent, iLoadEnd )
    local _Tmp = nil
    ResourceMgr.Load( iResourcesName, function(iAsset)
        _Tmp = iAsset
        if _Tmp == nil then
            D.LogError( "Can`t found asset: " .. iResourcesName )
            return
        end

        _Tmp.name = "Tmp_" .. iResourcesName
        _Tmp.transform:SetParent( iParent )
        _Tmp.transform.localPosition = Vector3.zero
        _Tmp.transform.localScale = Vector3.one
        _Tmp.transform.localRotation = Quaternion.identity

        _Tmp:SetActive(false)
        ---測試 將物件關閉 iLoadEnd 之後 由各種類icon自行添加關閉物件方法 
        ---原本的code 保留
        --[[
        _Tmp:SetActive(false)
        ]]
        ---原本的code 保留 end

        if ProjectMgr.IsDebug() == false then
            ---Debug 使用的索引字串, 正式版不使用: 刪除
            GameObject.Destroy( _Tmp.transform:Find( "TMP_Idx" ).gameObject )
        end

        if iLoadEnd then
            iLoadEnd( _Tmp )
        end
    end )
end

function BasicIcon:New( iTmpGObj, iType, iIdx, iParent, iWidth, iOnClick, iOnPointerDown, iOnPointerUp, iMultSelectGroup )
    ---@type BasicIcon
    local _Icon = {}
    
    setmetatable( _Icon, {__index = self} )
    
    if Extension.IsUnityObjectNull(iTmpGObj) then
        return nil
    end
    
    _Icon.m_Type = iType
    
    ---複製物件
    _Icon.gameObject = GameObject.Instantiate( iTmpGObj )
    _Icon.gameObject:SetActive( true )
    _Icon.transform = _Icon.gameObject.transform
    _Icon.m_MultSelectGroup = iMultSelectGroup

    --- 是否初始化 icon 框架
    _Icon.m_InitFrameIcon = false

    if iParent then
        _Icon.transform:SetParent( iParent )
    end

    _Icon.transform.localPosition = Vector3.zero
    _Icon.m_RectTransform = Extension.AddMissingComponent( _Icon.gameObject, typeof( UnityEngine.RectTransform ) )

    ---計算 Scale, 採用縮放的方式才不會影響子物件定位點導致顯示異常
    _Icon.m_IconFixScale = iWidth / _Icon.m_RectTransform.rect.width
    _Icon.transform.localScale = Vector3( _Icon.m_IconFixScale, _Icon.m_IconFixScale, _Icon.m_IconFixScale )

    --直接讓 m_UIEvent 有兩個 Script 繼承
    _Icon.m_UIEvent = Button.New( Extension.AddMissingComponent( _Icon.gameObject, typeof( ButtonEx ) ) )
    _Icon.m_UIEvent = EventTrigger.New(_Icon.m_UIEvent, true)

    _Icon.m_UIEvent:AddListener(EventTriggerType.PointerClick, BasicIcon.OnBasicClick, _Icon)
    _Icon.m_UIEvent:AddListener(EventTriggerType.PointerDown, BasicIcon.OnBasicPointerDown, _Icon)
    _Icon.m_UIEvent:AddListener(EventTriggerType.PointerUp, BasicIcon.OnBasicPointerUp, _Icon)
    -- 不主動添加OnDrag
    --_Icon.m_UIEvent:AddListener(EventTriggerType.Drag, function(sender) _Icon:OnBasicDrag(sender) end)
    
    _Icon:AddEvent(iOnClick, EventTriggerType.PointerClick)
            :AddEvent(iOnPointerDown, EventTriggerType.PointerDown)
            :AddEvent(iOnPointerUp, EventTriggerType.PointerUp)
            --:AddEvent(iOnDrag, EventTriggerType.Drag)

    ---物品圖
    if _Icon.m_Image_Icon == nil then
        _Icon.m_Image_Icon = _Icon.transform:Find( "Image_Icon" )
        if _Icon.m_Image_Icon then
            _Icon.m_Image_Icon = _Icon.m_Image_Icon:GetComponent( typeof( Image ) )
        end
    end

    ---CD
    if _Icon.m_Image_CD == nil then
        _Icon.m_Image_CD = _Icon.transform:Find( "Image_CD" )
        if _Icon.m_Image_CD then
            _Icon.m_Image_CD = _Icon.m_Image_CD:GetComponent( typeof( Image ) )
            local temp = _Icon.m_Image_CD.transform:Find( "TMP_CD" )
            _Icon.m_TMP_CD = temp:GetComponent( typeof( TMPro.TextMeshProUGUI ) )
            _Icon:SetObjectActive(_Icon.m_Image_CD, false)
            _Icon:SetObjectActive(_Icon.m_TMP_CD, false)
        end
    end

    --- 長按是否開 hint
    _Icon.m_NeedOpenHint = false
    --- 是否具有長按功能
    _Icon.m_NeedLongPress = false
    --- 長按時間
    _Icon.m_LongPressTime = IconSetting.m_LongPressTime
    --- 長按回呼
    _Icon.m_LongPressCallback = nil
    --- 長按中標記
    --_Icon.m_IsLongPressing = false
    --- 點一下是否顯示文字
    --_Icon.m_ClickiTwice = false

    if ProjectMgr.IsDebug() == true and _Icon.m_TMP_Idx == nil then
        _Icon.m_TMP_Idx = _Icon.transform:Find( "TMP_Idx" ):GetComponent( typeof( TMPro.TextMeshProUGUI ) )
        
        if ProjectMgr.IsShowDebug() then
            ---Debug 使用的 索引字串
            _Icon.m_TMP_Idx.gameObject:SetActive( true )
        else
            _Icon.m_TMP_Idx.gameObject:SetActive( false )
        end
    end

    _Icon:SetIdx( iIdx )
    
    return _Icon
end

--- Icon刷新
--- 除非是通用的變動，新規則請加在各自Icon底下
function BasicIcon:RefreshBasicIcon(iIdx, iTextureName, iForceUpdate)
    -- 一樣的 idx 就不需要再刷新編號 & 圖片
    if self.m_Idx ~= iIdx or iForceUpdate == true then
        self:SetIdx(iIdx)
        self:SetTexture(iTextureName)
    elseif self.m_Idx == 0 then
        self:SetTexture()
    end
end

function BasicIcon:SetObjectActive(iObj, iIsActive)
    if iObj.gameObject.activeSelf ~= iIsActive then
        iObj.gameObject:SetActive( iIsActive )
    end
end

--region 各種功能設定

---設定 DebugIdx
---@param iIdx number
function BasicIcon:SetIdx( iIdx )
    if ProjectMgr.IsShowDebug() == true and ProjectMgr.IsDebug() == true then
        ---Debug 使用的 索引字串
        self.m_TMP_Idx.text = iIdx
        self.gameObject.name = self.m_Type .. "_" .. tostring(iIdx)
    end
    
    self.m_Idx = iIdx
    
    return self
end

--- 設定物品名稱
---@param iName string 名稱
function BasicIcon:SetName(iName)
    self.m_Name = iName
    return self
end

---設定 Icon 圖
---@param iTextureName string Icon 圖名
---@param iColor string 修改顏色( HexColor )
---@param iColor Color 修改顏色( Color.gray 會傳 RGB 近來 )
function BasicIcon:SetTexture( iTextureName, iColor )
    if iTextureName and not string.IsNullOrEmpty(iTextureName) then
        self:SetObjectActive(self.m_Image_Icon, true)

        if iColor ~= nil then
            if type(iColor) == "table" then
                self.m_Image_Icon.color = iColor
            elseif type(iColor) == "string" then
                self.m_Image_Icon.color = Extension.GetColor(iColor)
            end
        else
            self.m_Image_Icon.color = Color.White
        end
        SpriteMgr.Load( iTextureName, self.m_Image_Icon)
    else
        self:SetObjectActive(self.m_Image_Icon, false)
        self.m_Image_Icon.color = Color.White
    end
    
    return self
end

--- 設定特殊圖片
---@param iType ESpecialTextureType 種類
function BasicIcon:SetSpecialTexture(iType)
    if iType ~= nil then
        self:SetObjectActive(self.m_Image_Icon, true)
        self.m_Image_Icon.color = Extension.GetColor(iType.m_Color)
        SpriteMgr.Load(iType.m_SpriteName, self.m_Image_Icon)
    end
end

--- 設定長按是否開 hint
---@param iNeedOpenHint boolean 長按是否開 hint
function BasicIcon:SetNeedOpenHint(iIsNeed)
    self.m_NeedOpenHint = iIsNeed
    return self
end

--- 設定 icon 可否長按及長按時間、事件 ( 設定否，則也會同時設定不會開 hint )
---@param iNeedLongPress boolean 可否長按
---@param iLongPressTime number 長按秒數 ( 否 則不須給 )
---@param iCallback function 長按結束回傳 ( 否 則不須給 )
function BasicIcon:SetLongPress( iNeedLongPress, iLongPressTime, iCallback )
    self.m_NeedLongPress = iNeedLongPress
    if not self.m_NeedLongPress then
        -- 如果不可以長按，那也不要開 hint 了
        self:SetNeedOpenHint(false)
            :SetLongPressTime(nil)
            :SetLongPressCallback(nil)
    else
        self:SetLongPressTime(iLongPressTime)
            :SetLongPressCallback(iCallback)
    end
    
    return self
end

function BasicIcon:SetNeedLongPress(iIsNeed)
    self.m_NeedLongPress = iIsNeed
    return self
end

function BasicIcon:SetLongPressTime(iTime)
    self.m_LongPressTime = iTime
    return self
end

---設定長按觸發委派
---@param iCallback function 傳 nil 進來會把所有委派清掉
function BasicIcon:SetLongPressCallback(iCallback)
    if iCallback then
        if not self.m_LongPressCallback then
            self.m_LongPressCallback = {}
        end

         -- 檢查有沒有重複註冊
        for k, v in pairs(self.m_LongPressCallback) do
            if v == iCallback then
                return self
            end
        end
        
        table.insert(self.m_LongPressCallback, iCallback)
    else
        self.m_LongPressCallback = nil
    end
    
    return self
end

--- 設定兩階段點選(會一起設定Select是否啟用)
---@param iNeedTwice boolean 是否兩階段點選
---@param iTextId number 要顯示的字串內容 ( 否 則不須給；是 且 沒有給 iIextIdx，則會套用預設 )
---@param iIsPriority boolean 不管任何規則，就要顯示現在設定的字
--- 設定不兩階段的話，按一下就會觸發 OnClick
function BasicIcon:SetClickTwice(iNeedTwice, iTextId, iIsPriority)
    self.m_NeedClickiTwice = iNeedTwice
    self:SetSelect(iNeedTwice)
    
    if iTextId then
        self.m_OnSelectTextId = iTextId
        self.m_SelectTextIdIsPriotity = iIsPriority
    else
        self:SetOnSelectText()
        self.m_SelectTextIdIsPriotity = false
    end
    
    return self
end

--- 添加選中時委派
--- 注意:委派後即便 Idx == 0 也會觸發OnSelect，需要寫好防呆
---@param ifunc function 委派函式 params : iIconSelf, iIsSelect
---@param iIsMult boolean 是否在多選模式中觸發
function BasicIcon:AddOnSelectAction(ifunc, iIsMult)
    if not ifunc then
        return self
    end
    
    if not self.m_OnSelectAction then
        self.m_OnSelectAction = {}
    end

    for k, v in pairs(self.m_OnSelectAction) do
        -- 重複委派檢查
        if v.m_Action == ifunc and v.m_IsMulti == iIsMult then
            return self
        end
    end

    table.insert(self.m_OnSelectAction, { m_Action = ifunc, m_IsMulti = iIsMult })
    return self
end

--- 刪除選中時委派
---@param ifunc function 要刪除的委派
---@param iIsMulti boolean 是否在多選模式中觸發
function BasicIcon:RemoveOnSelectAction(ifunc, iIsMulti)
    if not ifunc or iIsMulti == nil then
        return self
    end

    if not self.m_OnSelectAction then
        return self
    end

    for k, v in pairs(self.m_OnSelectAction) do
        -- 重複委派檢查
        if v.m_Action == ifunc and v.m_IsMulti == iIsMulti then
            self.m_OnSelectAction[k] = nil
        end
    end

    return self
end

--- 設定兩階段點選顯示文字
function BasicIcon:SetOnSelectText()
    --- 選擇中顯示的文字
    self.m_OnSelectTextId = IconSetting.m_SelectTextId[EItemType.None]

    return self
end

--- 啟用Select功能
function BasicIcon:SetSelect(iOn)
    self.m_NeedSelectAction = iOn
    return self
end

---設定CD遮罩開關
---@param iActive boolean CD 開關
function BasicIcon:SetCD( iActive )
    self:SetObjectActive(self.m_Image_CD, iActive)
    self:SetObjectActive(self.m_TMP_CD, iActive)
    return self
end

--- 封裝取得事件觸發
local function GetEventTable(iSelf, iEventType)
    local _NewTypeCheck = false -- 檢查是不是新的EventTrigger類型
    local _funcTable = nil -- 委派table
    if iEventType == EventTriggerType.PointerClick then
        if not iSelf.m_OnClick then
            iSelf.m_OnClick = {}
            --_NewTypeCheck = true -- 預設有給，不需要另外塞
        end
        _funcTable = iSelf.m_OnClick
    elseif iEventType == EventTriggerType.PointerDown then
        if not iSelf.m_OnPointerDown then
            iSelf.m_OnPointerDown = {}
            --_NewTypeCheck = true -- 預設有給，不需要另外塞
        end
        _funcTable = iSelf.m_OnPointerDown
    elseif iEventType == EventTriggerType.PointerUp then
        if not iSelf.m_OnPointerUp then
            iSelf.m_OnPointerUp = {}
            --_NewTypeCheck = true -- 預設有給，不需要另外塞
        end
        _funcTable = iSelf.m_OnPointerUp
    elseif iEventType == EventTriggerType.Drag then
        if not iSelf.m_OnDrag then
            iSelf.m_OnDrag = {}
            _NewTypeCheck = true
        end
        _funcTable = iSelf.m_OnDrag
    elseif iEventType == EventTriggerType.EndDrag then
        if not iSelf.m_OnDragEnd then
            iSelf.m_OnDragEnd = {}
            iSelf.m_hasDragEnd = true
            _NewTypeCheck = true
        end
        _funcTable = iSelf.m_OnDragEnd
    elseif iEventType == EventTriggerType.PointerEnter then
        if not iSelf.m_OnPointerEnter then
            iSelf.m_OnPointerEnter = {}
            _NewTypeCheck = true
        end
        _funcTable = iSelf.m_OnPointerEnter
    elseif iEventType == EventTriggerType.PointerExit then
        if not iSelf.m_OnPointerExit then
            iSelf.m_OnPointerExit = {}
            _NewTypeCheck = true
        end
        _funcTable = iSelf.m_OnPointerExit
    else
        D.LogError("[BasicIcon] 沒有符合的委派.")
    end

    return _funcTable, _NewTypeCheck
end

--- 添加事件
---@param ifunc function 委派
---@param iEventType EventTriggerType EventTriggerType
function BasicIcon:AddEvent(ifunc, iEventType)
    if not ifunc or not iEventType then
        return self
    end

    local _EventTable, _isNewType = GetEventTable(self, iEventType)
    if _isNewType then
        -- 沒添加過的類型，要加上EventTrigger
        self.m_UIEvent:AddListener(iEventType, ifunc, self)
        return self
    end

    for k, v in pairs(_EventTable) do
        -- 重複委派檢查
        if v == ifunc then
            return self
        end
    end

    table.insert(_EventTable, ifunc)
    return self
end

--- 刪除事件
---@param ifunc function 要刪除的委派
---@param iEventType EventTriggerType EventTriggerType
function BasicIcon:RemoveEvent(ifunc, iEventType)
    if not ifunc or not iEventType then
        return self
    end

    local _EventTable = GetEventTable(self, iEventType)
    if not _EventTable then
        return self
    end

    for k, v in pairs(_EventTable) do
        -- 重複委派檢查
        if v == ifunc then
            _EventTable[k] = nil
        end
    end

    return self
end

--region Icon 外框
function BasicIcon:SetFrameType(iRank)
    local _IsNewFrame = false
    ---複製物件
    if not self.m_FrameImage and not self.m_InitFrameIcon then
        self.m_InitFrameIcon = true

        self.m_FrameIcon = GameObject.Instantiate( self:GetFrameObj(iRank) )
        if not self.m_FrameIcon then
            return self
        end

        self.m_FrameIcon.name = "FrameIcon"
        self.m_FrameIcon:SetActive( true )
        self.m_FrameIcon.transform:SetParent( self.transform )
        self.m_FrameIcon.transform:SetAsFirstSibling()
        self.m_FrameIcon.transform.localPosition = Vector3.zero
        self.m_FrameIcon.transform.localScale = Vector3.one
        self.m_FrameIcon.transform.localRotation = Quaternion.identity

        local _RectTrans = self.m_FrameIcon:GetComponent("RectTransform")
        _RectTrans.anchorMin = Vector2.zero
        _RectTrans.anchorMax = Vector2.one
        _RectTrans.anchoredPosition = Vector2.zero
        _RectTrans.sizeDelta = Vector2.zero

        _IsNewFrame = true
    end

    self:SetFrameImage(iRank)

    if self.m_FrameImage then
        local _GetImageIdx = self.m_FrameImage[IconSetting.m_IconFrameReadValueStart].transform:GetSiblingIndex()
        self.m_Image_Icon.transform:SetParent( self.m_FrameIcon.transform )
        self.m_Image_Icon.transform:SetSiblingIndex(_GetImageIdx + 1)
    end
    
    return self, _IsNewFrame
end

function BasicIcon:SetScrollView(iScrollView)
    self.m_ScrollView = iScrollView
    return self
end

--- 獲取外框物件
function BasicIcon:GetFrameObj()
    local _FrameObjTable = IconMgr.m_FrameIcon.m_ItemFrameIcon[ERank.None]
    if not _FrameObjTable then
        return nil
    end
    
    return _FrameObjTable.m_GameObject
end

--- 設定外框圖
function BasicIcon:SetFrameImage()
    if not self.m_FrameImage then
        self.m_FrameImage = {}
        for key1 = IconSetting.m_IconFrameReadValueStart, IconSetting.m_IconFrameReadValueEnd do
            self.m_FrameImage[key1] = self.m_FrameIcon.transform:Find( "Image_Square_" .. key1 ):GetComponent( typeof( Image ) )
        end
    end
end
--endregion Icon 外框

--endregion

--- 更新 CD 遮罩表現
function BasicIcon:UpdateCD()
    if self.m_Idx == 0 then
        do return end
    end
    
    -- CD
    local _Time, _Percent = CDMgr.GetInfo( self.m_Type, self.m_Idx )
    
    -- CD 遮罩顏色
    local _Color = Color.Black
    _Color.a = IconSetting.m_CDMaskAlpha

    self.m_TMP_CD.text = string.format( "%.1f", _Time )
    self:SetObjectActive(self.m_TMP_CD, _Time > 0)
    
    self.m_Image_CD.fillAmount = _Percent
    self:SetObjectActive(self.m_Image_CD, _Percent > 0)
    self.m_Image_CD.color = _Color
end

--- 執行選中委派
---@param iIsSelect boolean 選中或取消選擇
function BasicIcon:DoSelectAction(iIsSelect)
    if not self.m_NeedSelectAction or not self.m_OnSelectAction then
        return
    end
    
    -- 是否在多選模式中
    local _isMulti = IconMgr.m_SelectType == IconMgr.ESelectType.Mult and table.Contains(IconMgr.m_MultSelectGroup, self.m_MultSelectGroup)

    for k, v in pairs(self.m_OnSelectAction) do
        -- 委派執行需求模式與當前模式相同才執行
        if v.m_IsMulti == _isMulti then
            v.m_Action(self, iIsSelect)
        else
            self:CancelSelect(false)
        end
    end
end

--region EventTrigger 觸發
--- 執行事件委派
local function DoEventAction(iSelf, iEventData, iEventType)
    local _EventTable = GetEventTable(iSelf, iEventType)
    
    if not _EventTable then
        return
    end

    for k, v in pairs(_EventTable) do
        v(iSelf, iEventData)
    end
end

--- 按一下
function BasicIcon:OnBasicClick(iEventData)
    if not self.m_IsLongPress then
        self.m_IsLongPress = false

        if self.m_NeedClickiTwice then
            -- 是否在多選模式中
            local _isMulti = IconMgr.m_SelectType == IconMgr.ESelectType.Mult and table.Contains(IconMgr.m_MultSelectGroup, self.m_MultSelectGroup)

            -- 點第一下
            if not self.m_ClickOnce then
                if self.m_Idx == 0 then
                    -- 觸發選擇委派
                    self:DoSelectAction(true)
                    return
                end
                
                self.m_ClickOnce = true

                -- 依據不同選擇狀態給予不同反應
                IconMgr.SelectIcon(self, _isMulti)
                if _isMulti then
                    DoEventAction(self, iEventData, EventTriggerType.PointerClick)
                end
            -- 點第二下
            else
                self.m_ClickOnce = nil
                if not _isMulti then
                    DoEventAction(self, iEventData, EventTriggerType.PointerClick)
                    self:CancelSelect()
                elseif _isMulti then
                    self:CancelSelect()
                end
            end
        else
            DoEventAction(self, iEventData, EventTriggerType.PointerClick)
        end
    else
        self.m_IsLongPress = false
    end
end

--- 按下
function BasicIcon:OnBasicPointerDown(iEventData)
    if self.m_Idx == 0 then
        return
    end

    if self.m_NeedLongPress then
        self.m_LongPressFunc = function (iEventData)
            self.m_WaitLongPressDelay = nil
            self.m_LongPressFunc = nil
            self.m_IsLongPress = true
            if not UIMgr.IsVisible(UIOrderTop_Controller) then
                return
            end
            UIOrderTop_Controller:ShowSlider(true, self, function()
                    self:OnBasicPointerUp(iEventData)

                    if self.m_LongPressCallback then
                        for _, f in pairs(self.m_LongPressCallback) do
                            f(self)
                        end
                    end
                end)
        end

        self.m_WaitLongPressDelay = true
        HEMTimeMgr.DoFunctionDelay(IconSetting.m_WaitLongPressTime, self.m_LongPressFunc, iEventData)
    end

    DoEventAction(self, iEventData, EventTriggerType.PointerDown)
end

--- 放開
function BasicIcon:OnBasicPointerUp(iEventData)
    if self.m_Idx == 0 then
        return
    end

    if self.m_NeedLongPress then
        if self.m_WaitLongPressDelay then
            HEMTimeMgr.CancelDoFunctionDelay(self.m_LongPressFunc)
        end

        if self.m_IsLongPress then
            if not UIMgr.IsVisible(UIOrderTop_Controller) then
                return
            end
            UIOrderTop_Controller:ShowSlider(false)

            DoEventAction(self, iEventData, EventTriggerType.PointerUp)
        end
    else
        DoEventAction(self, iEventData, EventTriggerType.PointerUp)
    end

    if self.m_isDragging then
        DoEventAction(self, iEventData, EventTriggerType.EndDrag)
        self.m_isDragging = false
    end
end

--- 拖曳
function BasicIcon:OnBasicDrag(iEventData)
    
    ---快捷鍵的部分 沒有id時也可以左右拖曳 換頁 快捷鍵的icon 生成時給予m_AllowEmptyDrag 讓icon 判斷拖曳
    if self.m_Idx == 0 and self.m_AllowEmptyDrag == nil then
        return
    end

    self.m_isDragging = true
    
    -- 沒有拖曳結束事件就避開拖曳方向判斷
    if not self.m_hasDragEnd then
        DoEventAction(self, iEventData, EventTriggerType.Drag)
        return
    end
    
    --region 拖曳方向判斷
    local _startPos = iEventData.pressPosition
    local _endPos = iEventData.position

    -- 取向量
    local _direction = _endPos - _startPos
    -- 移動到某個程度才算是拖曳
    if _direction.x * _direction.x + _direction.y * _direction.y < 100 then
        return
    end

    local _x_direct = _direction.x
    local _y_direct = _direction.y

    --- 用數值較大的決定採用的拖曳方向, false = X, true = Y
    local _useXorY = false
    if math.abs(_x_direct) <= math.abs(_y_direct) then
        _useXorY = true
    end

    if _useXorY then
        -- 上滑或下滑行為
        if _y_direct > 0 then
            self.m_DragType = EDragType.Up
        else
            self.m_DragType = EDragType.Down
        end
    else
        -- 左右滑行為
        if _x_direct > 0 then
            self.m_DragType = EDragType.Right
        else
            self.m_DragType = EDragType.Left
        end
    end
    --endregion
    
    DoEventAction(self, iEventData, EventTriggerType.Drag)
end
--endregion EventTrigger 觸發

--region 其他控制函式
--endregion 其他控制函式

--region 開關 Hint
function BasicIcon:OpenBasicHint()
    if not self.m_NeedOpenHint or self.m_Idx == 0 then
        return
    end
    
    self:OpenHint()
end

function BasicIcon:CloseBasicHint()
    self:CloseHint()
end
--endregion 開關 Hint


---BasicIcon 登記的觀察者 數據有變化是要做甚麼
---@param iEStateObserver EStateObserver 有變化的項目是甚麼 EStateObserver
function BasicIcon:OnStateChanged(iEStateObserver)
    ---道具類 我得最愛愛心顯示需求
    if iEStateObserver == EStateObserver.UpdateMyFavoriteItem and  self.m_Type ==  EIconType.Item then
        self:SetSubImage()
    ---道具類 物品狀態(使用限制)顯示需求
    elseif iEStateObserver == EStateObserver.UpdateItemState and  self.m_Type ==  EIconType.Item then
        self:ResetItemState()
    end
end
