﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
-----====================================================================

--============================ UI的基礎介面 =================================
require("Common/UIUnit/UIControllerBase")
--============================ Main底下的獨立 UI ============================
require("UI/MiniMap/MiniMap_Controller")
require("UI/HotKey/HotKey_Controller")
require("UI/Tracing/Tracing_Controller")
require("UI/AVG/AVG_Controller")
require("UI/Menu/Menu_Controller")
require("UI/NumberPanel/NumberPanel_Controller")
require("UI/ScrollingBoard/ScrollingBoard_Controller")
require("UI/CenterMsg/CenterMsg_Controller")
require("UI/ItemGetMsg/ItemGetMsg_Controller")
require("UI/Chat/ChatController")
--====================== 請在上方依序加入 Main 底下的 UI ======================
require("UI/Main/Main_Controller")
--================================ 其他 UI ===================================
require("UI/Loading/Loading_Controller")
require("UI/Login/Login_Controller")
require("UI/EventTalk/EventTalk_Controller")
require("UI/UIOrderTop/UIOrderTop_Controller")
require("UI/Bag/Bag_Controller")
require("UI/Hint/HintMgr_Controller")
require("UI/Hint/BuffHint_Controller")
require("UI/Hint/ItemHint_Controller")
require("UI/Hint/SkillHint_Controller")
require("UI/Hint/PetHint_Controller")
require("UI/Hint/CommonHint_Controller")
require("UI/CommonReward/CommonRewardBase_Controller")
require("UI/CommonReward/CommonReward_TimeShipUpgrade_Controller")
require("UI/CommonReward/CommonReward_LevelUpgrade_Controller")
require("UI/CommonReward/CommonReward_MultiItem_Controller")
require("UI/CommonReward/CommonReward_ItemAndText_Controller")
require("UI/CommonReward/CommonReward_MissionComplete_Controller")
require("UI/CommonReward/CommonReward_WinOrLose_Controller")
require("UI/CommonReward/CommonReward_PetUpgradeUI_Controller")
require("UI/CommonReward/CommonReward_PetFuse_Controller")
require("UI/Map/Map_Controller")
require("UI/Debug/Debug_Controller")
require("UI/GoldFinger/GoldFinger_Controller")
require("UI/BattleEditor/BattleEditor_Controller")
require("UI/Setting/Setting_Controller")
require("UI/MissionBook/MissionBook_Controller")
require("UI/Teach/Teach_Controller")
require("UI/RoleCreate/RoleCreate_Controller")
require("UI/TimeLine/TimeLine_Controller")
require("UI/CameraSwitch/CameraSwitch_Controller")
require("UI/CaptionTheater/CaptionTheater_Controller")
require("UI/PopItemChange/PopItemChange_Controller")
require("UI/Bulletin/Bulletin_Controller")
require("UI/UIBlackBase/UIBlackBase_Controller")
require("UI/AutoDrink/AutoDrink_Controller")
require("UI/Equipment/Equipment_Controller")
require("UI/RoleAttribute/RoleAttribute_Controller")
require("UI/TimeMachine/TimeMachine_Controller")
require("UI/TimeMachine/RoomRepair_Controller")
require("UI/SelectList/SelectList_Controller")
require("UI/CGPlayer/CGPlayer_Controller")
require("UI/SkillBook/SkillBook_Controller")
require("UI/BattleCalculator/BattleCalculator_Controller")
require("UI/ConfirmList/ConfirmList_Controller")
require("UI/Death/Death_Controller")
require("UI/RoleBuff/RoleBuffInfo_Controller")
require("UI/Interact/Interact_Controller")
require("UI/OtherPlayerEquipment/OtherPlayerEquipment_Controller")
require("UI/WugongDemo/WugongDemo_Controller")
require("UI/EnergyCore/EnergyCore_Controller")
require("UI/Playground/DinosaurGame/DinosaurGame_Controller")
require("UI/Playground/WhacAMole/WhacAMole_Controller")
require("UI/Playground/Playground_Controller")
require("UI/CommonQuery/CommonQuery_Type1_Controller")
require("UI/CommonQuery/CommonQuery_Type2_Controller")
require("UI/CommonQuery/CommonQuery_Type3_Controller")
require("UI/CommonQuery/CommonQuery_Type4_Controller")
require("UI/CommonQuery/CommonQuery_Type5_Controller")
require("UI/CommonQuery/CommonQuery_Type6_Controller")
require("UI/CommonQuery/CommonQuery_Type7_Controller")
require("UI/CommonQuery/CommonQuery_BlackBackGround_Controller")
require("UI/Playground/CircuitGame/CircuitGame_Controller")
require("UI/Activity/Activity_Controller")
require("UI/DungeonPass/DungeonPass_Controller")
require("UI/Recruit/Recruit_Controller")
require("UI/Recruit/RecruitHalf_Controller")
require("UI/ExploreDairy/ExploreDairy_Controller")
require("UI/Community/Community_Controller")
require("UI/Printer/Print3D_Controller")
require("UI/Schedule/Schedule_Controller")
require("UI/FastClickHint/FastClickHint_Controller")
require("UI/Playground/SpaceShip_Game/SpaceShipGame_Controller")
require("UI/QuickChatSet/QuickChatSet_Controller")
require("UI/Pet/PetPage_Controller")
require("UI/Mall/Mall_Controller")
require("UI/MysticShop/MysticShop_Controller")
require("UI/Playground/StreetFighter/StreetFighter_Controller")
require("UI/ReengineeringGrowth/ReengineeringGrowth_Controller")
require("UI/ExchangeShop/ExchangeShop_Controller")
require("UI/Mail/Mail_Controller")
require("UI/QuantumEchoEntry/QuantumEchoEntry_Controller")
require("UI/InnerBook/InnerBook_Controller")
--================================  UI 所需要對應的model ===================================
require("Common/PlayerData/PlayerData")

--============================ UI require 底部 ============================

---UI 設定
---@class UISetting
---author hui
---telephone #2950
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.3
UISetting = {}
local this = UISetting

--所有 UI 的名稱
local _AllUINames = {}

---沒有 Index 的 UI
local _UIDonotHaveIndex ={}

---檢查UI有沒有 Index
function UISetting.IsUIHaveIndex(iUI)
    return not table.Contains(_UIDonotHaveIndex, iUI)
end

---以 Controller 取得介面設定資料
---@param iUI Controller 各 UI 的 Controller
function UISetting.GetUISettingData(iUI)
    for key, value in pairs(this.m_UISettingData) do
        if value.m_UI == iUI then
            return value
        end
    end
    return nil
end

---以 Controller 取得介面設定資料
---@param iUI Controller 各 UI 的 Controller
function UISetting.GetUISettingDataKey(iUI)
    for key, value in pairs(this.m_UISettingData) do
        if value.m_UI == iUI then
            return key
        end
    end
    if not table.Contains(_UIDonotHaveIndex, iUI) then
        local _String = "介面名稱： [ "..iUI.m_Name.." ] 沒有 UIIndex "
        D.Log(GString.GetTextWithColor(_String , Color.Purple))
    end
    return nil
end

---依照 UIIndex 取得所需之 UIController
function UISetting.GetUIControllerByUIIndex(iUIIndex)

    --參數錯誤
    if iUIIndex < 0 then
        D.LogError("UIIndex 輸入錯誤，參數必須大於零，收到參數："..iUIIndex)
        return
    end

    --Table 內沒結構
    if this.m_UISettingData[iUIIndex] == nil then
        local _String = "UISetting.lua 內沒有此介面資料， 請新增 UISetting.m_UISettingData["..iUIIndex.."]，介面資料"
        D.Log(GString.GetTextWithColor(_String , Color.Red))
        return
    end

    --Table 有結構 但是 Controller 沒設定 所有 UI 完成後 再開
    --[[if this.m_UISettingData[iUIIndex].m_UI == nil then
        local _String = "找不到相對應介面的 UIController ，請在 UISetting.m_UISettingData["..iUIIndex.."]，內新增此介面的 Controller "
        D.Log(GString.GetTextWithColor(_String , Color.Red))
        return
    end]]

    return this.m_UISettingData[iUIIndex].m_UI
end

---依照 iUIName 取得所需之 UIController
function UISetting.GetUIControllerByUIName(iUIName)
    if string.IsNullOrEmpty(iUIName) then
        D.LogError("iUIName 字串為空值")
        return
    end
    for key, value in pairs(this.m_UISettingData) do
        if value.m_UI ~= nil and value.m_UI.m_UIView == iUIName then
            return value.m_UI
        end
    end
    D.LogError("找不到 UIController，請確認 UIName，收到字串："..iUIName)
    return nil
end

---永標 控制 UI 顯示範圍檢查
function UISetting.IsStaticFalgIDInRange(iStaticFlagID)
    if (iStaticFlagID >= UIVISIBLE_STATIC_FLAGID_MIN and iStaticFlagID <=UIVISIBLE_STATIC_FLAGID_MAX)
    or table.Contains(UIVisible_StaticFlag_Exception, iStaticFlagID) then
        return true
    else
        return false
    end
end

---檢查是否 有需要更動顯示
---@param iStaticFlagID ushort 受到變動的永標
function UISetting.CheckWhenPlayerGetStaticFlag(iStaticFlagID)
    if this.IsStaticFalgIDInRange(iStaticFlagID) then
        --元件是否已經開啟
        for _UIDatakey, _UIData in pairs(this.m_UISettingData) do
            --UI顯示判斷 在主介面上的要直接開
            if _UIData.m_StaticFlagID == iStaticFlagID then
                --檢查 永標 及玩家 等級
                if PlayerData.IsHaveStaticFlag(iStaticFlagID) then
                    if table.Contains(Main_Model.EUIINMain, _UIData.m_UI) then
                        UIMgr.Open(_UIData.m_UI)
                    end
                --沒永標
                else
                    if _UIData.m_StaticFlagID > 0 or _UIData.m_OpenLv > 0 then
                        if table.Contains(Main_Model.EUIINMain, _UIData.m_UI) then
                            UIMgr.Close(_UIData.m_UI)
                        end
                    end
                end
            end

            --元件顯示判斷
            if _UIData.m_ComponentsVisibleData ~= nil and table.Count(_UIData.m_ComponentsVisibleData) > 0 then
                for key, value in pairs(_UIData.m_ComponentsVisibleData) do
                    if value.m_StaticFlagID == iStaticFlagID then
                        if _UIData.m_UI ~= nil then
                            UIMgr.SetUIComponentVisible(_UIData.m_UI, key, value)
                        end
                    end
                end
            end
        end
    end
end

function UISetting.GetAllUINames()
    if table.Count(_AllUINames) == 0 then
        for key, value in pairs(this.m_UISettingData) do
            if value.m_UI ~= nil then
                table.insert(_AllUINames, value.m_UI.m_UIView)
            end
        end
    end

    return _AllUINames
end

--==================================== 以下為範例 ====================================

--================ 主介面 ================
---主介面 介面資料
--this.m_UISettingData[31] = {
    --=========介面=========
    ---主介面 本人
    --m_UI = Main_Controller,
    ---主介面 顯示永標 = 0 直接顯示
    --m_StaticFlagID = 0,
    ---主介面 顯示開放等級 = 0 直接顯示
    --m_OpenLv = 0,
    --=========元件=========
    ---主介面 需要各自控制顯示元件的資料 需要有控制的 再加
    --m_ComponentsVisibleData = {
        ---主介面 聊天按鈕 控制顯示資料(Key 的字串，要是 Prefab 上面的正確名字唷，一定要帶有"&")
        --["&AideChat_Btn"] = {
        --    聊天按鈕 元件控顯示永標
        --    m_StaticFlagID = 2809,
        --    聊天按鈕 檢查永標時的表現形態 [0 = 不顯示，1 = 反灰，2 = 自訂 function(iGObjComponent, iStaticFlagID, iIsActive) ]
        --    m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        --    聊天按鈕 檢查永標時的表現形態 如果為 2 時 需要設定的 function
        --    m_FunctionComponentVisible = function(iGObjComponent, iStaticFlagID, iIsActive) end
        --},
        ---主介面 PK 按鈕控制顯示資料
        --["&PK_Btn"] = {
        --    m_StaticFlagID = 2811,
        --    m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        --},
        ---主介面 晶片按鈕控制顯示資料
        --["&Chip_Btn"] = {
        --    m_StaticFlagID = 2812,
        --    m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        --},
        ---主介面 掛機按鈕控制顯示資料
        --["&Afk_Btn"] = {
        --    m_StaticFlagID = 2813,
        --    m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        --}
    --},
--}

--==================================== 以下為資料 ====================================

--- UI 與 編號對應的資料 Key = UIIndex
this.m_UISettingData = {}

--================ UI 黑色底板 ================
---共用詢問視窗 資料
this.m_UISettingData[0] = {
    --=========介面=========
    ---UI 黑色底板 本人
    m_UI = UIBlackBase_Controller,
    ---共用詢問視窗 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---共用詢問視窗 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 共用詢問視窗 ================
---共用詢問視窗 資料
this.m_UISettingData[1] = {
    --=========介面=========
    ---共用詢問視窗 本人
    m_UI = nil,
    ---共用詢問視窗 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---共用詢問視窗 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ Loading 介面 ================
---Loading 資料
this.m_UISettingData[3] = {
    --=========介面=========
    ---Loading 本人
    m_UI = Loading_Controller,
    ---Loading 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---Loading 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 中央訊息 ================
---中央訊息 資料
this.m_UISettingData[4] = {
    --=========介面=========
    ---中央訊息 本人
    m_UI = CenterMsg_Controller,
    ---中央訊息 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---中央訊息 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ AVG ================
---AVG 資料
this.m_UISettingData[6] = {
    --=========介面=========
    ---AVG 本人
    m_UI = AVG_Controller,
    ---AVG 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---AVG 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 系統設定介面 ================
---系統設定 介面資料
this.m_UISettingData[7] = {
    --=========介面=========
    ---系統設定 本人
    m_UI = Setting_Controller,
    ---系統設定 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---系統設定 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 信箱介面 ================
---信箱 介面資料
this.m_UISettingData[8] = {
    --=========介面=========
    ---信箱 本人
    m_UI = Mail_Controller,
    ---信箱 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---信箱 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = Mail_Model,
}

--================ UI 最頂層 ================
---UI 最頂層 介面資料
this.m_UISettingData[9] = {
    --=========介面=========
    ---UI 最頂層 本人
    m_UI = UIOrderTop_Controller,
    ---UI 最頂層 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---UI 最頂層 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 道具獲得訊息 ================
---道具獲得訊息 資料
this.m_UISettingData[10] = {
    --=========介面=========
    ---道具獲得訊息 本人
    m_UI = ItemGetMsg_Controller,
    ---道具獲得訊息 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---道具獲得訊息 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 數字鍵盤 介面 ================
---數字鍵盤 介面資料
this.m_UISettingData[15] = {
    --=========介面=========
    ---數字鍵盤 本人
    m_UI = NumberPanel_Controller,
    ---數字鍵盤 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---數字鍵盤 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 選擇清單 ================
---選擇清單 資料
this.m_UISettingData[16] = {
    --=========介面=========
    ---選擇清單 本人
    m_UI = SelectList_Controller,
    ---選擇清單 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---選擇清單 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 物品確認清單 介面 ================
---物品確認清單 介面資料
this.m_UISettingData[18] = {
    --=========介面=========
    ---物品確認清單 本人
    m_UI = ConfirmList_Controller,
    ---物品確認清單 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---物品確認清單 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 跑馬燈 介面 ================
---跑馬燈 介面資料
this.m_UISettingData[20] = {
    --=========介面=========
    ---跑馬燈 本人
    m_UI = ScrollingBoard_Controller,
    ---跑馬燈 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---跑馬燈 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0
}

--================ 登入介面 ================
---登入 介面資料
this.m_UISettingData[21] = {
    --=========介面=========
    ---登入 本人
    m_UI = Login_Controller,
    ---登入 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---登入 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0
}

--================ 創角 介面 ================
---創角 介面資料
this.m_UISettingData[23] = {
    --=========介面=========
    ---創角 本人
    m_UI = RoleCreate_Controller,
    ---創角 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---創角 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0
}

--================ 切換分流介面 ================
---切換分流 介面資料
this.m_UISettingData[24] = {
    --=========介面=========
    ---切換分流 本人
    m_UI = nil,
    ---切換分流 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---切換分流 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 公告介面 ================
---公告 介面資料
this.m_UISettingData[25] = {
    --=========介面=========
    ---公告 本人
    m_UI = Bulletin_Controller,
    ---公告 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---公告 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0
}

--================ 快速提示介面 ================
---快速提示 介面資料
this.m_UISettingData[26] = {
    --=========介面=========
    ---鏢局 本人
    m_UI = FastClickHint_Controller,
    ---鏢局 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---鏢局 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ CommonHint ================
---CommonHint 介面資料
this.m_UISettingData[27] = {
    --=========介面=========
    ---CommonHint 本人
    m_UI = CommonHint_Controller,
    ---CommonHint 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---CommonHint 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ CGPlayer 介面 ================
---CGPlayer 介面資料
this.m_UISettingData[28] = {
    --=========介面=========
    ---CGPlayer 本人
    m_UI = CGPlayer_Controller,
    ---CGPlayer 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---CGPlayer 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0
}

--================ 主介面 ================
---主介面 介面資料
this.m_UISettingData[31] = {
    --=========介面=========
    ---主介面 本人
    m_UI = Main_Controller,
    ---主介面 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---主介面 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    --=========元件=========
    ---主介面 需要各自控制顯示元件的資料 需要有控制的 再加
    m_ComponentsVisibleData = {
        ---主介面 聊天按鈕控制顯示資料
        ["&Btn_AideChat"] = {
            m_StaticFlagID = 2809,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 PK 按鈕控制顯示資料
        ["&Button_PK"] = {
            m_StaticFlagID = 2811,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        -- ---主介面 晶片按鈕控制顯示資料
        -- ["&Btn_Chip"] = {
        --     m_StaticFlagID = 2812,
        --     m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        -- },
        ---主介面 武裝升級主介面小Icon
        ["&Btn_Afk"] = {
            m_StaticFlagID = 9999,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Function,
            m_FunctionComponentVisible = function(iGObjComponent, iStaticFlagID, iIsActive)
            	local _IsRepaired = TimeMachineMgr.GetRoomState(TimeMachineMgr.ERoom.EnergyCore) > ETimeMachineState.Broken
                iGObjComponent.gameObject:SetActive(_IsRepaired)
            end
        },
        ---主介面 活動
        ["&Btn_Event"] = {
            m_StaticFlagID = 2802,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 促銷
        ["&Btn_Promotion"] = {
            m_StaticFlagID = 2807,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 修裝
		---功能做好後再把9999標記移除
        ["&Btn_Repair"] = {
            m_StaticFlagID = 9999,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 教學(指南)
        ["&Btn_Teach"] = {
            m_StaticFlagID = 2806,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---新的UI規劃不需要在主頁面顯示自動喝水提示 暫時註解保留
        --[[
        ---主介面 自動喝水按鈕
        ["&Group_AutoDrink"] = {
            m_StaticFlagID = 2819,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ]]
        ---主介面 左下快速聊天按鈕 資料跟聊天一樣
        ["&Btn_QuickChat"] = {
            m_StaticFlagID = 2809,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    },
}

--================ 戰鬥介面 ================
---戰鬥介面 資料
this.m_UISettingData[32] = {
    --=========介面=========
    ---戰鬥介面 本人
    m_UI = HotKey_Controller,
    ---戰鬥介面 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---戰鬥介面 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    --=========元件=========
    ---戰鬥介面 需要各自控制顯示元件的資料 需要有控制的 再加
    m_ComponentsVisibleData = {

    ---戰鬥介面 中間下面快捷列區域 控制顯示資料 功能暫時關閉 所以全部註解掉
    --[[
        ["&SecArea"] = {
            m_StaticFlagID = 2810,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ]]
    ---戰鬥介面 普通攻擊按鈕 控制顯示資料
        ["&Anchor_NormalAtk"] = {
            m_StaticFlagID = 2815,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊一按鈕 控制顯示資料
        ["&Anchor_One"] = {
            m_StaticFlagID = 2815,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊二按鈕 控制顯示資料
        ["&Anchor_Two"] = {
            m_StaticFlagID = 2815,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 目標按鈕 控制顯示資料
        ["&Btn_Target"] = {
            m_StaticFlagID = 2815,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 翻滾按鈕 控制顯示資料
        ["&Anchor_Move"] = {
            m_StaticFlagID = 2815,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊五按鈕 控制顯示資料
        ["&Anchor_Five"] = {
            m_StaticFlagID = 2816,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊三按鈕 控制顯示資料
        ["&Anchor_Three"] = {
            m_StaticFlagID = 2817,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊四按鈕 控制顯示資料
        ["&Anchor_Four"] = {
            m_StaticFlagID = 2817,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---內功的三個按鍵 50001 永標為暫代假資料 等企劃後續確認
    ---戰鬥介面 攻擊六按鈕(第一個內功) 控制顯示資料
        ["&Anchor_Six"] = {
            m_StaticFlagID = 50001,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊七按鈕(第二個內功) 控制顯示資料
        ["&Anchor_Seven"] = {
            m_StaticFlagID = 50001,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    ---戰鬥介面 攻擊八按鈕(第三個內功) 控制顯示資料
        ["&Anchor_Eight"] = {
            m_StaticFlagID = 50001,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 掛機按鈕控制顯示資料
        ["&Btn_AutoBattle"] = {
            m_StaticFlagID = 2813,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 寵物切換快捷 企劃永標未定 此為暫代
        ["&Btn_PetFunc"] = {
            m_StaticFlagID = 50002,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---主介面 坐騎快捷 企劃永標未定 此為暫代
        ["&Btn_Mount"] = {
            m_StaticFlagID = 50003,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
    },

}


--================ 目標清單介面 ================
---目標清單 介面資料
this.m_UISettingData[33] = {
    --=========介面=========
    ---目標清單 本人
    m_UI = Tracing_Controller,
    ---目標清單 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---目標清單 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 小地圖介面 ================
---小地圖 介面資料
this.m_UISettingData[37] = {
    --=========介面=========
    ---小地圖 本人
    m_UI = MiniMap_Controller,
    ---小地圖 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2808,
    ---小地圖 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}


--================ 掛機介面 ================
---掛機 介面資料
this.m_UISettingData[39] = {
    --=========介面=========
    ---掛機 本人
    m_UI = nil,
    ---掛機 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2813,
    ---掛機 顯示開放等級 = 0 直接顯示
    m_OpenLv = 1,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 自動喝水介面 ================
---自動喝水 介面資料
this.m_UISettingData[40] = {
    --=========介面=========
    ---自動喝水 本人
    m_UI = AutoDrink_Controller,
    ---自動喝水 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2819,
    ---自動喝水 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 死亡介面 ================
---死亡 介面資料
this.m_UISettingData[44] = {
    --=========介面=========
    ---死亡 本人
    m_UI = Death_Controller,
    ---死亡 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---死亡 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = Death_Model,
}

--================ 功能選單介面 ================
---功能選單 介面資料
this.m_UISettingData[45] = {
    --=========介面=========
    ---功能選單 本人
    m_UI = Menu_Controller,
    ---功能選單 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2801,
    ---功能選單 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 地圖介面 ================
---地圖 介面資料
this.m_UISettingData[51] = {
    --=========介面=========
    ---小地圖 本人
    m_UI = Map_Controller,
    ---小地圖 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---小地圖 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 寵物介面 ================
---寵物 介面資料
this.m_UISettingData[52] = {
    --=========介面=========
    ---寵物 本人
    m_UI = PetPage_Controller,
    ---寵物 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---寵物 顯示開放等級 = 0 直接顯示
    m_OpenLv = 10,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 寵物 Hint 介面 ================
---寵物 Hint 介面資料
this.m_UISettingData[53] = {
    --=========介面=========
    ---寵物 Hint 本人
    m_UI = PetHint_Controller,
    ---寵物 Hint 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---寵物 Hint 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 任務介面 ================
---任務 介面資料
this.m_UISettingData[61] = {
    --=========介面=========
    ---任務 本人
    m_UI = ExploreDairy_Controller,
    ---任務 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---任務 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 對話介面 ================
---對話 介面資料
this.m_UISettingData[62] = {
    --=========介面=========
    ---對話 本人
    m_UI = EventTalk_Controller,
    ---對話 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---對話 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 活動介面 ================
---活動 介面資料
this.m_UISettingData[63] = {
    --=========介面=========
    ---活動 本人
    m_UI = nil,
    ---活動 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,--2802,
    ---活動 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 打字機介面 ================
---打字機 介面資料
this.m_UISettingData[64] = {
    --=========介面=========
    ---打字機 本人
    m_UI = CaptionTheater_Controller,
    ---打字機 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---打字機 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 場景動畫 介面 ================
---場景動畫 介面資料
this.m_UISettingData[65] = {
    --=========介面=========
    ---場景動畫 本人
    m_UI = TimeLine_Controller,
    ---場景動畫 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---場景動畫 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 江湖介面 ================
---江湖 介面資料
this.m_UISettingData[71] = {
    --=========介面=========
    ---江湖 本人
    m_UI = Activity_Controller,
    ---江湖 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2803,
    ---江湖 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 副本通關介面 ================
---副本通關 介面資料
this.m_UISettingData[72] = {
    --=========介面=========
    ---副本通關 本人
    m_UI = DungeonPass_Controller,
    ---副本通關 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2803,
    ---副本通關 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 全版招募介面 ================
---全版招募 介面資料
this.m_UISettingData[73] = {
    --=========介面=========
    ---全版招募 本人
    m_UI = Recruit_Controller,
    ---全版招募 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2803,
    ---全版招募 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 半版招募介面 ================
---半版招募 介面資料
this.m_UISettingData[74] = {
    --=========介面=========
    ---半版招募 本人
    m_UI = RecruitHalf_Controller,
    ---半版招募 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2803,
    ---半版招募 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 時間表介面 ================
---時間表 介面資料
this.m_UISettingData[75] = {
    --=========介面=========
    ---時間表 本人
    m_UI = Schedule_Controller,
    ---時間表 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---時間表 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}


--================ 幫會介面 ================
---幫會 介面資料
this.m_UISettingData[82] = {
    --=========介面=========
    ---幫會 本人
    m_UI = nil,
    ---幫會 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2803,
    ---幫會 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 聊天介面 ================
---聊天 介面資料
this.m_UISettingData[83] = {
    --=========介面=========
    ---聊天 本人
    m_UI = ChatController,
    ---聊天 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---聊天 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}


--================ 社群介面 ================
---社群 介面資料
this.m_UISettingData[84] = {
    --=========介面=========
    ---社群 本人
    m_UI = Community_Controller,
    ---社群 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2809,
    ---社群 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 市集介面 ================
---市集 介面資料
this.m_UISettingData[87] = {
    --=========介面=========
    ---市集 本人
    m_UI = nil,
    ---市集 顯示永標 = 0 直接顯示
    m_StaticFlagID = 3701,
    ---市集 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 互動介面 ================
---互動 介面資料
this.m_UISettingData[89] = {
    --=========介面=========
    ---互動介面 本人
    m_UI = Interact_Controller,
    ---互動介面 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---互動介面 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 情緣介面 ================
---情緣 介面資料
this.m_UISettingData[91] = {
    --=========介面=========
    ---情緣 本人
    m_UI = nil,
    ---情緣 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2809,
    ---情緣 顯示開放等級 = 0 直接顯示
    m_OpenLv = 25,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}


--================ 屬性介面 ================
---屬性 介面資料
this.m_UISettingData[101] = {
    --=========介面=========
    ---屬性 本人
    m_UI = RoleAttribute_Controller,
    ---屬性 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---屬性 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = PlayerData,
}


--================ 背包介面 ================
---背包 介面資料
this.m_UISettingData[102] = {
    --=========介面=========
    ---背包 本人
    m_UI = Bag_Controller,
    ---背包 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---背包 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 物品 Hint 介面 ================
---物品 Hint 介面資料
this.m_UISettingData[103] = {
    --=========介面=========
    ---物品 Hint 本人
    m_UI = ItemHint_Controller,
    ---物品 Hint 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---物品 Hint 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 強化介面 ================
---強化 介面資料
this.m_UISettingData[104] = {
    --=========介面=========
    ---強化 本人
    m_UI = nil,
    ---強化 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---強化 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 量子回音(技能)介面 ================
---量子回音(技能) 介面資料
this.m_UISettingData[105] = {
    --=========介面=========
    ---量子回音(技能) 本人
    m_UI = QuantumEchoEntry_Controller,
    ---量子回音(技能) 顯示永標 = 0 直接顯示
    m_StaticFlagID = 6304,
    ---量子回音(技能) 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ SkillHint 介面 ================
---SkillHint 介面資料
this.m_UISettingData[106] = {
    --=========介面=========
    ---SkillHint 本人
    m_UI = SkillHint_Controller,
    ---SkillHint 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---SkillHint 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ BuffHint 介面 ================
---BuffHint 介面資料
this.m_UISettingData[107] = {
    --=========介面=========
    ---BuffHint 本人
    m_UI = BuffHint_Controller,
    ---BuffHint 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---BuffHint 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 裝備欄介面 ================
---裝備欄 介面資料
this.m_UISettingData[108] = {
    --=========介面=========
    ---裝備欄 本人
    m_UI = Equipment_Controller,
    ---裝備欄 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---裝備欄 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    --=========元件=========
    ---裝備欄 需要各自控制顯示元件的資料 需要有控制的 再加
    --[[m_ComponentsVisibleData = {
        ---裝備欄 套裝切換群組按鈕
        ["&GroupTab_Set"] = {
            m_OpenLv = 20,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        },
        ---裝備欄 套裝第三套
        ["&GroupBtn_Set3"] = {
            m_StaticFlagID = 2901,
            m_EShowTypeWithNoFlag = EShowTypeWithNoFlag.Default,
        }
    },]]

    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 排行榜介面 ================
---排行榜 介面資料
this.m_UISettingData[109] = {
    --=========介面=========
    ---排行榜 本人
    m_UI = nil,
    ---排行榜 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---排行榜 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 衣櫃介面 ================
---衣櫃 介面資料
this.m_UISettingData[111] = {
    --=========介面=========
    ---衣櫃 本人
    m_UI = nil,
    ---衣櫃 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---衣櫃 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 合成介面 ================
---合成 介面資料
this.m_UISettingData[112] = {
    --=========介面=========
    ---合成 本人
    m_UI = Print3D_Controller,
    ---合成 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---合成 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = Print3D_Model,
}

--================ 內功介面 ================
---內功 介面資料
this.m_UISettingData[113] = {
    --=========介面=========
    ---內功 本人
    m_UI = InnerBook_Controller,
    ---內功 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---內功 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}
--================ 時光機-量子核心艙介面 ================
---量子核心艙 介面資料
this.m_UISettingData[114] = {
    --=========介面=========
    ---量子核心艙 本人
    m_UI = EnergyCore_Controller,
    ---量子核心艙 顯示永標 = 0 直接顯示
    m_StaticFlagID = 1356,
    ---量子核心艙 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 再造增幅艙介面 ================
---再造增幅艙 介面資料
this.m_UISettingData[115] = {
    --=========介面=========
    ---再造增幅艙 本人
    m_UI = ReengineeringGrowth_Controller,
    ---再造增幅艙 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---再造增幅艙 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 傳送介面 ================
---傳送 介面資料
this.m_UISettingData[116] = {
    --=========介面=========
    ---傳送 本人
    m_UI = nil,
    ---傳送 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2808,
    ---傳送 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 量子空間介面 ================
---量子空間 介面資料
this.m_UISettingData[117] = {
    --=========介面=========
    ---量子空間 本人
    m_UI = nil,
    ---量子空間 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---量子空間 顯示開放等級 = 0 直接顯示
    m_OpenLv = 12,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}
--================ 時光機-修復艙室介面 ================
---修復艙室 介面資料
this.m_UISettingData[118] = {
    --=========介面=========
    ---修復艙室 本人
    m_UI = RoomRepair_Controller,
    ---修復艙室 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---修復艙室 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ 外功介面 ================
---外功 介面資料
this.m_UISettingData[119] = {
    --=========介面=========
    ---修復艙室 本人
    m_UI = SkillBook_Controller,
    ---外功 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---外功 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 時空商城 介面 ================
---時空商城 介面資料
this.m_UISettingData[127] = {
    --=========介面=========
    ---時空商城 本人
    m_UI = Mall_Controller,
    ---時空商城 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---時空商城 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = Mall_Model,
}

--================ 轉轉樂 介面 ================
---轉轉樂 介面資料
this.m_UISettingData[128] = {
    --=========介面=========
    ---轉轉樂 本人
    m_UI = nil,
    ---轉轉樂 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---轉轉樂 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 派對 介面 ================
---派對 介面資料
this.m_UISettingData[133] = {
    --=========介面=========
    ---派對 本人
    m_UI = nil,
    ---派對 顯示永標 = 0 直接顯示
    m_StaticFlagID = 9999,
    ---派對 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 教學介面 ================
---教學 介面資料
this.m_UISettingData[141] = {
    --=========介面=========
    ---教學 本人
    m_UI = Teach_Controller,
    ---教學 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---教學 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 九宮格介面 ================
---九宮格 介面資料
this.m_UISettingData[142] = {
    --=========介面=========
    ---九宮格 本人
    m_UI = nil,
    ---九宮格 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2804,
    ---九宮格 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 福利、成就介面 ================
---福利、成就 介面資料
this.m_UISettingData[143] = {
    --=========介面=========
    ---福利、成就 本人
    m_UI = nil,
    ---福利、成就 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2804,
    ---福利、成就 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 指南介面 ================
---指南 介面資料
this.m_UISettingData[146] = {
    --=========介面=========
    ---指南 本人
    m_UI = nil,
    ---指南 顯示永標 = 0 直接顯示
    m_StaticFlagID = 2806,
    ---指南 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 時光機介面 ================
---時光機 介面資料
this.m_UISettingData[150] = {
    --=========介面=========
    ---時光機 本人
    m_UI = TimeMachine_Controller,
    ---時光機 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---時光機 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 兌換介面 ================
---兌換 介面資料
this.m_UISettingData[164] = {
    --=========介面=========
    ---兌換 本人
    m_UI = ExchangeShop_Controller,
    ---兌換 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---兌換 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 鏢局介面 ================
---鏢局 介面資料
this.m_UISettingData[166] = {
    --=========介面=========
    ---鏢局 本人
    m_UI = nil,
    ---鏢局 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---鏢局 顯示開放等級 = 0 直接顯示
    m_OpenLv = 12,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 神秘商人介面 ================
---神秘商人 介面資料
this.m_UISettingData[167] = {
    --=========介面=========
    ---神秘商人 本人
    m_UI = MysticShop_Controller,
    ---神秘商人 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---神秘商人 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別1 介面 ================
---詢問視窗 類別1 介面資料
this.m_UISettingData[170] = {
    --=========介面=========
    ---詢問視窗 類別1 本人
    m_UI = CommonQuery_Type1_Controller,
    ---詢問視窗 類別1 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別1 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別2 介面 ================
---詢問視窗 類別2 介面資料
this.m_UISettingData[171] = {
    --=========介面=========
    ---詢問視窗 類別2 本人
    m_UI = CommonQuery_Type2_Controller,
    ---詢問視窗 類別2 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別2 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別3 介面 ================
---詢問視窗 類別6 介面資料
this.m_UISettingData[173] = {
    --=========介面=========
    ---詢問視窗 類別3 本人
    m_UI = CommonQuery_Type3_Controller,
    ---詢問視窗 類別3 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別3 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別7 介面 ================
---詢問視窗 類別4 介面資料
this.m_UISettingData[174] = {
    --=========介面=========
    ---詢問視窗 類別4 本人
    m_UI = CommonQuery_Type4_Controller,
    ---詢問視窗 類別4 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別4 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別8 介面 ================
---詢問視窗 類別5 介面資料
this.m_UISettingData[175] = {
    --=========介面=========
    ---詢問視窗 類別5 本人
    m_UI = CommonQuery_Type5_Controller,
    ---詢問視窗 類別5 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別5 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 類別9 介面 ================
---詢問視窗 類別6 介面資料
this.m_UISettingData[176] = {
    --=========介面=========
    ---詢問視窗 類別6 本人
    m_UI = CommonQuery_Type6_Controller,
    ---詢問視窗 類別6 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別6 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

---詢問視窗 類別7 介面資料
this.m_UISettingData[177] = {
    --=========介面=========
    ---詢問視窗 類別7 本人
    m_UI = CommonQuery_Type7_Controller,
    ---詢問視窗 類別7 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 類別7 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 遊樂場 介面 ================
---遊樂場 介面資料
this.m_UISettingData[200] = {
    --=========介面=========
    ---遊樂場 本人
    m_UI = Playground_Controller,
    ---遊樂場 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---遊樂場 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 小恐龍小遊戲 介面 ================
---小恐龍小遊戲 介面資料
this.m_UISettingData[201] = {
    --=========介面=========
    ---小恐龍小遊戲 本人
    m_UI = DinosaurGame_Controller,
    ---小恐龍小遊戲 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---小恐龍小遊戲 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 打地鼠小遊戲 介面 ================
---打地鼠小遊戲 介面資料
this.m_UISettingData[202] = {
    --=========介面=========
    ---打地鼠小遊戲 本人
    m_UI = WhacAMole_Controller,
    ---打地鼠小遊戲 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---打地鼠小遊戲 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 接電路小遊戲 介面 ================
---接電路小遊戲 介面資料
this.m_UISettingData[203] = {
    --=========介面=========
    ---接電路小遊戲 本人
    m_UI = CircuitGame_Controller,
    ---接電路小遊戲 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---接電路小遊戲 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 飛船小遊戲 介面 ================
---飛船小遊戲 介面資料
this.m_UISettingData[204] = {
    --=========介面=========
    ---飛船小遊戲 本人
    m_UI = SpaceShipGame_Controller,
    ---飛船小遊戲 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---飛船小遊戲 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 黃易快打小遊戲 介面 ================
---黃易快打小遊戲 介面資料
this.m_UISettingData[205] = {
    --=========介面=========
    ---黃易快打小遊戲 本人
    m_UI = StreetFighter_Controller,
    ---黃易快打小遊戲 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---黃易快打小遊戲 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
}

--================ Debug 介面 ================
---Debug 介面資料
this.m_UISettingData[240] = {
    --=========介面=========
    ---Debug 本人
    m_UI = Debug_Controller,
    ---Debug 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---Debug 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 金手指 介面 ================
---金手指 介面資料
this.m_UISettingData[241] = {
    --=========介面=========
    ---金手指 本人
    m_UI = GoldFinger_Controller,
    ---金手指 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---金手指 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 戰鬥編輯器 介面 ================
---戰鬥編輯器 介面資料
this.m_UISettingData[242] = {
    --=========介面=========
    ---戰鬥編輯器 本人
    m_UI = BattleEditor_Controller,
    ---戰鬥編輯器 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---戰鬥編輯器 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ DPS 計算 介面 ================
---DPS 計算 介面資料
this.m_UISettingData[243] = {
    --=========介面=========
    ---DPS 計算 本人
    m_UI = BattleCalculator_Controller,
    ---DPS 計算 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---DPS 計算 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ 詢問視窗 共用背景黑幕 介面 ================
---詢問視窗 共用背景黑幕 介面資料
this.m_UISettingData[5000] = {
    --=========介面=========
    ---詢問視窗 共用背景黑幕 本人
    m_UI = CommonQuery_BlackBackGround_Controller,
    ---詢問視窗 共用背景黑幕 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---詢問視窗 共用背景黑幕 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}

--================ HintMgr ================
---HintMgr 介面資料
this.m_UISettingData[5001] = {
    --=========介面=========
    ---HintMgr 本人
    m_UI = HintMgr_Controller,
    ---HintMgr 顯示永標 = 0 直接顯示
    m_StaticFlagID = 0,
    ---HintMgr 顯示開放等級 = 0 直接顯示
    m_OpenLv = 0,
    ---提供紅點系統如果要讀取是否顯示紅點要找哪個Model 如果對應系統不需要紅點提示 則設定成nil
    m_Model = nil,
}
