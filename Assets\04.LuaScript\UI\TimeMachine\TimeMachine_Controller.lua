---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================
---時光機 Controller
---@class TimeMachine_Controller
---author 蛋糕
---telephone #2916
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.3.21
TimeMachine_Controller = {}
local this = TimeMachine_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("TimeMachine_View", "TimeMachine_Controller", EUIOrderLayers.FullPage, false, "bg_005")

--- 區域資料夾群組
local _RoomGroup = {}
--- 房間按鈕群組
local _RoomButton = {}
--- 中央提示群組
local _AreaHint_Middle = {}

--- 艙房ICON名稱
local ROOM_ICON_NAME = "TMRoom_icon_"

--- 艙房ICON名稱
local ROOM_TEXTURE_NAME = "TMRoom_small_"

--- 進入房間所需資訊
local _EnterRoomInfo =
{
    m_RoomIdx = 0,
    m_IsRoomFix = true,
    m_RoomUIIdx = 0
}


--- 區域名稱字串
local ETimeMachineAreaNameIdx =
{
    --- 超能模組區
    [1] = 20300001,
    --- 工程研發區
    [2] = 20300002,
    --- 動力主控區
    [3] = 20300003
}
--- 艙室按鈕、資料夾選單滾動空間
local SCROLLVIEW_RANGE =
{
    MAX = 890,
    MIN = 746
}

--- 時光機動態表現移動位置
local m_TimeMachine_ImageAnchor =
{
    --- 原圖位置(未點選艙室時使用)
    [0] = Vector3.New(187.7,-57.4,0),
    --- 區域 1 位置
    [1] = Vector3.New(-352,-76,0),
    --- 區域 2 位置
    [2] = Vector3.New(219,33,0),
    --- 區域 3 位置
    [3] = Vector3.New(-476,-608,0),
}
--- 時光機動態表現縮放比例
local m_TimeMachine_ImageScale =
{
    --- 原圖比例(未點選艙室時使用)
    [0] = Vector3.New(0.87,0.87,0.87),
    --- 區域 1 比例
    [1] = Vector3.New(1.8,1.8,1.8),
    --- 區域 2 比例
    [2] = Vector3.New(1.22,1.22,1.22),
    --- 區域 3 比例
    [3] = Vector3.New(2.15,2.15,2.15),

}
--- 艙室貼圖暫存(關介面時要還)
local m_RoomTextureTable = {}

function TimeMachine_Controller.Init()
    -- 目前選擇區域 - 動態效果用
    this.m_LastTimeSelectArea = 0
    this.m_AreaCount = TimeMachineMgr.GetAreaCount()
    --- 上一頁按鈕
    this.m_Btn_Back = this.m_ViewRef.m_Dic_Trans:Get("&Button_Back")
    Button.AddListener(this.m_Btn_Back, EventTriggerType.PointerClick, function()
        UIMgr.CloseToPreviousPage(TimeMachine_Controller)
    end)
    --- 關閉按鈕
    this.m_Btn_Close = this.m_ViewRef.m_Dic_Trans:Get("&Button_Close_Blue")
    Button.AddListener(this.m_Btn_Close, EventTriggerType.PointerClick, function()
        UIMgr.Close(TimeMachine_Controller)
    end)

	---資源列
    this.m_Group_Resources = {}
	this.m_Group_Resources.transform = this.m_ViewRef.m_Dic_Trans:Get("&Group_Resources")
    local _CurrentResourceTable = 
    {
        [1] = ResourceBar:GetItemIDTableFromEResourceGroupTypeAndItemIDTable(EResourceGroupType.BaseCurrency)
    }
	this.m_Group_Resources.m_Resources = ResourceBar:InitResourceBar(this.m_Group_Resources.transform, _CurrentResourceTable)

    this.m_RawImage_TimeMaChine = this.m_ViewRef.m_Dic_Trans:Get("&RawImage_TimeMachine")
    this.m_Gobj_AreaImage = {}
    this.m_RawImage_AreaOutLine = {}
    this.m_RawImage_AreaGrid = {}
	for i = 1, 3 do
        this.m_Gobj_AreaImage[i] = this.m_ViewRef.m_Dic_Trans:Get("&RawImage_Area_"..i).gameObject
		this.m_RawImage_AreaOutLine[i] = this.m_ViewRef.m_Dic_RawImage:Get("&RawImage_AreaOutLine_"..i)
        this.m_RawImage_AreaGrid[i] = this.m_ViewRef.m_Dic_RawImage:Get("&RawImage_AreaGrid_"..i)

	end
    this.m_ScrollView_LeftPanel = this.m_ViewRef.m_Dic_Trans:Get("&ScrollView_LeftPanel")
    this.m_Gobj_RoomAreaContent = this.m_ViewRef.m_Dic_Trans:Get("&RoomAreaContent")

    this.m_TmpBtn_AreaFolder = this.m_ViewRef.m_Dic_Trans:Get("&Button_AreaFolder")
    this.m_TmpGobj_ButtonGroup = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_ButtonGroup")
    this.m_TmpBtn_Room = this.m_ViewRef.m_Dic_Trans:Get("&Button_Room")

    this.m_TmpRoomButton = this.m_ViewRef.m_Dic_Trans:Get("&Button_Room")
    this.m_GObj_GuideBox = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_GuideBox")
    this.m_TMP_GuideMessage = this.m_ViewRef.m_Dic_TMPText:Get("&Text_GuideMessage")
    this.m_Btn_GuideMission = this.m_ViewRef.m_Dic_Trans:Get("&Button_GuideMission")
    Button.AddListener(this.m_Btn_GuideMission, EventTriggerType.PointerClick, function()
        local _NextUnlockData = TimeMachineMgr.GetNextUnlockRoom()
        local _MissionData = MissionMgr.GetMissionByMovingFlag(_NextUnlockData.m_GuideMoveFlag)
        ExploreDairyMgr.OpenToMission(_MissionData)
    end)

    this.m_Gobj_RoomPanel = this.m_ViewRef.m_Dic_Trans:Get("&RoomInfo_Panel")
    this.m_UIAni_RoomPanel = this.m_ViewRef.m_Dic_UIAnimation:Get("&RoomInfo_Panel")
    this.m_TMP_RoomTitle = this.m_ViewRef.m_Dic_TMPText:Get("&Text_RoomTitle")
    this.m_Image_RoomIcon = this.m_ViewRef.m_Dic_Image:Get("&Image_RoomIcon")
    this.m_Image_RoomBG = this.m_ViewRef.m_Dic_RawImage:Get("&Image_RoomBG")
    this.m_GObj_UnlockGroup = this.m_ViewRef.m_Dic_Trans:Get("&Gobj_UnlockGroup")
    this.m_TMP_UnlockTitle = this.m_ViewRef.m_Dic_TMPText:Get("&Text_UnlockTitle")
    this.m_TMP_UnlockMission = this.m_ViewRef.m_Dic_TMPText:Get("&Text_UnlockMission")
    this.m_Btn_UnlockMission = this.m_ViewRef.m_Dic_Trans:Get("&Button_UnlockMission")

    ---修復物品背景
    this.m_GObj_KeyBG = this.m_ViewRef.m_Dic_Trans:Get("&GObj_KeyBG").gameObject
    ---修復物品
    this.m_Gobj_IconItem = IconMgr.NewItemIcon(0, this.m_ViewRef.m_Dic_Trans:Get("&Icon_Item"), 108, nil, false)

    Button.AddListener(this.m_Btn_UnlockMission, EventTriggerType.PointerClick, function()
        
        if(this.NowRoomMissionData) then
            ExploreDairyMgr.OpenToMission(this.NowRoomMissionData)
        end
    end)

    this.m_TMP_RoomDescribe = this.m_ViewRef.m_Dic_TMPText:Get("&Text_RoomDescribe")


    this.m_Btn_Enter = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Enter"))
    Button.AddListener(this.m_Btn_Enter, EventTriggerType.PointerClick, TimeMachine_Controller.OnButtonEnterClick)

    SpriteMgr.Load("Common_icon_003", function (iSprite)
        if iSprite ~= nil then
            this.m_Sprite_UpgradeIcon = iSprite
        end
    end)
    SpriteMgr.Load("Common_icon_004", function (iSprite)
        if iSprite ~= nil then
            this.m_Sprite_FixIcon = iSprite
        end
    end)
    TimeMachine_Controller.AreaInit()
    TimeMachine_Controller.AreaHintInit()
    TimeMachine_Controller.InstRoomButton()
end
--- 開啟時光機艙室方法
---@param iParam table 開啟時光機帶入參數 ([1] 指定開啟艙室面板編號)
function TimeMachine_Controller.Open(iParam)    
    for i = 1, this.m_AreaCount do
        local _RoomInFolder = TimeMachineMgr.GetRoomIdxInAreaFolder(i)
        for key, _RoomIdx in pairs(_RoomInFolder) do
            TimeMachine_Controller.RefreshRoomButton(_RoomIdx)
        end
        TimeMachine_Controller.RefreshAreaForderButton(i)
    end
    TimeMachine_Controller.SetMiddleAreaHint(false)

    TimeMachine_Controller.RefreshUnlockGuide()

    GroupButton.ResetAllButtonsState(this.m_Gobj_RoomAreaContent)
    
    this.m_LastTimeSelectArea = 0

    -- 處理開啟指定艙室面板
    if iParam ~= nil then
        this.m_OpenRoomIndex = iParam[1]
    end

    return true
end
function TimeMachine_Controller.ToDoAfterOpenUISucceed()    
    if this.m_OpenRoomIndex ~= nil and this.m_OpenRoomIndex ~= 0 then
        Button.OnPointerClick(_RoomButton[this.m_OpenRoomIndex].m_Gobj)
    end
end

function TimeMachine_Controller.Close()
    TimeMachine_Controller.ClearRoomTexture()
    TimeMachine_Controller.ResetEnterAreaEffect()
    this.m_Gobj_RoomPanel.gameObject:SetActive(false)
end
--- 刪UI
function TimeMachine_Controller.OnDestroy()
    _RoomGroup = {}
    _RoomButton = {}
    _AreaHint_Middle = {}
    return true
end

--- 區域資料夾初始化
function TimeMachine_Controller.AreaInit()
    for i = 1, this.m_AreaCount do
        local _Tmp_RoomGroup = {}
        -- 第一個用 prefab原有的原件
        if i == 1 then
            _Tmp_RoomGroup.m_Btn_AreaFolder = Button.New(this.m_TmpBtn_AreaFolder)
            _Tmp_RoomGroup.m_Btn_AreaButtonGroup = this.m_TmpGobj_ButtonGroup
        else
            _Tmp_RoomGroup.m_Btn_AreaFolder = Button.New(this.m_TmpBtn_AreaFolder:Instantiate(this.m_Gobj_RoomAreaContent))
            _Tmp_RoomGroup.m_Btn_AreaButtonGroup = this.m_TmpGobj_ButtonGroup:Instantiate(this.m_Gobj_RoomAreaContent)
        end
        Button.AddListener(_Tmp_RoomGroup.m_Btn_AreaFolder, EventTriggerType.PointerClick, function()
            TimeMachine_Controller.OnAreaFolderClick(i)
        end)
        _Tmp_RoomGroup.m_Image_FoldBG = _Tmp_RoomGroup.m_Btn_AreaFolder.transform:GetComponent(typeof( Image ))
        _Tmp_RoomGroup.m_TMP_AreaName = _Tmp_RoomGroup.m_Btn_AreaFolder.transform:Find("TMP_AreaName"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _Tmp_RoomGroup.m_TMP_FixFixPercent = _Tmp_RoomGroup.m_Btn_AreaFolder.transform:Find("TMP_FixPercent"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        _Tmp_RoomGroup.m_Image_FoldIcon = _Tmp_RoomGroup.m_Btn_AreaFolder.transform:Find("Image_FoldIcon")
        --是否摺疊旗標
        _Tmp_RoomGroup.m_IsFold = false

        _Tmp_RoomGroup.m_TMP_AreaName.text = TextData.Get(ETimeMachineAreaNameIdx[i])
        _RoomGroup[i] = _Tmp_RoomGroup
    end
end

--- 區域標示初始化
function TimeMachine_Controller.AreaHintInit()
    for i = 0, this.m_AreaCount do
        local _Tmp_AreaHint = {}
        _Tmp_AreaHint.m_Trans_MiddleArea = this.m_ViewRef.m_Dic_Trans:Get("&Image_AreaHint_"..i)
        if _Tmp_AreaHint.m_Trans_MiddleArea == nil then
            D.LogError("區域元件與表定數量不同請檢查串表或是UI")
        else
            _Tmp_AreaHint.m_Image_Light = _Tmp_AreaHint.m_Trans_MiddleArea.transform:Find("Image_Light"):GetComponent(typeof( Image ))
            _Tmp_AreaHint.m_TMP_AreaName = _Tmp_AreaHint.m_Trans_MiddleArea.transform:Find("TMP_AreaName"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
            _Tmp_AreaHint.m_TMP_AreaRepair_Title = _Tmp_AreaHint.m_Trans_MiddleArea.transform:Find("TMP_AreaRepair_Title"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
            _Tmp_AreaHint.m_TMP_AreaRepair_Rate = _Tmp_AreaHint.m_Trans_MiddleArea.transform:Find("TMP_AreaRepair_Rate"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
        end
        _AreaHint_Middle[i] = _Tmp_AreaHint
    end
end
-- 初始化艙房按鈕
function TimeMachine_Controller.InstRoomButton()
    for _BelongGroupIdx = 1, this.m_AreaCount do
        local _RoomInFolder = TimeMachineMgr.GetRoomIdxInAreaFolder(_BelongGroupIdx)
        for key, _RoomIdx in pairs(_RoomInFolder) do
            local _Tmp_RoomButton = {}
            -- 第一個按鈕用 prefab 上原有的
            if table.IsNullOrEmpty(_RoomButton) then
                _Tmp_RoomButton.m_Gobj = this.m_TmpBtn_Room
                _Tmp_RoomButton.m_Gobj:SetParent(_RoomGroup[_BelongGroupIdx].m_Btn_AreaButtonGroup)
            else
                _Tmp_RoomButton.m_Gobj = this.m_TmpBtn_Room:Instantiate(_RoomGroup[_BelongGroupIdx].m_Btn_AreaButtonGroup)
            end
            _Tmp_RoomButton.m_Gobj.transform.name = "RoomButton_Idx_".._RoomIdx
            _Tmp_RoomButton.m_TMP_RoomTitle = _Tmp_RoomButton.m_Gobj.transform:Find("TMP_RoomTitle"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
            _Tmp_RoomButton.m_TMP_RoomState = _Tmp_RoomButton.m_Gobj.transform:Find("Image_State_BG/&TMP_RoomState"):GetComponent(typeof( TMPro.TextMeshProUGUI ))
            _Tmp_RoomButton.m_Gobj.gameObject:SetActive(true)
            _Tmp_RoomButton.m_Image_RoomBtnFrame = _Tmp_RoomButton.m_Gobj.transform:Find("Image_RoomFrame"):GetComponent(typeof( Image ))
            _Tmp_RoomButton.m_UIColorChange_RoomBtnFrame = _Tmp_RoomButton.m_Image_RoomBtnFrame:GetComponent("UIRenderChangeColor")
            _Tmp_RoomButton.m_Image_RoomStateLV = {}
            for i = 1, 3 do
                _Tmp_RoomButton.m_Image_RoomStateLV[i] = _Tmp_RoomButton.m_Gobj.transform:Find("Image_State_BG/Image_StateLV_"..i):GetComponent(typeof( Image ))
            end

            _Tmp_RoomButton.m_Image_RoomIcon = _Tmp_RoomButton.m_Gobj.transform:Find("Image_RoomIcon"):GetComponent(typeof( Image ))
            SpriteMgr.Load(TimeMachine_Controller.GetRoomIconName(_RoomIdx) , _Tmp_RoomButton.m_Image_RoomIcon)

            _Tmp_RoomButton.m_Image_Upgrade = _Tmp_RoomButton.m_Gobj.transform:Find("Image_Upgrade"):GetComponent(typeof( Image ))
            Button.AddListener(_Tmp_RoomButton.m_Gobj, EventTriggerType.PointerClick, function()
                TimeMachine_Controller.OnRoomButtonClick(_RoomIdx)
            end)

            _RoomButton[_RoomIdx] = _Tmp_RoomButton
            GroupButton.AddButtonToList(this.m_Gobj_RoomAreaContent.gameObject,_Tmp_RoomButton.m_Gobj.gameObject)
        end
    end
end

--- 刷新艙房及對應資料夾資訊
---@param iRoomIdx number
function TimeMachine_Controller.RefreshRoomAndFolder(iRoomIdx)
    TimeMachine_Controller.RefreshRoomButton(iRoomIdx)

    TimeMachine_Controller.RefreshAreaForderButton(math.ceil( iRoomIdx / TimeMachineMgr.MaxRoomCountPerGroup))

    TimeMachine_Controller.RefreshSubPanel(iRoomIdx)

    TimeMachine_Controller.RefreshUnlockGuide()
end

--- 刷新區域資料夾按鈕
---@param iAreaIdx number 區域索引
function TimeMachine_Controller.RefreshAreaForderButton(iAreaIdx)
    local _AreaFolderData = TimeMachineMgr.GetRoomIdxInAreaFolder(iAreaIdx)
    -- 區域修復狀態
    local _AreaState = nil
    -- 區域修復值(百分比顯示)
    local _AreaFixRate = 0

    -- 取得區域狀態
    local function SetAreaState(iRoomState)
        -- 區域修復規則
        -- 全艙房損毀 - 毀損中(紅)
        -- 全艙房修復完成 - 修復完成(藍)
        -- 其餘皆為修復中(黃)
        if _AreaState == nil then
            _AreaState = iRoomState
        end

        if _AreaState == ETimeMachineState.Broken then
            if iRoomState ~= ETimeMachineState.Broken then
                _AreaState = ETimeMachineState.Repairing
            end
        elseif _AreaState == ETimeMachineState.Complete or _AreaState == ETimeMachineState.Adapt then
            if iRoomState == ETimeMachineState.Broken or iRoomState == ETimeMachineState.Repairing then
                _AreaState = ETimeMachineState.Repairing
            end
        else
            _AreaState = ETimeMachineState.Repairing
        end
    end

    for key, _RoomIdx in pairs(_AreaFolderData) do
        local _TmpData = TimeMachineMgr.GetRoomInfoByStep(_RoomIdx,TimeMachineMgr.GetRoomStep(_RoomIdx))
        _AreaFixRate = _AreaFixRate + _TmpData.m_FixPercentage
        local _RoomState,_RoomStateStep = TimeMachineMgr.GetRoomState(_RoomIdx)

        SetAreaState(_RoomState)
    end
    -- 千分位轉百分位 + %
    _AreaFixRate = math.floor(_AreaFixRate/10).."%"
    local _AreaColor = TimeMachineMgr.GetRoomStateColorInfo(_AreaState)
    _RoomGroup[iAreaIdx].m_TMP_FixFixPercent.text = GString.StringWithStyle(_AreaFixRate,_AreaColor.StringStyle)
    _RoomGroup[iAreaIdx].m_IsFold = false
    _RoomGroup[iAreaIdx].m_Btn_AreaButtonGroup.gameObject:SetActive(not _RoomGroup[iAreaIdx].m_IsFold)
    _RoomGroup[iAreaIdx].m_Btn_AreaFolder:SetSelect(false)
    
    _AreaHint_Middle[iAreaIdx].m_Image_Light.color = _AreaColor.Color_Grid
    _AreaHint_Middle[iAreaIdx].m_TMP_AreaName.text = TextData.Get(ETimeMachineAreaNameIdx[iAreaIdx])
    _AreaHint_Middle[iAreaIdx].m_TMP_AreaRepair_Title.text = TextData.Get(20300009)
    _AreaHint_Middle[iAreaIdx].m_TMP_AreaRepair_Rate.text = GString.StringWithStyle(_AreaFixRate,_AreaColor.StringStyle)
    this.m_RawImage_AreaOutLine[iAreaIdx].color = _AreaColor.Color
    --this.m_RawImage_AreaGrid[iAreaIdx].color = _AreaColor.Color_Grid
    
end

--- 刷新艙房按鈕
---@param iRoomIdx number 艙房編號
function TimeMachine_Controller.RefreshRoomButton(iRoomIdx)
    local _TmpData = TimeMachineMgr.GetRoomInfoByStep(iRoomIdx,TimeMachineMgr.GetRoomStep(iRoomIdx))
    local _RoomState,_RoomStateStep = TimeMachineMgr.GetRoomState(iRoomIdx)
    -- 按鈕顏色
    local _StateColor = TimeMachineMgr.GetRoomStateColorInfo(_RoomState)
    -- 修復階段字串
    local _RoomFixStepStr = ""

    -- 狀態0 階段0 -毀損中
    -- 狀態0、1 階段不為0 -修復中
    -- 狀態2 -改造中
    _RoomButton[iRoomIdx].m_Image_Upgrade.sprite = this.m_Sprite_UpgradeIcon
    if _TmpData.m_FixState == 0 or _TmpData.m_FixState == 1 then
        if _TmpData.m_FixStep == 0 then
            _RoomFixStepStr = TextData.Get(20300006)
            _RoomButton[iRoomIdx].m_Image_Upgrade.sprite = this.m_Sprite_FixIcon
        else
            _RoomFixStepStr = GString.Format(TextData.Get(20300004),_RoomStateStep)
        end
    else
        _RoomFixStepStr = GString.Format(TextData.Get(20300005),_RoomStateStep)
    end

    _RoomButton[iRoomIdx].m_TMP_RoomTitle.text = TextData.Get(_TmpData.m_RoomNameIdx)
    _RoomButton[iRoomIdx].m_TMP_RoomState.text = GString.StringWithStyle(_RoomFixStepStr,_StateColor.StringStyle)
    _RoomButton[iRoomIdx].m_Image_RoomBtnFrame.color = _StateColor.Color
    _RoomButton[iRoomIdx].m_UIColorChange_RoomBtnFrame.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, _StateColor.Color)

    _RoomButton[iRoomIdx].m_Image_Upgrade.color = _StateColor.Color
    this.m_Gobj_RoomAreaContent.transform.anchoredPosition = Vector2.zero

    _RoomButton[iRoomIdx].m_Image_RoomIcon.color = _StateColor.Color

    for i = 1, 3 do
        if i <= _RoomState+1 then
            _RoomButton[iRoomIdx].m_Image_RoomStateLV[i].color = _StateColor.Color
        else
            _RoomButton[iRoomIdx].m_Image_RoomStateLV[i].color = Color.Black
        end
    end

    local _FixItemInfo = TimeMachineMgr.CheckFixItemNum(iRoomIdx)
    local _IsCanFix = true
    if table.IsNullOrEmpty(_FixItemInfo) then
        _IsCanFix = false
    else
        if _FixItemInfo ~= nil then
            for key, value in pairs(_FixItemInfo) do
                _IsCanFix = _IsCanFix and value.m_IsEnough
            end
        end
    end

    _RoomButton[iRoomIdx].m_Image_Upgrade.transform.gameObject:SetActive(_IsCanFix)
end

--- 刷新艙房資訊卡
---@param iRoomIdx number
function TimeMachine_Controller.RefreshSubPanel(iRoomIdx)
    local _CurrentSetp = TimeMachineMgr.GetRoomStep(iRoomIdx)
    local _RoomData = TimeMachineMgr.GetRoomInfoByStep(iRoomIdx,_CurrentSetp)

    local _RoomTextureName = ROOM_TEXTURE_NAME .. _RoomData.m_OutSideImageNumber
    this.m_TMP_RoomTitle.text = TextData.Get(_RoomData.m_RoomNameIdx)
    if m_RoomTextureTable[iRoomIdx] == nil then
        m_RoomTextureTable[iRoomIdx]={}
        TextureMgr.Load(_RoomTextureName, false, function(iTex)

            this.m_Image_RoomBG.texture = iTex
            --this.m_Image_RoomBG.gameObject:SetActive(iTex ~= nil)
            -- 紀錄載入的圖片資料 用於查詢
            m_RoomTextureTable[iRoomIdx].m_Name = _RoomTextureName
            m_RoomTextureTable[iRoomIdx].m_Texture = iTex

            TimeMachine_Controller.EnterAreaEffect(iRoomIdx)
        end)
    else
        this.m_Image_RoomBG.texture = m_RoomTextureTable[iRoomIdx].m_Texture
        TimeMachine_Controller.EnterAreaEffect(iRoomIdx)
    end

    local _IsRoomBroken = _CurrentSetp == 0
    if _IsRoomBroken then
        -- 解鎖物品數量
        local _FixItem = TimeMachineMgr.CheckFixItemNum(iRoomIdx)
        local _ItemEnough = true
        for index, value in ipairs(_FixItem) do
            _ItemEnough = _ItemEnough and value.m_IsEnough
        end
        -- 任務動標抓任務
        local _MissionTitleStr = TimeMachine_Controller.GetMissionTitle(_RoomData.m_GuideMoveFlag)

        -- 目前房間 Mission Data
        this.NowRoomMissionData = MissionMgr.GetMissionByMovingFlag(_RoomData.m_GuideMoveFlag)

        Button.SetText(this.m_Btn_UnlockMission ,GString.Format(TextData.Get(20102001),_MissionTitleStr))

        Button.SetText(this.m_Btn_Enter ,TextData.Get(20103009))
        -- 按鈕設定
        this.m_Gobj_IconItem:RefreshIcon(_FixItem[1].m_ItemIdx)
        this.m_Gobj_IconItem:SetClickTwice(false)
        this.m_Gobj_IconItem:SetCount(_ItemEnough and TextData.Get(20300013) or GString.StringWithStyle(TextData.Get(20300013),"RO"))--20300013 動力源
        this.m_Gobj_IconItem:SetMask(not _ItemEnough)
        if _ItemEnough  then
            this.m_Btn_Enter:SetEnable()
        else
            this.m_Btn_Enter:SetDisable()
        end

        _EnterRoomInfo.m_IsRoomFix = false
    else
        -- 準備艙房進入面板
        this.m_TMP_RoomDescribe.text = TextData.Get(_RoomData.m_RoomDescribeIdx)

        Button.SetText(this.m_Btn_Enter ,TextData.Get(20103010))
        this.m_Btn_Enter:SetEnable()

        _EnterRoomInfo.m_RoomUIIdx = _RoomData.m_OpenUIIdx
        _EnterRoomInfo.m_IsRoomFix = true
    end

    _EnterRoomInfo.m_RoomIdx = iRoomIdx

    this.m_Image_RoomIcon.sprite = _RoomButton[iRoomIdx].m_Image_RoomIcon.sprite
    this.m_GObj_UnlockGroup.gameObject:SetActive(_IsRoomBroken)
    this.m_TMP_RoomDescribe.gameObject:SetActive(not _IsRoomBroken)

    this.m_Gobj_RoomPanel.gameObject:SetActive(true)
    this.m_GObj_KeyBG.gameObject:SetActive(_IsRoomBroken)
    this.m_Gobj_IconItem.transform.gameObject:SetActive(_IsRoomBroken)
end

--- 刷新解鎖引導區域資訊
function TimeMachine_Controller.RefreshUnlockGuide()
    local _NextUnlockData = TimeMachineMgr.GetNextUnlockRoom()
    if _NextUnlockData ~= nil then
        local _NextRoomNameStr = TextData.Get(_NextUnlockData.m_RoomNameIdx)
        local _MissionTitleStr = TimeMachine_Controller.GetMissionTitle(_NextUnlockData.m_GuideMoveFlag)

        this.m_TMP_GuideMessage.text = GString.Format(TextData.Get(20300007),_NextRoomNameStr)
        Button.SetText(this.m_Btn_GuideMission,GString.Format(TextData.Get(20102001),_MissionTitleStr))
        this.m_ScrollView_LeftPanel:GetComponent( typeof( RectTransform ) ):SetSizeWithCurrentAnchors(Axis.Vertical,SCROLLVIEW_RANGE.MIN)
    else
        -- 隱藏下一區
        -- 拉大按鈕區
        this.m_ScrollView_LeftPanel:GetComponent( typeof( RectTransform ) ):SetSizeWithCurrentAnchors(Axis.Vertical,SCROLLVIEW_RANGE.MAX)
    end
    this.m_GObj_GuideBox.gameObject:SetActive(_NextUnlockData ~= nil)
end

--- 取得任務標題字串
---@param iMovingFlag number 任務動標
---@return string 任務標題字串
function TimeMachine_Controller.GetMissionTitle(iMovingFlag)
    -- 任務動標抓任務
    local _MissionData = MissionMgr.GetMissionByMovingFlag(iMovingFlag)
    if _MissionData ~= nil then
        return EventStrAll:GetText(_MissionData.TitleStrID)
    else
        D.Log("<color=Red>請檢查任務動標是否正確</color>")
        return ""
    end
end

--- 取得艙房 Icon名稱
function TimeMachine_Controller.GetRoomIconName(iRoomIdx)

    local _RoomData = TimeMachineMgr.GetRoomInfoByRoomIdx(iRoomIdx)

    return ROOM_ICON_NAME.._RoomData.m_RoomIconIdx
end

--- 區域資料夾被點到時要做的事
---@param iGroupID number 區域編號
function TimeMachine_Controller.OnAreaFolderClick(iGroupID)
    _RoomGroup[iGroupID].m_IsFold = not _RoomGroup[iGroupID].m_IsFold
    
    _RoomGroup[iGroupID].m_Btn_AreaButtonGroup.gameObject:SetActive(not _RoomGroup[iGroupID].m_IsFold)
end

--- 艙房按鈕被點到時要做的事
---@param iRoomIdx number 艙房編號
function TimeMachine_Controller.OnRoomButtonClick(iRoomIdx)
    TimeMachine_Controller.RefreshSubPanel(iRoomIdx)
end

--- 啟動/進入房間被點到時要做的事
function TimeMachine_Controller.OnButtonEnterClick()
    if _EnterRoomInfo.m_IsRoomFix then
        -- 進入艙室
        UIMgr.OpenByIndex(_EnterRoomInfo.m_RoomUIIdx)
    else
        local _RoomData = TimeMachineMgr.GetRoomInfoByRoomIdx(_EnterRoomInfo.m_RoomIdx)
        local _FixItemData = TimeMachineMgr.CheckFixItemNum(_EnterRoomInfo.m_RoomIdx)
        -- 送啟動協定
        SendProtocol_007._041_002( _RoomData.m_Idx ,#_FixItemData  , _FixItemData)
    end
end

--- 進入區域效果
function TimeMachine_Controller.EnterAreaEffect(iRoomIdx)
    local _NowSelectArea = math.ceil( iRoomIdx / TimeMachineMgr.MaxRoomCountPerGroup)

    -- 不同區域才做動作
    if this.m_LastTimeSelectArea ~= _NowSelectArea then
        LeanTween.scale(this.m_RawImage_TimeMaChine , m_TimeMachine_ImageScale[_NowSelectArea], 0.6):setEase(LeanTweenType.easeOutQuint)
        if this.m_LastTimeSelectArea ~= 0 then
            this.m_Gobj_AreaImage[this.m_LastTimeSelectArea].gameObject:SetActive(false)
        end
        this.m_Gobj_AreaImage[_NowSelectArea].gameObject:SetActive(true)

        LeanTween.move(this.m_RawImage_TimeMaChine, m_TimeMachine_ImageAnchor[_NowSelectArea], 0.6):setEase(LeanTweenType.easeOutQuint)
        --切換區域需退出再進來
        this.m_UIAni_RoomPanel:DoActive(false)
        this.m_UIAni_RoomPanel:DoActive(true)
    end

    TimeMachine_Controller.SetMiddleAreaHint(true,_NowSelectArea)
    this.m_LastTimeSelectArea = _NowSelectArea
end

---重置區域效果(關介面時使用)
function TimeMachine_Controller.ResetEnterAreaEffect()
    this.m_RawImage_TimeMaChine.localScale = m_TimeMachine_ImageScale[0]
    this.m_RawImage_TimeMaChine.anchoredPosition3D = m_TimeMachine_ImageAnchor[0]
    if this.m_LastTimeSelectArea ~= 0 then
        this.m_Gobj_AreaImage[this.m_LastTimeSelectArea].gameObject:SetActive(false)
    end
    this.m_UIAni_RoomPanel:DoActive(false)
end

function TimeMachine_Controller.ClearRoomTexture()
    for key, value in pairs(m_RoomTextureTable) do
        TextureMgr.DestoryObject(value.m_Name)
        m_RoomTextureTable[key] = nil
    end
end

--- 設定中央提示文字
---@param iIsInRoom boolean 是否在艙室中
---@param iAreaIdx number 所在區域編號
function TimeMachine_Controller.SetMiddleAreaHint(iIsInRoom,iAreaIdx)
    if iIsInRoom then
        _AreaHint_Middle[0].m_Image_Light.color = _AreaHint_Middle[iAreaIdx].m_Image_Light.color
        _AreaHint_Middle[0].m_TMP_AreaName.text = _AreaHint_Middle[iAreaIdx].m_TMP_AreaName.text
        _AreaHint_Middle[0].m_TMP_AreaRepair_Title.text = _AreaHint_Middle[iAreaIdx].m_TMP_AreaRepair_Title.text
        _AreaHint_Middle[0].m_TMP_AreaRepair_Rate.text = _AreaHint_Middle[iAreaIdx].m_TMP_AreaRepair_Rate.text
    end

    _AreaHint_Middle[0].m_Trans_MiddleArea.gameObject:SetActive(iIsInRoom)
    for i = 1, this.m_AreaCount do
        _AreaHint_Middle[i].m_Trans_MiddleArea.gameObject:SetActive(not iIsInRoom)
    end
end
