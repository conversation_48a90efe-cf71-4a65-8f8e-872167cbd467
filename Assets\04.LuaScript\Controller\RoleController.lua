---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Controller/ModelController")
require("Controller/MoveController")
require("Controller/NPCEventController")
require("Logic/HUD/HUDController")
require("Controller/StateController")
require("Controller/GhostController")

---角色控制
---@class RoleController
---author 默默
---version 1.0
---since [ProjectBase] 0.1
---date 2022.5.24
RoleController = RoleController or {}

RoleController.PLAYER_BASE_POINT_NAME = "PlayerBasePoint"
RoleController.LVUP_AUDIO_ID = 15018

--region 敵我關係結構

---玩家PK狀態
---@class EPKState
EPKState = {
    Blue = 1,   -- 普通人(威望值正的 or 30000 ~ 60000)
    Red = 2,    -- 惡人(威望值負的 or 0 ~ 29999，不是紅名，會是紫名!!!)
    Purple = 3  -- 現行犯(剛揍過沒開 PK 的玩家，紫名!!!)
}

---敵我關係類型
---@class ERelatType
ERelatType = {
    --- 隊友
    Teammate = 1,
    --- 幫會
    Guild = 2,
    --- 陣營
    Alliance = 3,
    --- 情侶
    Couple = 4,
    --- 好友
    Friend = 5,
    --- 敵人
    Enemy = 6
}

---與玩家關係
---@class RelationshipState
local m_Relation = {
    [ERelatType.Teammate] = false, -- 隊友
    [ERelatType.Guild] = false,    -- 幫會
    [ERelatType.Alliance] = false, -- 陣營
    [ERelatType.Couple] = false,   -- 情侶
    [ERelatType.Friend] = false,   -- 好友
    [ERelatType.Enemy] = false     -- 敵人
}
--endregion

---@class ERoleType Role 種類
ERoleType = {
    None = 0,
    Self = 1,
    OtherPlayer = 2,
    NPC = 3
}

---@class EEmotionParam EmotionParam 其他表情動作(51 ~ 70)
EEmotionParam =
{
    [51] = "Die",
    [52] = "BattleState",
    [53] = "TurnAroundFade",
    [54] = "WalkFadeIn",
    [55] = "TeleportShow",
    [56] = "TeleportLeave",

}

---@param iGObj GameObject
---@param iRoleCreateData RoleCreateData
function RoleController.New(iGObj, iRoleCreateData, iAllDoneCallBack)
    ---@type RoleController
    local _RC = {}
    setmetatable(_RC, {__index = RoleController})

    _RC.m_PlayerID = iRoleCreateData.m_RoleID
    --寵物的話這個是他的表ID
    _RC.m_NPCID = iRoleCreateData.m_NPCID
    _RC.m_SID = iRoleCreateData.m_NPCID and iRoleCreateData.m_SID or iRoleCreateData.m_RoleID

    if _RC.m_NPCID == nil then
        _RC.m_IsSelf = _RC.m_PlayerID == PlayerData.GetRoleID()
    end

    _RC:SetRoleCreateData(iRoleCreateData)

    --region 作為物件的RoleController
    _RC.gameObject = iGObj
    _RC.transform = iGObj.transform
    _RC.transform.localPosition = iRoleCreateData.m_LocalPos
    _RC.transform.localRotation = Vector3.zero
    _RC.transform.localScale = Vector3.one
    if iGObj.transform:Find("ModelObject") then
        GameObject.Destroy(iGObj.transform:Find("ModelObject").gameObject)
    end
    _RC.m_ModelObject = GameObject.New("ModelObject")
    _RC.m_ModelObject.transform:SetParent(_RC.transform)
    _RC.m_ModelObject.transform.localPosition = Vector3.zero
    _RC.m_ModelObject.transform.localScale = Vector3.one
    _RC.m_ModelObject.transform.localRotation = iRoleCreateData.m_LocalRot
    --_RC.m_isModelVisible = true

    --endregion

    --region 玩家的其他資料
    ---@type RelationshipState
    _RC.m_Relation = {}
    for k,v in pairs(m_Relation) do
        _RC.m_Relation[k] = v
    end
    --endregion

    --region 包含在roleController的各種controller
    ---@type MoveController
    _RC.m_MoveController = MoveController:New(_RC)
    _RC.m_MoveController:SetPosition(iRoleCreateData.m_LocalPos)

    --預計取.m_ModelController時會要確認是否有m_ModelController
    --將會改寫成即時給-可能除了玩家類的吧

    _RC.m_StateController = StateController:New(iGObj, _RC)

    --endregion

    if _RC.m_IsSelf then
        -- 務必務必!! 讓系統知道這個是玩家本人
        _RC.gameObject.layer = Layer.PlayerSelf

        --登記觀察者 僅對玩家本人的 RoleConroller有作用
        GStateObserverManager.Register(EStateObserver.PlayerLevelRefresh, _RC)
        _RC.m_LV = PlayerData.GetLv()

        ResourceMgr.Load(RoleController.PLAYER_BASE_POINT_NAME,
                function( iAsset )
                    if iAsset ~= nil then
                        _RC.m_PlayerBasePoint = PlayerBasePoint.New(iAsset, true)
                        _RC.m_PlayerBasePoint.transform.gameObject:SetActive(false)

                        _RC.m_PlayerBasePoint.transform:SetParent(_RC.gameObject.transform)
                        _RC.m_PlayerBasePoint.transform.localPosition = Vector3.zero
                        if _RC.m_PlayerBasePoint.m_BattleTestObj ~= nil then
                            _RC.m_PlayerBasePoint.m_BattleTestObj = _RC.m_PlayerBasePoint.m_BattleTestObj.transform

                            if ProjectMgr.IsRelease() then
                                GameObject.Destroy(_RC.m_PlayerBasePoint.m_BattleTestObj.gameObject)
                            else
                                _RC.m_PlayerBasePoint.m_BattleTestObj:SetParent(RoleMgr.m_RoleParent)
                                _RC.m_PlayerBasePoint.m_SkillPos = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "Skill_C" ).transform
                                _RC.m_PlayerBasePoint.m_MovePos = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "MovePos_C" ).transform
                                _RC.m_PlayerBasePoint.m_SkillPos_S = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "Skill_S" ).transform
                                _RC.m_PlayerBasePoint.m_MovePos_S = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "MovePos_S" ).transform
                                _RC.m_PlayerBasePoint.m_TickPos = {}
                                _RC.m_PlayerBasePoint.m_TickPos_S = {}
                                for i = 1, 8 do
                                    _RC.m_PlayerBasePoint.m_TickPos[i] = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "Tick_" .. i .. "_C").transform
                                    _RC.m_PlayerBasePoint.m_TickPos_S[i] = _RC.m_PlayerBasePoint.m_BattleTestObj:Find( "Tick_" .. i .. "_S").transform
                                end
                            end
                        end

                    end
                end)
    else
        --CullingGroup
        local _Position = _RC.transform.position
        _Position.y = _Position.y + (_RC.m_Height * 0.5)
        -- 給預設 CullingState(註冊後會自動更新狀態)
        _RC.m_CurrentCulling = ECullingState.OUT_OF_RANGE
        --註冊並分配一個 BoundingSphere 給 RoleController
        _RC.m_BoundingSphereIdx = CullingGroupMgr.Inst:RegisterBoundingSphere(_RC, _Position, _RC.m_Radius)
        -- 更新 BoundingSphere 位置
        CullingGroupMgr.Inst:UpdateBoundingSphere(_RC.m_BoundingSphereIdx, _RC.transform)
    end

    if iRoleCreateData.m_AppearData ~= nil and _RC.m_IsSelf then
        AppearanceMgr.SetPlayerModel(_RC)
    end

    if iRoleCreateData.m_HUDData.m_CurHp <= 0 or iRoleCreateData.m_SocialStatus == ESocialStatus.Dead then
        _RC.m_StateController:ChangeState(EStateType.Dead)
    end

    -- HUD
    _RC.m_HUDController = HUDController.New(_RC.transform, iRoleCreateData.m_HUDData, _RC)
    _RC:HUDCallBack()
    _RC.m_HUDController:SetHeightAndScale(_RC.m_Height, _RC.m_OriginScale)
    _RC.m_HUDController:SetStateController(_RC.m_StateController)

    _RC.m_GhostController = GhostController:New(_RC)

    ---累計保留刷新次數
    _RC.m_UpdateDeltaCount = 0
    ---累計保留刷新時間
    _RC.m_UpdateDeltaTime = 0


    -- 受擊相關
    _RC.m_AffectData = {}
    _RC.m_AffectData.m_CurrentHitEffect = EHitState.NONE

    ---完完全全產出完畢成功要callBack
    if iAllDoneCallBack then
        iAllDoneCallBack(_RC)
    end

    return _RC
end

--- 戰鬥編輯器用 RC
function RoleController.New_Editor(iGObj)
    ---@type RoleController
    local _EditorRC = BattleMgr.GetBE_RC()
    if _EditorRC then
        _EditorRC.gameObject.transform.position = iGObj.transform.position
        _EditorRC.gameObject.transform:SetParent(iGObj.transform.parent)
        _EditorRC.m_ModelController.transform = iGObj.transform
        _EditorRC.m_ModelController.m_ModelObject = iGObj

        _EditorRC.m_ModelController.m_ModelObject.layer = Layer.NPC
        _EditorRC.m_ModelController.m_ModelObject.tag = "NPC"

        iGObj.transform:SetParent(_EditorRC.gameObject.transform)
        iGObj.transform.localPosition = Vector3.zero
        iGObj.transform.localScale = Vector3.one

        for i = 0, _EditorRC.m_ModelController.transform.childCount - 1 do
            local _childLOD = _EditorRC.m_ModelController.transform:GetChild(i)
            _childLOD.gameObject.layer = Layer.NPC
        end

        _EditorRC.m_ModelController.m_Renderer = _EditorRC.m_ModelController.m_ModelObject:GetComponentsInChildren(typeof(UnityEngine.Renderer))
        _EditorRC.m_ModelController.m_SkinnedMeshRenderer =  iGObj:GetComponentsInChildren(typeof(SkinnedMeshRenderer))
        _EditorRC.m_ModelController.m_Animator = iGObj:GetComponent("Animator")

        return _EditorRC
    end

    ---@type RoleController
    local _RC = {}
    setmetatable(_RC, {__index = RoleController})

    _RC.gameObject = GameObject.New("測試用模型")
    _RC.transform = _RC.gameObject.transform
    _RC.transform.localScale = Vector3.one
    _RC.gameObject.transform.position = iGObj.transform.position
    _RC.gameObject.transform:SetParent(iGObj.transform.parent)

    if _RC.gameObject.transform:Find("ModelObject") then
        GameObject.Destroy(_RC.gameObject.transform:Find("ModelObject").gameObject)
    end
    _RC.m_ModelObject = GameObject.New("ModelObject")
    _RC.m_ModelObject.transform:SetParent(_RC.gameObject.transform)
    _RC.m_ModelObject.transform.localPosition = Vector3.zero
    _RC.m_ModelObject.transform.localScale = Vector3.one

    _RC.m_IsSelf = false
    _RC.m_Height = 2
    _RC.m_Radius = 0.5

    _RC.m_TalkAction =
    {
        m_TalkMode = 0,
        m_NormalTalkID = 0,
        m_FightTalkID = 0
    }

    iGObj.transform:SetParent(_RC.gameObject.transform)
    iGObj.transform.localPosition = Vector3.zero
    iGObj.transform.localScale = Vector3.one

    ---@type MoveController
    _RC.m_MoveController = MoveController:New(_RC)

    --region 玩家的其他資料
    ---@type RelationshipState
    _RC.m_Relation = {}
    for k,v in pairs(m_Relation) do
        _RC.m_Relation[k] = v
    end
    --endregion

    -- 假 HUD Data
    local _HUD_Data = {}
    _HUD_Data.m_Name = "測試用_受擊模型"
    _HUD_Data.m_MaxHp = 1
    _HUD_Data.m_CurHp = 1
    _HUD_Data.m_CurShield = 0
    _HUD_Data.m_SID = "測試用"
    _RC.m_HUDController = HUDController.New(_RC.transform, _HUD_Data, _RC)
    _RC:HUDCallBack()
    _RC.m_HUDController.m_HUDActive = true
    _RC.m_HUDController:SetHeightAndScale(_RC.m_Height, 1)

    --- Debug 辨識用名稱
    _RC.m_DebugIdentifyName = _HUD_Data.m_Name

    ---Role 種類
    _RC.m_RoleType = ERoleType.NPC
    _RC.m_SkillActKind = SkillActData.iKind.Npc
    _RC.m_ModelController = ModelController:NewNPC_Editor(_RC, iGObj, _RC.m_DebugIdentifyName)
    _RC.m_StateController = StateController:New(_RC.gameObject, _RC, _RC.m_ModelController)
    _RC.m_HUDController:SetStateController(_RC.m_StateController)

    -- 受擊相關
    _RC.m_AffectData = {}
    _RC.m_AffectData.m_CurrentHitEffect = EHitState.NONE

    local _Position = _RC.transform.position
    _Position.y = _Position.y + (_RC.m_Height * 0.5)
    -- 給預設 CullingState，強制顯示
    _RC.m_CurrentCulling = ECullingState.IN_NEARBY_RANGE
    _RC.m_CullingEvent = {
        isVisible = true,
        previousDistance = ECullingState.IN_NEARBY_RANGE
    }

    --不註冊 BoundingSphere，編輯功能不加入 Culling
    --_RC.m_BoundingSphereIdx = CullingGroupMgr.Inst:RegisterBoundingSphere(_RC, _Position, _RC.m_Radius)
    -- 更新 BoundingSphere 位置
    --CullingGroupMgr.Inst:UpdateBoundingSphere(_RC.m_BoundingSphereIdx, _Position)
    ---累計保留刷新次數
    _RC.m_UpdateDeltaCount = 0
    ---累計保留刷新時間
    _RC.m_UpdateDeltaTime = 0

    BattleMgr.SetBE_RC(_RC)

    return _RC
end

---給已產出的RC新的RoleCreateData
---@param iRoleCreateData RoleCreateData
function RoleController:SetRoleCreateData(iRoleCreateData)

    --region RoleCreateData 相關的資料
    self.m_Lv = iRoleCreateData.m_Lv
    self.m_Height = iRoleCreateData:GetRealHeight()
    self.m_Radius = iRoleCreateData:GetRadius()
    self.m_Troops = iRoleCreateData.m_Troops
    self.m_OriginScale = iRoleCreateData:GetScale()
    --- Debug 辨識用名稱
    self.m_DebugIdentifyName = tostring(iRoleCreateData.m_HUDData.m_SID) .. "_" .. iRoleCreateData.m_HUDData.m_Name

    --self.m_PKState = iRoleCreateData.m_PKState
    if self.m_NPCID then
        self.m_RoleType = ERoleType.NPC
        if iRoleCreateData.m_NPCMode == EventNPCMode.CopyPlayer then
            ---@type table PK 相關，玩家本人
            self.m_PKInfo = {}
            ---@type boolean PK 開關
            self.m_PKInfo.m_PKEnable = iRoleCreateData.m_PKEnable
            ---@type EPKSwichStatus PK 開關狀態
            self.m_PKInfo.m_PKState = iRoleCreateData.m_PKState
        end
    else
        ---Role 種類
        self.m_RoleType = self.m_IsSelf and ERoleType.Self or ERoleType.OtherPlayer

        ---@type table PK 相關，玩家本人
        self.m_PKInfo = {}
        ---@type boolean PK 開關
        self.m_PKInfo.m_PKEnable = iRoleCreateData.m_PKEnable
        ---@type EPKSwichStatus PK 開關狀態
        self.m_PKInfo.m_PKState = iRoleCreateData.m_PKState
    end

    if iRoleCreateData.m_AppearData ~= nil then
        self.AppearData = iRoleCreateData.m_AppearData
        self.m_Enhance = iRoleCreateData.m_Enhance
        self.m_SkillActKind = iRoleCreateData.m_AppearData.m_Gender == EPlayerGender.Man and SkillActData.iKind.Man or SkillActData.iKind.Woman
        --self.m_IsModelUpdateComplete = false
    elseif iRoleCreateData.m_NPCAppearData ~= nil then
        ---Role 種類
        self.AppearData = iRoleCreateData.m_NPCAppearData
        self.m_SkillActKind = SkillActData.iKind.Npc
    end

    ---2022.07.29 Add by KK 寫入NPC使用的數值, 先存這三個後續有需要要改寫成結構
    -- iRC.m_SerialNo = iEventNPCPlantData.m_SerialNo
    -- iRC.m_EventState = iEventNPCPlantData.m_EventState
    self.m_NPCMode = iRoleCreateData.m_NPCMode
    ---NPCEvent
    if iRoleCreateData.m_EventID ~= nil then
        self.m_NPCEventController = iRoleCreateData.m_NPCEventController
    end
    --endregion

    self.m_IsFirstShowType = iRoleCreateData.m_IsFirstShowType

end

--region 淡入淡出
---@class RoleController.FadeType 淡入淡出種類
RoleController.FadeType =
{
    None = "None",
    FadeIn = "FadeIn",
    FadeOut = "FadeOut"
}

--- 執行模型淡入
function RoleController:ModelFadeIn()
    self.m_FadeType = RoleController.FadeType.FadeIn
    self:GetModelController():StartFade(self.m_FadeType)
end

--- 執行模型淡出
function RoleController:ModelFadeOut()
    self.m_FadeType = RoleController.FadeType.FadeOut
    self:GetModelController():StartFade(self.m_FadeType)
end
--endregion 淡入淡出

--- 釋放/回收
function RoleController:IsRelease(iRelease)
    if self.m_PlayerID and self.m_ModelController then
        if iRelease then
            self.m_ModelController:IsRelease()
        else
            AppearanceMgr.StoreModel(self.m_PlayerID, self.m_ModelController)
        end
        self.m_ModelController:SetRoleController()
        self.m_ModelController = nil
    end

    self:ReleaseHUD()
    --self:GetModelController():IsRelease()
end

function RoleController:GetName()
    if self.m_HUDController and self.m_HUDController.m_HUDData then
        return self.m_HUDController.m_HUDData.m_Name
    end
    return self.m_DebugIdentifyName
end

---@param iVec3 Vector3
function RoleController:SetPosition(iVec3)
    if self.m_MoveController ~= nil then
        self.m_MoveController:SetPosition(iVec3)
    else
        self.transform.localPosition = iVec3
    end

    if self.m_BoundingSphereIdx then
        CullingGroupMgr.Inst:UpdateBoundingSphere(self.m_BoundingSphereIdx, self.transform)
    end
end

--- 玩家(非自己) 與 NPC 用
---@param iVe3_Start Vector3 起始點
---@param iVec3_End Vector3 送來的終點
function RoleController:MoveToTargetPos(iVe3_Start, iVec3_End, iSpeed)
    if self.m_MoveController ~= nil --[[and not self.m_IsCulling]] then
        if iSpeed then
            self:UpdateMoveSpeed(iSpeed)
        end
        --其他玩家只有跟5-1停點不同(一點誤差都不行)才需要停
        if not self.m_MoveController.m_PredictPos:Equals(iVec3_End) then
            self.m_MoveController:PathFindingPToP(iVe3_Start, iVec3_End)
        end
    else
        self.transform.localPosition = iVec3_End
        CullingGroupMgr.Inst:UpdateBoundingSphere(self.m_BoundingSphereIdx, self.transform)
    end

end

--- 玩家(非自己) 用 5-1 移動
function RoleController:OtherVectorMove(iEndServerPos, iSpeed)
    if self.m_IsSelf then
        --就說不是自己用了還皮
        return
    end
    self:UpdateMoveSpeed(iSpeed)

    local _NewEnd = Vector3.zero
    _NewEnd:Copy(iEndServerPos)

    self.m_MoveController.m_PredictPos = iEndServerPos

    local _Vector = iEndServerPos - self.transform.position
    _Vector.y = 0
    _Vector:SetNormalize()

    if self.m_RoleType ~= ERoleType.NPC then
        ---其他玩家位移延伸兩倍距離
        _NewEnd = self.transform.position + ( _Vector * self.m_MoveController.m_HorizontalSpeed * 2)
    end

    self.m_MoveController:PathFindingPToP(self.transform.position, _NewEnd)

end

---取模型 Rotation
---@return Vector3 EulerAngles
function RoleController:GetlocalEulerAngle()
    local _Euler = GFunction.GetTransformLocalEulerAngles(self.m_ModelObject.transform)
    if self.m_NextRotation == nil then
        return _Euler
    else
        _Euler.x = 0
        _Euler.y = self.m_NextRotation
        _Euler.z = 0

        return _Euler
    end
end

function RoleController:SetRotation(iValue)
    self.m_NextRotation = nil
    if self.m_MoveController and not Extension.IsUnityObjectNull(self.m_ModelObject) then
        local _FaceVal = iValue
        if type(iValue) == "table" then
            _FaceVal = 180/math.pi * math.atan2(iValue.x, iValue.z)
        end
        local _y = self.m_ModelObject.transform.localEulerAngles.y

        _FaceVal = _FaceVal % 360

        --基本上 y<0 都是超小值
        if _y < 0 then
            _y = 0
        end
        _FaceVal = LeanTween.closestRot(_y, _FaceVal)

        self.m_NextRotation = _FaceVal
        -- LeanTween.rotateY(self.m_ModelObject , _FaceVal, 0.13):setOnComplete(
        --     System.Action(function()
        --         self.m_NextRotation = nil
        --     end))
    end
end

---取轉向物件
---@return Vector3 EulerAngles
function RoleController:GetRotationObj()
    return self.m_ModelObject.transform
end

function RoleController:SetLookAt(iValue)
    if iValue then
        local _Rotation = self.transform.localPosition - iValue

        self:SetRotation(_Rotation * - 1)

        --self.m_ModelObject.transform:LookAt(iValue)
    end
end

---取ModelController
---@return ModelController
function RoleController:GetModelController()
    if self.m_ModelController then
        return self.m_ModelController
    else
        --D.Log("RoleController沒有ModelController: "..  self:GetName())
        return AppearanceMgr.GetDefaultModel()
    end
end

---Culling Event 觸發
---由 CullingGroup 自動呼叫
---@param iState ECullingState
---@param iEvent CullingGroupEvent
function RoleController:SetCulling(iState, iEvent)

    self.m_CurrentCulling = iState
    if iEvent ~= nil then
        self.m_CullingEvent = iEvent
        if iEvent.isVisible and self.m_ModelController == nil then
            self.m_isModelVisible = true

            if self.m_PlayerID then
                self.m_ModelController = AppearanceMgr.New(self.AppearData, self.m_PlayerID, true, self.m_Enhance, Layer.Player,
                function(iMC)
                    if self.m_isModelVisible and (self.m_ModelController == nil or self.m_ModelController == iMC) then
                        AppearanceMgr.PlaceNewModel(self, iMC)

                    end
                end)

                self.m_ModelController:SetRoleController(self)

            else
                self.m_ModelController = AppearanceMgr.New(self.AppearData, self.m_NPCID, false, nil, Layer.NPC,
            function(iMC)
                    if self.m_isModelVisible and (self.m_ModelController == nil or self.m_ModelController == iMC) then
                        AppearanceMgr.PlaceNewModel(self, iMC)
                    end
                end)
                self.m_ModelController:SetRoleController(self)
            end
        elseif self.m_CurrentCulling > ECullingState.IN_NEARBY_RANGE and not iEvent.isVisible then
            self.m_isModelVisible = false
            if self.m_ModelController then
                if self.m_PlayerID then
                    AppearanceMgr.StoreModel(self.m_PlayerID, self.m_ModelController)
                    self.m_ModelController:SetRoleController()
                    self.m_ModelController = nil
                else
                    ---檢查是否存在NPC長駐特效 存在的話歸還特效
                    EffectMgr.ReturnNPCLongTimeEffect(self.m_ModelController.m_PlayingEffectTable,self.AppearData.m_DebutEffectID)
                    AppearanceMgr.StoreModel(self.m_NPCID, self.m_ModelController)
                    self.m_ModelController:SetRoleController()
                    self.m_ModelController = nil
                end
            end
        end
    end

    if self.m_PlayerID then
        RoleMgr.m_ActiveCullingTable[self.m_BoundingSphereIdx] = self
    else
        NPCMgr.m_ActiveCullingTable[self.m_BoundingSphereIdx] = self
    end
end

function RoleController:ChangeScale(iScale)
    -- 模型縮放
    self:GetModelController():SetModelSize(iScale, true)
    -- HUD
    self.m_HUDController:SetHeightAndScale(self.m_Height * iScale, iScale, true)

end

--- 檢查有沒有在指定範圍以內
---@param iCullgingState ECullingState
---@param iNeedVisible boolean 是否需要處於顯示狀態
---@return boolean
function RoleController:IsInRange(iCullgingState, iNeedVisible)
    if self.m_IsSelf then
        return true
    end

    if iNeedVisible then
        return self.m_CurrentCulling <= iCullgingState and self.m_CullingEvent.isVisible
    end

    return self.m_CurrentCulling <= iCullgingState
end

function RoleController:OnClick()
    if self.m_Relation[ERelatType.Enemy] then
        --未戰鬥的話則開啟自動戰鬥
        if not BattleMgr.m_IsAutoNormalBattle then
            --this.m_InterActTarget = nil
            BattleMgr.SetAutoNormalBattle(true, true)
        end
        return
    end

    if self.m_NPCEventController ~= nil then
        self.m_NPCEventController:OnClick()
    end
end

---是否能移動(這裡只是接口)
function RoleController:CanMove()
    if self.m_MoveController then
        return self.m_MoveController:CanMove()
    end
    return false
end

--region Update

function RoleController:Update()

    --依照距離，以m_CurrentCulling作為2的指數決定刷新率
    if self.m_CurrentCulling and self.m_UpdateDeltaCount < (2 ^ (self.m_CurrentCulling)) then
        self.m_UpdateDeltaCount = self.m_UpdateDeltaCount + 1
        self.m_UpdateDeltaTime = self.m_UpdateDeltaTime + HEMTimeMgr.m_DeltaTime
        return
    elseif self.m_CurrentCulling == nil then
        --自己的話 self.m_CurrentCulling == nil
        self.m_UpdateDeltaCount = 1
        self.m_UpdateDeltaTime = HEMTimeMgr.m_DeltaTime
    end

    if self.m_NextRotation and self.m_MoveController then
        local _tmp = self:GetRotationObj().localEulerAngles
        local minusWhole = 0 - (360 - self.m_NextRotation)
        local plusWhole = 360 + self.m_NextRotation
        local toDiffAbs = Mathf.Abs( self.m_NextRotation - _tmp.y )
        local minusDiff = Mathf.Abs(minusWhole- _tmp.y)
        local plusDiff = Mathf.Abs(plusWhole- _tmp.y)

        if toDiffAbs < 1 or minusDiff < 1 or plusDiff < 1 then
            self.m_NextRotation = nil
        else
            if self.m_NextRotation < 0 and _tmp.y > 180 then
                _tmp.y = _tmp.y - 360
            elseif self.m_NextRotation > 360 and _tmp.y < 180 then
                _tmp.y = _tmp.y + 360
            end
            local _euler = Mathf.Lerp(_tmp.y, self.m_NextRotation, ModelSetting.RotateAcceleration * Time.deltaTime)
            _tmp.y = _euler
            self:GetRotationObj().localEulerAngles = _tmp
        end

        setmetatable(_tmp, nil)
    end

    -- 狀態機更新
    if self.m_StateController then
        -- BattleMgr.ShowLog(self.transform.name, "green")
        self.m_StateController:Update()
    end

    -- 需顯示人物的範圍
    if self:IsInRange(ECullingState.IN_FARAWAY_RANGE, true) then
        -- HUD更新
        if self.m_HUDController and self.m_HUDController.m_HUDActive then
            self.m_HUDController:Update()
        end

        -- 受擊震動
        if self.m_AffectShake_Coroutine ~= nil then
            if coroutine.status(self.m_AffectShake_Coroutine) == "suspended" then
                coroutine.resume(self.m_AffectShake_Coroutine)
            elseif coroutine.status(self.m_AffectShake_Coroutine) == "dead" then
                self.m_AffectShake_Coroutine = nil
            end
        end

        -- 淡入淡出效果
        if self.m_FadeType and self.m_FadeType ~= RoleController.FadeType.None then
            local _MainCamera = CameraMgr.GetMainCamera()
            local _CullingRange = CameraMgr.GetCullingGroupSetting().RangeSetting
            local _isInRange, _Dis = GFunction.IsInRange_2D(_MainCamera.transform.position, self.transform.position, _CullingRange[ECullingState.IN_TRANSITION_RANGE +1])
            local _DisValue = Mathf.Clamp((_Dis - _CullingRange[ECullingState.IN_MEDIUM_RANGE +1]) /10, 0, 1)
            self:GetModelController():UpdateFade(_DisValue)

            -- 執行淡入，且已經完成淡入、或執行淡出，且完成淡出
            if (self.m_FadeType == RoleController.FadeType.FadeIn and _DisValue == 0) or
                    (self.m_FadeType == RoleController.FadeType.FadeOut and _DisValue == 1) then
                -- 重置狀態
                self:GetModelController():ResetFade(self.m_FadeType)
                self.m_FadeType = RoleController.FadeType.None
            end
        end

        -- 受擊輪廓狀態檢查
        --self:CheckHitEffect()
    end

    -- 殘影更新
    if self.m_GhostController then
        self.m_GhostController:Update()
    end

    -- 移動(位置)更新
    if self.m_MoveController then
        self.m_MoveController:Update()
    end

    --重刷新率
    if self.m_CurrentCulling and self.m_UpdateDeltaCount >= (2 ^ (self.m_CurrentCulling - 1)) then
        self.m_UpdateDeltaCount = 1
        self.m_UpdateDeltaTime = HEMTimeMgr.m_DeltaTime
    end
end

--- 遮擋狀態更新
--- 統整管理 CullingGroup 不同狀態對應的行為
--- 不在 SetCulling 中處理是因為 CallBack 與渲染幀不同步會導致物件閃爍
--- 透過 RoleMgr/NPCMgr 主動更新
function RoleController:CullingUpdate()

    -- if not self.m_HUDController then
    --     return false
    -- end

    -- 受隱藏的 NPC 什麼都看不到
    if self.m_BeSealed then
        self.gameObject:SetActive(false)
        self:SetHUDActive(false)
        return true
    elseif not self.gameObject.activeSelf then
        self.gameObject:SetActive(true)
    end

    local _HUDActive = false

    --region Culling隱藏與否相關
    -- 近範圍
    if self.m_CurrentCulling == ECullingState.IN_NEARBY_RANGE then
        -- 從可視變不可視
        if not self.m_CullingEvent.isVisible then
            if self.gameObject.activeSelf then
                --self.gameObject:SetActive(false)
            end
            _HUDActive = false
        -- 是可視狀態
        else
            if not self.gameObject.activeSelf then
                --self.gameObject:SetActive(true)
            end
            _HUDActive = true
        end

    -- 中範圍
    elseif self.m_CurrentCulling == ECullingState.IN_MEDIUM_RANGE then
        -- 從可視變不可視
        if not self.m_CullingEvent.isVisible then
            --self.gameObject:SetActive(false)
        -- 從不可視變可視
        else
            -- 這裡可能要加淡入
            if not self.gameObject.activeSelf then
                --self.gameObject:SetActive(true)
            end
        end
    elseif self.m_CurrentCulling == ECullingState.IN_TRANSITION_RANGE then
        -- 可視範圍
        if self.m_CullingEvent.isVisible then
            -- 從遠範圍過渡到中範圍
            if self.m_CullingEvent.previousDistance == ECullingState.IN_FARAWAY_RANGE then
                -- 淡入
                self:ModelFadeIn()
            -- 從中範圍過渡到遠範圍
            elseif self.m_CullingEvent.previousDistance == ECullingState.IN_MEDIUM_RANGE then
                -- 淡出
                self:ModelFadeOut()
            -- 同樣在過渡範圍變動
            elseif self.m_CullingEvent.previousDistance == ECullingState.IN_TRANSITION_RANGE then
                -- 淡入
                self:ModelFadeIn()
            end
        else
            --self.gameObject:SetActive(false)
        end

    -- 遠範圍
    elseif self.m_CurrentCulling == ECullingState.IN_FARAWAY_RANGE then
        if self.m_CullingEvent.isVisible then
            -- 淡入
            self:ModelFadeIn()
        end
        --self.gameObject:SetActive(false)
    -- 範圍外(基本上不會進這裡)
    elseif self.m_CurrentCulling == ECullingState.OUT_OF_RANGE then
        --self.gameObject:SetActive(false)
    else
        --self.gameObject:SetActive(false)
    end

    --endregion

    --蒐集用的NPC要看是否處於任務階段顯示名稱
    -- if not (SearchMgr.IsCollectableNPC(self) or self == SelectMgr.m_TargetController) then
    --     _HUDActive = false
    -- end

    self:SetHUDActive(_HUDActive)

    return true
end

-- ---更新模型
-- function RoleController:UpdateModel()
--     self.m_ModelController:UpdateBodyModel()
--     self.m_IsModelUpdateComplete = true

--     if self.m_IsSelf then
--         PlayerData.SetIsModelCreate(true)
--     else
--         CullingGroupMgr.Inst:UpdateBoundingSphere(self.m_BoundingSphereIdx, self.transform.position)
--         --self:UpdateNPCMotion(true)
--     end
--     --加入GhostEffect
--     self.m_GhostController = GhostController:New(self)
-- end

function RoleController:LateUpdate()
    if self.m_HUDController ~= nil then
        self.m_HUDController:LateUpdate()
    end
end

---泡泡框專用
function RoleController:UpdateNPCMotion(iIsEnable)
    if self.m_RoleType ~= ERoleType.NPC then
        return
    end

    if self.m_HUDController == nil then
        return
    end

    if iIsEnable then
        -- 泡泡框
        if self.m_HUDController.m_HUDActive then
            --BubbleMgr.StartBubbleRoutine(self.m_SID, self.m_TalkAction.m_NormalTalkID, self.m_TalkAction.m_TalkMode)
        end
        -- 任務提示

        -- 採集物特效

    else

    end
end

--endregion

--region 位移相關
function RoleController:UpdateMoveSpeed(iSpeed)
    self.m_MoveController:UpdateMoveSpeed(iSpeed)
end

--- 終止移動 自己不跑這裡
---@param iVec3 Vector3 Server給的最後位置
---@param iReason ESendMoveReason 終止移動的原因
function RoleController:EndMove(iVec3, iReason)
    iReason = iReason or ESendMoveReason.Non
    --不在範圍內直接更新座標 C#
    --if ( !isInRange )
    --{
    --    StopMove();
    --    UpdateLastPos( stopV3Pos );
    --    return;
    --}
        --坐騎乘客
    --if (IsRidingAsPassenger)
    --{
    --    return;
    --}

    -- 如果被剃除(不在畫面上)就直接更新座標
    if not self.m_IsSelf and not self:IsInRange(ECullingState.IN_TRANSITION_RANGE, true) then
        --self.StopMove(); -- TODO:想一下要寫在RoleController 還是 MoveController
    end

    --強制停止或破罡受擊、死亡 不論對象直接校正位置
    if iReason == ESendMoveReason.Forcibly or iReason == ESendMoveReason.ChangeSpeed or iReason == ESendMoveReason.OnHit then
        self.transform.position:GetFloorPosition()
        --- C#
        --m_IsAutoMoveToTargetPos = false;
        --MoveManager.Log( $"Stop Forcibly:{iReason}", "Info" );
        --StopMove( true, iReason == ESEndMoveReason.Forcibly );
    else
        if  self.m_IsSelf then --不處理自己的
            do
                return
            end
        end
        --CANDO:做其他人的後續處理 現在沒空用
    end
end

--- 更新最後的位置, 直接更新 transform.position
---@param iPos Vector3 要更新的值
function RoleController:UpdateLastPos(iPos)
    if self.m_IsSelf then
        self.m_MoveController.m_CharacterController.enabled = false
        self.transform.position:Copy(iPos)
        self.m_MoveController.m_CharacterController.enabled = true
    else
        self.transform.position:Copy(iPos)
    end
end

---被拉扯移動
function RoleController:ForceSkillMove(iForceSkillMoveData)
    local _originPos = GFunction.ServerPosToScenePos(iForceSkillMoveData.FromX, iForceSkillMoveData.FromY)
    local _Position = GFunction.ServerPosToScenePos(iForceSkillMoveData.ToX, iForceSkillMoveData.ToY)
    local _distance = Vector3.Distance(_originPos, _Position)
    local _duration = _distance / iForceSkillMoveData.Speed

    if self.m_IsSelf then
        MoveMgr.ResetStopPos(_Position)
    else
        self.m_MoveController:StopMove(true)
    end

    --MoveMgr.DebugLog(self.m_DebugIdentifyName.." 被技能移動，從 x: "..iForceSkillMoveData.FromX.."y: "..iForceSkillMoveData.FromY)
    --MoveMgr.DebugLog("被技能移動，到 x: "..iForceSkillMoveData.ToX.."y: "..iForceSkillMoveData.ToY)

    self.m_MoveController:SkillMove(_Position, _duration, true)
end

---戰鬥中的位移動作
function RoleController:SkillMove(iPos, iTime)
    self.m_MoveController:SkillMove(iPos, iTime)
    PetMgr.SendPetMovement(self.m_PlayerID, iPos)
end

--endregion 位移相關

---傳送特效
function RoleController:TeleportEffect(isShow)

    local _EffectData = EffectData.New()
    _EffectData.m_ID = isShow and EffectSetting.m_TeleportInParticle or EffectSetting.m_TeleportOutParticle
    _EffectData.m_BonePos = EBoneIdx.Foot
    --播特效
    self:SetEffect(_EffectData)
    self:GetModelController():ResetAnimator(true)
    self:GetModelController():StartScan(isShow)

    if isShow then
        self:GetModelController():SetAniTrigger(AnimationHash.Trigger_Breath)
    end
end

---特效播放設定
---@param iEffectID ushort 特效ID
---@param iBone EBoneIdx
---@param iLookAtTransform Transform 面向位置
function RoleController:SetEffect(iEffectData)
    -- if --[[self.m_IsShowEffect]] false then
    --     return
    -- end

    iEffectData.m_isLoop = false

    if self.m_ModelController then
        self:GetModelController():SetEffect(iEffectData)
    end
end

function RoleController:SetSpecialEffect(iEffectData, iSpecialTick, iStackCount)
    if self.m_ModelController then
        local _TempSkillData = SkillData.GetSkillDataByIdx(iSpecialTick)
        self:GetModelController():SetLoop2Effect(iEffectData, _TempSkillData, iStackCount)
    end
end

---@param iValueKind EValueKind
function RoleController:AffectShake(iValueKind)
    -- 如果是玩家就不抖
    if self.m_RoleType ~= ERoleType.NPC then
        return
    end

    -- 還沒生好 ModelController
    if not self.m_ModelController then
        return
    end

    -- 沒有啟用 AffectShakeScrObj.IsUse
    if not BattleMgr.m_AffectShakeScrObj.m_IsUse then
        return
    end

    self.m_AffectShake_Coroutine = coroutine.create(function()
        self:GetModelController():ShakeEffect(iValueKind)
    end)
end

---受擊輪廓效果
---@param iHitState EHitState
---@param iEndTime number 結束時間
function RoleController:SetHitEffect(iHitState, iEndTime)
    if iHitState == EHitState.FADING or iHitState == EHitState.NONE then
        return
    end

    -- 當前閃受擊的權重較高
    if iHitState < self.m_AffectData.m_CurrentHitEffect then
        return
    end

    -- 存下資訊
    self.m_AffectData.m_CurrentHitEffect = iHitState
    HEMTimeMgr.DoFunctionDelay(iEndTime, function()
        self:ResetHitEffect()
    end )

    -- 啟動受擊渲染
    self:GetModelController():SetHitEffect(iHitState)
end


--region 與模型相關的函數們

---檢查受擊渲染
function RoleController:ResetHitEffect()
    if self.m_AffectData.m_CurrentHitEffect == EHitState.NONE then
        return
    end

    self:GetModelController():ResetHitEffect()
    self.m_AffectData.m_CurrentHitEffect = EHitState.NONE
end

function RoleController:UpdateWeapon(iWeaponType)
    if self.m_StateController:GetStateType() == EStateType.Move then
        if self.m_IsSelf then
            MoveMgr.PlayerStopFirst(true)
        else
            self.m_MoveController:StopMove(true)
        end
    end

    --self.AppearData.m_WeaponType = iWeaponType

    --self:GetModelController():UpdateWeapon(iWeaponType)

    --self:GetModelController():SwitchWeaponAni()

    if self.m_IsSelf then
        HotKeyMgr.RefreshMainHotkey()
    end
end

---更新強化資料
function RoleController:UpdateEnhance(iPos, iGrowTime, iRarity)
    --self:GetModelController():UpdateEnhance(iPos, iGrowTime, iRarity)
    self.m_IsModelUpdateComplete = false
    local _sEnhanceData = self.m_Enhance:GetEquipEnhance(iPos)
    if _sEnhanceData then
        _sEnhanceData.m_Index = iGrowTime
        _sEnhanceData.m_Rarity = iRarity
    else
        D.LogError("No Weapon")
        return
    end
end

function RoleController:UpdateBody()
    if self.m_StateController:GetStateType() == EStateType.Move then
        self.m_MoveController:StopMove(true)
    end

    --self:GetModelController():UpdateModel(false)
end

---玩家是否還在核心技狀態中
function RoleController:SetBattleSuitEffect(isInBuff)
    self.AppearData.m_ActiveBattleSuit = isInBuff

    if isInBuff then
        self:GetModelController():StartBattleSuitEffect()
    else
        self:GetModelController():ResetBattleSuitEffect(true)
    end
end

--endregion

--- 接收 Tick 協定並播放動作
function RoleController:UseSkill(iData)
    if self.m_StateController:IsDead() then
        do return end
    end

    if self.m_IsSelf then
        -- 喊招

        -- 重設預按回普攻
        BattleMgr.ResetPreUseSkillId()
        --BattleMgr.m_SkillTimeCount = 0
    end

    local _Result, _SkillData, _SkillActData = GFunction.CheckSkillData(self.m_SkillActKind, iData.m_SkillId)
    if not _Result then
        _Result = false
    else
        --不是自己的話要先停
        if not self.m_IsSelf then
            self.m_MoveController:StopMove(true)
        end

        self.m_NowPlayingSkillActData = PlayingSkillActData.New(_SkillData, _SkillActData, function() self:OnEnd() end, iData.m_SId)

        --- 設定攻擊速度
        --- 普攻才會被攻速影響，其他技能不會
        if self.m_NowPlayingSkillActData.m_SkillData == nil or self.m_NowPlayingSkillActData.m_SkillData.m_SkillType ~= EMainHotKeyNum.NormalAtk then
            self.m_AtkSpeed = 1
        else
            self.m_AtkSpeed = iData.m_Speed
        end

        -- 計時用的
        self.m_NowPlayingSkillActData.m_SkillTimeCount = 0
        -- 結束時間用的
        self.m_NowPlayingSkillActData.m_SkillEndTime = self.m_NowPlayingSkillActData.m_SkillData:GetSkillTime() / self.m_AtkSpeed

        -- 由這裡算出真終點
        self:SetSkillMoveData(iData.m_Pos, iData.m_Distance)

        self:SetRotation(iData.m_Direction)
        self:PlayAction()

        -- 施放非普攻技能會讓普攻重置
        if self.m_IsSelf then
            if _SkillData.m_UseType ~= EHotKeyUseKind.Loop_1 then
                HotKeyUnit.CancelNormalSkill()
                self.m_PlayerBasePoint.transform.gameObject:SetActive(false)
            else
                HotKeyUnit.NextNormalSkill()
            end

            --- 特殊循環式技能需要改變 UI 顯示及技能資料
            if _SkillData.m_UseType == EHotKeyUseKind.Loop_2 then
                HotKeyMgr.ChangeSkillByLoop2( PlayerData.GetWeaponType(), _SkillData.m_SkillType, _SkillData.m_SkillOrderIdx )
            end
        end
    end
end

--- 使用蓄力技能
function RoleController:UsePowerSkill(iIdx)
    local _result = false
    if iIdx == 0 then
        _result = false
    end

    local _result, _SkillData, _SkillActData = GFunction.CheckSkillData(self.m_SkillActKind, iIdx)

    if _result then
        -- 蓄力先停止移動
        -- self.m_MoveController:StopMove(true)
        if self.m_IsSelf == true then
            BattleMgr.m_NowPowerTime = HEMTimeMgr.time
        end

        self.m_NowPlayingSkillActData = PlayingSkillActData.New(_SkillData, _SkillActData, function() self:OnEnd() end)
        -- 設定攻擊速度
        self.m_AtkSpeed = 1
        -- 除玩家以外的模型 計時用的
        self.m_NowPlayingSkillActData.m_SkillTimeCount = 0
        -- 除玩家以外的模型 結束時間用的
        self.m_NowPlayingSkillActData.m_SkillEndTime = self.m_NowPlayingSkillActData.m_SkillData:GetSkillTime()

        if SelectMgr.m_TargetGObj then
            self:SetLookAt(SelectMgr.m_TargetGObj.transform.position)
        end

        self:PlayAction()
    end

    if not self.m_PlayingPowerAlert then
        self.m_PlayingPowerAlert = {}
    end

    -- 警戒框演出，因為S端不會送Tick過來，需要C端自己處理
    for k, v in ipairs(_SkillData.m_TickAy) do
        if v.m_TickId ~= 0 then
            self.m_PlayingPowerAlert[k] = function(iCtrl, iTickID)
                BattleMgr.SetRangeFrame(iCtrl, iTickID)
            end

            HEMTimeMgr.DoFunctionDelay(v.m_TickTime/1000, self.m_PlayingPowerAlert[k], self, v.m_TickId)
        end
    end

    return _result
end

---收到8-3設定位移量
function RoleController:SetSkillMoveData(iStartPos, iDistance)
    self.m_SkillMoveStart = iStartPos
    self.m_SkillMoveDistance = iDistance
end

--[[
--- 使用技能 - 藉由 SkillActID (BattleEditor用)
function RoleController:UseSkillBySkillActID(iIdx)
    local _result = true
    if iIdx == 0 then
        _result = false
    end

    if _result then
        self.m_NowPlayingSkillActData = PlayingSkillActData.New(nil, SkillActData.GetSkillActDataByIdx(self.m_SkillActKind, iIdx), function() self:OnEnd() end)
        if self.m_NowPlayingSkillActData.m_SkillActData.m_SkillActID == 0 then
            D.LogError(GString.Format("_SkillActData.Id == 0, m_ActId == {0}", self.m_NowPlayingSkillActData.m_SkillActData.Id))
            _result = false
        end

        if SelectMgr.m_TargetGObj then
            self:SetLookAt(SelectMgr.m_TargetGObj.transform.position)
        end

        self:PlayAction()
    end

    return _result
end]]

--- 使用技能 - 藉由 SkillActData (BattleEditor用)
function RoleController:UseSkillBySkillActData(iSkillActData)
    local _result = true
    if iSkillActData == nil then
        _result = false
    end

    if _result then
        local _data
        if type(iSkillActData) == "table" then
            _data = iSkillActData
        else
            _data = JsonMgr.JsonToText(iSkillActData)
        end

        self.m_NowPlayingSkillActData = PlayingSkillActData.New(nil, _data, function() self:OnEnd() end)
        -- 設定攻擊速度
        self.m_AtkSpeed = 1
        -- 除玩家以外的模型 計時用的
        self.m_NowPlayingSkillActData.m_SkillTimeCount = 0
        -- 除玩家以外的模型 結束時間用的
        self.m_NowPlayingSkillActData.m_SkillEndTime = self.m_NowPlayingSkillActData.m_SkillActData.m_TotalTime + (BattleSetting.m_SkillEndTimeAdd * 0.001)

        self:PlayAction()
    end

    return _result
end

---取消 Skill
function RoleController:CancelSkill()
    local _StateCtrl = self.m_StateController

    if self.m_NowPlayingSkillActData then
        if self.m_NowPlayingSkillActData.m_SkillData and self.m_NowPlayingSkillActData.m_SkillData.m_ToBattleState == true then
            self.m_StateController:SetAtkNormalTime(true)
        end

        -- 移動中斷技能的時候要取消戰鬥動作 ( 強制播動作 )
        if (_StateCtrl.m_NextState and _StateCtrl.m_NextState.m_StateType == EStateType.Move and self.m_NowPlayingSkillActData.m_SkillData.m_MoveBreak) or BattleMgr.m_IsCancelPower then
            self:GetModelController():SetBattleState()
        end

        self.m_NowPlayingSkillActData:CancelSkillAct(self:GetModelController())
        self.m_NowPlayingSkillActData = nil
    end

    if self.m_IsSelf == true then
        BattleMgr.m_NowPowerTime = nil
    end

    self:ResetAlertData()
    self:ResetPowerAlertData()
end

--- 移除技能特效 目前僅有音效 ( Particle & Audio )
function RoleController:ReturnSkillEffect()
    --20231228 特效會分段播放 (幾下就有幾個特效 )，所以不用特別去清除
    --if self.m_NowPlayingSkillActData then
    --    -- 音效要等..有機會分段才能改
    --    self.m_NowPlayingSkillActData:ReturnSkillEffect()
    --end
end

--local _ActionCount = 0
function RoleController:PlayAction()
    local _Check = self.m_NowPlayingSkillActData:DoPlayAction(self:GetModelController())

    if _Check then
        self.m_StateController:ChangeState(EStateType.Atk)
        self:SetAtk()
    end
end

function RoleController:SetSneakEffect(iIsShow)
    self:GetModelController():SetSneakEffect(iIsShow)
end

---模型演出發光特效
function RoleController:SetGlowEffect(iIsShow)
    self:GetModelController():SetGlowEffect(iIsShow)
end

function RoleController:AddAlertEffect(iAlertData)
    if not self.m_PlayingAlertData then
        self.m_PlayingAlertData = {}
    end

    table.insert(self.m_PlayingAlertData, iAlertData)
end

--region 動作相關
---註冊Animation event監聽
function RoleController:InitModelAnimationEventListener()
    -- self.m_AnimationController:SetSelf(self);
    -- self.m_AnimationController:AnimationEventAddListener("OnParticle", self.OnParticle)
    -- self.m_AnimationController:AnimationEventAddListener("OnSound", self.OnPlaySound)
    -- self.m_AnimationController:AnimationEventAddListener("OnCamera", self.OnPlayCameraEffect)
    -- self.m_AnimationController:AnimationEventAddListener("OnEnd", self.OnEnd)
    -- self.m_AnimationController:AnimationEventAddListener("OnDead", self.OnDead)
end

function RoleController:SetAtk()
    -- 動作播放改至PlayingSkillData執行
    -- self:GetModelController():SetAtk(self.m_NowPlayingSkillActData.m_NowActionData.m_ActID)
    -- self:GetModelController():SetAtkSpeed(self.m_AtkSpeed * self.m_NowPlayingSkillActData.m_NowActionData.m_ActSpeed)

    -- --- 位移方向(前/後)
    -- local _MoveDir = 0
    -- if BattleMgr.m_IS_BATTLEEDITOR_DEBUG and self.m_IsSelf then
    --     _MoveDir = self.m_NowPlayingSkillActData.m_NowActionData.m_MoveDistance > 0 and Vector3.forward or Vector3.back
    -- else
    --     local _TempSkillData = self.m_NowPlayingSkillActData.m_SkillData
    --     if _TempSkillData.m_MoveDir > 0 then
    --         _MoveDir = _TempSkillData.m_MoveDir == SkillData.EMoveDirType.Forward and Vector3.forward or Vector3.back
    --     end
    -- end

    --- 位移路徑是否遭阻擋,計算後的位移終點
    local _isHit, _destination

    --- 玩家現位置
    local _PlayerPos = self.m_SkillMoveStart


    local _MoveDistance = Mathf.Abs(self.m_NowPlayingSkillActData.m_NowActionData.m_MoveDistance)

    if _MoveDistance > 0 then
        --- 畫位移線起點
        local _DrawPos = _PlayerPos

        -- 戰鬥編輯器位移，不管協定
        if BattleMgr.m_IS_BATTLEEDITOR_DEBUG and self.m_IsSelf then
            if self.m_SkillMoveCo then
                coroutine.stop(self.m_SkillMoveCo)
            end
            --暫編不走SkillMoveCo，改走PlayingSkillActData:DemoSkillMove

            --_isHit, _destination = self.m_MoveController:CheckMove(self.transform.position, _Vec * _MoveDistance)
            --_destination = _Vec * _MoveDistance
            self.m_NowPlayingSkillActData:DemoSkillMove(self)
            -- if self.m_SkillMoveCo then
            --     coroutine.stop(self.m_SkillMoveCo)
            -- end

            -- self.m_SkillMoveCo = coroutine.create(function()
            --     if self.m_SkillMoveCo then
            --         -- 按照表定時間播放動作
            --         coroutine.wait(_MoveStartTime / self.m_AtkSpeed)
            --     end

            --     -- 戰鬥編輯器流程不會算BattleMgr.m_NowSkillMove
            --     --self:SkillMove(_destination ,_MoveTime / self.m_AtkSpeed)
            --     MoveController.SkillMove(self, _destination, _MoveTime / self.m_AtkSpeed)
            -- end)

            -- coroutine.resume(self.m_SkillMoveCo)

            _DrawPos = self.transform.position

        -- 如果這個動作有位移，並且有收到8-3的初始位置及位移
        elseif self.m_SkillMoveDistance > 0 then
            local _MoveDir = Vector3.zero
            local _TempSkillData = self.m_NowPlayingSkillActData.m_SkillData
            if _TempSkillData.m_MoveDir > 0 then
                _MoveDir = _TempSkillData.m_MoveDir == SkillData.EMoveDirType.Forward and Vector3.forward or Vector3.back
            end
            --- 計算後的位移方向
            local _Vec = Quaternion.Euler(self:GetlocalEulerAngle()) * _MoveDir
            local _MoveStartTime = self.m_NowPlayingSkillActData.m_NowActionData.m_MoveStartTime
            local _MoveTime = self.m_NowPlayingSkillActData.m_NowActionData.m_MoveTime

            if self.m_IsSelf then
                _destination = BattleMgr.m_NowSkillMove
            else
                -- 如果動作的位移量比收到的位移量多
                if _MoveDistance >= (self.m_SkillMoveDistance * 0.01) then
                    -- 僅需播放收到的位移量
                    _isHit, _destination = self.m_MoveController:CheckMove(_PlayerPos, _Vec * (self.m_SkillMoveDistance * 0.01))
                    self.m_SkillMoveDistance = 0
                else
                    -- 如果收到的位移量比動作的更多，則將收到的位移量扣掉動作的位移量
                    self.m_SkillMoveDistance = self.m_SkillMoveDistance - (_MoveDistance * 100)
                    -- 並完整播放動作的位移
                    _isHit, _destination = self.m_MoveController:CheckMove(_PlayerPos, _Vec * (_MoveDistance))
                end
            end

            local _actionData = self.m_NowPlayingSkillActData.m_NowActionData

            local m_SkillMoveCo = function()

                if self.m_StateController:GetStateType() == EStateType.Atk then
                    --可能會被中斷
                    if _actionData == self.m_NowPlayingSkillActData.m_NowActionData then
                        -- 播放位移
                        self:SkillMove(_destination ,_MoveTime / self.m_AtkSpeed)
                    end
                end

            end

            HEMTimeMgr.DoFunctionDelay(_MoveStartTime / self.m_AtkSpeed, m_SkillMoveCo)

            _DrawPos = _PlayerPos
        end

        -- Debug功能，畫位移線
        if BattleMgr.m_IsShowSkillArea.Client and self == RoleMgr.m_RC_Player and _destination ~= nil then
            local _selfPos = _DrawPos
            local _EndPos = _destination
            _EndPos.y = _selfPos.y
            CameraMgr.DrawLine(_selfPos, _EndPos)
        end

    else
        -- 看是否強制校正玩家位置
        _destination = _PlayerPos
    end
end

-- function RoleController:PlayActionParticle()
--     -- if self.m_ModelController.m_AppearanceInfo == nil and self.m_ModelController.m_NPCAppearData == nil then
--     --     BattleMgr.ShowLog("self.m_ModelController.m_AppearanceInfo is nil", "red")
--     --     do
--     --         return
--     --     end
--     -- end

--     if self.m_NowPlayingSkillActData.m_eventsData[EAnimationEvent.OnParticle] == nil then
--         BattleMgr.ShowLog("NowActionData.ParticleAty is nil", "red")
--         do
--             return
--         end
--     end

--     local _EffectData = self.m_NowPlayingSkillActData.m_eventsData[EAnimationEvent.OnParticle][self.m_NowPlayingSkillActData.m_ParticleIndex]

--     if _EffectData == nil then
--         BattleMgr.ShowLog("_EffectData is nil", "red")
--         do
--             return
--         end
--     end

--     _EffectData.m_LookAtTrans = nil
--     _EffectData.m_Speed = _EffectData.m_Speed * self.m_AtkSpeed
--     _EffectData.m_Scale = _EffectData.IsFollow and _EffectData.m_Scale or _EffectData.m_Scale * self:GetModelController().transform.localScale.x

--     self:GetModelController():SetEffect(_EffectData)
--     -- else
--     --     -- 防呆播放
--     --     _ShowRotation = self:GetRotationObj().localEulerAngles
--     --     EffectMgr.PlayEffectWithParent(
--     --             EEffectType.Model,
--     --             _EffectData.m_ID,
--     --             self.transform,
--     --             self.m_PlayerID == PlayerData.GetRoleID(),
--     --             nil,
--     --             nil,
--     --             nil,
--     --             nil,
--     --             _EffectData.m_Speed * self.m_AtkSpeed,
--     --             _EffectData.m_Scale,
--     --             _ShowRotation,
--     --             _EffectData.m_isFollow)

--     --     BattleMgr.ShowAnimationLog("Not Find ModelController, PlayerID: " .. self.m_PlayerID , "red")
--     -- end

--     --- 特效變色
--     --[[ HEM2.0 目前沒有用到，所以先砍掉了
--     local function ChangeColorDelegate()
--         if BattleMgr.m_IS_BATTLEEDITOR_DEBUG or self.m_NowPlayingSkillActData.m_SkillData.m_ChangeSkillColor ~= 1 then
--             return nil
--         else
--             return function(iHashCode)
--                 local _EnhanecColor = {}
--                 _EnhanecColor.m_TColor = {}
--                 _EnhanecColor.m_TColor.ColorA = self.m_NowPlayingSkillActData.m_WugongData.m_WugongColorAy[1]
--                 _EnhanecColor.m_TColor.ColorB = self.m_NowPlayingSkillActData.m_WugongData.m_WugongColorAy[2]
--                 _EnhanecColor.m_TColor.ColorC = self.m_NowPlayingSkillActData.m_WugongData.m_WugongColorAy[3]
--                 _EnhanecColor.m_TColor.ColorD = self.m_NowPlayingSkillActData.m_WugongData.m_WugongColorAy[4]
--                 -- local _EnhanecColor = EnhanceColorData.GetEnhanceColorDataByIdx(
--                 --     (_RoleEnchanceEffect.m_ClothRarity == 0 and 1 or _RoleEnchanceEffect.m_ClothRarity) + 50) -- 串表上防具需要 +50
--                 EffectMgr.SetParticleEffectFX(iHashCode, _EnhanecColor)
--             end
--         end
--     end
--     --]]
-- end
--region Animator狀態設定

---戰鬥狀態
---@param iIsBattle boolean 是否設定成戰鬥狀態
function RoleController:SetBattleNormalState(iIsBattle)
    if not self.gameObject.activeSelf then
        do return end
    end
    -- HEMTimeMgr.AddListener(SearchMgr.Refresh_FriendlyNPC, 1, true)
    self:GetModelController():SetBattleNormalState(iIsBattle)
end

---受擊狀態
---@param iSenderRC RoleController 發招者RC
function RoleController:SetHitState(iSenderRC)
    if not self.gameObject.activeSelf or not self.m_StateController then
        return
    end

    -- 只有 Idle、Hit 狀態才能進受擊狀態
    local _State = self.m_StateController:GetStateType()
    if _State == EStateType.Idle
            or _State == EStateType.Hit
            or _State == EStateType.Move
    then
        -- 將發招者的RC暫存給HitState，用來算攻擊朝向
        if self.m_StateController:SetHitSender(iSenderRC) then
            -- 切換狀態
            self.m_StateController:ChangeState(EStateType.Hit)
        end
    end

end
--endregion Animator狀態設定

--region 資料相關

--- 顯示設定相關
local m_ISSHOWACTIONEFFECT = true

-- --- 重設粒子特效資料
-- function RoleController:ResetParticleEffectData()
--     if not self.m_NowPlayingSkillActData then
--         return
--     end
--     self.m_NowPlayingSkillActData:SetParticleIndex(0)
-- end

-- --- 重設相機特效資料
-- function RoleController:ResetCamEffectData()
--     if not self.m_NowPlayingSkillActData then
--         return
--     end
--     self.m_NowPlayingSkillActData:SetCamEffectIndex(0)
-- end

-- --- 重設音效特效資料
-- function RoleController:ResetSoundData()
--     self.m_NowPlayingSkillActData:SetSoundIndex(0)
-- end

-- function RoleController:SetSoundIndex(iIndex)
--     if not self.m_NowPlayingSkillActData then
--         return
--     end

--     self.m_NowPlayingSkillActData.m_SoundIndex = iIndex
--     -- 偵錯用
--     --D.Log("[Sound] " .. "Set Index : " .. iIndex .. ". " .. debug.traceback())
-- end

--- 重設警戒框資料
function RoleController:ResetAlertData()
    if self.m_PlayingAlertData then
        for k, v in ipairs(self.m_PlayingAlertData) do
            v.m_isDone = true
        end

        self.m_PlayingAlertData = nil
    end
end

--- 重設蓄力警戒框資料
function RoleController:ResetPowerAlertData()
    -- 蓄力是依靠C端自己演，需要把先塞好的延遲觸發清楚
    if self.m_PlayingPowerAlert then
        for k, v in ipairs(self.m_PlayingPowerAlert) do
            HEMTimeMgr.CancelDoFunctionDelay(v)
        end

        self.m_PlayingPowerAlert = nil
    end
end
--endregion 資料相關



--region Animation event呼叫

-- ---@param iSelf RoleController
-- ---@param iIndex number
-- ---@param iActIndex number
-- function RoleController.OnParticle(iSelf, iIndex, iActIndex)
--     iSelf:GetModelController():OnParticle(iIndex, iActIndex)
-- end

-- ---@param iSelf RoleController
-- ---@param iIndex number
-- ---@param iActIndex number
-- function RoleController.OnPlaySound(iSelf, iIndex, iActIndex)
--     iSelf:GetModelController():OnPlaySound(iIndex, iActIndex)
-- end

-- ---@param iSelf RoleController
-- ---@param iIndex number
-- ---@param iActIndex number
-- function RoleController.OnPlayCameraEffect(iSelf, iIndex, iActIndex)
--     iSelf:GetModelController():OnPlayCameraEffect( iIndex, iActIndex)
-- end

---@param iSelf RoleController
---@param iIndex number
---@param iActIndex number
function RoleController.OnEnd(iSelf, iIndex, iActIndex)
    iSelf:PlayAction()
end


function RoleController.OnDead(iSelf)
    BattleMgr.ShowAnimationLog("RoleController OnDead")
    --iSelf:GetModelController():OnDead()
end
--endregion Animation event呼叫
--endregion 動作相關

--region 表控隱藏角色
function RoleController:ResetAnim()
    self:GetModelController():ResetAnimator()
end

---執行動畫
function RoleController:DoAnim(iAnimID, isLoop)
    isLoop = isLoop and isLoop or false
    if iAnimID == 255 then
        self:GetModelController():SetAniBool(AnimationHash.Bool_IsLoopSituation, false)
        self:GetModelController():SetAniInteger(AnimationHash.Int_ShowID, 0)
    elseif EEmotionParam[iAnimID] then
        --自訂義特殊情感動作
        if iAnimID == 51 then --死亡
            self.m_StateController:ChangeState(EStateType.Dead)
        elseif iAnimID == 52 then --進入戰鬥
            self:SetBattleNormalState(true)
        elseif iAnimID == 53 then --轉身並消失
            local _toPos = Vector3(0, 0, 0)
            _toPos:Copy(self.m_ModelObject.transform.position)

            _toPos = _toPos + Vector3.back * 5 * self.m_ModelObject.transform.rotation
            self:MoveToTargetPos(self.transform.position, _toPos, 6)

            self:GetModelController():UpdateFade(0)
            self:GetModelController():DoFade(true, 2.5)
        elseif iAnimID == 54 then --走路並登場
            local _toPos = Vector3(0, 0, 0)
            _toPos:Copy(self.m_ModelObject.transform.position)

            self.transform.position = _toPos + Vector3.back * 5 * self.m_ModelObject.transform.rotation
            self:MoveToTargetPos(self.transform.position, _toPos, 6)

            self:GetModelController():UpdateFade(0)
            self:GetModelController():DoFade(false, 2.5)
        elseif iAnimID == 55 then --傳送出現
            self:TeleportEffect(true)
        elseif iAnimID == 56 then --傳送消失
            self:TeleportEffect(false)
        end
    else
        self:GetModelController():SetAniBool(AnimationHash.Bool_IsLoopSituation, isLoop)
        self:GetModelController():SetAniInteger(AnimationHash.Int_ShowID, iAnimID)
        self:GetModelController():SetAniTrigger(AnimationHash.Trigger_Show)
    end

end

---個別設定是否隱藏RC，隱藏/現身前該做甚麼
function RoleController:SetBeSealed(iSeal, iAnimID)

    --- 執行隱藏/顯示
    local _DoSeal = function()
        if iAnimID and iAnimID ~= 0 then
            ---播動畫後再執行隱藏
            if iSeal then
                self:DoAnim(iAnimID)
                coroutine.wait(2.5, self.m_SealCoroutine)
                self.gameObject:SetActive(false)
            end
        end

        self.gameObject:SetActive(not iSeal)

        self.m_BeSealed = iSeal
        if not self.m_BeSealed and self.m_CurrentCulling == ECullingState.IN_NEARBY_RANGE then
            self:SetHUDActive(true)
        else
            self:SetHUDActive(false)
            if SelectMgr.m_TargetController == self then
                SelectMgr.ClearSelect()
            end
        end

        if iAnimID and iAnimID ~= 0 then
            ---登場先開人物再動作
            if not iSeal then
                self:DoAnim(iAnimID)
            end
        end
    end

    if self.m_SealCoroutine == nil or coroutine.status(self.m_SealCoroutine) =="dead" then
        self.m_SealCoroutine = coroutine.create(function() _DoSeal() end)
        coroutine.resume(self.m_SealCoroutine)
    end
end

--endregion

--region 敵我關係功能

---判斷敵我函數集合
local f_CheckRelation = {}

--- 隊友檢查
f_CheckRelation[ERelatType.Teammate] = function(iRC)
    if iRC.m_RoleType ~= ERoleType.OtherPlayer  then
        return false
    end

    if not TeammateMgr.Get(ETeamKind.Team, iRC.m_PlayerID) then
        return false
    end

    return true
end

---幫會檢查
f_CheckRelation[ERelatType.Guild] = function(iRC)
    if iRC.m_RoleType ~= ERoleType.OtherPlayer  then
        return false
    end

    -- todo:幫會相關檢查
    return false
end

--- 陣營檢查
f_CheckRelation[ERelatType.Alliance] = function(iRC)
    if not RoleMgr.m_RC_Player then
        return false
    end

    if RoleMgr.m_RC_Player.m_Troops == 0 then
        return false
    elseif iRC.m_Troops == RoleMgr.m_RC_Player.m_Troops then
        return true
    end

    return false
end

--- 情侶檢查 todo:完成相關功能後記得改
f_CheckRelation[ERelatType.Couple] = function(iRC)
    if iRC.m_RoleType ~= ERoleType.OtherPlayer  then
        return false
    end

    return PlayerData.m_LoginPlayerData.m_HoneyId == iRC.m_PlayerID
end

--- 好友檢查
f_CheckRelation[ERelatType.Friend] = function(iRC)
    if iRC.m_RoleType ~= ERoleType.OtherPlayer then
        return false
    end

    if FriendMgr:GetDataExist(EFriendType.Friend) then
        return FriendMgr:GetIsCommunityByID(EFriendType.Friend, tostring(iRC.m_PlayerID))
    else
        return false
    end
end

--- 敵人檢查
f_CheckRelation[ERelatType.Enemy] = function(iRC)
    -- NPC 類型
    if iRC.m_RoleType == ERoleType.NPC and iRC.m_NPCMode ~= EventNPCMode.CopyPlayer then
        -- 可攻擊目標
        if iRC.m_NPCMode == EventNPCMode.Fight or iRC.m_NPCMode == EventNPCMode.FightCannotDie then
            -- if not iRC.m_Relation[ERelatType.Alliance] then --先把他註解掉 城戰  & 破碎戰場 才需要
                return true
            -- end

        -- 守衛
        elseif iRC.m_NPCMode == EventNPCMode.Guard then
            -- 威望值(善惡值)檢查
            if RoleMgr.m_Prestige and RoleMgr.m_Prestige < PRESTIGE_BOUNDARY then
                return true
            end
        end

    -- 其他玩家類型 或假人
    elseif iRC.m_RoleType == ERoleType.OtherPlayer or iRC.m_NPCMode == EventNPCMode.CopyPlayer then
        local _SceneData = SceneAttributeData.Get(SceneMgr.GetSceneID())
        local _ScenePVPType = _SceneData:GetPVPType()

        if _ScenePVPType == EScenePVPType.Neutrality then
            -- 被動PVP
            --不是隊友、沒有新手保護 Buff(4585)、沒有 PK 保護 Buff(4586)
            if not iRC.m_Relation[ERelatType.Teammate] and
                    not BuffMgr.GetRoleBuff(iRC.m_PlayerID, ESpecialBuffID.RookieProtect) and
                    not BuffMgr.GetRoleBuff(iRC.m_PlayerID, ESpecialBuffID.PKProtect) then

                --這人是假人 而且是假好人
                if tostring(iRC.m_PlayerID) == tostring(PlayerData.GetCardID()) or TeammateMgr.Get(ETeamKind.Team, iRC.m_PlayerID) then
                    return false
                end

                -- 是好友(情侶)，且開啟 好友(情侶) PK   -- 可以討論看看這設定取消
                -- 是不是在 PK Server
                if PlayerData.m_IsPKServer then
                    if RoleMgr.m_PKEnable == 1 or RoleMgr.m_PKState >= EPKState.Red then
                        -- 玩家本人開啟 PK 或紫人 = 誰都可以打
                        return true
                    elseif iRC.m_PKInfo.m_PKState >= EPKState.Red then
                        -- 對方是紫人
                        return true
                    end
                else
                    -- 和平分流不會有紫人
                    if RoleMgr.m_RC_Player.m_PKInfo.m_PKEnable == 1 and iRC.m_PKInfo.m_PKEnable == 1 then
                        -- 玩家雙方都開啟 PK
                        return true
                    end
                end
            end
        elseif _ScenePVPType == EScenePVPType.Hostility then
            -- 強制PVP
            -- PVP副本或公會戰
            if _SceneData.m_SceneType == ESceneType.PVPDungeon
                    or _SceneData.m_SceneType == ESceneType.GuildWar then
                -- 是同盟
                if iRC.m_Relation[ERelatType.Alliance] then
                    return false
                end
            end

            return true
        end

        --[[1.0規則，在確認完天外天、幻境、修羅王、城戰、PK伺服器顯示正常後可以刪除
        -- 一般場景或天外天
        if _SceneData.m_SceneType == ESceneType.Normal
                or _SceneData.m_SceneType == ESceneType.WorldBossWar then

            -- 場景允許 PK
            if _SceneData.m_Allow_PK then
                --不是隊友、沒有新手保護 Buff(4585)、沒有 PK 保護 Buff(4586)
                if not iRC.m_Relation[ERoleType.Teammate] and
                    not BuffMgr.GetRoleBuff(iRC.m_PlayerID, ESpecialBuffID.RookieProtect) and
                    not BuffMgr.GetRoleBuff(iRC.m_PlayerID, ESpecialBuffID.PKProtect) then

                    -- 玩家雙方有人沒開啟 PK
                    if not iRC.m_IsPKEnable or not RoleMgr.m_RC_Player.m_IsPKEnable then
                        return false
                    end

                    -- 是好友(情侶)，且開啟 好友(情侶) PK   -- 可以討論看看這設定取消

                    -- 是不是在 PK Server
                    if PlayerData.m_IsPKServer then
                        return true
                    end
                end
            end

            -- 在 PVP 場景
            if _SceneData:GetIsPVPDungeon() then
                return true
            end

            --todo:活動強制PK場景(修羅王)

        -- PVP副本或公會戰
        elseif _SceneData.m_SceneType == ESceneType.PVPDungeon
                or _SceneData.m_SceneType == ESceneType.GuildWar then

            if not iRC.m_Relation[ERelatType.Alliance] then
                return true
            end
        end]]
    end

    return false
end

---全關係更新
function RoleController:RefreshRelationShip()
    for k,v in pairs(ERelatType) do
        self:RefreshRelationByType(v)
    end

    self:UpdateHUDRelationship()
end

---由關係類型更新玩家關係
function RoleController:RefreshRelationByType(iRelationType, ...)
    self.m_Relation[iRelationType] = f_CheckRelation[iRelationType](self)
    return self.m_Relation[iRelationType]
end

--endregion 敵我關係功能

--local _LastSelcetRC = nil

--region HUD
--region 核心技
--- 更新核心技HUD
function RoleController:UpdateCoreSkillHUD(iPassiveType, iValue)
    if not self:CheckHUDRefreshable() then
        return
    end

    self.m_HUDController:SetCoreSkillInfo(iPassiveType, iValue)
end

--endregion
--- 檢查 HUD 是否可以更新
---@return boolean
function RoleController:CheckHUDRefreshable()
    if not self.m_HUDController then
        return false
    elseif not self.m_HUDController.m_IsInited then
        return false
    end

    return true
end

--- 改變 HUD 的血量資訊 (不更新的請給 nil)
---@param iMaxHp number 最大血量
---@param iCurHp number 現在血量
function RoleController:UpdatHUDHP(iMaxHp , iCurHp)
    if not self:CheckHUDRefreshable() then
        return
    end

    if iMaxHp then
        self.m_HUDController.m_HUDData.m_MaxHp = iMaxHp
    end

    if iCurHp then
        self.m_HUDController.m_HUDData.m_CurHp = iCurHp
    end

    if SelectMgr.m_TargetController == self then
        Main_SubCtrl_TargetInfo.UpdateHP()
    end

    self.m_HUDController:SetHUDDirty(EHUD_UpdateChecker.HP)
end

--- 改變 HUD 的護盾資訊 (不更新的請給 nil)
---@param iCurHp number 現在護盾
function RoleController:UpdatHUDShield(iCurShield)
    if not self:CheckHUDRefreshable() then
        return
    end

    iCurShield = iCurShield and iCurShield or 0
    if iCurShield then
        self.m_HUDController.m_HUDData.m_CurShield = iCurShield
    end

    if SelectMgr.m_TargetController == self then
        Main_SubCtrl_TargetInfo.UpdateHP()
    end

    self.m_HUDController:SetHUDDirty(EHUD_UpdateChecker.HP)
end

--- 更新HUD
---@param iEHUDChecker EHUD_UpdateChecker
function RoleController:UpdateHUD(iEHUDChecker)
    if not self:CheckHUDRefreshable() then
        return
    end

    self.m_HUDController:SetHUDDirty(iEHUDChecker)
end

function RoleController:UpdateHUDRelationship()
    if not self:CheckHUDRefreshable() then
        return
    end

    -- 玩家才有
    if self == RoleMgr.GetPlayerRC() then
        local _HUDData = self.m_HUDController.m_HUDData
        _HUDData.m_PKEnable = RoleMgr.m_PKEnable
        _HUDData.m_PKState = RoleMgr.m_PKState
    elseif self.m_PKInfo then
        local _HUDData = self.m_HUDController.m_HUDData
        _HUDData.m_PKEnable = self.m_PKInfo.m_PKEnable
        _HUDData.m_PKState = self.m_PKInfo.m_PKState
    end

    self.m_HUDController:RefreshRelationship(self.m_Relation)
end

function RoleController:ReleaseHUD()
    if not self:CheckHUDRefreshable() then
        return
    end

    self.m_HUDController:ReturnHUD()
end

-- --- 委派給選擇系統更新 HUD 顯示與否
-- ---@param iRC RoleController
-- function RoleController.SetSelectHUDActive(iRC)
--     if not iRC or not iRC.m_HUDController then
--         return
--     end

--     if _LastSelcetRC then
--         _LastSelcetRC:SetHUDActive(false)
--     end

--     _LastSelcetRC = iRC
--     iRC:SetHUDActive(true)
-- end

--- 開關 HUD
function RoleController:SetHUDActive(iIsActive)
    if not self:CheckHUDRefreshable() then
        return
    end

    self.m_HUDController:SetHUD(iIsActive)
end

function RoleController:HUDCallBack()
    -- 敵我關係更新，因為更新完會把資料塞進 HUD，所以必須放在 HUDController 之後
    self:RefreshRelationShip()

    self.m_HUDController:SetHUDDirty(EHUD_UpdateChecker.Enable)
    if self.m_IsSelf then
        self:SetHUDActive(true)
    else
        self:CullingUpdate()
    end
end

--region 泡泡框

---@param iData BubbleDialogData
function RoleController:StartBubbleBox(iData)
    return self.m_HUDController:StartBubbleBox(iData)
end

---存下當前對話狀態
function RoleController:SetHPCurrentOrder(iStage)
    if not self.m_HUDController then
        return
    end

    return self.m_HUDController:SetHPCurrentOrder(iStage)
end

function RoleController:RegisterHPObserve(iBool)
    if not self.m_HUDController then
        return
    end

    self.m_HUDController:RegisterHPObserve(iBool)
end

--- 停止泡泡框對話
function RoleController:StopBubbleBox()
    if not self.m_HUDController then
        return
    end
    self.m_HUDController:StopBubbleBox()
end
--endregion
--endregion

--等級提升時演出特效 僅對玩家本人的 RoleConroller有作用
function RoleController:OnStateChanged(iEStateObserver, iEBaseValues)

    if iEStateObserver == EStateObserver.PlayerLevelRefresh  and  PlayerData.GetLv() > self.m_LV   then
        EffectMgr.PlayEffectWithParent(EEffectType.Model, ModelSetting.m_PlayerLevelUPEffect, self.transform)
        AudioMgr.PlayAudio(AudioMgr.EAudioType.Sound, AudioMgr.EMixerGroup.UI, RoleController.LVUP_AUDIO_ID, self.transform, false, false)
        self.m_LV = PlayerData.GetLv()
    end
end

function RoleController:OnUnrequire()
    HUDController:OnUnrequire()
    return true
end
