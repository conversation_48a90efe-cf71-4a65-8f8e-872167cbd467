﻿---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2025 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---內功系統-六脈靈功頁面
---@class SixSpirit_Controller
---author Jin
---telephone #2909
---version 1.0
---since [黃易群俠傳M] 1.0
---date 2025.3.21
SixSpirit_Controller = {}
local this = SixSpirit_Controller

---當前選擇武功Idx
local _WugongIdx = 1

---開始推演靈功
local _IsDeduction = false

---推演倒數
local _DeductionCountdown = 0

---推演時間
local _DeductionTime = 1

---靈功推演道具ID
local _MeridianDeductionItemID = {
    [1] = {69061, 69065, 69067},
    [2] = {69062, 69065, 69067},
    [3] = {69063, 69066, 69067},
    [4] = {69064, 69066, 69067},
    [5] = {69063, 69065, 69067},
    [6] = {69064, 69065, 69067},
}

---開始靈功升級
local _IsUpgrade = false

---靈功升級點擊次數
local _ClickCount = 0

---靈功升級等級Table
local _UpgradeLvTable = {}

---靈功升級道具ID
local _MeridianUpgradeItemID = 55002

---裝備中字串
local _EquipingString = 20102037
---裝備靈功字串
local _EquipString = 20103017
---取消裝備字串
local _CancelEquipString = 20103040
---靈功推演字串
local _DeductionString = 21002107
---靈功升級字串
local _UpgradeString = 21002103
---達最高等級字串
local _MaxLevelString = 20102039

---初始化
function SixSpirit_Controller.Init(iController)
    this.m_Controller = iController
    this.m_ViewRef = iController.m_ViewRef

    --顯示機率按鈕
    this.m_Btn_prob = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_Prob"))
    this.m_Btn_prob:AddListener(EventTriggerType.PointerClick, function() SixSpirit_Controller.OnClick_Prob() end)

    --尚未獲得任何靈功
    this.m_Group_NoSpirit = this.m_ViewRef.m_Dic_Trans:Get("&Group_NoSpirit")
    this.m_Text_SpiritTip = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritTip")

    --靈功資訊
    this.m_Group_SpiritInfo = this.m_ViewRef.m_Dic_Trans:Get("&Group_SpiritInfo")
    local _Trans_SkillIcon = this.m_ViewRef.m_Dic_Trans:Get("&Trans_SkillIcon")
    this.m_Trans_SkillIcon = IconMgr.NewInnerSkillIcon(0, _Trans_SkillIcon, 110)
    this.m_Text_SpiritName = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritWGName")
    this.m_Text_SpiritLv = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritWGLv")
    this.m_Text_SpiritCaption = this.m_ViewRef.m_Dic_TMPText:Get("&Text_SpiritCaption")

    --尚未推演出靈功
    this.m_Group_NotYetSpirit = this.m_ViewRef.m_Dic_Trans:Get("&Group_NotYetSpirit")

    --靈功Icon區域
    this.m_Group_Skill = this.m_ViewRef.m_Dic_Trans:Get("&Group_Skill")
    this.m_Trans_Skill = this.m_ViewRef.m_Dic_Trans:Get("&Trans_Skill")
    this.m_SkillData = {}
    for i = 1, 9 do
        local _SkillObject = this.m_Trans_Skill:Instantiate( this.m_Trans_Skill )
        _SkillObject:SetParent(this.m_Group_Skill)
        _SkillObject:SetSiblingIndex(i - 1)
        
        local _SkillInfo = {}
        _SkillInfo.m_GameObject = _SkillObject.gameObject
        _SkillInfo.m_SkillIcon = IconMgr.NewInnerSkillIcon(0, _SkillInfo.m_GameObject.transform,110, function() SixSpirit_Controller.OnClick_Skill(i) end)
        _SkillInfo.m_SkillIcon.gameObject.transform:SetSiblingIndex(0)

        table.insert(this.m_SkillData, _SkillInfo)
    end
    this.m_Trans_Skill.gameObject:SetActive(false)

    --按鈕區域
    this.m_Btn_Left = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_SixSpirit_Left"))
    this.m_Btn_Left:AddListener(EventTriggerType.PointerClick, function() SixSpirit_Controller.OnClick_Left() end)
    this.m_Btn_Middle = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_SixSpirit_Middle"))
    this.m_Btn_Middle:AddListener(EventTriggerType.PointerClick, function() SixSpirit_Controller.OnClick_Middle() end)
    this.m_Btn_Right = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Btn_SixSpirit_Right"))
    this.m_Btn_Right:AddListener(EventTriggerType.PointerClick, function() SixSpirit_Controller.OnClick_Right() end)

    --靈功推演演出
    this.m_Image_Loading = this.m_ViewRef.m_Dic_Trans:Get("&Image_Loading")
    this.m_Slider = this.m_Image_Loading:Find("Slider"):GetComponent("Slider")
    this.m_Text_Percent = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Percent")
    this.m_Image_Loading.gameObject:SetActive(false)
end

function SixSpirit_Controller.Update()
    --推演要跑動畫
    if _IsDeduction then
        SixSpirit_Controller.SetLoadingActive(true)
        _DeductionCountdown = _DeductionCountdown + HEMTimeMgr.m_DeltaTime
        local _Percentage = math.ceil(_DeductionCountdown / _DeductionTime * 100)
        if _Percentage > 100 then _Percentage = 100 end
        this.m_Slider.value = _Percentage * 0.01
        this.m_Text_Percent.text = _Percentage .. "%"
        
        if _Percentage >= 100 then
            local _ItemID, _SelectIdx = CommonQuery_Type6_Controller.GetSelectItem()
            local _Packet = {}
            _Packet.m_MeridianID = InnerBook_Model.m_MeridianIdx
            _Packet.m_Step = _WugongIdx
            _Packet.m_ItemID = _ItemID
            SendProtocol_003._017(6, _Packet)
            InnerBook_Controller.SetMaskActive(false)

            _IsDeduction = false
            _DeductionCountdown = 0
            _Percentage = 0
        end
    end

    --靈功升級要依等級更新資訊(之後會改用註冊的)
    if _IsUpgrade then
        if _ClickCount ~= CommonQuery_Type2_Controller.GetCurrentTimes() then
            _ClickCount = CommonQuery_Type2_Controller.GetCurrentTimes()
            local _Lv = _UpgradeLvTable[_ClickCount]
            SixSpirit_Controller.SetWugongUpgradeInfo(_Lv)
        end
    end
end

function SixSpirit_Controller.Open()
    SixSpirit_Controller.SetLoadingActive(false)
    InnerBook_Controller.OnClick_Meridian(1)
    GStateObserverManager.Register(EStateObserver.UpdateSixSpirit, this)
end

function SixSpirit_Controller.Close()
    GStateObserverManager.UnRegister(EStateObserver.UpdateSixSpirit, this)
end

---設定loading介面開關
function SixSpirit_Controller.SetLoadingActive(iShow)
    this.m_Image_Loading.gameObject:SetActive(iShow)
end

---設定靈功資訊
function SixSpirit_Controller.SetWugongInfo(iIdx, iWugongIdx)
    local _Step = InnerBook_Model.m_CurrentMeridian.m_Step
    local _Point = InnerBook_Model.m_CurrentMeridian.m_Point
    local _Data = InnerMeridianSkillData.GetInnerMeridianSkillDataByIdx(iIdx)
    
    local _IsNoSpirit = _Step == 1 and _Point < 9
    this.m_Group_NoSpirit.gameObject:SetActive(_IsNoSpirit)
    this.m_Group_SpiritInfo.gameObject:SetActive(not _IsNoSpirit)
    this.m_Group_NotYetSpirit.gameObject:SetActive(false)
    if _IsNoSpirit then
        local _MeridianText = GString.Format(TextData.Get(10101014), TextData.Get(20112030 + iIdx), TextData.Get(21002050 + _Step))
        local _TipStr = GString.Format(TextData.Get(10101006), _MeridianText, TextData.Get(21002102))
        this.m_Text_SpiritTip.text = _TipStr
    end

    --設定靈功
    for k, v in pairs(this.m_SkillData) do
        v.m_SkillIcon:ShowSelect(k == 1)

        local _CurWugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[k]
        if k <= 3 then
            local _WugongData = WugongData.GetWugongDataByIdx(_Data[k].m_WugongID)
            if _WugongData then
                v.m_SkillIcon:RefreshIcon(_WugongData.m_Idx)
            else
                v.m_SkillIcon:RefreshIcon(0)
            end
        else
            if _CurWugongData ~= nil and _CurWugongData.m_ID ~= 0 then
                local _WugongData = WugongData.GetWugongDataByIdx(_CurWugongData.m_ID)
                v.m_SkillIcon:RefreshIcon(_WugongData.m_Idx)
            else
                v.m_SkillIcon:RefreshIcon(0)
            end
        end

        ---品階大於idx
        local _IsStepGreater = _Step > k
        ---品階小於idx
        local _IsStepLess = _Step < k
        ---品階等於idx
        local _IsStepEqual = _Step == k
        ---穴位等於9
        local _IsPointMax = _Point == InnerBook_Model.m_AcuMaxCount
        ---穴位小於9
        local _IsPointNotMax = _Point < InnerBook_Model.m_AcuMaxCount
        ---有靈功
        local _HaveWugong = _CurWugongData ~= nil and _CurWugongData.m_ID ~= 0
        ---沒靈功
        local _NotHaveWugong = _CurWugongData == nil or _CurWugongData.m_ID == 0
        ---品階大於3
        local _IsStepGreater3 = k > 3
        
        --顯示品階遮罩
        if _IsStepLess then
            local _Text = GString.StringWithStyle(TextData.Get(21002050 + k), "W")
            v.m_SkillIcon:SetMask(true, _Text)
        else
            --是否裝備中
            if InnerBook_Model.CheckCurrentWugong(k) then
                local _Text = GString.StringWithStyle(TextData.Get(_EquipingString), "OO")
                v.m_SkillIcon:SetMask(true, _Text)
            else
                v.m_SkillIcon:SetMask(false)
            end
        end

        --顯示圓形
        if _IsStepEqual and _IsPointNotMax then
            v.m_SkillIcon:SetCircle(true, _Point)
        else
            v.m_SkillIcon:SetCircle(false)
        end

        --顯示+號
        if _IsStepGreater3 and (_IsStepGreater or (_IsStepEqual and _IsPointMax)) and _NotHaveWugong then
            v.m_SkillIcon:SetAdd(true)
        else
            v.m_SkillIcon:SetAdd(false)
        end

        --顯示交換
        if _IsStepGreater3 and _HaveWugong then
            v.m_SkillIcon:SetChange(true)
        else
            v.m_SkillIcon:SetChange(false)
        end
    end

    SixSpirit_Controller.OnClick_Skill(iWugongIdx)

    InnerBook_Controller.SetCommonMeridianInfo(iIdx, _Step, _Point)
end

---設定靈功升級資訊
function SixSpirit_Controller.SetWugongUpgradeInfo(iLv)
    local _WugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[_WugongIdx]
    local _Wugong = WugongData.GetWugongDataByIdx(_WugongData.m_ID)
    this.m_Trans_SkillIcon:RefreshIcon(_Wugong.m_Idx)
    local _WugongName = _Wugong.m_Name
    local _WugongLv = iLv
    local _LvStr = GString.Format(TextData.Get(20111037), _WugongLv, 100)
    this.m_Text_SpiritName.text = _WugongName
    this.m_Text_SpiritLv.text = _LvStr
    local _Caption = GString.StyleChangeBatch(GFunction.GetSkillUpLevelFormatString(_WugongData.m_ID, iLv), InnerBook_Model.m_EmphasizeStyle)
    this.m_Text_SpiritCaption.text = _Caption
end

---收到靈功推演資料(不是真的收到 還要確認)
function SixSpirit_Controller.CheckSpiritWugongData(iData)
    SixSpirit_Controller.SetLoadingActive(false)
    --還沒推演過靈功
    if InnerBook_Model.m_MeridianData[iData.m_MeridianID].m_WugongData[iData.m_Step] == nil or InnerBook_Model.m_MeridianData[iData.m_MeridianID].m_WugongData[iData.m_Step].m_ID == 0 then
        --直接送確認
        SendProtocol_003._017(7, {m_MeridianID = iData.m_MeridianID, m_Step = iData.m_Step, m_WugongID = iData.m_WugongID})

    --已推演過 顯示換靈功介面
    else
        InnerBook_Controller.SetChooseWugong(iData)
    end
end

--region 點擊專區

---點擊靈功1-9
function SixSpirit_Controller.OnClick_Skill(iIdx)
    _WugongIdx = iIdx or _WugongIdx

    for k, v in pairs(this.m_SkillData) do
        v.m_SkillIcon:ShowSelect(k == _WugongIdx)
    end

    this.m_Btn_prob.gameObject:SetActive(_WugongIdx > 3)

    SixSpirit_Controller.SetBtnState()

    local _Step = InnerBook_Model.m_CurrentMeridian.m_Step
    local _Point = InnerBook_Model.m_CurrentMeridian.m_Point
    if _WugongIdx > _Step or (_WugongIdx == _Step and _Point < 9) then
        return
    end

    local _WugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[_WugongIdx]
    local _HaveWugong = _WugongData ~= nil and _WugongData.m_ID ~= 0
    this.m_Group_NoSpirit.gameObject:SetActive(false)
    this.m_Group_SpiritInfo.gameObject:SetActive(_HaveWugong)
    this.m_Group_NotYetSpirit.gameObject:SetActive(not _HaveWugong)

    local _WugongID = 0
    if _WugongIdx <= 3 then
        local _Data = InnerMeridianSkillData.GetInnerMeridianSkillDataByIdx(InnerBook_Model.m_MeridianIdx)
        _Data = _Data[_WugongIdx]
        _WugongID = _Data.m_WugongID
    else
        _WugongID = _WugongData ~= nil and _WugongData.m_ID or 0
    end

    local _Wugong = WugongData.GetWugongDataByIdx(_WugongID)
    if _WugongID ~= 0 and _WugongData ~= nil then
        this.m_Trans_SkillIcon:RefreshIcon(_Wugong.m_Idx)
        local _WugongName = _Wugong.m_Name
        local _WugongLv = _WugongData.m_Lv
        local _LvStr = GString.Format(TextData.Get(20111037), _WugongLv, 100)
        this.m_Text_SpiritName.text = _WugongName
        this.m_Text_SpiritLv.text = _LvStr
        this.m_Text_SpiritCaption.text = TextData.Get(_Wugong.m_TextIdx)
    end
end

---點擊顯示機率介面
function SixSpirit_Controller.OnClick_Prob()
    local _MeridianSkillData = InnerMeridianSkillData.GetInnerMeridianSkillDataByIdx(InnerBook_Model.m_MeridianIdx)
    local _IDTable = {}
    local _ProbTable = {}
    for k, v in pairs(_MeridianSkillData) do
        if v.m_Define == 4 then
            table.insert(_IDTable, v.m_WugongID)
            local _Prob = GString.SetPercentageString((v.m_RandProb * 0.001), 2)
            table.insert(_ProbTable, _Prob)
        end
    end

    local _Type4Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.ShowProbability) 
    _Type4Data:Type4_BuildData(EIconType.Skill,_IDTable, _ProbTable, {})
    CommonQueryMgr.AddNewInform(211, {}, {}, nil, nil, nil, nil, nil, nil, _Type4Data)
end

---裝備或取消裝備靈功
function SixSpirit_Controller.OnClick_Left()
    local _IsEquip = InnerBook_Model.CheckCurrentWugong(_WugongIdx)
    --裝備靈功
    if not _IsEquip then
        SendProtocol_003._017(8, {m_MeridianID = InnerBook_Model.m_MeridianIdx, m_Step = _WugongIdx})

    --取消裝備
    else
        SendProtocol_003._017(8, {m_MeridianID = InnerBook_Model.m_MeridianIdx, m_Step = 0})
    end
end

---靈功推演
function SixSpirit_Controller.OnClick_Middle()
    local _ItemID = _MeridianDeductionItemID[InnerBook_Model.m_MeridianIdx]
    local _ItemCount = table.Count(_ItemID)
    local _Type6Data = CustomCommonQueryData_Type6.NewData(_ItemCount, _ItemID, {1, 1, 1}, nil, nil, nil)
    CommonQueryMgr.AddNewInform(564, {}, nil,
        function()
            _IsDeduction = true
            InnerBook_Controller.SetMaskActive(false)
        end,
        nil, 
        function()
            InnerBook_Controller.SetMaskActive(false)
        end,
        nil, nil, nil, _Type6Data)

    InnerBook_Controller.SetMaskActive(true, false)
end

---設定靈功升級道具需求數量
local function SetUpgradeCount(iID, iLv)
    local _MeridianData = InnerMeridianSkillData.GetInnerMeridianSkillDataByID(InnerBook_Model.m_MeridianIdx, iID)
    if _MeridianData == nil then
        return {}, 0
    end

    local _NeedCount = _MeridianData.m_NeedCount
    local _CountTable = {}
    for i = iLv, 100 do
        for j = 1, _NeedCount do
            table.insert(_CountTable, i)
        end
    end
    table.remove(_CountTable, 1)

    local _TotalCount = table.Count(_CountTable)
    local _BagItemCount = BagMgr.GetItemInBagAmount(_MeridianUpgradeItemID)
    local _MaxCount = _TotalCount > _BagItemCount and _BagItemCount or _TotalCount
    return _CountTable, _MaxCount
end

---靈功升級
function SixSpirit_Controller.OnClick_Right()
    local _WugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[_WugongIdx]
    if _WugongData ~= nil and _WugongData.m_ID ~= 0 then
        local _ItemCount = BagMgr.GetItemInBagAmount(_MeridianUpgradeItemID)
        local _Type2Data = CommonQueryMgr.GetNewCommonQueryData(ECommonUIPrefabType.Item_ValueVariation)
	    _Type2Data:BuildIconBoxTable(EIconBoxDataType.Icon, {_MeridianUpgradeItemID}, {(_ItemCount)}, {true}, false)
        local _Wugong = WugongData.GetWugongDataByIdx(_WugongData.m_ID)
        local _Name = _Wugong.m_Name
        local _PreLv = _WugongData.m_Lv
	    _Type2Data:BuildDescriptionBoxTable({_Name}, {_PreLv}, {_PreLv}, {false}, {true})
        local _CountTable, _MaxCount = SetUpgradeCount(_WugongData.m_ID, _PreLv)
        _UpgradeLvTable = _CountTable
	    _Type2Data:BuildTimeTable(_MaxCount, 1, {0}, {_CountTable}, {}, true)
        _Type2Data:BuildConfirmBtnStatus(_ItemCount > 0)
	    CommonQueryMgr.AddNewInform(563, {TextData.Get(21002104)}, {TextData.Get(21002105)},
            function()
                local _Packet = {}
                _Packet.m_MeridianID = InnerBook_Model.m_MeridianIdx
                _Packet.m_Step = _WugongIdx
                _Packet.m_WugongID = _WugongData.m_ID
                _Packet.m_Lv = _CountTable[CommonQuery_Type2_Controller.GetCurrentTimes()]
                SendProtocol_003._017(9, _Packet)
                InnerBook_Controller.SetMaskActive(false)
                _IsUpgrade = false
            end,
            nil,
            function()
                InnerBook_Controller.SetMaskActive(false)
                SixSpirit_Controller.OnClick_Skill(_WugongIdx)
                _IsUpgrade = false
            end,
            nil,
            nil, nil, _Type2Data)

        InnerBook_Controller.SetMaskActive(true)
        _IsUpgrade = true
    end
end

--endregion

---設定按鈕文字和啟用狀態
local function SetBtnState(iLeftText, iMiddleText, iRightText, iLeftState, iMiddleState, iRightState)
    this.m_Btn_Left:SetText(TextData.Get(iLeftText))
    this.m_Btn_Middle:SetText(TextData.Get(iMiddleText))
    this.m_Btn_Right:SetText(TextData.Get(iRightText))
    if iLeftState then
        this.m_Btn_Left:SetEnable()
    else
        this.m_Btn_Left:SetDisable(false)
    end
    if iMiddleState then
        this.m_Btn_Middle:SetEnable()
    else
        this.m_Btn_Middle:SetDisable(false)
    end
    if iRightState then
        this.m_Btn_Right:SetEnable()
    else
        this.m_Btn_Right:SetDisable(false)
    end
end

---設定按鈕狀態
function SixSpirit_Controller.SetBtnState()
    local _WugongData = InnerBook_Model.m_CurrentMeridian.m_WugongData[_WugongIdx]
    local _IsEquip = InnerBook_Model.CheckCurrentWugong(_WugongIdx)
    local _UpgradeStr, _CanClickDeduction, _CanClickUpgrade = nil, nil, nil
    --靈功已裝備
    if _IsEquip then
        _UpgradeStr = _WugongData.m_Lv < 100 and _UpgradeString or _MaxLevelString
        _CanClickDeduction = not (_WugongIdx <= 3) and true or false
        _CanClickUpgrade = _WugongData.m_Lv  < 100 and true or false
        SetBtnState(_CancelEquipString, _DeductionString, _UpgradeStr, true, _CanClickDeduction, _CanClickUpgrade)

    --沒裝備
    else
        if _WugongIdx <= 3 then
            --靈功已解鎖
            if _WugongData ~= nil and _WugongIdx <= InnerBook_Model.m_CurrentMeridian.m_Step then
                _UpgradeStr = _WugongData.m_Lv < 100 and _UpgradeString or _MaxLevelString
                SetBtnState(_EquipString, _DeductionString, _UpgradeStr, true, false, true)
            else
                SetBtnState(_EquipString, _DeductionString, _UpgradeString, false, false, false)
            end
        else
            --階級已達到
            if _WugongIdx <= InnerBook_Model.m_CurrentMeridian.m_Step then
                --靈功已推演
                if _WugongData ~= nil and _WugongData.m_ID ~= 0 then
                    _UpgradeStr = _WugongData.m_Lv < 100 and _UpgradeString or _MaxLevelString
                    _CanClickUpgrade = _WugongData.m_Lv < 100 and true or false
                    SetBtnState(_EquipString, _DeductionString, _UpgradeStr, true, true, _CanClickUpgrade)
                else
                    SetBtnState(_EquipString, _DeductionString, _UpgradeString, false, true, false)
                end
            end
        end
    end
end

---狀態改變通知
function SixSpirit_Controller:OnStateChanged(iState, ...)
    if iState == EStateObserver.UpdateSixSpirit then
        local _Param = {...}
        local _Idx = _Param[1]
        local _Step = _Param[2]
        SixSpirit_Controller.SetWugongInfo(_Idx, _Step)
    end
end