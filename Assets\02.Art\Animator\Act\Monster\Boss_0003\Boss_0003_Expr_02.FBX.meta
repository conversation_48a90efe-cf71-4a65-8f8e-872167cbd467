fileFormatVersion: 2
guid: 2254b869c89c30048997cf7b319fe345
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Finger0
    100014: Bip001 L Finger01
    100016: Bip001 L Finger0Nub
    100018: Bip001 L Foot
    100020: Bip001 L Forearm
    100022: Bip001 L Hand
    100024: Bip001 L Thigh
    100026: Bip001 L Toe0
    100028: Bip001 L Toe0Nub
    100030: Bip001 L UpperArm
    100032: Bip001 Neck
    100034: Bip001 Neck1
    100036: Bip001 Pelvis
    100038: Bip001 R Calf
    100040: Bip001 R Clavicle
    100042: Bip001 R Finger0
    100044: Bip001 R Finger01
    100046: Bip001 R Finger0Nub
    100048: Bip001 R Foot
    100050: Bip001 R Forearm
    100052: Bip001 R Hand
    100054: Bip001 R Thigh
    100056: Bip001 R Toe0
    100058: Bip001 R Toe0Nub
    100060: Bip001 R UpperArm
    100062: Bip001 Spine
    100064: Bip001 Spine1
    100066: Bip001 Tail
    100068: Bip001 Tail1
    100070: Bip001 Tail2
    100072: Bip001 Tail3
    100074: Bip001 TailNub
    100076: Bone009
    100078: Bone011(mirrored)
    100080: Bone011(mirrored)(mirrored)
    100082: Bone012(mirrored)
    100084: Bone012(mirrored)(mirrored)
    100086: Bone013(mirrored)
    100088: Bone013(mirrored)(mirrored)
    100090: Bone015
    100092: Bone016
    100094: Bone017
    100096: Bone018
    100098: Bone020
    100100: Bone021
    100102: Bone022
    100104: Bone023
    100106: Bone024
    100108: Bone024(mirrored)
    100110: Bone024(mirrored)(mirrored)
    100112: Bone024(mirrored)A
    100114: //RootNode
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Finger0
    400014: Bip001 L Finger01
    400016: Bip001 L Finger0Nub
    400018: Bip001 L Foot
    400020: Bip001 L Forearm
    400022: Bip001 L Hand
    400024: Bip001 L Thigh
    400026: Bip001 L Toe0
    400028: Bip001 L Toe0Nub
    400030: Bip001 L UpperArm
    400032: Bip001 Neck
    400034: Bip001 Neck1
    400036: Bip001 Pelvis
    400038: Bip001 R Calf
    400040: Bip001 R Clavicle
    400042: Bip001 R Finger0
    400044: Bip001 R Finger01
    400046: Bip001 R Finger0Nub
    400048: Bip001 R Foot
    400050: Bip001 R Forearm
    400052: Bip001 R Hand
    400054: Bip001 R Thigh
    400056: Bip001 R Toe0
    400058: Bip001 R Toe0Nub
    400060: Bip001 R UpperArm
    400062: Bip001 Spine
    400064: Bip001 Spine1
    400066: Bip001 Tail
    400068: Bip001 Tail1
    400070: Bip001 Tail2
    400072: Bip001 Tail3
    400074: Bip001 TailNub
    400076: Bone009
    400078: Bone011(mirrored)
    400080: Bone011(mirrored)(mirrored)
    400082: Bone012(mirrored)
    400084: Bone012(mirrored)(mirrored)
    400086: Bone013(mirrored)
    400088: Bone013(mirrored)(mirrored)
    400090: Bone015
    400092: Bone016
    400094: Bone017
    400096: Bone018
    400098: Bone020
    400100: Bone021
    400102: Bone022
    400104: Bone023
    400106: Bone024
    400108: Bone024(mirrored)
    400110: Bone024(mirrored)(mirrored)
    400112: Bone024(mirrored)A
    400114: //RootNode
    7400000: Boss_0003_Expr_02
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 4311273aa8d47f34b8bc8ef7d6d42727,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
