fileFormatVersion: 2
guid: 11a5cf8030c44cd4a9f7ba43683a84f3
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: BackPoint
    100002: Bip001 Head
    100004: Bip001 Spine1
    100006: Eye Down L
    100008: Eye Down R
    100010: Eye Up L
    100012: Eye Up R
    100014: L Forearm
    100016: L Hand
    100018: L Machine
    100020: L UpperArm
    100022: LB Calf
    100024: LB Foot
    100026: LB Thigh
    100028: LF Calf
    100030: LF Foot
    100032: LF Thigh
    100034: M_0006_Base
    100036: //RootNode
    100038: R Forearm
    100040: R Hand
    100042: R Machine
    100044: R UpperArm
    100046: RB Calf
    100048: RB Foot
    100050: RB Thigh
    100052: RF Calf
    100054: RF Foot
    100056: RF Thigh
    100058: Floor
    400000: BackPoint
    400002: Bip001 Head
    400004: Bip001 Spine1
    400006: Eye Down L
    400008: Eye Down R
    400010: Eye Up L
    400012: Eye Up R
    400014: L Forearm
    400016: L Hand
    400018: L Machine
    400020: L UpperArm
    400022: LB Calf
    400024: LB Foot
    400026: LB Thigh
    400028: LF Calf
    400030: LF Foot
    400032: LF Thigh
    400034: M_0006_Base
    400036: //RootNode
    400038: R Forearm
    400040: R Hand
    400042: R Machine
    400044: R UpperArm
    400046: RB Calf
    400048: RB Foot
    400050: RB Thigh
    400052: RF Calf
    400054: RF Foot
    400056: RF Thigh
    400058: Floor
    2100000: No Name
    2300000: //RootNode
    2300002: Floor
    3300000: //RootNode
    3300002: Floor
    4300000: Floor
    7400000: M_0006_Idle_01
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: M_0006_Idle_01
      takeName: M_0006_Idle_01
      firstFrame: 0
      lastFrame: 70
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 0
    importBlendShapes: 0
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 0
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 1
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 5db747e9de84b534fb524d52846aac1c,
    type: 3}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
